﻿#pragma once

#include "DSBaseModel.h"

#include "JsonObject.h"
#include "SubSystems/MVC/Core/Macro.h"
#include "DesignStation/SubSystems/Pendant/DSPendantSubsystem.h"
#include "Engine/Engine.h"
#include "Gizmo/DSGizmoModel.h"
#include "Subsystems/Camera/DSCameraSubsystem.h"
#include "Subsystems/MVC/DSMVCSubsystem.h"
#include "Subsystems/Resource/DSResourceSubsystem.h"
#include "Subsystems/Drawing/DSDrawingSubsystem.h"

DEFINE_LOG_CATEGORY(DSBaseModelLog);

// no broadcast type --- MVC will not be notified
extern const TArray<EDSModelType> NoBroadcastNoModifyType = {
	EDSModelType::E_None,
	EDSModelType::E_Create,
	EDSModelType::E_CreatePath,
	EDSModelType::E_Room_Point,
	EDSModelType::E_House_Area_Split_Line_Point,
	EDSModelType::E_Axis2D,
	EDSModelType::E_Scale2D,
	EDSModelType::E_Scale2DForPath,
	EDSModelType::E_Ruler,
	EDSModelType::E_RulerDisplayer,
	EDSModelType::E_Point,
	EDSModelType::E_DrawingPoint,
	EDSModelType::E_Segment,
	EDSModelType::E_Plane,
	EDSModelType::E_Path,
	EDSModelType::E_PathPoint,
	//EDSModelType::E_HouseWallPathPoint,
	//EDSModelType::E_BeamPathPoint,
	//EDSModelType::E_PlatformPathPoint,
	EDSModelType::E_SegmentPoint
};

//to wall ruler mark flag
extern const FString RULER_WALL_FRONT;
extern const FString RULER_WALL_RIGHT;
extern const FString RULER_WALL_BACK;
extern const FString RULER_WALL_LEFT;

//to item ruler mark flag
extern const FString RULER_ITEM_FRONT;
extern const FString RULER_ITEM_RIGHT;
extern const FString RULER_ITEM_BACK;
extern const FString RULER_ITEM_LEFT;

//to room ruler mark flag
extern const FString RULER_CEILING_UP;
extern const FString RULER_FLOOR_DOWN;
extern const FString RULER_ITEM_UP;
extern const FString RULER_ITEM_DOWN;

//self ruler mark flag
extern const FString RULER_PATH_SELF_RIGHT;
extern const FString RULER_PATH_SELF_LEFT;

//start point left/right ruler mark flag
extern const FString RULER_PATH_POINT_START_0;
extern const FString RULER_PATH_POINT_START_1;

//end point left/right ruler mark flag
extern const FString RULER_PATH_POINT_END_0;
extern const FString RULER_PATH_POINT_END_1;

//path[wall, platform...] ruler mark flag
extern const FString RULER_POINT_PATH_0;
extern const FString RULER_POINT_PATH_1;
extern const FString RULER_POINT_PATH_2;
extern const FString RULER_POINT_PATH_3;
extern const FString RULER_POINT_PATH_4;
extern const FString RULER_POINT_PATH_5;

//self WHD ruler mark flag
extern const FString RULER_SELF_WIDTH;
extern const FString RULER_SELF_DEPTH;
extern const FString RULER_SELF_HEIGHT;

//segment ruler mark flag
extern const FString RULER_SEGMENT;

//segment start point left/right ruler mark flag
extern const FString RULER_SEGMENT_START_0;
extern const FString RULER_SEGMENT_START_1;

//segment end point left/right ruler mark flag
extern const FString RULER_SEGMENT_END_0;
extern const FString RULER_SEGMENT_END_1;

//on wall left/right ruler mark flag
extern const FString RULER_ON_WALL_POS;
extern const FString RULER_ON_WALL_NEG;

UDSBaseModel::UDSBaseModel()
	: UUID(FGuid::NewGuid().ToString())
	  , ModelType(EDSModelType::E_None)
	  , Property(nullptr)
	  , bIsDeleted(false)
	  , bCanMoved(true)
	  , PreScaleLocation(FVector::ZeroVector)
{
}

UDSBaseModel::UDSBaseModel(const EDSModelType& InType)
	: UUID(FGuid::NewGuid().ToString())
	  , ModelType(InType)
	  , Property(nullptr)
	  , bIsDeleted(false)
	  , bCanMoved(true)
{
}

UWorld* UDSBaseModel::GetWorld() const
{
	UWorld* ResultWorld = UObject::GetWorld();
	if (ResultWorld == nullptr)
	{
		if (FWorldContext* WorldContext = GEngine->GetWorldContextFromGameViewport(GEngine->GameViewport))
		{
			return WorldContext->World();
		}
	}

	return ResultWorld;
}

TMap<FString, bool> UDSBaseModel::GetModelRulerMark(const EDSModelType& InType)
{
	TMap<FString, bool> Res;
	switch (InType)
	{
	case EDSModelType::E_House_Area_Split_Line:
		{
			Res.Add(RULER_SEGMENT, true);
			Res.Add(RULER_SEGMENT_START_0, true);
			Res.Add(RULER_SEGMENT_START_1, true);
			Res.Add(RULER_SEGMENT_END_0, true);
			Res.Add(RULER_SEGMENT_END_1, true);
			break;
		}
	case EDSModelType::E_House_Pillar:
		{
			Res.Add(RULER_ITEM_BACK, true);
			Res.Add(RULER_WALL_BACK, true);
			Res.Add(RULER_ITEM_RIGHT, true);
			Res.Add(RULER_WALL_RIGHT, true);
			Res.Add(RULER_ITEM_LEFT, true);
			Res.Add(RULER_WALL_LEFT, true);
			Res.Add(RULER_ITEM_FRONT, true);
			Res.Add(RULER_WALL_FRONT, true);
			Res.Add(RULER_SELF_WIDTH, true);
			Res.Add(RULER_SELF_DEPTH, true);
			break;
		}
	case EDSModelType::E_House_Wall:
	case EDSModelType::E_House_Beam:
	case EDSModelType::E_House_Platform:
		{
			Res.Add(RULER_PATH_SELF_LEFT, true);
			Res.Add(RULER_PATH_SELF_RIGHT, true);
			Res.Add(RULER_PATH_POINT_START_0, true);
			Res.Add(RULER_PATH_POINT_START_1, true);
			Res.Add(RULER_PATH_POINT_END_0, true);
			Res.Add(RULER_PATH_POINT_END_1, true);
			break;
		}
	case EDSModelType::E_HouseWallPathPoint:
	case EDSModelType::E_BeamPathPoint:
	case EDSModelType::E_PlatformPathPoint:
		{
			Res.Add(RULER_POINT_PATH_0, true);
			Res.Add(RULER_POINT_PATH_1, true);
			Res.Add(RULER_POINT_PATH_2, true);
			Res.Add(RULER_POINT_PATH_3, true);
			Res.Add(RULER_POINT_PATH_4, true);
			Res.Add(RULER_POINT_PATH_5, true);
			break;
		}
	case EDSModelType::E_House_Window:
	case EDSModelType::E_House_Door:
		{
			Res.Add(RULER_PATH_SELF_RIGHT, true);
			Res.Add(RULER_PATH_SELF_LEFT, true);
			Res.Add(RULER_ON_WALL_POS, true);
			Res.Add(RULER_ON_WALL_NEG, true);
			break;
		}
	default: break;
	}
	return Res;
}

void UDSBaseModel::PostInitProperties()
{
	Super::PostInitProperties();

	DSBeginPlay();
}

void UDSBaseModel::DSBeginPlay()
{
	InitProperty();
	InitAdditionData();
	InitModelHiddenFlags();
}

void UDSBaseModel::InitAdditionData()
{
	ModelState = FDSModelState(
		FDSModelCreateMark(true),
		FDSModelTransformMark(true, true, true),
		FDSModelTransformMark(true, true, true)
	);
	PendantInfo = FPendantInfo(
		FDSAxisMark(true, true, true, true),
		FDSScaleMark(true, true),
		FDSRulerMark(
			FDSRuler_Inner(true, true, true, true),
			FDSRuler_RectSelf(false, false),
			FDSRuler_Inner(true, true, true, true),
			FDSRuler_Path(false, false),
			FDSRuler_Segment(false),
			FDSRuler_OnWall(false, false)
		)
	);
}

void UDSBaseModel::InitModelHiddenFlags()
{
	ModelHiddenFlags = FHiddenFlags(false, false);
	ModelHiddenFlags.OnFlagHidden.BindUObject(this, &ThisClass::OnHiddenFlagHandle);
	ModelHiddenFlags.OnFlagUnHidden.BindUObject(this, &ThisClass::OnUnHiddenFlagHandle);
}

void UDSBaseModel::Initialized()
{
	InitComponents();
	InitOutlines();
	InitMeshInfo();
}

void UDSBaseModel::SetModelType(const EDSModelType& InType)
{
	ModelType = InType;
}

bool UDSBaseModel::IsValid()
{
	return !UUID.IsEmpty() && ModelType != EDSModelType::E_None && Property.IsValid();
}

void UDSBaseModel::Clear()
{
	UUID.Empty();
	ModelType = EDSModelType::E_None;
	Property.Reset();
}

bool UDSBaseModel::operator==(UDSBaseModel* InModel) const
{
	if (DS_MODEL_VALID_FOR_USE(InModel))
	{
		return UUID.Equals(InModel->UUID);
	}
	return false;
}

bool UDSBaseModel::operator!=(UDSBaseModel* InModel) const
{
	return !(*this == InModel);
}

void UDSBaseModel::BindDelegates()
{
}

void UDSBaseModel::UnBindDelegates()
{
	GetModelViewExecuteDelegate().Unbind();
	OnModelPoolDeleteDelegate.Unbind();
	DSRefreshPropertyWidgetDelegate.Unbind();
}

void UDSBaseModel::UnBindMultiDelegates()
{
	OnLinkModelUpdatedDelegate.Clear();
	OnLinkModelDeletedDelegate.Clear();
}

void UDSBaseModel::ShallowCopy(UDSBaseModel* InModel)
{
	checkf(false, TEXT("UDSBaseModel::ShallowCopy --- Base Model No Action, Please ReWrite In Your Model"))
}

void UDSBaseModel::DeepCopy(UDSBaseModel* InModel)
{
	checkf(false, TEXT("UDSBaseModel::DeepCopy --- Base Model No Action, Please ReWrite In Your Model"))
}

bool UDSBaseModel::IsNewGenerate() const
{
	return ModelState.IsNewGenerate();
}

bool UDSBaseModel::SetNoNewGenerate()
{
	const bool OldMark = ModelState.IsNewGenerate();
	ModelState.SetToNoNew();
	ModelState.ResetCopy();
	return OldMark;
}

void UDSBaseModel::SetIsContinueGenerate(bool bContinue)
{
	ModelState.SetIsContinueGenerate(bContinue);
}

bool UDSBaseModel::IsContinueGenerate() const
{
	return ModelState.IsContinueGenerate();
}

void UDSBaseModel::SetCopyGenerate(bool bCopy)
{
	ModelState.SetToCopy();
}

bool UDSBaseModel::IsCopyGenerate() const
{
	return ModelState.IsCopyGenerate();
}

bool UDSBaseModel::CanMove(bool Is2D) const
{
	return ModelState.CanMove(Is2D);
}

bool UDSBaseModel::CanRot(bool Is2D) const
{
	return ModelState.CanRot(Is2D);
}

bool UDSBaseModel::CanScale(bool Is2D) const
{
	return ModelState.CanScale(Is2D);
}

bool UDSBaseModel::HasAxis_2D() const
{
	return PendantInfo.HasAxis_2D();
}

bool UDSBaseModel::HasAxis_3D_Move() const
{
	return PendantInfo.HasAxis_3D_Move();
}

bool UDSBaseModel::HasAxis_3D_Rot() const
{
	return PendantInfo.HasAxis_3D_Rot();
}

bool UDSBaseModel::HasAxis_3D_Scale() const
{
	return PendantInfo.HasAxis_3D_Scale();
}

bool UDSBaseModel::HasScale_2D() const
{
	return PendantInfo.HasScale_2D();
}

bool UDSBaseModel::HasScale_3D() const
{
	return PendantInfo.HasScale_3D();
}

bool UDSBaseModel::Has2DRuler() const
{
	return PendantInfo.Has2DRuler();
}

bool UDSBaseModel::Has3DRuler() const
{
	return PendantInfo.Has3DRuler();
}

bool UDSBaseModel::bThisRulerShow(const FString& InRulerName)
{
	return PendantInfo.RulerMark.RulerMarkMap.Contains(InRulerName) ? PendantInfo.RulerMark.RulerMarkMap[InRulerName] : false;
}

void UDSBaseModel::ClearComponentModels()
{
	for (UDSBaseModel* Model : ComponentModelSet)
	{
		if (Model)
		{
			Model->SetOwnerModel(nullptr);
		}
	}
	ComponentModelSet.Empty();
}

void UDSBaseModel::RemoveComponentModel(UDSBaseModel* InModel)
{
	if (InModel)
	{
		InModel->SetOwnerModel(nullptr);
	}
	ComponentModelSet.Remove(InModel);
}

void UDSBaseModel::AppendComponentModel(TArray<UDSBaseModel*> InModels)
{
	for (UDSBaseModel* Model : InModels)
	{
		if (Model && !ComponentModelSet.Contains(Model))
		{
			ComponentModelSet.Add(Model);
			Model->SetOwnerModel(this);
		}
	}
}

UDSBaseModel* UDSBaseModel::GetTopLevelOwnerModel()
{
	UDSBaseModel* TopLevelOwner = this;
	while (TopLevelOwner->GetOwnerModel())
	{
		TopLevelOwner = TopLevelOwner->GetOwnerModel();
	}
	return TopLevelOwner;
}

UDSBaseModel* UDSBaseModel::GetOwnerModel()
{
	return OwnerModel;
}

void UDSBaseModel::SetOwnerModel(UDSBaseModel* InModel)
{
	OwnerModel = InModel;
}

FPendantInfo UDSBaseModel::GetPendantInfo()
{
	return PendantInfo;
}

void UDSBaseModel::AddModelStateFlag(EModelState InState)
{
	if (InState == EModelState::E_Selected)
	{
		//UE_LOG(LogTemp, Warning, TEXT("UDSBaseModel::AddModelStateFlag --- Model E_Selected"));
	}
	ModelStateFlag.AddState(InState);


	for (auto& Iter : ComponentModelSet)
	{
		if (Iter == nullptr) continue;
		Iter->AddModelStateFlag(InState);
	}
	UDSMVCSubsystem::GetInstance()->OnModelStateFlagChange(this, ModelStateFlag);
}

void UDSBaseModel::RemoveModelStateFlag(EModelState InState)
{
	ModelStateFlag.RemoveState(InState);
	for (auto& CMS : ComponentModelSet)
	{
		if (CMS == nullptr) continue;
		CMS->RemoveModelStateFlag(InState);
	}
	UDSMVCSubsystem::GetInstance()->OnModelStateFlagChange(this, ModelStateFlag);
}

bool UDSBaseModel::IsHasModelFlag(const EModelState& InState)
{
	return ModelStateFlag.HasState(InState);
}

bool UDSBaseModel::IsSelected()
{
	return IsHasModelFlag(EModelState::E_Selected);
}

FBox UDSBaseModel::GetBoundBox()
{
	//使用尺寸得到轮廓点初始化FBOX
	TArray<FVector> EightBoundaryPoints = GetModelOrientedBoundingBox();
	return FBox(EightBoundaryPoints);

	/*FBox Bound = FBox(ForceInit);
	for (auto& FOL : OutlineInfo.FrameOutline)
	{
		for (const auto& P : FOL)
		{
			Bound += P;
		}
	}
	return Bound;*/
}

void UDSBaseModel::AddToMultiSelect(const FString& InMultiUUID)
{
	MultiGroupData.SetMultiUUID(InMultiUUID);
}

void UDSBaseModel::AddToGroupSelect(const FString& InGroupUUID)
{
	MultiGroupData.SetGroupUUID(InGroupUUID);
}

void UDSBaseModel::ReleaseFromGroup()
{
	MultiGroupData.ReleaseGroup();
	DSRefreshForMultiGroupDelegate.Clear();
}

void UDSBaseModel::BroadcastToRefreshForMultiGroup()
{
	DSRefreshForMultiGroupDelegate.Broadcast(this);
}

void UDSBaseModel::BroadcastToDeleteForMultiGroup()
{
	DSDeleteForMultiGroupDelegate.Broadcast(this);
}

FVector UDSBaseModel::CalculateModelSize() const
{
	if (!Property)
	{
		return FVector::OneVector;
	}

	return FVector(Property->SizeProperty.Width, Property->SizeProperty.Depth, Property->SizeProperty.Height);
}

FVector UDSBaseModel::CalculateScaledTranslationOffset(EDSGizmoOperatorType OperatorType, const FVector& SourceSize, const FVector& NewSize, const TArray<FVector>& OriginOBB,
                                                       const TArray<FVector>& ScaledOBB)
{
	FVector Offset = FVector::ZeroVector;

	FVector OriginCenter = (OriginOBB[0] + OriginOBB[6]) * 0.5f;

	FVector MoveDir;
	double MoveDistance = 0.0f;
	auto ActualTransform = Property->GetActualTransform();
	FQuat ModelRot = ActualTransform.GetRotation();
	switch (OperatorType)
	{
	case EDSGizmoOperatorType::Left_Depth:
		{
			FVector OriginPoint = (OriginOBB[3] + OriginOBB[6]) * 0.5f;
			FVector ScaledPoint = (ScaledOBB[3] + ScaledOBB[6]) * 0.5f;

			FVector PointDir = OriginPoint - OriginCenter;
			PointDir.Normalize();

			FVector OriginToScale = ScaledPoint - OriginPoint;

			double DotVal = FVector::DotProduct(PointDir, OriginToScale.GetSafeNormal());
			double DirFactor = DotVal >= 0.0f ? -1.0f : 1.0f;

			MoveDistance = OriginToScale.Size();
			MoveDir = ModelRot.GetRightVector() * DirFactor;

			Offset = MoveDir * MoveDistance;
		}
		break;
	case EDSGizmoOperatorType::Front_Width:
		{
			FVector OriginPoint = (OriginOBB[0] + OriginOBB[7]) * 0.5f;
			FVector ScaledPoint = (ScaledOBB[0] + ScaledOBB[7]) * 0.5f;

			FVector PointDir = OriginPoint - OriginCenter;
			PointDir.Normalize();

			FVector OriginToScale = ScaledPoint - OriginPoint;

			double DotVal = FVector::DotProduct(PointDir, OriginToScale.GetSafeNormal());
			double DirFactor = DotVal >= 0.0f ? 1.0f : -1.0f;

			MoveDistance = OriginToScale.Size();
			MoveDir = ModelRot.GetForwardVector() * DirFactor;

			Offset = MoveDir * MoveDistance;
		}
		break;
	case EDSGizmoOperatorType::Right_Depth:
		{
			FVector OriginPoint = (OriginOBB[0] + OriginOBB[5]) * 0.5f;
			FVector ScaledPoint = (ScaledOBB[0] + ScaledOBB[5]) * 0.5f;

			FVector PointDir = OriginPoint - OriginCenter;
			PointDir.Normalize();

			FVector OriginToScale = ScaledPoint - OriginPoint;

			double DotVal = FVector::DotProduct(PointDir, OriginToScale.GetSafeNormal());
			double DirFactor = DotVal >= 0.0f ? 1.0f : -1.0f;

			MoveDistance = OriginToScale.Size();
			MoveDir = ModelRot.GetRightVector() * DirFactor;

			Offset = MoveDir * MoveDistance;
		}
		break;
	case EDSGizmoOperatorType::Back_Width:
		{
			FVector OriginPoint = (OriginOBB[1] + OriginOBB[6]) * 0.5f;
			FVector ScaledPoint = (ScaledOBB[1] + ScaledOBB[6]) * 0.5f;

			FVector PointDir = OriginPoint - OriginCenter;
			PointDir.Normalize();

			FVector OriginToScale = ScaledPoint - OriginPoint;

			double DotVal = FVector::DotProduct(PointDir, OriginToScale.GetSafeNormal());
			double DirFactor = DotVal >= 0.0f ? -1.0f : 1.0f;

			MoveDistance = OriginToScale.Size();
			MoveDir = ModelRot.GetForwardVector() * DirFactor;

			Offset = MoveDir * MoveDistance;
		}
		break;
	case EDSGizmoOperatorType::Up_Height:
		{
			FVector OriginPoint = (OriginOBB[4] + OriginOBB[6]) * 0.5f;
			FVector ScaledPoint = (ScaledOBB[4] + ScaledOBB[6]) * 0.5f;

			FVector PointDir = OriginPoint - OriginCenter;
			PointDir.Normalize();

			FVector OriginToScale = ScaledPoint - OriginPoint;

			double DotVal = FVector::DotProduct(PointDir, OriginToScale.GetSafeNormal());
			double DirFactor = DotVal >= 0.0f ? 1.0f : -1.0f;

			MoveDistance = OriginToScale.Size();
			MoveDir = ModelRot.GetUpVector() * DirFactor;

			Offset = MoveDir * MoveDistance;
		}
		break;
	case EDSGizmoOperatorType::Down_Height:
		{
			FVector OriginPoint = (OriginOBB[0] + OriginOBB[2]) * 0.5f;
			FVector ScaledPoint = (ScaledOBB[0] + ScaledOBB[2]) * 0.5f;

			FVector PointDir = OriginPoint - OriginCenter;
			PointDir.Normalize();

			FVector OriginToScale = ScaledPoint - OriginPoint;

			double DotVal = FVector::DotProduct(PointDir, OriginToScale.GetSafeNormal());
			double DirFactor = DotVal >= 0.0f ? -1.0f : 1.0f;

			MoveDistance = OriginToScale.Size();
			MoveDir = ModelRot.GetUpVector() * DirFactor;

			Offset = MoveDir * MoveDistance;
		}
		break;
	case EDSGizmoOperatorType::TopLeft_Width_Depth:
		{
			FVector ScaleVector = ScaledOBB[3] - OriginOBB[3];

			MoveDistance = ScaleVector.Size();

			MoveDir = ScaleVector.GetSafeNormal();
			MoveDir *= -1.0f;

			Offset = MoveDir * MoveDistance;
		}
		break;
	case EDSGizmoOperatorType::TopRight_Width_Depth:
		{
			FVector ScaleVector = ScaledOBB[0] - OriginOBB[0];

			MoveDistance = ScaleVector.Size();

			MoveDir = ScaleVector.GetSafeNormal();
			MoveDir *= -1.0f;

			Offset = MoveDir * MoveDistance;
		}
		break;
	case EDSGizmoOperatorType::BottomLeft_Width_Depth:
		{
			FVector ScaleVector = ScaledOBB[2] - OriginOBB[2];

			MoveDistance = ScaleVector.Size();

			MoveDir = ScaleVector.GetSafeNormal();
			MoveDir *= -1.0f;

			Offset = MoveDir * MoveDistance;
		}
		break;
	case EDSGizmoOperatorType::BottomRight_Width_Depth:
		{
			FVector ScaleVector = ScaledOBB[1] - OriginOBB[1];

			MoveDistance = ScaleVector.Size();

			MoveDir = ScaleVector.GetSafeNormal();
			MoveDir *= -1.0f;

			Offset = MoveDir * MoveDistance;
		}
		break;
	default:
		break;
	}

	return Offset;
}

TArray<FDSGizmoScalePoint> UDSBaseModel::GetAllScaleOperatorPoints() const
{
	TArray<FVector> BoxPoints = GetModelOrientedBoundingBox();

	FVector TopCenter = (BoxPoints[0] + BoxPoints[2]) * 0.5f;

	TArray<FDSGizmoScalePoint> ScalePoints;
	ScalePoints.AddDefaulted(8);

	ScalePoints[0].Type = EDSGizmoOperatorType::Left_Depth;
	ScalePoints[0].WorldLocation = (BoxPoints[0] + BoxPoints[1]) * 0.5f;
	ScalePoints[0].WorldDirection = (ScalePoints[0].WorldLocation - TopCenter).GetSafeNormal();

	ScalePoints[1].Type = EDSGizmoOperatorType::TopLeft_Width_Depth;
	ScalePoints[1].WorldLocation = BoxPoints[1];
	ScalePoints[1].WorldDirection = (ScalePoints[1].WorldLocation - TopCenter).GetSafeNormal();

	ScalePoints[2].Type = EDSGizmoOperatorType::Front_Width;
	ScalePoints[2].WorldLocation = (BoxPoints[1] + BoxPoints[2]) * 0.5f;
	ScalePoints[2].WorldDirection = (ScalePoints[2].WorldLocation - TopCenter).GetSafeNormal();

	ScalePoints[3].Type = EDSGizmoOperatorType::TopRight_Width_Depth;
	ScalePoints[3].WorldLocation = BoxPoints[2];
	ScalePoints[3].WorldDirection = (ScalePoints[3].WorldLocation - TopCenter).GetSafeNormal();

	ScalePoints[4].Type = EDSGizmoOperatorType::Right_Depth;
	ScalePoints[4].WorldLocation = (BoxPoints[2] + BoxPoints[3]) * 0.5f;
	ScalePoints[4].WorldDirection = (ScalePoints[4].WorldLocation - TopCenter).GetSafeNormal();

	ScalePoints[5].Type = EDSGizmoOperatorType::BottomRight_Width_Depth;
	ScalePoints[5].WorldLocation = BoxPoints[3];
	ScalePoints[5].WorldDirection = (ScalePoints[5].WorldLocation - TopCenter).GetSafeNormal();

	ScalePoints[6].Type = EDSGizmoOperatorType::Back_Width;
	ScalePoints[6].WorldLocation = (BoxPoints[3] + BoxPoints[0]) * 0.5f;
	ScalePoints[6].WorldDirection = (ScalePoints[6].WorldLocation - TopCenter).GetSafeNormal();

	ScalePoints[7].Type = EDSGizmoOperatorType::BottomLeft_Width_Depth;
	ScalePoints[7].WorldLocation = BoxPoints[0];
	ScalePoints[7].WorldDirection = (ScalePoints[7].WorldLocation - TopCenter).GetSafeNormal();

	return ScalePoints;
}

bool UDSBaseModel::IsValidSizeToScale(EDSGizmoOperatorType OperatorType, const FVector& SourceSize,
                                      const FVector& NewSize) const
{
	return false;
}

bool UDSBaseModel::ShouldTranslateModelAfterScaleByGizmo(EDSGizmoOperatorType OperatorType, const FVector& SourceSize,
                                                         const FVector& NewSize)
{
	return true;
}

bool UDSBaseModel::ClampScaleSizeToLimitation(EDSGizmoOperatorType OperatorType, const FVector& SourceSize, FVector& NewSize)
{
	return false;
}

void UDSBaseModel::TranslateByGizmo(const EAxis::Type& InRotAxis, const FTransform& SourceTrans, const FTransform& NewTrans)
{
	if (Property.IsValid())
	{
		Property->TransformProperty.Location = NewTrans.GetLocation();

		OnExecuteAction(FDSModelExecuteType::ExecuteTransformByAxis);
	}
}

void UDSBaseModel::PreTranslateByGizmo(const EAxis::Type& InRotAxis, const FTransform& SourceTrans, const FTransform& NewTrans)
{
}

void UDSBaseModel::PostTranslateByGizmo(const EAxis::Type& InRotAxis, const FTransform& SourceTrans, const FTransform& NewTrans)
{
}

void UDSBaseModel::RotatorByGizmo(const EAxis::Type& InRotAxis, const FTransform& SourceTrans, const FTransform& NewTrans)
{
	if (Property.IsValid())
	{
		Property->TransformProperty.Location = NewTrans.GetLocation();
		Property->TransformProperty.Rotation = NewTrans.GetRotation().Rotator();
		Property->TransformProperty.Scale = NewTrans.GetScale3D();

		OnExecuteAction(FDSModelExecuteType::ExecuteTransformByAxis, FDSBroadcastMarkData::OnlyOutlineBroadcastMark);
	}
}

void UDSBaseModel::PreRotatorByGizmo(const EAxis::Type& InRotAxis, const FTransform& SourceTrans, const FTransform& NewTrans)
{
}

void UDSBaseModel::PostRotatorByGizmo(const EAxis::Type& InRotAxis, const FTransform& SourceTrans, const FTransform& NewTrans)
{
}

void UDSBaseModel::ScaleSizeByGizmo(EDSGizmoOperatorType OperatorType, const FVector& SourceSize,
                                    const FVector& NewSize)
{
	if (!Property)
	{
		return;
	}

	if (ShouldTranslateModelAfterScaleByGizmo(OperatorType, SourceSize, NewSize))
	{
		Property->SizeProperty.Width = SourceSize.X;
		Property->SizeProperty.Depth = SourceSize.Y;
		Property->SizeProperty.Height = SourceSize.Z;

		TArray<FVector> OriginOBB = GetModelOrientedBoundingBox(false);

		Property->SizeProperty.Width = NewSize.X;
		Property->SizeProperty.Depth = NewSize.Y;
		Property->SizeProperty.Height = NewSize.Z;

		TArray<FVector> ScaledOBB = GetModelOrientedBoundingBox(false);

		FVector MoveOffset = CalculateScaledTranslationOffset(OperatorType, SourceSize, NewSize, OriginOBB, ScaledOBB);

		Property->TransformProperty.Location = PreScaleLocation + MoveOffset;
		OnExecuteAction(FDSModelExecuteType::ExecuteTransform);
	}
	else
	{
		Property->SizeProperty.Width = NewSize.X;
		Property->SizeProperty.Depth = NewSize.Y;
		Property->SizeProperty.Height = NewSize.Z;
	}

	UpdatePivotOffset();

	if (UDSPendantSubsystem::IsInitialized() && UDSPendantSubsystem::GetInstance()->GetGizmoModel() != nullptr)
	{
		UDSGizmoModel* GizmoModel = UDSPendantSubsystem::GetInstance()->GetGizmoModel();
		FTransform ModelTransform = Property->TransformProperty.ToUETransform();
		GizmoModel->GetPropertySharedPtr()->TransformProperty.Location = ModelTransform.TransformPosition(Property->GizmoStatusProperty.PivotOffset);
		GizmoModel->OnExecuteAction(FDSModelExecuteType::ExecuteTransform);
	}
}

void UDSBaseModel::PreScaleSizeByGizmo(EDSGizmoOperatorType OperatorType, const FVector& SourceSize,
                                       const FVector& NewSize)
{
	if (!Property)
	{
		return;
	}

	PreScaleLocation = Property->TransformProperty.Location;
}

void UDSBaseModel::PostScaleSizeByGizmo(EDSGizmoOperatorType OperatorType, const FVector& SourceSize,
                                        const FVector& NewSize)
{
	PreScaleLocation = FVector::ZeroVector;
}

void UDSBaseModel::UpdateSnapInfo()
{
}

FSnapInfo UDSBaseModel::GetSnapInfo()
{
	return SnapInfo;
}

void UDSBaseModel::UpdateMeshInfo()
{
}

void UDSBaseModel::UpdateComponentMeshInfoUV(const int32& InIndex)
{
}

void UDSBaseModel::UpdateMeshInfoUV()
{
}

const FDSMeshInfo& UDSBaseModel::GetMeshInfo() const
{
	return MeshInfo;
}

void UDSBaseModel::AddToIgnoreDSModel(UDSBaseModel* InModel)
{
	if (DS_MODEL_VALID_FOR_USE(InModel))
	{
		ADSBaseView* ToIgnoreView = InModel->GetOwnedView();
		ADSBaseView* ThisView = this->GetOwnedView();
		if (ThisView && ToIgnoreView)
		{
			ThisView->AddToIgnoreDSView(ToIgnoreView);
			ToIgnoreView->AddToIgnoreDSView(ThisView);
		}
	}
}

const FOutlineInfo& UDSBaseModel::GetOutlineInfo() const
{
	return OutlineInfo;
}

void UDSBaseModel::UpdateOutlineInfo()
{
}

EDSGizmoModeType UDSBaseModel::GetDefaultGizmoModeType(bool bIs2DScene) const
{
	return EDSGizmoModeType::GMT_None;
}

void UDSBaseModel::UpdatePivotOffset()
{
}

TArray<FVector> UDSBaseModel::GetBottomOutline()
{
	return TArray<FVector>();
}

TArray<FVector> UDSBaseModel::GetWorldTopOutline()
{
	auto Loc = Property->GetTransformProperty().Location;
	auto Scale = Property->GetTransformProperty().Scale;
	TArray<FVector> Outline;
	for (auto& Iter : OutlineInfo.TopOutline)
	{
		auto ScaleVec = Iter;
		ScaleVec.X *= Scale.X;
		ScaleVec.Y *= Scale.Y;
		ScaleVec.Z *= Scale.Z;

		Outline.Add(Loc + ScaleVec);
	}
	return Outline;
}

TArray<FVector> UDSBaseModel::GetWorldBottomOutline()
{
	auto Loc = Property->GetTransformProperty().Location;
	auto Scale = Property->GetTransformProperty().Scale;
	TArray<FVector> Outline;
	for (auto& Iter : OutlineInfo.BottomOutline)
	{
		auto ScaleVec = Iter;
		ScaleVec.X *= Scale.X;
		ScaleVec.Y *= Scale.Y;
		ScaleVec.Z *= Scale.Z;

		Outline.Add(Loc + ScaleVec);
	}
	return Outline;
}

TArray<FVector> UDSBaseModel::GetWorldOutline()
{
	return TArray<FVector>();
}

TArray<TArray<FVector>> UDSBaseModel::GetWorldFrameOutline()
{
	TArray<TArray<FVector>> FrameOutline;
	auto Loc = Property->GetTransformProperty().Location;

	for (auto& Iter : OutlineInfo.FrameOutline)
	{
		TArray<FVector> Outline;
		for (auto& P : Iter)
		{
			Outline.Add(P + Loc);
		}
		FrameOutline.Add(Outline);
	}
	return FrameOutline;
}

bool UDSBaseModel::GetModelOuterRect(TArray<FVector>& OutRect)
{
	TArray<FVector> Outline = GetWorldTopOutline();

	if (Outline.Num() > 0)
	{
		Outline.Sort([](const FVector& A, const FVector& B) { return A.X > B.X; });
		auto MaxX = Outline[0].X;
		auto MinX = Outline.Last().X;

		Outline.Sort([](const FVector& A, const FVector& B) { return A.Y > B.Y; });
		auto MaxY = Outline[0].Y;
		auto MinY = Outline.Last().Y;

		OutRect.Empty();
		OutRect.SetNumZeroed(4);

		OutRect[0] = FVector(MaxX, MinY, 0.f);
		OutRect[1] = FVector(MaxX, MaxY, 0.f);
		OutRect[2] = FVector(MinX, MaxY, 0.f);
		OutRect[3] = FVector(MinX, MinY, 0.f);

		return true;
	}
	return false;
}

bool UDSBaseModel::GetModelOuter(TArray<FVector>& Outer)
{
	TArray<FVector> Outline = GetWorldTopOutline();

	if (Outline.Num() > 0)
	{
		Outline.Sort([](const FVector& A, const FVector& B) { return A.X > B.X; });

		auto Back = Outline[0];
		for (auto i = 1; i < Outline.Num(); ++i)
		{
			if (FMath::IsNearlyEqual(Back.X, Outline[i].X, 0.01f))
			{
				Back = (Back + Outline[i]) * 0.5f;
			}
		}

		auto Front = Outline.Last();

		for (auto i = Outline.Num() - 2; i >= 0; --i)
		{
			if (FMath::IsNearlyEqual(Front.X, Outline[i].X, 0.01f))
			{
				Front = (Front + Outline[i]) * 0.5f;
			}
		}

		Outline.Sort([](const FVector& A, const FVector& B) { return A.Y > B.Y; });

		auto Right = Outline[0];
		for (auto i = 1; i < Outline.Num(); ++i)
		{
			if (FMath::IsNearlyEqual(Right.Y, Outline[i].Y, 0.01f))
			{
				Right = (Right + Outline[i]) * 0.5f;
			}
		}

		auto Left = Outline.Last();
		for (auto i = Outline.Num() - 2; i >= 0; --i)
		{
			if (FMath::IsNearlyEqual(Left.Y, Outline[i].Y, 0.01f))
			{
				Left = (Left + Outline[i]) * 0.5f;
			}
		}

		Back.Z = 0.f;
		Right.Z = 0.f;
		Front.Z = 0.f;
		Left.Z = 0.f;

		Outer.Empty();
		Outer.Add(Back);
		Outer.Add(Right);
		Outer.Add(Front);
		Outer.Add(Left);

		return true;
	}
	return false;
}

bool UDSBaseModel::GetModelCenterCross(TArray<FVector>& OutCross)
{
	TArray<FVector> Outline = GetWorldTopOutline();

	if (Outline.Num() > 0)
	{
		Outline.Sort([](const FVector& A, const FVector& B) { return A.X > B.X; });

		auto Bottom = Outline[0];
		for (auto i = 1; i < Outline.Num(); ++i)
		{
			if (FMath::IsNearlyEqual(Bottom.X, Outline[i].X, 0.01f))
			{
				Bottom = (Bottom + Outline[i]) * 0.5f;
			}
		}

		auto Top = Outline.Last();

		for (auto i = Outline.Num() - 2; i >= 0; --i)
		{
			if (FMath::IsNearlyEqual(Top.X, Outline[i].X, 0.01f))
			{
				Top = (Top + Outline[i]) * 0.5f;
			}
		}

		Outline.Sort([](const FVector& A, const FVector& B) { return A.Y > B.Y; });

		auto Right = Outline[0];
		for (auto i = 1; i < Outline.Num(); ++i)
		{
			if (FMath::IsNearlyEqual(Bottom.Y, Outline[i].Y, 0.01f))
			{
				Right = (Right + Outline[i]) * 0.5f;
			}
		}

		auto Left = Outline.Last();
		for (auto i = Outline.Num() - 2; i >= 0; --i)
		{
			if (FMath::IsNearlyEqual(Left.Y, Outline[i].Y, 0.01f))
			{
				Left = (Left + Outline[i]) * 0.5f;
			}
		}

		Top.Z = 0.f;
		Bottom.Z = 0.f;
		Right.Z = 0.f;
		Left.Z = 0.f;

		OutCross.Empty();
		OutCross.Add(Top);
		OutCross.Add(Bottom);
		OutCross.Add(Right);
		OutCross.Add(Left);

		return true;
	}
	return false;
}

TArray<FVector> UDSBaseModel::GetModelOrientedBoundingBox(bool bUseFixedSize) const
{
	return {
		FVector(0.0f, 0.0f, 1.0f), FVector(1.0f, 0.0f, 1.0f), FVector(1.0f, 1.0f, 1.0f), FVector(0.0f, 1.0f, 1.0f),
		FVector(0.0f, 0.0f, 0.0f), FVector(1.0f, 0.0f, 0.0f), FVector(1.0f, 1.0f, 0.0f), FVector(0.0f, 1.0f, 0.0f)
	};
}

TArray<FVector> UDSBaseModel::GetModelOrientedBoundingBoxWithoutRotator(bool bUseFixedSize) const
{
	return {
		FVector(0.0f, 0.0f, 1.0f), FVector(1.0f, 0.0f, 1.0f), FVector(1.0f, 1.0f, 1.0f), FVector(0.0f, 1.0f, 1.0f),
		FVector(0.0f, 0.0f, 0.0f), FVector(1.0f, 0.0f, 0.0f), FVector(1.0f, 1.0f, 0.0f), FVector(0.0f, 1.0f, 0.0f)
	};
}

void UDSBaseModel::GetModelOrientedBoundingBox(FVector& Center, FVector& Extents, FQuat& Rotation) const
{
	Center = FVector::ZeroVector;
	Extents = FVector::ZeroVector;
	Rotation = FQuat(ForceInit);
}

void UDSBaseModel::GetModelCollectionCaptureComponentsAndActors(TArray<UPrimitiveComponent*>& ShowComponents, TArray<AActor*>& ShowActors)
{
	if (OwnedView)
	{
		OwnedView->GetModelCollectionCaptureComponentsAndActors(ShowComponents, ShowActors);
	}
}

FDSStateProperty UDSBaseModel::GetModelStateProperty() const
{
	checkf(Property.IsValid(), TEXT("Model Property No Init"));

	return Property->GetStateProperty();
}

bool UDSBaseModel::HasLock() const
{
	return GetModelStateProperty().IsLock();
}

bool UDSBaseModel::HasHidden() const
{
	return GetModelStateProperty().IsHidden();
}

bool UDSBaseModel::HasFavourite() const
{
	return GetModelStateProperty().IsFavourite();
}

bool UDSBaseModel::GetViewHidden() const
{
	return HasHidden() || bAreaHidden || bViewTypeHidden;
}

void UDSBaseModel::GenerateUUID()
{
	UUID = FGuid::NewGuid().ToString();
}

void UDSBaseModel::OnReSelected()
{
	UDSMVCSubsystem::GetInstance()->SetCurrentModel(this);
}

 void UDSBaseModel::OnExecuteAction(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
 {
 	switch (InExecuteType.BaseExecuteType)
 	{
 	case EDSModelExecuteBaseType::E_MET_None:
 		break;
 	case EDSModelExecuteBaseType::E_MET_All:
 	{
 		OnExecute_All(InExecuteType, BroadcastMarkPtr);
 		break;
 	}
 	case EDSModelExecuteBaseType::E_MET_Update:
 	{
 		OnExecute_Update(InExecuteType, BroadcastMarkPtr);
 		break;
 	}
 	case EDSModelExecuteBaseType::E_MET_Select:
 	{
 		OnExecute_Select(InExecuteType, BroadcastMarkPtr);
 		break;
 	}
 	case EDSModelExecuteBaseType::E_MET_Hover:
 	{
 		OnExecute_Hover(InExecuteType, BroadcastMarkPtr);
 		break;
 	}
 	case EDSModelExecuteBaseType::E_MET_Spawn:
 	{
 		OnExecute_Spawn(InExecuteType, BroadcastMarkPtr);
 		break;
 	}
 	case EDSModelExecuteBaseType::E_MET_Transform:
 	{
 		OnExecute_Transform(InExecuteType, BroadcastMarkPtr);
 		break;
 	}
 	case EDSModelExecuteBaseType::E_MET_Delete:
 	{
 		OnExecute_Delete(InExecuteType, BroadcastMarkPtr);
 		break;
 	}
 	case EDSModelExecuteBaseType::E_MET_Lock:
 	{
 		OnExecute_Lock(InExecuteType, BroadcastMarkPtr);
 		break;
 	}
 	case EDSModelExecuteBaseType::E_MET_Hidden:
 	{
 		OnExecute_Hidden(InExecuteType, BroadcastMarkPtr);
 		break;
 	}
 	case EDSModelExecuteBaseType::E_MET_Favorite:
 	{
 		OnExecute_Favorite(InExecuteType, BroadcastMarkPtr);
 		break;
 	}
 	case EDSModelExecuteBaseType::E_MET_Follow:
 	{
 		OnExecute_Follow(InExecuteType, BroadcastMarkPtr);
 		break;
 	}
 	case EDSModelExecuteBaseType::E_MET_Refresh:
 	{
 		OnExecute_Refresh(InExecuteType, BroadcastMarkPtr);
 		break;
 	}
 	case EDSModelExecuteBaseType::E_MET_Revoke:
 	{
 		OnExecute_Revoke(InExecuteType, BroadcastMarkPtr);
 		break;
 	}
 	case EDSModelExecuteBaseType::E_MET_Flip:
 	{
 		OnExecute_Flip(InExecuteType, BroadcastMarkPtr);
 		break;
 	}
 	case EDSModelExecuteBaseType::E_MET_Multi:
 	{
 		OnExecute_Multi(InExecuteType, BroadcastMarkPtr);
 		break;
 	}
 	case EDSModelExecuteBaseType::E_MET_Group:
 	{
 		OnExecute_Group(InExecuteType, BroadcastMarkPtr);
 		break;
 	}
 	case EDSModelExecuteBaseType::E_MET_Overlap:
 	{
 		OnExecute_Overlap(InExecuteType, BroadcastMarkPtr);
 		break;
 	}
 	case EDSModelExecuteBaseType::E_MET_Enable:
 	{
 		OnExecute_Enable(InExecuteType, BroadcastMarkPtr);
 		break;
 	}
 	case EDSModelExecuteBaseType::E_MET_Section:
 	{
 		OnExecute_Section(InExecuteType, BroadcastMarkPtr);
 		break;
 	}
 	default: break;	
 	}

 	//broadcast to MVC
 	if (UDSMVCSubsystem::IsInitialized())
 	{
 		UDSMVCSubsystem::GetInstance()->OnModelExecuteCommandComplete(this, InExecuteType, BroadcastMarkPtr);
 	}
 }

void UDSBaseModel::OnExecute_All(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	GetModelViewExecuteDelegate().ExecuteIfBound(this, FDSModelExecuteType::ExecuteAll);
}


//update
void UDSBaseModel::OnExecute_Update(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	if (InExecuteType.MarkType == static_cast<uint8>(EDSExecuteType_Update::E_METU_UpdateSelf))
	{
		OnExecute_Model_UpdateSelf(FDSModelExecuteType::ExecuteUpdateSelf, BroadcastMarkPtr);
	}
	else
	{
		OnExecute_Model_UpdateByProperty(FDSModelExecuteType::ExecuteUpdateByProperty, BroadcastMarkPtr);
	}
}

void UDSBaseModel::OnExecute_Model_UpdateSelf(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	GetModelViewExecuteDelegate().ExecuteIfBound(this, InExecuteType);
}

void UDSBaseModel::OnExecute_Model_UpdateByProperty(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	GetModelViewExecuteDelegate().ExecuteIfBound(this, InExecuteType);
}


//select
void UDSBaseModel::OnExecute_Select(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	if(InExecuteType.MarkType == static_cast<uint8>(EDSExecuteType_Select::E_METS_Select))
	{
		OnExecute_Model_Select(InExecuteType, BroadcastMarkPtr);
	}
	else if(InExecuteType.MarkType == static_cast<uint8>(EDSExecuteType_Select::E_METS_UnSelect))
	{
		OnExecute_Model_UnSelect(InExecuteType, BroadcastMarkPtr);
	}
	else if(InExecuteType.MarkType == static_cast<uint8>(EDSExecuteType_Select::E_METS_SelectComponent))
	{
		OnExecute_Model_SelectComponent(InExecuteType, BroadcastMarkPtr);
	}
	else if(InExecuteType.MarkType == static_cast<uint8>(EDSExecuteType_Select::E_METS_UnSelectComponent))
	{
		OnExecute_Model_UnSelectComponent(InExecuteType, BroadcastMarkPtr);
	}
	else if(InExecuteType.MarkType == static_cast<uint8>(EDSExecuteType_Select::E_METS_PreciseSelect))
	{
		OnExecute_Model_PreciseSelect(InExecuteType, BroadcastMarkPtr);
	}
	else if (InExecuteType.MarkType == static_cast<uint8>(EDSExecuteType_Select::E_METS_UnPreciseSelect))
	{
        OnExecute_Model_UnPreciseSelect(InExecuteType, BroadcastMarkPtr);
	}
}

void UDSBaseModel::OnExecute_Model_Select(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	if (ModelStateFlag.NeedExecuteState(EModelState::E_Selected) && !ModelStateFlag.HasState(EModelState::E_Overlap))
	{
		AddModelStateFlag(EModelState::E_Selected);
		RemoveModelStateFlag(EModelState::E_SelectComponent);

		for (auto& Iter : ComponentModelSet)
		{
			Iter->OnExecuteAction(InExecuteType, BroadcastMarkPtr);
		}

		GetModelViewExecuteDelegate().ExecuteIfBound(this, FDSModelExecuteType::ExecuteSelect);
	}
	else
	{
		AddModelStateFlag(EModelState::E_Selected);
		RemoveModelStateFlag(EModelState::E_SelectComponent);
	}
}

void UDSBaseModel::OnExecute_Model_UnSelect(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	if (ModelStateFlag.HasState(EModelState::E_Selected) && !ModelStateFlag.HasState(EModelState::E_Overlap))
	{
		RemoveModelStateFlag(EModelState::E_Selected);
		RemoveModelStateFlag(EModelState::E_SelectComponent);
		RemoveModelStateFlag(EModelState::E_Hovered);

		for (auto& Iter : ComponentModelSet)
		{
			Iter->OnExecuteAction(InExecuteType, BroadcastMarkPtr);
		}

		GetModelViewExecuteDelegate().ExecuteIfBound(this, FDSModelExecuteType::ExecuteUnSelect);
	}
	else
	{
		RemoveModelStateFlag(EModelState::E_Selected);
		RemoveModelStateFlag(EModelState::E_SelectComponent);
		RemoveModelStateFlag(EModelState::E_Hovered);
	}
}

void UDSBaseModel::OnExecute_Model_SelectComponent(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	AddModelStateFlag(EModelState::E_SelectComponent);
	
	//更新轮廓线数据
	if (ExpandOutlineForComponentSelect())
	{
		UDSDrawingSubsystem::GetInstance()->UpdateModel(this);
	}

	GetModelViewExecuteDelegate().ExecuteIfBound(this, FDSModelExecuteType::ExecuteSelectComponent);
}

void UDSBaseModel::OnExecute_Model_UnSelectComponent(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	RemoveModelStateFlag(EModelState::E_SelectComponent);
	RemoveModelStateFlag(EModelState::E_Hovered);

	//还原轮廓线
	if (RecoverOutline())
	{
		UDSDrawingSubsystem::GetInstance()->UpdateModel(this);
	}

	GetModelViewExecuteDelegate().ExecuteIfBound(this, FDSModelExecuteType::ExecuteUnSelectComponent);
}

void UDSBaseModel::OnExecute_Model_PreciseSelect(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
}

void UDSBaseModel::OnExecute_Model_UnPreciseSelect(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
}

//hover
void UDSBaseModel::OnExecute_Hover(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	if(InExecuteType.MarkType == static_cast<uint8>(EDSExecuteType_Hover::E_METH_Hover))
	{
		OnExecute_Model_Hover(InExecuteType, BroadcastMarkPtr);
	}
	else if(InExecuteType.MarkType == static_cast<uint8>(EDSExecuteType_Hover::E_METH_UnHover))
	{
		OnExecute_Model_UnHover(InExecuteType, BroadcastMarkPtr);
	}
}

void UDSBaseModel::OnExecute_Model_Hover(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	if (ModelStateFlag.NeedExecuteState(EModelState::E_Hovered))
	{
		AddModelStateFlag(EModelState::E_Hovered);
		for (auto& Iter : ComponentModelSet)
		{
			Iter->OnExecuteAction(FDSModelExecuteType::ExecuteHover, BroadcastMarkPtr);
		}

		GetModelViewExecuteDelegate().ExecuteIfBound(this, FDSModelExecuteType::ExecuteHover);
	}
	else
	{
		AddModelStateFlag(EModelState::E_Hovered);
	}
}

void UDSBaseModel::OnExecute_Model_UnHover(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	if (ModelStateFlag.NeedExecuteState(EModelState::E_Hovered))
	{
		RemoveModelStateFlag(EModelState::E_Hovered);

		for (auto& Iter : ComponentModelSet)
		{
			Iter->OnExecuteAction(FDSModelExecuteType::ExecuteUnHover, BroadcastMarkPtr);
		}

		GetModelViewExecuteDelegate().ExecuteIfBound(this, FDSModelExecuteType::ExecuteUnHover);
	}
	else
	{
		RemoveModelStateFlag(EModelState::E_Hovered);
	}
}


//spawn
void UDSBaseModel::OnExecute_Spawn(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	if(InExecuteType.MarkType == static_cast<uint8>(EDSExecuteType_Spawn::E_METSP_Spawn))
	{
		OnExecute_Model_Spawn(FDSModelExecuteType::ExecuteSpawn, BroadcastMarkPtr);
	}
	else if(InExecuteType.MarkType == static_cast<uint8>(EDSExecuteType_Spawn::E_METSP_GenerateFormProperty))
	{
		OnExecute_Model_GenerateByProperty(FDSModelExecuteType::ExecuteGenerateFormProperty, BroadcastMarkPtr);
	}
	else if(InExecuteType.MarkType == static_cast<uint8>(EDSExecuteType_Spawn::E_METSP_GenerateMesh))
	{
		OnExecute_Model_GenerateMesh(InExecuteType, BroadcastMarkPtr);
	}
}

void UDSBaseModel::OnExecute_Model_Spawn(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	Initialized();

	if (SupportsLazyLoad())
	{
		StopObserveResourceStatusEvent();
		ObserveResourceStatusEvent();
	}

	GetModelViewExecuteDelegate().ExecuteIfBound(this, FDSModelExecuteType::ExecuteSpawn);
}

void UDSBaseModel::OnExecute_Model_GenerateByProperty(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
    GetModelViewExecuteDelegate().ExecuteIfBound(this, FDSModelExecuteType::ExecuteGenerateFormProperty);
}

void UDSBaseModel::OnExecute_Model_GenerateMesh(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	GetModelViewExecuteDelegate().ExecuteIfBound(this, InExecuteType);
}

//transform
void UDSBaseModel::OnExecute_Transform(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	if(InExecuteType.MarkType == static_cast<uint8>(EDSExecuteType_Transform::E_METTR_Transform))
	{
		OnExecute_Model_Transform(InExecuteType, BroadcastMarkPtr);
	}
	else if(InExecuteType.MarkType == static_cast<uint8>(EDSExecuteType_Transform::E_METTR_TransformByAXis))
	{
		OnExecute_Model_TransformByAxis(InExecuteType, BroadcastMarkPtr);
	}
	else if(InExecuteType.MarkType == static_cast<uint8>(EDSExecuteType_Transform::E_METTR_TransformNormal))
	{
		OnExecute_Model_TransformNormal(InExecuteType, BroadcastMarkPtr);
	}
}

void UDSBaseModel::OnExecute_Model_Transform(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{

	GetModelViewExecuteDelegate().ExecuteIfBound(this, InExecuteType);

	for (auto& Iter : ComponentModelSet)
	{
		Iter->OnExecuteAction(InExecuteType, BroadcastMarkPtr);
	}

	UpdateSnapInfo();
	UpdateOutlineInfo();
	

	BroadcastToRefreshForMultiGroup();
}

void UDSBaseModel::OnExecute_Model_TransformByAxis(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	OnExecute_Model_Transform(InExecuteType, BroadcastMarkPtr);
}

void UDSBaseModel::OnExecute_Model_TransformNormal(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	OnExecute_Model_Transform(InExecuteType, BroadcastMarkPtr);
}


//delete
void UDSBaseModel::OnExecute_Delete(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	if(InExecuteType.MarkType == static_cast<uint8>(EDSExecuteType_Delete::E_METD_Delete))
	{
		OnExecute_Model_Delete(InExecuteType, BroadcastMarkPtr);
	}
	else if(InExecuteType.MarkType == static_cast<uint8>(EDSExecuteType_Delete::E_METD_DeleteSelf))
	{
		OnExecute_Model_DeleteSelf(InExecuteType, BroadcastMarkPtr);
	}
	else if(InExecuteType.MarkType == static_cast<uint8>(EDSExecuteType_Delete::E_METD_DeleteSelfOnly))
	{
		OnExecute_Model_DeleteSelfOnly(InExecuteType, BroadcastMarkPtr);
	}
}

void UDSBaseModel::OnExecute_Model_Delete(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	if (Property->GetIsLock())
	{
		return;
	}
	for (auto& Iter : ComponentModelSet)
	{
		Iter->OnExecuteAction(InExecuteType, BroadcastMarkPtr);
	}

	bIsDeleted = true;

	OnLinkModelDeletedDelegate.Broadcast(this);

	LinkModels.Empty();

	StopObserveResourceStatusEvent();

	if (OnModelPoolDeleteDelegate.IsBound())
	{
		OnModelPoolDeleteDelegate.ExecuteIfBound(this);
	}

	BroadcastToDeleteForMultiGroup();
}

void UDSBaseModel::OnExecute_Model_DeleteSelf(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	for (auto& Iter : ComponentModelSet)
	{
		Iter->OnExecuteAction(InExecuteType, BroadcastMarkPtr);
	}

	bIsDeleted = true;

	LinkModels.Empty();

	StopObserveResourceStatusEvent();

	if (OnModelPoolDeleteDelegate.IsBound())
	{
		OnModelPoolDeleteDelegate.ExecuteIfBound(this);
	}

	BroadcastToDeleteForMultiGroup();
}

void UDSBaseModel::OnExecute_Model_DeleteSelfOnly(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	GetDeleteToResourcePoolDelegate().ExecuteIfBound(this);
}

//lock
void UDSBaseModel::OnExecute_Lock(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	if(InExecuteType.MarkType == static_cast<uint8>(EDSExecuteType_Lock::E_METL_Lock))
	{
		OnExecute_Model_Lock(InExecuteType, BroadcastMarkPtr);
	}
	else if(InExecuteType.MarkType == static_cast<uint8>(EDSExecuteType_Lock::E_METL_UnLock))
	{
		OnExecute_Model_UnLock(InExecuteType, BroadcastMarkPtr);
	}
}

void UDSBaseModel::OnExecute_Model_Lock(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	if (Property)
	{
		Property->StateProperty.bLock = true;
	}

	GetModelViewExecuteDelegate().ExecuteIfBound(this, FDSModelExecuteType::ExecuteUpdateSelf);
}

void UDSBaseModel::OnExecute_Model_UnLock(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	if (Property)
	{
		Property->StateProperty.bLock = false;
	}

	GetModelViewExecuteDelegate().ExecuteIfBound(this, FDSModelExecuteType::ExecuteUpdateSelf);
}

//hide
void UDSBaseModel::OnExecute_Hidden(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	if(InExecuteType.MarkType == static_cast<uint8>(EDSExecuteType_Hidden::E_METH_Hidden))
	{
		OnExecute_Model_Hidden(InExecuteType, BroadcastMarkPtr);
	}
	else if(InExecuteType.MarkType == static_cast<uint8>(EDSExecuteType_Hidden::E_METH_UnHidden))
	{
		OnExecute_Model_UnHidden(InExecuteType, BroadcastMarkPtr);
	}
	else if(InExecuteType.MarkType == static_cast<uint8>(EDSExecuteType_Hidden::E_METH_AreaHidden))
	{
		OnExecute_Model_AreaHidden(InExecuteType, BroadcastMarkPtr);
	}
	else if(InExecuteType.MarkType == static_cast<uint8>(EDSExecuteType_Hidden::E_METH_UnAreaHidden))
	{
		OnExecute_Model_UnAreaHidden(InExecuteType, BroadcastMarkPtr);
	}
	else if(InExecuteType.MarkType == static_cast<uint8>(EDSExecuteType_Hidden::E_METH_ViewTypeHidden))
	{
		OnExecute_Model_ViewTypeHidden(InExecuteType, BroadcastMarkPtr);
	}
	else if(InExecuteType.MarkType == static_cast<uint8>(EDSExecuteType_Hidden::E_METH_UnViewTypeHidden))
	{
		OnExecute_Model_UnViewTypeHidden(InExecuteType, BroadcastMarkPtr);
	}
}

void UDSBaseModel::OnExecute_Model_Hidden(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	if (Property)
	{
		Property->StateProperty.bHidden = true;
	}
	AddModelStateFlag(EModelState::E_Hidden);

	for (auto& Iter : ComponentModelSet)
	{
		Iter->OnExecuteAction(InExecuteType, BroadcastMarkPtr);
	}
	GetModelViewExecuteDelegate().ExecuteIfBound(this, InExecuteType);
}

void UDSBaseModel::OnExecute_Model_UnHidden(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	if (Property)
	{
		Property->StateProperty.bHidden = false;
	}

	for (auto& Iter : ComponentModelSet)
	{
		Iter->OnExecuteAction(InExecuteType, BroadcastMarkPtr);
	}
	if (!GetViewHidden())
	{
		RemoveModelStateFlag(EModelState::E_Hidden);
		GetModelViewExecuteDelegate().ExecuteIfBound(this, InExecuteType);
	}
}

void UDSBaseModel::OnExecute_Model_AreaHidden(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	bAreaHidden = true;
	AddModelStateFlag(EModelState::E_Hidden);

	for (auto& Iter : ComponentModelSet)
	{
		Iter->OnExecuteAction(InExecuteType, BroadcastMarkPtr);
	}
	GetModelViewExecuteDelegate().ExecuteIfBound(this, InExecuteType);
}

void UDSBaseModel::OnExecute_Model_UnAreaHidden(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	bAreaHidden = false;
	for (auto& Iter : ComponentModelSet)
	{
		Iter->OnExecuteAction(InExecuteType, BroadcastMarkPtr);
	}
	if (!GetViewHidden())
	{
		RemoveModelStateFlag(EModelState::E_Hidden);
		GetModelViewExecuteDelegate().ExecuteIfBound(this, InExecuteType);
	}
}

void UDSBaseModel::OnExecute_Model_ViewTypeHidden(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	bViewTypeHidden = true;
	AddModelStateFlag(EModelState::E_Hidden);
	for (auto& Iter : ComponentModelSet)
	{
		Iter->OnExecuteAction(InExecuteType, BroadcastMarkPtr);
	}
	GetModelViewExecuteDelegate().ExecuteIfBound(this, InExecuteType);
}

void UDSBaseModel::OnExecute_Model_UnViewTypeHidden(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	bViewTypeHidden = false;
	for (auto& Iter : ComponentModelSet)
	{
		Iter->OnExecuteAction(InExecuteType, BroadcastMarkPtr);
	}
	if (!GetViewHidden())
	{
		RemoveModelStateFlag(EModelState::E_Hidden);
		GetModelViewExecuteDelegate().ExecuteIfBound(this, InExecuteType);
	}
}

void UDSBaseModel::OnHiddenFlagHandle()
{
	OnExecute_Model_Hidden(FDSModelExecuteType::ExecuteHidden, FDSBroadcastMarkData::BroadcastToMVCMark);
}

void UDSBaseModel::OnUnHiddenFlagHandle()
{
	OnExecute_Model_UnHidden(FDSModelExecuteType::ExecuteUnHidden, FDSBroadcastMarkData::BroadcastToMVCMark);
}



//favorite
void UDSBaseModel::OnExecute_Favorite(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	if(InExecuteType.MarkType == static_cast<uint8>(EDSExecuteType_Favorite::E_METF_Favorite))
	{
		OnExecute_Model_Favorite(InExecuteType, BroadcastMarkPtr);
	}
	else if(InExecuteType.MarkType == static_cast<uint8>(EDSExecuteType_Favorite::E_METF_UnFavorite))
	{
		OnExecute_Model_UnFavorite(InExecuteType, BroadcastMarkPtr);
	}
}

void UDSBaseModel::OnExecute_Model_Favorite(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
}

void UDSBaseModel::OnExecute_Model_UnFavorite(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
}

//follow
void UDSBaseModel::OnExecute_Follow(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
}

//refresh
void UDSBaseModel::OnExecute_Refresh(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	if (InExecuteType.MarkType == static_cast<uint8>(EDSExecuteType_Refresh::E_METR_RefreshMaterial))
	{
		OnExecute_Model_RefreshMaterial(InExecuteType, BroadcastMarkPtr);
	}
	else if(InExecuteType.MarkType == static_cast<uint8>(EDSExecuteType_Refresh::E_METR_RefreshMaterialForce))
	{
		OnExecute_Model_RefreshMaterialForce(InExecuteType, BroadcastMarkPtr);
	}
}

void UDSBaseModel::OnExecute_Model_RefreshMaterial(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	GetModelViewExecuteDelegate().ExecuteIfBound(this, InExecuteType);
}

void UDSBaseModel::OnExecute_Model_RefreshMaterialForce(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	GetModelViewExecuteDelegate().ExecuteIfBound(this, InExecuteType);
}

//revoke
void UDSBaseModel::OnExecute_Revoke(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	if (InExecuteType.MarkType == static_cast<uint8>(EDSExecuteType_Revoke::E_METR_Redo))
	{
		OnExecute_Model_Undo(InExecuteType, BroadcastMarkPtr);
	}
	else if (InExecuteType.MarkType == static_cast<uint8>(EDSExecuteType_Revoke::E_METR_Undo))
	{
        OnExecute_Model_Redo(InExecuteType, BroadcastMarkPtr);
	}
}

void UDSBaseModel::OnExecute_Model_Undo(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
}

void UDSBaseModel::OnExecute_Model_Redo(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
}


//flip
void UDSBaseModel::OnExecute_Flip(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	if(InExecuteType.MarkType == static_cast<uint8>(EDSExecuteType_Flip::E_METF_FlipX))
	{
		OnExecute_Model_FlipX(InExecuteType, BroadcastMarkPtr);
	}
	else if(InExecuteType.MarkType == static_cast<uint8>(EDSExecuteType_Flip::E_METF_FlipY))
	{
		OnExecute_Model_FlipY(InExecuteType, BroadcastMarkPtr);
	}
}

void UDSBaseModel::OnExecute_Model_FlipX(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
}

void UDSBaseModel::OnExecute_Model_FlipY(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
}

//multi
void UDSBaseModel::OnExecute_Multi(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	if (InExecuteType.MarkType == static_cast<uint8>(EDSExecuteType_Multi::E_METM_Add))
	{
		OnExecute_Model_MultiAdd(InExecuteType, BroadcastMarkPtr);
	}
	else if (InExecuteType.MarkType == static_cast<uint8>(EDSExecuteType_Multi::E_METM_Release))
	{
        OnExecute_Model_MultiRelease(InExecuteType, BroadcastMarkPtr);
	}
}

void UDSBaseModel::OnExecute_Model_MultiAdd(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	GetModelViewExecuteDelegate().ExecuteIfBound(this, FDSModelExecuteType::ExecuteMultiAdd);
}

void UDSBaseModel::OnExecute_Model_MultiRelease(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	GetModelViewExecuteDelegate().ExecuteIfBound(this, FDSModelExecuteType::ExecuteMultiRelease);
}


//group
void UDSBaseModel::OnExecute_Group(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	if (InExecuteType.MarkType == static_cast<uint8>(EDSExecuteType_Group::E_METG_Combine))
	{
		OnExecute_Model_GroupCombine(InExecuteType, BroadcastMarkPtr);
	}
	else if (InExecuteType.MarkType == static_cast<uint8>(EDSExecuteType_Group::E_METG_Release))
	{
        OnExecute_Model_GroupRelease(InExecuteType, BroadcastMarkPtr);
	}
}

void UDSBaseModel::OnExecute_Model_GroupCombine(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	GetModelViewExecuteDelegate().ExecuteIfBound(this, FDSModelExecuteType::ExecuteGroupCombine);
}

void UDSBaseModel::OnExecute_Model_GroupRelease(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	GetModelViewExecuteDelegate().ExecuteIfBound(this, FDSModelExecuteType::ExecuteGroupRelease);
}

//overlap
void UDSBaseModel::OnExecute_Overlap(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	if (InExecuteType.MarkType == static_cast<uint8>(EDSExecuteType_Overlap::E_METO_Overlap))
	{
        OnExecute_Model_Overlap(InExecuteType, BroadcastMarkPtr);
	}
	else if (InExecuteType.MarkType == static_cast<uint8>(EDSExecuteType_Overlap::E_METO_UnOverlap))
	{
        OnExecute_Model_UnOverlap(InExecuteType, BroadcastMarkPtr);
	}
}

void UDSBaseModel::OnExecute_Model_Overlap(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	AddModelStateFlag(EModelState::E_Overlap);
	GetModelViewExecuteDelegate().ExecuteIfBound(this, InExecuteType);
}

void UDSBaseModel::OnExecute_Model_UnOverlap(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	RemoveModelStateFlag(EModelState::E_Overlap);
	//RemoveModelStateFlag(EModelState::E_Selected);
	//RemoveModelStateFlag(EModelState::E_SelectComponent);
	//RemoveModelStateFlag(EModelState::E_Hovered);
	GetModelViewExecuteDelegate().ExecuteIfBound(this, InExecuteType);
}

//enable
void UDSBaseModel::OnExecute_Enable(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	if (InExecuteType.MarkType == static_cast<uint8>(EDSExecuteType_Enable::E_METE_Enable))
	{
        OnExecute_Model_Enable(InExecuteType, BroadcastMarkPtr);
	}
	else if (InExecuteType.MarkType == static_cast<uint8>(EDSExecuteType_Enable::E_METE_Disable))
	{
        OnExecute_Model_Disable(InExecuteType, BroadcastMarkPtr);
	}
}

void UDSBaseModel::OnExecute_Model_Disable(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	if (ModelStateFlag.NeedExecuteState(EModelState::E_Invalid))
	{
		AddModelStateFlag(EModelState::E_Invalid);
		for (auto& Iter : ComponentModelSet)
		{
			Iter->OnExecuteAction(InExecuteType, BroadcastMarkPtr);
		}
		GetModelViewExecuteDelegate().ExecuteIfBound(this, InExecuteType);
	}
	else
	{
		AddModelStateFlag(EModelState::E_Invalid);
	}
}

void UDSBaseModel::OnExecute_Model_Enable(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	if (ModelStateFlag.HasState(EModelState::E_Invalid))
	{
		RemoveModelStateFlag(EModelState::E_Invalid);

		for (auto& Iter : ComponentModelSet)
		{
			Iter->OnExecuteAction(InExecuteType, BroadcastMarkPtr);
		}

		GetModelViewExecuteDelegate().ExecuteIfBound(this, InExecuteType);
	}
	else
	{
		RemoveModelStateFlag(EModelState::E_Invalid);
	}
}

//section
void UDSBaseModel::OnExecute_Section(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	if (InExecuteType.MarkType == static_cast<uint8>(EDSExecuteType_Section::E_METS_UV))
    {
		OnExecute_Model_UV(InExecuteType, BroadcastMarkPtr);
    }
}

void UDSBaseModel::OnExecute_Model_UV(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	GetModelViewExecuteDelegate().ExecuteIfBound(this, InExecuteType);
}

//void UDSBaseModel::OnSpawn()
//{
//	//Spawn流程不负责通知View，也不负责通知Mvc
//	Initialized();
//
//
//	if (SupportsLazyLoad())
//	{
//		ObserveResourceStatusEvent();
//	}
//
//	//CallModelChangeToRefreshDelegate(static_cast<int32>(EActionCommandType::E_Spawn));
//}

void UDSBaseModel::SyncTransform(const FTransform& InTrans, bool NeedExecute /*= false*/)
{
	if (Property.IsValid())
	{
		Property->TransformProperty.SetTransform(InTrans);

		if (NeedExecute)
		{
			OnExecuteAction(FDSModelExecuteType::ExecuteTransform, FDSBroadcastMarkData::OnlyOutlineBroadcastMark);
		}
	}
}

FVector UDSBaseModel::GetCurrentSize()
{
	if (Property.IsValid())
	{
		return Property->GetSizeProperty().ToVector();
	}
	return FVector();
}

FVector UDSBaseModel::GetCurrentRealSize()
{
	return GetCurrentSize();
}


void UDSBaseModel::FlipHiddenState()
{
	if (Property.IsValid())
	{
		bool CurHiddenState = Property->StateProperty.bHidden;
		if (CurHiddenState)
		{// hidden --> visibility
			OnExecute_Model_UnHidden(FDSModelExecuteType::ExecuteUnHidden, FDSBroadcastMarkData::BroadcastToMVCMark);
		}
		else
		{
			OnExecute_Model_Hidden(FDSModelExecuteType::ExecuteHidden, FDSBroadcastMarkData::BroadcastToMVCMark);
		}
	}
}

void UDSBaseModel::OnRefreshDrawing()
{
	
}

void UDSBaseModel::SetMaterialByUUID(const FString& InUUID, const FString& InResourceID, const double& InUSize, const double& InVSize,bool bUpdateUV)
{
	FDSMaterialProperty DefaultMaterialProperty;

	FDSMaterialInfo MaterialInfo2D;
	MaterialInfo2D.Index = 0;
	MaterialInfo2D.MaterialPath = InUUID;
	MaterialInfo2D.Id = InResourceID;
	MaterialInfo2D.USize = InUSize;
	MaterialInfo2D.VSize = InVSize;

	FDSMaterialInfo MaterialInfo3D;
	MaterialInfo3D.Index = 0;
	MaterialInfo3D.MaterialPath = InUUID;
	MaterialInfo3D.Id = InResourceID;
	MaterialInfo3D.USize = InUSize;
	MaterialInfo3D.VSize = InVSize;

	DefaultMaterialProperty.MaterialInfo2D.Add(MaterialInfo2D);
	DefaultMaterialProperty.MaterialInfo3D.Add(MaterialInfo3D);

	Property->SetMaterialProperty(DefaultMaterialProperty);

	if (bUpdateUV)
	{
		UpdateMeshInfoUV();
	}
	
	OnExecute_Model_RefreshMaterialForce(FDSModelExecuteType::ExecuteRefreshMaterialForce, FDSBroadcastMarkData::BroadcastToMVCMark);
}

void UDSBaseModel::AddLinkModel(UDSBaseModel* InModel)
{
	if (InModel)
	{
		if (!LinkModels.Contains(InModel))
		{
			LinkModels.Add(InModel);
			OnLinkModelUpdatedDelegate.AddUFunction(InModel, FName(TEXT("OnLinkModelUpdatedHandle")));
			OnLinkModelDeletedDelegate.AddUFunction(InModel, FName(TEXT("OnLinkModelDeletedHandle")));
			OnLinkModelTransformDelegate.AddUFunction(InModel, FName(TEXT("OnLinkModelTransformHandle")));
			OnLinkModelZHeightDelegate.AddUFunction(InModel, FName(TEXT("OnLinkModelZHeightHandle")));
		}
	}
}

void UDSBaseModel::DeleteLinkModel(UDSBaseModel* InModel)
{
	LinkModels.Remove(InModel);
	if (OnLinkModelUpdatedDelegate.IsBoundToObject(InModel))
	{
		OnLinkModelUpdatedDelegate.RemoveAll(InModel);
	}
	if (OnLinkModelDeletedDelegate.IsBoundToObject(InModel))
	{
		OnLinkModelDeletedDelegate.RemoveAll(InModel);
	}
}

void UDSBaseModel::ClearLinkModel()
{
	LinkModels.Empty();
	OnLinkModelUpdatedDelegate.Clear();
	OnLinkModelDeletedDelegate.Clear();
}

void UDSBaseModel::SetDeleted(bool InDeleted)
{
	bIsDeleted = InDeleted;
}

TSet<TWeakObjectPtr<UDSBaseModel>> UDSBaseModel::GetLinkModels()
{
	return LinkModels;
}

bool UDSBaseModel::GetIsDeleted()
{
	return bIsDeleted;
}

bool UDSBaseModel::CanMoved()
{
	return bCanMoved;
}

void UDSBaseModel::ClearStyle()
{
	OnExecute_Model_UnSelect(FDSModelExecuteType::ExecuteUnSelect, FDSBroadcastMarkData::BroadcastToMVCMark);
	UDSMVCSubsystem::GetInstance()->SetCurrentModel(nullptr);
}

void UDSBaseModel::Serialization(TSharedRef<TJsonWriter<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>> JsonWriter)
{
	JsonWriter->WriteValue("UUID", UUID);
	JsonWriter->WriteValue("ModelType", static_cast<int32>(ModelType));
	JsonWriter->WriteValue("bIsDeleted", bIsDeleted);
	JsonWriter->WriteArrayStart(TEXT("LinkModels"));
	{
		for (auto& It : LinkModels)
		{
			if (It.IsValid())
			{
				JsonWriter->WriteValue(It->GetUUID());
			}
		}
	}
	JsonWriter->WriteArrayEnd();
	JsonWriter->WriteValue("OwnerModel", OwnerModel ? OwnerModel->GetUUID() : TEXT(""));
	JsonWriter->WriteValue("CreateIndex", CreateIndex);
	JsonWriter->WriteObjectStart(TEXT("Property"));
	{
		Property->Serialization(JsonWriter);
	}
	JsonWriter->WriteObjectEnd();
	JsonWriter->WriteValue("bCanMoved", bCanMoved);
}

void UDSBaseModel::Deserialization(const TSharedPtr<FJsonObject>& InJsonData, bool bDeserializeID)
{
	if (bDeserializeID)
	{
		if (!UUID.IsEmpty() && UUID != InJsonData->GetStringField(TEXT("UUID")))
		{
			UDSMVCSubsystem::GetInstance()->ReplaceUUID(this, UUID, InJsonData->GetStringField(TEXT("UUID")));
		}

		UUID = InJsonData->GetStringField(TEXT("UUID"));
	}
	ModelType = static_cast<EDSModelType>(InJsonData->GetIntegerField(TEXT("ModelType")));
	bIsDeleted = InJsonData->GetBoolField(TEXT("bIsDeleted"));
	bCanMoved = InJsonData->GetBoolField(TEXT("bCanMoved"));
	Property->Deserialization(InJsonData->GetObjectField(TEXT("Property")));
}

void UDSBaseModel::SetCreateIndex(const int32& InIndex)
{
	CreateIndex = InIndex;
}

int32 UDSBaseModel::GetCreateIndex()
{
	return CreateIndex;
}

void UDSBaseModel::BindCameraTypeChangeHandle()
{
	if (UDSCameraSubsystem::GetInstance())
	{
		UDSCameraSubsystem::GetInstance()->CameraTypeDelegate.AddUObject(this, &ThisClass::OnCameraTypeChange);

		ECameraType CameraType = UDSCameraSubsystem::GetInstance()->GetCameraType();
		if (CameraType == ECameraType::EWalk || CameraType == ECameraType::EDollHouse)
		{
			bAreaHidden = true;
			ModelStateFlag.AddState(EModelState::E_Hidden);
		}
	}
}

void UDSBaseModel::ObserveResourceStatusEvent()
{
	if (UDSResourceSubsystem::IsInitialized())
	{
		UDSResourceSubsystem::GetInstance()->OnResourceStatusChangedEvent().AddDynamic(
			this, &ThisClass::OnResourceStatusInPoolChanged);
	}
}

void UDSBaseModel::StopObserveResourceStatusEvent()
{
	if (UDSResourceSubsystem::IsInitialized())
	{
		UDSResourceSubsystem::GetInstance()->OnResourceStatusChangedEvent().RemoveDynamic(this, &ThisClass::OnResourceStatusInPoolChanged);
	}
}

bool UDSBaseModel::CheckIfNeedsStopObserveResourceStatus() const
{
	return false;
}

void UDSBaseModel::OnResourceStatusInPoolChanged(const FString& Identify, EDSResourceType ResourceType, EResourceStatusEventType EventType)
{
}
