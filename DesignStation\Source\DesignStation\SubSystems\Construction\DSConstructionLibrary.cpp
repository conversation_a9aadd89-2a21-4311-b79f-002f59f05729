// Fill out your copyright notice in the Description page of Project Settings.


#include "SubSystems/Construction/DSConstructionLibrary.h"
#include "SubSystems/MVC/Model/Custom/DSCupboardModel.h"
#include "SubSystems/MVC/Library/DSToolLibrary.h"
#include "SubSystems/MVC/View/Custom/DSCupboardBaseView.h"
#include "GeometricCalculate/Library/GeometryLibrary.h"
#include "GeometryAlgorithms/Public/Curve/PolygonIntersectionUtils.h"
#include "Clipper2/Library/Clipper2Library.h"
#include "Subsystems/MVC/Model/Custom/Library/DSCupboardLibrary.h"
#include "SubSystems/MVC/Model/House/HouseFurniture/DSHouseWinDoorModel.h"
#include "SubSystems/MVC/Model/House/Wall/DSHouseWallModel.h"
#include "SubSystems/MVC/Model/House/Area/DSHouseAreaModel.h"
#include "SubSystems/MVC/Core/Property/HouseAreaProperty.h"
#include "Clipper2/Core/clipper2/clipper.h"
#include "Clipper2/Core/clipper2/clipper.core.h"
#include "SubSystems/Library/DesignStationFunctionLibrary.h"
#include "Algo/Reverse.h"
#include "SubSystems/MVC/Library/CupBoardDoorLibrary.h"

using namespace New_Clipper2Lib;


void UDSConstructionLibrary::GetBoxOutlines(const FBox& InBox, TArray<TArray<FVector>>& OutOutlines)
{
	FVector Points[8];
	InBox.GetVertices(Points);


	/**
	 * GetVertices Return 8 points.
	 * Vertices order :
	 *
	 *       1+++++++++++5
	 *      + +         ++
	 *     +  +        + +
	 *    +   +       +  +
	 *
	 *   6+++++++++++7   +
	 *   +    +      +   +
	 *   +    0++++++++++3
	 *   +   +       +  +
	 *   + +         + +
	 *   2+++++++++++4
	 */


	OutOutlines.AddDefaulted(6);
	OutOutlines[0].Append({ Points[1],Points[5],Points[7],Points[6] });   //上
	OutOutlines[1].Append({ Points[0],Points[2],Points[4],Points[3] });   //下
	OutOutlines[2].Append({ Points[1],Points[6],Points[2],Points[0] });   //左
	OutOutlines[3].Append({ Points[5],Points[3],Points[4],Points[7] });   //右
	OutOutlines[4].Append({ Points[6],Points[7],Points[4],Points[2] });   //前
	OutOutlines[5].Append({ Points[5],Points[1],Points[0],Points[3] });   //后
}

static void GetCupboardNode6Outline_Inner(TArray<AImportPakBaseClass*> InActors, TArray<FDSPrimitive_MPolygon3D>& OutMPolygon3D)
{
	float ThresholdValue = FMath::Cos(FMath::DegreesToRadians(89.5));
	OutMPolygon3D.Empty();
	OutMPolygon3D.AddDefaulted(6);

	for(int32 i=0;i<InActors.Num();++i)
	{
		auto ActorIte = InActors[i];
		FBox ActorBox = ActorIte->CalculateComponentsBoundingBoxInLocalSpace(false,false);

		TArray<TArray<FVector>> OutOutlines;
		UDSConstructionLibrary::GetBoxOutlines(ActorBox, OutOutlines);

		//计算Actor的变换矩阵
		FTransform CompT = ActorIte->GetRootComponent()->GetRelativeTransform();
		USceneComponent* AttachComp = ActorIte->GetRootComponent();
		while (AttachComp->GetAttachParent() != nullptr)
		{
			AttachComp = AttachComp->GetAttachParent();
			if (AttachComp->GetAttachParent() != nullptr)
			{
				CompT *= AttachComp->GetRelativeTransform();
			}
		}

		for (int32 i = 0; i < OutOutlines.Num(); ++i)
		{
			auto& Plane = OutOutlines[i];

			for (auto& PlaneIte : Plane)
			{
				PlaneIte = CompT.TransformPosition(PlaneIte);
			}

			FDSPrimitive_Polygon3D NewPolygon;
			NewPolygon.Outter = Plane;

			FVector FaceNormal = FVector::CrossProduct(Plane[1] - Plane[0], Plane[3] - Plane[0]).GetSafeNormal();

			if (FVector::DotProduct(FaceNormal, FVector::ZAxisVector) > ThresholdValue)
			{//上
				OutMPolygon3D[0].FaceNormal = FVector::ZAxisVector;
				OutMPolygon3D[0].Polygons.Add(MoveTemp(NewPolygon));
			}
			if (FVector::DotProduct(FaceNormal, -FVector::ZAxisVector) > ThresholdValue)
			{//下
				OutMPolygon3D[1].FaceNormal = FVector::ZAxisVector;
				OutMPolygon3D[1].Polygons.Add(MoveTemp(NewPolygon));
			}
			if (FVector::DotProduct(FaceNormal, -FVector::XAxisVector) > ThresholdValue)
			{//左
				OutMPolygon3D[2].FaceNormal = FVector::ZAxisVector;
				OutMPolygon3D[2].Polygons.Add(MoveTemp(NewPolygon));
			}
			if (FVector::DotProduct(FaceNormal, FVector::XAxisVector) > ThresholdValue)
			{//右
				OutMPolygon3D[3].FaceNormal = FVector::ZAxisVector;
				OutMPolygon3D[3].Polygons.Add(MoveTemp(NewPolygon));
			}
			if (FVector::DotProduct(FaceNormal, FVector::YAxisVector) > ThresholdValue)
			{//前
				OutMPolygon3D[4].FaceNormal = FVector::ZAxisVector;
				OutMPolygon3D[4].Polygons.Add(MoveTemp(NewPolygon));
			}
			if (FVector::DotProduct(FaceNormal, -FVector::YAxisVector) > ThresholdValue)
			{//后
				OutMPolygon3D[5].FaceNormal = FVector::ZAxisVector;

				OutMPolygon3D[5].Polygons.Add(MoveTemp(NewPolygon));
			}
		}
	}
}

static void GetCupboardNode6MeshOutline_Inner(TArray<UVolatileMeshComponent*> InMeshComponents,
	TArray<FTransform> InMeshToNodeTs, TArray<FDSPrimitive_MPolygon3D>& OutMPolygon3D)
{
	float ThresholdValue = FMath::Cos(FMath::DegreesToRadians(89.5));

	TArray<FBox> MeshBoxs;
	TArray< TArray<TArray<FVector2D> >> Polygons;
	TArray<FVector> Normals;
	MeshBoxs.AddDefaulted(6);
	Polygons.AddDefaulted(6);
	OutMPolygon3D.AddDefaulted(6);

	for (int32 i = 0; i < InMeshComponents.Num(); ++i)
	{
		auto VolatileComponent = InMeshComponents[i];
		//FTransform T = VolatileComponent->GetRelativeTransform();   //把顶点转换到父空间
		FTransform T = InMeshToNodeTs[i];

		for (int32 i = 0; i < VolatileComponent->GetNumSections(); ++i)
		{
			auto MeshSection = VolatileComponent->GetProcMeshSection(i);
			if (MeshSection != nullptr)
			{
				for (int32 Index = 0; Index < MeshSection->ProcIndexBuffer.Num(); Index += 3)
				{
					FVector Pos01 = T.TransformPosition(MeshSection->ProcVertexBuffer[MeshSection->ProcIndexBuffer[Index]].Position);
					FVector Pos02 = T.TransformPosition(MeshSection->ProcVertexBuffer[MeshSection->ProcIndexBuffer[Index + 1]].Position);
					FVector Pos03 = T.TransformPosition(MeshSection->ProcVertexBuffer[MeshSection->ProcIndexBuffer[Index + 2]].Position);

					//FVector Pos01 = MeshSection->ProcVertexBuffer[MeshSection->ProcIndexBuffer[Index]].Position;
					//FVector Pos02 = MeshSection->ProcVertexBuffer[MeshSection->ProcIndexBuffer[Index + 1]].Position;
					//FVector Pos03 = MeshSection->ProcVertexBuffer[MeshSection->ProcIndexBuffer[Index + 2]].Position;

					FVector CrossV = FVector::CrossProduct(Pos03 - Pos01, Pos02 - Pos01);
					FVector FaceNormal = CrossV.GetSafeNormal();

					if (FVector::DotProduct(FaceNormal, FVector::ZAxisVector) > ThresholdValue)
					{//上
						//Normals[0] = T.TransformVector(FVector::ZAxisVector);
						OutMPolygon3D[0].FaceNormal = FVector::ZAxisVector;

						TArray<FVector2D> Polygon{ FVector2D(Pos03.X,Pos03.Y),FVector2D(Pos02.X,Pos02.Y),FVector2D(Pos01.X,Pos01.Y) };
						int32 NewIndex = Polygons[0].Add(MoveTemp(Polygon));

						MeshBoxs[0] += FBox({ Pos01, Pos02, Pos03 });
					}
					if (FVector::DotProduct(FaceNormal, -FVector::ZAxisVector) > ThresholdValue)
					{//下
						//Normals[1] = T.TransformVector(-FVector::ZAxisVector);
						OutMPolygon3D[1].FaceNormal = -FVector::ZAxisVector;

						//TArray<FVector2D> Polygon{ FVector2D(Pos03.X,Pos03.Y),FVector2D(Pos02.X,Pos02.Y),FVector2D(Pos01.X,Pos01.Y) };
						TArray<FVector2D> Polygon{ FVector2D(Pos01.X,Pos01.Y),FVector2D(Pos02.X,Pos02.Y),FVector2D(Pos03.X,Pos03.Y) };
						int32 NewIndex = Polygons[1].Add(MoveTemp(Polygon));

						MeshBoxs[1] += FBox({ Pos01, Pos02, Pos03 });
					}
					if (FVector::DotProduct(FaceNormal, -FVector::XAxisVector) > ThresholdValue)
					{//左
						//Normals[2] = T.TransformVector(-FVector::XAxisVector);
						OutMPolygon3D[2].FaceNormal = -FVector::XAxisVector;

						//TArray<FVector2D> Polygon{ FVector2D(Pos03.Y, Pos03.Z),FVector2D(Pos02.Y,Pos02.Z),FVector2D(Pos01.Y,Pos01.Z) };
						TArray<FVector2D> Polygon{ FVector2D(Pos01.Y, Pos01.Z),FVector2D(Pos02.Y,Pos02.Z),FVector2D(Pos03.Y,Pos03.Z) };
						int32 NewIndex = Polygons[2].Add(MoveTemp(Polygon));

						MeshBoxs[2] += FBox({ Pos01, Pos02, Pos03 });
					}
					if (FVector::DotProduct(FaceNormal, FVector::XAxisVector) > ThresholdValue)
					{//右
						//Normals[3] = T.TransformVector(FVector::XAxisVector);
						OutMPolygon3D[3].FaceNormal = FVector::XAxisVector;

						TArray<FVector2D> Polygon{ FVector2D(Pos03.Y, Pos03.Z),FVector2D(Pos02.Y,Pos02.Z),FVector2D(Pos01.Y,Pos01.Z) };
						int32 NewIndex = Polygons[3].Add(MoveTemp(Polygon));

						MeshBoxs[3] += FBox({ Pos01, Pos02, Pos03 });
					}
					if (FVector::DotProduct(FaceNormal, FVector::YAxisVector) > ThresholdValue)
					{//前
						//Normals[4] = T.TransformVector(FVector::YAxisVector);
						OutMPolygon3D[4].FaceNormal = FVector::YAxisVector;

						TArray<FVector2D> Polygon{ FVector2D(Pos03.X,Pos03.Z),FVector2D(Pos02.X,Pos02.Z),FVector2D(Pos01.X,Pos01.Z) };
						int32 NewIndex = Polygons[4].Add(MoveTemp(Polygon));

						MeshBoxs[4] += FBox({ Pos01, Pos02, Pos03 });
					}
					if (FVector::DotProduct(FaceNormal, -FVector::YAxisVector) > ThresholdValue)
					{//后
						//Normals[5] = T.TransformVector(-FVector::YAxisVector);
						OutMPolygon3D[5].FaceNormal = -FVector::YAxisVector;

						//TArray<FVector2D> Polygon{ FVector2D(Pos03.X,Pos03.Z),FVector2D(Pos02.X,Pos02.Z),FVector2D(Pos01.X,Pos01.Z) };
						TArray<FVector2D> Polygon{ FVector2D(Pos01.X,Pos01.Z),FVector2D(Pos02.X,Pos02.Z),FVector2D(Pos03.X,Pos03.Z) };
						int32 NewIndex = Polygons[5].Add(MoveTemp(Polygon));

						MeshBoxs[5] += FBox({ Pos01, Pos02, Pos03 });
					}
				}
			}
		}

	}

	//上
	int32 FaceIndex = 0;
	if (Polygons[FaceIndex].Num() > 0)
	{
		float Area = 0.0f;
		//TArray<TArray<FVector2D>> ReslutPaths = FClipper2Library::PolygonUnion03(Polygons[0], { Polygons[0][0] }, &Area);

		TArray<FDSPolygon2DHasHole> Paths;
		FClipper2Library::PolygonUnion04(Polygons[FaceIndex], { Polygons[FaceIndex][0] }, Paths, &Area);
		FDSPrimitive_MPolygon3D ResultPaths;
		ResultPaths.FaceIndex = FaceIndex;
		ResultPaths.FaceNormal = OutMPolygon3D[0].FaceNormal;
		for (auto& Ite : Paths)
		{
			FDSPrimitive_Polygon3D Poly;

			for (auto& OuterIte : Ite.Outer)
			{
				Poly.Outter.Add(FVector(OuterIte.X, OuterIte.Y, MeshBoxs[FaceIndex].Max.Z));
			}

			for (auto& HoleIte : Ite.Holes)
			{
				int32 HoleIndex = Poly.Holes.AddDefaulted();
				for (auto& HolePoint : HoleIte)
				{
					Poly.Holes[HoleIndex].Add(FVector(HolePoint.X, HolePoint.Y, MeshBoxs[FaceIndex].Max.Z));
				}
			}

			ResultPaths.Polygons.Add(MoveTemp(Poly));
		}

		OutMPolygon3D[0] = MoveTemp(ResultPaths);
	}

	//下
	FaceIndex = 1;
	if (Polygons[FaceIndex].Num() > 0)
	{
		float Area = 0.0f;
		//TArray<TArray<FVector2D>> ReslutPaths = FClipper2Library::PolygonUnion03(Polygons[1], { Polygons[1][0] }, &Area);
		TArray<FDSPolygon2DHasHole> Paths;
		FClipper2Library::PolygonUnion04(Polygons[FaceIndex], { Polygons[FaceIndex][0] }, Paths, &Area);

		FDSPrimitive_MPolygon3D ResultPaths;
		ResultPaths.FaceIndex = FaceIndex;
		ResultPaths.FaceNormal = OutMPolygon3D[1].FaceNormal;
		for (auto& Ite : Paths)
		{
			FDSPrimitive_Polygon3D Poly;

			for (auto& OuterIte : Ite.Outer)
			{
				Poly.Outter.Add(FVector(OuterIte.X, OuterIte.Y, MeshBoxs[FaceIndex].Min.Z));
			}

			Algo::Reverse(Poly.Outter);

			for (auto& HoleIte : Ite.Holes)
			{
				int32 HoleIndex = Poly.Holes.AddDefaulted();
				for (auto& HolePoint : HoleIte)
				{
					Poly.Holes[HoleIndex].Add(FVector(HolePoint.X, HolePoint.Y, MeshBoxs[FaceIndex].Min.Z));
				}
				Algo::Reverse(Poly.Holes[HoleIndex]);
			}

			ResultPaths.Polygons.Add(MoveTemp(Poly));
		}
		OutMPolygon3D[1] = MoveTemp(ResultPaths);
	}

	//左
	FaceIndex = 2;
	if (Polygons[FaceIndex].Num() > 0)
	{
		float Area = 0.0f;
		//TArray<TArray<FVector2D>> ReslutPaths = FClipper2Library::PolygonUnion03(Polygons[0], { Polygons[0][0] }, &Area);

		TArray<FDSPolygon2DHasHole> Paths;
		FClipper2Library::PolygonUnion04(Polygons[FaceIndex], { Polygons[FaceIndex][0] }, Paths, &Area);
		FDSPrimitive_MPolygon3D ResultPaths;
		ResultPaths.FaceIndex = FaceIndex;
		ResultPaths.FaceNormal = OutMPolygon3D[2].FaceNormal;
		for (auto& Ite : Paths)
		{
			FDSPrimitive_Polygon3D Poly;

			for (auto& OuterIte : Ite.Outer)
			{
				Poly.Outter.Add(FVector(MeshBoxs[FaceIndex].Min.X, OuterIte.X, OuterIte.Y));
			}
			Algo::Reverse(Poly.Outter);
			for (auto& HoleIte : Ite.Holes)
			{
				int32 HoleIndex = Poly.Holes.AddDefaulted();
				for (auto& HolePoint : HoleIte)
				{
					Poly.Holes[HoleIndex].Add(FVector(MeshBoxs[FaceIndex].Min.X, HolePoint.X, HolePoint.Y));
				}
				Algo::Reverse(Poly.Holes[HoleIndex]);
			}

			ResultPaths.Polygons.Add(MoveTemp(Poly));
		}
		OutMPolygon3D[2] = MoveTemp(ResultPaths);
	}

	//右
	FaceIndex = 3;
	if (Polygons[FaceIndex].Num() > 0)
	{
		float Area = 0.0f;
		//TArray<TArray<FVector2D>> ReslutPaths = FClipper2Library::PolygonUnion03(Polygons[0], { Polygons[0][0] }, &Area);

		TArray<FDSPolygon2DHasHole> Paths;
		FClipper2Library::PolygonUnion04(Polygons[FaceIndex], { Polygons[FaceIndex][0] }, Paths, &Area);
		FDSPrimitive_MPolygon3D ResultPaths;
		ResultPaths.FaceIndex = FaceIndex;
		ResultPaths.FaceNormal = OutMPolygon3D[3].FaceNormal;
		for (auto& Ite : Paths)
		{
			FDSPrimitive_Polygon3D Poly;

			for (auto& OuterIte : Ite.Outer)
			{
				Poly.Outter.Add(FVector(MeshBoxs[FaceIndex].Max.X, OuterIte.X, OuterIte.Y));
			}

			for (auto& HoleIte : Ite.Holes)
			{
				int32 HoleIndex = Poly.Holes.AddDefaulted();
				for (auto& HolePoint : HoleIte)
				{
					Poly.Holes[HoleIndex].Add(FVector(MeshBoxs[FaceIndex].Max.X, HolePoint.X, HolePoint.Y));
				}
			}

			ResultPaths.Polygons.Add(MoveTemp(Poly));
		}
		OutMPolygon3D[3] = MoveTemp(ResultPaths);
	}

	//前
	FaceIndex = 4;
	if (Polygons[FaceIndex].Num() > 0)
	{
		float Area = 0.0f;
		//TArray<TArray<FVector2D>> ReslutPaths = FClipper2Library::PolygonUnion03(Polygons[0], { Polygons[0][0] }, &Area);

		TArray<FDSPolygon2DHasHole> Paths;
		FClipper2Library::PolygonUnion04(Polygons[FaceIndex], { Polygons[FaceIndex][0] }, Paths, &Area);
		FDSPrimitive_MPolygon3D ResultPaths;
		ResultPaths.FaceIndex = FaceIndex;
		ResultPaths.FaceNormal = OutMPolygon3D[4].FaceNormal;
		for (auto& Ite : Paths)
		{
			FDSPrimitive_Polygon3D Poly;

			for (auto& OuterIte : Ite.Outer)
			{
				Poly.Outter.Add(FVector(OuterIte.X, MeshBoxs[FaceIndex].Max.Y, OuterIte.Y));
			}
			Algo::Reverse(Poly.Outter);
			for (auto& HoleIte : Ite.Holes)
			{
				int32 HoleIndex = Poly.Holes.AddDefaulted();
				for (auto& HolePoint : HoleIte)
				{
					Poly.Holes[HoleIndex].Add(FVector(HolePoint.X, MeshBoxs[FaceIndex].Max.Y, HolePoint.Y));
				}
				Algo::Reverse(Poly.Holes[HoleIndex]);
			}

			ResultPaths.Polygons.Add(MoveTemp(Poly));
		}
		OutMPolygon3D[4] = MoveTemp(ResultPaths);
	}

	//后
	FaceIndex = 5;
	if (Polygons[FaceIndex].Num() > 0)
	{
		float Area = 0.0f;
		//TArray<TArray<FVector2D>> ReslutPaths = FClipper2Library::PolygonUnion03(Polygons[0], { Polygons[0][0] }, &Area);

		TArray<FDSPolygon2DHasHole> Paths;
		FClipper2Library::PolygonUnion04(Polygons[FaceIndex], { Polygons[FaceIndex][0] }, Paths, &Area);
		FDSPrimitive_MPolygon3D ResultPaths;
		ResultPaths.FaceIndex = FaceIndex;
		ResultPaths.FaceNormal = OutMPolygon3D[5].FaceNormal;
		for (auto& Ite : Paths)
		{
			FDSPrimitive_Polygon3D Poly;

			for (auto& OuterIte : Ite.Outer)
			{
				Poly.Outter.Add(FVector(OuterIte.X, MeshBoxs[FaceIndex].Min.Y, OuterIte.Y));
			}

			for (auto& HoleIte : Ite.Holes)
			{
				int32 HoleIndex = Poly.Holes.AddDefaulted();
				for (auto& HolePoint : HoleIte)
				{
					Poly.Holes[HoleIndex].Add(FVector(HolePoint.X, MeshBoxs[FaceIndex].Min.Y, HolePoint.Y));
				}

			}

			ResultPaths.Polygons.Add(MoveTemp(Poly));
		}
		OutMPolygon3D[5] = MoveTemp(ResultPaths);
	}

}

static void GetCupboardNode6MeshOutline_Inner(TArray<UVolatileMeshComponent*> InMeshComponents,
	TArray<FTransform> InMeshToNodeTs, const FTransform& InNodeToProjective, TArray<FDSPrimitive_MPolygon3D>& OutMPolygon3D)
{
	GetCupboardNode6MeshOutline_Inner(InMeshComponents, InMeshToNodeTs, OutMPolygon3D);

	for (int32 i = 0; i < OutMPolygon3D.Num(); ++i)
	{
		OutMPolygon3D[i].FaceNormal = InNodeToProjective.TransformVector(OutMPolygon3D[i].FaceNormal).GetSafeNormal();

		for (auto& PolyIte : OutMPolygon3D[i].Polygons)
		{
			for (auto& PointIte : PolyIte.Outter)
			{
				PointIte = InNodeToProjective.TransformPosition(PointIte);
			}

			for (auto& HoleIte : PolyIte.Holes)
			{
				for (auto& PointIte : HoleIte)
				{
					PointIte = InNodeToProjective.TransformPosition(PointIte);
				}
			}
		}
	}
}


static void Polygon2DHasHoleToMPolygon3D(const TArray<FDSPolygon2DHasHole>& InA, FDSPrimitive_MPolygon3D& OutB)
{

}

bool UDSConstructionLibrary::CalcConstructionPrimitive(TArray<UVolatileMeshComponent*> InMeshComponents, const FTransform& InWorldToProjective, FDSPrimitive_MPolygon3D& OutPrimitive)
{
	//FVector ViewDir = InWorldToProjective.GetUnitAxis(EAxis::Z);
	FVector ViewDir = FVector::ZAxisVector;
	for (UVolatileMeshComponent* VolatileComponent : InMeshComponents)
	{
		FTransform T = VolatileComponent->GetComponentTransform() * InWorldToProjective;

		FBox MeshBox(EForceInit::ForceInitToZero);
		float MaxArea = 0.0f;
		TArray<TArray<FVector2D>> Polygons;
		int32 MaxIndex = 0;
		for (int32 i = 0; i < VolatileComponent->GetNumSections(); ++i)
		{
			auto MeshSection = VolatileComponent->GetProcMeshSection(i);
			if (MeshSection != nullptr)
			{
				for (int32 Index = 0; Index < MeshSection->ProcIndexBuffer.Num(); Index += 3)
				{
					FVector Pos01 = T.TransformPosition(MeshSection->ProcVertexBuffer[MeshSection->ProcIndexBuffer[Index]].Position);
					FVector Pos02 = T.TransformPosition(MeshSection->ProcVertexBuffer[MeshSection->ProcIndexBuffer[Index + 1]].Position);
					FVector Pos03 = T.TransformPosition(MeshSection->ProcVertexBuffer[MeshSection->ProcIndexBuffer[Index + 2]].Position);

					//FVector Normal01 = MeshSection->ProcVertexBuffer[Index].Normal;
					//FVector Normal02 = MeshSection->ProcVertexBuffer[Index + 1].Normal;
					//FVector Normal03 = MeshSection->ProcVertexBuffer[Index + 2].Normal;

					FVector CrossV = FVector::CrossProduct(Pos03 - Pos01, Pos02 - Pos01);
					FVector FaceNormal = CrossV.GetSafeNormal();
					float TempValue = FMath::Cos(FMath::DegreesToRadians(89.5));
					if (FVector::DotProduct(FaceNormal, ViewDir) > TempValue)
					{
						//TArray<FVector> Polygon{ Pos03,Pos02,Pos01 };
						TArray<FVector2D> Polygon{ FVector2D(Pos03.X,Pos03.Y),FVector2D(Pos02.X,Pos02.Y),FVector2D(Pos01.X,Pos01.Y) };
						int32 NewIndex = Polygons.Add(MoveTemp(Polygon));

						MeshBox += FBox({ Pos01, Pos02, Pos03 });

						float Area = 0.5f * CrossV.Size();
						if (Area > MaxArea)
						{
							MaxIndex = NewIndex;
						}
					}
				}
			}
		}

		if (Polygons.Num() > 0)
		{
			TArray<FVector2D> Polygon = Polygons[MaxIndex];
			float Area = 0.0f;
			TArray<FDSPolygon2DHasHole> ResultPaths;
			FClipper2Library::PolygonUnion04(Polygons, { Polygon }, ResultPaths, &Area);
			Polygon2DHasHoleToMPolygon3D(ResultPaths, OutPrimitive);

			return OutPrimitive.Polygons.Num() > 0 && OutPrimitive.Polygons[0].Outter.Num() > 0;
			//OutPrimitive.Points.Append(FClipper2Library::PolygonUnion02(Polygons, { Polygon }, MeshBox.GetCenter().Z,&OutPrimitive.Area));
			//return OutPrimitive.Points.Num() > 0 && OutPrimitive.Points[0].Num() >= 3;
		}
	}

	return false;
}

/*
void UDSConstructionLibrary::GetConstructionPrimitive_TheModel(UDSBaseModel* InModel, TArray<TSharedPtr<FDSPrimitiveBase>>& OutPrimitive,
	const FTransform &InWorldToProjective)
{
	EDSModelType ModelType = InModel->GetModelType();
	if (UDSToolLibrary::IsCustomType(InModel))
	{
		UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(InModel);
		TArray<FDSPrimitive_MPolygon3D> MPolygon3D;
		GetCupboardNode6MeshOutline(CupboardModel, InWorldToProjective, MPolygon3D);

		//返回朝向Z轴正方向的面
		for (auto& MPIte : MPolygon3D)
		{
			float ThresholdValue = FMath::Cos(FMath::DegreesToRadians(89.5));
			if (FVector::DotProduct(MPIte.FaceNormal, FVector::ZAxisVector) > ThresholdValue)
			{
				OutPrimitive.Add(MakeShareable(new FDSPrimitive_MPolygon3D(MPIte)));
			}
		}

	}
	else if (ModelType == EDSModelType::E_House_Wall)
	{
		UDSHouseWallModel* WallModel = Cast<UDSHouseWallModel>(InModel);
		TArray<FVector> Outlines = WallModel->GetBottomOutline();
		if (Outlines.Num() >= 3)
		{
			TSharedPtr<FDSPrimitive_Polygon> NewPrimitive = MakeShareable(new FDSPrimitive_Polygon());

			for (auto& PointIte : Outlines)
			{
				NewPrimitive->Points.Add(InWorldToProjective.TransformPosition(PointIte));
			}

			OutPrimitive.Add(NewPrimitive);
		}
	}
}
*/

void UDSConstructionLibrary::GetConstructionPrimitive_TheModel(UDSBaseModel* InModel, TArray<TSharedPtr<FDSPrimitiveBase>>& OutPrimitive)
{
	EDSModelType ModelType = InModel->GetModelType();
	if (UDSToolLibrary::IsCustomType(InModel))
	{
		UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(InModel);
		TArray<FDSPrimitive_MPolygon3D> MPolygon3D;
		GetCupboardNode6MeshOutline(CupboardModel, FTransform::Identity, MPolygon3D);

		for (auto& MPIte : MPolygon3D)
		{
			OutPrimitive.Add(MakeShareable(new FDSPrimitive_MPolygon3D(MPIte)));
		}

	}
	else if (ModelType == EDSModelType::E_House_Wall)
	{
		UDSHouseWallModel* WallModel = Cast<UDSHouseWallModel>(InModel);
		TArray<FVector> Outlines = WallModel->GetBottomOutline();
		if (Outlines.Num() >= 3)
		{
			TSharedPtr<FDSPrimitive_MPolygonHatch> NewPrimitive = MakeShareable(new FDSPrimitive_MPolygonHatch());
			NewPrimitive->MPolygon.Points.Add (Outlines);
			OutPrimitive.Add(NewPrimitive);
		}
	}
}

void UDSConstructionLibrary::Polygon2DBoolean_Union(const TArray<FVector2D>& InA, const TArray<FVector2D>& InB, TArray<TArray<FVector2D>>& OutResult)
{
	UE::Geometry::FPolygon2d Poly2dA(InA);
	UE::Geometry::FPolygon2d Poly2dB(InB);
	double Area = 0.0f;

	UE::Geometry::FGeneralPolygon2d PolyA;
	PolyA.SetOuter(Poly2dA);

	UE::Geometry::FGeneralPolygon2d PolyB;
	PolyB.SetOuter(Poly2dB);

	UE::Geometry::TBooleanPolygon2Polygon2<UE::Geometry::EPolygonBooleanOp::Union, UE::Geometry::FGeneralPolygon2d, double> IntersectPolygon(PolyA, PolyB);
	bool bRes = IntersectPolygon.ComputeResult();
	if (bRes)
	{
		for (auto& Ite : IntersectPolygon.Result) 
		{
			auto& Outer = Ite.GetOuter();
			if (Outer.IsClockwise())
			{
				OutResult.Add(Outer.GetVertices());
			}
			else
			{
				TArray<FVector2D> ReversedVertices;
				for (int32 Idx = Outer.VertexCount() - 1; Idx >= 0; --Idx)
				{
					ReversedVertices.Add(Outer[Idx]);
				}
				OutResult.Add(ReversedVertices);
			}
		}
	}
}

void UDSConstructionLibrary::GetConstructionPrimitive_NoModelNode(UDSCupboardModel* InModel,
	TMap<TSharedPtr<FMultiComponentDataItem>, TArray<FDSPrimitive_MPolygon3D>>& OutNodeData, 
	FOnCheckIsRightNode InCheck)
{
	ADSCupboardBaseView* CupboardView = Cast<ADSCupboardBaseView>(InModel->GetOwnedView());
	if (CupboardView == nullptr)
		return;

	TMap<TSharedPtr<FMultiComponentDataItem>, TArray<UVolatileMeshComponent*>> NodeVolatileComponentsMap;
	TMap<TSharedPtr<FMultiComponentDataItem>, TArray<FTransform>> NodeTransfromMap;

	TArray<UVolatileMeshComponent*> VolatileComponents;
	CupboardView->GetComponents<UVolatileMeshComponent>(VolatileComponents);
	for (UVolatileMeshComponent* VolatileComponent : VolatileComponents)
	{
		FName* TagName = VolatileComponent->ComponentTags.FindByPredicate(
			[](const FName& Tag)
			{
				return Tag.ToString().Find(TEXT("NodeUUID_")) == 0;
			}
		);

		if (TagName != nullptr)
		{
			FString TagString = (*TagName).ToString();
			FString NodeUUID = TagString.Right(TagString.Len() - FString(TEXT("NodeUUID_")).Len());
			if (!NodeUUID.IsEmpty())
			{
				auto FindNode = UDSCupboardLibrary::GetNodeByNodeUUID(InModel->GetComponentTreeDataRef(), NodeUUID);
				if (FindNode != nullptr)
				{
					TArray<TSharedPtr<FMultiComponentDataItem>> OutNodes;
					InModel->CollectComponentPath_Public(InModel->GetComponentTreeDataRef(), FindNode, OutNodes);

					FTransform ComponentRelativeTransform;

					TSharedPtr<FMultiComponentDataItem> TargetNode;
					for (int32 i = OutNodes.Num() - 1; i >= 0; --i)
					{
						FTransform CurrentTransform;
						CurrentTransform.SetLocation(OutNodes[i]->ComponentLocation.GetLocation());
						CurrentTransform.SetRotation(OutNodes[i]->ComponentRotation.GetRotation().Quaternion());
						CurrentTransform.SetScale3D(OutNodes[i]->ComponentScale.GetScale());

						if (InCheck.Execute(OutNodes[i]))
						{
							TargetNode = OutNodes[i];
							break;
						}

						ComponentRelativeTransform = ComponentRelativeTransform * CurrentTransform;
					}

					if (TargetNode.IsValid())
					{
						if (!NodeVolatileComponentsMap.FindOrAdd(TargetNode).Contains(VolatileComponent))
						{
							NodeVolatileComponentsMap.FindOrAdd(TargetNode).Add(VolatileComponent);

							FTransform CompT = VolatileComponent->GetRelativeTransform();

							USceneComponent* AttachComp = VolatileComponent;
							while (AttachComp->GetAttachParent() != nullptr)
							{
								AttachComp = AttachComp->GetAttachParent();
								if (AttachComp->GetAttachParent() != nullptr)
								{
									CompT *= AttachComp->GetRelativeTransform();
								}
							}

							NodeTransfromMap.FindOrAdd(TargetNode).Add(CompT);
						}

					}
				}
			}
		}
	}

	TMap<TSharedPtr<FMultiComponentDataItem>, TArray<AImportPakBaseClass*>> ActorMap;
	TArray<AImportPakBaseClass*> AllPakActors = CupboardView->GetCustomMeshInfo().ImportActors;
	for (auto ActorIte : AllPakActors)
	{
		FName* TagName = ActorIte->Tags.FindByPredicate(
			[](const FName& Tag)
			{
				return Tag.ToString().Find(TEXT("NodeUUID_")) == 0;
			}
		);

		if (TagName != nullptr)
		{
			FString TagString = (*TagName).ToString();
			FString NodeUUID = TagString.Right(TagString.Len() - FString(TEXT("NodeUUID_")).Len());
			if (!NodeUUID.IsEmpty())
			{
				auto FindNode = UDSCupboardLibrary::GetNodeByNodeUUID(InModel->GetComponentTreeDataRef(), NodeUUID);
				if (FindNode != nullptr)
				{
					TArray<TSharedPtr<FMultiComponentDataItem>> OutNodes;
					InModel->CollectComponentPath_Public(InModel->GetComponentTreeDataRef(), FindNode, OutNodes);

					TSharedPtr<FMultiComponentDataItem> TargetNode;
					for (int32 i = OutNodes.Num() - 1; i >= 0; --i)
					{
						if (InCheck.Execute(OutNodes[i]))
						{
							TargetNode = OutNodes[i];
							break;
						}
					}

					if (TargetNode.IsValid())
					{
						if (!ActorMap.FindOrAdd(TargetNode).Contains(ActorIte))
						{
							ActorMap.FindOrAdd(TargetNode).Add(ActorIte);

							//FTransform CompT = ActorIte->GetRootComponent()->GetRelativeTransform();

							//USceneComponent* AttachComp = ActorIte->GetRootComponent();
							//while (AttachComp->GetAttachParent() != nullptr)
							//{
							//	AttachComp = AttachComp->GetAttachParent();
							//	if (AttachComp->GetAttachParent() != nullptr)
							//	{
							//		CompT *= AttachComp->GetRelativeTransform();
							//	}
							//}

							//NodeTransfromMap.FindOrAdd(TargetNode).Add(CompT);
						}

					}

				}
			}
		}

	}

	//FTransform T = InModel->GetRootCupboardModel()->GetOwnedView()->GetActorTransform() * InWorldToProjective;
	FTransform T = InModel->GetRootCupboardModel()->GetOwnedView()->GetActorTransform();

	for (auto& Ite : NodeVolatileComponentsMap)
	{
		TArray<FDSPrimitive_MPolygon3D> MPolyS;
		GetCupboardNode6MeshOutline_Inner(Ite.Value, NodeTransfromMap[Ite.Key], T, MPolyS);
		if (MPolyS.Num() > 0)
		{
			/*
			//返回朝向Z轴正方向的面
			TArray<FDSPrimitive_MPolygon3D> TempMPolyS;
			for (auto& MPIte : MPolyS)
			{
				float ThresholdValue = FMath::Cos(FMath::DegreesToRadians(89.5));
				if (FVector::DotProduct(MPIte.FaceNormal, FVector::ZAxisVector) > ThresholdValue)
				{
					TempMPolyS.Add(MPIte);
				}
			}
			*/

			OutNodeData.Add(Ite.Key, MoveTemp(MPolyS));
		}
	}

	for (auto& Ite : ActorMap)
	{
		TArray<FDSPrimitive_MPolygon3D> MPolyS;
		GetCupboardNode6Outline_Inner(Ite.Value, MPolyS);
		for(int32 i=0;i< MPolyS.Num();++i)
		{
			OutNodeData.FindOrAdd(Ite.Key)[i].Polygons.Append(MPolyS[i].Polygons);
		}
	}
}

void UDSConstructionLibrary::GetNoModelNodeInModel(UDSCupboardModel* InModel, TArray< TSharedPtr<FMultiComponentDataItem>>& OutNodes,
	TArray<UVolatileMeshComponent*>& OutMeshComponents)
{
	ADSCupboardBaseView* CupboardView = Cast<ADSCupboardBaseView>(InModel->GetOwnedView());
	if (CupboardView == nullptr)
		return;

	TMap<TSharedPtr<FMultiComponentDataItem>, TArray<UVolatileMeshComponent*>> NodeVolatileComponentsMap;
	TMap<TSharedPtr<FMultiComponentDataItem>, TArray<FTransform>> NodeTransfromMap;

	TArray<UVolatileMeshComponent*> VolatileComponents;
	CupboardView->GetComponents<UVolatileMeshComponent>(VolatileComponents);
	for (UVolatileMeshComponent* VolatileComponent : VolatileComponents)
	{
		FName* TagName = VolatileComponent->ComponentTags.FindByPredicate(
			[](const FName& Tag)
			{
				return Tag.ToString().Find(TEXT("NodeUUID_")) == 0;
			}
		);

		if (TagName != nullptr)
		{
			FString TagString = (*TagName).ToString();
			FString NodeUUID = TagString.Right(TagString.Len() - FString(TEXT("NodeUUID_")).Len());
			if (!NodeUUID.IsEmpty())
			{
				auto FindNode = UDSCupboardLibrary::GetNodeByNodeUUID(InModel->GetComponentTreeDataRef(), NodeUUID);
				if (FindNode != nullptr)
				{
					OutNodes.Add(FindNode);
					OutMeshComponents.Add(VolatileComponent);
				}
			}
		}
	}
}

FTransform UDSConstructionLibrary::GetComponentLocalTransfrom(UVolatileMeshComponent* InCom)
{
	FTransform CompT = InCom->GetRelativeTransform();

	USceneComponent* AttachComp = InCom;
	while (AttachComp->GetAttachParent() != nullptr)
	{
		AttachComp = AttachComp->GetAttachParent();
		if (AttachComp->GetAttachParent() != nullptr)
		{
			CompT *= AttachComp->GetRelativeTransform();
		}
	}

	return CompT;
}

void UDSConstructionLibrary::Polygon2DBoolean_Union(const TArray<FDSPrimitive_MPolygon>& InPrimitives, FDSPrimitive_MPolygon& OutPrimitive)
{

}

bool UDSConstructionLibrary::IsBoardType(int32 ModelType)
{
	if (UDSCupboardLibrary::IsDoorBoardType(ModelType))
		return true;

	if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXGTB"), ModelType))
		return true;

	return false;
}


FString UDSConstructionLibrary::GetLayerName(TSharedPtr<FMultiComponentDataItem> InNode,EDSModelType InModelType)
{
	if (InNode.IsValid())
	{
		if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), InNode->ModelType)
			|| UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXLS"), InNode->ModelType))
		{
			return TEXT("柜门");
		}
	}
	else
	{
		if (InModelType == EDSModelType::E_House_Wall)
		{
			return TEXT("0");
		}
	}

	return TEXT("0");
}

FString UDSConstructionLibrary::GetBlockName(UDSBaseModel* InModel, TSharedPtr<FMultiComponentDataItem> InNode, UDSHouseAreaModel* InAreaModel)
{
	if (InNode.IsValid())
	{
		return FString::Printf(TEXT("定制_%s_%s"), *InNode->ComponentName, *FGuid::NewGuid().ToString());
	}
	else
	{
		EDSModelType ModelType = InModel->GetModelType();
		if (ModelType == EDSModelType::E_House_Wall)
		{
			//UDSHouseWallModel* WallModel = Cast<UDSHouseWallModel>(InModel);
			return FString::Printf(TEXT("墙_%s"),/* *(InAreaModel->GetTypedProperty<FDSHouseAreaProperty>()->GetAreaName()),*/ *FGuid::NewGuid().ToString());
		}
	}

	return InModel->GetUUID();
}

void UDSConstructionLibrary::CalcCupboard3DOutline(UDSCupboardModel* InModel, TArray<TArray<FVector>>& OutOutlines/*, TArray<FVector>& Normals*/)
{
	if (InModel == nullptr)
		return;

	FBox OBB = InModel->GetBoundBoxByPropertyCalculate();

	GetBoxOutlines(OBB, OutOutlines);

	//FVector Scale3D = GetCupboardTransform(InModel).GetScale3D();
	//if (Scale3D.X < 0 || Scale3D.Y < 0 || Scale3D.Z < 0)    //镜像了
	//{
		FVector CenterInLocal = OBB.GetCenter();
		for (auto& Points : OutOutlines)
		{
			for (auto& Point : Points)
			{
				Point = Point - CenterInLocal;
			}
		}
	//}



	/*
	TArray<FVector> Points = InModel->GetModelOrientedBoundingBox();
	if (Points.Num() != 8)
		return;

	OutOutlines.AddDefaulted(6);
	OutOutlines[0].Append({ Points[0],Points[1],Points[2],Points[3] });
	OutOutlines[1].Append({ Points[4],Points[7],Points[6],Points[5] });
	OutOutlines[2].Append({ Points[0],Points[3],Points[7],Points[4] });
	OutOutlines[3].Append({ Points[1],Points[5],Points[6],Points[2] });
	OutOutlines[4].Append({ Points[3],Points[2],Points[6],Points[7] });
	OutOutlines[5].Append({ Points[1],Points[0],Points[4],Points[5] });
	*/
	//Normals.AddDefaulted(6);
	//Normals[0] = FVector::CrossProduct(OutOutlines[0][0], OutOutlines[0][3]).GetSafeNormal();
	//Normals[1] = FVector::CrossProduct(OutOutlines[1][0], OutOutlines[1][3]).GetSafeNormal();
	//Normals[2] = FVector::CrossProduct(OutOutlines[2][0], OutOutlines[2][3]).GetSafeNormal();
	//Normals[3] = FVector::CrossProduct(OutOutlines[3][0], OutOutlines[3][3]).GetSafeNormal();
	//Normals[4] = FVector::CrossProduct(OutOutlines[4][0], OutOutlines[4][3]).GetSafeNormal();
	//Normals[5] = FVector::CrossProduct(OutOutlines[5][0], OutOutlines[5][3]).GetSafeNormal();
}

FBox UDSConstructionLibrary::GetCupboardNodeBox(TSharedPtr<FMultiComponentDataItem> InNode)
{
	auto FindAxisLength = [](const TArray<FParameterData>& Parameters, const FString& ParamName) ->FDecimal
		{
			if (const FParameterData* FoundParam = Parameters.FindByPredicate([&](const FParameterData& InParam) { return InParam.Data.name == ParamName; }))
			{
				return FDecimal(FoundParam->Data.value) * 0.1;
			}

			return FDecimal(0);
		};

	//const FDSCupboardModelInfo& ModelInfo = InModel->GetModelInfo();
	FVector Size = FVector::ZeroVector;

	if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXLS"), InNode->ModelType))  //拉手
	{
		Size.X = FindAxisLength(InNode->ComponentParameters, TEXT("LSW")).ToDouble();
		Size.Y = FindAxisLength(InNode->ComponentParameters, TEXT("LSD")).ToDouble();
		Size.Z = FindAxisLength(InNode->ComponentParameters, TEXT("LSH")).ToDouble();
	}
	else
	{
		Size = UDSCupboardLibrary::GetFixedSizeParameter(InNode);
	}

	FBox Res(ForceInit);

	//relative base on self
	FVector Loc = FVector::ZeroVector;
	FVector FarPoint = Loc + FVector::XAxisVector * Size.X + FVector::YAxisVector * Size.Y + FVector::ZAxisVector * Size.Z;

	Res += Loc;
	Res += FarPoint;

	Res = Res.ShiftBy(UDSCupboardLibrary::GetFixedOffsetParameter(InNode));

	return Res;
}

void UDSConstructionLibrary::CalcCupboard3DOutline(TSharedPtr<FMultiComponentDataItem> InNode, TArray<TArray<FVector>>& OutOutlines)
{
	FBox OBB = GetCupboardNodeBox(InNode);

	GetBoxOutlines(OBB, OutOutlines);
}

bool UDSConstructionLibrary::CalcCupboard2DOutline_Local(UDSCupboardModel* InModel, TArray<FDSPrimitive_MPolygon3D>& OutMPolygon3D)
{
	if (InModel == nullptr)
		return false;

	TArray<TArray<FVector>> FrameOutlines;
	CalcCupboard3DOutline(InModel, FrameOutlines);

	if (FrameOutlines.IsEmpty())
		return false;

	//FTransform T = InModel->GetProperty()->GetActualTransform();

	for (auto& Ite : FrameOutlines)
	{
		auto& Plane = Ite;

		//for (auto& PlaneIte : Plane)
		//{
		//	PlaneIte = T.TransformPosition(PlaneIte);
		//}

		FDSPrimitive_MPolygon3D NewMPoly;
		NewMPoly.FaceNormal = FVector::CrossProduct(Plane[1] - Plane[0], Plane[3] - Plane[0]).GetSafeNormal();
		FDSPrimitive_Polygon3D NewPoly;
		NewPoly.Outter = Ite;
		NewMPoly.Polygons.Add(MoveTemp(NewPoly));
		OutMPolygon3D.Add(MoveTemp(NewMPoly));
	}

	return true;
}


bool UDSConstructionLibrary::CalcCupboard2DOutline(UDSCupboardModel* InModel, const FTransform& InWorldToProjective,
	TArray<TArray<FVector>>& OutOutline)
{
	if (InModel == nullptr)
		return false;

	TArray<TArray<FVector>> FrameOutlines; 
	CalcCupboard3DOutline(InModel, FrameOutlines);

	if (FrameOutlines.IsEmpty())
		return false;

	FTransform WorldT = GetCupboardTransformNoMirror(InModel);

	FTransform T = WorldT * InWorldToProjective;

	//FBox MeshBox(EForceInit::ForceInitToZero);
	//TArray<TArray<FVector>> Paths;
	for (int32 i = 0; i < FrameOutlines.Num(); ++i)
	{
		auto& Plane = FrameOutlines[i];

		for (auto& PlaneIte : Plane)
		{
			PlaneIte = T.TransformPosition(PlaneIte);
		}

		//FVector PlaneNomal = FVector::CrossProduct(Plane[1] - Plane[0], Plane[3] - Plane[0]).GetSafeNormal();

		//float TempValue = FMath::Cos(FMath::DegreesToRadians(89.5));
		//if (FVector::DotProduct(PlaneNomal, FVector::ZAxisVector) > TempValue)
		//{
			OutOutline.Add(Plane);
		//}
	}

	return true;

	/*
	if (Paths.Num() > 1)
	{
		float Area = 0.0f;
		TArray<FVector> Polygon = Paths[0];
		TArray<TArray<FVector>> Result = FClipper2Library::PolygonUnion02(Paths, { Polygon }, MeshBox.GetCenter().Z, &Area);

		if (Area > 0.1f)
		{
			OutOutline.Append(MoveTemp(Result));
			OutArea = Area;
			return true;
		}
	}
	else
	{
		float Area = FClipper2Library::GetAreaSizeCM(Paths);
		if (Area > 0.1f)
		{
			OutOutline.Append(MoveTemp(Paths));
			OutArea = Area;
			return true;
		}
	}
	*/
	return false;
}

bool UDSConstructionLibrary::CalcCupboard2DOutline(UDSCupboardModel* InModel, const FTransform& InWorldToProjective,
	TArray<FDSPrimitive_MPolygon3D>& OutMPolygon3D)
{
	TArray<TArray<FVector>> OutOutline;
	bool bResult = CalcCupboard2DOutline(InModel, InWorldToProjective, OutOutline);
	for (auto& Ite : OutOutline)
	{
		FDSPrimitive_MPolygon3D NewMPoly;
		NewMPoly.FaceNormal = FVector::CrossProduct(Ite[1] - Ite[0], Ite[3] - Ite[0]).GetSafeNormal();
		FDSPrimitive_Polygon3D NewPoly;
		NewPoly.Outter = Ite;
		NewMPoly.Polygons.Add(MoveTemp(NewPoly));
		OutMPolygon3D.Add(MoveTemp(NewMPoly));
	}
	return bResult;
}

bool UDSConstructionLibrary::CalcCupboard2DOutline(TSharedPtr<FMultiComponentDataItem> InNode, const FTransform& InWorld, TArray<FDSPrimitive_MPolygon3D>& OutMPolygon3D)
{
	TArray<TArray<FVector>> OutOutline;
	CalcCupboard3DOutline(InNode, OutOutline);
	for (auto& Ite : OutOutline)
	{
		for (auto& PointIte : Ite)
		{
			PointIte = InWorld.TransformPosition(PointIte);
		}

		FDSPrimitive_MPolygon3D NewMPoly;
		NewMPoly.FaceNormal = FVector::CrossProduct(Ite[1] - Ite[0], Ite[3] - Ite[0]).GetSafeNormal();
		FDSPrimitive_Polygon3D NewPoly;
		NewPoly.Outter = Ite;
		NewMPoly.Polygons.Add(MoveTemp(NewPoly));
		OutMPolygon3D.Add(MoveTemp(NewMPoly));
	}
	return false;
}

void UDSConstructionLibrary::GetCupboardNode6MeshOutline(UDSCupboardModel* InModel, const FTransform& InWorldToProjective, TArray<FDSPrimitive_MPolygon3D>& OutMPolygon3D)
{
	ADSCupboardBaseView* CupboardView = Cast<ADSCupboardBaseView>(InModel->GetOwnedView());
	if (CupboardView != nullptr)
	{
		FTransform WorldT = CupboardView->GetActorTransform();
		FTransform ProjT = WorldT * InWorldToProjective;

		TArray<UVolatileMeshComponent*> VolatileComponents;
		CupboardView->GetComponents<UVolatileMeshComponent>(VolatileComponents);

		TArray<FTransform> Ts;
		for (UVolatileMeshComponent* VolatileComponent : VolatileComponents)
		{
			Ts.Add(VolatileComponent->GetRelativeTransform());   //把顶点转换到父空间
		}

		GetCupboardNode6MeshOutline_Inner(VolatileComponents, Ts, ProjT, OutMPolygon3D);
	}
}

void UDSConstructionLibrary::GetCupboardNode6MeshOutline_Local(UDSCupboardModel* InModel, TArray<FDSPrimitive_MPolygon3D>& OutMPolygon3D)
{
	ADSCupboardBaseView* CupboardView = Cast<ADSCupboardBaseView>(InModel->GetOwnedView());
	if (CupboardView != nullptr)
	{
		TArray<UVolatileMeshComponent*> VolatileComponents;
		CupboardView->GetComponents<UVolatileMeshComponent>(VolatileComponents);

		TArray<FTransform> Ts;
		for (UVolatileMeshComponent* VolatileComponent : VolatileComponents)
		{
			Ts.Add(VolatileComponent->GetRelativeTransform());   //把顶点转换到父空间
		}

		GetCupboardNode6MeshOutline_Inner(VolatileComponents, Ts, OutMPolygon3D);
	}
}


FBox UDSConstructionLibrary::CalcRoomBox(UDSHouseAreaModel* InAreaModel)
{
	TSharedPtr<FDSHouseAreaProperty> RoomProperty = InAreaModel->GetTypedProperty<FDSHouseAreaProperty>();
	return FBox(RoomProperty->Points);
}

bool UDSConstructionLibrary::GetDoorOpenDir2DOutline(UDSCupboardModel* InModel, const FTransform& InWorldToProjective, TArray<FVector>& OutOutline)
{
	if (InModel == nullptr)
		return false;

	TArray<TArray<FVector>> FrameOutlines;
	CalcCupboard3DOutline(InModel, FrameOutlines);

	if (FrameOutlines.IsEmpty())
		return false;

	FTransform T = GetCupboardTransformNoMirror(InModel) * InWorldToProjective;

	{
		auto& Plane = FrameOutlines[4];

		TArray<FVector> DoorDirLines;
		FString Value = InModel->GetComponentTreeDataRef()->GetParameterValue(TEXT("KX"));
		int32 IntValue = FCString::Atoi(*Value);
		CalcDoorOpenDir2DOutline(Plane, DoorDirLines, IntValue);

		for (auto& PlaneIte : Plane)
		{
			PlaneIte = T.TransformPosition(PlaneIte);
		}


		for (auto& PointIte : DoorDirLines)
		{
			PointIte = T.TransformPosition(PointIte);
		}


		FVector PlaneNomal = FVector::CrossProduct(Plane[1] - Plane[0], Plane[3] - Plane[0]).GetSafeNormal();
		float TempValue = FMath::Cos(FMath::DegreesToRadians(89.5));
		if (FVector::DotProduct(PlaneNomal, FVector::ZAxisVector) > TempValue)
		{
			OutOutline = DoorDirLines;
			return true;
		}
	}

	{
		auto& Plane = FrameOutlines[5];

		TArray<FVector> DoorDirLines;
		FString Value = InModel->GetComponentTreeDataRef()->GetParameterValue(TEXT("KX"));
		int32 IntValue = FCString::Atoi(*Value);
		CalcDoorOpenDir2DOutline(Plane, DoorDirLines, IntValue);

		for (auto& PlaneIte : Plane)
		{
			PlaneIte = T.TransformPosition(PlaneIte);
		}

		for (auto& PointIte : DoorDirLines)
		{
			PointIte = T.TransformPosition(PointIte);
		}

		FVector PlaneNomal = FVector::CrossProduct(Plane[1] - Plane[0], Plane[3] - Plane[0]).GetSafeNormal();
		float TempValue = FMath::Cos(FMath::DegreesToRadians(89.5));
		if (FVector::DotProduct(PlaneNomal, FVector::ZAxisVector) > TempValue)
		{
			//FString Value = InModel->GetComponentTreeDataRef()->GetParameterValue(TEXT("KX"));
			//int32 IntValue = FCString::Atoi(*Value);
			//CalcDoorOpenDir2DOutline(Plane, OutOutline, IntValue);
			OutOutline = DoorDirLines;
			return true;
		}
	}

	return false;
}

void UDSConstructionLibrary::CalcDoorOpenDir2DOutline(const TArray<FVector>& InDoorOutline, TArray<FVector>& OutOutline, int32 InKX)
{
	if (InKX == 0)  //左开
	{
		OutOutline.Add(InDoorOutline[1]);
		OutOutline.Add((InDoorOutline[0] + InDoorOutline[3]) * 0.5f);
		OutOutline.Add(InDoorOutline[2]);
	}
	else if (InKX == 1)   //右开
	{
		OutOutline.Add(InDoorOutline[0]);
		OutOutline.Add((InDoorOutline[1] + InDoorOutline[2]) * 0.5f);
		OutOutline.Add(InDoorOutline[3]);
	}
	else if (InKX == 2)   //上开
	{
		OutOutline.Add(InDoorOutline[0]);
		OutOutline.Add((InDoorOutline[2] + InDoorOutline[3]) * 0.5f);
		OutOutline.Add(InDoorOutline[1]);
	}
	else if (InKX == 3)   //下开
	{
		OutOutline.Add(InDoorOutline[3]);
		OutOutline.Add((InDoorOutline[0] + InDoorOutline[1]) * 0.5f);
		OutOutline.Add(InDoorOutline[2]);
	}
}

void UDSConstructionLibrary::GetConstructionDatasByModel(UDSCupboardModel* InModel, const TArray<TSharedPtr<FDSConstructionData>>& InDatas,
	TArray<TSharedPtr<FDSConstructionData>>& OutDatas)
{
	for (auto Ite : InDatas)
	{
		if (InModel->GetUUID() == Ite->SelfUUID)
		{
			OutDatas.Add(Ite);
		}
	}

	for (auto Ite : InDatas)
	{
		if (InModel->GetUUID() == Ite->ParentUUID)
		{
			OutDatas.Add(Ite);
		}
	}
}

bool UDSConstructionLibrary::IsHasDoorNode(TSharedPtr<FMultiComponentDataItem> InNode)
{
	if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), InNode->ModelType))
	{
		return true;
	}

	for (auto Ite : InNode->ChildComponent)
	{
		if (IsHasDoorNode(Ite))
			return true;
	}

	return false;
}

FTransform UDSConstructionLibrary::GetCupboardTransform(UDSCupboardModel* InModel)
{
	FTransform ModelT = InModel->GetProperty()->GetActualTransform();

	if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), InModel->GetModelInfoRef().ComponentTreeData->ModelType))
	{
		ModelT = UDSCupBoardDoorLibrary::CalDoorWorldTransform(InModel);
	}

	return ModelT;
}

FTransform UDSConstructionLibrary::GetCupboardTransformNoMirror(UDSCupboardModel* InModel)
{
	FTransform ModelT = InModel->GetProperty()->GetActualTransform();

	if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), InModel->GetModelInfoRef().ComponentTreeData->ModelType))
	{
		ModelT = UDSCupBoardDoorLibrary::CalDoorWorldTransform(InModel);
	}

	FBox OBB = InModel->GetBoundBoxByPropertyCalculate();
	FVector CenterInLocal = OBB.GetCenter();
	FVector CenterInWorld = ModelT.TransformPosition(CenterInLocal);
	ModelT.SetScale3D(ModelT.GetScale3D().GetAbs());
	ModelT.SetTranslation(CenterInWorld);

	return ModelT;
}

FVector UDSConstructionLibrary::UE4ToCAD(const FVector &InVector)
{
	//FTransform PTOCAD(FVector::YAxisVector, FVector::XAxisVector, FVector::ZAxisVector, FVector::ZeroVector);
	//PTOCAD.SetScale3D(FVector(-1, 1, 1));

	//return PTOCAD.TransformPosition(InVector);

	return FVector(InVector.Y * 10.0f, InVector.X * 10.0f, InVector.Z * 10.0f);
}

FVector UDSConstructionLibrary::CADToUE4(const FVector& InVector)
{
	return FVector(InVector.X * 0.1f, InVector.Y * 0.1f, InVector.Z * 0.1f);
}

bool UDSConstructionLibrary::ChildNodeIsNoNoModelType(TSharedPtr<FMultiComponentDataItem> InNode)
{
	for (auto NoteIte : InNode->ChildComponent)
	{
		if (NoteIte->ModelType >= 0)
			return false;
	}

	return true;
}
