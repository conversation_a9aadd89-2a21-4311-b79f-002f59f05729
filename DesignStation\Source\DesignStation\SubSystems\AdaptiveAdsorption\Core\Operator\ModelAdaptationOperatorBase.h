// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "SubSystems/MVC/Model/Core/DSMarkData.h"


class UDSBaseModel;
struct FAdaptationData;
/**
 * 
 */

class DESIGNSTATION_API FModelAdaptationOperatorBase :public TSharedFromThis<FModelAdaptationOperatorBase>
{
public:
	FModelAdaptationOperatorBase(UDSBaseModel* InSourceModel);

	virtual ~FModelAdaptationOperatorBase() { SourceModel = nullptr; };

public:

	virtual bool PrepareAdaptation(UDSBaseModel* InSourceMolde) =0;

	virtual void OnAdaptationStarting() = 0;

	virtual bool HandleAdaptiveAndAdssorptionByHitPoint(const FVector& RayStartPoint, const FVector& RayDir) = 0;

	virtual	void ExecuteStepAdaptation(const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr = FDSBroadcastMarkData::BroadcastToMVCMark) {};
	virtual	void ExecuteStepAdaptation(bool bReCalculateBeDependency, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr = FDSBroadcastMarkData::BroadcastToMVCMark) {};

	virtual void ShowDebug(UObject* WorldContextObject) = 0;

	virtual void UpdateSourceIntersectionTransform() {};

protected:
	virtual void OnAdaptiveAndAdSorptionCallback(const TSharedPtr<FAdaptationData>& AdaptationData) {};
protected:
	UDSBaseModel* SourceModel;

	TSharedPtr<FDSBroadcastMarkData> BroadcastMarkPtr;
};