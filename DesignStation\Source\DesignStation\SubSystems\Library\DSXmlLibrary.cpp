﻿#include "DSXmlLibrary.h"

#include "DesignStationFunctionLibrary.h"
#include "XmlFile.h"
#include "Subsystems/File/Core/ProtoDataConvert/ProtobufOperatorFunctionLibrary.h"
#include "SubSystems/MVC/DSMVCSubsystem.h"
#include "SubSystems/MVC/Core/Property/HouseAreaProperty.h"
#include "Subsystems/Resource/DSResourceSubsystem.h"
#include "SubSystems/UI/DSUISubsystem.h"

static const TMap<int32, TPair<FString, FString>> XmlTagMap = {
	{0, TPair<FString, FString>(TEXT("Unit"),TEXT("unit"))}, //（柜体映射）
	{1, TPair<FString, FString>(TEXT("Functor"), TEXT("functor"))}, //（功能件映射）,
	{2, TPair<FString, FString>(TEXT("DoorBoard"),TEXT("door_board"))}, //（门板映射）,
	{3, TPair<FString, FString>(TEXT("Hardware"),TEXT("hardware"))}, //（五金映射）,
	{4, TPair<FString, FString>(TEXT("Knob"),TEXT("knob"))}, //（拉手映射）,
	{5, TPair<FString, FString>(TEXT("RomePi11ar"),TEXT("rome_pi11ar"))}, //（罗马柱映射）,
	{6, TPair<FString, FString>(TEXT("Line"),TEXT("line"))}, //（顶线映射）（下拖线映射）（踢脚线映射）,
	{7, TPair<FString, FString>(TEXT("WashBasin"),TEXT("washBasin"))}, //（水槽映射）,
	{8, TPair<FString, FString>(TEXT("WaterFaucet"),TEXT("waterFaucet"))}, //（龙头映射）,
	{9, TPair<FString, FString>(TEXT("Table"),TEXT("table"))}, //（台面映射）,
	{10, TPair<FString, FString>(TEXT("CabinetBoard"),TEXT("cabinet_board"))}, //（柜体板映射）,
	{11, TPair<FString, FString>(TEXT("DoorCode"),TEXT("door_code"))}, //（门芯映射）,
	{12, TPair<FString, FString>(TEXT("Accessory"),TEXT("accessory"))}, //（附件配件映射）,
	{13, TPair<FString, FString>(TEXT("Buckle"),TEXT("buckle"))}, //（扣手映射）,
	{14, TPair<FString, FString>(TEXT("MetalsScheme"),TEXT("metalsScheme"))} //（五金方案映射）}
};

static const TMap<int32, TPair<FString, FString>> SingleIDMap =
{
	{91, TPair<FString, FString>(TEXT("RomePi11ar"),TEXT("rome_pi11ar"))},
	{96, TPair<FString, FString>(TEXT("Line"),TEXT("line"))},
	{101, TPair<FString, FString>(TEXT("Line"),TEXT("line"))},
	{106, TPair<FString, FString>(TEXT("Line"),TEXT("line"))},
	{221, TPair<FString, FString>(TEXT("WashBasin"),TEXT("washBasin"))},
	{225, TPair<FString, FString>(TEXT("WaterFaucet"),TEXT("waterFaucet"))},
	{231, TPair<FString, FString>(TEXT("Table"),TEXT("table"))},
	{241, TPair<FString, FString>(TEXT("CabinetBoard"),TEXT("cabinet_board"))},
	{261, TPair<FString, FString>(TEXT("DoorCode"),TEXT("door_code"))},
	{281, TPair<FString, FString>(TEXT("Accessory"),TEXT("accessory"))},
	{301, TPair<FString, FString>(TEXT("Buckle"),TEXT("buckle"))},
	{321, TPair<FString, FString>(TEXT("Knob"),TEXT("knob"))},
	{331, TPair<FString, FString>(TEXT("MetalsScheme"),TEXT("metalsScheme"))},
	{336, TPair<FString, FString>(TEXT("MetalsScheme"),TEXT("metalsScheme"))},
	{341, TPair<FString, FString>(TEXT("DoorBoard"),TEXT("door_board"))},
	{351, TPair<FString, FString>(TEXT("DoorCode"),TEXT("door_code"))}
};

TPair<FString, FString> UXmlLibrary::GetStringFromModelType(const int32& NumericID)
{
	// Unit (单元): 0-9
	if (NumericID >= 0 && NumericID <= 9)
	{
		return TPair<FString, FString>(TEXT("Unit"),TEXT("unit"));
	}
	// Functor (功能件): 21-25
	if (NumericID >= 21 && NumericID <= 25)
	{
		return TPair<FString, FString>(TEXT("Functor"), TEXT("functor"));
	}
	// DoorBoard (门板): 41-46
	if (NumericID >= 41 && NumericID <= 46)
	{
		return TPair<FString, FString>(TEXT("DoorBoard"),TEXT("door_board"));
	}
	// Hardware (五金): 81-88
	if (NumericID >= 81 && NumericID <= 88)
	{
		return TPair<FString, FString>(TEXT("Hardware"),TEXT("hardware"));
	}

	if (const TPair<FString, FString>* Result = SingleIDMap.Find(NumericID))
	{
		return *Result;
	}

	// 如果所有情况都不匹配
	return TPair<FString, FString>();
}

bool UXmlLibrary::IsPointInPolygon(const FVector2D& Point, const TArray<FVector2D>& Polygon)
{
	int32 Intersections = 0;
	int32 NumVertices = Polygon.Num();

	constexpr float Tolerance = 0.1f;

	for (int32 i = 0; i < NumVertices; ++i)
	{
		FVector2D A = Polygon[i];
		FVector2D B = Polygon[(i + 1) % NumVertices];

		if (Point.Equals(A, Tolerance) || Point.Equals(B, Tolerance))
		{
			return false;
		}

		if ((A.Y > Point.Y) != (B.Y > Point.Y))
		{
			float IntersectX = (B.X - A.X) * (Point.Y - A.Y) / (B.Y - A.Y) + A.X;

			if (Point.X < IntersectX)
			{
				Intersections++;
			}
			else if (FMath::Abs(Point.X - IntersectX) <= Tolerance)
			{
				return true;
			}
		}
	}

	return (Intersections % 2 == 1);
}

FString UXmlLibrary::FormatNumericString(const FString& InputString, int32 MaxDecimalPlaces)
{
	// 去除前后空格
	FString TrimmedString = InputString.TrimStartAndEnd();

	// 检查是否为空字符串
	if (TrimmedString.IsEmpty())
	{
		return TEXT("0");
	}

	// 检查是否为有效数字
	bool bIsValidNumber = false;
	double NumericValue = 0.0;

	// 尝试转换为double
	if (TrimmedString.IsNumeric())
	{
		NumericValue = FCString::Atod(*TrimmedString);
		bIsValidNumber = true;
	}

	// 如果不是有效数字，返回原字符串
	if (!bIsValidNumber)
	{
		return InputString;
	}

	// 处理特殊值
	if (FMath::IsNaN(NumericValue) || !FMath::IsFinite(NumericValue))
	{
		return TEXT("0");
	}

	// 四舍五入到指定小数位数，避免浮点精度问题
	double RoundedValue = FMath::RoundToDouble(NumericValue * FMath::Pow(10.0, MaxDecimalPlaces)) / FMath::Pow(10.0, MaxDecimalPlaces);

	// 检查是否接近整数（处理0.99999999这种情况）
	double IntegerPart = FMath::Floor(RoundedValue);
	double FractionalPart = RoundedValue - IntegerPart;

	// 如果小数部分非常接近1，则进位
	if (FractionalPart > 0.999999)
	{
		RoundedValue = IntegerPart + 1.0;
	}
	// 如果小数部分非常接近0，则去掉小数部分
	else if (FractionalPart < 0.000001)
	{
		RoundedValue = IntegerPart;
	}

	// 转换为字符串
	FString ResultString;

	// 检查是否为整数
	if (FMath::Abs(RoundedValue - FMath::RoundToInt32(RoundedValue)) < 0.000001)
	{
		// 是整数，直接格式化为整数
		ResultString = FString::Printf(TEXT("%.0f"), RoundedValue);
	}
	else
	{
		// 是小数，格式化为小数并去除尾随的0
		ResultString = FString::Printf(TEXT("%.*f"), MaxDecimalPlaces, RoundedValue);

		// 去除尾随的0和小数点
		while (ResultString.EndsWith(TEXT("0")) && ResultString.Contains(TEXT(".")))
		{
			ResultString = ResultString.LeftChop(1);
		}

		// 如果最后一个字符是小数点，也去掉
		if (ResultString.EndsWith(TEXT(".")))
		{
			ResultString = ResultString.LeftChop(1);
		}
	}

	return ResultString;
}

void UXmlLibrary::TestFormatNumericString()
{
	TArray<FString> TestCases = {
		TEXT("564.0000000000000000"), // 应该输出: 564
		TEXT("0.0371520000000000"), // 应该输出: 0.037152
		TEXT("0.99999999"), // 应该输出: 1
		TEXT("123.456000"), // 应该输出: 123.456
		TEXT("0.000000"), // 应该输出: 0
		TEXT("1000.0"), // 应该输出: 1000
		TEXT("3.14159265359"), // 应该输出: 3.141593 (默认6位小数)
		TEXT("0.000001"), // 应该输出: 0.000001
		TEXT("0.0000001"), // 应该输出: 0 (超过精度)
		TEXT("999.999999"), // 应该输出: 1000
		TEXT("abc"), // 应该输出: abc (非数字)
		TEXT(""), // 应该输出: 0
		TEXT("  123.45  "), // 应该输出: 123.45
		TEXT("-456.789000"), // 应该输出: -456.789
		TEXT("0"), // 应该输出: 0
		TEXT("1.0") // 应该输出: 1
	};
}

void UXmlLibrary::ExportDataToXml(const FString& FilePath)
{
	TMap<UDSBaseModel*, TArray<UDSBaseModel*>> AreasModelMap;

	if (UDSMVCSubsystem::GetInstance()->GetModels(EDSModelType::E_House_Area).Num() < 1)
	{
		UDSUISubsystem::GetInstance()->SetToastState(EToastState::EMistake, TEXT("请先绘制房间"));
		return;
	}

	for (auto& Room : UDSMVCSubsystem::GetInstance()->GetModels(EDSModelType::E_House_Area))
	{
		TArray<UDSBaseModel*> CurAreaModels;
		TSharedPtr<FDSHouseAreaProperty> AreaProperty = Room->GetTypedProperty<FDSHouseAreaProperty>();
		TArray<FVector2D> AreaPolygon;
		for (auto& Point : AreaProperty->Points)
		{
			AreaPolygon.Add(FVector2D(Point.X, Point.Y));
		}

		for (auto& CM : UDSMVCSubsystem::GetInstance()->GetAllCustomModels())
		{
			FVector2D Loc = FVector2D(CM->GetPropertySharedPtr()->TransformProperty.Location.X, CM->GetPropertySharedPtr()->TransformProperty.Location.Y);
			if (IsPointInPolygon(Loc, AreaPolygon) && UDSToolLibrary::IsCustomCabinetType(CM->GetModelType()))
			{
				CurAreaModels.Add(CM);
			}
		}

		AreasModelMap.FindOrAdd(Room).Append(CurAreaModels);
	}

	int32 TotalUDSBaseModelCount = 0;
	for (const TPair<UDSBaseModel*, TArray<UDSBaseModel*>>& Pair : AreasModelMap)
	{
		const TArray<UDSBaseModel*>& ModelsArray = Pair.Value;
		TotalUDSBaseModelCount += ModelsArray.Num();
	}

	if (TotalUDSBaseModelCount < 1)
	{
		UDSUISubsystem::GetInstance()->SetToastState(EToastState::EMistake, TEXT("没有找到参与报价的商品"));
		return;
	}

	FXmlFile NewXmlFile;
	const FString RootElementName = TEXT("CabinetLayoutQuote");
	FString MinimalXmlString = FString::Printf(TEXT("<%s></%s>"), *RootElementName, *RootElementName);

	if (!NewXmlFile.LoadFile(MinimalXmlString, EConstructMethod::ConstructFromBuffer))
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to initialize FXmlFile with minimal root: %s"), *NewXmlFile.GetLastError());
		return;
	}

	FXmlNode* RootNode = NewXmlFile.GetRootNode();
	if (!RootNode)
	{
		UE_LOG(LogTemp, Error, TEXT("RootNode is null after loading minimal XML. This should not happen if LoadFile succeeded."));
		return;
	}

	TArray<FXmlAttribute> RootAttributes;
	RootAttributes.Add(FXmlAttribute(TEXT("value"), TEXT("橱衣柜报价系统")));
	RootNode->SetAttributes(RootAttributes);

	RootNode->AppendChildNode(TEXT("WardrobeQuote"));
	FXmlNode* WardrobeQuoteNode = nullptr;
	if (RootNode->GetChildrenNodes().Num() > 0)
	{
		WardrobeQuoteNode = RootNode->GetChildrenNodes().Last();
		if (WardrobeQuoteNode && WardrobeQuoteNode->GetTag() != TEXT("WardrobeQuote"))
		{
			UE_LOG(LogTemp, Error, TEXT("Retrieved last child from RootNode, but tag is not WardrobeQuote."));
			WardrobeQuoteNode = nullptr;
		}
	}

	if (WardrobeQuoteNode)
	{
		//<WardrobeQuote>
		{
			TArray<FXmlAttribute> WardrobeQuoteAttributes;
			WardrobeQuoteAttributes.Add(FXmlAttribute(TEXT("quoteWay"), TEXT("")));
			WardrobeQuoteAttributes.Add(FXmlAttribute(TEXT("glocalFactor"), TEXT("")));
			WardrobeQuoteAttributes.Add(FXmlAttribute(TEXT("styleName"), TEXT("")));
			WardrobeQuoteAttributes.Add(FXmlAttribute(TEXT("styleGlass"), TEXT("")));
			WardrobeQuoteAttributes.Add(FXmlAttribute(TEXT("styleGYG"), TEXT("")));
			WardrobeQuoteAttributes.Add(FXmlAttribute(TEXT("hingeBrand"), TEXT("")));
			WardrobeQuoteAttributes.Add(FXmlAttribute(TEXT("slidewayBrand"), TEXT("")));
			WardrobeQuoteAttributes.Add(FXmlAttribute(TEXT("DCFCT"), TEXT("")));
			WardrobeQuoteAttributes.Add(FXmlAttribute(TEXT("Version"), TEXT("")));
			WardrobeQuoteAttributes.Add(FXmlAttribute(TEXT("organization"), TEXT("")));
			WardrobeQuoteAttributes.Add(FXmlAttribute(TEXT("deptId"), TEXT("")));
			WardrobeQuoteAttributes.Add(FXmlAttribute(TEXT("organId"), TEXT("")));
			WardrobeQuoteNode->SetAttributes(WardrobeQuoteAttributes);
		}

		//<SchemeInfo>
		{
			WardrobeQuoteNode->AppendChildNode(TEXT("SchemeInfo"));
			FXmlNode* SchemeInfoNode = WardrobeQuoteNode->GetChildrenNodes().Last();
			TArray<FXmlAttribute> SchemeInfoAttributes;
			SchemeInfoAttributes.Add(FXmlAttribute(TEXT("schemeName"), TEXT("")));
			SchemeInfoAttributes.Add(FXmlAttribute(TEXT("userName"), TEXT("")));
			SchemeInfoAttributes.Add(FXmlAttribute(TEXT("userPhone"), TEXT("")));
			SchemeInfoAttributes.Add(FXmlAttribute(TEXT("cityName"), TEXT("")));
			SchemeInfoAttributes.Add(FXmlAttribute(TEXT("modelName"), TEXT("")));
			SchemeInfoAttributes.Add(FXmlAttribute(TEXT("buildName"), TEXT("")));
			SchemeInfoAttributes.Add(FXmlAttribute(TEXT("designer"), TEXT("")));
			SchemeInfoAttributes.Add(FXmlAttribute(TEXT("designerName"), TEXT("")));
			SchemeInfoAttributes.Add(FXmlAttribute(TEXT("designerPhone"), TEXT("")));
			SchemeInfoAttributes.Add(FXmlAttribute(TEXT("designerID"), TEXT("")));
			SchemeInfoNode->SetAttributes(SchemeInfoAttributes);
		}

		//<ShoppingInfo>
		{
			WardrobeQuoteNode->AppendChildNode(TEXT("ShoppingInfo"));
			FXmlNode* ShoppingInfoNode = WardrobeQuoteNode->GetChildrenNodes().Last();
			TArray<FXmlAttribute> ShoppingInfoAttributes;
			ShoppingInfoAttributes.Add(FXmlAttribute(TEXT("shopName"), TEXT("")));
			ShoppingInfoAttributes.Add(FXmlAttribute(TEXT("shopTel"), TEXT("")));
			ShoppingInfoAttributes.Add(FXmlAttribute(TEXT("shopUser"), TEXT("")));
			ShoppingInfoAttributes.Add(FXmlAttribute(TEXT("shopMobie"), TEXT("")));
			ShoppingInfoAttributes.Add(FXmlAttribute(TEXT("shopProvince"), TEXT("")));
			ShoppingInfoAttributes.Add(FXmlAttribute(TEXT("shopCity"), TEXT("")));
			ShoppingInfoAttributes.Add(FXmlAttribute(TEXT("shopDistrict"), TEXT("")));
			ShoppingInfoAttributes.Add(FXmlAttribute(TEXT("shopAddress"), TEXT("")));
			ShoppingInfoNode->SetAttributes(ShoppingInfoAttributes);
		}

		//<Room>
		for (auto& Room : UDSMVCSubsystem::GetInstance()->GetModels(EDSModelType::E_House_Area))
		{
			TSharedPtr<FDSHouseAreaProperty> AreaProperty = Room->GetTypedProperty<FDSHouseAreaProperty>();
			WardrobeQuoteNode->AppendChildNode(TEXT("Room"));
			FXmlNode* RoomNode = WardrobeQuoteNode->GetChildrenNodes().Last();

			TArray<FXmlAttribute> RoomAttributes;
			RoomAttributes.Add(FXmlAttribute(TEXT("roomName"), AreaProperty->AreaName));
			RoomAttributes.Add(FXmlAttribute(TEXT("roomId"), Room->GetUUID()));
			RoomNode->SetAttributes(RoomAttributes);

			RoomNode->AppendChildNode(TEXT("Group"));
			FXmlNode* GroupNode = RoomNode->GetChildrenNodes().Last();

			//<Group>
			int32 ModelIndex = 1; // Start cabinet numbering from 1
			for (auto& Model : AreasModelMap[Room])
			{
				UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(Model);
				if (!CupboardModel)
				{
					continue;
				}
				const FParameterData* Tags = CupboardModel->GetModelInfoRef().ComponentTreeData->ComponentParameters.FindByPredicate([](const FParameterData& InParam)
				{
					return InParam.Data.name.Equals(TEXT("LXYS"));
				});

				const FParameterData* MaterialNode = CupboardModel->GetModelInfoRef().ComponentTreeData->ComponentParameters.FindByPredicate([](const FParameterData& InParam)
				{
					return InParam.Data.name.Equals(TEXT("DZCZ"));
				});

				// Generate cabinet number for this model (starting from 1)
				FString CabinetNo = FString::FromInt(ModelIndex);

				FString RootTag = Tags->Data.GetFormattedValue();
				FString RootMaterialId = MaterialNode->Data.GetFormattedValue();

				CollectGroupElement(GroupNode, CupboardModel->GetModelInfoRef().ComponentTreeData, RootTag, RootMaterialId, CabinetNo);
				ModelIndex++;
			}

			//<Accessory>附件配件
			if (AreaProperty->AccessoryResInfosInRoom.IsEmpty())
			{
				continue;
			}

			for (auto AccessoryResInfo : AreaProperty->AccessoryResInfosInRoom)
			{
				CollectAccessoryElement(RoomNode, AccessoryResInfo);
			}
		}
	}

	FPlatformFileManager::Get().GetPlatformFile().CreateDirectoryTree(*FPaths::GetPath(FilePath));

	if (NewXmlFile.Save(FilePath))
	{
		UDSUISubsystem::GetInstance()->SetToastState(EToastState::ESuccess, TEXT("XML文件已生成!"));
		UE_LOG(LogTemp, Log, TEXT("XML file successfully generated and saved to: %s"), *FilePath);
	}
	else
	{
		UDSUISubsystem::GetInstance()->SetToastState(EToastState::EMistake, TEXT("XML文件生成失败!"));
		UE_LOG(LogTemp, Error, TEXT("Failed to save XML file to: %s. Error: %s"), *FilePath, *NewXmlFile.GetLastError());
	}
}

void UXmlLibrary::CollectGroupElement(FXmlNode*& ParentNode, const TSharedPtr<FMultiComponentDataItem>& ComponentDataItem, FString RootTag, FString RootMaterialId, const FString& CabinetNo)
{
	//如果组件节点不可见，则不添加到xml文件中（非操作隐藏bhidden）
	if (FCString::Atoi(*ComponentDataItem->ComponentVisibility.GetFormattedValue()) == 0)
	{
		return;
	}

	const FParameterData* BJJD = ComponentDataItem->ComponentParameters.FindByPredicate([](const FParameterData& InParam)
	{
		return InParam.Data.name.Equals(TEXT("BJJD"));
	});

	const FParameterData* Tags = ComponentDataItem->ComponentParameters.FindByPredicate([](const FParameterData& InParam)
	{
		return InParam.Data.name.Equals(TEXT("LXYS"));
	});

	const FParameterData* MaterialNode = ComponentDataItem->ComponentParameters.FindByPredicate([](const FParameterData& InParam)
	{
		return InParam.Data.name.Equals(TEXT("DZCZ"));
	});

	if (BJJD && FCString::Atoi(*BJJD->Data.GetFormattedValue()) == 0)
	{
		// 当BJJD为0时，跳过当前元素，直接处理子元素
		// 子元素将使用当前传递下来的RootTag、RootMaterialId和CabinetNo

		// 更新RootTag和RootMaterialId（如果当前元素有自己的值）
		if (Tags)
		{
			RootTag = Tags->Data.GetFormattedValue();
		}

		if (MaterialNode)
		{
			RootMaterialId = MaterialNode->Data.GetFormattedValue();
		}

		// 直接处理子元素，将它们输出到当前的父级节点中
		for (auto& It : ComponentDataItem->ChildComponent)
		{
			if (FCString::Atoi(*It->ComponentVisibility.GetFormattedValue()) == 0)
			{
				continue;
			}
			// 跳级处理：子元素直接继承当前的CabinetNo，不增加层级
			CollectGroupElement(ParentNode, It, RootTag, RootMaterialId, CabinetNo);
		}
		return; // 跳过当前元素的正常处理流程
	}

	if (!Tags && RootTag.IsEmpty()) //根没有LXYS，且自身也没有LXYS，则不添加到xml文件中
	{
		return;
	}

	if (Tags) //如果有自己的LXYS用自己的，没有用根传递下来的，并且要将RootTag设置为LXYS的值
	{
		RootTag = Tags->Data.GetFormattedValue();
	}

	if (MaterialNode) //如果有自己的DZCZ用自己的，没有用根传递下来的
	{
		RootMaterialId = MaterialNode->Data.GetFormattedValue();
	}

	AddNextLevelTagS(ComponentDataItem, ParentNode, XmlTagMap[FCString::Atoi(*RootTag)], RootTag, RootMaterialId, CabinetNo);
}

//Unit下层开始递归
void UXmlLibrary::AddNextLevelTagS(const TSharedPtr<FMultiComponentDataItem>& ComponentDataItem, FXmlNode* ParentNode, const TPair<FString, FString>& TagName, const FString& RootTag, const FString& RootMaterialId, const FString& CabinetNo)
{
	ParentNode->AppendChildNode(TagName.Key);
	FXmlNode* SelfNode = ParentNode->GetChildrenNodes().Last();

	TMap<FString, FString> SpecialValueMap;
	TMap<FString, FString> MustValueMap;

	MustValueMap.Add(TEXT("TYPE"), TagName.Value);
	MustValueMap.Add(TEXT("NAME"), ComponentDataItem->ComponentName);
	MustValueMap.Add(TEXT("CABINETNO"), CabinetNo);
	MustValueMap.Add(TEXT("UID"), ComponentDataItem->UUID);
	MustValueMap.Add(TEXT("FOLDERID"), ComponentDataItem->ComponentID.GetFormattedValue());
	MustValueMap.Add(TEXT("CODE"), ComponentDataItem->Code);
	if (auto ResourceInfo = UDSResourceSubsystem::GetInstance()->FindResourceFromCache(RootMaterialId))
	{
		MustValueMap.Add(TEXT("MATERIALNAME"), ResourceInfo->Name);
		MustValueMap.Add(TEXT("MATERIALDESIGNCODE"), ResourceInfo->FolderCode);
	}
	MustValueMap.Add(TEXT("LOCATIONX"), ComponentDataItem->ComponentLocation.LocationX.GetFormattedValue());
	MustValueMap.Add(TEXT("LOCATIONY"), ComponentDataItem->ComponentLocation.LocationY.GetFormattedValue());
	MustValueMap.Add(TEXT("LOCATIONZ"), ComponentDataItem->ComponentLocation.LocationZ.GetFormattedValue());
	MustValueMap.Add(TEXT("ROLL"), ComponentDataItem->ComponentRotation.Roll.GetFormattedValue());
	MustValueMap.Add(TEXT("PITCH"), ComponentDataItem->ComponentRotation.Pitch.GetFormattedValue());
	MustValueMap.Add(TEXT("YAW"), ComponentDataItem->ComponentRotation.Yaw.GetFormattedValue());
	MustValueMap.Add(TEXT("SCALEX"), ComponentDataItem->ComponentScale.X.GetFormattedValue());
	MustValueMap.Add(TEXT("SCALEY"), ComponentDataItem->ComponentScale.Y.GetFormattedValue());
	MustValueMap.Add(TEXT("SCALEZ"), ComponentDataItem->ComponentScale.Z.GetFormattedValue());

	//门板要额外组合参数，要额外解析一下门结构
	if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), ComponentDataItem->ModelType))
	{
		DoorAddPlusValue(ComponentDataItem, MustValueMap);
	}

	for (auto& It : ComponentDataItem->ComponentParameters)
	{
		if (!It.Data.Special.IsEmpty() && FCString::Atoi(*It.Data.Special_exp) == 1)
		{
			SpecialValueMap.Add(It.Data.name, FormatNumericString(It.Data.value, 6));
		}

		if (!It.Data.Must.IsEmpty() && FCString::Atoi(*It.Data.Must_exp) == 1)
		{
			MustValueMap.Add(It.Data.name, FormatNumericString(It.Data.value, 6));
		}
	}

	TArray<FXmlAttribute> SpecialAttributes;
	TArray<FXmlAttribute> MustAttributes;

	for (auto& SA : SpecialValueMap)
	{
		SpecialAttributes.Add(FXmlAttribute(SA.Key, SA.Value));
	}

	for (auto& MA : MustValueMap)
	{
		MustAttributes.Add(FXmlAttribute(MA.Key, MA.Value));
	}

	//先添加自己的Must参数
	SelfNode->SetAttributes(MustAttributes);

	//再添加自己标签下，第一个SpecialVariables标签
	SelfNode->AppendChildNode(TEXT("SpecialVariables"));
	FXmlNode* ChildSpecialNode = SelfNode->GetChildrenNodes().Last();
	ChildSpecialNode->SetAttributes(SpecialAttributes);

	int32 ChildIndex = 1;
	for (auto& It : ComponentDataItem->ChildComponent)
	{
		if (FCString::Atoi(*It->ComponentVisibility.GetFormattedValue()) == 0)
		{
			continue;
		}
		// Generate hierarchical cabinet number for child components
		FString ChildCabinetNo = CabinetNo + TEXT("-") + FString::FromInt(ChildIndex);
		CollectGroupElement(SelfNode, It, RootTag, RootMaterialId, ChildCabinetNo);
		ChildIndex++;
	}
}

void UXmlLibrary::DoorAddPlusValue(const TSharedPtr<FMultiComponentDataItem>& ComponentDataItem, TMap<FString, FString>& MustValueMap)
{
	FParameterData* bIsavoid = ComponentDataItem.Get()->ComponentParameters.FindByPredicate([](const FParameterData& InParam) -> bool
	{
		return InParam.Data.name.Equals(TEXT("ISAVOID"));
	});

	if (!bIsavoid)
	{
		return;
	}

	//虽然铰链没避让，还要判断延伸值是否为0，如果不是0，ISAVOID也要标记成1
	if (FCString::Atoi(*bIsavoid->Data.GetFormattedValue()) == 0)
	{
		const int32 Uy = FCString::Atoi(*ComponentDataItem.Get()->GetParameterValue(TEXT("UY")));
		const int32 Dy = FCString::Atoi(*ComponentDataItem.Get()->GetParameterValue(TEXT("DY")));
		const int32 Ly = FCString::Atoi(*ComponentDataItem.Get()->GetParameterValue(TEXT("LY")));
		const int32 Ry = FCString::Atoi(*ComponentDataItem.Get()->GetParameterValue(TEXT("RY")));

		if (Uy != 0 || Dy != 0 || Ly != 0 || Ry != 0)
		{
			bIsavoid->Data.value = TEXT("1");
			bIsavoid->Data.expression = TEXT("1");
		}
	}
	MustValueMap.Add(TEXT("ISAVOID"), bIsavoid->Data.GetFormattedValue());

	auto HingPlan = ComponentDataItem->ChildComponent.FindByPredicate([](const TSharedPtr<FMultiComponentDataItem>& InItem)
	{
		return UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(InItem->ModelType) == EDSModelType::E_Custom_HingePlan;
	});

	if (!HingPlan)
	{
		return;
	}

	TArray<TSharedPtr<FMultiComponentDataItem>> HingeList;
	for (const auto& Hinge : HingPlan->Get()->ChildComponent)
	{
		if (Hinge->ComponentVisibility.GetFormattedValue() == TEXT("1"))
		{
			HingeList.Add(Hinge);
		}
	}

	HingeList.Sort([](const TSharedPtr<FMultiComponentDataItem>& A, const TSharedPtr<FMultiComponentDataItem>& B)
	{
		return FCString::Atoi(*A->ComponentLocation.LocationZ.GetFormattedValue()) < FCString::Atoi(*B->ComponentLocation.LocationZ.GetFormattedValue());
	});

	TArray<FString> LocationStringArray;
	for (auto& It : HingeList)
	{
		LocationStringArray.Add(FString::FromInt(FCString::Atoi(*It->ComponentLocation.LocationZ.GetFormattedValue())));
	}

	if (LocationStringArray.IsEmpty())
	{
		return;
	}

	FString JoinedString;
	int32 NumElementsToProcess = FMath::Min(LocationStringArray.Num(), 10);
	JoinedString.Append(FString::Printf(TEXT("%c"), 'A' + FCString::Atoi(*HingPlan->Get()->GetParameterValue(TEXT("BRAND")))));
	JoinedString.Append(TEXT("|"));

	for (int32 i = 0; i < NumElementsToProcess; ++i)
	{
		FString Prefix = FString::Printf(TEXT("%c"), 'A' + i);
		JoinedString.Append(Prefix);
		JoinedString.Append(TEXT("_"));
		JoinedString.Append(LocationStringArray[i]);
	}

	MustValueMap.Add(TEXT("HINGE"), JoinedString);
}

void UXmlLibrary::CollectAccessoryElement(FXmlNode*& ParentNode, const FDSAccessoryResInfo& AccessoryResInfo)
{
	ParentNode->AppendChildNode(TEXT("Accessory"));
	FXmlNode* AccessoryNode = ParentNode->GetChildrenNodes().Last();

	FRefToLocalFileData AccessoryData;
	FString FilePath = FPaths::Combine(FPaths::ProjectSavedDir(), TEXT("Resources"), FString::Printf(TEXT("%s.dat"), *AccessoryResInfo.FolderId));
	if (!FPaths::FileExists(FilePath) || !FProtobufOperatorFunctionLibrary::LoadRelationFromFile(FilePath, AccessoryData))
	{
		return;
	}

	TMap<FString, FString> SpecialValueMap;
	TMap<FString, FString> MustValueMap;

	TSharedPtr<FMultiComponentDataItem> OriginalNode = MakeShared<FMultiComponentDataItem>();
	UDSCupboardLibrary::ConstructTreeRootNode(AccessoryData, *OriginalNode);

	MustValueMap.Add(TEXT("TYPE"), GetStringFromModelType(OriginalNode->ModelType).Value);
	MustValueMap.Add(TEXT("CODE"), OriginalNode->Code);
	MustValueMap.Add(TEXT("NUM"), FString::FromInt(AccessoryResInfo.Num));
	MustValueMap.Add(TEXT("NAME"), OriginalNode->ComponentName);
	MustValueMap.Add(TEXT("FOLDERID"), OriginalNode->ComponentID.GetFormattedValue());

	for (auto& It : OriginalNode->ComponentParameters)
	{
		if (!It.Data.Special.IsEmpty() && FCString::Atoi(*It.Data.Special_exp) == 1)
		{
			SpecialValueMap.Add(It.Data.name, FormatNumericString(It.Data.value, 6));
		}

		if (!It.Data.Must.IsEmpty() && FCString::Atoi(*It.Data.Must_exp) == 1)
		{
			MustValueMap.Add(It.Data.name, FormatNumericString(It.Data.value, 6));
		}
	}

	TArray<FXmlAttribute> SpecialAttributes;
	TArray<FXmlAttribute> MustAttributes;

	for (auto& SA : SpecialValueMap)
	{
		SpecialAttributes.Add(FXmlAttribute(SA.Key, SA.Value));
	}

	for (auto& MA : MustValueMap)
	{
		MustAttributes.Add(FXmlAttribute(MA.Key, MA.Value));
	}

	AccessoryNode->SetAttributes(MustAttributes);

	AccessoryNode->AppendChildNode(TEXT("SpecialVariables"));
	FXmlNode* ChildSpecialNode = AccessoryNode->GetChildrenNodes().Last();
	ChildSpecialNode->SetAttributes(SpecialAttributes);
}
