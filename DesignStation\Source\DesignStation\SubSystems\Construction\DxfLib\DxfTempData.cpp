#include "DxfTempData.h"
#include "pch.h"
#include "src/dl_attributes.h"
#include "FormatConversion.h"


//DECLARE_LOG_CATEGORY_EXTERN(LogConstruction, Log, All)

using namespace DxfLib;


FDxfTempData* FDxfTempData::pDxfTempData = nullptr;



 void DxfLib::FDxfLineTypeData::WriteToDxfFile(const DL_Writer& DW) const
 {
	 std::string nameUpper = Name;
	 std::transform(nameUpper.begin(), nameUpper.end(), nameUpper.begin(), ::toupper);

	 if (Name.empty()) {
		 std::cerr << "DL_Dxf::writeLinetype: "
			 << "Line type name must not be empty\n";
		 return;
	 }
	/* if (nameUpper == "BYBLOCK" || nameUpper == "BYLAYER") {
		 return;
	 }*/
	 // write id (not for R12)
	 if (nameUpper == "BYBLOCK") {
		 DW.tableLinetypeEntry(0x14);
	 }
	 else if (nameUpper == "BYLAYER") {
		 DW.tableLinetypeEntry(0x15);
	 }
	 else if (nameUpper == "CONTINUOUS") {
		 DW.tableLinetypeEntry(0x16);
	 }
	 else {
		 DW.tableLinetypeEntry();
	 }

	 DW.dxfString(DL_NAME_CODE, Name);
	 DW.dxfInt(DL_LSTYLE_FLAGS_CODE, Flags);

	 if (nameUpper == "BYBLOCK") {
		 DW.dxfString(3, "");
		 DW.dxfInt(72, 65);
		 DW.dxfInt(73, 0);
		 DW.dxfReal(40, 0.0);
	 }
	 else if (nameUpper == "BYLAYER") {
		 DW.dxfString(3, "");
		 DW.dxfInt(72, 65);
		 DW.dxfInt(73, 0);
		 DW.dxfReal(40, 0.0);
	 }
	 else if (nameUpper == "CONTINUOUS") {
		 DW.dxfString(3, "Solid line");
		 DW.dxfInt(72, 65);
		 DW.dxfInt(73, 0);
		 DW.dxfReal(40, 0.0);
	 }
	 else {
		 DW.dxfString(3, Description);
		 DW.dxfInt(72, 65);
		 DW.dxfInt(73, Patterns.size());
		 DW.dxfReal(40, PatternLength);
		 
	 }
	 if (Patterns.size()>0)
	 {
		 for (int i = 0; i < Patterns.size(); i++) {
			 DW.dxfReal(49, Patterns[i]);
				 DW.dxfInt(74, 0);
		 }
	 }
 }

 void DxfLib::FDxfStyleData::WriteToDxfFile(const DL_Writer& DW) const
 {
	 DW.tableStyleEntry(DW.GetStyleHandleByStyleName(Name));
	 DW.dxfString(2, Name);
	 DW.dxfInt(70,Flags);
	 DW.dxfReal(40, FixedTextHeight);
	 DW.dxfReal(41, WidthFactor);
	 DW.dxfReal(50,ObliqueAngle);
	 DW.dxfInt(71, TextGenerationFlags);
	 DW.dxfReal(42, LastHeightUsed);

	 DW.dxfString(3, PrimaryFontFile);
	 DW.dxfString(4, BigFontFile);

	 //if (version == DL_VERSION_2000) {
		// DW.dxfString(3, "");
		// DW.dxfString(4, "");
		// DW.dxfString(1001, "ACAD");
		// //DW.dxfString(1000, style.name);
		// DW.dxfString(1000, style.primaryFontFile);
		// int xFlags = 0;
		// if (style.bold) {
		//	 xFlags = xFlags | 0x2000000;
		// }
		// if (style.italic) {
		//	 xFlags = xFlags | 0x1000000;
		// }
		// DW.dxfInt(1071, xFlags);
	 //}
	 //else {
		// DW.dxfString(3, style.primaryFontFile);
		// DW.dxfString(4, style.bigFontFile);
	 //}
	 //DW.dxfString(  0, "ENDTAB");
 }

 void DxfLib::FDxfAppIDData::WriteToDxfFile(const DL_Writer& DW) const
 {
	 if (Name.empty()) {
		 std::cerr << "DL_Dxf::writeAppid: "
			 << "Application  name must not be empty\n";
		 return;
	 }

	 std::string n = Name;
	 std::transform(n.begin(), n.end(), n.begin(), ::toupper);

	 if (n == "ACAD") {
		 DW.tableAppidEntry(0x12);
	 }
	 else {
		 DW.tableAppidEntry();
	 }
	 DW.dxfString(2, Name);
	 DW.dxfInt(70, Flags);
 }

 void DxfLib::FDxfDimensionStyle::WriteToDxfFile(const DL_Writer& DW) const
 {
	 if (Name.empty())
	 {
		 return;
	 }
	 DW.dxfString(0,"DIMSTYLE");
	 DW.handle(105);
	 DW.dxfString(100, "AcDbSymbolTableRecord");
	 DW.dxfString(100, "AcDbDimStyleTableRecord");
	 DW.dxfString(2, Name);
	
	 TryWriteValue(DW, 3, DIMPOST, !DIMPOST.empty());
	
	 TryWriteValue(DW, 4, DIMAPOST, !DIMAPOST.empty());

	 DW.dxfInt(70, Flags);

	 TryWriteValue(DW, 40, DIMSCALE, !IS_DOUBLE_ZERO(DIMSCALE));
	 TryWriteValue(DW, 41, DIMASZ, !IS_DOUBLE_ZERO(DIMASZ));
	 TryWriteValue(DW, 42, DIMEXO, !IS_DOUBLE_ZERO(DIMEXO));
	 TryWriteValue(DW, 43, DIMDLI, !IS_DOUBLE_ZERO(DIMDLI));
	 TryWriteValue(DW, 44, DIMEXE, !IS_DOUBLE_ZERO(DIMEXE));
	 TryWriteValue(DW, 45, DIMRND, !IS_DOUBLE_ZERO(DIMRND));
	 TryWriteValue(DW, 46, DIMDLE, !IS_DOUBLE_ZERO(DIMDLE));
	 TryWriteValue(DW, 47, DIMTP, !IS_DOUBLE_ZERO(DIMTP));
	 TryWriteValue(DW, 48, DIMTM, !IS_DOUBLE_ZERO(DIMTM));


	 TryWriteValue(DW, 140, DIMTXT, !IS_DOUBLE_ZERO(DIMTXT));
	 TryWriteValue(DW, 141, DIMCEN, !IS_DOUBLE_ZERO(DIMCEN));
	 TryWriteValue(DW, 142, DIMTSZ, !IS_DOUBLE_ZERO(DIMTSZ));
	 TryWriteValue(DW, 143, DIMALTF, !IS_DOUBLE_ZERO(DIMALTF));
	 TryWriteValue(DW, 144, DIMLFAC, !IS_DOUBLE_ZERO(DIMLFAC));
	 TryWriteValue(DW, 145, DIMTVP, !IS_DOUBLE_ZERO(DIMTVP));
	 TryWriteValue(DW, 146, DIMTFAC, !IS_DOUBLE_ZERO(DIMTFAC));
	 TryWriteValue(DW, 147, DIMGAP, !IS_DOUBLE_ZERO(DIMGAP));
	 TryWriteValue(DW, 148, DIMALTRND, !IS_DOUBLE_ZERO(DIMALTRND));


	 TryWriteValue(DW, 71, DIMTOL, DIMTOL>=0);
	 TryWriteValue(DW, 72, DIMLIM, DIMLIM >= 0);
	 TryWriteValue(DW, 73, DIMTIH, DIMTIH >= 0);
	 TryWriteValue(DW, 74, DIMTOH, DIMTOH >= 0);
	 TryWriteValue(DW, 75, DIMSE1, DIMSE1 >= 0);
	 TryWriteValue(DW, 76, DIMSE2, DIMSE2 >= 0);
	 TryWriteValue(DW, 77, DIMTAD, DIMTAD >= 0);
	 TryWriteValue(DW, 78, DIMZIN, DIMZIN >= 0);
	 TryWriteValue(DW, 79, DIMAZIN, DIMAZIN >= 0);
;

	 TryWriteValue(DW, 170, DIMALT, DIMALT >= 0);
	 TryWriteValue(DW, 171, DIMALTD, DIMALTD >= 0);
	 TryWriteValue(DW, 172, DIMTOFL, DIMTOFL >= 0);
	 TryWriteValue(DW, 173, DIMSAH, DIMSAH >= 0);
	 TryWriteValue(DW, 174, DIMTIX, DIMTIX >= 0);
	 TryWriteValue(DW, 175, DIMSOXD, DIMSOXD >= 0);
	 TryWriteValue(DW, 176, DIMCLRD, DIMCLRD >= 0);
	 TryWriteValue(DW, 177, DIMCLRE, DIMCLRE >= 0);
	 TryWriteValue(DW, 178, DIMCLRT, DIMCLRT >= 0);
	 TryWriteValue(DW, 179, DIMADEC, DIMADEC >= 0);


	 TryWriteValue(DW, 271, DIMDEC, DIMDEC >= 0);
	 TryWriteValue(DW, 272, DIMTDEC, DIMTDEC >= 0);
	 TryWriteValue(DW, 273, DIMALTU, DIMALTU >= 0);
	 TryWriteValue(DW, 274, DIMALTTD, DIMALTTD >= 0);
	 TryWriteValue(DW, 275, DIMAUNIT, DIMAUNIT >= 0);
	 TryWriteValue(DW, 276, DIMFRAC, DIMFRAC >= 0);
	 TryWriteValue(DW, 277, DIMLUNIT, DIMLUNIT >= 0);
	 TryWriteValue(DW, 278, DIMDSEP, DIMDSEP >= 0);
	 TryWriteValue(DW, 279, DIMTMOVE, DIMTMOVE >= 0);


	 TryWriteValue(DW, 280, DIMJUST, DIMJUST >= 0);
	 TryWriteValue(DW, 281, DIMSD1, DIMSD1 >= 0);
	 TryWriteValue(DW, 282, DIMSD2, DIMSD2 >= 0);
	 TryWriteValue(DW, 283, DIMTOLJ, DIMTOLJ >= 0);
	 TryWriteValue(DW, 284, DIMTZIN, DIMTZIN >= 0);
	 TryWriteValue(DW, 285, DIMALTZ, DIMALTZ >= 0);
	 TryWriteValue(DW, 286, DIMALTTZ, DIMALTTZ >= 0);
	 TryWriteValue(DW, 288, DIMUPT, DIMUPT >= 0);
	 TryWriteValue(DW, 289, DIMATFIT, DIMATFIT >= 0);


	 
	 unsigned long StyleHandle = DW.GetStyleHandleByStyleName(StyleName);
	 if (StyleHandle>0)
	 {
		 DW.dxfHex(340, StyleHandle);
	 }
 }

 DxfLib::FDxfBaseEntityData::FDxfBaseEntityData(const DL_Attributes& Attributes)
 {
	 Color = Attributes.getColor();
	 Color24 = Attributes.getColor24();
	 Width = Attributes.getWidth();
	 LineTypeScale = Attributes.getLinetypeScale();
	 Layer = Attributes.getLayer();
	 LineType = Attributes.getLinetype();
 }

 void DxfLib::FDxfBaseEntityData::WriteBaseData( const DL_Writer& DW)const
 {
	 // layer name:
	 DW.dxfString(8, Layer);

	 // R12 doesn't accept BYLAYER values. The value has to be missing
	 //   in that case.
	 if (Color != 256) {
		 DW.dxfInt(62, Color);
	 }
	 if (Color24 != -1) {
		 DW.dxfInt(420, Color24);
	 }
	 if (Width!=-1)
	 {
		 DW.dxfInt(370, Width);
	 }
	 if (!IS_DOUBLE_ZERO(LineTypeScale-1))
	 {
		 DW.dxfReal(48, LineTypeScale);
	 }
	 std::string linetype = LineType;
	 std::transform(linetype.begin(), linetype.end(), linetype.begin(), ::toupper);
	 if (linetype != "BYLAYER") {
		 DW.dxfString(6, linetype);
	 }
 }


 void DxfLib::FDxfLayer::WriteToDxfFile( const DL_Writer& DW) const
 {
	 if (Name.empty()) {
		 std::cerr << "DL_Dxf::writeLayer: "
			 << "Layer name must not be empty\n";
		 return;
	 }

	 int color = Color;
	 if (color >= 256) {
		 std::cerr << "Layer color cannot be " << color << ". Changed to 7.\n";
		 color = 7;
	 }
	 if (bOff) {
		 // negative color value means layer is off:
		 color = -color;
	 }

	 if (Name == "0") {
		 DW.tableLayerEntry(0x10);
	 }
	 else {
		 DW.tableLayerEntry();
	 }
	 DW.dxfString(2, Name);
	 DW.dxfInt(70, Flags);
	 DW.dxfInt(62, color);
	 if ( Color24 != -1) {
		 DW.dxfInt(420, Color24);
	 }
	
	 DW.dxfString(6, LineType.length() == 0 ?
		 std::string("CONTINUOUS") : LineType);

	 // layer defpoints cannot be plotted
	 std::string lstr = Name;
	 std::transform(lstr.begin(), lstr.end(), lstr.begin(), ::tolower);
	 if (lstr == "defpoints") {
		 DW.dxfInt(290, 0);
	 }
	 if ( Width != -1) {
		 DW.dxfInt(370, Width);
	 }
	 DW.dxfHex(390, 0xF);
 }

 void DxfLib::FDxfPointData::WriteToDxfFile( const DL_Writer& DW) const
 {
	 DW.entity("POINT");
	 DW.dxfString(DL_SUBCLASS, "AcDbEntity");

	 WriteBaseData(DW);
	 DW.dxfString(DL_SUBCLASS, "AcDbPoint");
	 DW.coord(DL_POINT_COORD_CODE, Point.X, Point.Y, Point.Z);
 }

 void DxfLib::FDxfLineData::WriteToDxfFile( const DL_Writer& DW) const
 {
	 DW.entity("LINE");
	 DW.dxfString(DL_SUBCLASS, "AcDbEntity");
	 WriteBaseData(DW);
	 DW.dxfString(DL_SUBCLASS, "AcDbLine");
	 DW.coord(DL_LINE_START_CODE,StartPoint.X, StartPoint.Y, StartPoint.Z);
	 DW.coord(DL_LINE_END_CODE, EndPoint.X,EndPoint.Y,EndPoint.Z);
 }

 void DxfLib::FDxfXLineData::WriteToDxfFile(const DL_Writer& DW) const
 {
	 DW.entity("XLINE");
	 DW.dxfString(DL_SUBCLASS, "AcDbEntity");
	 WriteBaseData(DW);
	 DW.dxfString(DL_SUBCLASS, "AcDbLine");
	 DW.coord(DL_LINE_START_CODE, Point.X, Point.Y, Point.Z);
	 DW.coord(DL_LINE_END_CODE, Dir.X, Dir.Y, Dir.Z);
 }

 void DxfLib::FDxfRayData::WriteToDxfFile(const DL_Writer& DW) const
 {

	 DW.entity("RAY");
	 DW.dxfString(DL_SUBCLASS, "AcDbEntity");
	 WriteBaseData(DW);
	 DW.dxfString(DL_SUBCLASS, "AcDbLine");
	 DW.coord(DL_LINE_START_CODE, Point.X, Point.Y, Point.Z);
	 DW.coord(DL_LINE_END_CODE, Dir.X, Dir.Y, Dir.Z);
 }

 void DxfLib::FDxfArcData::WriteToDxfFile(const DL_Writer& DW) const
 {
	 DW.entity("ARC");
	 DW.dxfString(DL_SUBCLASS, "AcDbEntity");
	 WriteBaseData(DW);
	 DW.dxfString(DL_SUBCLASS, "AcDbCircle");
	 DW.coord(DL_CENTER_CODE, Center.X, Center.Y, Center.Z);
	 DW.dxfReal(DL_RADIUS_CODE, Radius);
	 DW.dxfString(DL_SUBCLASS, "AcDbArc");
	 DW.dxfReal(DL_ANGLE_CODE, StartAngle);
	 DW.dxfReal(51, EndAngle);

 }

 void DxfLib::FDxfCircleData::WriteToDxfFile(const DL_Writer& DW) const
 {
	 DW.entity("CIRCLE");
	 DW.dxfString(100, "AcDbEntity");
	 WriteBaseData(DW);
	 DW.dxfString(100, "AcDbCircle");
	 DW.coord(DL_CENTER_CODE, Center.X,Center.Y,Center.Z);
	 DW.dxfReal(DL_RADIUS_CODE, Radius);
 }

 DxfLib::FDxfLWPolyLineData::FDxfLWPolyLineData(const uint& InVertexNum, const int& InFlags, const DL_Attributes& InAttributes)
	 : FDxfBaseEntityData(InAttributes)
 {
	 VertexNum = InVertexNum;
	 Flags = InFlags;
 }


 void DxfLib::FDxfLWPolyLineData::AddPoint(const FVector& InPoint)
 {
	 Points.Add(InPoint);
 }
 void DxfLib::FDxfLWPolyLineData::SetPoints(const TArray<FVector>& InPoints)
 {
	 Points = InPoints;
 }

 void DxfLib::FDxfLWPolyLineData::WriteToDxfFile(const DL_Writer& DW) const
 {

	 DW.entity("LWPOLYLINE");
	 DW.dxfString(100, "AcDbEntity");
	 WriteBaseData(DW);
	 DW.dxfString(100, "AcDbPolyline");
	 DW.dxfInt(90, VertexNum);
	 DW.dxfInt(70, Flags);
	 for (size_t i = 0; i < Points.Num(); i++)
	 {
		 DW.dxfReal(10, Points[i].X);
		 DW.dxfReal(20, Points[i].Y);
	 }
 }


 void DxfLib::FDxfTextData::WriteToDxfFile(const DL_Writer& DW) const
 {

	 DW.entity("TEXT");
	 DW.dxfString(DL_SUBCLASS, "AcDbEntity");
	 WriteBaseData(DW);
	 DW.dxfString(DL_SUBCLASS, "AcDbText");
	 DW.coord(DL_FIRST_XCOORD_CODE, InsertPoint.X, InsertPoint.Y, InsertPoint.Z);
	 DW.dxfReal(DL_TXTHI_CODE, Height);
	 DW.dxfString(DL_TEXTVAL_CODE, Text);
	 if (!IS_DOUBLE_ZERO(TextAngle))
	 {
		 DW.dxfReal(DL_ROTATION_CODE, TextAngle);
	 }
	 if (XScaleFactor != 1)
	 {
		 DW.dxfReal(DL_SCALE_X_CODE, XScaleFactor);
	 }
	 if (!Style.empty() && !Style._Equal("Standard"))
	 {
		 DW.dxfString(DL_TXT_STYLE_CODE, Style);
	 }
	 if (TextGenerationFlags != 0)
	 {
		 DW.dxfInt(71, TextGenerationFlags);
	 }
	 if (hJustification != 0)
	 {
		 DW.dxfInt(72, hJustification);
	 }
	 if (vJustification != 0 || hJustification != 0)
	 {
		 DW.coord(DL_FIRST_XCOORD_CODE + 1, AlignmentPoint.X, AlignmentPoint.Y, AlignmentPoint.Z);
	 }
	 // required twice for some reason:
	 DW.dxfString(DL_SUBCLASS, "AcDbText");

	 DW.dxfInt(73, vJustification);
 }
 void DxfLib::FDxfMTextData::WriteToDxfFile(const DL_Writer& DW) const
 {
	 DW.entity("MTEXT");
	 DW.dxfString(DL_SUBCLASS, "AcDbEntity");
	 WriteBaseData(DW);
	 DW.dxfString(DL_SUBCLASS, "AcDbMText");
	 DW.coord(DL_CRDGRP_START, InsertPoint.X, InsertPoint.Y, InsertPoint.Z);
	 DW.dxfReal(DL_TXTHI_CODE, Height);
	 DW.dxfReal(41, BoxWidth);

	 DW.dxfInt(71, AttachmentPoint);
	 DW.dxfInt(72, DrawingDir);

	 // Creare text chunks of 250 characters each:
	 int length = Text.length();
	 char chunk[251];
	 int i;
	 for (i = 250; i < length; i += 250) {
		 strncpy_s(chunk, 251, &Text.c_str()[i - 250], 250);
		 chunk[250] = '\0';
		 DW.dxfString(3, chunk);
	 }
	 strncpy_s(chunk, 251, &Text.c_str()[i - 250], 250);
	 chunk[250] = '\0';
	 DW.dxfString(DL_TEXTVAL_CODE, chunk);

	 if (!Style.empty() && !Style._Equal("Standard"))
	 {
		 DW.dxfString(DL_TXT_STYLE_CODE, Style);
	 }
	 if (!IS_DOUBLE_ZERO(TextAngle))
	 {
		 // since dxflib 2.0.2.1: degrees not rad (error in autodesk dxf doc)
		 DW.dxfReal(DL_ROTATION_CODE, TextAngle);
	 }
	 DW.dxfInt(73, LineSpacingStyle);
	 DW.dxfReal(44,LineSpacingFactor);
 }

 void DxfLib::FDxfHatchPolyLineEdgeData::WriteToDxfFile(const DL_Writer& DW) const
 {
	 int EdgeNum = Points.size();
	 if (EdgeNum<0)
	 {
		 //UE_LOG(LogConstruction, Error, TEXT("Hatch Boundary Edge Points Is Null"));
		 return;
	 }
	 DW.dxfInt(92, PathTypeFlags);
	 DW.dxfInt(72, bConvexity);
	 DW.dxfInt(73, bClose);
	 DW.dxfInt(93, Points.size());
	 for (size_t i = 0; i < Points.size(); i++)
	 {
		 const FVector& Vector = Points[i];
		 DW.dxfReal(10, Vector.X);
		 DW.dxfReal(20, Vector.Y);
		 if (!IS_DOUBLE_ZERO(Vector.Z))
		 {
			 DW.dxfReal(42, Vector.Z);
		 }
	 }
	 //不支持源边界对象引用
	 DW.dxfInt(97, 0);
 }

 bool DxfLib::FDxfHatchPolyLineEdgeData::IsValid() const
 {
	 return Points.size()>0;
 }

 void DxfLib::FDxfHatchPolyLineEdgeData::AddPoints(const FVector& InPoint)
 {
	 Points.push_back(InPoint);
 }


 void DxfLib::FDxfHatchData::WriteToDxfFile(const DL_Writer& DW) const
 {
	 int BoundaryNum = Edges.size();
	 if (BoundaryNum <1)
	 {
		 //UE_LOG(LogConstruction, Error, TEXT("HatchEdgesIsNull"));
		 return;
	 }

	 DW.entity("HATCH");
	 DW.dxfString(100, "AcDbEntity");
	 WriteBaseData(DW);
	 DW.dxfString(100, "AcDbHatch");
	 DW.dxfReal(10, 0.0);             // elevation
	 DW.dxfReal(20, 0.0);
	 DW.dxfReal(30, 0.0);
	 DW.dxfReal(210, 0.0);             // extrusion dir.
	 DW.dxfReal(220, 0.0);
	 DW.dxfReal(230, 1.0);
	 if (HatchPattern.bSolid == false) {
		 DW.dxfString(2, HatchPattern.Name);
	 }
	 else {
		 DW.dxfString(2, "SOLID");
	 }
	 DW.dxfInt(70, (int)HatchPattern.bSolid);
	 DW.dxfInt(71, 0);                // non-associative
	 DW.dxfInt(91, BoundaryNum);

	 for (size_t i = 0; i < Edges.size(); i++)
	 {
		 Edges[i]->WriteToDxfFile(DW);
	 }

	 DW.dxfInt(75, 0);
	 DW.dxfInt(76, 1);
	 if (!HatchPattern.bSolid)
	 {
		 DW.dxfReal(52, HatchPattern.Angle);
		 DW.dxfReal(41, HatchPattern.Scale);
		 DW.dxfInt(77, 0);
		 
		 DW.dxfInt(78, 1);
		 DW.dxfReal(53, HatchPattern.Angle);
		 DW.dxfReal(43, 0.0);
		 DW.dxfReal(44, 0.0);
		 DW.dxfReal(45, HatchPattern.Scale*-2.245064030267288);
		 DW.dxfReal(46, HatchPattern.Scale * 2.245064030267288);
		 DW.dxfInt(79, 0);
	 }
	 DW.dxfReal(47, 3.0);
	 DW.dxfInt(98, 0);
	 DW.dxfString(1001, "ACAD");
	 DW.dxfReal(1010, HatchPattern.Origin.X);
	 DW.dxfReal(1020, HatchPattern.Origin.Y);
	 DW.dxfReal(1030, 0.0);
	
 }
 void DxfLib::FDxfHatchData::AddEdge(FDxfHatchEdgeData* pEdge)
 {
	 if (pEdge)
	 {
		 Edges.push_back(pEdge);
	 }
 }


 void DxfLib::FDxfAttDefData::WriteToDxfFile(const DL_Writer& DW) const
 {
	 DW.entity("ATTDEF");
	 DW.dxfString(DL_SUBCLASS, "AcDbEntity");
	 WriteBaseData(DW);
	 DW.dxfString(DL_SUBCLASS, "AcDbText");
	 DW.coord(DL_CRDGRP_START, InsertPoint.X, InsertPoint.Y, InsertPoint.Z);
	 DW.dxfReal(DL_TXTHI_CODE, Height);
	 DW.dxfString(DL_ATT_VAL_CODE, Text);
	 if (!IS_DOUBLE_ZERO(TextAngle))
	 {
		 DW.dxfReal(DL_ROTATION_CODE, TextAngle);
	 }
	 if (XScaleFactor != 1)
	 {
		 DW.dxfReal(DL_SCALE_X_CODE, XScaleFactor);
	 }
	 if ( !Style.empty() && !Style._Equal("Standard"))
	 {
		 DW.dxfString(DL_TXT_STYLE_CODE, Style);
	 }
	 if (TextGenerationFlags!= 0)
	 {
		 DW.dxfInt(71, TextGenerationFlags);
	 }
	 if (hJustification != 0)
	 {
		 DW.dxfInt(72, hJustification);
	 }
	 if (vJustification!=0 ||hJustification != 0)
	 {
		 DW.coord(DL_FIRST_XCOORD_CODE + 1, AlignmentPoint.X, AlignmentPoint.Y, AlignmentPoint.Z);
	 }
	 DW.dxfString(DL_SUBCLASS, "AcDbAttributeDefinition");

	 DW.dxfString(DL_ATT_TAG_CODE, Tag);
	
	
	 if (vJustification != 0)
	 {
		 DW.dxfInt(74, vJustification);
	 }
 }

 void DxfLib::FDxfBlockData::AddChildEntity(FDxfBaseEntityData* InEntity)
 {
	 if (InEntity)
	 {
		 ChildrenEntities.push_back(InEntity);
	 }
 }

 void DxfLib::FDxfBlockData::AddAttDef(const FDxfAttDefData* AttDef)
 {
	 auto ExistAttDef =  Attdefs.find(AttDef->Tag);
	 if (ExistAttDef == Attdefs.end())
	 {
		 Attdefs[AttDef->Tag] = AttDef;
	 }
 }

 void DxfLib::FDxfBlockData::WriteToDxfFile(const DL_Writer& DW) const
 {
	 //
	 DW.dxfString(0, "BLOCK");
	 DW.handle();
	 //dxfHex(330, blockHandle);
	 DW.dxfString(100, "AcDbEntity");
	 /*if (h == 0x1C) {
		 dxfInt(67, 1);
	 }*/
	 DW.dxfString(8, Layer);                 
	 DW.dxfString(100, "AcDbBlockBegin");
	 std::string ShowerName = SourceName.empty()?Name:Name + "-" + SourceName;
	 DW.dxfString(2, ShowerName);
	 DW.dxfInt(70, 0);
	 DW.coord(10, Point.X, Point.Y,Point.Z);
	 DW.dxfString(3, ShowerName);
	 DW.dxfString(1, "");

	 for (size_t i = 0; i < ChildrenEntities.size(); i++)
	 {
		 ChildrenEntities[i]->WriteToDxfFile(DW);
	 }
	 for (auto Itemr  = Attdefs.begin(); Itemr  !=  Attdefs.end(); Itemr ++)
	 {
		 Itemr->second->WriteToDxfFile(DW);
	 }
	 DW.sectionBlockEntryEnd();
 }

 void DxfLib::FDxfBlockData::GetShowerName( std::string& ShowerName)const 
 {
	 ShowerName = SourceName.empty() ? Name : Name + "-" + SourceName;
 }

 const FDxfAttDefData*  DxfLib::FDxfBlockData::GetAttDefByTag(const std::string& InTag) const
 {
	 auto ExistAttdef = Attdefs.find(InTag);
	 if (ExistAttdef == Attdefs.end())
	 {
		 return nullptr;
	 }
	 return ExistAttdef->second;
 }
 void DxfLib::FDxfInsertData::WriteToDxfFile(const DL_Writer& DW) const
 {
	 FString TempName = DxfStrToUEStr(Name);
	 FString TempSourceName = DxfStrToUEStr(SourceName);
	 const FDxfBlockData* BlockData = FDxfTempData::GetInstance()->GetBlockByName(FName(TempName), TempSourceName);
	 if (BlockData == nullptr)
	 {
		 return;
	 }
	 if (Name.empty()) {
		 return;
	 }
	 DW.entity("INSERT");
	 DW.dxfString(100, "AcDbEntity");
	 WriteBaseData(DW);
	 DW.dxfString(100, "AcDbBlockReference");

	 DW.dxfString(2, SourceName.empty() ? Name : Name+"-"+SourceName);
	 DW.coord(DL_INSERT_COORD_CODE, Point.X, Point.Y, Point.Z);
	 if (Scale.X != 1.0 || Scale.Y != 1.0) {
		 DW.dxfReal(41, Scale.X);
		 DW.dxfReal(42, Scale.Y);
		 DW.dxfReal(43, 1);
	 }
	 if (TextAngle != 0.0) {
		 DW.dxfReal(50, TextAngle);
	 }
	 //写入Attrib
	 for (auto Itemr = BlockData->Attdefs.begin(); Itemr != BlockData->Attdefs.end(); Itemr++)
	 {
		 std::string Text = "";
		 auto ExistAttrib = Attribs.find(Itemr->second->Tag);
		 if (ExistAttrib == Attribs.end())
		 {
			 Text = Itemr->second->Text;
		 }
		 else
		 {
			 Text = ExistAttrib->second.Text;
		 }
		 const FDxfAttDefData* Attdef = Itemr->second;
		 DL_Attributes Attribute(Attdef->Layer,Attdef->Color, Attdef->Color24,Attdef->Width,Attdef->LineType,Attdef->LineTypeScale);
		 FVector Insert =GetRotatePosition(Attdef->InsertPoint*Scale + Point, TextAngle, Point);
		 FVector Alignment = GetRotatePosition(Attdef->AlignmentPoint*Scale + Point, TextAngle, Point);

		 double Height = std::min(Scale.X, Scale.Y) * Attdef->Height;

		 FDxfInsertAttrib InsertAttrib = FDxfInsertAttrib(Insert, Alignment, Height,
			 Attdef->XScaleFactor, Attdef->TextGenerationFlags, Attdef->hJustification,
			 Attdef->vJustification, Attdef->TextAngle+TextAngle, Text, Attdef->Style, Attdef->Tag, Attribute);
		 InsertAttrib.WriteToDxfFile(DW);
	 }
	 if (BlockData->Attdefs.size() > 0)
	 {
		 DW.entity("SEQEND");
		 DW.dxfString(100, "AcDbEntity");
	 }
 }

 void DxfLib::FDxfInsertData::AddAttrib(const FDxfAttrib& InAttrib)
 {
	 auto ExistAttrib = Attribs.find(InAttrib.Tag);
	 if (ExistAttrib == Attribs.end())
	 {
		 Attribs[InAttrib.Tag] = InAttrib;
	 }
 }

 void DxfLib::FDxfInsertData::AddAttrib(const std::string& InTag, const std::string& InText)
 {
	 FDxfAttrib Attrib = FDxfAttrib(InTag, InText);
	 AddAttrib(Attrib);
 }

 void DxfLib::FDxfInsertAttrib::WriteToDxfFile(const DL_Writer& DW) const
 {

	 DW.entity("ATTRIB");
	 DW.dxfString(DL_SUBCLASS, "AcDbEntity");
	 WriteBaseData(DW);
	 DW.dxfString(DL_SUBCLASS, "AcDbText");
	 DW.coord(DL_FIRST_XCOORD_CODE, InsertPoint.X, InsertPoint.Y, InsertPoint.Z);
	 DW.dxfReal(DL_TXTHI_CODE, Height);
	 DW.dxfString(DL_ATT_VAL_CODE, Text);
	 if (!IS_DOUBLE_ZERO(TextAngle))
	 {
		 DW.dxfReal(DL_ROTATION_CODE, TextAngle);
	 }
	 if (XScaleFactor != 1)
	 {
		 DW.dxfReal(DL_SCALE_X_CODE, XScaleFactor);
	 }
	 if (!Style.empty() && !Style._Equal("Standard"))
	 {
		 DW.dxfString(DL_TXT_STYLE_CODE, Style);
	 }
	 if (TextGenerationFlags != 0)
	 {
		 DW.dxfInt(71, TextGenerationFlags);
	 }
	 if (hJustification != 0)
	 {
		 DW.dxfInt(72, hJustification);
	 }
	 if (vJustification != 0 || hJustification != 0)
	 {
		 DW.coord(DL_FIRST_XCOORD_CODE + 1, AlignmentPoint.X, AlignmentPoint.Y, AlignmentPoint.Z);
	 }
	 DW.dxfString(DL_SUBCLASS, "AcDbAttribute");

	 DW.dxfString(DL_ATT_TAG_CODE, Tag);

	 if (vJustification != 0)
	 {
		 DW.dxfInt(74, vJustification);
	 }
 }

#pragma region  Dimemsion

 void DxfLib::FDxfDimensionBase::WriteDimStyleOverrides(const DL_Writer& DW) const
 {
	
	 DW.dxfString(1001, "ACAD");
	 DW.dxfString(1000, "DSTYLE");
	 DW.dxfString(1002, "{");
	 if (Type & 0x80) {
		 // custom text position:
		 DW.dxfInt(1070, 279);
		 DW.dxfInt(1070, 2);
	 }
	 
	 if (LinearFactor>0)
	 {
		 DW.dxfInt(1070, 144);
		 DW.dxfReal(1040, LinearFactor);
	 }
	
	 if (DimScale>0)
	 {
		 DW.dxfInt(1070, 40);
		 DW.dxfReal(1040, DimScale);
	 }
	 DW.dxfString(1002, "}");
 }

 void DxfLib::FDxfDimensionBase::WriteDimBaseData(const DL_Writer& DW) const
 {
	 DW.coord(10, DefinitionPoint.X,
		 DefinitionPoint.Y,
		 DefinitionPoint.Z);

	 DW.coord(11, TextPoint.X, TextPoint.Y, 0);

	 DW.dxfInt(70, Type);
	 DW.dxfInt(71, AttachmentPoint);
	 DW.dxfInt(72, LineSpacingStyle); // opt
	 //DW.dxfInt(74, DimensionBaseData.Arrow1Flipped);
	 //DW.dxfInt(75, DimensionBaseData.Arrow2Flipped);
	 DW.dxfReal(41, LineSpacingFactor); // opt

	 DW.dxfReal(42, TextAngle);

	 DW.dxfString(1, Text);   // opt
	 if (Style.empty())
	 {
		 DW.dxfString(3, "Standard");
	 }
	 else
	 {
		 DW.dxfString(3, Style);
		 //DW.dxfString(3, "Standard");
	 }
 }


 void DxfLib::FDxfDimAlignedData::WriteToDxfFile(const DL_Writer& DW) const
 {
	 DW.entity("DIMENSION");

	 DW.dxfString(100, "AcDbEntity");
	 WriteBaseData(DW);
	 DW.dxfString(100, "AcDbDimension");
	 
	 DimensionBaseData.WriteDimBaseData(DW);

	 DW.dxfString(100, "AcDbAlignedDimension");
	 DW.coord(13, ExtenPoint1.X, ExtenPoint1.Y, 0.0);
	 DW.coord(14, ExtenPoint2.X, ExtenPoint2.Y, 0.0);

	 DimensionBaseData.WriteDimStyleOverrides(DW);

 }
 void DxfLib::FDxfDimLinearData::WriteToDxfFile(const DL_Writer& DW) const
 {
	 DW.entity("DIMENSION");

	 DW.dxfString(100, "AcDbEntity");
	 WriteBaseData(DW);
	 DW.dxfString(100, "AcDbDimension");

	 DimensionBaseData.WriteDimBaseData(DW);

	 DW.dxfString(100, "AcDbAlignedDimension");

	 DW.coord(13, ExtenPoint1.X, ExtenPoint1.Y, 0.0);
	 DW.coord(14, ExtenPoint2.X, ExtenPoint2.Y, 0.0);
	 DW.dxfReal(50, Angle);
	 //DW.dxfReal(50, Angle / (2.0 * M_PI) * 360.0);
	 DW.dxfString(100, "AcDbRotatedDimension");
	 DimensionBaseData.WriteDimStyleOverrides(DW);
 }

 void DxfLib::FDxfDimRadialData::WriteToDxfFile(const DL_Writer& DW) const
 {

	 DW.entity("DIMENSION");

	 DW.dxfString(100, "AcDbEntity");
	 WriteBaseData(DW);
	 DW.dxfString(100, "AcDbDimension");

	 DimensionBaseData.WriteDimBaseData(DW);

	 DW.dxfString(100, "AcDbRadialDimension");

	 DW.coord(15, CirclePoint.X, CirclePoint.Y, 0.0);
	 DW.dxfReal(40, Leader);

	 DimensionBaseData.WriteDimStyleOverrides(DW);
 }

 void DxfLib::FDxfDimDiametricData::WriteToDxfFile(const DL_Writer& DW) const
 {
	 DW.entity("DIMENSION");

	 DW.dxfString(100, "AcDbEntity");
	 WriteBaseData(DW);
	 DW.dxfString(100, "AcDbDimension");

	 DimensionBaseData.WriteDimBaseData(DW);
	 DW.dxfString(100, "AcDbDiametricDimension");

	 DW.coord(15, CirclePoint.X, CirclePoint.Y, 0.0);
	 DW.dxfReal(40, Leader);
	 DimensionBaseData.WriteDimStyleOverrides(DW);
 }

 void DxfLib::FDxfDimAngular2LineData::WriteToDxfFile(const DL_Writer& DW) const
 {
	 DW.entity("DIMENSION");

	 DW.dxfString(100, "AcDbEntity");
	 WriteBaseData(DW);
	 DW.dxfString(100, "AcDbDimension");

	 DimensionBaseData.WriteDimBaseData(DW);
	 DW.dxfString(100, "AcDb2LineAngularDimension");

	 DW.coord(13, LinePoint1.X, LinePoint1.Y, 0.0);
	 DW.coord(14, LinePoint2.X, LinePoint2.Y, 0.0);
	 DW.coord(15, LinePoint3.X, LinePoint3.Y, 0.0);
	 DW.coord(16, LinePoint4.X, LinePoint4.Y, 0.0);
 }

 void DxfLib::FDxfDimAngular3PointData::WriteToDxfFile(const DL_Writer& DW) const
 {
	 DW.entity("DIMENSION");

	 DW.dxfString(100, "AcDbEntity");
	 WriteBaseData(DW);
	 DW.dxfString(100, "AcDbDimension");

	 DimensionBaseData.WriteDimBaseData(DW);
	 DW.dxfString(100, "AcDb3PointAngularDimension");

	 DW.coord(13, Point1.X, Point1.Y, 0.0);
	 DW.coord(14, Point2.X, Point2.Y, 0.0);
	 DW.coord(15, Point3.X, Point3.Y, 0.0);
 }

 void DxfLib::FDxfDimOrdinateData::WriteToDxfFile(const DL_Writer& DW) const
 {
	 DW.entity("DIMENSION");

	 DW.dxfString(100, "AcDbEntity");
	 WriteBaseData(DW);
	 DW.dxfString(100, "AcDbDimension");

	 DW.coord(10, DimensionBaseData.DefinitionPoint.X,
		 DimensionBaseData.DefinitionPoint.Y,
		 DimensionBaseData.DefinitionPoint.Z);

	 DW.coord(11, DimensionBaseData.TextPoint.X, DimensionBaseData.TextPoint.Y, 0);
	 int NewType = DimensionBaseData.Type;
	 if (bXType)
	 {
		 NewType |= 0x40;
	 }
	 DW.dxfInt(70, NewType);
	 DW.dxfInt(71, DimensionBaseData.AttachmentPoint);
	 DW.dxfInt(72, DimensionBaseData.LineSpacingStyle); // opt
	 //DW.dxfInt(74, DimensionBaseData.Arrow1Flipped);
	 //DW.dxfInt(75, DimensionBaseData.Arrow2Flipped);
	 DW.dxfReal(41, DimensionBaseData.LineSpacingFactor); // opt

	 DW.dxfReal(42, DimensionBaseData.TextAngle);

	 DW.dxfString(1, DimensionBaseData.Text);   // opt
	 DW.dxfString(3, DimensionBaseData.Style);
	 //DW.dxfString(3, "Standard");
	 DW.dxfString(100, "AcDbOrdinateDimension");

	 DW.coord(13, StartPoint.X, StartPoint.Y, 0.0);
	 DW.coord(14, EndPoint.X, EndPoint.Y, 0.0);
 }

#pragma endregion

 void DxfLib::FDxfHeader::AddHeaderItem(FDxfHeaderItemBase* pHeaderItem)
 {
	 if (pHeaderItem)
	 {
		 HeaderItems.push_back(pHeaderItem);
	 }
 }

 void DxfLib::FDxfHeader::WriteToDxfFile(const DL_Writer& DW)const
 {
	 DW.sectionHeader();
	 for (size_t i = 0; i < HeaderItems.size(); i++)
	 {
		 if (HeaderItems[i])
		 {
			 HeaderItems[i]->WriteToDxfFile(DW);
		 }
	 }
	 DW.sectionEnd();
 }


 void DxfLib::FDxfTables::AddLineType(FDxfLineTypeData* pLineTypeData)
 {
	 if (pLineTypeData)
	 {
		 LineTypeDatas.push_back(pLineTypeData);
	 }
 }

 void DxfLib::FDxfTables::AddLayer(FDxfLayer* pLayer)
 {
	 if (pLayer)
	 {
		 Layers.push_back(pLayer);
	 }
 }

 void DxfLib::FDxfTables::AddStyle(FDxfStyleData* pStyle)
 {
	 if (pStyle)
	 {
		 Styles.push_back(pStyle);
	 }
 }

 void DxfLib::FDxfTables::AddAppID(FDxfAppIDData* pAppID)
 {
	 if (pAppID)
	 {
		 AppIds.push_back(pAppID);
	 }
 }

 void DxfLib::FDxfTables::AddDimendionStyle(FDxfDimensionStyle* pDimStyle)
 {
	 if (pDimStyle)
	 {
		 DimStyles.push_back(pDimStyle);
	 }
 }

 void DxfLib::FDxfTables::WriteToDxfFile(const DL_Writer& DW) const
 {
	 DW.sectionTables();

	 WriteSampleVport(DW);

	 //LineType
	 DW.tableLinetypes(LineTypeDatas.size());
	 for (size_t i = 0; i < LineTypeDatas.size(); i++)
	 {
		 LineTypeDatas[i]->WriteToDxfFile(DW);
	 }
	 DW.tableEnd();
	 //Layer
	 DW.tableLayers(Layers.size());
	 for (size_t i = 0; i < Layers.size(); i++)
	 {
		 Layers[i]->WriteToDxfFile(DW);
	 }
	 DW.tableEnd();
	 //Style
	 DW.tableStyle(Styles.size());
	 for (size_t i = 0; i < Styles.size(); i++)
	 {
		 Styles[i]->WriteToDxfFile(DW);
	 }
	 DW.tableEnd();

	 //View	
	 WriteSampleView(DW);
	 //UCS
	 WriteSampleUCS(DW);
	 //AppID
	 WriteAppIDs(DW);
	 //DimStyle
	 WriteDimStyle(DW);
	 //BlockRecord
	 WriteSampleBlockRecord(DW);

	 //
	 DW.sectionEnd();
 }

 void DxfLib::FDxfTables::WriteToDxfFile(const DL_Writer& DW, const FBlockSource* DefaultBlocks) const
 {
	 DW.sectionTables();

	 WriteSampleVport(DW);

	 //LineType
	 DW.tableLinetypes(LineTypeDatas.size());
	 for (size_t i = 0; i < LineTypeDatas.size(); i++)
	 {
		 LineTypeDatas[i]->WriteToDxfFile(DW);
	 }
	 DW.tableEnd();
	 //Layer
	 DW.tableLayers(Layers.size());
	 for (size_t i = 0; i < Layers.size(); i++)
	 {
		 Layers[i]->WriteToDxfFile(DW);
	 }
	 DW.tableEnd();
	 //Style
	 DW.tableStyle(Styles.size());
	 for (size_t i = 0; i < Styles.size(); i++)
	 {
		 Styles[i]->WriteToDxfFile(DW);
	 }
	 DW.tableEnd();

	 //View	
	 WriteSampleView(DW);
	 //UCS
	 WriteSampleUCS(DW);
	 //AppID
	 WriteAppIDs(DW);
	 //DimStyle
	 WriteDimStyle(DW);
	 //BlockRecord
	 WriteSampleBlockRecord(DW,false);

	 if (DefaultBlocks != nullptr)
	 {
		 for (auto& Ite : DefaultBlocks->GetBlocks())
		 {
			 const FDxfBlockData* NeedBlock = Ite.Value;
			 if (!NeedBlock)
			 {
				 continue;
			 }
			 std::string ShowerName;
			 NeedBlock->GetShowerName(ShowerName);
			 DW.dxfString(0, "BLOCK_RECORD");
			 DW.handle();
			 //int msh = DW.handle();
			 //DW.setModelSpaceHandle(msh);
			 //DW.dxfHex(330, 1);
			 DW.dxfString(100, "AcDbSymbolTableRecord");
			 DW.dxfString(100, "AcDbBlockTableRecord");
			 DW.dxfString(2, ShowerName);
			 DW.dxfHex(340, 0);
		 }
	 }

	 DW.tableEnd();
	 //
	 DW.sectionEnd();
 }

 void DxfLib::FDxfTables::WriteSampleUCS(const DL_Writer& DW) const
 {
	 DW.dxfString(0, "TABLE");
	 DW.dxfString(2, "UCS");
	 DW.dxfHex(5, 7);
	 //DW.dxfHex(330, 0);
	 DW.dxfString(100, "AcDbSymbolTable");
	 DW.dxfInt(70, 0);
	 DW.dxfString(0, "ENDTAB");
 }

 void DxfLib::FDxfTables::WriteSampleVport(const DL_Writer& DW) const
 {
	 DW.dxfString(0, "TABLE");
	 DW.dxfString(2, "VPORT");
	 DW.dxfHex(5, 0x8);
	 //DW.dxfHex(330, 0);
	 DW.dxfString(100, "AcDbSymbolTable");
	 DW.dxfInt(70, 1);
	 DW.dxfString(0, "VPORT");
	 //DW.dxfHex(5, 0x2F);
	 DW.handle();
	 //DW.dxfHex(330, 8);
	 DW.dxfString(100, "AcDbSymbolTableRecord");
	 DW.dxfString(100, "AcDbViewportTableRecord");
	 DW.dxfString(2, "*Active");
	 DW.dxfInt(70, 0);
	 DW.dxfReal(10, 0.0);
	 DW.dxfReal(20, 0.0);
	 DW.dxfReal(11, 1.0);
	 DW.dxfReal(21, 1.0);
	 DW.dxfReal(12, 286.3055555555555);
	 DW.dxfReal(22, 148.5);
	 DW.dxfReal(13, 0.0);
	 DW.dxfReal(23, 0.0);
	 DW.dxfReal(14, 10.0);
	 DW.dxfReal(24, 10.0);
	 DW.dxfReal(15, 10.0);
	 DW.dxfReal(25, 10.0);
	 DW.dxfReal(16, 0.0);
	 DW.dxfReal(26, 0.0);
	 DW.dxfReal(36, 1.0);
	 DW.dxfReal(17, 0.0);
	 DW.dxfReal(27, 0.0);
	 DW.dxfReal(37, 0.0);
	 DW.dxfReal(40, 297.0);
	 DW.dxfReal(41, 1.92798353909465);
	 DW.dxfReal(42, 50.0);
	 DW.dxfReal(43, 0.0);
	 DW.dxfReal(44, 0.0);
	 DW.dxfReal(50, 0.0);
	 DW.dxfReal(51, 0.0);
	 DW.dxfInt(71, 0);
	 DW.dxfInt(72, 100);
	 DW.dxfInt(73, 1);
	 DW.dxfInt(74, 3);
	 DW.dxfInt(75, 1);
	 DW.dxfInt(76, 1);
	 DW.dxfInt(77, 0);
	 DW.dxfInt(78, 0);

	 DW.dxfInt(281, 0);
	 DW.dxfInt(65, 1);
	 DW.dxfReal(110, 0.0);
	 DW.dxfReal(120, 0.0);
	 DW.dxfReal(130, 0.0);
	 DW.dxfReal(111, 1.0);
	 DW.dxfReal(121, 0.0);
	 DW.dxfReal(131, 0.0);
	 DW.dxfReal(112, 0.0);
	 DW.dxfReal(122, 1.0);
	 DW.dxfReal(132, 0.0);
	 DW.dxfInt(79, 0);
	 DW.dxfReal(146, 0.0);
	 DW.dxfString(0, "ENDTAB");
 }

 void DxfLib::FDxfTables::WriteSampleView(const DL_Writer& DW) const
 {
	 DW.dxfString(0, "TABLE");
	 DW.dxfString(2, "VIEW");
	 DW.dxfHex(5, 6);
	 //dw.dxfHex(330, 0);
	 DW.dxfString(100, "AcDbSymbolTable");
	 DW.dxfInt(70, 0);
	 DW.dxfString(0, "ENDTAB");
 }

 void DxfLib::FDxfTables::WriteSampleBlockRecord(const DL_Writer& DW, bool bWriterEnd) const
 {
	 //BlockRecords
	 DW.dxfString(0, "TABLE");
	 DW.dxfString(2, "BLOCK_RECORD");
	 DW.dxfHex(5, 1);
	 //DW.dxfHex(330, 0);
	 DW.dxfString(100, "AcDbSymbolTable");
	 DW.dxfInt(70, 1);

	 DW.dxfString(0, "BLOCK_RECORD");
	 DW.dxfHex(5, 0x1F);
	 //int msh = DW.handle();
	 //DW.setModelSpaceHandle(msh);
	 //DW.dxfHex(330, 1);
	 DW.dxfString(100, "AcDbSymbolTableRecord");
	 DW.dxfString(100, "AcDbBlockTableRecord");
	 DW.dxfString(2, "*Model_Space");
	 DW.dxfHex(340, 0x22);

	 DW.dxfString(0, "BLOCK_RECORD");
	 DW.dxfHex(5, 0x1B);
	 //int psh = DW.handle();
	 //DW.setPaperSpaceHandle(psh);
	 //DW.dxfHex(330, 1);
	 DW.dxfString(100, "AcDbSymbolTableRecord");
	 DW.dxfString(100, "AcDbBlockTableRecord");
	 DW.dxfString(2, "*Paper_Space");
	 DW.dxfHex(340, 0x1E);

	 DW.dxfString(0, "BLOCK_RECORD");
	 DW.dxfHex(5, 0x23);
	 //int ps0h = DW.handle();
	 //DW.setPaperSpace0Handle(ps0h);
	 //DW.dxfHex(330, 1);
	 DW.dxfString(100, "AcDbSymbolTableRecord");
	 DW.dxfString(100, "AcDbBlockTableRecord");
	 DW.dxfString(2, "*Paper_Space0");
	 DW.dxfHex(340, 0x26);
	 if (bWriterEnd)
	 {
		 DW.tableEnd();
	 }
	
 }

 void DxfLib::FDxfTables::WriteAppIDs(const DL_Writer& DW) const
 {

	 DW.tableAppid(AppIds.size());
	 for (size_t i = 0; i < AppIds.size(); i++)
	 {
		 AppIds[i]->WriteToDxfFile(DW);
	 }
	 DW.tableEnd();
 }

 void DxfLib::FDxfTables::WriteDimStyle(const DL_Writer& DW) const
 {
	 //DimStyle
	 DW.dxfString(0, "TABLE");
	 DW.dxfString(2, "DIMSTYLE");
	 DW.dxfHex(5, 0xA);
	 DW.dxfString(100, "AcDbSymbolTable");
	 DW.dxfInt(70, 1);
	 DW.dxfString(100, "AcDbDimStyleTable");
	 DW.dxfInt(71, 0);
	 for (size_t i = 0; i < DimStyles.size(); i++)
	 {
		 DimStyles[i]->WriteToDxfFile(DW);
	 }
	 DW.tableEnd();
 }

 void DxfLib::FBlockSource::AddBlock(FDxfBlockData* pBlockData)
 {
	 if (pBlockData)
	 {
		 //std::string Utf8Name = Format::FormatConversion::gbk_to_utf8(pBlockData->Name);
		 FString BlockName = DxfLib::DxfStrToUEStr(pBlockData->Name);
		 auto Block = Blocks.Find(BlockName);
		 if (Block == nullptr)
		 {
			 Blocks.Add(BlockName, pBlockData);
		 }
		 else
		 {
			 delete Blocks[BlockName];
			 Blocks[BlockName] = pBlockData;
		 }
	 }
 }

 void DxfLib::FBlockSource::WriteToDxfFile(const DL_Writer& DW) const
 {
	 for (auto& Ite : Blocks)
	 {
		 Ite.Value->WriteToDxfFile(DW);
	 }

	 /*
	 for (auto Item  = Blocks.begin(); Item != Blocks.end(); Item++)
	 {
		 Item->second->WriteToDxfFile(DW);
	 }
	 */
 }

 const FDxfBlockData* DxfLib::FBlockSource::GetBlock(const FString& BlockName) const
 {
	 if (BlockName.IsEmpty())
	 {
		 return nullptr;
	 }
	 auto Block = Blocks.Find(BlockName);
	 if (Block == nullptr)
	 {
		 return  nullptr;
	 }
	 return *Block;
 }


 void DxfLib::FDxfBlocks::AddBlock(FDxfBlockData* pBlockData)
 {	
	 if (pBlockData)
	 {
		 //std::string Utf8Name = Format::FormatConversion::gbk_to_utf8(pBlockData->SourceName);
		 FString SourceName = DxfLib::DxfStrToUEStr(pBlockData->SourceName);
		 auto Block = BlockMap.Find(SourceName);
		 if (Block == nullptr)
		 {
			 FBlockSource* BlockSource = new FBlockSource();
			 BlockSource->AddBlock(pBlockData);
			 BlockMap.Add(SourceName, BlockSource);
		 }
		 else
		 {
			 BlockMap[SourceName]->AddBlock(pBlockData);
		 }
	 }
 }

 void DxfLib::FDxfBlocks::WriteToDxfFile(const DL_Writer& DW) const
 {
	 DW.sectionBlocks();
	 /*
	 for (auto Item  = BlockMap.begin(); Item != BlockMap.end(); Item++)
	 {
		 Item->second->WriteToDxfFile(DW);
	 }
	 */

	 for (auto& Ite : BlockMap)
	 {
		 if (Ite.Value != nullptr)
		 {
			 Ite.Value->WriteToDxfFile(DW);
		 }
	 }
	 DW.sectionEnd();
 }
 const DxfLib::FDxfBlockData* DxfLib::FDxfBlocks::GetBlock(const FString& BlockName,const FString& SourceName) const
 {
	 if (BlockName.IsEmpty())
	 {
		 return nullptr;
	 }
	 auto Block = BlockMap.Find(SourceName);
	 if (Block == nullptr)
	 {
		 return  nullptr;
	 }
	 return (*Block)->GetBlock(BlockName);;
 }
 const DxfLib::FBlockSource* DxfLib::FDxfBlocks::GetBlockSource(const FString& SourceName) const
 {
	 auto Block = BlockMap.Find(SourceName);
	 if (Block == nullptr)
	 {
		 return  nullptr;
	 }
	 return (*Block);
 }
 DxfLib::FDxfTempData* DxfLib::FDxfTempData::GetInstance()
 {
	 return pDxfTempData;
 }

 void DxfLib::FDxfTempData::CreateInstance()
 {
	 if (pDxfTempData != nullptr)
		 DeleteInstance();
	 pDxfTempData = new FDxfTempData();
 }

 void DxfLib::FDxfTempData::DeleteInstance()
 {
	 if (pDxfTempData)
	 {
		 pDxfTempData = nullptr;
	 }
 }

 DxfLib::FDxfTempData::~FDxfTempData()
 {
	 DeleteInstance();
 }

 const DxfLib::FDxfTempData& DxfLib::FDxfTempData::operator=(const FDxfTempData& DxfTempData)
 {

	 return  DxfTempData;
	 // TODO: 在此处插入 return 语句
 }




 TSharedPtr<DxfLib::FDxfHeader> DxfLib::FDxfTempData::GetDxfHeader()
 {
	 if (!DxfHeader)
	 {
		 DxfHeader = MakeShared<FDxfHeader>();
	 }
	 return DxfHeader;
 }

 TSharedPtr < DxfLib::FDxfTables> DxfLib::FDxfTempData::GetDxfTables()
 {
	 if (!DxfTables)
	 {
		 DxfTables = MakeShared<FDxfTables>();
	 }
	 return DxfTables;
 }

 TSharedPtr <DxfLib::FDxfBlocks> DxfLib::FDxfTempData::GetDxfBlocks()
 {
	 if (!DxfBlocks)
	 {
		 DxfBlocks = MakeShared<FDxfBlocks>();
	 }
	 return DxfBlocks;
 }

 void DxfLib::FDxfTempData::Clear()
 {
	 DxfHeader.Reset();
	 DxfTables.Reset();
	 DxfBlocks.Reset();
	 EntityDatas.Empty();
 }

 //const FDxfBlockData* DxfLib::FDxfTempData::GetBlockByName(const std::string& BlockName,const std::string& SourceName) const
 //{
	// const FDxfBlockData* BlockData = nullptr;
	// BlockData = DxfBlocks.get()->GetBlock(BlockName, SourceName);
	// return BlockData;
 //}

 const DxfLib::FDxfBlockData* DxfLib::FDxfTempData::GetBlockByName(const FName& InBlockName, const FString& SourceName) const
 {
	 return DxfBlocks->GetBlock(InBlockName.ToString(), SourceName);
 }


 void DxfLib::FDxfTempData::AddBlock(FDxfBlockData* InBlock)
 {
	 DxfBlocks->AddBlock(InBlock);
 }

 void DxfLib::FDxfTempData::AddEntity(TSharedPtr<FDxfBaseEntityData> InEntity)
 {
	 EntityDatas.Add(InEntity);
 }