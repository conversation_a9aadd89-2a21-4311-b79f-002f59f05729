#include "Clipper2Library.h"
#include "Clipper2/Core/clipper2/clipper.core.h"
#include "Clipper2/Core/clipper2/clipper.h"

using namespace New_Clipper2Lib;

void FClipper2Library::CreatePolygonByPath(const FVector& InStart, const FVector& InEnd, TArray<FVector>& OutPolygon, const double& InOffset)
{
	PathsD Polygon;
	auto Z = InStart.Z;
	Polygon.push_back(MakePathD({InStart.X, InStart.Y, InEnd.X, InEnd.Y}));
	PathsD Solution = InflatePaths(Polygon, InOffset, JoinType::Miter, EndType::Butt);
	for (auto& P : Solution)
	{
		for (auto& I : P)
		{
			OutPolygon.Add(FVector(I.x, I.y, Z));
		}
	}
}

void FClipper2Library::CreatePolygonByPaths(const TArray<TPair<FVector, FVector>>& InPaths, TArray<FVector>& OutPolygon, const double& InOffset)
{
	if (InPaths.Num() <= 0)
	{
		return;
	}
	PathsD Polygon;
	auto Z = InPaths[0].Key.Z;
	for (auto& InSeg : InPaths)
	{
		Polygon.push_back(MakePathD({InSeg.Key.X, InSeg.Key.Y, InSeg.Value.X, InSeg.Value.Y}));
	}
	PathsD Solution = InflatePaths(Polygon, InOffset, JoinType::Miter, EndType::Butt);

	for (auto& P : Solution)
	{
		for (auto& I : P)
		{
			OutPolygon.Add(FVector(I.x, I.y, Z));
		}
	}
}

bool FClipper2Library::PathAndPolygonIntersection(const FVector& Start, const FVector& End, const double& InOffset, const TArray<FVector>& InAreaOutline)
{
	PathsD Seg;
	Seg.push_back(MakePathD({Start.X, Start.Y, End.X, End.Y}));
	PathsD Path = InflatePaths(Seg, InOffset, JoinType::Miter, EndType::Butt);
	PathsD Area;

	PathD OL;
	for (int32 i = 0; i < InAreaOutline.Num(); i++)
	{
		PointD P;
		P.x = InAreaOutline[i].X;
		P.y = InAreaOutline[i].Y;
		OL.push_back(P);
	}
	Area.push_back(OL);

	PathsD Solution = Intersect(Path, Area, FillRule::EvenOdd);

	return Solution.size() > 0;
}

bool FClipper2Library::PolygonIntersection(const TArray<FVector>& InOutlineA, const TArray<FVector>& InOutlineB)
{
	PathsD PolyA;
	PathD OLA;
	for (int32 i = 0; i < InOutlineA.Num(); i++)
	{
		for (int32 j = 0; j < InOutlineB.Num(); j++)
		{
			if (InOutlineA[i].Equals(InOutlineB[j], 0.1f))
			{
				return true;
			}
		}
		PointD P;
		P.x = InOutlineA[i].X;
		P.y = InOutlineA[i].Y;
		OLA.push_back(P);
	}
	PolyA.push_back(OLA);

	PathsD PolyB;
	PathD OLB;
	for (int32 i = 0; i < InOutlineB.Num(); i++)
	{
		PointD P;
		P.x = InOutlineB[i].X;
		P.y = InOutlineB[i].Y;
		OLB.push_back(P);
	}
	PolyB.push_back(OLB);

	PathsD Solution = Intersect(PolyA, PolyB, FillRule::EvenOdd);

	return Solution.size() > 0;
}

bool FClipper2Library::PolygonDifference02(const TArray<TArray<FVector>>& InOutlineA, const TArray<TArray<FVector>>& InOutlineB,
	TArray<TArray<FVector>>& OutPaths, float* OutArea , int32 InPrecision )
{
	if (InOutlineA.Num() <= 0)
	{
		return false;
	}

	if (InOutlineA[0].Num() <= 0)
	{
		return false;
	}

	//auto Z = InZ;

	PathsD PolyA;
	for (int32 j = 0; j < InOutlineA.Num(); ++j)
	{
		PathD OLA;
		for (int32 i = 0; i < InOutlineA[j].Num(); i++)
		{
			PointD P;
			P.x = InOutlineA[j][i].X;
			P.y = InOutlineA[j][i].Y;
			OLA.push_back(P);
		}
		PolyA.push_back(OLA);
	}

	PathsD PolyB;
	for (int32 j = 0; j < InOutlineB.Num(); ++j)
	{
		PathD OLB;
		for (int32 i = 0; i < InOutlineB[j].Num(); i++)
		{
			PointD P;
			P.x = InOutlineB[j][i].X;
			P.y = InOutlineB[j][i].Y;
			OLB.push_back(P);
		}
		PolyB.push_back(OLB);
	}

	//PathsD SolA = InflatePaths(PolyA, 0.1, JoinType::Miter, EndType::Butt);
	//PathsD SolB = InflatePaths(PolyB, 0.1, JoinType::Miter, EndType::Butt);

	PathsD Solution = Difference(PolyA, PolyB, FillRule::NonZero, InPrecision);
	PathsD Res = SimplifyPaths(Solution, 0.1);

	if (Res.empty())
		return false;

	if (Res[0].empty())
		return false;

	if (OutArea != nullptr)
	{
		*OutArea = Area(Res);
	}

	for (auto& P : Res)
	{
		TArray<FVector> OutPolygon;
		for (auto& I : P)
		{
			OutPolygon.Add(FVector(I.x, I.y, /*Z*/ 0));
		}
		OutPaths.Add(OutPolygon);
	}

	return true;
}

TArray<FVector> FClipper2Library::PolygonDifference(const TArray<FVector>& InOutlineA, const TArray<FVector>& InOutlineB)
{
	if (InOutlineB.Num() <= 0)
	{
		return InOutlineA;
	}
	auto Z = InOutlineA[0].Z;

	PathsD PolyA;
	PathD OLA;

	for (int32 i = 0; i < InOutlineA.Num(); i++)
	{
		PointD P;
		P.x = InOutlineA[i].X * 1e10;
		P.y = InOutlineA[i].Y * 1e10;
		OLA.push_back(P);
	}
	PolyA.push_back(OLA);

	PathsD PolyB;
	PathD OLB;
	for (int32 i = 0; i < InOutlineB.Num(); i++)
	{
		auto X = InOutlineB[i].X * 1e10;
		auto Y = InOutlineB[i].Y * 1e10;
		for (int32 j = 0; j < OLA.size(); j++)
		{
			int32 TempX = OLA.at(j).x;
			int32 TempY = OLA.at(j).y;

			if (FMath::IsNearlyEqual(X, TempX, 1))
			{
				X = TempX;
			}
			if (FMath::IsNearlyEqual(Y, TempY, 1))
			{
				Y = TempY;
			}
		}
		PointD P;
		P.x = X;
		P.y = Y;
		OLB.push_back(P);
	}
	PolyB.push_back(OLB);

	PathsD Solution = Difference(PolyA, PolyB, FillRule::NonZero);
	PathsD Res = SimplifyPaths(Solution, 0.1);
	TArray<FVector> OutPolygon;
	for (auto& P : Res)
	{
		for (auto& I : P)
		{
			OutPolygon.Add(FVector(I.x * 1e-10, I.y * 1e-10, Z));
		}
	}
	return OutPolygon;
}

TArray<FVector> FClipper2Library::PolygonUnion(const TArray<FVector>& InOutlineA, const TArray<FVector>& InOutlineB)
{
	if (InOutlineA.Num() <= 0)
	{
		return TArray<FVector>{};
	}
	auto Z = InOutlineA[0].Z;

	PathsD PolyA;
	PathD OLA;

	for (int32 i = 0; i < InOutlineA.Num(); i++)
	{
		PointD P;
		P.x = InOutlineA[i].X * 1e10;
		P.y = InOutlineA[i].Y * 1e10;
		OLA.push_back(P);
	}
	PolyA.push_back(OLA);

	PathsD PolyB;
	PathD OLB;
	for (int32 i = 0; i < InOutlineB.Num(); i++)
	{
		auto X = InOutlineB[i].X * 1e10;
		auto Y = InOutlineB[i].Y * 1e10;
		for (int32 j = 0; j < OLA.size(); j++)
		{
			int32 TempX = OLA.at(j).x;
			int32 TempY = OLA.at(j).y;

			if (FMath::IsNearlyEqual(X, TempX, 1))
			{
				X = TempX;
			}
			if (FMath::IsNearlyEqual(Y, TempY, 1))
			{
				Y = TempY;
			}
		}
		PointD P;
		P.x = X;
		P.y = Y;
		OLB.push_back(P);
	}
	PolyB.push_back(OLB);

	//PathsD SolA = InflatePaths(PolyA, 0.1, JoinType::Miter, EndType::Butt);
	//PathsD SolB = InflatePaths(PolyB, 0.1, JoinType::Miter, EndType::Butt);

	PathsD Solution = Union(PolyA, PolyB, FillRule::NonZero);
	PathsD Res = SimplifyPaths(Solution, 0.1);
	TArray<FVector> OutPolygon;
	for (auto& P : Res)
	{
		for (auto& I : P)
		{
			OutPolygon.Add(FVector(I.x * 1e-10, I.y * 1e-10, Z));
		}
	}
	return OutPolygon;
}

TArray<TArray<FVector>> FClipper2Library::PolygonUnion02(const TArray<TArray<FVector>>& InOutlineA,
	const TArray<TArray<FVector>>& InOutlineB, float InZ, float* OutArea, int32 InPrecision)
{
	if (InOutlineA.Num() <= 0)
	{
		return TArray<TArray<FVector>>{};
	}

	if (InOutlineA[0].Num() <= 0)
	{
		return TArray<TArray<FVector>>{};
	}

	auto Z = InZ;

	PathsD PolyA;
	for (int32 j = 0; j < InOutlineA.Num(); ++j)
	{
		PathD OLA;
		for (int32 i = 0; i < InOutlineA[j].Num(); i++)
		{
			PointD P;
			P.x = InOutlineA[j][i].X;
			P.y = InOutlineA[j][i].Y;
			OLA.push_back(P);
		}
		PolyA.push_back(OLA);
	}

	PathsD PolyB;
	for (int32 j = 0; j < InOutlineB.Num(); ++j)
	{
		PathD OLB;
		for (int32 i = 0; i < InOutlineB[j].Num(); i++)
		{
			PointD P;
			P.x = InOutlineB[j][i].X ;
			P.y = InOutlineB[j][i].Y ;
			OLB.push_back(P);
		}
		PolyB.push_back(OLB);
	}

	//PathsD SolA = InflatePaths(PolyA, 0.1, JoinType::Miter, EndType::Butt);
	//PathsD SolB = InflatePaths(PolyB, 0.1, JoinType::Miter, EndType::Butt);

	PathsD Solution = Union(PolyA, PolyB, FillRule::NonZero, InPrecision);
	PathsD Res = SimplifyPaths(Solution, 0.1);

	if (OutArea != nullptr)
	{
		*OutArea = Area(Res);
	}
	TArray<TArray<FVector>> OutPolygons;
	for (auto& P : Res)
	{
		TArray<FVector> OutPolygon;
		for (auto& I : P)
		{
			OutPolygon.Add(FVector(I.x, I.y, Z));
		}
		OutPolygons.Add(OutPolygon);
	}

	return OutPolygons;
}

TArray<TArray<FVector2D>> FClipper2Library::PolygonUnion03(const TArray<TArray<FVector2D>>& InOutlineA,
	const TArray<TArray<FVector2D>>& InOutlineB, float* OutArea, int32 InPrecision )

{
	if (InOutlineA.Num() <= 0)
	{
		return TArray<TArray<FVector2D>>{};
	}

	if (InOutlineA[0].Num() <= 0)
	{
		return TArray<TArray<FVector2D>>{};
	}

	PathsD PolyA;
	for (int32 j = 0; j < InOutlineA.Num(); ++j)
	{
		PathD OLA;
		for (int32 i = 0; i < InOutlineA[j].Num(); i++)
		{
			PointD P;
			P.x = InOutlineA[j][i].X;
			P.y = InOutlineA[j][i].Y;
			OLA.push_back(P);
		}
		PolyA.push_back(OLA);
	}

	PathsD PolyB;
	for (int32 j = 0; j < InOutlineB.Num(); ++j)
	{
		PathD OLB;
		for (int32 i = 0; i < InOutlineB[j].Num(); i++)
		{
			PointD P;
			P.x = InOutlineB[j][i].X;
			P.y = InOutlineB[j][i].Y;
			OLB.push_back(P);
		}
		PolyB.push_back(OLB);
	}

	//PathsD SolA = InflatePaths(PolyA, 0.1, JoinType::Miter, EndType::Butt);
	//PathsD SolB = InflatePaths(PolyB, 0.1, JoinType::Miter, EndType::Butt);

	PathsD Solution = Union(PolyA, PolyB, FillRule::NonZero, InPrecision);
	PathsD Res = SimplifyPaths(Solution, 0.1);

	if (OutArea != nullptr)
	{
		*OutArea = Area(Res);
	}
	TArray<TArray<FVector2D>> OutPolygons;
	for (auto& P : Res)
	{
		TArray<FVector2D> OutPolygon;
		for (auto& I : P)
		{
			OutPolygon.Add(FVector2D(I.x, I.y));
		}
		OutPolygons.Add(OutPolygon);
	}

	return OutPolygons;
}

static void ParsePolyTreeDToUEPolygon(const PolyTreeD& InPoly, TArray<FDSPolygon2DHasHole>& OutUEPolygons)
{
	if (InPoly.Polygon().size() > 0)
	{
		if (!InPoly.IsHole())
		{
			PathD SimplePath = New_Clipper2Lib::SimplifyPath(InPoly.Polygon(), 0.1);
			FDSPolygon2DHasHole NewPoly;

			for (auto Ite : SimplePath)
			{
				NewPoly.Outer.Add(FVector2D(Ite.x, Ite.y));
			}


			if (InPoly.Count() > 0)
			{
				for (int32 i = 0; i < InPoly.Count(); ++i)
				{
					PolyPathD* ChildPath = InPoly.Child(i);
					if (ChildPath->IsHole())
					{
						SimplePath = New_Clipper2Lib::SimplifyPath(ChildPath->Polygon(), 0.1);
						int32 HoleIndex = NewPoly.Holes.AddDefaulted();
						for (auto PointIte : SimplePath)
						{
							NewPoly.Holes[HoleIndex].Add(FVector2D(PointIte.x, PointIte.y));
						}
					}
				}
			}

			OutUEPolygons.Add(MoveTemp(NewPoly));
		}
		else
		{
			for (int32 i = 0; i < InPoly.Count(); ++i)
			{
				PolyPathD* ChildPath = InPoly.Child(i);
				ParsePolyTreeDToUEPolygon(*ChildPath, OutUEPolygons);
			}
		}
	}
	else
	{
		for (int32 i = 0; i < InPoly.Count(); ++i)
		{
			PolyPathD* ChildPath = InPoly.Child(i);
			ParsePolyTreeDToUEPolygon(*ChildPath, OutUEPolygons);
		}
	}
}

void FClipper2Library::PolygonUnion04(const TArray<TArray<FVector2D>>& InOutlineA,
	const TArray<TArray<FVector2D>>& InOutlineB, TArray<FDSPolygon2DHasHole>& OutPaths, float* OutArea , int32 InPrecision)
{
	if (InOutlineA.Num() <= 0)
	{
		return;
	}

	if (InOutlineA[0].Num() <= 0)
	{
		return;
	}

	//auto Z = InZ;

	PathsD PolyA;
	for (int32 j = 0; j < InOutlineA.Num(); ++j)
	{
		PathD OLA;
		for (int32 i = 0; i < InOutlineA[j].Num(); i++)
		{
			PointD P;
			P.x = InOutlineA[j][i].X;
			P.y = InOutlineA[j][i].Y;
			OLA.push_back(P);
		}
		PolyA.push_back(OLA);
	}

	PathsD PolyB;
	for (int32 j = 0; j < InOutlineB.Num(); ++j)
	{
		PathD OLB;
		for (int32 i = 0; i < InOutlineB[j].Num(); i++)
		{
			PointD P;
			P.x = InOutlineB[j][i].X;
			P.y = InOutlineB[j][i].Y;
			OLB.push_back(P);
		}
		PolyB.push_back(OLB);
	}

	//PathsD SolA = InflatePaths(PolyA, 0.1, JoinType::Miter, EndType::Butt);
	//PathsD SolB = InflatePaths(PolyB, 0.1, JoinType::Miter, EndType::Butt);

	//PathsD Solution = Union(PolyA, PolyB, FillRule::NonZero, InPrecision);
	PolyTreeD Solution;
	New_Clipper2Lib::BooleanOp(ClipType::Union, FillRule::NonZero, PolyA, PolyB, Solution, InPrecision);
	ParsePolyTreeDToUEPolygon(Solution, OutPaths);
	//PathsD Res = SimplifyPaths(Solution, 0.1);

	//if (OutArea != nullptr)
	//{
	//	*OutArea = OutPaths.Area();
	//}
}


void FClipper2Library::CreateEllipse(const FVector& InP, const double& InX, const double& InY, const int32& InStep, TArray<FVector>& OutPolygon)
{
	PointD P;
	P.x = InP.X;
	P.y = InP.Y;

	auto Solution = Ellipse<double>(P, InX, InY, InStep);

	auto Z = InP.Z;

	for (auto& S : Solution)
	{
		OutPolygon.Add(FVector(S.x, S.y, Z));
	}
}

std::function<bool(const PolyPathD&, TArray<TArray<FVector>>&)> DSGetPoly = [](const PolyPathD& InTree, TArray<TArray<FVector>>& OutPolygon) -> bool
{
	for (int32 i = 0; i < InTree.Count(); ++i)
	{
		auto Childi = InTree.Child(i);
		for (int32 j = 0; j < Childi->Count(); ++j)
		{
			auto Poly = Childi->Child(j);

			if (Poly)
			{
				TArray<FVector> OutPoly;
				auto Path = Poly->Polygon();
				for (auto& P : Path)
				{
					FVector Point;
					Point.X = P.x;
					Point.Y = P.y;
					Point.Z = 0.f;
					OutPoly.Insert(Point, 0);
				}

				if (OutPoly.Num() >= 3)
				{
					OutPolygon.Add(OutPoly);
					DSGetPoly(*Poly, OutPolygon);
				}
			}
		}
	}
	return true;
};

void FClipper2Library::CreatePathsArea(const TArray<TArray<FVector>>& InPaths, TArray<TArray<FVector>>& OutPolygon)
{
	OutPolygon.Empty();
	PathsD Area;
	PolyTreeD Polytree;
	ClipperD CD;

	for (auto& Iter : InPaths)
	{
		PathsD Subject;
		PathD Path;
		for (auto& V : Iter)
		{
			PointD Pt;
			Pt.x = V.X;
			Pt.y = V.Y;
			Path.push_back(Pt);
		}
		Subject.push_back(Path);
		CD.AddSubject(Subject);
	}

	CD.Execute(ClipType::Union, FillRule::Negative, Polytree);

	std::function<bool(const PolyPathD&)> GetPoly = [&](const PolyPathD& InTree) -> bool
	{
		for (int32 i = 0; i < InTree.Count(); ++i)
		{
			auto Childi = InTree.Child(i);
			for (int32 j = 0; j < Childi->Count(); ++j)
			{
				auto Poly = Childi->Child(j);

				if (Poly)
				{
					TArray<FVector> OutPoly;
					auto Path = Poly->Polygon();
					for (auto& P : Path)
					{
						FVector Point;
						Point.X = P.x;
						Point.Y = P.y;
						Point.Z = 0.f;
						OutPoly.Insert(Point, 0);
					}

					if (OutPoly.Num() >= 3)
					{
						OutPolygon.Add(OutPoly);
						GetPoly(*Poly);
					}
				}
			}
		}
		return true;
	};
	GetPoly(Polytree);
}

void FClipper2Library::CreatePathsArea(const TArray<TPair<FVector, FVector>>& InSegments, TArray<TArray<FVector>>& OutPolygon)
{
	ClipperD CD;
	PolyTreeD Polytree;

	for (auto& Seg : InSegments)
	{
		PointD Start;
		PointD End;

		Start.x = Seg.Key.X;
		Start.y = Seg.Key.Y;

		End.x = Seg.Value.X;
		End.y = Seg.Value.Y;

		PathsD Polygon;
		Polygon.push_back(MakePathD({Start.x, Start.y, End.x, End.y}));
		PathsD Solution = InflatePaths(Polygon, 0.01, JoinType::Miter, EndType::Butt);
		CD.AddSubject(Solution);
	}

	std::function<void(PolyPathD* InPolyPathD)> GenerateChildPolys = [&](PolyPathD* InPolyPathD) -> void
	{
		int32 Count = InPolyPathD->Count();
		if (Count <= 0)
		{
			return;
		}
		for (int32 i = 0; i < Count; ++i)
		{
			auto ChildCount = InPolyPathD->Child(i)->Count();
			if (ChildCount <= 0)
			{
				return;
			}
			auto PT = InPolyPathD->Child(i);
			for (int32 j = 0; j < ChildCount; ++j)
			{
				auto CPT = PT->Child(j);
				auto Poly = CPT->Polygon();
				TArray<FVector> OutPoly;
				for (auto& P : Poly)
				{
					FVector Point;
					Point.X = P.x;
					Point.Y = P.y;
					Point.Z = 0.f;
					OutPoly.Insert(Point, 0);
				}

				if (OutPoly.Num() >= 3)
				{
					OutPolygon.Add(OutPoly);
				}

				GenerateChildPolys(CPT);
			}
		}
	};

	CD.Execute(ClipType::Union, FillRule::NonZero, Polytree);
	GenerateChildPolys(&Polytree);
}

void FClipper2Library::SplitSelfIntersection(const TArray<TPair<FVector, FVector>>& InSegments, TArray<TPair<FVector, FVector>>& OutSegments)
{
	TMap<TPair<FVector, FVector>, TPair<bool, FVector>> SplitPoints{};
	for (auto& A : InSegments)
	{
		SplitPoints.Add(A, TPair<bool, FVector>{false, FVector::ZeroVector});
		for (auto& B : InSegments)
		{
			if (A == B)
			{
				continue;
			}
			FVector Temp;
			if (FMath::SegmentIntersection2D(A.Key, A.Value, B.Key, B.Value, Temp) && !A.Key.Equals(Temp, 0.1) && !A.Value.Equals(Temp, 0.1))
			{
				SplitPoints[A] = TPair<bool, FVector>{true, Temp};
				break;
			}
		}
	}
	for (auto& Iter : SplitPoints)
	{
		if (Iter.Value.Key)
		{
			OutSegments.Add(TPair<FVector, FVector>{Iter.Key.Key, Iter.Value.Value});
			OutSegments.Add(TPair<FVector, FVector>{Iter.Value.Value, Iter.Key.Value});
		}
		else
		{
			OutSegments.Add(Iter.Key);
		}
	}
}

void FClipper2Library::CreatePathsAreaSelfIntersect(const TArray<TPair<FVector, FVector>>& InSegments, TArray<TArray<FVector>>& OutPolygon, TArray<TPair<FVector, FVector>>& OutEmptySegment)
{
	TArray<TPair<FVector, FVector>> NewSegments{};
	SplitSelfIntersection(InSegments, NewSegments);

	TArray<TSet<TPair<FVector, FVector>>> SegSet;

	for (const auto& S : NewSegments)
	{
		bool bIntersection = false;
		for (auto& A : SegSet)
		{
			for (auto& V : A)
			{
				FVector Temp;
				if (FMath::SegmentIntersection2D(S.Key, S.Value, V.Key, V.Value, Temp))
				{
					A.Add(S);
					bIntersection = true;
					break;
				}
			}
			if (bIntersection)
			{
				break;
			}
		}
		if (!bIntersection)
		{
			SegSet.Add({S});
		}
	}

	for (auto& S : SegSet)
	{
		for (auto& A : S)
		{
			TSet<FVector> Intersctions{};
			auto DirA = (A.Value - A.Key).GetSafeNormal();
			for (auto& B : S)
			{
				if (A == B)
				{
					continue;
				}
				auto DirB = (B.Value - B.Key).GetSafeNormal();

				FVector Temp;
				if (FMath::SegmentIntersection2D(A.Key - DirA * 1, A.Value + DirA * 1, B.Key - DirB * 1, B.Value + DirB * 1, Temp))
				{
					bool bContains = false;
					for (auto& I : Intersctions)
					{
						bContains = I.Equals(Temp, 1.0);
						if (bContains)
						{
							break;
						}
					}
					if (!bContains)
					{
						Intersctions.Add(Temp);
					}
				}
			}
			if (Intersctions.Num() < 2)
			{
				OutEmptySegment.Add(A);
			}
		}
	}

	for (auto& S : SegSet)
	{
		TArray<TArray<FVector>> NewSet{};
		ClipperD CD;
		PolyTreeD Polytree;
		for (auto& Seg : S)
		{
			PointD Start;
			PointD End;

			Start.x = Seg.Key.X;
			Start.y = Seg.Key.Y;

			End.x = Seg.Value.X;
			End.y = Seg.Value.Y;

			PathsD Polygon;
			Polygon.push_back(MakePathD({Start.x, Start.y, End.x, End.y}));
			PathsD Solution = InflatePaths(Polygon, 0.01, JoinType::Miter, EndType::Square);
			CD.AddSubject(Solution);
		}

		CD.Execute(ClipType::Union, FillRule::Positive, Polytree);

		DSGetPoly(Polytree, NewSet);
		if (NewSet.IsEmpty())
		{
			//OutEmptySegment.Append(S.Array());
		}
		else
		{
			OutPolygon.Append(NewSet);
		}
	}
}

double FClipper2Library::GetAreaSize(const TArray<FVector>& InArea)
{
	PathD Path;

	for (auto& V : InArea)
	{
		PointD Pt;
		Pt.x = V.X;
		Pt.y = V.Y;
		Path.push_back(Pt);
	}

	return Area(Path) * 0.001f;
}

double FClipper2Library::GetAreaSizeCM(const TArray<TArray<FVector>>& InArea)
{
	PathsD Paths;
	for (auto& PathIte : InArea)
	{
		PathD Path;

		for (auto& V : PathIte)
		{
			PointD Pt;
			Pt.x = V.X;
			Pt.y = V.Y;
			Path.push_back(Pt);
		}
		Paths.push_back(std::move(Path));
	}

	return Area(Paths);
}

FVector FClipper2Library::GetAreaCenter(const TArray<FVector>& InArea, const TArray<TArray<FVector>>& InHoles)
{
	if (InArea.Num() < 3)
	{
		return FVector::ZeroVector;
	}

	auto PointInHole = [&](const FVector& InPoint)
	{
		PointD Pt;
		Pt.x = InPoint.X;
		Pt.y = InPoint.Y;

		for (auto& Hole : InHoles)
		{
			PathD HolePath;
			for (auto& V : Hole)
			{
				PointD P;
				P.x = V.X;
				P.y = V.Y;
				HolePath.push_back(P);
			}
			auto RestInHole = PointInPolygon<double>(Pt, HolePath);
			if (RestInHole == PointInPolygonResult::IsInside
				|| RestInHole == PointInPolygonResult::IsOn)
			{
				return true;
			}
		}
		return false;
	};

	PathD Path;
	int32 c = 0;
	TArray<TPair<FVector, FVector>> Segs;
	for (auto& V : InArea)
	{
		PointD Pt;
		Pt.x = V.X;
		Pt.y = V.Y;
		Path.push_back(Pt);

		int32 n = (c + 1) % InArea.Num();
		Segs.Add(TPair<FVector, FVector>{InArea[c], InArea[n]});
		++c;
	}

	auto SortPoints = InArea;
	SortPoints.Sort([](const FVector& InA, const FVector& InB) { return InA.X > InB.X; });
	auto MaxX = SortPoints[0].X;
	auto MinX = SortPoints.Last().X;

	SortPoints.Sort([](const FVector& InA, const FVector& InB) { return InA.Y > InB.Y; });
	auto MaxY = SortPoints[0].Y;
	auto MinY = SortPoints.Last().Y;

	double Step = 10.f;

	TMap<FVector, double> PointDist;

	for (int32 i = MinX; i < MaxX; i += Step)
	{
		for (int32 j = MinY; j < MaxY; j += Step)
		{
			PointD Pt;
			Pt.x = i;
			Pt.y = j;

			auto Rest = PointInPolygon<double>(Pt, Path);

			bool bInHole = PointInHole(FVector(i, j, 0.f));

			if (Rest == PointInPolygonResult::IsInside && !bInHole)
			{
				double MinDist = MAX_dbl;
				FVector P = FVector(i, j, 0.f);
				for (auto& Seg : Segs)
				{
					auto Dist = FMath::PointDistToSegment(P, Seg.Key, Seg.Value);
					if (Dist < MinDist)
					{
						if (PointDist.Contains(P))
						{
							PointDist[P] = Dist;
						}
						else
						{
							PointDist.Add(P, Dist);
						}
						MinDist = Dist;
					}
				}
			}
		}
	}

	PointDist.ValueSort([](const double& InA, const double& InB) { return InA >= InB; });

	if (PointDist.Num() > 0)
	{
		auto OutPoint = PointDist.begin()->Key;

		InHoles.IsEmpty();
		TArray<TPair<FVector, FVector>> HoleSegs;
		for (auto& H : InHoles)
		{
			auto Count = H.Num();
			for (int32 i = 0; i < Count; i++)
			{
				int32 n = (i + 1) % Count;
				HoleSegs.Add(TPair<FVector, FVector>{H[i], H[n]});
			}
		}
		TPair<FVector, FVector> CoverageSeg;
		bool bCovered = false;
		for (auto Seg : HoleSegs)
		{
			auto Dist = FMath::PointDistToSegment(OutPoint, Seg.Key, Seg.Value);
			if (Dist < 10.f)
			{
				CoverageSeg = Seg;
				bCovered = true;
				break;
			}
		}
		if (bCovered)
		{
			auto Dir = (CoverageSeg.Value - CoverageSeg.Key).GetSafeNormal();
			auto Nor = FVector::CrossProduct(Dir, FVector(0.f, 0.f, 1.f)).GetSafeNormal();
			auto Middle = (CoverageSeg.Value + CoverageSeg.Key) * 0.5f;
			auto Offset = Nor * 2.f;
			auto LCenter = Middle + Offset;
			auto RCenter = Middle - Offset;

			bool bLInHole = PointInHole(LCenter);

			if (bLInHole)
			{
				Nor = -Nor;
			}

			auto NewStart = Middle;
			auto NewEnd = Middle;

			while (true)
			{
				NewEnd = NewEnd + Nor * 10.f;
				PointD Pt;
				Pt.x = NewEnd.X;
				Pt.y = NewEnd.Y;
				if (PointInPolygon<double>(Pt, Path) != PointInPolygonResult::IsInside || PointInHole(NewEnd))
				{
					break;
				}
				OutPoint = (NewStart + NewEnd) * 0.5f;
			}
		}
		return OutPoint;
	}

	return FVector();
}

TArray<TPair<FVector, FVector>> FClipper2Library::GenerateSegments(const TArray<TArray<FVector>>& InPoint)
{
	TArray<TPair<FVector, FVector>> Res;

	for (auto& Out : InPoint)
	{
		auto Count = Out.Num();
		for (int32 i = 0; i < Count; i++)
		{
			int32 n = (i + 1) % Count;
			bool bContain = false;
			for (auto& Exist : Res)
			{
				if (Out[i].Equals(Exist.Key, 2) && Out[n].Equals(Exist.Value, 2) || Out[n].Equals(Exist.Key, 2) && Out[i].Equals(Exist.Value, 2))
				{
					bContain = true;
					break;
				}
			}
			if (!bContain)
			{
				Res.Add(TPair<FVector, FVector>{Out[i], Out[n]});
			}
		}
	}

	return Res;
}

TArray<TArray<FVector>> FClipper2Library::ComputeOuterContour(const TArray<TArray<FVector2D>>& AllPaths, float Height)
{
	ClipperD Clipper = ClipperD();
	PathsD Solution = PathsD();
	PathsD OutSolution = PathsD();

	for (auto& Path : AllPaths)
	{
		PathD Polygon;
		for (auto& V : Path)
		{
			Polygon.push_back(PointD(V.X, V.Y));
		}
		Solution.push_back(Polygon);
	}

	Clipper.AddSubject(Solution);
	Clipper.Execute(ClipType::Union, FillRule::NonZero, OutSolution);
	PathsD SimplifyOutSolution = SimplifyPaths(OutSolution, 0.1, true);

	// 转换结果
	TArray<TArray<FVector>> OuterContours;
	for (const PathD Poly : SimplifyOutSolution)
	{
		TArray<FVector> Contour;
		for (const PointD& Pt : Poly)
		{
			Contour.Add(FVector(Pt.x, Pt.y, Height)); // 转换为 FVector
		}
		OuterContours.Add(Contour);
	}

	return OuterContours;
}

TArray<TArray<FVector>> FClipper2Library::ComputeOuterContour(const TArray<TArray<FVector>>& AllPaths, float Height)
{
	ClipperD Clipper = ClipperD();
	PathsD Solution = PathsD();
	PathsD OutSolution = PathsD();

	for (auto& Path : AllPaths)
	{
		PathD Polygon;
		for (auto& V : Path)
		{
			Polygon.push_back(PointD(V.X, V.Y));
		}
		Solution.push_back(Polygon);
	}

	Clipper.AddSubject(Solution);
	Clipper.Execute(ClipType::Union, FillRule::NonZero, OutSolution);
	PathsD SimplifyOutSolution = SimplifyPaths(OutSolution, 0.01, true);

	// 转换结果
	TArray<TArray<FVector>> OuterContours;
	for (const PathD Poly : SimplifyOutSolution)
	{
		TArray<FVector> Contour;
		for (const PointD& Pt : Poly)
		{
			Contour.Add(FVector(Pt.x, Pt.y, Height)); // 转换为 FVector
		}
		OuterContours.Add(Contour);
	}

	return OuterContours;
}

TArray<TArray<FVector>> FClipper2Library::ComputeDiffContour(const TArray<TArray<FVector>>& AllPaths, float Height)
{
	ClipperD Clipper = ClipperD();
	PathsD Solution = PathsD();
	PathsD OutSolution = PathsD();

	for (auto& Path : AllPaths)
	{
		PathD Polygon;
		for (auto& V : Path)
		{
			Polygon.push_back(PointD(V.X, V.Y));
		}
		Solution.push_back(Polygon);
	}

	Clipper.AddSubject(Solution);
	Clipper.Execute(ClipType::Difference, FillRule::NonZero, OutSolution);
	PathsD SimplifyOutSolution = SimplifyPaths(OutSolution, 0.1, true);

	// 转换结果
	TArray<TArray<FVector>> OuterContours;
	for (const PathD Poly : SimplifyOutSolution)
	{
		TArray<FVector> Contour;
		for (const PointD& Pt : Poly)
		{
			Contour.Add(FVector(Pt.x, Pt.y, Height)); // 转换为 FVector
		}
		OuterContours.Add(Contour);
	}

	return OuterContours;
}

TArray<TArray<FVector>> FClipper2Library::ComputeIntersectionContour(const TArray<FVector2D>& SubjectContour, const TArray<FVector2D>& ClipContour)
{
	ClipperD Clipper = ClipperD();
	PathsD SubjectSolution = PathsD();
	PathsD ClipSolution = PathsD();
	PathsD OutSolution = PathsD();

	PathD SubjectContourPolygon;
	for (const FVector2D& Rect : SubjectContour)
	{
		SubjectContourPolygon.push_back(PointD(Rect.X, Rect.Y));
	}
	SubjectSolution.push_back(SubjectContourPolygon);

	PathD ClipContourPolygon;
	for (const FVector2D& Rect : ClipContour)
	{
		ClipContourPolygon.push_back(PointD(Rect.X, Rect.Y));
	}
	ClipSolution.push_back(ClipContourPolygon);

	Clipper.AddSubject(SubjectSolution);
	Clipper.AddClip(ClipSolution);
	Clipper.Execute(ClipType::Intersection, FillRule::EvenOdd, OutSolution);
	PathsD SimplifyOutSolution = SimplifyPaths(OutSolution, 0.1, true);

	// 转换结果
	TArray<TArray<FVector>> OuterContours;
	for (const PathD Poly : SimplifyOutSolution)
	{
		TArray<FVector> Contour;
		for (const PointD& Pt : Poly)
		{
			Contour.Add(FVector(Pt.x, Pt.y, 0)); // 转换为 FVector
		}
		OuterContours.Add(Contour);
	}

	return OuterContours;
}

TArray<TArray<FVector>> FClipper2Library::ComputeIntersectionContour(const TArray<FVector>& SubjectContour, const TArray<FVector>& ClipContour)
{
	ClipperD Clipper = ClipperD();
	PathsD SubjectSolution = PathsD();
	PathsD ClipSolution = PathsD();
	PathsD OutSolution = PathsD();

	PathD SubjectContourPolygon;
	for (const FVector& Rect : SubjectContour)
	{
		SubjectContourPolygon.push_back(PointD(Rect.X, Rect.Y));
	}
	SubjectSolution.push_back(SubjectContourPolygon);

	PathD ClipContourPolygon;
	for (const FVector& Rect : ClipContour)
	{
		ClipContourPolygon.push_back(PointD(Rect.X, Rect.Y));
	}
	ClipSolution.push_back(ClipContourPolygon);

	Clipper.AddSubject(SubjectSolution);
	Clipper.AddClip(ClipSolution);
	Clipper.Execute(ClipType::Intersection, FillRule::EvenOdd, OutSolution);
	PathsD SimplifyOutSolution = SimplifyPaths(OutSolution, 0.1, true);

	// 转换结果
	TArray<TArray<FVector>> OuterContours;
	for (const PathD Poly : SimplifyOutSolution)
	{
		TArray<FVector> Contour;
		for (const PointD& Pt : Poly)
		{
			Contour.Add(FVector(Pt.x, Pt.y, 0)); // 转换为 FVector
		}
		OuterContours.Add(Contour);
	}

	return OuterContours;
}

TArray<TArray<FVector>> FClipper2Library::ComputeDiffContour(const TArray<TArray<FVector>>& SubjectContours, const TArray<TArray<FVector>>& ClipContours, float Height)
{
	ClipperD Clipper = ClipperD();
	PathsD SubjectSolution = PathsD();
	PathsD ClipSolution = PathsD();
	PathsD OutSolution = PathsD();

	for (auto& SubjectContour : SubjectContours)
	{
		PathD Polygon;
		for (auto& V : SubjectContour)
		{
			Polygon.push_back(PointD(V.X, V.Y));
		}
		SubjectSolution.push_back(Polygon);
	}

	for (auto& ClipContour : ClipContours)
	{
		PathD Polygon;
		for (auto& V : ClipContour)
		{
			Polygon.push_back(PointD(V.X, V.Y));
		}
		ClipSolution.push_back(Polygon);
	}

	Clipper.AddSubject(SubjectSolution);
	Clipper.AddClip(ClipSolution);
	Clipper.Execute(ClipType::Difference, FillRule::NonZero, OutSolution);
	PathsD SimplifyOutSolution = SimplifyPaths(OutSolution, 0.1, true);

	// 转换结果
	TArray<TArray<FVector>> OuterContours;
	for (const PathD Poly : SimplifyOutSolution)
	{
		TArray<FVector> Contour;
		for (const PointD& Pt : Poly)
		{
			Contour.Add(FVector(Pt.x, Pt.y, Height)); // 转换为 FVector
		}
		OuterContours.Add(Contour);
	}

	return OuterContours;
}

TArray<FVector> FClipper2Library::OffsetPolygon(const TArray<FVector>& PolygonVertices, double OffsetWidth)
{
	if (PolygonVertices.Num() < 3)
	{
		return TArray<FVector>();
	}
	// 将 UE5 的 FVector2D 转换为 Clipper2 的 Path64
	Path64 Path;
	for (const FVector& Vertex : PolygonVertices)
	{
		Path.push_back(Point64(static_cast<int64>(Vertex.X * 10000), static_cast<int64>(Vertex.Y * 10000)));
	}

	double Z = PolygonVertices[0].Z;

	// 创建 ClipperOffset 对象
	ClipperOffset Offset(0);
	Offset.AddPath(Path, JoinType::Miter, EndType::Polygon);

	// 执行内缩操作
	Paths64 Solution;
	Offset.Execute(OffsetWidth * 10000, Solution);

	// 将结果转换回 UE5 的 FVector2D
	TArray<FVector> ResultVertices;
	if (!Solution.empty())
	{
		for (const Point64& Point : Solution[0])
		{
			ResultVertices.Add(FVector(static_cast<float>(Point.x) / 10000, static_cast<float>(Point.y) / 10000, Z));
		}
	}

	return ResultVertices;
}
