﻿// Fill out your copyright notice in the Description page of Project Settings.


#include "Parser/ExpressionParser.h"

#include "Lexer/ExpressionLexer.h"

DECLARE_LOG_CATEGORY_CLASS(LogExpressionParser, Log, All);

FExpressionParser::FExpressionParser()
	: Offset(0)
{
}

bool FExpressionParser::Parse(const FString& InContent, TArray<FAstNodePtr>& OutAstTree, FString& OutError)
{
	Offset = 0;
	Tokens.Empty();
	
	try
	{
		FExpressionLexer Lexer;
		Lexer.SetContent(InContent);

		if (!Lexer.Tokenize(Tokens, OutError))
		{
			return false;
		}
		
		while (true)
		{
			SkipWhitespace();
			const FExpressionLexerToken& Token = PeekToken();
			if (Token.Type == EExpressionLexerTokenType::EndOfFile)
			{
				break;
			}

			if (Token.Type == EExpressionLexerTokenType::Comment)
			{
				ConsumeToken();
				continue;
			}

			OutAstTree.Push(ParseExpression());
		}

		SkipWhitespace();
		if (Offset < Tokens.Num() && Tokens[Offset].Type != EExpressionLexerTokenType::EndOfFile)
		{
			const FExpressionLexerToken& Remaining = Tokens[Offset];
			OutError = FString::Printf(TEXT("Unexpected token after expression: '%s' at [%d:%d]"), *Remaining.Value, Remaining.Position.X, Remaining.Position.Y);
			return false;
		}

		return true;	
	}
	catch (std::exception& Err)
	{
		OutAstTree.Empty();
		OutError = UTF8_TO_TCHAR(Err.what());
		return false;
	}
}

const TArray<FExpressionLexerToken>& FExpressionParser::GetTokens() const
{
	return Tokens;
}

void FExpressionParser::SkipWhitespace()
{
	while (Offset < Tokens.Num() && (Tokens[Offset].Type == EExpressionLexerTokenType::Whitespace || Tokens[Offset].Type == EExpressionLexerTokenType::LineFeed))
	{
		++Offset;
	}
}

const FExpressionLexerToken& FExpressionParser::PeekToken()
{
	SkipWhitespace();
	if (Offset < Tokens.Num())
	{
		return Tokens[Offset];
	}

	static FExpressionLexerToken EOFToken(EExpressionLexerTokenType::EndOfFile, TEXT(""), FIntPoint(0));
	return EOFToken;
}

const FExpressionLexerToken& FExpressionParser::ConsumeToken()
{
	SkipWhitespace();
	const FExpressionLexerToken& Token = PeekToken();
	++Offset;
	return Token;
}

bool FExpressionParser::MatchType(EExpressionLexerTokenType InType)
{
	SkipWhitespace();
	if (PeekToken().Type == InType)
	{
		ConsumeToken();
		return true;
	}

	return false;
}

bool FExpressionParser::MatchValue(const FString& InValue)
{
	SkipWhitespace();
	if (PeekToken().Value == InValue)
	{
		ConsumeToken();
		return true;
	}

	return false;
}

FAstNodePtr FExpressionParser::ParseExpression()
{
	return ParseLogicalOr();
}

FAstNodePtr FExpressionParser::ParseLogicalOr()
{
	FAstNodePtr Node = ParseLogicalAnd();
	while (true)
	{
		const FExpressionLexerToken& Token = PeekToken();
		if (MatchValue(TEXT("||")))
		{
			FAstNodePtr Right = ParseLogicalAnd();
			Node = MakeShared<FAstNodeBinaryOp>(Node, Token.Value, Right);
		}
		else
		{
			break;
		}
	}
	return Node;
}

FAstNodePtr FExpressionParser::ParseLogicalAnd()
{
	FAstNodePtr Node = ParseEquality();
	while (true)
	{
		const FExpressionLexerToken& Token = PeekToken();
		if (MatchValue(TEXT("&&")))
		{
			FAstNodePtr Right = ParseEquality();
			Node = MakeShared<FAstNodeBinaryOp>(Node, Token.Value, Right);
		}
		else
		{
			break;
		}
	}
	return Node;
}

FAstNodePtr FExpressionParser::ParseEquality()
{
	FAstNodePtr Node = ParseComparision();
	while (true)
	{
		const FExpressionLexerToken& Token = PeekToken();
		if (MatchValue(TEXT("==")) || MatchValue(TEXT("!=")))
		{
			FAstNodePtr Right = ParseComparision();
			Node = MakeShared<FAstNodeBinaryOp>(Node, Token.Value, Right);
		}
		else
		{
			break;
		}
	}

	return Node;
}

FAstNodePtr FExpressionParser::ParseComparision()
{
	FAstNodePtr Node = ParseTerm();
	while (true)
	{
		const FExpressionLexerToken& Token = PeekToken();
		if (MatchValue(TEXT("<")) || MatchValue(TEXT(">")) || MatchValue(TEXT("<=")) || MatchValue(TEXT(">=")))
		{
			FAstNodePtr Right = ParseTerm();
			Node = MakeShared<FAstNodeBinaryOp>(Node, Token.Value, Right);
		}
		else
		{
			break;
		}
	}
	return Node;
}

FAstNodePtr FExpressionParser::ParseTerm()
{
	FAstNodePtr Node = ParseFactor();
	while (true)
	{
		const FExpressionLexerToken& Token = PeekToken();
		if (MatchValue(TEXT("+")) || MatchValue(TEXT("-")))
		{
			FAstNodePtr Right = ParseFactor();
			Node = MakeShared<FAstNodeBinaryOp>(Node, Token.Value, Right);
		}
		else
		{
			break;
		}
	}

	return Node;
}

FAstNodePtr FExpressionParser::ParseFactor()
{
	FAstNodePtr Node = ParseExponent();

	while (true)
	{
		const FExpressionLexerToken& Token = PeekToken();
		if (MatchValue(TEXT("*")) || MatchValue(TEXT("/")) || MatchValue(TEXT("%")))
		{
			FAstNodePtr Right = ParseExponent();
			Node = MakeShared<FAstNodeBinaryOp>(Node, Token.Value, Right);
		}
		else
		{
			break;
		}
	}

	return Node;
}

FAstNodePtr FExpressionParser::ParseExponent()
{
	FAstNodePtr Node = ParseUnary();
	if (MatchValue(TEXT("^")))
	{
		const FExpressionLexerToken& Token = Tokens[Offset - 1];
		FAstNodePtr Right = ParseExponent();
		Node = MakeShared<FAstNodeBinaryOp>(Node, Token.Value, Right);
	}

	return Node;
}

FAstNodePtr FExpressionParser::ParseUnary()
{
	const FExpressionLexerToken& Token = PeekToken();
	if (MatchValue(TEXT("-")) || MatchValue(TEXT("!")) || MatchValue(TEXT("+")) || MatchValue(TEXT("~")))
	{
		FAstNodePtr Operand = ParseUnary();
		return MakeShared<FAstNodeUnaryOp>(Token.Value, Operand);
	}

	return ParsePrimary();
}

FAstNodePtr FExpressionParser::ParsePrimary()
{
	const FExpressionLexerToken& Token = PeekToken();

	switch (Token.Type)
	{
	case EExpressionLexerTokenType::Number:
	case EExpressionLexerTokenType::Float:
		{
			ConsumeToken();
			return MakeShared<FAstNodeNumber>(Token.Value);
		}
	case EExpressionLexerTokenType::String:
	case EExpressionLexerTokenType::CharLiteral:
		{
			ConsumeToken();
			return MakeShared<FAstNodeString>(Token.Value);
		}
	case EExpressionLexerTokenType::Identifier:
		{
			FString Name = Token.Value;
			ConsumeToken();
			if (MatchType(EExpressionLexerTokenType::LeftParen))
			{
				return ParseFunctionCall(Name);
			}
			else
			{
				return MakeShared<FAstNodeVariable>(Name);
			}
		}
	case EExpressionLexerTokenType::LeftParen:
		{
			ConsumeToken();
			FAstNodePtr Expr = ParseExpression();
			if (!MatchType(EExpressionLexerTokenType::RightParen))
			{
				throw std::runtime_error(TCHAR_TO_UTF8(TEXT("Expected ')'")));
			}
			return Expr;
		}
	default:
		throw std::runtime_error(TCHAR_TO_UTF8(TEXT("Unexpected token in primary expression")));
	}
}

FAstNodePtr FExpressionParser::ParseFunctionCall(const FString& InName)
{
	TArray<FAstNodePtr> Args;

	// 检查是否为空参数列表 ()
	if (MatchType(EExpressionLexerTokenType::RightParen))
	{
		return MakeShared<FAstNodeFunctionCall>(InName, Args);
	}

	// 解析参数列表
	do
	{
		Args.Add(ParseExpression());

		// 检查是否结束
		if (PeekToken().Type == EExpressionLexerTokenType::RightParen)
		{
			break;
		}

		// 必须有逗号分隔参数
		if (!MatchType(EExpressionLexerTokenType::Comma))
		{
			throw std::runtime_error(TCHAR_TO_UTF8(*FString::Printf(TEXT("Expected ',' or ')' after argument %d of function '%s'"), Args.Num(), *InName)));
		}
	} while (true);

	// 确保以右括号结束
	if (!MatchType(EExpressionLexerTokenType::RightParen))
	{
		throw std::runtime_error(TCHAR_TO_UTF8(*FString::Printf(TEXT("Expected ')' after function arguments for '%s' at [%d:%d]"), *InName, PeekToken().Position.X, PeekToken().Position.Y)));
	}

	return MakeShared<FAstNodeFunctionCall>(InName, Args);
}
