#pragma once
#include "LightPanelGenerator.h"
#include "Calculate.h"

ULightPanelGenerator* gLightPanelGeneratorInstance = nullptr;

ULightPanelGenerator* ULightPanelGenerator::GetInstance()
{
	return gLightPanelGeneratorInstance;
}

ULightPanelGenerator::ULightPanelGenerator()
{
	gLightPanelGeneratorInstance = this;
}

ULightPanelGenerator::~ULightPanelGenerator()
{
	gLightPanelGeneratorInstance = nullptr;
}

TArray<FLightPanelData> ULightPanelGenerator::GeneratePanels(const TArray<FVector>& InArea, const TArray<TPair<FVector, FVector>>& InWindows, double MinSize, double Spacing, double WallDistance)
{
	std::vector<AreaLightCalculate::Point> areaPolygon;
	for (const FVector& Vertex : InArea)
	{
		areaPolygon.push_back({Vertex.X, Vertex.Y});
	}

	std::vector<AreaLightCalculate::Point> windowCenter;
	for (const TPair<FVector, FVector>& Window : InWindows)
	{
		auto Center = AreaLightCalculate::Point((Window.Key.X + Window.Value.X) / 2.0, (Window.Key.Y + Window.Value.Y) / 2.0);
		windowCenter.push_back(Center);
	}

	std::vector<AreaLightCalculate::Rectangle> lightPanels; 
	AreaLightCalculate::generate_light_panels_for_rect(areaPolygon, MinSize, Spacing, WallDistance, lightPanels, windowCenter);

	TArray<FLightPanelData> OutPanels;
	for (const AreaLightCalculate::Rectangle& Panel : lightPanels)
	{
		FLightPanelData Data;
		Data.Center = FVector2D(Panel.x, Panel.y);
		Data.Size = Panel.size;
		Data.Brightness = Panel.brightness;
		OutPanels.Add(Data);
	}

	return OutPanels;
}
