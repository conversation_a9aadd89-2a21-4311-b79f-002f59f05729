﻿// Fill out your copyright notice in the Description page of Project Settings.


#include "DSRulerLibrary.h"
#include "Curve/PolygonIntersectionUtils.h"
#include "ImageProcess.h"
#include "Library/Clipper2Library.h"
#include "SubSystems/Library/DesignStationFunctionLibrary.h"
#include "SubSystems/MVC/DSMVCSubsystem.h"
#include "SubSystems/MVC/Core/Macro.h"
#include "SubSystems/MVC/Core/Property/HouseAreaProperty.h"
#include "Subsystems/MVC/Model/House/Area/DSHouseAreaSplitLineModel.h"
#include "SubSystems/MVC/Core/Property/HousePathProperty.h"
#include "SubSystems/MVC/Core/Property/PathPointProperty.h"
#include "SubSystems/MVC/Library/DSToolLibrary.h"
#include "Subsystems/MVC/Core/Property/HouseRoofProperty.h"
#include "SubSystems/MVC/Model/Abstruct/DSPathModel.h"
#include "SubSystems/MVC/Model/Abstruct/DSPathPointModel.h"
#include "SubSystems/MVC/Model/DoorAndWindow/DSDoorAndWindowBaseModel.h"
#include "SubSystems/MVC/Model/House/Wall/DSHouseWallModel.h"
#include "SubSystems/MVC/View/Custom/DSCupboardBaseView.h"
#include "Subsystems/MVC/Model/Kitchen/Sink/DSSinkModel.h"
#include "Subsystems/MVC/View/Kitchen/Sink/DSSinkView.h"
#include "Subsystems/MVC/Core/Property/SinkProperty.h"
#include "SubSystems/AdaptiveAdsorption/Core/Operator/FunctionalAdaptationOperator.h"
#include "SubSystems/ModelDependencySubsystem/DSModelDependencySubsystem.h"
#include "Runtime/Engine/Classes/Kismet/KismetSystemLibrary.h"
#include "SubSystems/MVC/Core/Property/LineOrnamentLineProperty.h"
#include "SubSystems/MVC/Model/Group/DSMultiModel.h" 
#include "SubSystems/MVC/Core/Property/FurnitureProperty.h"
#include "SubSystems/MVC/Model/Furniture/MoldingFurniture/DSMoldingCeilingModel.h"
#include "Kismet/GameplayStatics.h"
#include "Subsystems/MVC/Model/Group/DSGroupModel.h"
#include "SubSystems/MVC/Core/Property/DoorAndWindowProperty.h"
#include "Subsystems/MVC/Model/Custom/Library/DSWallBoardLibrary.h"
#include "SubSystems/AdaptiveAdsorption/Core/Operator/DrawerAdaptationOperator.h"

DEFINE_LOG_CATEGORY(DSRulerLibraryLog);

//to wall ruler mark flag
extern const FString RULER_WALL_FRONT;
extern const FString RULER_WALL_RIGHT;
extern const FString RULER_WALL_BACK;
extern const FString RULER_WALL_LEFT;

//to item ruler mark flag
extern const FString RULER_ITEM_FRONT;
extern const FString RULER_ITEM_RIGHT;
extern const FString RULER_ITEM_BACK;
extern const FString RULER_ITEM_LEFT;

//to room ruler mark flag
extern const FString RULER_CEILING_UP;
extern const FString RULER_FLOOR_DOWN;
extern const FString RULER_ITEM_UP;
extern const FString RULER_ITEM_DOWN;

//self ruler mark flag
extern const FString RULER_PATH_SELF_RIGHT;
extern const FString RULER_PATH_SELF_LEFT;

//start point left/right ruler mark flag
extern const FString RULER_PATH_POINT_START_0;
extern const FString RULER_PATH_POINT_START_1;

//end point left/right ruler mark flag
extern const FString RULER_PATH_POINT_END_0;
extern const FString RULER_PATH_POINT_END_1;

//path[wall, platform...] ruler mark flag
extern const FString RULER_POINT_PATH_0;
extern const FString RULER_POINT_PATH_1;
extern const FString RULER_POINT_PATH_2;
extern const FString RULER_POINT_PATH_3;
extern const FString RULER_POINT_PATH_4;
extern const FString RULER_POINT_PATH_5;

//self WHD ruler mark flag
extern const FString RULER_SELF_WIDTH;
extern const FString RULER_SELF_DEPTH;
extern const FString RULER_SELF_HEIGHT;

//segment ruler mark flag
extern const FString RULER_SEGMENT;

//segment start point left/right ruler mark flag
extern const FString RULER_SEGMENT_START_0;
extern const FString RULER_SEGMENT_START_1;

//segment end point left/right ruler mark flag
extern const FString RULER_SEGMENT_END_0;
extern const FString RULER_SEGMENT_END_1;

//on wall left/right ruler mark flag
extern const FString RULER_ON_WALL_POS;
extern const FString RULER_ON_WALL_NEG;

extern const double RULER_OFFSET;

extern const double DS_Rect_Ruler_Hit_Dist = 10000.0;

constexpr double DS_Point_Same_Tol = 2;

void UDSRulerLibrary::RefreshRulerProperty(FDSRulerDisplayerProperty* RulerProperty, UDSBaseModel* InTargetModel)
{
	if (!RulerProperty || !DS_MODEL_VALID_FOR_USE(InTargetModel))
	{
		return;
	}

	RulerProperty->ResetRulerShowState();
	
	TArray<EDSModelType> FilterTypes;

	switch (UDSMVCSubsystem::GetInstance()->GetStateType())
	{
	case EDSFSMState::FSM_CounterTop:
		{
			FilterTypes = { EDSModelType::E_Generated_CounterTop, EDSModelType::E_Generated_CounterTop_Line, EDSModelType::E_Generated_CounterTop_Point };
		}
		break;
	default:
		break;
	}

	if (FilterTypes.IsValidIndex(0) && !FilterTypes.Contains(InTargetModel->GetModelType()))
	{
		return;
	}

	if (InTargetModel->GetModelType() == EDSModelType::E_House_Area_Split_Line)
	{
		RefreshRulerProperty_AreaLine(RulerProperty, InTargetModel);
	}
	else if (InTargetModel->GetModelType() == EDSModelType::E_House_Pillar)
	{
		RefreshRulerProperty_Pillar(RulerProperty, InTargetModel);
	}
	else if (InTargetModel->GetModelType() == EDSModelType::E_House_Wall
		|| InTargetModel->GetModelType() == EDSModelType::E_House_Beam
		|| InTargetModel->GetModelType() == EDSModelType::E_House_Platform)
	{
		RefreshRulerProperty_Segment(RulerProperty, InTargetModel);
	}
	else if (InTargetModel->GetModelType() == EDSModelType::E_HouseWallPathPoint
		|| InTargetModel->GetModelType() == EDSModelType::E_BeamPathPoint
		|| InTargetModel->GetModelType() == EDSModelType::E_PlatformPathPoint)
	{
		RefreshRulerProperty_Point(RulerProperty, InTargetModel);
	}
	else if (InTargetModel->GetModelType() == EDSModelType::E_House_Window
		|| InTargetModel->GetModelType() == EDSModelType::E_House_Door)
	{
		RefreshRulerProperty_WinDoor(RulerProperty, InTargetModel);
	}
	else if (InTargetModel->GetModelType() >= EDSModelType::E_Custom_UpperCabinet
		&& InTargetModel->GetModelType() < EDSModelType::E_Custom_Furniture_Range_End)
	{
		if (InTargetModel->GetModelType() == EDSModelType::E_Custom_LayoutDoor)
		{
			RefreshRulerProperty_LayoutDoor(RulerProperty, InTargetModel);
		}
		else if (InTargetModel->GetModelType() == EDSModelType::E_Custom_Knob
			|| InTargetModel->GetModelType() == EDSModelType::E_Custom_DoorPanel
			|| InTargetModel->GetModelType() == EDSModelType::E_Custom_DoorPanel_AluminumFrame
			|| InTargetModel->GetModelType() == EDSModelType::E_Custom_DoorPanel_Flat
			|| InTargetModel->GetModelType() == EDSModelType::E_Custom_DoorPanel_Fake
			|| InTargetModel->GetModelType() == EDSModelType::E_Custom_DoorPanel_Glass
			|| InTargetModel->GetModelType() == EDSModelType::E_Custom_DoorPanel_SolidWood
			|| InTargetModel->GetModelType() == EDSModelType::E_Custom_CabinetBoard)
		{
			//暂时不显示标尺
		}
		else
		{
			RefreshRulerProperty_CustomCupboard(RulerProperty, InTargetModel);
		}
	}
	else if (InTargetModel->GetModelType() == EDSModelType::E_Generated_CounterTop)
	{
		UDSBaseModel* CurrentModel = UDSMVCSubsystem::GetInstance()->GetCurrentModel();
		if (CurrentModel != nullptr && CurrentModel->GetModelType() == EDSModelType::E_Generated_CounterTop_Line)
		{
			RefreshRulerProperty_CounterTopLine(RulerProperty, CurrentModel);
		}
		else if (CurrentModel != nullptr && CurrentModel->GetModelType() == EDSModelType::E_Generated_CounterTop_Point)
		{
			RefreshRulerProperty_CounterTopPoint(RulerProperty, CurrentModel);
		}
		else
		{
			RefreshRulerProperty_CounterTop(RulerProperty, InTargetModel);
		}
	}
	else if (InTargetModel->GetModelType() == EDSModelType::E_Generated_CounterTop_Line)
	{
		RefreshRulerProperty_CounterTopLine(RulerProperty, InTargetModel);
	}
	else if (InTargetModel->GetModelType() == EDSModelType::E_Generated_CounterTop_Point)
	{
		RefreshRulerProperty_CounterTopPoint(RulerProperty, InTargetModel);
	}
	else if (InTargetModel->GetModelType() == EDSModelType::E_Custom_Sink)
	{
		RefreshRulerProperty_Sink(RulerProperty, InTargetModel);
	}
	else if (InTargetModel->GetModelType() == EDSModelType::E_Custom_Stove)
	{
		RefreshRulerProperty_Stove(RulerProperty, InTargetModel);
	}
	else if (InTargetModel->GetModelType() == EDSModelType::E_Custom_RangeHood)
	{
		RefreshRulerProperty_RangeHood(RulerProperty, InTargetModel);
	}
	else if (InTargetModel->GetModelType() == EDSModelType::E_Generated_LineOrnamentBase_Line)
	{
		RefreshRulerProperty_OrnamentLine(RulerProperty, InTargetModel);
	}
	else if (InTargetModel->GetModelType() == EDSModelType::E_Furniture_HouseFurniture)
	{
		RefreshRulerProperty_Furniture(RulerProperty, InTargetModel);
	}
	else if (InTargetModel->GetModelType() == EDSModelType::E_MultiSelect || InTargetModel->GetModelType() == EDSModelType::E_Group)
	{
		RefreshRulerProperty_MultiSelect(RulerProperty, InTargetModel);
	}
	else if (InTargetModel->GetModelType() == EDSModelType::E_Furniture_MoldingCeiling)
	{
		RefreshRulerProperty_MoldingCeiling(RulerProperty, InTargetModel);
	}
	else
	{
		UE_LOG(DSRulerLibraryLog, Verbose, TEXT("RefreshRulerProperty --- Model Type [%d] No Ruler"), static_cast<int32>(InTargetModel->GetModelType()));
	}
}

void UDSRulerLibrary::RefreshRulerProperty_AreaLine(FDSRulerDisplayerProperty* RulerProperty, UDSBaseModel* InTargetModel)
{
	UE_LOG(DSRulerLibraryLog, Verbose, TEXT("RefreshRulerProperty_AreaLine"));
	auto Line = Cast<UDSHouseAreaSplitLineModel>(InTargetModel);
	auto Prop = static_cast<FDSSegmentProperty*>(Line->GetProperty());
	auto Start = Prop->SegmentStart;
	auto End = Prop->SegmentEnd;
	auto Nor = Prop->GetNormal();

	auto LinkAreas = Line->GetLinkAreas();
	int32 Index = 0;
	bool LRShow = false;
	for (auto& Iter : LinkAreas)
	{
		if (!DS_MODEL_VALID_FOR_USE(Iter))
		{
			continue;
		}
		auto AreaProp = StaticCastSharedPtr<FDSHouseAreaProperty>(Iter->GetPropertySharedPtr());
		if (!AreaProp.IsValid())
		{
			continue;
		}
		auto AreaOutline = AreaProp->Points;

		for (int32 i = 0; i < AreaOutline.Num(); i++)
		{
			int32 n = (i + 1) % AreaOutline.Num();
			auto LStart = AreaOutline[i];
			auto LEnd = AreaOutline[n];

			if ((LStart.Equals(Start, DS_Point_Same_Tol) && LEnd.Equals(End, DS_Point_Same_Tol))
				|| (LStart.Equals(End, DS_Point_Same_Tol) && LEnd.Equals(Start, DS_Point_Same_Tol)))
			{
				continue;
			}

			auto LNor = FVector::CrossProduct(LEnd - LStart, FVector::ZAxisVector).GetSafeNormal();
			FString Name = "null";

			if (LStart.Equals(Start, DS_Point_Same_Tol))
			{
				Name = Index == 0 ? RULER_SEGMENT_START_0 : RULER_SEGMENT_START_1;
			}
			else if (LEnd.Equals(Start, DS_Point_Same_Tol))
			{
				Name = Index == 0 ? RULER_SEGMENT_START_0 : RULER_SEGMENT_START_1;
				auto Temp = LEnd;
				LEnd = LStart;
				LStart = Temp;
			}
			else if (LStart.Equals(End, DS_Point_Same_Tol))
			{
				Name = Index == 0 ? RULER_SEGMENT_END_0 : RULER_SEGMENT_END_1;
			}
			else if (LEnd.Equals(End, DS_Point_Same_Tol))
			{
				Name = Index == 0 ? RULER_SEGMENT_END_0 : RULER_SEGMENT_END_1;
				auto Temp = LEnd;
				LEnd = LStart;
				LStart = Temp;
			}

			bool RulerShow = InTargetModel->bThisRulerShow(Name);
			if (RulerProperty->RulerMap.Contains(Name))
			{
				//UE_LOG(DSRulerLibraryLog, Warning, TEXT("Ruler Name %s Set Property"), *Name);

				RulerProperty->RulerMap[Name].bShow = RulerShow;
				RulerProperty->RulerMap[Name].bOnlyRead = false;

				RulerProperty->RulerMap[Name].SegmentStart = LStart + LNor * RULER_OFFSET;
				RulerProperty->RulerMap[Name].SegmentEnd = LEnd + LNor * RULER_OFFSET;

				LRShow = true;
			}
		}

		++Index;
	}

	RulerProperty->RulerMap[RULER_SEGMENT].SegmentStart = Start + Nor * RULER_OFFSET;
	RulerProperty->RulerMap[RULER_SEGMENT].SegmentEnd = End + Nor * RULER_OFFSET;
	RulerProperty->RulerMap[RULER_SEGMENT].bShow = InTargetModel->bThisRulerShow(RULER_SEGMENT);
	RulerProperty->RulerMap[RULER_SEGMENT].bOnlyRead = LRShow;
}

void UDSRulerLibrary::RefreshRulerProperty_Pillar(FDSRulerDisplayerProperty* RulerProperty, UDSBaseModel* InTargetModel)
{
	TArray<FVector> Outer;
	TArray<FVector> Rect;
	if (InTargetModel->GetModelOuter(Outer) && InTargetModel->GetModelOuterRect(Rect))
	{
		auto AllModels = UDSMVCSubsystem::GetInstance()->GetAllModels();
		auto& RulerMap = RulerProperty->RulerMap;

		auto Count = Rect.Num();
		if (Outer.Num() != Rect.Num())
		{
			return;
		}

		for (int32 i = 0; i < Count; ++i)
		{
			FString RulerItem = TEXT("");
			FString RulerWall = TEXT("");
			GetRulerInfo_Pillar(i, RulerItem, RulerWall);

			int32 n = (i + 1) % Count;

			auto PathStart = Outer[i]; // (Rect[i] + Rect[n]) * 0.5f;
			PathStart.Z = 0.f;

			bool bVaild = true;
			auto AllPaths = UDSMVCSubsystem::GetInstance()->GetModels(
				{EDSModelType::E_House_Wall, EDSModelType::E_House_Beam, EDSModelType::E_House_Platform}
			);
			// auto AllPaths = UDSMVCSubsystem::GetInstance()->GetModels(EDSModelType::E_House_Wall);
			// AllPaths.Append(UDSMVCSubsystem::GetInstance()->GetModels(EDSModelType::E_House_Beam));
			// AllPaths.Append(UDSMVCSubsystem::GetInstance()->GetModels(EDSModelType::E_House_Platform));

			for (auto& Iter : AllPaths)
			{
				auto OL = Iter->GetBottomOutline();

				if (FImageProcessModule::Get()->PointInPolygon(PathStart, OL, true, 1.f))
				{
					bVaild = false;
					break;
				}
			}

			auto Dir = (Rect[n] - Rect[i]).GetSafeNormal();
			auto Nor = FVector::CrossProduct(Dir, FVector::ZAxisVector).GetSafeNormal();
			auto PathEnd = PathStart + Nor * DS_Rect_Ruler_Hit_Dist;
			PathEnd.Z = 0.f;
			auto Width = (Rect[n] - Rect[i]).Size2D();

			double MinDist = MAX_dbl;
			double MinDistToWall = MAX_dbl;

			bool bHitMod = false;
			for (auto& Mod : AllModels)
			{
				if (Mod == InTargetModel)
				{
					continue;
				}
				auto ModType = Mod->GetModelType();

				if (ModType != EDSModelType::E_House_Wall && ModType != EDSModelType::E_House_Pillar
					&& ModType != EDSModelType::E_House_Beam && ModType != EDSModelType::E_House_Platform)
				{
					continue;
				}

				TArray<FVector> ModRect;

				bool bIsPath = ModType == EDSModelType::E_House_Wall || ModType == EDSModelType::E_House_Beam || ModType == EDSModelType::E_House_Platform;
				if (bIsPath)
				{
					ModRect = Mod->GetBottomOutline();
				}
				else
				{
					Mod->GetModelOuterRect(ModRect);
				}
				bool bHit = FClipper2Library::PathAndPolygonIntersection(PathStart, PathEnd, Width * 0.5f, ModRect);
				if (!bHit)
				{
					continue;
				}
				bHitMod = true;
				for (int32 j = 0; j < ModRect.Num(); j++)
				{
					int32 m = (j + 1) % ModRect.Num();

					if (bIsPath)
					{
						FVector IscLoc;
						bool bIntersection = FMath::SegmentIntersection2D(PathStart, PathEnd, ModRect[j], ModRect[m], IscLoc);
						if (bIntersection)
						{
							auto Dist = FVector::Dist2D(IscLoc, PathStart);
							if (Dist < DS_Rect_Ruler_Hit_Dist)
							{
								if (Dist < MinDistToWall)
								{
									MinDistToWall = Dist;
									RulerMap[RulerWall].SegmentStart = PathStart;
									RulerMap[RulerWall].SegmentStart.Z = 10.f;
									RulerMap[RulerWall].SegmentEnd = IscLoc;
									RulerMap[RulerWall].SegmentEnd.Z = 10.f;
									RulerMap[RulerWall].bShow = bVaild;
									RulerMap[RulerWall].bOnlyRead = InTargetModel->GetProperty()->GetIsLock() || InTargetModel->GetProperty()->GetIsMoving();
								}
							}
						}
					}
					else
					{
						if (FGeometryLibrary::TwoLineParallel(Rect[i], Rect[n], ModRect[j], ModRect[m]))
						{
							auto Dist = FGeometryLibrary::TwoLineDistance(Rect[i], Rect[n], ModRect[j], ModRect[m]);
							if (Dist < DS_Rect_Ruler_Hit_Dist)
							{
								auto RulerDir = FGeometryLibrary::DirectionBetweenTwoLines(Rect[i], Rect[n], ModRect[j], ModRect[m]);
								if (ModType == EDSModelType::E_House_Pillar || ModType == EDSModelType::E_House_Platform || ModType == EDSModelType::E_House_Beam)
								{
									if (Dist < MinDist/* && Dist < FVector::Dist2D(RulerMap[RulerItem].SegmentStart, RulerMap[RulerItem].SegmentEnd)*/)
									{
										MinDist = Dist;
										RulerMap[RulerItem].SegmentStart = Outer[i];
										RulerMap[RulerItem].SegmentStart.Z = 10.f;
										RulerMap[RulerItem].SegmentEnd = Outer[i] + RulerDir * Dist;
										RulerMap[RulerItem].SegmentEnd.Z = 10.f;
										RulerMap[RulerItem].bShow = bVaild;
										RulerMap[RulerItem].bOnlyRead = InTargetModel->GetProperty()->GetIsLock() || InTargetModel->GetProperty()->GetIsMoving();
									}
								}
							}
						}
					}
				}
			}
			if (!bHitMod)
			{
				RulerMap[RulerItem].SegmentStart = FVector::ZeroVector;
				RulerMap[RulerItem].SegmentEnd = FVector::ZeroVector;
				RulerMap[RulerItem].bShow = false;
				RulerMap[RulerWall].bShow = false;
			}
		}

		auto SizeProp = InTargetModel->GetProperty()->GetSizeProperty();
		auto SelfWidth = SizeProp.Width;
		auto SelfDepth = SizeProp.Depth;

		auto HW = SelfWidth * 0.5f;
		auto HD = SelfDepth * 0.5f;

		auto SelfCenter = InTargetModel->GetProperty()->GetTransformProperty().Location;
		auto ForwardDir = InTargetModel->GetProperty()->GetTransformProperty().Rotation.Vector();
		auto RightDir = FVector::CrossProduct(FVector::ZAxisVector, ForwardDir).GetSafeNormal();


		auto SelfWidthStart = SelfCenter + (HD + RULER_OFFSET) * ForwardDir - HW * RightDir;
		auto SelfWidthEnd = SelfCenter + (HD + RULER_OFFSET) * ForwardDir + HW * RightDir;

		auto SelfDepthStart = SelfCenter + HD * ForwardDir + (HW + RULER_OFFSET) * RightDir;
		auto SelfDepthEnd = SelfCenter - HD * ForwardDir + (HW + RULER_OFFSET) * RightDir;

		RulerProperty->RulerMap[RULER_SELF_WIDTH].SegmentStart = SelfWidthStart;
		RulerProperty->RulerMap[RULER_SELF_WIDTH].SegmentEnd = SelfWidthEnd;
		RulerProperty->RulerMap[RULER_SELF_WIDTH].bOnlyRead = InTargetModel->GetProperty()->GetIsLock() || InTargetModel->GetProperty()->GetIsMoving();
		RulerProperty->RulerMap[RULER_SELF_WIDTH].bShow = InTargetModel->bThisRulerShow(RULER_SELF_WIDTH);

		RulerProperty->RulerMap[RULER_SELF_DEPTH].SegmentStart = SelfDepthStart;
		RulerProperty->RulerMap[RULER_SELF_DEPTH].SegmentEnd = SelfDepthEnd;
		RulerProperty->RulerMap[RULER_SELF_DEPTH].bOnlyRead = InTargetModel->GetProperty()->GetIsLock() || InTargetModel->GetProperty()->GetIsMoving();
		RulerProperty->RulerMap[RULER_SELF_DEPTH].bShow = InTargetModel->bThisRulerShow(RULER_SELF_WIDTH);

		GetRulerInfo_NearlyShow_WallAndItem(
			RulerMap[RULER_WALL_BACK], InTargetModel->bThisRulerShow(RULER_WALL_BACK),
			RulerMap[RULER_ITEM_BACK], InTargetModel->bThisRulerShow(RULER_ITEM_BACK));

		GetRulerInfo_NearlyShow_WallAndItem(
			RulerMap[RULER_WALL_RIGHT], InTargetModel->bThisRulerShow(RULER_WALL_RIGHT),
			RulerMap[RULER_ITEM_RIGHT], InTargetModel->bThisRulerShow(RULER_ITEM_RIGHT));

		GetRulerInfo_NearlyShow_WallAndItem(
			RulerMap[RULER_WALL_LEFT], InTargetModel->bThisRulerShow(RULER_WALL_LEFT),
			RulerMap[RULER_ITEM_LEFT], InTargetModel->bThisRulerShow(RULER_ITEM_LEFT));

		GetRulerInfo_NearlyShow_WallAndItem(
			RulerMap[RULER_WALL_FRONT], InTargetModel->bThisRulerShow(RULER_WALL_FRONT),
			RulerMap[RULER_ITEM_FRONT], InTargetModel->bThisRulerShow(RULER_ITEM_FRONT));
	}
}

void UDSRulerLibrary::RefreshRulerProperty_Segment(FDSRulerDisplayerProperty* RulerProperty, UDSBaseModel* InTargetModel)
{
	auto PathModel = Cast<UDSPathModel>(InTargetModel);
	auto PathProp = static_cast<FDSHousePathProperty*>(InTargetModel->GetProperty());

	auto LeftSeg = PathProp->GetLeftSegment();
	auto LeftStart = LeftSeg->SegmentStart;
	auto LeftEnd = LeftSeg->SegmentEnd;

	auto RightSeg = PathProp->GetRightSegment();
	auto RightStart = RightSeg->SegmentStart;
	auto RightEnd = RightSeg->SegmentEnd;

	auto Nor = PathProp->GetNormal();

	RulerProperty->RulerMap[RULER_PATH_SELF_LEFT].SegmentStart = RightStart - (Nor * RULER_OFFSET);
	RulerProperty->RulerMap[RULER_PATH_SELF_LEFT].SegmentEnd = RightEnd - (Nor * RULER_OFFSET);

	RulerProperty->RulerMap[RULER_PATH_SELF_RIGHT].SegmentStart = LeftStart + (Nor * RULER_OFFSET);
	RulerProperty->RulerMap[RULER_PATH_SELF_RIGHT].SegmentEnd = LeftEnd + (Nor * RULER_OFFSET);

	auto PathStart = PathModel->GetPathPointStart();
	auto PathEnd = PathModel->GetPathPointEnd();

	if (PathStart && PathStart->GetLinkModels().Num() > 1)
	{
		TArray<UDSPathModel*> LinkPaths;
		for (auto& Path : PathStart->GetLinkModels())
		{
			if (DS_MODEL_VALID_FOR_USE(Path))
			{
				if (Path != InTargetModel && Path->GetModelType() == InTargetModel->GetModelType())
				{
					LinkPaths.Add(Cast<UDSPathModel>(Path));
				}
			}
		}

		TArray<UDSPathModel*> ParPaths;
		if (LinkPaths.Num() == 1)
		{
			ParPaths = LinkPaths;
		}
		else
		{
			auto MaxRad = -999.f;
			auto MinRad = 999.f;
			UDSPathModel* MaxModel = nullptr;
			UDSPathModel* MinModel = nullptr;

			auto RulerDir = PathProp->GetDirection();
			for (auto& P0 : LinkPaths)
			{
				auto OtherPoint = P0->GetPathPointStart() == PathStart ? P0->GetPathPointEnd() : P0->GetPathPointStart();
				if (OtherPoint)
				{
					auto Dir = (OtherPoint->GetProperty()->GetTransformProperty().Location - PathStart->GetProperty()->GetTransformProperty().Location).GetSafeNormal();
					auto Rad = FGeometryLibrary::RadOfTwoVector2D(RulerDir, Dir);
					if (Rad < MinRad)
					{
						MinRad = Rad;
						MinModel = Cast<UDSPathModel>(P0);
					}
					if (Rad > MaxRad)
					{
						MaxRad = Rad;
						MaxModel = Cast<UDSPathModel>(P0);
					}
				}
			}
			if (MaxModel)
			{
				ParPaths.AddUnique(MaxModel);
			}
			if (MinModel)
			{
				ParPaths.AddUnique(MinModel);
			}
		}

		auto LinkCount = ParPaths.Num();
		if (LinkCount <= 2)
		{
			int32 Index = 0;
			for (auto& LP : ParPaths)
			{
				auto LinkProp = static_cast<FDSHousePathProperty*>(LP->GetProperty());

				auto Seg = LinkProp->GetLeftSegment()->GetLength() <= LinkProp->GetRightSegment()->GetLength() ? LinkProp->GetLeftSegment() : LinkProp->GetRightSegment();

				auto LinkSegStart = Seg->SegmentStart;
				auto LinkSegEnd = Seg->SegmentEnd;
				auto LinkNor = LinkProp->GetLeftSegment()->GetLength() <= LinkProp->GetRightSegment()->GetLength() ? LinkProp->GetNormal() : -LinkProp->GetNormal();

				auto LinkWallStart = LP->GetPathPointStart();
				auto LinkWallEnd = LP->GetPathPointEnd();
				auto Name = Index == 0 ? RULER_PATH_POINT_START_0 : RULER_PATH_POINT_START_1;

				if (LinkWallStart == PathStart || LinkWallEnd == PathStart)
				{
					auto LeftStart = LinkProp->GetLeftSegment()->SegmentStart;
					auto LeftEnd = LinkProp->GetLeftSegment()->SegmentEnd;

					RulerProperty->RulerMap[Name].SegmentStart = FVector::Distance(LeftStart, PathStart->GetProperty()->GetTransformProperty().Location) < FVector::Distance(
						                                             LeftEnd, PathStart->GetProperty()->GetTransformProperty().Location) ?
						                                             LinkSegStart :
						                                             LinkSegEnd;
					RulerProperty->RulerMap[Name].SegmentEnd = RulerProperty->RulerMap[Name].SegmentStart.Equals(LinkSegStart) ? LinkSegEnd : LinkSegStart;

					RulerProperty->RulerMap[Name].SegmentStart += LinkNor * RULER_OFFSET;
					RulerProperty->RulerMap[Name].SegmentEnd += LinkNor * RULER_OFFSET;

					RulerProperty->RulerMap[Name].bShow = InTargetModel->bThisRulerShow(Name);
					RulerProperty->RulerMap[Name].bOnlyRead = false;
				}
				++Index;
			}
		}
	}
	if (PathEnd && PathEnd->GetLinkModels().Num() > 1)
	{
		TArray<UDSPathModel*> LinkPaths;
		for (auto& Path : PathEnd->GetLinkModels())
		{
			if (DS_MODEL_VALID_FOR_USE(Path))
			{
				if (Path != InTargetModel && Path->GetModelType() == InTargetModel->GetModelType())
				{
					LinkPaths.Add(Cast<UDSPathModel>(Path));
				}
			}
		}

		TArray<UDSPathModel*> ParPaths;
		if (LinkPaths.Num() == 1)
		{
			ParPaths = LinkPaths;
		}
		else
		{
			auto MaxRad = -999.f;
			auto MinRad = 999.f;
			UDSPathModel* MaxModel = nullptr;
			UDSPathModel* MinModel = nullptr;

			auto RulerDir = -PathProp->GetDirection();
			for (auto& P0 : LinkPaths)
			{
				auto Prop0 = static_cast<FDSHousePathProperty*>(P0->GetProperty());

				auto OtherPoint = P0->GetPathPointStart() == PathEnd ? P0->GetPathPointEnd() : P0->GetPathPointStart();
				if (OtherPoint)
				{
					auto Dir = (OtherPoint->GetProperty()->GetTransformProperty().Location - PathEnd->GetProperty()->GetTransformProperty().Location).GetSafeNormal();
					auto Rad = FGeometryLibrary::RadOfTwoVector2D(RulerDir, Dir);
					if (Rad < MinRad)
					{
						MinRad = Rad;
						MinModel = Cast<UDSPathModel>(P0);
					}
					if (Rad > MaxRad)
					{
						MaxRad = Rad;
						MaxModel = Cast<UDSPathModel>(P0);
					}
				}
			}
			if (MaxModel)
			{
				ParPaths.AddUnique(MaxModel);
			}
			if (MinModel)
			{
				ParPaths.AddUnique(MinModel);
			}
		}


		auto LinkCount = ParPaths.Num();
		if (LinkCount <= 2)
		{
			int32 Index = 0;
			for (auto& LP : ParPaths)
			{
				auto LinkProp = static_cast<FDSHousePathProperty*>(LP->GetProperty());

				auto Seg = LinkProp->GetLeftSegment()->GetLength() <= LinkProp->GetRightSegment()->GetLength() ? LinkProp->GetLeftSegment() : LinkProp->GetRightSegment();

				auto LinkSegStart = Seg->SegmentStart;
				auto LinkSegEnd = Seg->SegmentEnd;
				auto LinkNor = LinkProp->GetLeftSegment()->GetLength() <= LinkProp->GetRightSegment()->GetLength() ? LinkProp->GetNormal() : -LinkProp->GetNormal();

				auto LinkWallStart = LP->GetPathPointStart();
				auto LinkWallEnd = LP->GetPathPointEnd();
				auto Name = Index == 0 ? RULER_PATH_POINT_END_0 : RULER_PATH_POINT_END_1;

				if (LinkWallStart == PathEnd || LinkWallEnd == PathEnd)
				{
					auto LeftStart = LinkProp->GetLeftSegment()->SegmentStart;
					auto LeftEnd = LinkProp->GetLeftSegment()->SegmentEnd;

					RulerProperty->RulerMap[Name].SegmentStart = FVector::Distance(LeftStart, PathEnd->GetProperty()->GetTransformProperty().Location) < FVector::Distance(
						                                             LeftEnd, PathEnd->GetProperty()->GetTransformProperty().Location) ?
						                                             LinkSegStart :
						                                             LinkSegEnd;
					RulerProperty->RulerMap[Name].SegmentEnd = RulerProperty->RulerMap[Name].SegmentStart.Equals(LinkSegStart) ? LinkSegEnd : LinkSegStart;

					RulerProperty->RulerMap[Name].SegmentStart += LinkNor * RULER_OFFSET;
					RulerProperty->RulerMap[Name].SegmentEnd += LinkNor * RULER_OFFSET;

					RulerProperty->RulerMap[Name].bShow = InTargetModel->bThisRulerShow(Name);
					RulerProperty->RulerMap[Name].bOnlyRead = false;
				}
				++Index;
			}
		}
	}

	bool bShowLeft = RulerProperty->RulerMap[RULER_PATH_SELF_RIGHT].GetLength() <= RulerProperty->RulerMap[RULER_PATH_SELF_LEFT].GetLength();

	RulerProperty->RulerMap[RULER_PATH_SELF_RIGHT].bShow = bShowLeft && InTargetModel->bThisRulerShow(RULER_PATH_SELF_RIGHT);
	RulerProperty->RulerMap[RULER_PATH_SELF_LEFT].bShow = !bShowLeft && InTargetModel->bThisRulerShow(RULER_PATH_SELF_LEFT);

	if (PathStart && PathStart->GetLinkModels().Num() > 1 && PathEnd && PathEnd->GetLinkModels().Num() > 1)
	{
		RulerProperty->RulerMap[RULER_PATH_SELF_LEFT].bOnlyRead = true;
		RulerProperty->RulerMap[RULER_PATH_SELF_RIGHT].bOnlyRead = true;
	}
	else
	{
		RulerProperty->RulerMap[RULER_PATH_SELF_LEFT].bOnlyRead = false;
		RulerProperty->RulerMap[RULER_PATH_SELF_RIGHT].bOnlyRead = false;
	}
}

void UDSRulerLibrary::RefreshRulerProperty_Point(FDSRulerDisplayerProperty* RulerProperty, UDSBaseModel* InTargetModel)
{
	auto PointModel = Cast<UDSPathPointModel>(InTargetModel);
	auto PathProp = static_cast<FDSPathPointProperty*>(PointModel->GetProperty());


	TArray<UDSPathModel*> LinkPaths;
	for (auto& Iter : PointModel->GetLinkModels())
	{
		auto Path = Cast<UDSPathModel>(Iter);
		if (Path)
		{
			LinkPaths.Add(Path);
		}
	}

	if (LinkPaths.Num() <= 6)
	{
		int32 Index = 0;
		for (auto& LP : LinkPaths)
		{
			auto LinkProp = static_cast<FDSHousePathProperty*>(LP->GetProperty());
			auto LLen = LinkProp->GetLeftSegment()->GetLength();
			auto RLen = LinkProp->GetRightSegment()->GetLength();

			auto Seg = (FMath::Abs(LLen - RLen) < 1 || LLen < RLen) ? LinkProp->GetLeftSegment() : LinkProp->GetRightSegment();


			auto LinkSegStart = Seg->SegmentStart;
			auto LinkSegEnd = Seg->SegmentEnd;
			auto LinkNor = LinkProp->GetLeftSegment()->GetLength() <= LinkProp->GetRightSegment()->GetLength() ? LinkProp->GetNormal() : -LinkProp->GetNormal();

			auto LinkWallStart = LP->GetPathPointStart();
			auto LinkWallEnd = LP->GetPathPointEnd();
			auto Name = FString(TEXT("RULER_POINT_PATH_")) + FString::FromInt(Index);

			auto TarLoc = PointModel->GetProperty()->GetTransformProperty().Location;

			if (LinkWallStart == PointModel || LinkWallEnd == PointModel)
			{
				auto LeftStart = LinkProp->SegmentStart;
				auto LeftEnd = LinkProp->SegmentEnd;

				RulerProperty->RulerMap[Name].SegmentStart = FVector::Distance(LeftStart, TarLoc) < FVector::Distance(LeftEnd, TarLoc) ? LinkSegStart : LinkSegEnd;
				RulerProperty->RulerMap[Name].SegmentEnd = RulerProperty->RulerMap[Name].SegmentStart.Equals(LinkSegStart) ? LinkSegEnd : LinkSegStart;

				RulerProperty->RulerMap[Name].SegmentStart += LinkNor * RULER_OFFSET;
				RulerProperty->RulerMap[Name].SegmentEnd += LinkNor * RULER_OFFSET;

				RulerProperty->RulerMap[Name].bShow = InTargetModel->bThisRulerShow(Name);
				//PropertyRulerDisplayer->RulerMap[Name].bFocus = Index == 0;
				RulerProperty->RulerMap[Name].bOnlyRead = false;
			}
			++Index;
		}
	}
}

void UDSRulerLibrary::RefreshRulerProperty_WinDoor(FDSRulerDisplayerProperty* RulerProperty, UDSBaseModel* InTargetModel)
{
	auto PathModel = Cast<UDSDoorAndWindowBaseModel>(InTargetModel);
	auto LinkWall = PathModel->GetSnapWall(); //PathModel->GetLinkWall();
	if (LinkWall && UDSToolLibrary::ModelVaild(InTargetModel))
	{
		auto LinkProp = static_cast<FDSHousePathProperty*>(LinkWall->GetProperty());
		auto LinkLeft = LinkProp->GetLeftSegment();
		auto LinkRight = LinkProp->GetRightSegment();
		auto LinkDir = LinkProp->GetDirection();
		auto LinkNor = LinkProp->GetNormal();

		bool bLeft = LinkLeft->GetLength() >= LinkRight->GetLength();

		auto PathProp = static_cast<FDSHousePathProperty*>(InTargetModel->GetProperty());

		auto LeftSeg = PathProp->GetLeftSegment();
		auto LeftStart = LeftSeg->SegmentStart;
		auto LeftEnd = LeftSeg->SegmentEnd;

		auto RightSeg = PathProp->GetRightSegment();
		auto RightStart = RightSeg->SegmentStart;
		auto RightEnd = RightSeg->SegmentEnd;

		TArray<FVector> Points = {LeftStart, LeftEnd, RightStart, RightEnd};
		auto Nor = PathProp->GetNormal();

		RulerProperty->RulerMap[RULER_PATH_SELF_RIGHT].SegmentStart = RightStart - (Nor * RULER_OFFSET);
		RulerProperty->RulerMap[RULER_PATH_SELF_RIGHT].SegmentEnd = RightEnd - (Nor * RULER_OFFSET);

		RulerProperty->RulerMap[RULER_PATH_SELF_LEFT].SegmentStart = LeftStart + (Nor * RULER_OFFSET);
		RulerProperty->RulerMap[RULER_PATH_SELF_LEFT].SegmentEnd = LeftEnd + (Nor * RULER_OFFSET);

		bool bShowLeft = true;
		if (bLeft && PathProp->GetDirection().Dot(LinkDir) < 0 || !bLeft && PathProp->GetDirection().Dot(LinkDir) > 0)
		{
			bShowLeft = false;
			Nor = -Nor;
		}

		//bool bShowLeft = PropertyRulerDisplayer->RulerMap[RULER_PATH_SELF_RIGHT].GetLength() <= PropertyRulerDisplayer->RulerMap[RULER_PATH_SELF_LEFT].GetLength();

		RulerProperty->RulerMap[RULER_PATH_SELF_RIGHT].bOnlyRead = InTargetModel->IsHasModelFlag(EModelState::E_Dragging);;
		RulerProperty->RulerMap[RULER_PATH_SELF_LEFT].bOnlyRead = InTargetModel->IsHasModelFlag(EModelState::E_Dragging);;

		RulerProperty->RulerMap[RULER_PATH_SELF_RIGHT].bShow = bShowLeft && InTargetModel->bThisRulerShow(RULER_PATH_SELF_RIGHT);
		RulerProperty->RulerMap[RULER_PATH_SELF_LEFT].bShow = !bShowLeft && InTargetModel->bThisRulerShow(RULER_PATH_SELF_LEFT);

		TPair<FVector, FVector> RulerPair = !bShowLeft ?
			                                    TPair<FVector, FVector>(RulerProperty->RulerMap[RULER_PATH_SELF_LEFT].SegmentStart, RulerProperty->RulerMap[RULER_PATH_SELF_LEFT].SegmentEnd) :
			                                    TPair<FVector, FVector>(RulerProperty->RulerMap[RULER_PATH_SELF_RIGHT].SegmentStart, RulerProperty->RulerMap[RULER_PATH_SELF_RIGHT].SegmentEnd);

		auto WallProp = static_cast<FDSHousePathProperty*>(LinkWall->GetProperty());
		auto WallRight = WallProp->GetLeftSegment(); //change to left
		auto Short = WallProp->GetShortSegment();
		auto WallStart = FMath::ClosestPointOnSegment(Short->SegmentStart, WallRight->SegmentStart, WallRight->SegmentEnd);
		auto WallEnd = FMath::ClosestPointOnSegment(Short->SegmentEnd, WallRight->SegmentStart, WallRight->SegmentEnd);


		Points.Sort([&](const FVector& A, const FVector& B) { return FVector::Dist2D(WallEnd, A) < FVector::Dist2D(WallEnd, B); });
		auto PosPoint = Points[0];
		Points.Sort([&](const FVector& A, const FVector& B) { return FVector::Dist2D(WallStart, A) < FVector::Dist2D(WallStart, B); });
		auto NegPoint = Points[0];


		auto DWs = Cast<UDSHouseWallModel>(LinkWall)->GetDoorAndWindow();
		Points.Empty();
		auto MinPos = MAX_dbl;
		auto MinNeg = MAX_dbl;
		auto DWDir = (NegPoint - PosPoint).GetSafeNormal();
		for (auto& Iter : DWs)
		{
			if (!DS_MODEL_VALID_FOR_USE(Iter) || Iter == PathModel)
			{
				continue;
			}
			auto DWProp = static_cast<FDSPathProperty*>(Iter->GetProperty());
			auto Start = DWProp->SegmentStart;
			auto End = DWProp->SegmentEnd;
			TArray<FVector> NewPoints = {DWProp->SegmentStart, DWProp->SegmentEnd};

			for (auto& P : NewPoints)
			{
				auto Dist = FVector::Dist2D(P, PosPoint);
				auto Dir = (P - PosPoint).GetSafeNormal();
				if (DWDir.Dot(Dir) < 0 && Dist < MinPos)
				{
					MinPos = Dist;
					WallEnd = FMath::ClosestPointOnSegment(P, PosPoint - DWDir * 99999, NegPoint + DWDir * 99999);
				}
				if (DWDir.Dot(Dir) > 0 && Dist < MinNeg)
				{
					MinNeg = Dist;
					WallStart = FMath::ClosestPointOnSegment(P, PosPoint - DWDir * 99999, NegPoint + DWDir * 99999);
				}
			}
		}

		auto RulerDir = (RulerPair.Value - RulerPair.Key).GetSafeNormal();

		RulerPair.Value += RulerDir * 99999.f;
		RulerPair.Key -= RulerDir * 99999.f;


		RulerProperty->RulerMap[RULER_ON_WALL_POS].SegmentStart = FMath::ClosestPointOnSegment(PosPoint, RulerPair.Value, RulerPair.Key); //PosPoint + (LinkNor * RULER_OFFSET);
		RulerProperty->RulerMap[RULER_ON_WALL_POS].SegmentEnd = FMath::ClosestPointOnSegment(WallEnd, RulerPair.Value, RulerPair.Key); //WallEnd + (LinkNor * RULER_OFFSET);
		RulerProperty->RulerMap[RULER_ON_WALL_POS].bShow = InTargetModel->bThisRulerShow(RULER_ON_WALL_POS);
		RulerProperty->RulerMap[RULER_ON_WALL_POS].bOnlyRead = InTargetModel->IsHasModelFlag(EModelState::E_Dragging);
		RulerProperty->RulerMap[RULER_ON_WALL_NEG].SegmentStart = FMath::ClosestPointOnSegment(NegPoint, RulerPair.Value, RulerPair.Key); // NegPoint + (LinkNor * RULER_OFFSET);
		RulerProperty->RulerMap[RULER_ON_WALL_NEG].SegmentEnd = FMath::ClosestPointOnSegment(WallStart, RulerPair.Value, RulerPair.Key); // WallStart + (LinkNor * RULER_OFFSET);
		RulerProperty->RulerMap[RULER_ON_WALL_NEG].bShow = InTargetModel->bThisRulerShow(RULER_ON_WALL_NEG);
		RulerProperty->RulerMap[RULER_ON_WALL_NEG].bOnlyRead = InTargetModel->IsHasModelFlag(EModelState::E_Dragging);
	}
}

void UDSRulerLibrary::RefreshRulerProperty_CustomCupboard(FDSRulerDisplayerProperty* RulerProperty,
                                                          UDSBaseModel* InTargetModel)
{
	UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(InTargetModel);

	if (CupboardModel->GetOwnerModel()
		&& (CupboardModel->GetOwnerModel()->GetModelType() == EDSModelType::E_House_Door || CupboardModel->GetOwnerModel()->GetModelType() == EDSModelType::E_House_Window))
	{
		return;
	}

	ADesignStationController* Controller = ADesignStationController::Get();
	if (CupboardModel == nullptr || Controller == nullptr || !UDSMVCSubsystem::IsInitialized())
	{
		return;
	}

	bool bIsComponent = false;
	if (CupboardModel->GetOwnerModel() != nullptr)
	{
		bIsComponent = UDSToolLibrary::IsCustomType(CupboardModel);
	}

	if (bIsComponent && CupboardModel->GetOwnedView() == nullptr)
	{
		return;
	}

	bool bRealWallBoard = UDSWallBoardLibrary::IsRealWallBoard(CupboardModel);  //是否是护墙板


	bool bIsFunctionalCupboardModel = CupboardModel->IsFunctionalCupboardModel();
	bool bIsPartSelectState = UDSMVCSubsystem::GetInstance()->GetState()->IsWholePartSelectState() || UDSMVCSubsystem::GetInstance()->GetState()->IsPartSelectState();
	
	TArray<FVector> CupboardPoints;
	if (bIsComponent)
	{
		CupboardPoints = bRealWallBoard ? CupboardModel->GetModelOrientedBoundingBox() : UDesignStationFunctionLibrary::GetActorOrientedBoundingBox(CupboardModel->GetOwnedView());
	}
	else
	{
		CupboardPoints = CupboardModel->GetModelOrientedBoundingBox();
	}

	RulerProperty->RulerMap[RULER_SELF_WIDTH].bShow = false;
	RulerProperty->RulerMap[RULER_SELF_WIDTH].bOnlyRead = bIsPartSelectState;
	RulerProperty->RulerMap[RULER_SELF_WIDTH].SegmentStart = CupboardPoints[0];
	RulerProperty->RulerMap[RULER_SELF_WIDTH].SegmentEnd = CupboardPoints[1];

	RulerProperty->RulerMap[RULER_SELF_DEPTH].bShow = false;
	RulerProperty->RulerMap[RULER_SELF_DEPTH].bOnlyRead = bIsPartSelectState;
	RulerProperty->RulerMap[RULER_SELF_DEPTH].SegmentStart = CupboardPoints[0];
	RulerProperty->RulerMap[RULER_SELF_DEPTH].SegmentEnd = CupboardPoints[3];

	TSharedPtr<FDSBaseProperty> CupboardProperty = CupboardModel->GetPropertySharedPtr();
	if (!CupboardProperty)
	{
		return;
	}

	bool bTransform = CupboardModel->IsHasModelFlag(EModelState::E_Transform);

	RulerProperty->RulerMap[RULER_ITEM_LEFT].bOnlyRead = CupboardProperty->GetIsLock() || bTransform || bIsPartSelectState && bIsComponent;
	RulerProperty->RulerMap[RULER_ITEM_FRONT].bOnlyRead = CupboardProperty->GetIsLock() || bTransform || bIsPartSelectState && bIsComponent;
	RulerProperty->RulerMap[RULER_ITEM_RIGHT].bOnlyRead = CupboardProperty->GetIsLock() || bTransform || bIsPartSelectState && bIsComponent;
	RulerProperty->RulerMap[RULER_ITEM_BACK].bOnlyRead = CupboardProperty->GetIsLock() || bTransform || bIsPartSelectState && bIsComponent;
	RulerProperty->RulerMap[RULER_ITEM_UP].bOnlyRead = CupboardProperty->GetIsLock() || bTransform || bIsPartSelectState && bIsComponent;
	RulerProperty->RulerMap[RULER_ITEM_DOWN].bOnlyRead = CupboardProperty->GetIsLock() || bTransform || bIsPartSelectState && bIsComponent;


	if (bIsFunctionalCupboardModel || CupboardModel->GetOwnerModel() != nullptr)
	{
		RulerProperty->RulerMap[RULER_WALL_LEFT].bShow = false;
		RulerProperty->RulerMap[RULER_WALL_FRONT].bShow = false;
		RulerProperty->RulerMap[RULER_WALL_RIGHT].bShow = false;
		RulerProperty->RulerMap[RULER_WALL_BACK].bShow = false;
	}
	else
	{
		RulerProperty->RulerMap[RULER_WALL_LEFT].bOnlyRead = CupboardProperty->GetIsLock() || bTransform || bIsPartSelectState && bIsComponent;
		RulerProperty->RulerMap[RULER_WALL_FRONT].bOnlyRead = CupboardProperty->GetIsLock() || bTransform || bIsPartSelectState && bIsComponent;
		RulerProperty->RulerMap[RULER_WALL_RIGHT].bOnlyRead = CupboardProperty->GetIsLock() || bTransform || bIsPartSelectState && bIsComponent;
		RulerProperty->RulerMap[RULER_WALL_BACK].bOnlyRead = CupboardProperty->GetIsLock() || bTransform || bIsPartSelectState && bIsComponent;
	}


	// Do we need to use the middle point of line?
	float FractionalValue = FMath::Abs(FMath::Fractional(CupboardProperty->TransformProperty.Rotation.Yaw / 90.0f));
	bool bUseMiddlePoints = FMath::IsNearlyZero(FractionalValue, 1.f / 90.f) || FMath::IsNearlyZero(1.f - FractionalValue, 1.f / 90.f);

	if (bUseMiddlePoints)
	{
		TArray<FVector> Points;
		for (size_t i = 0; i < 4; i++)
		{
			FVector MiddlePoint = (CupboardPoints[i] + CupboardPoints[i + 1]) * 0.5f;
			MiddlePoint.Z = (CupboardPoints[0].Z + CupboardPoints[i + 4].Z) * 0.5f;
			Points.Add(MiddlePoint);
		}

		RulerProperty->RulerMap[RULER_ITEM_LEFT].SegmentStart = Points[0];

		RulerProperty->RulerMap[RULER_ITEM_FRONT].SegmentStart = Points[1];

		RulerProperty->RulerMap[RULER_ITEM_RIGHT].SegmentStart = Points[2];

		RulerProperty->RulerMap[RULER_ITEM_BACK].SegmentStart = Points[3];


		for (int32 Index = 0; Index < 4; Index++)
		{
			if (RulerProperty->RulerMap[RULER_ITEM_LEFT].SegmentStart.Y > Points[Index].Y)
			{
				RulerProperty->RulerMap[RULER_ITEM_LEFT].SegmentStart = Points[Index];
			}

			if (RulerProperty->RulerMap[RULER_ITEM_FRONT].SegmentStart.X < Points[Index].X)
			{
				RulerProperty->RulerMap[RULER_ITEM_FRONT].SegmentStart = Points[Index];
			}

			if (RulerProperty->RulerMap[RULER_ITEM_RIGHT].SegmentStart.Y < Points[Index].Y)
			{
				RulerProperty->RulerMap[RULER_ITEM_RIGHT].SegmentStart = Points[Index];
			}

			if (RulerProperty->RulerMap[RULER_ITEM_BACK].SegmentStart.X > Points[Index].X)
			{
				RulerProperty->RulerMap[RULER_ITEM_BACK].SegmentStart = Points[Index];
			}
		}
	}
	else
	{
		RulerProperty->RulerMap[RULER_ITEM_LEFT].SegmentStart = CupboardPoints[0];
		RulerProperty->RulerMap[RULER_ITEM_FRONT].SegmentStart = CupboardPoints[1];
		RulerProperty->RulerMap[RULER_ITEM_RIGHT].SegmentStart = CupboardPoints[2];
		RulerProperty->RulerMap[RULER_ITEM_BACK].SegmentStart = CupboardPoints[3];

		for (int32 Index = 0; Index < 4; Index++)
		{
			if (RulerProperty->RulerMap[RULER_ITEM_LEFT].SegmentStart.Y > CupboardPoints[Index].Y)
			{
				RulerProperty->RulerMap[RULER_ITEM_LEFT].SegmentStart = CupboardPoints[Index];
			}

			if (RulerProperty->RulerMap[RULER_ITEM_FRONT].SegmentStart.X < CupboardPoints[Index].X)
			{
				RulerProperty->RulerMap[RULER_ITEM_FRONT].SegmentStart = CupboardPoints[Index];
			}

			if (RulerProperty->RulerMap[RULER_ITEM_RIGHT].SegmentStart.Y < CupboardPoints[Index].Y)
			{
				RulerProperty->RulerMap[RULER_ITEM_RIGHT].SegmentStart = CupboardPoints[Index];
			}

			if (RulerProperty->RulerMap[RULER_ITEM_BACK].SegmentStart.X > CupboardPoints[Index].X)
			{
				RulerProperty->RulerMap[RULER_ITEM_BACK].SegmentStart = CupboardPoints[Index];
			}
		}
	}

	RulerProperty->RulerMap[RULER_ITEM_DOWN].SegmentStart = (CupboardPoints[4] + CupboardPoints[6]) * 0.5f;
	RulerProperty->RulerMap[RULER_ITEM_UP].SegmentStart = (CupboardPoints[0] + CupboardPoints[2]) * 0.5f;

	RulerProperty->RulerMap[RULER_WALL_LEFT].SegmentStart = RulerProperty->RulerMap[RULER_ITEM_LEFT].SegmentStart;
	RulerProperty->RulerMap[RULER_WALL_FRONT].SegmentStart = RulerProperty->RulerMap[RULER_ITEM_FRONT].SegmentStart;
	RulerProperty->RulerMap[RULER_WALL_RIGHT].SegmentStart = RulerProperty->RulerMap[RULER_ITEM_RIGHT].SegmentStart;
	RulerProperty->RulerMap[RULER_WALL_BACK].SegmentStart = RulerProperty->RulerMap[RULER_ITEM_BACK].SegmentStart;

	UE::Geometry::TPolygon2<double> CupboardPolygon;
	CupboardPolygon.AppendVertex({RulerProperty->RulerMap[RULER_ITEM_LEFT].SegmentStart.X, RulerProperty->RulerMap[RULER_ITEM_LEFT].SegmentStart.Y});
	CupboardPolygon.AppendVertex({RulerProperty->RulerMap[RULER_ITEM_FRONT].SegmentStart.X, RulerProperty->RulerMap[RULER_ITEM_FRONT].SegmentStart.Y});
	CupboardPolygon.AppendVertex({RulerProperty->RulerMap[RULER_ITEM_RIGHT].SegmentStart.X, RulerProperty->RulerMap[RULER_ITEM_RIGHT].SegmentStart.Y});
	CupboardPolygon.AppendVertex({RulerProperty->RulerMap[RULER_ITEM_BACK].SegmentStart.X, RulerProperty->RulerMap[RULER_ITEM_BACK].SegmentStart.Y});

	//功能件不计算到墙的标尺
	if(!bIsFunctionalCupboardModel && CupboardModel->GetOwnerModel() == nullptr)
	{

		TArray<UDSBaseModel*> PathModels = UDSMVCSubsystem::GetInstance()->GetModels({ EDSModelType::E_House_Wall, EDSModelType::E_House_Beam, EDSModelType::E_House_Platform });

		TMap<FString, FVector> WallTags = {
			{RULER_WALL_LEFT, FVector::LeftVector},
			{RULER_WALL_FRONT, FVector::ForwardVector},
			{RULER_WALL_RIGHT, FVector::RightVector},
			{RULER_WALL_BACK, FVector::BackwardVector}
		};

		// Calculate the end points of ruler for path.
		for (UDSBaseModel* Model : PathModels)
		{
			if (Model == nullptr)
			{
				continue;
			}

			TArray<FVector> Outlines = Model->GetBottomOutline();

			UE::Geometry::TPolygon2<double> WallPolygon;
			for (int32 Index = 0; Index < Outlines.Num(); Index++)
			{
				WallPolygon.AppendVertex({ Outlines[Index].X, Outlines[Index].Y });
			}

			if (Controller->Is2DScene())
			{
				if (WallPolygon.Contains(CupboardPolygon) || CupboardPolygon.Contains(WallPolygon) || CupboardPolygon.Intersects(WallPolygon))
				{
					continue;
				}
			}
			else
			{
				TSharedPtr<FDSHousePathProperty> PathProperty = StaticCastSharedPtr<FDSHousePathProperty>(Model->GetPropertySharedPtr());
				if (!PathProperty)
				{
					continue;
				}

				double SegmentStartPointZ = RulerProperty->RulerMap[RULER_ITEM_LEFT].SegmentStart.Z;
				double ModelHeightZ = Outlines[0].Z + PathProperty->GetHeight();
				if (SegmentStartPointZ > ModelHeightZ || SegmentStartPointZ < Outlines[0].Z)
				{
					continue;
				}
			}

			for (UE::Geometry::TSegment2<double> Segment : WallPolygon.Segments())
			{
				FVector IntersectionPoint;
				for (const TPair<FString, FVector>& Pair : WallTags)
				{
					FVector2D StartPoint2D(RulerProperty->RulerMap[Pair.Key].SegmentStart.X, RulerProperty->RulerMap[Pair.Key].SegmentStart.Y);
					FVector2D ClosestPoint = FMath::ClosestPointOnSegment2D(StartPoint2D, Segment.StartPoint(), Segment.EndPoint());
					if (ClosestPoint.Equals(StartPoint2D,0.1f))
					{
						RulerProperty->RulerMap[Pair.Key].bShow = false;
						continue;
					}


					if (FMath::SegmentIntersection2D(RulerProperty->RulerMap[Pair.Key].SegmentStart,
						RulerProperty->RulerMap[Pair.Key].SegmentStart + (Pair.Value * 1000000.0f),
						FVector(Segment.StartPoint(), RulerProperty->RulerMap[Pair.Key].SegmentStart.Z),
						FVector(Segment.EndPoint(), RulerProperty->RulerMap[Pair.Key].SegmentStart.Z),
						IntersectionPoint))
					{
						if (!RulerProperty->RulerMap[Pair.Key].bShow)
						{
							RulerProperty->RulerMap[Pair.Key].bShow = true;
							RulerProperty->RulerMap[Pair.Key].SegmentEnd = IntersectionPoint;
						}
						else
						{
							float PreDist = FVector::Distance(RulerProperty->RulerMap[Pair.Key].SegmentStart, RulerProperty->RulerMap[Pair.Key].SegmentEnd);
							float CurrentDist = FVector::Distance(RulerProperty->RulerMap[Pair.Key].SegmentStart, IntersectionPoint);
							if (CurrentDist < PreDist)
							{
								RulerProperty->RulerMap[Pair.Key].SegmentEnd = IntersectionPoint;
							}
						}
					}
				}
			}
		}

	}

	TArray<UDSBaseModel*> ItemModels;
	TArray<FBoxSphereBounds> ViewBoxs;
	
	if (bIsFunctionalCupboardModel)
	{
		//功能件2D下没有标尺
		if (Controller->Is2DScene())
		{
			return;
		}
	
		auto AdaptationOperator = Cast<UDSCupboardModel>(InTargetModel)->GetAdaptationOperator();
		if (!AdaptationOperator.IsValid())
		{
			AdaptationOperator = Cast<UDSCupboardModel>(InTargetModel)->CreateAdaptationOperator();
			AdaptationOperator->PrepareAdaptation(InTargetModel);
			if (TSharedPtr<FDrawerAdaptationOperator> DrawerOp = StaticCastSharedPtr<FDrawerAdaptationOperator>(AdaptationOperator))
			{//抽屉
				DrawerOp->GetAdaptationEvenInfo();
			}
		}
		if (AdaptationOperator.IsValid())
		{
			//AdaptationOperator->PrepareAdaptation(InTargetModel);
			TSharedPtr<FFunctionalAdaptationOperator> FunctionOperator = StaticCastSharedPtr<FFunctionalAdaptationOperator>(AdaptationOperator);
			if (FunctionOperator.IsValid())
			{
				FunctionOperator->GetIntersectionSeg([&](const FVector& Start, const FVector& End, bool bExist) {
					if (!bExist || Start.Equals(End, 0.1f))
					{
						RulerProperty->RulerMap[RULER_ITEM_LEFT].bShow = false;
					}
					else
					{
						RulerProperty->RulerMap[RULER_ITEM_LEFT].bShow = true;
						RulerProperty->RulerMap[RULER_ITEM_LEFT].SegmentStart = Start;
						RulerProperty->RulerMap[RULER_ITEM_LEFT].SegmentEnd = End;
					}
					}, FVector::BackwardVector);

				FunctionOperator->GetIntersectionSeg([&](const FVector& Start, const FVector& End, bool bExist) {
					if (!bExist || Start.Equals(End, 0.1f))
					{
						RulerProperty->RulerMap[RULER_ITEM_RIGHT].bShow = false;
					}
					else
					{
						RulerProperty->RulerMap[RULER_ITEM_RIGHT].bShow = true;
						RulerProperty->RulerMap[RULER_ITEM_RIGHT].SegmentStart = Start;
						RulerProperty->RulerMap[RULER_ITEM_RIGHT].SegmentEnd = End;
					}
					}, FVector::ForwardVector);

				FunctionOperator->GetIntersectionSeg([&](const FVector& Start, const FVector& End, bool bExist) {
					if (!bExist || Start.Equals(End, 0.1f))
					{
						RulerProperty->RulerMap[RULER_ITEM_UP].bShow = false;
					}
					else
					{
						RulerProperty->RulerMap[RULER_ITEM_UP].bShow = true;
						RulerProperty->RulerMap[RULER_ITEM_UP].SegmentStart = Start;
						RulerProperty->RulerMap[RULER_ITEM_UP].SegmentEnd = End;
					}
					}, FVector::UpVector);

				FunctionOperator->GetIntersectionSeg([&](const FVector& Start, const FVector& End, bool bExist) {
					if (!bExist || Start.Equals(End, 0.1f))
					{
						RulerProperty->RulerMap[RULER_ITEM_DOWN].bShow = false;
					}
					else
					{
						RulerProperty->RulerMap[RULER_ITEM_DOWN].bShow = true;
						RulerProperty->RulerMap[RULER_ITEM_DOWN].SegmentStart = Start;
						RulerProperty->RulerMap[RULER_ITEM_DOWN].SegmentEnd = End;
					}
					}, FVector::DownVector);


			}
		}
	}
	else
	{
		if (bIsPartSelectState)
		{
			UDSCupboardModel* RootCupboardModel = CupboardModel->GetRootCupboardModel();
			ItemModels.Append(UDSMVCSubsystem::GetInstance()->GetAllCustomModels().FilterByPredicate([CupboardModel, RootCupboardModel](UDSBaseModel* InCustomModel)
			{
				return !UDSToolLibrary::ModelIsSelf(InCustomModel) && InCustomModel != CupboardModel && Cast<UDSCupboardModel>(InCustomModel)->GetRootCupboardModel() == RootCupboardModel;
			}));
		}
		else
		{
			ItemModels = UDSMVCSubsystem::GetInstance()->GetModels({ EDSModelType::E_House_Roof, EDSModelType::E_House_Area, EDSModelType::E_House_Pillar, EDSModelType::E_Furniture_HouseFurniture });

			TArray<UDSBaseModel*> CupboardModels = UDSMVCSubsystem::GetInstance()->GetModels({
				EDSModelType::E_Custom_UpperCabinet ,
				EDSModelType::E_Custom_WallCabinet, // 吊柜
				EDSModelType::E_Custom_BaseCabinet, // 地柜
				EDSModelType::E_Custom_TallCabinet, // 高柜
				EDSModelType::E_Custom_CornerCabinet, // 转角柜
				EDSModelType::E_Custom_Tatami,
				EDSModelType::E_Custom_WallBoardCabinet,
				EDSModelType::E_Custom_LayoutDoor,
				EDSModelType::E_Custom_CornerCutCabinet, // 切角柜
			});

			for (UDSBaseModel* Model : CupboardModels)
			{
				if (Model == nullptr || Model == CupboardModel)
				{
					continue;
				}

				if (Model->GetOwnerModel() == nullptr || !Model->GetOwnerModel()->IsA<UDSCupboardModel>())
				{
					ItemModels.Add(Model);
				}
			}
		}

		for (UDSBaseModel* Model : ItemModels)
		{
			if (Model == nullptr || Model->GetOwnedView() == nullptr)
			{
				continue;
			}

			if (Controller->Is2DScene())
			{
				if (Model->GetModelType() == EDSModelType::E_House_Area || Model->GetModelType() == EDSModelType::E_House_Roof)
				{
					continue;
				}
			}
			FBoxSphereBounds ViewBox(ForceInitToZero);

			if (UDSToolLibrary::IsCustomType(Model) && bIsPartSelectState)
			{
				TArray<UPrimitiveComponent*> ViewComponents;
				Model->GetOwnedView()->GetComponents<UPrimitiveComponent>(ViewComponents);

				TArray<AActor*> AttachedActors;
				Model->GetOwnedView()->GetAttachedActors(AttachedActors);
				for (AActor* Actor : AttachedActors)
				{
					if (Actor == nullptr || Actor->IsA<ADSCupboardBaseView>())
					{
						continue;
					}

					TArray<UPrimitiveComponent*> ActorComponents;
					Actor->GetComponents<UPrimitiveComponent>(ActorComponents);

					ViewComponents.Append(MoveTemp(ActorComponents));
				}

				for (UPrimitiveComponent* Component : ViewComponents)
				{
					if (!Component->IsVisible() || !Component->IsCollisionEnabled())
					{
						continue;
					}

					FBoxSphereBounds ComponentBounds = Component->GetLocalBounds();
					ViewBox = ViewBox + ComponentBounds.TransformBy(Component->GetRelativeTransform());
				}

				ViewBox = ViewBox.TransformBy(Model->GetOwnedView()->GetActorTransform());
			}
			else
			{
				if(Model->GetModelType() == EDSModelType::E_House_Roof)
				{
					TSharedPtr<FDSHouseRoofProperty> PathProperty = StaticCastSharedPtr<FDSHouseRoofProperty>(Model->GetPropertySharedPtr());
					if (!PathProperty)
					{
						continue;
					}
					TArray<FVector> OutPoints;
					Model->GetModelOuterRect(OutPoints);
					if (!OutPoints.IsValidIndex(3))
					{
						continue;
					}
					ViewBox.BoxExtent =((OutPoints[2] - OutPoints[0]) * 0.5f).GetAbs();
					ViewBox.Origin = (OutPoints[2] + OutPoints[0]) * 0.5f;
					ViewBox.Origin.Z = PathProperty->DistanceToFloor;
				}
				else if (Model->GetModelType() == EDSModelType::E_House_Area)
				{
					TSharedPtr<FDSHouseAreaProperty> AreaProperty = Model->GetTypedProperty<FDSHouseAreaProperty>();
					ViewBox = FBox(AreaProperty->Points);
				}
				else
				{
					//ViewBox = UDesignStationFunctionLibrary::GetActorAxisAlignedBoundingBox_Recursively(Model->GetOwnedView());
					TArray<FVector> TempPoints = Model->GetModelOrientedBoundingBox();
					FBox TempBox(TempPoints);
					ViewBox.BoxExtent = TempBox.GetExtent();
					ViewBox.Origin = TempBox.GetCenter();
				}
			}
			ViewBoxs.Add(ViewBox);
		}
	}
	

	TMap<FString, FVector> ItemTags = {
		{RULER_ITEM_LEFT, FVector::LeftVector},
		{RULER_ITEM_FRONT, FVector::ForwardVector},
		{RULER_ITEM_RIGHT, FVector::RightVector},
		{RULER_ITEM_BACK, FVector::BackwardVector}
	};

	TMap<FString, FVector> TargetMeshDirectionMap = {
		{RULER_ITEM_LEFT, FVector::RightVector},
		{RULER_ITEM_FRONT, FVector::BackwardVector},
		{RULER_ITEM_RIGHT, FVector::LeftVector},
		{RULER_ITEM_BACK, FVector::ForwardVector}
	};

	for (const auto&  ViewBox : ViewBoxs)
	{

		if (Controller->Is2DScene())
		{
			UE::Geometry::TPolygon2<double> CurrentViewPolygon;
			CurrentViewPolygon.AppendVertex({ViewBox.Origin.X - ViewBox.BoxExtent.X, ViewBox.Origin.Y - ViewBox.BoxExtent.Y});
			CurrentViewPolygon.AppendVertex({ViewBox.Origin.X + ViewBox.BoxExtent.X, ViewBox.Origin.Y - ViewBox.BoxExtent.Y});
			CurrentViewPolygon.AppendVertex({ViewBox.Origin.X + ViewBox.BoxExtent.X, ViewBox.Origin.Y + ViewBox.BoxExtent.Y});
			CurrentViewPolygon.AppendVertex({ViewBox.Origin.X - ViewBox.BoxExtent.X, ViewBox.Origin.Y + ViewBox.BoxExtent.Y});

			// Check if two polygons intersected or contained.
			bool bViewIntersected = CupboardPolygon.Contains(CurrentViewPolygon) || CurrentViewPolygon.Contains(CupboardPolygon) || CupboardPolygon.Intersects(CurrentViewPolygon);
			if (bViewIntersected)
			{
				continue;
			}

			// LEFT - FRONT - RIGHT - BACK.
			for (UE::Geometry::TSegment2<double> Segment : CurrentViewPolygon.Segments())
			{
				FVector IntersectionPoint;
				for (const TPair<FString, FVector>& Pair : ItemTags)
				{
					if (FMath::SegmentIntersection2D(RulerProperty->RulerMap[Pair.Key].SegmentStart,
					                                 RulerProperty->RulerMap[Pair.Key].SegmentStart + (Pair.Value * 1000000.0f),
					                                 FVector(Segment.StartPoint(), RulerProperty->RulerMap[Pair.Key].SegmentStart.Z),
					                                 FVector(Segment.EndPoint(), RulerProperty->RulerMap[Pair.Key].SegmentStart.Z),
					                                 IntersectionPoint))
					{
						if (!RulerProperty->RulerMap[Pair.Key].bShow)
						{
							RulerProperty->RulerMap[Pair.Key].bShow = true;
							RulerProperty->RulerMap[Pair.Key].SegmentEnd = IntersectionPoint;
						}
						else
						{
							float PreDist = FVector::Distance(RulerProperty->RulerMap[Pair.Key].SegmentStart, RulerProperty->RulerMap[Pair.Key].SegmentEnd);
							float CurrentDist = FVector::Distance(RulerProperty->RulerMap[Pair.Key].SegmentStart, IntersectionPoint);
							if (CurrentDist < PreDist)
							{
								RulerProperty->RulerMap[Pair.Key].SegmentEnd = IntersectionPoint;
							}
						}
					}
				}
			}
		}
		else
		{
			TArray<FVector> RectangularMesh;
			FVector IntersectionPoint;
			if (!FMath::IsNearlyZero(ViewBox.BoxExtent.Z, 0.01f))
			{
				// LEFT - FRONT - RIGHT - BACK
				for (const TPair<FString, FVector>& Pair : ItemTags)
				{
					const FVector& StartPoint = RulerProperty->RulerMap[Pair.Key].SegmentStart;
					RectangularMesh = UDesignStationFunctionLibrary::GetRectangularMeshFromBox(ViewBox, TargetMeshDirectionMap[Pair.Key]);
					if (UDesignStationFunctionLibrary::RayTriangleIntersection(StartPoint, Pair.Value, RectangularMesh[0], RectangularMesh[1], RectangularMesh[2], IntersectionPoint)
						|| UDesignStationFunctionLibrary::RayTriangleIntersection(StartPoint, Pair.Value, RectangularMesh[2], RectangularMesh[3], RectangularMesh[0], IntersectionPoint))
					{
						if (!RulerProperty->RulerMap[Pair.Key].bShow)
						{
							RulerProperty->RulerMap[Pair.Key].bShow = true;
							RulerProperty->RulerMap[Pair.Key].SegmentEnd = IntersectionPoint;
						}
						else
						{
							float PreDist = FVector::Distance(StartPoint, RulerProperty->RulerMap[Pair.Key].SegmentEnd);
							float CurrentDist = FVector::Distance(StartPoint, IntersectionPoint);
							if (PreDist > CurrentDist)
							{
								RulerProperty->RulerMap[Pair.Key].SegmentEnd = IntersectionPoint;
							}
						}
					}
				}
			}
			// UP
			RectangularMesh = UDesignStationFunctionLibrary::GetRectangularMeshFromBox(ViewBox, FVector::DownVector);


			if (!RectangularMesh.IsEmpty() && (UDesignStationFunctionLibrary::RayTriangleIntersection(RulerProperty->RulerMap[RULER_ITEM_UP].SegmentStart, FVector::UpVector, RectangularMesh[0],
			                                                                                          RectangularMesh[1], RectangularMesh[2], IntersectionPoint)
				|| UDesignStationFunctionLibrary::RayTriangleIntersection(RulerProperty->RulerMap[RULER_ITEM_UP].SegmentStart, FVector::UpVector, RectangularMesh[2], RectangularMesh[3],
				                                                          RectangularMesh[0], IntersectionPoint)))
			{
				if (!RulerProperty->RulerMap[RULER_ITEM_UP].bShow)
				{
					RulerProperty->RulerMap[RULER_ITEM_UP].bShow = true;
					RulerProperty->RulerMap[RULER_ITEM_UP].SegmentEnd = IntersectionPoint;
				}
				else
				{
					float PreDist = FVector::Distance(RulerProperty->RulerMap[RULER_ITEM_UP].SegmentStart, RulerProperty->RulerMap[RULER_ITEM_UP].SegmentEnd);
					float CurrentDist = FVector::Distance(RulerProperty->RulerMap[RULER_ITEM_UP].SegmentStart, IntersectionPoint);
					if (PreDist > CurrentDist)
					{
						RulerProperty->RulerMap[RULER_ITEM_UP].SegmentEnd = IntersectionPoint;
					}
				}
			}


			// Down
			RectangularMesh = UDesignStationFunctionLibrary::GetRectangularMeshFromBox(ViewBox, FVector::UpVector);

			if (!RectangularMesh.IsEmpty() && (UDesignStationFunctionLibrary::RayTriangleIntersection(RulerProperty->RulerMap[RULER_ITEM_DOWN].SegmentStart, FVector::DownVector, RectangularMesh[0],
			                                                                                          RectangularMesh[2], RectangularMesh[1], IntersectionPoint)
				|| UDesignStationFunctionLibrary::RayTriangleIntersection(RulerProperty->RulerMap[RULER_ITEM_DOWN].SegmentStart, FVector::DownVector, RectangularMesh[0], RectangularMesh[2],
				                                                          RectangularMesh[3], IntersectionPoint)))
			{
				if (!RulerProperty->RulerMap[RULER_ITEM_DOWN].bShow)
				{
					RulerProperty->RulerMap[RULER_ITEM_DOWN].bShow = true;
					RulerProperty->RulerMap[RULER_ITEM_DOWN].SegmentEnd = IntersectionPoint;
				}
				else
				{
					float PreDist = FVector::Distance(RulerProperty->RulerMap[RULER_ITEM_DOWN].SegmentStart, RulerProperty->RulerMap[RULER_ITEM_DOWN].SegmentEnd);
					float CurrentDist = FVector::Distance(RulerProperty->RulerMap[RULER_ITEM_DOWN].SegmentStart, IntersectionPoint);
					if (PreDist > CurrentDist)
					{
						RulerProperty->RulerMap[RULER_ITEM_DOWN].SegmentEnd = IntersectionPoint;
					}
				}
			}
		}
	}

	if (Controller->Is2DScene())
	{
		//2D下展示最近的标尺
		//FRONT
		if (RulerProperty->RulerMap[RULER_ITEM_FRONT].bShow && RulerProperty->RulerMap[RULER_WALL_FRONT].bShow)
		{
			auto DistItem = FVector::Distance(RulerProperty->RulerMap[RULER_ITEM_FRONT].SegmentStart, RulerProperty->RulerMap[RULER_ITEM_FRONT].SegmentEnd);
			auto DistWall = FVector::Distance(RulerProperty->RulerMap[RULER_WALL_FRONT].SegmentStart, RulerProperty->RulerMap[RULER_WALL_FRONT].SegmentEnd);
			if (DistItem < DistWall)
			{
				RulerProperty->RulerMap[RULER_WALL_FRONT].bShow = false;
			}
			else
			{
				RulerProperty->RulerMap[RULER_ITEM_FRONT].bShow = false;
			}
		}
		//BACK
		if (RulerProperty->RulerMap[RULER_ITEM_BACK].bShow && RulerProperty->RulerMap[RULER_WALL_BACK].bShow)
		{
			auto DistItem = FVector::Distance(RulerProperty->RulerMap[RULER_ITEM_BACK].SegmentStart, RulerProperty->RulerMap[RULER_ITEM_BACK].SegmentEnd);
			auto DistWall = FVector::Distance(RulerProperty->RulerMap[RULER_WALL_BACK].SegmentStart, RulerProperty->RulerMap[RULER_WALL_BACK].SegmentEnd);
			if (DistItem < DistWall)
			{
				RulerProperty->RulerMap[RULER_WALL_BACK].bShow = false;
			}
			else
			{
				RulerProperty->RulerMap[RULER_ITEM_BACK].bShow = false;
			}
		}
		//LEFT
		if (RulerProperty->RulerMap[RULER_ITEM_LEFT].bShow && RulerProperty->RulerMap[RULER_WALL_LEFT].bShow)
		{
			auto DistItem = FVector::Distance(RulerProperty->RulerMap[RULER_ITEM_LEFT].SegmentStart, RulerProperty->RulerMap[RULER_ITEM_LEFT].SegmentEnd);
			auto DistWall = FVector::Distance(RulerProperty->RulerMap[RULER_WALL_LEFT].SegmentStart, RulerProperty->RulerMap[RULER_WALL_LEFT].SegmentEnd);
			if (DistItem < DistWall)
			{
				RulerProperty->RulerMap[RULER_WALL_LEFT].bShow = false;
			}
			else
			{
				RulerProperty->RulerMap[RULER_ITEM_LEFT].bShow = false;
			}
		}
		//RIGHT
		if (RulerProperty->RulerMap[RULER_ITEM_RIGHT].bShow && RulerProperty->RulerMap[RULER_WALL_RIGHT].bShow)
		{
			auto DistItem = FVector::Distance(RulerProperty->RulerMap[RULER_ITEM_RIGHT].SegmentStart, RulerProperty->RulerMap[RULER_ITEM_RIGHT].SegmentEnd);
			auto DistWall = FVector::Distance(RulerProperty->RulerMap[RULER_WALL_RIGHT].SegmentStart, RulerProperty->RulerMap[RULER_WALL_RIGHT].SegmentEnd);
			if (DistItem < DistWall)
			{
				RulerProperty->RulerMap[RULER_WALL_RIGHT].bShow = false;
			}
			else
			{
				RulerProperty->RulerMap[RULER_ITEM_RIGHT].bShow = false;
			}
		}
	}
}

TMap<TWeakObjectPtr<UDSBaseModel>, FString> UDSRulerLibrary::RefreshRulerProperty_CounterTop(FDSRulerDisplayerProperty* RulerProperty, UDSBaseModel* InTargetModel)
{
	TMap<TWeakObjectPtr<UDSBaseModel>, FString> LineRulerMap;

	int32 CurrentRulerIndex = 0;
	TArray<FString> AllRulerNames = CollectAllRulerNames();

	TArray<TWeakObjectPtr<UDSBaseModel>> LinkModels = InTargetModel->GetLinkModels().Array();

	for (TWeakObjectPtr<UDSBaseModel> LinkModel : LinkModels)
	{
		if (!AllRulerNames.IsValidIndex(CurrentRulerIndex))
		{
			break;
		}

		if (!LinkModel.IsValid() || LinkModel->GetModelType() != EDSModelType::E_Generated_CounterTop_Line)
		{
			continue;
		}

		const FString RulerName = AllRulerNames[CurrentRulerIndex];
		FDSRulerProperty& CurrentRuler = RulerProperty->RulerMap[RulerName];

		TSharedPtr<FDSCounterTopLineProperty> LineProperty = LinkModel->GetTypedProperty<FDSCounterTopLineProperty>();
		FVector Offset = LineProperty->LineRightDirection * RULER_OFFSET;

		CurrentRuler.SegmentStart = LineProperty->StartPoint.Position + Offset;
		CurrentRuler.SegmentEnd = LineProperty->EndPoint.Position + Offset;
		CurrentRuler.bShow = true;
		CurrentRuler.bOnlyRead = true;

		LineRulerMap.Add(LinkModel, RulerName);

		++CurrentRulerIndex;
	}

	return LineRulerMap;
}

void UDSRulerLibrary::RefreshRulerProperty_CounterTopLine(FDSRulerDisplayerProperty* RulerProperty, UDSBaseModel* InTargetModel)
{
	TArray<TWeakObjectPtr<UDSBaseModel>> LinkModels = InTargetModel->GetLinkModels().Array();
	if (LinkModels.Num() != 1 || !LinkModels[0].IsValid() || LinkModels[0]->GetModelType() != EDSModelType::E_Generated_CounterTop)
	{
		return;
	}

	TSharedPtr<FDSCounterTopLineProperty> TargetProperty = InTargetModel->GetTypedProperty<FDSCounterTopLineProperty>();

	TMap<TWeakObjectPtr<UDSBaseModel>, FString> LineRulerMap = RefreshRulerProperty_CounterTop(RulerProperty, LinkModels[0].Get());

	if (!InTargetModel->IsSelected())
	{
		return;
	}

	for (const TPair<TWeakObjectPtr<UDSBaseModel>, FString>& Pair : LineRulerMap)
	{
		if (Pair.Key->GetUUID() == InTargetModel->GetUUID())
		{
			continue;
		}

		TSharedPtr<FDSCounterTopLineProperty> LineProperty = Pair.Key->GetTypedProperty<FDSCounterTopLineProperty>();
		if (TargetProperty->PointIndex.X == LineProperty->PointIndex.X || TargetProperty->PointIndex.X == LineProperty->PointIndex.Y ||
			TargetProperty->PointIndex.Y == LineProperty->PointIndex.X || TargetProperty->PointIndex.Y == LineProperty->PointIndex.Y)
		{
			RulerProperty->RulerMap[Pair.Value].bOnlyRead = false;
			RulerProperty->RulerMap[Pair.Value].LimitRange.X = 1;
		}
	}
}

void UDSRulerLibrary::RefreshRulerProperty_CounterTopPoint(FDSRulerDisplayerProperty* RulerProperty, UDSBaseModel* InTargetModel)
{
	TArray<TWeakObjectPtr<UDSBaseModel>> LinkModels = InTargetModel->GetLinkModels().Array();
	if (LinkModels.Num() != 1 || !LinkModels[0].IsValid() || LinkModels[0]->GetModelType() != EDSModelType::E_Generated_CounterTop)
	{
		return;
	}

	TSharedPtr<FDSCounterTopPointProperty> TargetProperty = InTargetModel->GetTypedProperty<FDSCounterTopPointProperty>();

	TMap<TWeakObjectPtr<UDSBaseModel>, FString> LineRulerMap = RefreshRulerProperty_CounterTop(RulerProperty, LinkModels[0].Get());

	if (!InTargetModel->IsSelected())
	{
		return;
	}

	for (const TPair<TWeakObjectPtr<UDSBaseModel>, FString>& Pair : LineRulerMap)
	{
		if (Pair.Key->GetUUID() == InTargetModel->GetUUID())
		{
			continue;
		}

		TSharedPtr<FDSCounterTopLineProperty> LineProperty = Pair.Key->GetTypedProperty<FDSCounterTopLineProperty>();
		if (TargetProperty->PointIndex == LineProperty->PointIndex.X || TargetProperty->PointIndex == LineProperty->PointIndex.Y)
		{
			RulerProperty->RulerMap[Pair.Value].bOnlyRead = false;
		}
	}
}

void UDSRulerLibrary::RefreshRulerProperty_Sink(FDSRulerDisplayerProperty* RulerProperty, UDSBaseModel* InTargetModel)
{
	TSharedPtr<FDSSinkProperty> TargetProperty = InTargetModel->GetTypedProperty<FDSSinkProperty>();
	auto CupboardUUID = UDSModelDependencySubsystem::GetInstance()->FindCupboardBySink(InTargetModel->GetUUID());
	auto Cupboard = UDSMVCSubsystem::GetInstance()->GetModelByID(CupboardUUID);
	if (Cupboard)
	{
		auto CupboardBox = Cupboard->GetModelOrientedBoundingBox();
		if (CupboardBox.Num() == 8)
		{
			TArray<FVector> CupboardTopOutline = { CupboardBox[0],CupboardBox[1],CupboardBox[2],CupboardBox[3] };

			auto Position_LTB = TargetProperty->GetTransformProperty().GetLocation();
			auto SinkSize = TargetProperty->SizeProperty;
			auto Width = SinkSize.Width * 0.1; //x axis
			auto Depth = SinkSize.Depth * 0.1;//y axis

			auto Center = TargetProperty->GetTopCenterLocation();
			auto SinkX = TargetProperty->GetTransformProperty().GetRotation().Vector();
			auto SinkY = FVector::CrossProduct(SinkX, FVector::UpVector);
			auto RelativeLocation = TargetProperty->RelativeLocation;

			auto RelativeRotator = TargetProperty->RelativeRotation;
			auto WorldRotator = TargetProperty->GetTransformProperty().GetRotation();

			auto Rect = FGeometryLibrary::CalculateRectangleCorners(Center, Width, Depth, WorldRotator);

			for (auto  & Iter : Rect)
			{
				Iter += RelativeLocation.X * SinkX;
				Iter += RelativeLocation.Y * SinkY;
			}

			auto FinalRotRect = FGeometryLibrary::RotatePointsAroundCenter(TargetProperty->GetTransformProperty().GetLocation(), RelativeRotator, Rect);

			//index = 0 : front ; index = 1 : left ; index = 2 : back ; index = 3 : right
			auto DistanceSegments = FGeometryLibrary::CalculateRectanglePolygonIntersections(FinalRotRect, CupboardTopOutline);

			auto& FrontRuler = RulerProperty->RulerMap[RULER_ITEM_FRONT];
			auto& BackRuler = RulerProperty->RulerMap[RULER_ITEM_BACK];
			auto& LeftRuler = RulerProperty->RulerMap[RULER_ITEM_LEFT];
			auto& RightRuler = RulerProperty->RulerMap[RULER_ITEM_RIGHT];
			auto& DownRuler = RulerProperty->RulerMap[RULER_ITEM_DOWN];
			auto& UpRuler = RulerProperty->RulerMap[RULER_ITEM_UP];

			FrontRuler.bOnlyRead = false;
			BackRuler.bOnlyRead = false;
			LeftRuler.bOnlyRead = false;
			RightRuler.bOnlyRead = false;
			DownRuler.bOnlyRead = false;
			UpRuler.bOnlyRead = false;

			for (auto& Iter : DistanceSegments)
			{
				Iter.Key.Z = (Center.Z + 10);
				Iter.Key.Z = (Center.Z + 10);

				Iter.Value.Z = (Center.Z + 10);
				Iter.Value.Z = (Center.Z + 10);
			}

			TArray<FVector> StartPoints;
			DistanceSegments.GenerateKeyArray(StartPoints);

			TArray<FVector> EndPoints;
			DistanceSegments.GenerateValueArray(EndPoints);

			FrontRuler.SegmentStart = StartPoints[0];
			FrontRuler.SegmentEnd = EndPoints[0];
			FrontRuler.bShow = FGeometryLibrary::PointInPolygon2D(CupboardTopOutline, StartPoints[0]) && FVector::Dist2D(FrontRuler.SegmentStart, FrontRuler.SegmentEnd) > 0.01;

			LeftRuler.SegmentStart = StartPoints[1];
			LeftRuler.SegmentEnd = EndPoints[1];
			LeftRuler.bShow = FGeometryLibrary::PointInPolygon2D(CupboardTopOutline, StartPoints[1]) && FVector::Dist2D(LeftRuler.SegmentStart, LeftRuler.SegmentEnd) > 0.01;

			BackRuler.SegmentStart = StartPoints[2];
			BackRuler.SegmentEnd = EndPoints[2];
			BackRuler.bShow = FGeometryLibrary::PointInPolygon2D(CupboardTopOutline, StartPoints[2]) && FVector::Dist2D(BackRuler.SegmentStart, BackRuler.SegmentEnd) > 0.01;

			RightRuler.SegmentStart = StartPoints[3];
			RightRuler.SegmentEnd = EndPoints[3];
			RightRuler.bShow = FGeometryLibrary::PointInPolygon2D(CupboardTopOutline, StartPoints[3]) && FVector::Dist2D(RightRuler.SegmentStart, RightRuler.SegmentEnd) > 0.01;

			//down ruler
			{
				auto CenterBottom = Center - FVector::UpVector * SinkSize.Height * 0.1;
				CenterBottom += RelativeLocation.X * SinkX;
				CenterBottom += RelativeLocation.Y * SinkY;

				FHitResult HitResult;
				FVector Start = CenterBottom;
				FVector End = CenterBottom - FVector::UpVector * 5000;

				TArray<FHitResult> HitResults;
				TArray<AActor*> IngoreActors = TArray<AActor*>{ UDSMVCSubsystem::GetInstance()->GetView(InTargetModel) };
				bool bHit = UKismetSystemLibrary::LineTraceSingle(GWorld, Start, End, UEngineTypes::ConvertToTraceType(ECollisionChannel::ECC_Visibility), true, IngoreActors, EDrawDebugTrace::Type::None, HitResult, true, FLinearColor::Red, FLinearColor::Green, 1.0f);
				if (bHit)
				{
					auto H = HitResult;
					//for (auto & H : HitResults)
					{
						auto Name = H.GetActor()->GetFName().ToString();
						//UE_LOG(LogTemp, Warning, TEXT("HitResult.GetActor()->GetFName().ToString() = %s"), *Name);
						auto EndPoint = H.ImpactPoint;
						DownRuler.SegmentStart = CenterBottom;
						DownRuler.SegmentEnd = EndPoint;
						DownRuler.bShow = FVector::Distance(DownRuler.SegmentStart, DownRuler.SegmentEnd) > 0.01;
					}
				}
			}

			//up ruler
			{
				auto TopCenter = Center;
				TopCenter += RelativeLocation.X * SinkX;
				TopCenter += RelativeLocation.Y * SinkY;

				FHitResult HitResult;
				FVector Start = Center;
				FVector End = Start + FVector::UpVector * 5000;

				TArray<FHitResult> HitResults;
				TArray<AActor*> IngoreActors = TArray<AActor*>{ UDSMVCSubsystem::GetInstance()->GetView(InTargetModel) };
				bool bHit = UKismetSystemLibrary::LineTraceSingle(GWorld, Start, End, UEngineTypes::ConvertToTraceType(ECollisionChannel::ECC_Visibility), true, IngoreActors, EDrawDebugTrace::Type::None, HitResult, true, FLinearColor::Red, FLinearColor::Green, 1.0f);
				if (bHit)
				{
					auto H = HitResult;
					//for (auto & H : HitResults)
					{
						auto Name = H.GetActor()->GetFName().ToString();
						//UE_LOG(LogTemp, Warning, TEXT("HitResult.GetActor()->GetFName().ToString() = %s"), *Name);
						auto EndPoint = H.ImpactPoint;
						UpRuler.SegmentStart = Start;
						UpRuler.SegmentEnd = EndPoint;
						UpRuler.bShow = FVector::Distance(UpRuler.SegmentStart, UpRuler.SegmentEnd) > 0.01;
					}
				}
			}
		}
	}
}

void UDSRulerLibrary::RefreshRulerProperty_Stove(FDSRulerDisplayerProperty* RulerProperty, UDSBaseModel* InTargetModel)
{
	TSharedPtr<FDSStoveProperty> TargetProperty = InTargetModel->GetTypedProperty<FDSStoveProperty>();
	auto CupboardUUID = UDSModelDependencySubsystem::GetInstance()->FindCupboardByStove(InTargetModel->GetUUID());
	auto Cupboard = UDSMVCSubsystem::GetInstance()->GetModelByID(CupboardUUID);
	if (Cupboard)
	{
		auto CupboardBox = Cupboard->GetModelOrientedBoundingBox();
		auto KtBox = InTargetModel->GetModelOrientedBoundingBox();
		if (CupboardBox.Num() == 8 && KtBox.Num() == 8)
		{
			TArray<FVector> CupboardTopOutline = { CupboardBox[0],CupboardBox[1],CupboardBox[2],CupboardBox[3] };
			TArray<FVector> KtOutline = { KtBox[0],KtBox[1],KtBox[2],KtBox[3] };

			//auto Position_LTB = TargetProperty->GetTransformProperty().GetLocation();
			auto StoveSize = TargetProperty->SizeProperty;
			auto Width = StoveSize.Width * 0.1; //x axis
			auto Depth = StoveSize.Depth * 0.1;//y axis

			//auto StoveX = TargetProperty->GetTransformProperty().GetRotation().Vector();
			//auto StoveY = FVector::CrossProduct(FVector::UpVector,StoveX);
			//auto RelativeLocation = TargetProperty->RelativeLocation;

			//auto Center = TargetProperty->GetTopCenterLocation();
			//Center += RelativeLocation.X * StoveX;
			//Center += RelativeLocation.Y * StoveY;
			//Center += RelativeLocation.Z * FVector::UpVector;


			//auto RelativeRotator = TargetProperty->RelativeRotation;
			//auto WorldRotator = TargetProperty->GetTransformProperty().GetRotation();

			//auto Rect = FGeometryLibrary::CalculateRectangleCorners(Center, Width, Depth, WorldRotator);

			//for (auto& Iter : Rect)
			//{
			//	Iter += RelativeLocation.X * StoveX;
			//	Iter += RelativeLocation.Y * StoveY;
			//	Iter += RelativeLocation.Z * FVector::UpVector;
			//}

			//auto FinalRotRect = FGeometryLibrary::RotatePointsAroundCenter(TargetProperty->GetTransformProperty().GetLocation(), RelativeRotator, Rect);

			//index = 0 : front ; index = 1 : left ; index = 2 : back ; index = 3 : right
			auto DistanceSegments = FGeometryLibrary::CalculateRectanglePolygonIntersections(KtOutline, CupboardTopOutline);

			if (DistanceSegments.Num() != 4)
			{
				return;
			}
			auto Center = (KtOutline[0] + KtOutline[2] + KtOutline[1] + KtOutline[3]) * 0.25;

			auto& FrontRuler = RulerProperty->RulerMap[RULER_ITEM_FRONT];
			auto& BackRuler = RulerProperty->RulerMap[RULER_ITEM_BACK];
			auto& LeftRuler = RulerProperty->RulerMap[RULER_ITEM_LEFT];
			auto& RightRuler = RulerProperty->RulerMap[RULER_ITEM_RIGHT];
			auto& DownRuler = RulerProperty->RulerMap[RULER_ITEM_DOWN];
			auto& UpRuler = RulerProperty->RulerMap[RULER_ITEM_UP];

			FrontRuler.bOnlyRead = false;
			BackRuler.bOnlyRead = false;
			LeftRuler.bOnlyRead = false;
			RightRuler.bOnlyRead = false;
			DownRuler.bOnlyRead = false;
			UpRuler.bOnlyRead = false;

			for (auto& Iter : DistanceSegments)
			{
				Iter.Key.Z = (Center.Z + 10);
				Iter.Key.Z = (Center.Z + 10);

				Iter.Value.Z = (Center.Z + 10);
				Iter.Value.Z = (Center.Z + 10);
			}

			TArray<FVector> StartPoints;
			DistanceSegments.GenerateKeyArray(StartPoints);

			TArray<FVector> EndPoints;
			DistanceSegments.GenerateValueArray(EndPoints);

			FrontRuler.SegmentStart = StartPoints[0];
			FrontRuler.SegmentEnd = EndPoints[0];
			FrontRuler.bShow = FGeometryLibrary::PointInPolygon2D(CupboardTopOutline, StartPoints[0]) && FVector::Dist2D(FrontRuler.SegmentStart, FrontRuler.SegmentEnd) > 0.01;

			LeftRuler.SegmentStart = StartPoints[1];
			LeftRuler.SegmentEnd = EndPoints[1];
			LeftRuler.bShow = FGeometryLibrary::PointInPolygon2D(CupboardTopOutline, StartPoints[1]) && FVector::Dist2D(LeftRuler.SegmentStart, LeftRuler.SegmentEnd) > 0.01;

			BackRuler.SegmentStart = StartPoints[2];
			BackRuler.SegmentEnd = EndPoints[2];
			BackRuler.bShow = FGeometryLibrary::PointInPolygon2D(CupboardTopOutline, StartPoints[2]) && FVector::Dist2D(BackRuler.SegmentStart, BackRuler.SegmentEnd) > 0.01;

			RightRuler.SegmentStart = StartPoints[3];
			RightRuler.SegmentEnd = EndPoints[3];
			RightRuler.bShow = FGeometryLibrary::PointInPolygon2D(CupboardTopOutline, StartPoints[3]) && FVector::Dist2D(RightRuler.SegmentStart, RightRuler.SegmentEnd) > 0.01;

			//down ruler
			{
				auto CenterBottom = Center - FVector::UpVector * StoveSize.Height * 0.1;
				//CenterBottom += RelativeLocation.X * StoveX;
				//CenterBottom += RelativeLocation.Y * StoveY;

				FHitResult HitResult;
				FVector Start = CenterBottom;
				FVector End = CenterBottom - FVector::UpVector * 5000;

				TArray<FHitResult> HitResults;
				TArray<AActor*> IngoreActors = TArray<AActor*>{ UDSMVCSubsystem::GetInstance()->GetView(InTargetModel) };
				bool bHit = UKismetSystemLibrary::LineTraceSingle(GWorld, Start, End, UEngineTypes::ConvertToTraceType(ECollisionChannel::ECC_Visibility), true, IngoreActors, EDrawDebugTrace::Type::None, HitResult, true, FLinearColor::Red, FLinearColor::Green, 1.0f);
				if (bHit)
				{
					auto H = HitResult;
					//for (auto & H : HitResults)
					{
						auto Name = H.GetActor()->GetFName().ToString();
						//UE_LOG(LogTemp, Warning, TEXT("HitResult.GetActor()->GetFName().ToString() = %s"), *Name);
						auto EndPoint = H.ImpactPoint;
						DownRuler.SegmentStart = CenterBottom;
						DownRuler.SegmentEnd = EndPoint;
						DownRuler.bShow = FVector::Distance(DownRuler.SegmentStart, DownRuler.SegmentEnd) > 0.01;
					}
				}
			}

			//up ruler
			{
				auto TopCenter = Center;
				//TopCenter += RelativeLocation.X * StoveX;
				//TopCenter += RelativeLocation.Y * StoveY;

				FHitResult HitResult;
				FVector Start = Center;
				FVector End = Start + FVector::UpVector * 5000;

				TArray<FHitResult> HitResults;
				TArray<AActor*> IngoreActors = TArray<AActor*>{ UDSMVCSubsystem::GetInstance()->GetView(InTargetModel) };
				bool bHit = UKismetSystemLibrary::LineTraceSingle(GWorld, Start, End, UEngineTypes::ConvertToTraceType(ECollisionChannel::ECC_Visibility), true, IngoreActors, EDrawDebugTrace::Type::None, HitResult, true, FLinearColor::Red, FLinearColor::Green, 1.0f);
				if (bHit)
				{
					auto H = HitResult;
					//for (auto & H : HitResults)
					{
						auto Name = H.GetActor()->GetFName().ToString();
						//UE_LOG(LogTemp, Warning, TEXT("HitResult.GetActor()->GetFName().ToString() = %s"), *Name);
						auto EndPoint = H.ImpactPoint;
						UpRuler.SegmentStart = Start;
						UpRuler.SegmentEnd = EndPoint;
						UpRuler.bShow = FVector::Distance(UpRuler.SegmentStart, UpRuler.SegmentEnd) > 0.01;
					}
				}
			}
		}
	}
}

void UDSRulerLibrary::RefreshRulerProperty_MoldingCeiling(FDSRulerDisplayerProperty* RulerProperty, UDSBaseModel* InTargetModel)
{
	auto MoldingCeilingProperty = InTargetModel->GetTypedProperty<FDSMoldingCeilingProperty>();
	auto CeilingModel = Cast<UDSMoldingCeilingModel>(InTargetModel);
	TArray<FVector> BoundingBox = CeilingModel->GetModelOrientedBoundingBox()
		;
	auto TopOutline = InTargetModel->GetOutlineInfo().TopOutline;
	auto BottomOutline = InTargetModel->GetOutlineInfo().BottomOutline;

	if (TopOutline.Num() < 4 || BottomOutline.Num() < 4)
	{
		return;
	}

	auto Top = (TopOutline[0] + TopOutline[2] + TopOutline[1] + TopOutline[3]) * 0.25;
	auto Bottom = (BottomOutline[0] + BottomOutline[2] + BottomOutline[1] + BottomOutline[3]) * 0.25;
	auto Center = (Top + Bottom) * 0.5;
	auto Rect = FGeometryLibrary::CalculateRectangleCorners(Center, MoldingCeilingProperty->SizeProperty.Width * 0.1, MoldingCeilingProperty->SizeProperty.Depth * 0.1, MoldingCeilingProperty->GetActualTransform().Rotator());
	auto Starts = FGeometryLibrary::CalculateRectangleRayPoints(Rect);

	auto AllWalls = UDSMVCSubsystem::GetInstance()->GetModels({ EDSModelType::E_House_Wall,EDSModelType::E_House_Pillar });
	TArray<AActor*> WallViews;
	for (auto& Wall : AllWalls)
	{
		auto V = UDSMVCSubsystem::GetInstance()->GetView(Wall);
		if (V)
		{
			WallViews.Add(V);
			for (auto& Cmp : Wall->GetComponentModels())
			{
				auto CV = UDSMVCSubsystem::GetInstance()->GetView(Cmp);
				WallViews.Add(CV);
			}
		}
	}

	auto AllModels = UDSMVCSubsystem::GetInstance()->GetAllModels();
	TArray<AActor*> ItemViews;
	for (auto& Model : AllModels)
	{
		if (Model->GetModelType() != EDSModelType::E_House_Wall 
			&& Model->GetModelType() != EDSModelType::E_House_Pillar 
			&& Model->GetModelType() != EDSModelType::E_House_Beam
			&& Model->GetModelType() != EDSModelType::E_Furniture_MoldingCeiling
			&& Model->GetModelType() != EDSModelType::E_Plane)
		{
			auto V = UDSMVCSubsystem::GetInstance()->GetView(Model);
			if (V)
			{
				ItemViews.Add(V);
				TArray<AActor*> ChildViews;
				V->GetAllChildActors(ChildViews);
				ItemViews.Append(ChildViews);
			}
		}
	}

	auto TargetView = UDSMVCSubsystem::GetInstance()->GetView(InTargetModel);

	TArray<AActor*> ThisActors;
	ThisActors.Add(TargetView);
	TArray<AActor*> ChildViews;
	TargetView->GetAllChildActors(ChildViews);
	ThisActors.Append(ChildViews);

	WallViews.Append(ThisActors);
	ItemViews.Append(ThisActors);

	auto Ends     = PerformRaycastFromPoints(GWorld, Starts, 10000, ItemViews);
	//auto EndsWall = PerformRaycastFromPoints(GWorld, Starts, 10000, ItemViews);


	auto& FrontRuler = RulerProperty->RulerMap[RULER_ITEM_FRONT];
	auto& BackRuler = RulerProperty->RulerMap[RULER_ITEM_BACK];
	auto& LeftRuler = RulerProperty->RulerMap[RULER_ITEM_LEFT];
	auto& RightRuler = RulerProperty->RulerMap[RULER_ITEM_RIGHT];
	auto& DownRuler = RulerProperty->RulerMap[RULER_ITEM_DOWN];


	auto& FrontRulerWall = RulerProperty->RulerMap[RULER_WALL_FRONT];
	auto& BackRulerWall = RulerProperty->RulerMap[RULER_WALL_BACK];
	auto& LeftRulerWall = RulerProperty->RulerMap[RULER_WALL_LEFT];
	auto& RightRulerWall = RulerProperty->RulerMap[RULER_WALL_RIGHT];
	auto& DownRulerFloor = RulerProperty->RulerMap[RULER_FLOOR_DOWN];

	FrontRulerWall.bShow = false;
	BackRulerWall.bShow = false;
	LeftRulerWall.bShow = false;
	RightRulerWall.bShow = false;
	DownRulerFloor.bShow = false;

	FrontRuler.bOnlyRead = false;
	BackRuler.bOnlyRead = false;
	LeftRuler.bOnlyRead = false;
	RightRuler.bOnlyRead = false;
	DownRuler.bOnlyRead = false;

	auto Dir0 = FVector::CrossProduct((Ends[0] - Starts[0]).GetSafeNormal(), FVector::ZAxisVector);
	auto Dir1 = FVector::CrossProduct((Ends[1] - Starts[1]).GetSafeNormal(), FVector::ZAxisVector);
	auto Dir2 = FVector::CrossProduct((Ends[2] - Starts[2]).GetSafeNormal(), FVector::ZAxisVector);
	auto Dir3 = FVector::CrossProduct((Ends[3] - Starts[3]).GetSafeNormal(), FVector::ZAxisVector);

	FrontRuler.SegmentStart = Starts[0] + Dir0 * 10;
	FrontRuler.SegmentEnd = Ends[0] + Dir0 * 10;
	FrontRuler.bShow = FVector::Dist2D(FrontRuler.SegmentStart, FrontRuler.SegmentEnd) >= 0.1;

	LeftRuler.SegmentStart = Starts[1] + Dir1 * 10;
	LeftRuler.SegmentEnd = Ends[1] + Dir1 * 10;
	LeftRuler.bShow = FVector::Dist2D(LeftRuler.SegmentStart, LeftRuler.SegmentEnd) >= 0.1;

	BackRuler.SegmentStart = Starts[2] + Dir2 * 10;
	BackRuler.SegmentEnd = Ends[2] + Dir2 * 10;
	BackRuler.bShow = FVector::Dist2D(BackRuler.SegmentStart, BackRuler.SegmentEnd) >= 0.1;

	RightRuler.SegmentStart = Starts[3] + Dir3 * 10;
	RightRuler.SegmentEnd = Ends[3] + Dir3 * 10;
	RightRuler.bShow = FVector::Dist2D(RightRuler.SegmentStart, RightRuler.SegmentEnd) >= 0.1;

}

void UDSRulerLibrary::RefreshRulerProperty_LayoutDoor(FDSRulerDisplayerProperty* RulerProperty, UDSBaseModel* InTargetModel)
{
	if (InTargetModel->GetModelType() == EDSModelType::E_Custom_LayoutDoor)
	{
		if (!RefreshRulerProperty_LayoutDoorOnWall(RulerProperty, InTargetModel))
		{
			RefreshRulerProperty_CustomCupboard(RulerProperty, InTargetModel);
		}
	}
}

bool UDSRulerLibrary::RefreshRulerProperty_LayoutDoorOnWall(FDSRulerDisplayerProperty* RulerProperty, UDSBaseModel* InTargetModel)
{
	UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(InTargetModel);

	ADesignStationController* Controller = ADesignStationController::Get();
	if (CupboardModel == nullptr || Controller == nullptr || !UDSMVCSubsystem::IsInitialized())
	{
		return false;
	}

	TArray<FVector> CupboardPoints = CupboardModel->GetModelOrientedBoundingBox();

	TSharedPtr<FDSBaseProperty> Property = CupboardModel->GetPropertySharedPtr();
	if (!Property)
	{
		return false;
	}

	auto DoorBox = InTargetModel->GetModelOrientedBoundingBox();
	TArray<FVector> TopOutline = { DoorBox[0],DoorBox[1],DoorBox[2],DoorBox[3] };
	auto Walls = UDSMVCSubsystem::GetInstance()->GetModels({ EDSModelType::E_House_Wall });
	UDSBaseModel* WallModel = nullptr;
	for (auto& Wall : Walls)
	{
		auto WallOutline = Wall->GetWorldTopOutline();
		if (FGeometryLibrary::PolygonIntersection(TopOutline, WallOutline))
		{
			WallModel = Wall;
			break;
		}
	}

	if (!WallModel)
	{
		return false;
	}
	auto WallProp = WallModel->GetTypedProperty<FDSHousePathProperty>();

	auto Center = (DoorBox[0] + DoorBox[1] + DoorBox[2] + DoorBox[3] + DoorBox[4] + DoorBox[5] + DoorBox[6] + DoorBox[7]) * 0.125;

	auto AxisX = Property->GetTransformProperty().GetRotation().Vector();
	auto AxisY = FVector::CrossProduct(FVector::UpVector, AxisX);

	auto Rect = FGeometryLibrary::CalculateRectangleCorners(Center, Property->SizeProperty.Width * 0.1, Property->SizeProperty.Depth * 0.1, Property->GetActualTransform().Rotator());


	auto Starts = FGeometryLibrary::CalculateRectangleRayPoints(Rect);
	Starts.Sort([&](const FVector& A, const FVector& B) { return A.Dot(AxisX) > B.Dot(AxisX); });



	auto LeftStart = Starts[0];
	auto RightStart = Starts[3];
	auto LeftAxis = (Starts[0] - Starts[3]).GetSafeNormal();
	auto RightAxis = -LeftAxis;
	auto WallSegStart = WallProp->SegmentStart;
	auto WallSegEnd = WallProp->SegmentEnd;
	auto WallSegfDir = (WallSegEnd - WallSegStart).GetSafeNormal();
	auto LeftEnd = LeftAxis.Dot(WallSegfDir) > 0 ? WallSegEnd : WallSegStart;
	auto RightEnd = RightAxis.Dot(WallSegfDir) > 0 ? WallSegEnd : WallSegStart;
	LeftEnd.Z = LeftStart.Z;
	RightEnd.Z = RightStart.Z;

	auto& LeftRuler = RulerProperty->RulerMap[RULER_ITEM_LEFT];
	auto& RightRuler = RulerProperty->RulerMap[RULER_ITEM_RIGHT];

	auto& FrontRulerWall = RulerProperty->RulerMap[RULER_WALL_FRONT];
	auto& DownRulerFloor = RulerProperty->RulerMap[RULER_FLOOR_DOWN];
	auto& UpRulerWall = RulerProperty->RulerMap[RULER_CEILING_UP];

	bool bTransform = CupboardModel->IsHasModelFlag(EModelState::E_Transform);

	LeftRuler.bOnlyRead = Property->GetIsLock() || bTransform;
	RightRuler.bOnlyRead = Property->GetIsLock() || bTransform;

	FrontRulerWall.bOnlyRead = Property->GetIsLock() || bTransform;
	DownRulerFloor.bOnlyRead = Property->GetIsLock() || bTransform;
	UpRulerWall.bOnlyRead = Property->GetIsLock() || bTransform;

	TArray<AActor*> IgnDoorAndWin;
	auto AllModels = UDSMVCSubsystem::GetInstance()->GetModels({ EDSModelType::E_Custom_LayoutDoor, EDSModelType::E_House_Door,EDSModelType::E_House_Window });
	double LeftMin = FVector::DistSquared2D(LeftStart, LeftEnd);
	double RightMin = FVector::DistSquared2D(RightStart, RightEnd);
	for (auto & M : AllModels)
	{

		if (M == InTargetModel)
		{
			continue;
		}
		TArray<FVector> ModelOutline;
		if (M->GetModelType() == EDSModelType::E_Custom_LayoutDoor)
		{
			auto OL = M->GetModelOrientedBoundingBox();
			ModelOutline = { OL[0],OL[1],OL[2],OL[3] };
		}
		else
		{
			auto DWProp = M->GetTypedProperty<FDSDoorAndWindowProperty>();
			auto DWStart = DWProp->SegmentStart;
			auto DWEnd = DWProp->SegmentEnd;
			auto Thickness = DWProp->GetThickness();
			auto DWDir = (DWEnd - DWStart).GetSafeNormal();
			auto DWNormal = FVector::CrossProduct(DWDir, FVector::UpVector).GetSafeNormal();

			ModelOutline = {
				DWStart + DWNormal * Thickness,
				DWStart - DWNormal * Thickness,
				DWEnd - DWNormal * Thickness,
				DWEnd + DWNormal * Thickness
			};
			
		}

		TArray<FVector> LeftPoints;
		FGeometryLibrary::PolygonAndSegmentIntersection(ModelOutline, LeftStart, LeftEnd, LeftPoints);
		if (!LeftPoints.IsEmpty())
		{
			LeftPoints.Sort([&](const FVector& A, const FVector& B) { return FVector::DistSquared(A, LeftStart) < FVector::DistSquared(B, LeftStart); });
			if (FVector::DistSquared2D(LeftPoints[0], LeftStart) < LeftMin)
			{
				LeftMin = FVector::DistSquared2D(LeftPoints[0], LeftStart);
				LeftEnd = LeftPoints[0];
			}
		}

		TArray<FVector> RightPoints;
		FGeometryLibrary::PolygonAndSegmentIntersection(ModelOutline, RightStart, RightEnd, RightPoints);
		if (!RightPoints.IsEmpty())
		{
			RightPoints.Sort([&](const FVector& A, const FVector& B) { return FVector::DistSquared(A, RightStart) < FVector::DistSquared(B, RightStart); });
			if (FVector::DistSquared2D(RightPoints[0], RightStart) < RightMin)
			{
				RightMin = FVector::DistSquared2D(RightPoints[0], RightStart);
				RightEnd = RightPoints[0];
			}
		}
	}

	auto WallOutline = WallModel->GetWorldTopOutline();
	auto WallStart = Property->GetActualTransform().GetLocation();
	auto WallEnd = WallStart + AxisY * 10000.0;
	auto UpStart = (DoorBox[0] + DoorBox[1] + DoorBox[2] + DoorBox[3]) * 0.25;
	auto UpEnd = UpStart;
	auto DownStart = (DoorBox[4] + DoorBox[5] + DoorBox[6] + DoorBox[7]) * 0.25;
	auto DownEnd = DownStart;

	TArray<FVector> EndPoints;
	FGeometryLibrary::PolygonAndSegmentIntersection(WallOutline, WallStart, WallEnd, EndPoints);
	if (EndPoints.IsEmpty())
	{
		WallEnd = WallStart;
	}
	else
	{
		EndPoints.Sort([&](const FVector& A, const FVector& B) { return FVector::DistSquared(A, WallStart) > FVector::DistSquared(B, WallStart); });
		WallEnd = EndPoints[0];
	}

	if (WallProp->GetHeight() > UpEnd.Z)
	{
		UpEnd.Z = WallProp->GetHeight();
	}

	if (DownStart.Z >0)
	{
		DownEnd.Z = 0;
	}

	bool bIs2D = Controller->Is2DScene();

	LeftRuler.SegmentStart = LeftStart;
	LeftRuler.SegmentEnd = LeftEnd;
	LeftRuler.bShow = FVector::Dist2D(LeftRuler.SegmentStart, LeftRuler.SegmentEnd) > 0.01 && !bIs2D;

	RightRuler.SegmentStart = RightStart;
	RightRuler.SegmentEnd = RightEnd;
	RightRuler.bShow = FVector::Dist2D(RightRuler.SegmentStart, RightRuler.SegmentEnd) > 0.01 && !bIs2D;

	FrontRulerWall.SegmentStart = WallStart;
	FrontRulerWall.SegmentEnd = WallEnd;
	FrontRulerWall.bShow = FVector::Dist2D(FrontRulerWall.SegmentStart, FrontRulerWall.SegmentEnd) > 0.01 && !bIs2D;

	DownRulerFloor.SegmentStart = DownStart;
	DownRulerFloor.SegmentEnd = DownEnd;
	DownRulerFloor.bShow = FVector::Distance(DownRulerFloor.SegmentStart, DownRulerFloor.SegmentEnd) > 0.01 && !bIs2D;

	UpRulerWall.SegmentStart = UpStart;
	UpRulerWall.SegmentEnd = UpEnd;
	UpRulerWall.bShow = FVector::Distance(UpRulerWall.SegmentStart, UpRulerWall.SegmentEnd) > 0.01 && !bIs2D;

	return true;
}


void UDSRulerLibrary::RefreshRulerProperty_RangeHood(FDSRulerDisplayerProperty* RulerProperty, UDSBaseModel* InTargetModel)
{
	auto RangeHoodProperty = InTargetModel->GetTypedProperty<FDSRangeHoodProperty>();

	auto Center = RangeHoodProperty->GetTopCenterLocation();
	auto AxisX = RangeHoodProperty->GetTransformProperty().GetRotation().Vector();
	auto AxisY = FVector::CrossProduct(FVector::UpVector,AxisX);
	auto RelativeLocation = RangeHoodProperty->RelativeLocation;
	auto RelativeRotator = RangeHoodProperty->RelativeRotation;

	Center += RelativeLocation.X * AxisX;
	Center += RelativeLocation.Y * AxisY;
	Center += RelativeLocation.Z * FVector::UpVector;

	auto Bottom = Center + FVector::DownVector * RangeHoodProperty->SizeProperty.Height * 0.1;
	auto Rect = FGeometryLibrary::CalculateRectangleCorners(RangeHoodProperty->GetTopCenterLocation(), RangeHoodProperty->SizeProperty.Width * 0.1, RangeHoodProperty->SizeProperty.Depth * 0.1, RangeHoodProperty->GetTransformProperty().GetRotation());
	for (auto& Iter : Rect)
	{
		Iter += RelativeLocation.X * AxisX;
		Iter += RelativeLocation.Y * AxisY;
		Iter += RelativeLocation.Z * FVector::UpVector;
	}



	auto FinalRotRect = FGeometryLibrary::RotatePointsAroundCenter(Center, RelativeRotator, Rect);

	auto Starts = FGeometryLibrary::CalculateRectangleRayPoints(FinalRotRect);
	auto TargetView = UDSMVCSubsystem::GetInstance()->GetView(InTargetModel);

	auto AllWalls = UDSMVCSubsystem::GetInstance()->GetModels({EDSModelType::E_House_Wall,EDSModelType::E_House_Pillar});
	TArray<AActor*> WallViews;
	for (auto& Wall : AllWalls)
	{
		auto V = UDSMVCSubsystem::GetInstance()->GetView(Wall);
		if (V)
		{
			WallViews.Add(V);
			for (auto& Cmp : Wall->GetComponentModels())
			{
				auto CV = UDSMVCSubsystem::GetInstance()->GetView(Cmp);
				WallViews.Add(CV);
			}
		}
	}



	auto AllModels = UDSMVCSubsystem::GetInstance()->GetAllModels();
	TArray<AActor*> ItemViews;
	for (auto& Model : AllModels)
	{
		if (Model->GetModelType() != EDSModelType::E_House_Wall && Model->GetModelType() != EDSModelType::E_House_Pillar && Model->GetModelType() != EDSModelType::E_Plane)
		{
			auto V = UDSMVCSubsystem::GetInstance()->GetView(Model);
			if (V)
			{
				ItemViews.Add(V);
				TArray<AActor*> ChildViews;
				V->GetAllChildActors(ChildViews);
				ItemViews.Append(ChildViews);
			}
		}
	}

	TArray<AActor*> ExpectTopDown;
	for (auto& Model : AllModels)
	{
		if (Model->GetModelType() != EDSModelType::E_House_Area && Model->GetModelType() != EDSModelType::E_House_Roof && Model->GetModelType() != EDSModelType::E_RoofArea)
		{
			auto V = UDSMVCSubsystem::GetInstance()->GetView(Model);
			if (V)
			{
				ExpectTopDown.Add(V);
				TArray<AActor*> ChildViews;
				V->GetAllChildActors(ChildViews);
				ExpectTopDown.Append(ChildViews);
			}
		}
	}

	TArray<AActor*> ExpectArea;
	for (auto& Model : AllModels)
	{
		if (Model->GetModelType() == EDSModelType::E_House_Area)
		{
			auto V = UDSMVCSubsystem::GetInstance()->GetView(Model);
			if (V)
			{
				ExpectArea.Add(V);
				TArray<AActor*> ChildViews;
				V->GetAllChildActors(ChildViews);
				ExpectArea.Append(ChildViews);
			}
		}
	}

	TArray<AActor*> ThisActors;
	ThisActors.Add(TargetView);
	TArray<AActor*> ChildViews;
	TargetView->GetAllChildActors(ChildViews);
	ThisActors.Append(ChildViews);

	WallViews.Append(ThisActors);
	ItemViews.Append(ThisActors);
	ExpectTopDown.Append(ThisActors);
	ExpectArea.Append(ThisActors);

	auto Ends = PerformRaycastFromPoints(GWorld, Starts, 10000, WallViews);
	auto EndsWall = PerformRaycastFromPoints(GWorld, Starts, 10000, ItemViews);

	auto& FrontRuler = RulerProperty->RulerMap[RULER_ITEM_FRONT];
	auto& BackRuler = RulerProperty->RulerMap[RULER_ITEM_BACK];
	auto& LeftRuler = RulerProperty->RulerMap[RULER_ITEM_LEFT];
	auto& RightRuler = RulerProperty->RulerMap[RULER_ITEM_RIGHT];
	auto& DownRuler = RulerProperty->RulerMap[RULER_ITEM_DOWN];

	auto& FrontRulerWall = RulerProperty->RulerMap[RULER_WALL_FRONT];
	auto& BackRulerWall = RulerProperty->RulerMap[RULER_WALL_BACK];
	auto& LeftRulerWall = RulerProperty->RulerMap[RULER_WALL_LEFT];
	auto& RightRulerWall = RulerProperty->RulerMap[RULER_WALL_RIGHT];
	auto& DownRulerFloor = RulerProperty->RulerMap[RULER_FLOOR_DOWN];


	FrontRuler.bOnlyRead = false;
	BackRuler.bOnlyRead = false;
	LeftRuler.bOnlyRead = false;
	RightRuler.bOnlyRead = false;
	DownRuler.bOnlyRead = false;

	FrontRulerWall.bOnlyRead = false;
	BackRulerWall.bOnlyRead = false;
	LeftRulerWall.bOnlyRead = false;
	RightRulerWall.bOnlyRead = false;
	DownRulerFloor.bOnlyRead = false;

	auto Dir0 = FVector::CrossProduct((Ends[0] - Starts[0]).GetSafeNormal(), FVector::ZAxisVector);
	auto Dir1 = FVector::CrossProduct((Ends[1] - Starts[1]).GetSafeNormal(), FVector::ZAxisVector);
	auto Dir2 = FVector::CrossProduct((Ends[2] - Starts[2]).GetSafeNormal(), FVector::ZAxisVector);
	auto Dir3 = FVector::CrossProduct((Ends[3] - Starts[3]).GetSafeNormal(), FVector::ZAxisVector);

	FrontRuler.SegmentStart = Starts[0] + Dir0 * 10;
	FrontRuler.SegmentEnd = Ends[0] + Dir0 * 10;
	FrontRuler.bShow = FVector::Dist2D(FrontRuler.SegmentStart, FrontRuler.SegmentEnd) > 0.01;

	LeftRuler.SegmentStart = Starts[1] + Dir1 * 10;
	LeftRuler.SegmentEnd = Ends[1] + Dir1 * 10;
	LeftRuler.bShow = FVector::Dist2D(LeftRuler.SegmentStart, LeftRuler.SegmentEnd) > 0.01;

	BackRuler.SegmentStart = Starts[2] + Dir2 * 10;
	BackRuler.SegmentEnd = Ends[2] + Dir2 * 10;
	BackRuler.bShow = FVector::Dist2D(BackRuler.SegmentStart, BackRuler.SegmentEnd) > 0.01;

	RightRuler.SegmentStart = Starts[3] + Dir3 * 10;
	RightRuler.SegmentEnd = Ends[3] + Dir3 * 10;
	RightRuler.bShow = FVector::Dist2D(RightRuler.SegmentStart, RightRuler.SegmentEnd) > 0.01;

	FrontRulerWall.SegmentStart = Starts[0] - Dir0 * 10;
	FrontRulerWall.SegmentEnd = EndsWall[0] - Dir0 * 10;
	FrontRulerWall.bShow = FVector::Dist2D(FrontRulerWall.SegmentStart, FrontRulerWall.SegmentEnd) > 0.01;

	LeftRulerWall.SegmentStart = Starts[1] - Dir1 * 10;
	LeftRulerWall.SegmentEnd = EndsWall[1] - Dir1 * 10;
	LeftRulerWall.bShow = FVector::Dist2D(LeftRulerWall.SegmentStart, LeftRulerWall.SegmentEnd) > 0.01;

	BackRulerWall.SegmentStart = Starts[2] - Dir2 * 10;
	BackRulerWall.SegmentEnd = EndsWall[2] - Dir2 * 10;
	BackRulerWall.bShow = FVector::Dist2D(BackRulerWall.SegmentStart, BackRulerWall.SegmentEnd) > 0.01;

	RightRulerWall.SegmentStart = Starts[3] - Dir3 * 10;
	RightRulerWall.SegmentEnd = EndsWall[3] - Dir3 * 10;
	RightRulerWall.bShow = FVector::Dist2D(RightRulerWall.SegmentStart, RightRulerWall.SegmentEnd) > 0.01;

	auto DownEndFloor = PerformRaycastFromPoints_DownVisibility(GWorld, Bottom, 10000,ExpectTopDown);
	DownRulerFloor.SegmentStart = Bottom - FVector::XAxisVector * 10;
	DownRulerFloor.SegmentEnd = DownEndFloor - FVector::XAxisVector * 10;
	DownRulerFloor.bShow = FVector::Distance(DownRulerFloor.SegmentStart, DownRulerFloor.SegmentEnd) > 0.01;

	auto DownEnd = PerformRaycastFromPoints_Down(GWorld, Bottom, 10000, ExpectArea);
	DownRuler.SegmentStart = Bottom + FVector::XAxisVector * 10;
	DownRuler.SegmentEnd = DownEnd + FVector::XAxisVector * 10;
	DownRuler.bShow = FVector::Distance(DownRuler.SegmentStart, DownRuler.SegmentEnd) > 0.01 && FVector::Distance(DownRuler.SegmentStart, DownRuler.SegmentEnd) < FVector::Distance(DownRulerFloor.SegmentStart, DownRulerFloor.SegmentEnd);
	//DownRulerFloor.bShow = FVector::Distance(DownRulerFloor.SegmentStart, DownRulerFloor.SegmentEnd) > 0.01 && !DownRuler.bShow;
}

void UDSRulerLibrary::RefreshRulerProperty_Furniture(FDSRulerDisplayerProperty* RulerProperty, UDSBaseModel* InTargetModel)
{
	auto Property = InTargetModel->GetTypedProperty<FDSSoftFurnitureProperty>();

	auto TopOutline = InTargetModel->GetOutlineInfo().TopOutline;
	auto BottomOutline = InTargetModel->GetOutlineInfo().BottomOutline;

	if (TopOutline.Num() <4  || BottomOutline.Num() < 4)
	{
		return;
	}

	auto Top = (TopOutline[0] + TopOutline[2] + TopOutline[1] + TopOutline[3]) * 0.25;
	auto Bottom = (BottomOutline[0] + BottomOutline[2] + BottomOutline[1] + BottomOutline[3]) * 0.25;
	auto Center = (Top + Bottom) * 0.5;

	auto AxisX = Property->GetTransformProperty().GetRotation().Vector();
	auto AxisY = FVector::CrossProduct(FVector::UpVector, AxisX);
	auto RelativeLocation = Property->RelativeLocation;
	auto RelativeRotator = Property->RelativeRotation;
	
	auto Rect = FGeometryLibrary::CalculateRectangleCorners(Center, Property->SizeProperty.Width * 0.1, Property->SizeProperty.Depth * 0.1, Property->GetActualTransform().Rotator());

	auto Starts = FGeometryLibrary::CalculateRectangleRayPoints(Rect);

	CalculateRectDistance(Starts, Top, Bottom, RulerProperty, InTargetModel,true,true);
}

void UDSRulerLibrary::RefreshRulerProperty_OrnamentLine(FDSRulerDisplayerProperty* RulerProperty,
	UDSBaseModel* InTargetModel)
{
	if (InTargetModel == nullptr || RulerProperty == nullptr)
	{
		return;
	}
	
	TArray<TWeakObjectPtr<UDSBaseModel>> LinkModels = InTargetModel->GetLinkModels().Array();
	if (!LinkModels.IsValidIndex(0) || !LinkModels[0].IsValid() || !UDSToolLibrary::IsGeneratedLineType(LinkModels[0]->GetModelType()))
	{
		return;
	}

	TSharedPtr<FDSLineOrnamentLineProperty> OrnamentLineProperty = InTargetModel->GetTypedProperty<FDSLineOrnamentLineProperty>();
	TSharedPtr<FDSGeneratedLineBaseProperty> LineBaseProperty = LinkModels[0]->GetTypedProperty<FDSGeneratedLineBaseProperty>();

	int32 PreSegmentPos = OrnamentLineProperty->LineSegmentPos - 1;
	PreSegmentPos = PreSegmentPos < 0 ? LineBaseProperty->LineSegments.Num() - 1 : PreSegmentPos;

	int32 NextSegmentPos = (OrnamentLineProperty->LineSegmentPos + 1) % LineBaseProperty->LineSegments.Num();
	
	const FDSGeneratedLineSegment& RightLine = LineBaseProperty->LineSegments[PreSegmentPos];
	const FDSGeneratedLineSegment& LeftLine = LineBaseProperty->LineSegments[NextSegmentPos];
	const FDSGeneratedLineSegment& CurrentLine = LineBaseProperty->LineSegments[OrnamentLineProperty->LineSegmentPos];

	if (FVector::DotProduct(CurrentLine.SegmentRightDir, LeftLine.SegmentRightDir) < 0.9)
	{
		RulerProperty->RulerMap[RULER_ITEM_LEFT].bFocus = false;
		RulerProperty->RulerMap[RULER_ITEM_LEFT].bShow = true;
		RulerProperty->RulerMap[RULER_ITEM_LEFT].bOnlyRead = OrnamentLineProperty->GetIsMoving();
		RulerProperty->RulerMap[RULER_ITEM_LEFT].LimitRange.X = 1;
		RulerProperty->RulerMap[RULER_ITEM_LEFT].SegmentStart = LeftLine.StartPoint.Position + LeftLine.SegmentRightDir * 5.0f;
		RulerProperty->RulerMap[RULER_ITEM_LEFT].SegmentEnd = LeftLine.EndPoint.Position + LeftLine.SegmentRightDir * 5.0f;
	}

	if (FVector::DotProduct(CurrentLine.SegmentRightDir, RightLine.SegmentRightDir) < 0.9)
	{
		RulerProperty->RulerMap[RULER_ITEM_RIGHT].bFocus = false;
		RulerProperty->RulerMap[RULER_ITEM_RIGHT].bShow = true;
		RulerProperty->RulerMap[RULER_ITEM_RIGHT].bOnlyRead = OrnamentLineProperty->GetIsMoving();
		RulerProperty->RulerMap[RULER_ITEM_RIGHT].LimitRange.X = 1;
		RulerProperty->RulerMap[RULER_ITEM_RIGHT].SegmentStart = RightLine.StartPoint.Position + RightLine.SegmentRightDir * 5.0f;
		RulerProperty->RulerMap[RULER_ITEM_RIGHT].SegmentEnd = RightLine.EndPoint.Position + RightLine.SegmentRightDir * 5.0f;
	}
}

void UDSRulerLibrary::RefreshRulerProperty_MultiSelect(FDSRulerDisplayerProperty* RulerProperty, UDSBaseModel* InTargetModel)
{
	// 线条状态下多选不生成标尺
	if (UDSMVCSubsystem::GetInstance()->GetStateType() == EDSFSMState::FSM_Line)
	{
		return;
	}

	auto OutlineInfo = InTargetModel->GetOutlineInfo();
	auto TopOutline = OutlineInfo.TopOutline;
	auto BottomOutline = OutlineInfo.BottomOutline;
	if (TopOutline.Num() < 4)
	{
		return;
	}
	auto TopCenter = (TopOutline[0] + TopOutline[2] + TopOutline[1] + TopOutline[3]) * 0.25;
	auto BottomCenter = (BottomOutline[0] + BottomOutline[2] + BottomOutline[1] + BottomOutline[3]) * 0.25;
	auto Center = (TopCenter + BottomCenter) * 0.5;
	auto Rect = TopOutline;
	for (auto & R : Rect)
	{
		R.Z = Center.Z;
	}
	auto Starts = FGeometryLibrary::CalculateRectangleRayPoints(Rect);
	CalculateRectDistance(Starts, TopCenter, BottomCenter,RulerProperty, InTargetModel);
}

void UDSRulerLibrary::CalculateInnerDistance(UDSBaseModel* InTargetModel, UDSBaseModel* OuterModel)
{
	if (OuterModel && UDSToolLibrary::IsCustomCabinetType(OuterModel->GetModelType())
		&& (InTargetModel->GetModelType() == EDSModelType::E_Custom_Stove || InTargetModel->GetModelType() == EDSModelType::E_Custom_Sink))
	{
		auto PreProperty = StaticCastSharedPtr<FDSKitchenBaseProperty>(InTargetModel->GetPropertySharedPtr());
		auto Model = Cast<UDSCupboardModel>(OuterModel);
		auto CupboardProperty = OuterModel->GetPropertySharedPtr();
		if (!Model || !PreProperty)
		{
			return;
		}

		auto Center = PreProperty->GetTopCenterLocation();
		//auto ReX = PreProperty->RelativeLocation.X;
		//auto ReY = PreProperty->RelativeLocation.Y;

		auto StoveX = PreProperty->GetTransformProperty().GetRotation().Vector();
		auto StoveY = FVector::CrossProduct(StoveX, FVector::UpVector);

		auto Rect = FGeometryLibrary::CalculateRectangleCorners(Center, PreProperty->SizeProperty.Width * 0.1, PreProperty->SizeProperty.Depth * 0.1, PreProperty->GetTransformProperty().GetRotation());

		//for (auto& Iter : Rect)0
		//{
		//    Iter += ReX * StoveX;
		//    Iter += ReY * StoveY;
		//}

		//distance to cupboard
		auto CupboardBox = Model->GetModelOrientedBoundingBox();

		auto AxisY = CupboardProperty->GetTransformProperty().GetRotation().Vector();;
		auto AxisX = FVector::CrossProduct(AxisY, FVector::ZAxisVector).GetSafeNormal();

		if (CupboardBox.Num() == 8)
		{
			TArray<FVector>  CupboardTopOutline = { CupboardBox[0],CupboardBox[1],CupboardBox[2],CupboardBox[3] };
			FVector CupboardCenter = (CupboardBox[0] + CupboardBox[1] + CupboardBox[2] + CupboardBox[3]) * 0.25;
			auto RelativeOutline = FGeometryLibrary::CalculateRelativePositions(AxisX, AxisY, CupboardTopOutline);

			//relative transform to cupboard
			PreProperty->ToCupboard = CupboardCenter - Center;

			//index = 0 : front ; index = 1 : left ; index = 2 : back ; index = 3 : right
			TMap<FVector, FVector> DistanceSegments = FGeometryLibrary::CalculateRectanglePolygonIntersectionsRelativeAxis(Rect, CupboardTopOutline, AxisX, AxisY);
			if (DistanceSegments.Num() == 4)
			{
				TArray<FVector> StartPoints;
				DistanceSegments.GenerateKeyArray(StartPoints);
				PreProperty->CupboardFrontDistance = FVector::Dist2D(StartPoints[0], DistanceSegments[StartPoints[0]]) * 10;
				if (!FGeometryLibrary::PointInPolygon2D(RelativeOutline, StartPoints[0]))
				{
					PreProperty->CupboardFrontDistance = -PreProperty->CupboardFrontDistance;
				}
				PreProperty->CupboardLeftDistance = FVector::Dist2D(StartPoints[1], DistanceSegments[StartPoints[1]]) * 10;
				if (!FGeometryLibrary::PointInPolygon2D(RelativeOutline, StartPoints[1]))
				{
					PreProperty->CupboardLeftDistance = -PreProperty->CupboardLeftDistance;
				}
				PreProperty->CupboardBackDistance = FVector::Dist2D(StartPoints[2], DistanceSegments[StartPoints[2]]) * 10;
				if (!FGeometryLibrary::PointInPolygon2D(RelativeOutline, StartPoints[2]))
				{
					PreProperty->CupboardBackDistance = -PreProperty->CupboardBackDistance;
				}
				PreProperty->CupboardRightDistance = FVector::Dist2D(StartPoints[3], DistanceSegments[StartPoints[3]]) * 10;
				if (!FGeometryLibrary::PointInPolygon2D(RelativeOutline, StartPoints[3]))
				{
					PreProperty->CupboardRightDistance = -PreProperty->CupboardRightDistance;
				}
			}
		}
	}
}

void UDSRulerLibrary::CalculateRectDistance(const TArray<FVector> Starts, const FVector& Top, const FVector& Bottom,FDSRulerDisplayerProperty* RulerProperty, UDSBaseModel* InTargetModel, bool bShowTop, bool bShowDown)
{
	auto TargetView = UDSMVCSubsystem::GetInstance()->GetView(InTargetModel);
	TArray<AActor* > MultiViews;

	if (InTargetModel->GetModelType() == EDSModelType::E_MultiSelect)
	{
		auto MultiModel = Cast<UDSMultiModel>(InTargetModel);
		auto IncludeModels = MultiModel->GetIncludeModel();
		for (auto& Inc : IncludeModels)
		{
			if (Inc->GetOwnerModel() && Inc->GetOwnerModel() != Inc)
			{
				return;
			}
			auto IncludeView = UDSMVCSubsystem::GetInstance()->GetView(Inc);
			if (IncludeView)
			{
				MultiViews.Add(IncludeView);

				if (IncludeView->GetBpView())
				{
					MultiViews.Add(IncludeView->GetBpView());
				}

				TArray<AActor*> ChildViews;
				IncludeView->GetAttachedActors(ChildViews);
				TArray<AActor*> AttachViews;
				IncludeView->GetAttachedActors(AttachViews);
				MultiViews.Append(ChildViews);
				//MultiViews.Append(AttachViews);

				for (auto& Cmp : Inc->GetComponentModels())
				{
					auto CV = UDSMVCSubsystem::GetInstance()->GetView(Cmp);
					MultiViews.Add(CV);
				}
			}
		}
	}
	else if (InTargetModel->GetModelType() == EDSModelType::E_Group)
	{
		auto GroupModel = Cast<UDSGroupModel>(InTargetModel);
		auto IncludeModels = GroupModel->GetItems();
		for (auto& Inc : IncludeModels)
		{
			if (Inc->GetOwnerModel() && Inc->GetOwnerModel() != Inc)
			{
				return;
			}
			auto IncludeView = UDSMVCSubsystem::GetInstance()->GetView(Inc);
			if (IncludeView)
			{
				MultiViews.Add(IncludeView);

				if (IncludeView->GetBpView())
				{
					MultiViews.Add(IncludeView->GetBpView());
				}

				TArray<AActor*> ChildViews;
				IncludeView->GetAllChildActors(ChildViews);
				TArray<AActor*> AttachViews;
				IncludeView->GetAttachedActors(AttachViews);
				MultiViews.Append(ChildViews);
				//MultiViews.Append(AttachViews);

				for (auto& Cmp : Inc->GetComponentModels())
				{
					auto CV = UDSMVCSubsystem::GetInstance()->GetView(Cmp);
					MultiViews.Add(CV);
				}
			}
		}
	}

	auto Property =InTargetModel->GetPropertySharedPtr();

	auto AllWalls = UDSMVCSubsystem::GetInstance()->GetModels({ EDSModelType::E_House_Wall,EDSModelType::E_House_Pillar, EDSModelType::E_House_Area_Label });
	TArray<AActor*> WallViews;
	for (auto& Wall : AllWalls)
	{
		auto V = UDSMVCSubsystem::GetInstance()->GetView(Wall);
		if (V)
		{
			WallViews.Add(V);
			for (auto& Cmp : Wall->GetComponentModels())
			{
				auto CV = UDSMVCSubsystem::GetInstance()->GetView(Cmp);
				WallViews.Add(CV);
			}
		}
	}



	auto AllModels = UDSMVCSubsystem::GetInstance()->GetAllModels();
	TArray<AActor*> ItemViews;
	for (auto& Model : AllModels)
	{
		if (Model->GetModelType() != EDSModelType::E_House_Wall && Model->GetModelType() != EDSModelType::E_House_Pillar && Model->GetModelType() != EDSModelType::E_Plane)
		{
			auto V = UDSMVCSubsystem::GetInstance()->GetView(Model);
			if (V)
			{
				ItemViews.Add(V);
				TArray<AActor*> ChildViews;
				V->GetAllChildActors(ChildViews);
				ItemViews.Append(ChildViews);
			}
		}
	}

	TArray<AActor*> ExpectTopDown;
	for (auto& Model : AllModels)
	{
		if (Model->GetModelType() != EDSModelType::E_House_Area && Model->GetModelType() != EDSModelType::E_House_Roof && Model->GetModelType() != EDSModelType::E_RoofArea)
		{
			auto V = UDSMVCSubsystem::GetInstance()->GetView(Model);
			if (V)
			{
				ExpectTopDown.Add(V);
				TArray<AActor*> ChildViews;
				V->GetAllChildActors(ChildViews);
				ExpectTopDown.Append(ChildViews);
			}
		}
	}

	TArray<AActor*> ExpectArea;
	for (auto& Model : AllModels)
	{
		if (Model->GetModelType() == EDSModelType::E_House_Area)
		{
			auto V = UDSMVCSubsystem::GetInstance()->GetView(Model);
			if (V)
			{
				ExpectArea.Add(V);
				TArray<AActor*> ChildViews;
				V->GetAllChildActors(ChildViews);
				ExpectArea.Append(ChildViews);
			}
		}
	}

	TArray<AActor*> ThisActors;
	if (TargetView)
	{
		ThisActors.Add(TargetView);
	}
	ThisActors.Append(MultiViews);

	TArray<AActor*> ChildViews;
	TargetView->GetAllChildActors(ChildViews);
	ThisActors.Append(ChildViews);

	WallViews.Append(ThisActors);
	ItemViews.Append(ThisActors);
	ExpectTopDown.Append(ThisActors);
	ExpectArea.Append(ThisActors);

	ADesignStationController* Controller = ADesignStationController::Get();

	bool bIs2D = Controller->Is2DScene();

	auto Ends = IntersectionToItems( Starts, 10000, InTargetModel);
	auto EndsWall = PerformRaycastFromPoints(GWorld, Starts, 10000, ItemViews);

	auto& FrontRuler = RulerProperty->RulerMap[RULER_ITEM_FRONT];
	auto& BackRuler = RulerProperty->RulerMap[RULER_ITEM_BACK];
	auto& LeftRuler = RulerProperty->RulerMap[RULER_ITEM_LEFT];
	auto& RightRuler = RulerProperty->RulerMap[RULER_ITEM_RIGHT];
	auto& DownRuler = RulerProperty->RulerMap[RULER_ITEM_DOWN];
	//auto& UpRuler = RulerProperty->RulerMap[RULER_ITEM_UP];

	auto& FrontRulerWall = RulerProperty->RulerMap[RULER_WALL_FRONT];
	auto& BackRulerWall = RulerProperty->RulerMap[RULER_WALL_BACK];
	auto& LeftRulerWall = RulerProperty->RulerMap[RULER_WALL_LEFT];
	auto& RightRulerWall = RulerProperty->RulerMap[RULER_WALL_RIGHT];
	auto& DownRulerFloor = RulerProperty->RulerMap[RULER_FLOOR_DOWN];
	auto& UpRulerCeiling = RulerProperty->RulerMap[RULER_CEILING_UP];


	bool bTransform = InTargetModel->IsHasModelFlag(EModelState::E_Transform);

	FrontRuler.bOnlyRead = Property->GetIsLock() || bTransform;
	BackRuler.bOnlyRead = Property->GetIsLock() || bTransform;
	LeftRuler.bOnlyRead = Property->GetIsLock() || bTransform;
	RightRuler.bOnlyRead = Property->GetIsLock() || bTransform;
	DownRuler.bOnlyRead = Property->GetIsLock() || bTransform;
	//UpRuler.bOnlyRead = Property->GetIsLock() || Property->GetIsMoving();

	FrontRulerWall.bOnlyRead = Property->GetIsLock() || bTransform;
	BackRulerWall.bOnlyRead = Property->GetIsLock() || bTransform;
	LeftRulerWall.bOnlyRead = Property->GetIsLock() || bTransform;
	RightRulerWall.bOnlyRead = Property->GetIsLock() || bTransform;
	DownRulerFloor.bOnlyRead = Property->GetIsLock() || bTransform;
	UpRulerCeiling.bOnlyRead = Property->GetIsLock() || bTransform;

	auto Dir0 = FVector::CrossProduct((Ends[0] - Starts[0]).GetSafeNormal(), FVector::ZAxisVector);
	auto Dir1 = FVector::CrossProduct((Ends[1] - Starts[1]).GetSafeNormal(), FVector::ZAxisVector);
	auto Dir2 = FVector::CrossProduct((Ends[2] - Starts[2]).GetSafeNormal(), FVector::ZAxisVector);
	auto Dir3 = FVector::CrossProduct((Ends[3] - Starts[3]).GetSafeNormal(), FVector::ZAxisVector);

	FrontRuler.SegmentStart = Starts[0] + Dir0 * 10;
	FrontRuler.SegmentEnd = Ends[0] + Dir0 * 10;
	FrontRuler.bShow = FVector::Dist2D(FrontRuler.SegmentStart, FrontRuler.SegmentEnd) > 0.01;

	LeftRuler.SegmentStart = Starts[1] + Dir1 * 10;
	LeftRuler.SegmentEnd = Ends[1] + Dir1 * 10;
	LeftRuler.bShow = FVector::Dist2D(LeftRuler.SegmentStart, LeftRuler.SegmentEnd) > 0.01;

	BackRuler.SegmentStart = Starts[2] + Dir2 * 10;
	BackRuler.SegmentEnd = Ends[2] + Dir2 * 10;
	BackRuler.bShow = FVector::Dist2D(BackRuler.SegmentStart, BackRuler.SegmentEnd) > 0.01;

	RightRuler.SegmentStart = Starts[3] + Dir3 * 10;
	RightRuler.SegmentEnd = Ends[3] + Dir3 * 10;
	RightRuler.bShow = FVector::Dist2D(RightRuler.SegmentStart, RightRuler.SegmentEnd) > 0.01;

	FrontRulerWall.SegmentStart = Starts[0] - Dir0 * 10;
	FrontRulerWall.SegmentEnd = EndsWall[0] - Dir0 * 10;
	FrontRulerWall.bShow = FVector::Dist2D(FrontRulerWall.SegmentStart, FrontRulerWall.SegmentEnd) > 0.01;

	LeftRulerWall.SegmentStart = Starts[1] - Dir1 * 10;
	LeftRulerWall.SegmentEnd = EndsWall[1] - Dir1 * 10;
	LeftRulerWall.bShow = FVector::Dist2D(LeftRulerWall.SegmentStart, LeftRulerWall.SegmentEnd) > 0.01;

	BackRulerWall.SegmentStart = Starts[2] - Dir2 * 10;
	BackRulerWall.SegmentEnd = EndsWall[2] - Dir2 * 10;
	BackRulerWall.bShow = FVector::Dist2D(BackRulerWall.SegmentStart, BackRulerWall.SegmentEnd) > 0.01;

	RightRulerWall.SegmentStart = Starts[3] - Dir3 * 10;
	RightRulerWall.SegmentEnd = EndsWall[3] - Dir3 * 10;
	RightRulerWall.bShow = FVector::Dist2D(RightRulerWall.SegmentStart, RightRulerWall.SegmentEnd) > 0.01;

	if (bIs2D)
	{
		if (FVector::Dist2D(FrontRuler.SegmentStart, FrontRuler.SegmentEnd) < FVector::Dist2D(FrontRulerWall.SegmentStart, FrontRulerWall.SegmentEnd)
			&& FrontRuler.bShow)
		{
			FrontRulerWall.bShow = false;
		}
		else if(FrontRulerWall.bShow)
		{
			FrontRuler.bShow = false;
		}

		if (FVector::Dist2D(LeftRuler.SegmentStart, LeftRuler.SegmentEnd) < FVector::Dist2D(LeftRulerWall.SegmentStart, LeftRulerWall.SegmentEnd)
			&& LeftRuler.bShow)
		{
			LeftRulerWall.bShow = false;
		}
		else if (LeftRulerWall.bShow)
		{
			LeftRuler.bShow = false;
		}

		if (FVector::Dist2D(BackRuler.SegmentStart, BackRuler.SegmentEnd) < FVector::Dist2D(BackRulerWall.SegmentStart, BackRulerWall.SegmentEnd)
			&& BackRuler.bShow)
		{
			BackRulerWall.bShow = false;
		}
		else if (BackRulerWall.bShow)
		{
			BackRuler.bShow = false;
		}

		if (FVector::Dist2D(RightRuler.SegmentStart, RightRuler.SegmentEnd) < FVector::Dist2D(RightRulerWall.SegmentStart, RightRulerWall.SegmentEnd)
			&& RightRuler.bShow)
		{
			RightRulerWall.bShow = false;
		}
		else if (RightRulerWall.bShow)
		{
			RightRuler.bShow = false;
		}

	}

	auto DownEndFloor = PerformRaycastFromPoints_DownVisibility(GWorld, Bottom, 10000, ExpectTopDown);
	DownRulerFloor.SegmentStart = Bottom - FVector::XAxisVector * 10;
	DownRulerFloor.SegmentEnd = DownEndFloor - FVector::XAxisVector * 10;
	DownRulerFloor.bShow = FVector::Distance(DownRulerFloor.SegmentStart, DownRulerFloor.SegmentEnd) > 0.01 && !bIs2D && bShowDown;

	auto DownEnd = PerformRaycastFromPoints_Down(GWorld, Bottom, 10000, ExpectArea);
	DownRuler.SegmentStart = Bottom + FVector::XAxisVector * 10;
	DownRuler.SegmentEnd = DownEnd + FVector::XAxisVector * 10;
	DownRuler.bShow = FVector::Distance(DownRuler.SegmentStart, DownRuler.SegmentEnd) > 0.01 && FVector::Distance(DownRuler.SegmentStart, DownRuler.SegmentEnd) < FVector::Distance(DownRulerFloor.SegmentStart, DownRulerFloor.SegmentEnd) && !bIs2D && bShowDown;

	auto UpEndCeiling = PerformRaycastFromPoints_Up(GWorld, Top, 10000, ExpectTopDown);
	UpRulerCeiling.SegmentStart = Top /*- FVector::XAxisVector * 10*/;
	UpRulerCeiling.SegmentEnd = UpEndCeiling/* - FVector::XAxisVector * 10*/;
	UpRulerCeiling.bShow = FVector::Distance(UpRulerCeiling.SegmentStart, UpRulerCeiling.SegmentEnd) > 0.01 && !bIs2D && bShowTop;
}

TArray<FString> UDSRulerLibrary::CollectAllRulerNames()
{
	return {
		RULER_WALL_FRONT, RULER_WALL_RIGHT, RULER_WALL_BACK, RULER_WALL_LEFT,
		RULER_ITEM_FRONT, RULER_ITEM_RIGHT, RULER_ITEM_BACK, RULER_ITEM_LEFT,
		RULER_CEILING_UP, RULER_FLOOR_DOWN, RULER_ITEM_UP, RULER_ITEM_DOWN,
		RULER_PATH_SELF_RIGHT, RULER_PATH_SELF_LEFT, RULER_PATH_POINT_START_0,
		RULER_PATH_POINT_START_1, RULER_PATH_POINT_END_0, RULER_PATH_POINT_END_1,
		RULER_POINT_PATH_0, RULER_POINT_PATH_1, RULER_POINT_PATH_2, RULER_POINT_PATH_3,
		RULER_POINT_PATH_4, RULER_POINT_PATH_5, RULER_SELF_WIDTH, RULER_SELF_DEPTH,
		RULER_SELF_HEIGHT, RULER_SEGMENT, RULER_SEGMENT_START_0, RULER_SEGMENT_START_1,
		RULER_SEGMENT_END_0, RULER_SEGMENT_END_1, RULER_ON_WALL_POS, RULER_ON_WALL_NEG
	};
}

void UDSRulerLibrary::GetRulerInfo_Pillar(const int32& InCountIndex, FString& OutRulerItem, FString& OutRulerWall)
{
	if (InCountIndex == 0)
	{
		OutRulerItem = RULER_ITEM_BACK;
		OutRulerWall = RULER_WALL_BACK;
	}
	else if (InCountIndex == 1)
	{
		OutRulerItem = RULER_ITEM_RIGHT;
		OutRulerWall = RULER_WALL_RIGHT;
	}
	else if (InCountIndex == 2)
	{
		OutRulerItem = RULER_ITEM_LEFT;
		OutRulerWall = RULER_WALL_LEFT;
	}
	else if (InCountIndex == 3)
	{
		OutRulerItem = RULER_ITEM_FRONT;
		OutRulerWall = RULER_WALL_FRONT;
	}
}

void UDSRulerLibrary::GetRulerInfo_NearlyShow_WallAndItem(
	FDSRulerProperty& WallProperty, bool WallMarkShow,
	FDSRulerProperty& ItemProperty, bool ItemMarkShow)
{
	// UE_LOG(DSRulerLibraryLog, Warning, TEXT("GetRulerInfo_NearlyShow_WallAndItem [%f][%d][%f][%d]"),
	// 	WallProperty.GetLength(), WallProperty.bShow, ItemProperty.GetLength(), ItemProperty.bShow);
	if (WallProperty.bShow && ItemProperty.bShow)
	{
		bool bWallNearly = WallProperty.GetLength() <= ItemProperty.GetLength();
		WallProperty.bShow = bWallNearly ? WallProperty.bShow && WallMarkShow : false;
		ItemProperty.bShow = bWallNearly ? false : ItemProperty.bShow && ItemMarkShow;
	}
}

TArray<FVector> UDSRulerLibrary::PerformRaycastFromPoints(UWorld* World, const TArray<FVector>& Points, float TraceDistance, const TArray<AActor*>& Ingores)
{
	TArray<FVector> HitPoints;
	if (!World || Points.Num() != 4)
	{
		return HitPoints; // Return empty array if the world is invalid or points are not exactly 4
	}

	// Directions for -X, -Y, X, Y
	TArray<FVector> Directions = { FVector(-1, 0, 0), FVector(0, -1, 0), FVector(1, 0, 0), FVector(0, 1, 0) };

	for (int32 i = 0; i < Points.Num(); ++i)
	{
		FVector Start = Points[i];
		FVector StartTop = Start + FVector::UpVector * 50;
		FVector StartDown = Start + FVector::DownVector * 50;

		FVector End = Start + Directions[i] * TraceDistance;
		FVector EndTop = StartTop + Directions[i] * TraceDistance;
		FVector EndDown = StartDown + Directions[i] * TraceDistance;

		FHitResult HitResult;
		FHitResult HitResultTop;
		FHitResult HitResultDown;

		//APlayerController* Controller = UGameplayStatics::GetPlayerController(World, 0);

		APlayerController* Controller = World->GetFirstPlayerController();
		if (Controller == nullptr)
		{
			return Points;
		}

		auto IgnoreActors = Ingores;
		IgnoreActors.Add(Controller->GetPawn());

		TArray<FHitResult> HitResults;
		Controller->GetWorld()->LineTraceMultiByChannel(HitResults, Start, End, ECollisionChannel::ECC_Visibility);
		TArray<FHitResult> HitResultsTop;
		Controller->GetWorld()->LineTraceMultiByChannel(HitResultsTop, StartTop, EndTop, ECollisionChannel::ECC_Visibility);
		TArray<FHitResult> HitResultsDown;
		Controller->GetWorld()->LineTraceMultiByChannel(HitResultsDown, StartDown, EndDown, ECollisionChannel::ECC_Visibility);

		//bool bHit = UKismetSystemLibrary::LineTraceMulti(GWorld, Start, End, UEngineTypes::ConvertToTraceType(ECollisionChannel::ECC_Visibility), true, IgnoreActors, EDrawDebugTrace::Type::None, HitResult, true);
		//bool bHitTop = UKismetSystemLibrary::LineTraceMulti(GWorld, StartTop, EndTop, UEngineTypes::ConvertToTraceType(ECollisionChannel::ECC_Visibility), true, IgnoreActors, EDrawDebugTrace::Type::None, HitResultTop, true);
		//bool bHitDown = UKismetSystemLibrary::LineTraceMulti(GWorld, StartDown, EndDown, UEngineTypes::ConvertToTraceType(ECollisionChannel::ECC_Visibility), true, IgnoreActors, EDrawDebugTrace::Type::None, HitResultDown, true);
		bool bHit = !HitResults.IsEmpty();
		bool bHitTop = !HitResultsTop.IsEmpty();
		bool bHitDown = !HitResultsDown.IsEmpty();
			
		double Dist = 99999;

		if (bHit)
		{
			HitResults.Sort([](const FHitResult& A, const FHitResult& B) { return A.Distance < B.Distance; });
			for (auto & Hit : HitResults)
			{
				HitResult = Hit;
				ADSBaseView* OtherDSActor = Cast<ADSBaseView>(HitResult.GetActor()->GetAttachParentActor());
				if (!OtherDSActor)
				{
					OtherDSActor = Cast<ADSBaseView>(HitResult.GetActor());
				}
				if (OtherDSActor && !IgnoreActors.Contains(OtherDSActor))
				{
					Dist = FVector::Dist2D(HitResult.Location, Start);
					bHit = true;
					break;
				}
				else
				{
					bHit = false;
				}
			}

		}
		if (bHitTop)
		{
			HitResultsTop.Sort([](const FHitResult& A, const FHitResult& B) { return A.Distance < B.Distance; });
			for (auto& Hit : HitResultsTop)
			{
				HitResultTop = Hit;
				ADSBaseView* OtherDSActor = Cast<ADSBaseView>(HitResultTop.GetActor()->GetAttachParentActor());
				if (!OtherDSActor)
				{
					OtherDSActor = Cast<ADSBaseView>(HitResultTop.GetActor());
				}
				if (OtherDSActor && !IgnoreActors.Contains(OtherDSActor))
				{
					auto TopLocation = HitResultTop.Location;
					if (Dist > FVector::Dist2D(TopLocation, StartTop))
					{
						Dist = FVector::Dist2D(TopLocation, StartTop);
					}
					bHitTop = true;
					break;
				}
				else
				{
					bHitTop = false;
				}
			}
		}
		if (bHitDown)
		{
			HitResultsDown.Sort([](const FHitResult& A, const FHitResult& B) { return A.Distance < B.Distance; });
			for (auto& Hit : HitResultsDown)
			{
				HitResultDown = Hit;
				ADSBaseView* OtherDSActor = Cast<ADSBaseView>(HitResultDown.GetActor()->GetAttachParentActor());
				if (!OtherDSActor)
				{
					OtherDSActor = Cast<ADSBaseView>(HitResultDown.GetActor());
				}
				if (OtherDSActor && !IgnoreActors.Contains(OtherDSActor))
				{
					auto DownLocation = HitResultDown.Location;
					if (Dist > FVector::Dist2D(DownLocation, StartDown))
					{
						Dist = FVector::Dist2D(DownLocation, StartDown);
					}
					bHitDown = true;
					break;
				}
				else
				{
					bHitDown = false;
				}
			}
		}
		if (bHit || bHitTop || bHitDown)
		{
			HitPoints.Add(Start + Directions[i] * Dist);
		}
		else
		{
			HitPoints.Add(Start);
		}
	}

	return HitPoints;
}

FVector UDSRulerLibrary::PerformRaycastFromPoints_Up(UWorld* World, const FVector& Point, float TraceDistance, const TArray<AActor*>& Ingores)
{
	FHitResult HitResult;
	FCollisionQueryParams QueryParams;
	APlayerController* Controller = World->GetFirstPlayerController();
	if (Controller == nullptr)
	{
		return Point;
	}

	auto WallHeight = UDSConfigSubsystem::GetInstance()->GetValue_Wall_Float(DSSetting::Wall::EWallType::E_DefaultWallH) * 0.1;

	//bool bHit = UKismetSystemLibrary::LineTraceSingle(GWorld, Point, Point + FVector::UpVector * TraceDistance, UEngineTypes::ConvertToTraceType(ECollisionChannel::ECC_Visibility), true, Ingores, EDrawDebugTrace::Type::None, HitResult, true);

	if (WallHeight > Point.Z)
	{
		auto NewLoc = Point;
		NewLoc.Z = WallHeight;
		return NewLoc;
	}
	return Point;
}

FVector UDSRulerLibrary::PerformRaycastFromPoints_Down(UWorld* World, const FVector& Point, float TraceDistance, const TArray<AActor*>& Ingores)
{
	FHitResult HitResult;
	FCollisionQueryParams QueryParams;
	APlayerController* Controller = World->GetFirstPlayerController();
	if (Controller == nullptr)
	{
		return Point;
	}
	QueryParams.AddIgnoredActor(Cast<AActor>(Controller->GetPawn()));
	QueryParams.AddIgnoredActors(Ingores);
	QueryParams.bTraceComplex = false;
	//TArray<FHitResult> HitResults;
	//bool bHit  = UKismetSystemLibrary::LineTraceMulti(GWorld, Point, Point + FVector::DownVector * TraceDistance, UEngineTypes::ConvertToTraceType(ECollisionChannel::ECC_Visibility), true, Ingores, EDrawDebugTrace::Type::None, HitResults, true, FLinearColor::Red, FLinearColor::Green, 1.0f);
	//bool bHit = World->LineTraceSingleByChannel(HitResult, Point, Point + FVector::DownVector * TraceDistance, ECC_Visibility, QueryParams);
	TArray<FHitResult> HitResults;
	Controller->GetWorld()->LineTraceMultiByChannel(HitResults, Point, Point + FVector::DownVector * TraceDistance, ECollisionChannel::ECC_Visibility);
	bool bHit = !HitResults.IsEmpty();

	if (bHit)
	{
		HitResults.Sort([](const FHitResult& A, const FHitResult& B) { return A.Distance < B.Distance; });
		ADSBaseView* OtherDSActor = Cast<ADSBaseView>(HitResults[0].GetActor()->GetAttachParentActor());
		if (!OtherDSActor)
        {
			OtherDSActor = Cast<ADSBaseView>(HitResults[0].GetActor());
        }
		if (OtherDSActor)
		{
			auto Model = OtherDSActor->GetModel();
			auto Box = Model->GetModelOrientedBoundingBox();
			auto Res = HitResults[0].ImpactPoint;
			Res.Z = Box[0].Z;
			return Res;
		}
	}
	return Point;
}

FVector UDSRulerLibrary::PerformRaycastFromPoints_DownVisibility(UWorld* World, const FVector& Point, float TraceDistance, const TArray<AActor*>& Ingores)
{
	FCollisionQueryParams QueryParams;
	APlayerController* Controller = World->GetFirstPlayerController();
	if (Controller == nullptr)
	{
		return Point;
	}

	QueryParams.AddIgnoredActor(Cast<AActor>(Controller->GetPawn()));
	QueryParams.AddIgnoredActors(Ingores);
	QueryParams.bTraceComplex = true;
	//FHitResult HitResult;
	////bool bHit = UKismetSystemLibrary::LineTraceMulti(GWorld, Point, Point + FVector::DownVector * TraceDistance, UEngineTypes::ConvertToTraceType(ECollisionChannel::ECC_Visibility), true, Ingores, EDrawDebugTrace::Type::None, HitResults, true, FLinearColor::Red, FLinearColor::Green, 1.0f);
	//bool bHit = World->LineTraceSingleByChannel(HitResult, Point, Point + FVector::DownVector * TraceDistance, ECC_Visibility, QueryParams);

	TArray<FHitResult> HitResults;
	Controller->GetWorld()->LineTraceMultiByChannel(HitResults, Point, Point + FVector::DownVector * TraceDistance, ECollisionChannel::ECC_Visibility);
	bool bHit = !HitResults.IsEmpty();

	if (bHit)
	{
		HitResults.Sort([](const FHitResult& A, const FHitResult& B) { return A.Distance < B.Distance; });
		for (auto & Iter : HitResults)
		{
			auto View = Cast<ADSBaseView>(Iter.GetActor());
			if (View && View->IsValidLowLevel() && View->GetModelType() == EDSModelType::E_House_Area)
			{
				return Iter.Location;
			}
		}
	}
	return Point;
}

TArray<FVector> UDSRulerLibrary::IntersectionToItems(const TArray<FVector>& Points, float TraceDistance, UDSBaseModel* InTargetModel)
{
	TArray<UDSBaseModel*> ItemModels;
	TArray<FBoxSphereBounds> ViewBoxs;

	ItemModels = UDSMVCSubsystem::GetInstance()->GetModels(
		{ EDSModelType::E_House_Pillar, EDSModelType::E_Furniture_HouseFurniture });


	TArray<UDSBaseModel*> CupboardModels = UDSMVCSubsystem::GetInstance()->GetModels({
		EDSModelType::E_Custom_UpperCabinet ,
		EDSModelType::E_Custom_WallCabinet, // 吊柜
		EDSModelType::E_Custom_BaseCabinet, // 地柜
		EDSModelType::E_Custom_TallCabinet, // 高柜
		EDSModelType::E_Custom_CornerCabinet, // 转角柜
		EDSModelType::E_Custom_Tatami,
		EDSModelType::E_Custom_WallBoardCabinet,
		EDSModelType::E_Custom_LayoutDoor
		});
	for (UDSBaseModel* Model : CupboardModels)
	{
		if (Model == nullptr || Model == InTargetModel)
		{
			continue;
		}

		bool bInclude = false;
		if (InTargetModel->GetModelType() == EDSModelType::E_MultiSelect || InTargetModel->GetModelType() == EDSModelType::E_Group)
		{
			auto IncludeModels = Cast<UDSMultiModel>(InTargetModel)->GetIncludeModel();
			for (auto & Iter : IncludeModels)
			{
				if (Iter == Model || Iter->GetComponentModels().Contains(Model))
				{
					bInclude = true;
					break;
				}
			}
		}

		if (!bInclude && (Model->GetOwnerModel() == nullptr || !Model->GetOwnerModel()->IsA<UDSCupboardModel>()))
		{
			ItemModels.Add(Model);
		}
	}

	ItemModels.Remove(InTargetModel);

	for (UDSBaseModel* Model : ItemModels)
	{
		if (Model == nullptr || Model->GetOwnedView() == nullptr)
		{
			continue;
		}

		auto ItemZ = Model->GetPropertySharedPtr()->GetActualTransform().GetLocation().Z;
		auto TargetZ = InTargetModel->GetPropertySharedPtr()->GetActualTransform().GetLocation().Z;
		auto TargetHeight = InTargetModel->GetPropertySharedPtr()->GetSizeProperty().Height * 0.1;

		if (FMath::Abs(ItemZ- TargetZ) > TargetHeight)
		{
			continue;
		}

		FBoxSphereBounds ViewBox(ForceInitToZero);

		bool bIsCupboard = Model->GetModelType() >= EDSModelType::E_Custom_UpperCabinet && Model->GetModelType() < EDSModelType::E_Custom_Furniture_Range_End;
		if (bIsCupboard && UDSMVCSubsystem::IsInitialized() && UDSMVCSubsystem::GetInstance()->GetState() != nullptr && UDSMVCSubsystem::GetInstance()->GetState()->GetMouseSelectType() ==
			EDSSelectType::E_Part_Select)
		{
			TArray<UPrimitiveComponent*> ViewComponents;
			Model->GetOwnedView()->GetComponents<UPrimitiveComponent>(ViewComponents);

			TArray<AActor*> AttachedActors;
			Model->GetOwnedView()->GetAttachedActors(AttachedActors);
			for (AActor* Actor : AttachedActors)
			{
				if (Actor == nullptr || Actor->IsA<ADSCupboardBaseView>())
				{
					continue;
				}

				TArray<UPrimitiveComponent*> ActorComponents;
				Actor->GetComponents<UPrimitiveComponent>(ActorComponents);

				ViewComponents.Append(MoveTemp(ActorComponents));
			}

			for (UPrimitiveComponent* Component : ViewComponents)
			{
				if (!Component->IsVisible() || !Component->IsCollisionEnabled())
				{
					continue;
				}

				FBoxSphereBounds ComponentBounds = Component->GetLocalBounds();
				ViewBox = ViewBox + ComponentBounds.TransformBy(Component->GetRelativeTransform());
			}

			ViewBox = ViewBox.TransformBy(Model->GetOwnedView()->GetActorTransform());
		}
		else
		{	
			ViewBox = UDesignStationFunctionLibrary::GetActorAxisAlignedBoundingBox_Recursively(Model->GetOwnedView());		
		}
		ViewBoxs.Add(ViewBox);
	}

	TArray<FVector> Directions = { FVector(-1, 0, 0), FVector(0, -1, 0), FVector(1, 0, 0), FVector(0, 1, 0) };
	TArray<double>  Distances = { TraceDistance, TraceDistance, TraceDistance, TraceDistance };

	int32 Index = 0;

	TArray<FVector> OutPoints = Points;

	for (auto& Start : Points)
	{
		for (const auto& ViewBox : ViewBoxs)
		{
			UE::Geometry::TPolygon2<double> CurrentViewPolygon;
			CurrentViewPolygon.AppendVertex({ ViewBox.Origin.X - ViewBox.BoxExtent.X, ViewBox.Origin.Y - ViewBox.BoxExtent.Y });
			CurrentViewPolygon.AppendVertex({ ViewBox.Origin.X + ViewBox.BoxExtent.X, ViewBox.Origin.Y - ViewBox.BoxExtent.Y });
			CurrentViewPolygon.AppendVertex({ ViewBox.Origin.X + ViewBox.BoxExtent.X, ViewBox.Origin.Y + ViewBox.BoxExtent.Y });
			CurrentViewPolygon.AppendVertex({ ViewBox.Origin.X - ViewBox.BoxExtent.X, ViewBox.Origin.Y + ViewBox.BoxExtent.Y });

			for (UE::Geometry::TSegment2<double> Segment : CurrentViewPolygon.Segments())
			{
				FVector IntersectionPoint;
				if (FMath::SegmentIntersection2D(Start,
					Start + (Directions[Index] * TraceDistance),
					FVector(Segment.StartPoint(), Start.Z),
					FVector(Segment.EndPoint(), Start.Z),
					IntersectionPoint))
				{
					if (FVector::Distance(Start, IntersectionPoint) < Distances[Index])
					{
						Distances[Index] = FVector::Distance(Start, IntersectionPoint);
						OutPoints[Index] = IntersectionPoint;
					}
				}
			}
		}
		++Index;
	}
	return OutPoints;
}