// Fill out your copyright notice in the Description page of Project Settings.

#pragma once
#include "AdaptationDynamicData.h"
#include "CoreMinimal.h"
#include "FunctionalDependencyInfo.generated.h"


USTRUCT()
struct FFunctionalDependencySubNodeInfo
{
	GENERATED_USTRUCT_BODY()
public:
	FFunctionalDependencySubNodeInfo() :NodeUUID(""), SubNodeUUID(TEXT("")), Direction(EAdaptationDirection::E_None) {
	};

	FFunctionalDependencySubNodeInfo(const FString& InNodeUUID,const FString& InSubNodeUUID, EAdaptationDirection InDirection)
		:NodeUUID(InNodeUUID), SubNodeUUID(InSubNodeUUID), Direction(InDirection) {
	};
	FString NodeUUID;
	FString SubNodeUUID;
	EAdaptationDirection Direction;


	FORCEINLINE bool operator==(const FFunctionalDependencySubNodeInfo& Other) const
	{
		return NodeUUID == Other.NodeUUID && SubNodeUUID == Other.SubNodeUUID && Direction == Other.Direction;
	};

	bool IsValid() const
	{
		return !NodeUUID.IsEmpty() && !SubNodeUUID.IsEmpty() && Direction != EAdaptationDirection::E_None;
	};
public:
	//序列化和反序列化
	void Serialization(TSharedRef<TJsonWriter<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>> JsonWriter);
	void Deserialization(const TSharedPtr<FJsonObject>& InJsonData);

};
FORCEINLINE uint32 GetTypeHash(const FFunctionalDependencySubNodeInfo& Key) 
{
	return HashCombine(GetTypeHash(Key.NodeUUID), HashCombine(GetTypeHash(Key.SubNodeUUID), GetTypeHash(Key.Direction)));
};

struct FFunctionalDependencyInfo :public TSharedFromThis<FFunctionalDependencyInfo>
{
	FFunctionalDependencyInfo(const FString& InUUID)
		:UUID(InUUID), bEnablePassiveAdaptation(false), Name(TEXT(""))
	{
	};
	FFunctionalDependencyInfo(const FString& InUUID,bool bInPassiveAdaptation)
		:UUID(InUUID), bEnablePassiveAdaptation(bInPassiveAdaptation), Name(TEXT(""))
	{
	};

public:
	FString UUID; //ComponentTreeUUID

	//TWeakObjectPtr<class UDSBaseModel> OwnerModelPtr;
	bool bEnablePassiveAdaptation;

	TMap<FFunctionalDependencySubNodeInfo, TWeakPtr<FFunctionalDependencyInfo>> DependentNodes;

	TMap<TWeakPtr<FFunctionalDependencyInfo>, EAdaptationDirection> BeDependentNodes;

	//缓存的依赖节点信息, 加载文件使用
	TMap<FFunctionalDependencySubNodeInfo, FString> DependentNodesCache;
	TMap<FString, EAdaptationDirection> BeDependentNodesCache;

	//debug
	FString Name;

public:

	const FString& GetUUID() const { return UUID; };

	void AddDependentNode(const FFunctionalDependencySubNodeInfo& InSubNodeInfo, TWeakPtr<FFunctionalDependencyInfo> NodeInfo);

	void AddBeDenendentNode(TSharedRef<FFunctionalDependencyInfo> InDependencyInfo, EAdaptationDirection InDir);

	void ClearDependentNode();

	void RemoveBeDependentNode(TSharedRef<FFunctionalDependencyInfo> BeDependencyInfo);

	bool BeDependentBy(const FString& UUID);

	const TMap<TWeakPtr<FFunctionalDependencyInfo>, EAdaptationDirection>& GetBeDependentNodes() const { return BeDependentNodes; };
	const TMap<FFunctionalDependencySubNodeInfo, TWeakPtr<FFunctionalDependencyInfo>>& GetDependentNodes() const { return DependentNodes; };


	FFunctionalDependencySubNodeInfo GetDependencySubNode(const FString& InNodeUUID,bool bSubNodeUUID = false) const;

	void OnCopy(const FFunctionalDependencyInfo& InInfo);

	TArray<TWeakPtr<FFunctionalDependencyInfo>> GetBeDependentNodesWithDepth();

public:
	//序列化和反序列化
	void Serialization(TSharedRef<TJsonWriter<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>> JsonWriter);
	void Deserialization(const TSharedPtr<FJsonObject>& InJsonData);
};


struct FFunctionalDependencyMap
{
private:

	TMap<FString, TSharedPtr<FFunctionalDependencyInfo>> DependencyInfoMap;

public:
	const TSharedPtr<FFunctionalDependencyInfo>& AddDependencyInfo(const FString& InUUID);

	const TSharedPtr<FFunctionalDependencyInfo>& AddDependencyInfo(const FString& InUUID,bool bInPassiveAdaptation);

	void ClearDependencyInfo(const FString& SourceUUID);

	TSharedPtr<FFunctionalDependencyInfo> GetDependencyInfo(const FString& SourceUUID) const;

	TMap<FString, TSharedPtr<FFunctionalDependencyInfo>>& GetDependencyInfoMapRef() { return DependencyInfoMap; };

	void RemoveDependencyInfo(const FString& SourceUUID);

	void ReplaceDependencyInfo(const FString& SourceUUID, const FString& TargetUUID);

	void OnCopy(const TSharedPtr<FFunctionalDependencyMap>& InMap);
public:
	//序列化和反序列化
	void Serialization(TSharedRef<TJsonWriter<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>> JsonWriter);
	void Deserialization(const TSharedPtr<FJsonObject>& InJsonData);
};