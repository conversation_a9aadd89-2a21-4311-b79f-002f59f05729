﻿#include "DSCameraSubsystem.h"

#include "BaseNetworkFileTask.h"
#include "DSCameraDataConfig.h"
#include "DSCameraDataSet.h"
#include "EasyNetworkFileSubsystem.h"
#include "IHttpResponse.h"
#include "NetworkFileUploadTask.h"
#include "TimerManager.h"
#include "BasicClasses/CameraPawn.h"
#include "BasicClasses/DesignStationController.h"
#include "Components/SceneCaptureComponent2D.h"
#include "Engine/TextureRenderTarget2D.h"
#include "Kismet/KismetMathLibrary.h"
#include "MiniMap/DSMiniMapSceneCapture.h"
#include "Subsystems/DSNetworkSubsystem.h"
#include "Subsystems/CustomConfig/DSConfigSubsystem.h"
#include "SubSystems/Drawing/DSDrawingSubsystem.h"
#include "SubSystems/File/DSFileSubsystem.h"
#include "SubSystems/MVC/Model/Custom/DownloadTaskPayload/CameraSavePayload.h"
#include "SubSystems/MVC/Model/House/Area/DSHouseAreaModel.h"
#include "SubSystems/MVC/StateMachine/Library/DSFSMLibrary.h"
#include "SubSystems/UI/DSUISubsystem.h"
#include "SubSystems/MVC/Library/DSToolLibrary.h"
#include "SubSystems/MVC/Model/Group/DSGroupModel.h"

#include "SubSystems/Camera/CollectionSceneCapture/CollectionSceneCapture2D.h"
#include "SubSystems/MVC/DSMVCSubsystem.h"

extern const int32 Z_SLIDE_STEP;

extern const int32 Z_2D_SLIDER_STEP;

UDSCameraSubsystem* UDSCameraSubsystem::Instance = nullptr;

UDSCameraSubsystem::UDSCameraSubsystem()
{
	Instance = this;
}

bool UDSCameraSubsystem::IsInitialized()
{
	return Instance != nullptr;
}

UDSCameraSubsystem* UDSCameraSubsystem::GetInstance()
{
	return Instance;
}

void UDSCameraSubsystem::Initialize(FSubsystemCollectionBase& Collection)
{
	Super::Initialize(Collection);
}

void UDSCameraSubsystem::Deinitialize()
{
	Super::Deinitialize();

	//RemoveInitTimeHandle
	auto World = GetWorld();
	if (World && InitialTimer.IsValid())
	{
		World->GetTimerManager().ClearTimer(InitialTimer);
	}
}

void UDSCameraSubsystem::SaveCurrentInfo()
{
	if (UDSFileSubsystem::GetInstance()->CurPlanId == 0)
	{
		UDSUISubsystem::GetInstance()->SetToastState(EToastState::EWarning, TEXT("请先保存方案"), TEXT(""), true);
		return;
	}
	ACameraPawn* Pawn = ADesignStationController::Get()->GetDSPawn();
	if (Pawn == nullptr)
	{
		return;
	}
	if (Pawn->GetCurrentCameraType() == ECameraType::EXYPlan2D ||
		Pawn->GetCurrentCameraType() == ECameraType::EXYPlan2D_Ceil)
	{
		return;
	}

	FDSCameraData SaveData;
	SaveData.CameraType = CurCameraType;
	Pawn->GetCameraState(SaveData.CameraState);

	// 检查是否有非常接近的
	for (auto _data : SavedCameraPawnData)
	{
		if (_data.CameraType != SaveData.CameraType)
		{
			continue;
		}

		if (_data.CameraState.ProjectionMode == ECameraProjectionMode::Type::Perspective)
		{
			if (!FMath::IsNearlyZero(_data.CameraState.FOV - SaveData.CameraState.FOV) ||
				!FMath::IsNearlyZero(_data.CameraState.ArmLength - SaveData.CameraState.ArmLength) ||
				!FMath::IsNearlyEqual(_data.CameraState.NearClippingPlane, SaveData.CameraState.NearClippingPlane))
			{
				continue;
			}
		}
		else if (_data.CameraState.ProjectionMode == ECameraProjectionMode::Type::Orthographic)
		{
			if (!FMath::IsNearlyZero(_data.CameraState.OrthWidth - SaveData.CameraState.OrthWidth))
			{
				continue;
			}
		}
		if (FVector::Distance(_data.CameraState.Location, SaveData.CameraState.Location) < 1 &&
			(_data.CameraState.Rotation - SaveData.CameraState.Rotation).IsNearlyZero())
		{
			return;
		}
	}

	SavedCameraPawnData.Insert(SaveData, 0);
	CameraListAdded.Broadcast(SavedCameraPawnData[0]);

	ScreenshotHandle = GetWorld()->GetGameViewport()->OnScreenshotCaptured().AddLambda(
		[this, TargetThumbnail = SaveData.CameraState.ThumbnailImage](int32 Width, int32 Height, const TArray<FColor>& Colors)
		{
			if (TargetThumbnail)
			{
				TArray<uint8> RawColorData;
				RawColorData.AddDefaulted(Colors.Num() * 4);
				FMemory::Memcpy(RawColorData.GetData(), Colors.GetData(), RawColorData.Num());

				FTexture2DDynamicResource* TextureResource = static_cast<FTexture2DDynamicResource*>(TargetThumbnail->GetResource());
				if (TextureResource)
				{
					ENQUEUE_RENDER_COMMAND(FWriteRawDataToTexture)(
						[TextureResource, RawData = MoveTemp(RawColorData)](FRHICommandListImmediate& RHICmdList)
						{
							TextureResource->WriteRawToTexture_RenderThread(RawData);
						});
				}
			}

			GetWorld()->GetGameViewport()->OnScreenshotCaptured().Remove(ScreenshotHandle);
		});

	GScreenshotResolutionX = 192;
	GScreenshotResolutionY = 108;
	GetWorld()->GetGameViewport()->Viewport->TakeHighResScreenShot();
}

void UDSCameraSubsystem::DelIndexSaveInfo(FString UUID)
{
	for (int32 index = 0; index < SavedCameraPawnData.Num(); ++index)
	{
		if (SavedCameraPawnData[index].UUID == UUID)
		{
			FDSCameraData Data = SavedCameraPawnData[index];
			SavedCameraPawnData.RemoveAt(index);
			CameraListDeleted.Broadcast(Data);
		}
	}
}

void UDSCameraSubsystem::SetSavedCameraData(const TArray<FDSCameraData>& InData)
{
	SavedCameraPawnData = InData;

	for (auto& Element : SavedCameraPawnData)
	{
		CameraListAdded.Broadcast(Element);
	}
}

const TArray<FDSCameraData>& UDSCameraSubsystem::GetSavedCameraData() const
{
	return SavedCameraPawnData;
}

const FVector2D& UDSCameraSubsystem::GetMoveSpeed() const
{
	return CameraMoveSpeed;
}

void UDSCameraSubsystem::SetMoveSpeed(const FVector2D& InSpeed)
{
	CameraMoveSpeed = InSpeed;
	UDSConfigSubsystem::GetInstance()->SetValue_CameraSettingSpeed(InSpeed);
}

bool UDSCameraSubsystem::IsViewLocked()
{
	return bViewLocked;
}

void UDSCameraSubsystem::SetViewLocked(bool Flag)
{
	bViewLocked = Flag;
	UDSConfigSubsystem::GetInstance()->SetValue_CameraSettingViewLocked(Flag);

	if (OnCameraViewLocked_Handle.IsBound())
	{
		OnCameraViewLocked_Handle.Broadcast(Flag);
	}
}

float UDSCameraSubsystem::GetFOV()
{
	return CameraFOV;
}

void UDSCameraSubsystem::SetFOV(float InFOV)
{
	CameraFOV = InFOV;
	ACameraPawn* Pawn = ADesignStationController::Get()->GetDSPawn();
	if (Pawn == nullptr)
	{
		return;
	}
	Pawn->SetCameraFOV(InFOV);
	UDSConfigSubsystem::GetInstance()->SetValue_CameraSettingInFOV(InFOV);
}

double UDSCameraSubsystem::GetCameraHeight()
{
	ACameraPawn* Pawn = ADesignStationController::Get()->GetDSPawn();
	if (Pawn == nullptr)
	{
		return 0.0f;
	}

	double H = Pawn->GetActorLocation().Z;
	return Pawn->GetActorLocation().Z;
}

void UDSCameraSubsystem::SetCameraHeight(double InHeight)
{
	ACameraPawn* Pawn = ADesignStationController::Get()->GetDSPawn();
	if (Pawn == nullptr)
	{
		return;
	}
	FVector PawnLocation = Pawn->GetActorLocation();
	PawnLocation.Z = InHeight;
	if (Pawn->GetCurrentCameraType() != ECameraType::EXYPlan2D && Pawn->GetCurrentCameraType() != ECameraType::EXYPlan2D_Ceil)
	{
		Pawn->MoveDirect(PawnLocation);
	}
}

double UDSCameraSubsystem::GetCameraPitch()
{
	if (ADesignStationController::Get()->PlayerCameraManager)
	{
		return ADesignStationController::Get()->PlayerCameraManager->GetCameraRotation().Pitch;
	}
	return 0.0f;
}

void UDSCameraSubsystem::SetCameraPitch(double InAngle)
{
	if (ADesignStationController::Get()->PlayerCameraManager)
	{
		FRotator Rotator = ADesignStationController::Get()->PlayerCameraManager->GetCameraRotation();
		Rotator.Pitch = InAngle;

		ADesignStationController::Get()->SetControlRotation(Rotator);
	}
}

void UDSCameraSubsystem::RefreshCameraPosition(float DeltPer)
{
	if (DeltPer < 0.f)
	{
		return;
	}
	// 现在的算法是
	// 根据Pawn当前的位置，算出在 100m X 100m的框体内，相对于 0,0点的偏差值，占最大距离的百分比
	ACameraPawn* Pawn = ADesignStationController::Get()->GetDSPawn();
	if (Pawn == nullptr)
	{
		return;
	}

	auto CurrentCameraType = Pawn->GetCurrentCameraType();
	if (CurrentCameraType == ECameraType::EXYPlan2D || CurrentCameraType == ECameraType::EXYPlan2D_Ceil)
	{
		float CurrentWidth = CameraData_2D_Default_Width;
		if (DeltPer == 1.f)
		{
			CurrentWidth = CameraData_2D_MaxWidth;
		}
		else if (DeltPer <= 100.f)
		{
			CurrentWidth = FMath::Lerp<float>(CameraData_2D_MAX_HEIGHT, CameraData_2D_Default_Width, DeltPer / 100);
		}
		else
		{
			CurrentWidth = (CameraData_2D_Default_Width - CameraData_2D_MinWidth) / DeltPer * 100.f + CameraData_2D_MinWidth;
		}
		Pawn->SetOrthoWidth(CurrentWidth);
	}
	else if (CurrentCameraType == ECameraType::EDollHouse)
	{
		float CurrentHeight = CameraData_3D_Defalut_Distance;
		if (DeltPer == 1.f)
		{
			CurrentHeight = CameraData_3D_MAX_Distance;
		}
		else if (DeltPer <= 100.f)
		{
			CurrentHeight = FMath::Lerp<float>(CameraData_3D_MAX_Distance, CameraData_3D_Defalut_Distance, DeltPer / 100);
		}
		else
		{
			CurrentHeight = (CameraData_3D_Defalut_Distance - CameraData_3D_MIN_Distance) / DeltPer * 100.f + CameraData_3D_MIN_Distance;
		}
		Pawn->SetCameraLength(CurrentHeight);
	}
	/*int32 Height = 0;
	if (Pawn->GetCurrentCameraType() == ECameraType::EXYPlan2D_Ceil ||
		Pawn->GetCurrentCameraType() == ECameraType::EXYPlan2D)
	{
		int DeltHeight = - (DeltPer * (CameraData_2D_MIN_HEIGHT - CameraData_2D_MAX_HEIGHT) / 100);
		Pawn->SetHeight(Pawn->GetActorLocation().Z + DeltHeight);
		CameraInfoUpdate.Broadcast();
	}
	else
	{
		FVector Location = Pawn->GetActorLocation();
		FVector NewPos = Location + Pawn->GetActorForwardVector() * DeltPer * CameraData_MAX_WIDTH / 100;

		if ((NewPos.X * NewPos.X) + (NewPos.Y * NewPos.Y) <= CameraData_MAX_WIDTH * CameraData_MAX_WIDTH &&
			FMath::Abs(NewPos.Z) < CameraData_MAX_HEIGHT)
		{
			Pawn->SetActorLocation(NewPos);
		}
		else
		{
			Location.Z = FMath::Clamp(Location.Z, CameraData_MIN_HEIGHT, CameraData_MAX_HEIGHT);

			if (Location.X * Location.X + Location.Y * Location.Y > CameraData_MAX_WIDTH * CameraData_MAX_WIDTH)
			{
				Location.X = Location.X * FMath::Sqrt(CameraData_MAX_WIDTH * CameraData_MAX_WIDTH / (Location.X * Location.X + Location.Y * Location.Y));
				Location.Y = Location.Y * FMath::Sqrt(CameraData_MAX_WIDTH * CameraData_MAX_WIDTH / (Location.X * Location.X + Location.Y * Location.Y));
			}


			Pawn->SetActorLocation(NewPos);
		}

		CameraInfoUpdate.Broadcast();
	}*/
}

double UDSCameraSubsystem::CalcCameraHeightPer()
{
	ACameraPawn* Pawn = ADesignStationController::Get()->GetDSPawn();
	if (Pawn == nullptr)
	{
		return 0.0f;
	}

	if (Pawn->GetCurrentCameraType() == ECameraType::EXYPlan2D_Ceil ||
		Pawn->GetCurrentCameraType() == ECameraType::EXYPlan2D)
	{
		double Height = Pawn->GetActorLocation().Z;
		return 100.0 - (Height - CameraData_2D_MIN_HEIGHT) * 100 / (CameraData_2D_MAX_HEIGHT - CameraData_2D_MIN_HEIGHT);
	}
	return 0.0f;
}

double UDSCameraSubsystem::CalcCameraPosPer()
{
	ACameraPawn* Pawn = ADesignStationController::Get()->GetDSPawn();
	if (Pawn == nullptr)
	{
		return 0.0f;
	}

	if (Pawn->GetCurrentCameraType() == ECameraType::EDollHouse ||
		Pawn->GetCurrentCameraType() == ECameraType::EWalk)
	{
		FVector Location = Pawn->GetActorLocation();
		return 100.0 - FVector::Dist2D(Location, FVector::ZeroVector) * 100 / (CameraData_MAX_WIDTH);
	}
	return 0.0f;
}

float UDSCameraSubsystem::GetCameraDistanPer()
{
	ACameraPawn* Pawn = ADesignStationController::Get()->GetDSPawn();
	if (Pawn == nullptr)
	{
		return 1.0f;
	}
	float Percentage = 1.f;
	ECameraType CurrentCameraType = Pawn->GetCurrentCameraType();
	if (CurrentCameraType == ECameraType::EXYPlan2D || CurrentCameraType == ECameraType::EXYPlan2D_Ceil)
	{
		auto CurrentWidth = Pawn->GetOrhthWidth();
		if (FMath::IsNearlyEqual(CurrentWidth, CameraData_2D_MaxWidth))
		{
			Percentage = 1.f;
		}
		else if (CurrentWidth >= CameraData_2D_Default_Width)
		{
			Percentage = ((CameraData_2D_MaxWidth - CurrentWidth) / (CameraData_2D_MaxWidth - CameraData_2D_Default_Width)) * 100.f;
		}
		else
		{
			Percentage = ((CameraData_2D_Default_Width - CameraData_2D_MinWidth) / (CurrentWidth - CameraData_2D_MinWidth)) * 100.f;
		}
		//Percentage =(Pawn->GetOrhthWidth() - CameraData_2D_MIN_HEIGHT) / (CameraData_2D_MAX_HEIGHT - CameraData_2D_MIN_HEIGHT)*100.f;
	}
	else if (CurrentCameraType == ECameraType::EDollHouse)
	{
		auto CurrentWidth = Pawn->GetCameraArmLength();
		if (FMath::IsNearlyEqual(CurrentWidth, CameraData_3D_MAX_Distance))
		{
			Percentage = 1.f;
		}
		else if (CurrentWidth >= CameraData_3D_Defalut_Distance)
		{
			Percentage = ((CameraData_3D_MAX_Distance - CurrentWidth) / (CameraData_3D_MAX_Distance - CameraData_3D_Defalut_Distance)) * 100.f;
		}
		else
		{
			Percentage = ((CameraData_3D_Defalut_Distance - CameraData_3D_MIN_Distance) / (CurrentWidth - CameraData_3D_MIN_Distance)) * 100.f;
		}
	}
	else
	{
		return 1.0f;
	}
	return Percentage;
}

ECameraType UDSCameraSubsystem::GetCameraType() const
{
	return CurCameraType;
}

void UDSCameraSubsystem::ChangeCameraType(ECameraType InType, bool bInitialized /*= false*/)
{
	if (InType == ECameraType::EXYPlan2D_Ceil)
	{
		//InType = ECameraType::EDollHouse;
	}
	if (CurCameraType == InType && !bInitialized)
	{
		return;
	}

	ACameraPawn* CameraPawn = ADesignStationController::Get()->GetPawn<ACameraPawn>();
	if (CameraPawn == nullptr)
	{
		return;
	}
	if (!bInitialized)
	{
		SavePreCameraState(CurCameraType, false);

		//判断主相机要切换到的类型，来设置小地图相机类型，只有跨2D、3D的切换才需要切小地图
		if ((CurCameraType == ECameraType::EXYPlan2D && InType != ECameraType::EXYPlan2D_Ceil)
			|| (CurCameraType == ECameraType::EXYPlan2D_Ceil && InType != ECameraType::EXYPlan2D)
			|| (CurCameraType == ECameraType::EWalk && InType != ECameraType::EDollHouse)
			|| (CurCameraType == ECameraType::EDollHouse && InType != ECameraType::EWalk))
		{
			//保存小地图相机当前相机数据
			SavePreCameraState(CurMiniMapCameraType, true);
			//设置小地图相机类型为当前主相机类型
			CurMiniMapCameraType = CurCameraType;

			//切换小地图相机
			MiniMapCapture->ChangeCameraType(CurMiniMapCameraType, PreCameraStates[CurMiniMapCameraType]);
		}
		//设置主相机要切换的类型
		CurCameraType = InType;
	}
	else
	{
		CurCameraType = InType;

		if (CurCameraType == ECameraType::EXYPlan2D || CurCameraType == ECameraType::EXYPlan2D_Ceil)
		{
			CurMiniMapCameraType = ECameraType::EDollHouse;
		}
		else
		{
			CurMiniMapCameraType = ECameraType::EXYPlan2D;
		}
		//切换小地图相机
		MiniMapCapture->ChangeCameraType(CurMiniMapCameraType, PreCameraStates[CurMiniMapCameraType]);
	}

	CameraPawn->ChangeCameraType(CurCameraType, PreCameraStates[CurCameraType]);

	CameraTypeDelegate.Broadcast(static_cast<int32>(InType));

	// auto StateType = UDSMVCSubsystem::GetInstance()->GetStateType();
	// bool bHidePendant = StateType == EDSFSMState::FSM_Sink;

	bool bHidePendant = UDSFSMLibrary::ShouldHiddenPendantByState();
	if (!bHidePendant)
	{
		bHidePendant = UDSFSMLibrary::ShouldHiddenPendantByCameraState(UDSMVCSubsystem::GetInstance()->GetCurrentModel(), InType == ECameraType::EXYPlan2D || InType == ECameraType::EXYPlan2D_Ceil);
		if (bHidePendant)
		{
			UDSMVCSubsystem::GetInstance()->SetCurrentModel(nullptr);

		}
		else
		{
			UDSPendantSubsystem::GetInstance()->OnPendantEntrance_Show(
				UDSMVCSubsystem::GetInstance()->GetCurrentModel(), 
				FDSBroadcastMarkData::NotBroadcastToMVCMark, 
				ADesignStationController::Get()->Is2DScene(), 
				true
			);
		}
	}

	//切换相机视角，都要去根据当前选中的model，去刷新小工具体条
	if (UDSMVCSubsystem::GetInstance()->GetCurrentModel() && UDSUISubsystem::GetInstance()->GetFunctionBarBoxWidget())
	{
		if (bHidePendant)
		{
			UDSUISubsystem::GetInstance()->HiddenUI(false,true);
		}
		else
		{
			UDSUISubsystem::GetInstance()->GetFunctionBarBoxWidget()->SwitchButtons(UDSMVCSubsystem::GetInstance()->GetCurrentModel(), false);
		}
	}

	if (!bInitialized)
	{
		UDSMVCSubsystem::GetInstance()->SwitchCameraType(ECameraType::EXYPlan2D == CurCameraType);
		UDSDrawingSubsystem::GetInstance()->SetDefaultHidden(CurCameraType != ECameraType::EXYPlan2D);
		UDSDrawingSubsystem::GetInstance()->Drawing();
		if (bHidePendant)
		{
			UDSPendantSubsystem::GetInstance()->HidePendant();
			UDSDrawingSubsystem::GetInstance()->HideAuxiliaryLine();
		}
		else
		{
			UDSPendantSubsystem::GetInstance()->RefreshPendant(UDSMVCSubsystem::GetInstance()->GetCurrentModel(), FDSModelExecuteType::ExecuteAll, FDSBroadcastMarkData::NotBroadcastToMVCMark);
		}
	}

	//视野切换后处理小地图对象隐藏
	TArray<EDSModelType> MiniMapHiddenModelTypes;
	switch (CurMiniMapCameraType)
	{
	case ECameraType::EXYPlan2D:
	{
		MiniMapHiddenModelTypes = { EDSModelType::E_House_Roof,EDSModelType::E_HouseWallPathPoint,
			EDSModelType::E_RoofArea, EDSModelType::E_Furniture_MoldingCeiling ,
			 };
	}
		break;
	case ECameraType::EXYPlan2D_Ceil:
	{
		MiniMapHiddenModelTypes = { EDSModelType::E_House_Roof,EDSModelType::E_HouseWallPathPoint};
	}
	break;
	case ECameraType::EDollHouse:
	{
		MiniMapHiddenModelTypes = { EDSModelType::E_House_Roof,EDSModelType::E_HouseWallPathPoint,EDSModelType::E_RoofArea,
		EDSModelType::E_House_Door,EDSModelType::E_House_Window };
	}
	break;
	case ECameraType::EWalk:
	{
		MiniMapHiddenModelTypes = { EDSModelType::E_HouseWallPathPoint,
		EDSModelType::E_House_Door,EDSModelType::E_House_Window };
	};
	break;
	default:
		break;
	}

	auto MiniMapHiddenModels = UDSMVCSubsystem::GetInstance()->GetModels(MiniMapHiddenModelTypes);
	MiniMapCapture->GetCaptureComponent2D()->HiddenActors.Reset();
	for (auto& Iter: MiniMapHiddenModels)
	{
		if (IsValid(Iter) && IsValid(Iter->GetOwnedView()))
		{
			MiniMapCapture->AddHiddenActor(Iter->GetOwnedView());
		}
	}
}

void UDSCameraSubsystem::UpdateCameraStateBySavedID(const FString& InStateID)
{
	ACameraPawn* CameraPawn = ADesignStationController::Get()->GetPawn<ACameraPawn>();
	if (CameraPawn == nullptr)
	{
		return;
	}
	for (auto Iter : SavedCameraPawnData)
	{
		if (Iter.UUID.Equals(InStateID))
		{
			if (CurCameraType == Iter.CameraType)
			{
				//SavePreCameraState(CurCameraType, false);
				CameraPawn->ApplyNewCameraState(Iter.CameraState);
				CameraInfoUpdate.Broadcast();
			}
			else
			{
				SavePreCameraState(CurCameraType, false);
				//判断主相机要切换到的类型，来设置小地图相机类型，只有跨2D、3D的切换才需要切小地图
				if ((CurCameraType == ECameraType::EXYPlan2D && Iter.CameraType != ECameraType::EXYPlan2D_Ceil)
					|| CurCameraType == ECameraType::EXYPlan2D_Ceil && Iter.CameraType != ECameraType::EXYPlan2D
					|| CurCameraType == ECameraType::EWalk && Iter.CameraType != ECameraType::EDollHouse
					|| CurCameraType == ECameraType::EDollHouse && Iter.CameraType != ECameraType::EWalk)
				{
					//设置小地图相机类型为当前主相机类型
					CurMiniMapCameraType = CurCameraType;

					//切换小地图相机
					MiniMapCapture->ChangeCameraType(CurMiniMapCameraType, PreCameraStates[CurMiniMapCameraType]);
				}
				CurCameraType = Iter.CameraType;
				CameraPawn->ChangeCameraType(Iter.CameraType, Iter.CameraState);
				CameraTypeDelegate.Broadcast(static_cast<int32>(Iter.CameraType));
			}
		}
	}
}

void UDSCameraSubsystem::SwitchCameraToNext()
{
	int32 NextType = (static_cast<int32>(CurCameraType)) + 1;
	if (NextType > static_cast<int32>(ECameraType::EWalk))
	{
		NextType = static_cast<int32>(ECameraType::EXYPlan2D);
	}
	ChangeCameraType(static_cast<ECameraType>(NextType));
}

void UDSCameraSubsystem::SetMouseInMiniMap(bool bNewValue)
{
	bMouseInMiniMap = bNewValue;
}

void UDSCameraSubsystem::OnModelExecuteCommandComplete(UDSBaseModel* InModel, const FDSModelExecuteType& InExecuteType)
{
	if (!IsValid(InModel) || InExecuteType != FDSModelExecuteType::ExecuteSpawn)
	{
		return;
	}
	if (InModel->GetModelType() == EDSModelType::E_House_Roof)
	{
		MiniMapCapture->AddHiddenActor(InModel->GetOwnedView());
	}
	else if((CurMiniMapCameraType == ECameraType::EDollHouse || CurMiniMapCameraType == ECameraType::EWalk))
	{
		EDSModelType InModelType = InModel->GetModelType();
		if (InModelType == EDSModelType::E_HouseWallPathPoint || InModelType == EDSModelType::E_House_Door ||
			InModelType == EDSModelType::E_House_Window)
		{
			MiniMapCapture->AddHiddenActor(InModel->GetOwnedView());
		}
	}
}

void UDSCameraSubsystem::OnMouseDragCamera(const FPointerEvent& CurrentMouseEvent)
{
	if (bMouseInMiniMap)
	{
		USceneCaptureComponent2D* CameraComp = MiniMapCapture->GetCaptureComponent2D();

		FVector2D CursorDelta = CurrentMouseEvent.GetCursorDelta() * 10.0f;
		
		if (CurMiniMapCameraType == ECameraType::EXYPlan2D || CurMiniMapCameraType == ECameraType::EXYPlan2D_Ceil)
		{
			FVector CurrentLoc = MiniMapCapture->GetActorLocation();
			FVector TargetPosition = CurrentLoc + CameraComp->GetRightVector() * -1.0f * CursorDelta.X + CameraComp->GetUpVector() * CursorDelta.Y;

			MiniMapCapture->MoveDirect(TargetPosition);
		}
		else if (CurMiniMapCameraType == ECameraType::EDollHouse)
		{
			FTransform CameraTransform = CameraComp->GetComponentTransform();
			FVector CurrentLoc = MiniMapCapture->GetActorLocation();
			FVector TargetPosition = CurrentLoc + CameraTransform.GetUnitAxis(EAxis::Y) * -1.0f * CursorDelta.X + CameraTransform.GetUnitAxis(EAxis::Z) * CursorDelta.Y;
			MiniMapCapture->MoveDirect(TargetPosition);
		}
		else
		{
			MiniMapCapture->MoveForward(CursorDelta.Y);
			MiniMapCapture->MoveRight(-CursorDelta.X);
		}
	}
	else
	{
		ACameraPawn* CameraPawn = ADesignStationController::Get()->GetDSPawn();
		if (CameraPawn == nullptr)
		{
			return;
		}
		
		if (CurCameraType == ECameraType::EXYPlan2D || CurCameraType == ECameraType::EXYPlan2D_Ceil)
		{
			FVector CurrentLoc = CameraPawn->GetActorLocation();
			FVector TargetPosition = CurrentLoc + CameraPawn->GetCameraComponent()->GetRightVector() * -1.0f * CurrentMouseEvent.GetCursorDelta().X + CameraPawn->GetCameraComponent()->GetUpVector() * CurrentMouseEvent.GetCursorDelta().Y;

			CameraPawn->MoveDirect(TargetPosition);
		}
		else if (CurCameraType == ECameraType::EDollHouse)
		{
			FTransform CameraTransform = CameraPawn->GetCameraComponent()->GetComponentTransform();
			FVector CurrentLoc = CameraPawn->GetActorLocation();
			FVector TargetPosition = CurrentLoc + CameraTransform.GetUnitAxis(EAxis::Y) * -1.0f * CurrentMouseEvent.GetCursorDelta().X + CameraTransform.GetUnitAxis(EAxis::Z) * CurrentMouseEvent.GetCursorDelta().Y;
			CameraPawn->MoveDirect(TargetPosition);
		}
		else
		{
			CameraPawn->MoveForward(CurrentMouseEvent.GetCursorDelta().Y);
			CameraPawn->MoveRight(-CurrentMouseEvent.GetCursorDelta().X);
		}
	}
}

void UDSCameraSubsystem::OnKeyBoard_Reset_Execute()
{
	ACameraPawn* Pawn = ADesignStationController::Get()->GetDSPawn();
	if (Pawn == nullptr)
	{
		return;
	}
	Pawn->ResetCamera();
}

void UDSCameraSubsystem::OnKeyBoard_CrtlAndD_Execute()
{
	SaveCurrentInfo();
}

void UDSCameraSubsystem::OnMoveForwardAndBack(float AxisValue)
{
	ACameraPawn* Pawn = ADesignStationController::Get()->GetDSPawn();
	if (Pawn == nullptr)
	{
		return;
	}
	Pawn->MoveForward(AxisValue);
}

void UDSCameraSubsystem::OnMoveLeftRight(float AxisValue)
{
	ACameraPawn* Pawn = ADesignStationController::Get()->GetDSPawn();
	if (Pawn == nullptr)
	{
		return;
	}
	Pawn->MoveRight(-AxisValue);
}

void UDSCameraSubsystem::OnMoveUpDown(float AxisValue)
{
	if (FMath::IsNearlyZero(AxisValue))
	{
		return;
	}
	ACameraPawn* Pawn = ADesignStationController::Get()->GetDSPawn();
	if (Pawn == nullptr)
	{
		return;
	}
	ECameraType CurrentCameraType = Pawn->GetCurrentCameraType();
	if (CurrentCameraType == ECameraType::EDollHouse || CurrentCameraType == ECameraType::EWalk)
	{
		/*double speed = UDSCameraSubsystem::GetInstance()->GetMoveSpeed().X / 1000;
		ZoomFactor = FMath::Clamp<float>(AxisValue, -1.0f, 1.0f);

		if (FMath::IsNearlyZero(ZoomFactor))
			return;

		CAMERA_Z_SLIDE(ZoomFactor * speed * 0.2 * Z_SLIDE_STEP)*/
		Pawn->MoveUp_World(AxisValue);
	}
}

void UDSCameraSubsystem::OnMouseWheelScroll(float AxisValue)
{
	ACameraPawn* Pawn = ADesignStationController::Get()->GetDSPawn();
	if (Pawn == nullptr)
	{
		return;
	}
	if (FMath::IsNearlyEqual(AxisValue, 0.0))
	{
		return;
	}
	if (bMouseInMiniMap)
	{
		if (MiniMapCapture == nullptr)
		{
			return;
		}
		if (CurMiniMapCameraType == ECameraType::EXYPlan2D || CurMiniMapCameraType == ECameraType::EXYPlan2D_Ceil)
		{
			MiniMapCapture->UpdateOriWidth(AxisValue * Z_SLIDE_STEP);
		}
		else if (CurMiniMapCameraType == ECameraType::EDollHouse)
		{
			MiniMapCapture->UpdateCameraLength(AxisValue * Z_SLIDE_STEP);
		}
		else
		{
			MiniMapCapture->MoveForward(-AxisValue * Z_SLIDE_STEP);
		}
	}
	else
	{
		if (CurCameraType == ECameraType::EXYPlan2D || CurCameraType == ECameraType::EXYPlan2D_Ceil)
		{
			Pawn->UpdateOriWidth(AxisValue * Z_SLIDE_STEP);
		}
		else if (CurCameraType == ECameraType::EDollHouse)
		{
			Pawn->UpdateCameraLength(AxisValue * Z_SLIDE_STEP);
		}
		else
		{
			Pawn->MoveForward(-AxisValue * Z_SLIDE_STEP);
		}
	}
}

void UDSCameraSubsystem::OnBeginRotateCamera(const FGeometry& MyGeometry, const FPointerEvent& InMouseEvent)
{
	RotateStartMouseEvent = InMouseEvent;

	if (!bMouseInMiniMap)
	{
		ACameraPawn* Pawn = ADesignStationController::Get()->GetDSPawn();
		RotateStartRotation = Pawn->GetControlRotation();	
	}
}

void UDSCameraSubsystem::OnRotateCamera(const FGeometry& MyGeometry, const FPointerEvent& InMouseEvent)
{
	if (IsViewLocked())
	{
		return;
	}
	
	if (bMouseInMiniMap)
	{
		if (CurMiniMapCameraType == ECameraType::EXYPlan2D || CurMiniMapCameraType == ECameraType::EXYPlan2D_Ceil)
		{
			return;
		}
		
		FVector2D Delta = InMouseEvent.GetCursorDelta();
		Delta.X = FMath::Clamp(Delta.X, -1.0f, 1.0f);
		Delta.Y = FMath::Clamp(Delta.Y, -1.0f, 1.0f);

		if (CurMiniMapCameraType == ECameraType::EDollHouse)
		{
			MiniMapCapture->Turn(Delta.X);
			MiniMapCapture->LookUp(-Delta.Y);
		}
		else
		{
			MiniMapCapture->Turn(-Delta.X);
			MiniMapCapture->LookUp(Delta.Y);
		}
	}
	else if (CurCameraType != ECameraType::EXYPlan2D && CurCameraType != ECameraType::EXYPlan2D_Ceil)
	{
		FVector2D StartPos = MyGeometry.AbsoluteToLocal(RotateStartMouseEvent.GetScreenSpacePosition());
		FVector2D CurrentPos = MyGeometry.AbsoluteToLocal(InMouseEvent.GetScreenSpacePosition());
	
		FVector2D MoveDelta = CurrentPos - StartPos;
		FVector2D RotateFactor = MoveDelta / (MyGeometry.GetAbsoluteSize() * 0.5f);

		FRotator RotationDelta;
		RotationDelta.Yaw = RotateFactor.X * 360.0f;
		RotationDelta.Pitch = -RotateFactor.Y * 360.0f;
		RotationDelta.Roll = 0.0f;

		FRotator ControlRotation = RotateStartRotation + RotationDelta;
		ADesignStationController::Get()->SetControlRotation(ControlRotation);	
	}
}

void UDSCameraSubsystem::OnEndRotateCamera(const FGeometry& MyGeometry, const FPointerEvent& InMouseEvent)
{
	RotateStartMouseEvent = FPointerEvent();
	RotateStartRotation = FRotator::ZeroRotator;
}

void UDSCameraSubsystem::OnInitialized()
{
	//获取配置数据
	DSSetting::CameraSetting::FCameraSettingInfo SettingInfo;
	UDSConfigSubsystem::GetInstance()->GetValue_CameraSetting(SettingInfo);
	CameraMoveSpeed = SettingInfo.Speed;
	bViewLocked = SettingInfo.bViewLocked;
	CameraFOV = SettingInfo.InFOV;
	PreCameraStates.Add(ECameraType::EDollHouse, FCameraState::GetDollHouseDefaultCameraState());
	PreCameraStates.Add(ECameraType::EXYPlan2D, FCameraState::GetXYPlanDefaultCameraState());
	PreCameraStates.Add(ECameraType::EXYPlan2D_Ceil, FCameraState::GetXYPlanDefaultCameraState());
	PreCameraStates.Add(ECameraType::EWalk, FCameraState::GetWallkDefaultCameraState());
	//生成小地图渲染相机
	MiniMapCapture = ADesignStationController::Get()->CreateMiniMapCaptureActor();
	ChangeCameraType(ECameraType::EXYPlan2D, true);
}

bool UDSCameraSubsystem::DeprojectMiniMapPositionToWorld(FVector2D& ScreenPosition, FVector& WorldPosition, FVector& WorldDirection)
{
	return DeprojectMiniMapPositionToWorldWithUV(MiniMapMouseUV, ScreenPosition, WorldPosition, WorldDirection);
}

bool UDSCameraSubsystem::DeprojectMiniMapPositionToWorldWithUV(const FVector2D& TargetUV, FVector2D& ScreenPosition, FVector& WorldPosition, FVector& WorldDirection)
{
	if (MiniMapCapture == nullptr)
	{
		false;
	}
	if (USceneCaptureComponent2D* SceneCaptureComponent2D = MiniMapCapture->GetCaptureComponent2D())
	{
		if (SceneCaptureComponent2D->TextureTarget)
		{
			FMinimalViewInfo ViewInfo;
			SceneCaptureComponent2D->GetCameraView(0.0f, ViewInfo);

			FMatrix ProjectionMatrix;
			if (SceneCaptureComponent2D->bUseCustomProjectionMatrix)
			{
				ProjectionMatrix = AdjustProjectionMatrixForRHI(SceneCaptureComponent2D->CustomProjectionMatrix);
			}
			else //
			{
				ProjectionMatrix = AdjustProjectionMatrixForRHI(ViewInfo.CalculateProjectionMatrix());
			}
			FMatrix InvProjectionMatrix = ProjectionMatrix.Inverse();

			// A view matrix is the inverse of the viewer's matrix, so an inverse view matrix is just the viewer's matrix.
			// To save precision, we directly compute the viewer's matrix, plus it also avoids the cost of the inverse.
			// The matrix to convert from world coordinate space to view coordinate space also needs to be included (this
			// is the transpose of the similar matrix used in CalculateViewProjectionMatricesFromMinimalView).
			FMatrix InvViewMatrix = FMatrix(
				FPlane(0, 1, 0, 0),
				FPlane(0, 0, 1, 0),
				FPlane(1, 0, 0, 0),
				FPlane(0, 0, 0, 1)) * FRotationTranslationMatrix(ViewInfo.Rotation, ViewInfo.Location);

			FIntPoint TargetSize = FIntPoint(SceneCaptureComponent2D->TextureTarget->SizeX, SceneCaptureComponent2D->TextureTarget->SizeY);

			FSceneView::DeprojectScreenToWorld(
				TargetUV * FVector2D(TargetSize),
				FIntRect(FIntPoint(0, 0), TargetSize),
				InvViewMatrix,
				InvProjectionMatrix,
				WorldPosition,
				WorldDirection);

			ScreenPosition = TargetUV * FVector2D(TargetSize);
			return true;
		}
	}
	// something went wrong, zero things and return false
	WorldPosition = FVector::ZeroVector;
	WorldDirection = FVector::ZeroVector;
	return false;
}

bool UDSCameraSubsystem::ProjectWorldLocationToMiniMap(const FVector& WorldLocation, FVector2D& ScreenLocation)
{
	if (MiniMapCapture == nullptr)
	{
		false;
	}
	if (USceneCaptureComponent2D* SceneCaptureComponent2D = MiniMapCapture->GetCaptureComponent2D())
	{
		if (SceneCaptureComponent2D->TextureTarget)
		{
			FMinimalViewInfo ViewInfo;
			SceneCaptureComponent2D->GetCameraView(0.0f, ViewInfo);

			FMatrix ProjectionMatrix;
			if (SceneCaptureComponent2D->bUseCustomProjectionMatrix)
			{
				ProjectionMatrix = AdjustProjectionMatrixForRHI(SceneCaptureComponent2D->CustomProjectionMatrix);
			}
			else //
			{
				ProjectionMatrix = AdjustProjectionMatrixForRHI(ViewInfo.CalculateProjectionMatrix());
			}
			FMatrix InvProjectionMatrix = ProjectionMatrix.Inverse();

			// A view matrix is the inverse of the viewer's matrix, so an inverse view matrix is just the viewer's matrix.
			// To save precision, we directly compute the viewer's matrix, plus it also avoids the cost of the inverse.
			// The matrix to convert from world coordinate space to view coordinate space also needs to be included (this
			// is the transpose of the similar matrix used in CalculateViewProjectionMatricesFromMinimalView).
			FMatrix InvViewMatrix = FMatrix(
				FPlane(0, 1, 0, 0),
				FPlane(0, 0, 1, 0),
				FPlane(1, 0, 0, 0),
				FPlane(0, 0, 0, 1)) * FRotationTranslationMatrix(ViewInfo.Rotation, ViewInfo.Location);

			FIntPoint TargetSize = FIntPoint(SceneCaptureComponent2D->TextureTarget->SizeX, SceneCaptureComponent2D->TextureTarget->SizeY);
			InvProjectionMatrix = FTranslationMatrix(-ViewInfo.Location)
				* FInverseRotationMatrix(ViewInfo.Rotation)
				* FMatrix(
					FPlane(0, 0, 1, 0),
					FPlane(1, 0, 0, 0),
					FPlane(0, 1, 0, 0),
					FPlane(0, 0, 0, 1)) * ProjectionMatrix;
			bool binView = FSceneView::ProjectWorldToScreen(WorldLocation, FIntRect(FIntPoint(0, 0), TargetSize), InvProjectionMatrix, ScreenLocation);
			return binView;
		}
	}
	return false;
}

bool UDSCameraSubsystem::ProjectWorldLocationToMiniMapWithDistance(const FVector& WorldLocation, FVector& ScreenLocation)
{
	FVector2D ScreecPos2D;
	bool bSuccess = ProjectWorldLocationToMiniMap(WorldLocation, ScreecPos2D);

	if (bSuccess)
	{
		USceneCaptureComponent2D* SceneCaptureComponent2D = MiniMapCapture->GetCaptureComponent2D();
		FMinimalViewInfo ViewInfo;
		SceneCaptureComponent2D->GetCameraView(0.0f, ViewInfo);

		ScreenLocation = FVector(ScreecPos2D.X, ScreecPos2D.Y, FVector::Dist(ViewInfo.Location, WorldLocation));
		return true;
	}
	return false;
}

void UDSCameraSubsystem::UpdataMiniMapMouseUV(const FVector2D& InNewValue)
{
	MiniMapMouseUV = InNewValue;
}

void UDSCameraSubsystem::GetMiniMapCameraLocation(FVector& OutLoccation) const
{
	if (MiniMapCapture)
	{
		OutLoccation = MiniMapCapture->GetActorLocation();
	}
}

void UDSCameraSubsystem::GetCameraPawnLocation(FVector& OutLoccation) const
{
	ACameraPawn* Pawn = ADesignStationController::Get()->GetDSPawn();
	if (Pawn == nullptr)
	{
		return;
	}
	OutLoccation = Pawn->GetActorLocation();
}

void UDSCameraSubsystem::GetCameraPawnState(FCameraState& OutCameraState) const
{
	ACameraPawn* Pawn = ADesignStationController::Get()->GetDSPawn();
	if (Pawn == nullptr)
	{
		return;
	}
	Pawn->GetCameraState(OutCameraState);
	// TODO: 在此处插入 return 语句
}

FVector UDSCameraSubsystem::GetCameraLocation() const
{
	if (ADesignStationController::Get()->PlayerCameraManager)
	{
		return ADesignStationController::Get()->PlayerCameraManager->GetCameraLocation();
	}
	return FVector::ZeroVector;
}

FRotator UDSCameraSubsystem::GetCameraRotation() const
{
	if (ADesignStationController::Get()->PlayerCameraManager)
	{
		return ADesignStationController::Get()->PlayerCameraManager->GetCameraRotation();
	}
	return FRotator::ZeroRotator;
}

FRotator UDSCameraSubsystem::GetControllerRotation() const
{
	return ADesignStationController::Get()->GetControlRotation();
}

void UDSCameraSubsystem::SetCameraPawnLocation(const FVector& Location, bool bUseZ)
{
	ACameraPawn* Pawn = ADesignStationController::Get()->GetDSPawn();
	if (Pawn == nullptr)
	{
		return;
	}
	if (bUseZ)
	{
		Pawn->MoveDirect(Location);
	}
	else
	{
		auto CurrentLocation = Pawn->GetActorLocation();
		CurrentLocation.X = Location.X;
		CurrentLocation.Y = Location.Y;
		Pawn->MoveDirect(CurrentLocation);
	}
}

void UDSCameraSubsystem::SetCameraArmLength(float NewLength)
{
	ACameraPawn* Pawn = ADesignStationController::Get()->GetDSPawn();
	if (Pawn == nullptr)
	{
		return;
	}
	Pawn->SetCameraLength(NewLength);
}

void UDSCameraSubsystem::SetControlRotation(FRotator NewRotation)
{
	//不循序相机在Roll上旋转
	NewRotation.Roll = 0.f;
	ADesignStationController::Get()->SetControlRotation(NewRotation);
}

FVector2D UDSCameraSubsystem::OnMiniMapViewSizeChange(const FVector2D& NewViewSize)
{
	int32 SizeX, SizeY;
	SizeX = FMath::RoundToInt(NewViewSize.X);
	SizeY = FMath::RoundToInt(NewViewSize.Y);
	float Size = NewViewSize.X / NewViewSize.Y;
	if (Size < 16.f / 9.f)
	{
		SizeY += SizeY % 2;
		SizeX = SizeY * 16 / 9;
	}
	else
	{
		SizeX += SizeX % 2;
		SizeY += (SizeX / 16 * 9) % 2;
	}
	MiniMapCapture->SetRenderTextureSize(SizeX, SizeY);
	return FVector2D(SizeX, SizeY);
}

void UDSCameraSubsystem::UpdateCameraDataByCenterAndExtents(const FVector2D& InCenter, const FVector2D& Extents)
{
	//TODO:设置所有状态下相机看向当前中心
	ACameraPawn* Pawn = ADesignStationController::Get()->GetDSPawn();
	if (Pawn == nullptr)
	{
		return;
	}

	const float CurrentFov = CurCameraType == ECameraType::EDollHouse ? Pawn->GetCameraFOV() : PreCameraStates[ECameraType::EDollHouse].FOV;
	float NewWidth = FMath::Max(Extents.X * 16.f / 9.f, Extents.Y) * 2.f;

	float Length = NewWidth / FMath::Tan(FMath::DegreesToRadians(CurrentFov / 2));

	FVector NewLocation = FVector(InCenter.X, InCenter.Y, 0);

	PreCameraStates[ECameraType::EXYPlan2D].OrthWidth = NewWidth;
	PreCameraStates[ECameraType::EXYPlan2D].Location = NewLocation;

	NewLocation.Z = FCameraState::GetDollHouseDefaultCameraState().Location.Z;
	PreCameraStates[ECameraType::EDollHouse].Location = NewLocation;
	PreCameraStates[ECameraType::EDollHouse].Rotation = FRotator(-45.f, -45.f, 0);
	PreCameraStates[ECameraType::EDollHouse].ArmLength = Length;

	NewLocation.Z = FCameraState::GetWallkDefaultCameraState().Location.Z;
	PreCameraStates[ECameraType::EWalk].Location = NewLocation;
	PreCameraStates[ECameraType::EWalk].Rotation = FRotator::ZeroRotator;

	if (CurCameraType == ECameraType::EXYPlan2D || CurCameraType == ECameraType::EXYPlan2D_Ceil)
	{
		NewLocation.Z = FCameraState::GetXYPlanDefaultCameraState().Location.Z;
	}
	else if (CurCameraType == ECameraType::EDollHouse)
	{
		NewLocation.Z = FCameraState::GetDollHouseDefaultCameraState().Location.Z;
	}
	else
	{
		NewLocation.Z = FCameraState::GetWallkDefaultCameraState().Location.Z;
	}
	Pawn->MoveDirect(NewLocation);
	if (CurCameraType == ECameraType::EXYPlan2D || CurCameraType == ECameraType::EXYPlan2D_Ceil)
	{
		Pawn->SetOrthoWidth(NewWidth);
	}
	else if (CurCameraType == ECameraType::EDollHouse)
	{
		SetControlRotation(FRotator(-45.f, -45.f, 0));
		Pawn->SetCameraLength(Length);
	}
	else
	{
		SetControlRotation(FRotator::ZeroRotator);
	}
	if (MiniMapCapture)
	{
		if (CurMiniMapCameraType == ECameraType::EXYPlan2D || CurMiniMapCameraType == ECameraType::EXYPlan2D_Ceil)
		{
			NewLocation.Z = FCameraState::GetXYPlanDefaultCameraState().Location.Z;
		}
		else if (CurMiniMapCameraType == ECameraType::EDollHouse)
		{
			NewLocation.Z = FCameraState::GetDollHouseDefaultCameraState().Location.Z;
		}
		else
		{
			NewLocation.Z = FCameraState::GetWallkDefaultCameraState().Location.Z;
		}
		MiniMapCapture->MoveDirect(NewLocation);
		if (CurMiniMapCameraType == ECameraType::EXYPlan2D || CurCameraType == ECameraType::EXYPlan2D_Ceil)
		{
			MiniMapCapture->SetOrthoWidth(NewWidth);
		}
		else if (CurMiniMapCameraType == ECameraType::EDollHouse)
		{
			MiniMapCapture->SetCameraLength(Length);
			MiniMapCapture->SetViewRotation(FRotator(-45.f, -45.f, 0));
		}
		else
		{
			MiniMapCapture->SetViewRotation(FRotator::ZeroRotator);
		}
	}
}

void UDSCameraSubsystem::SpawnCaptureActor()
{
	if (UWorld* World = GetWorld())
	{
		CaptureActor = World->SpawnActor<AMultiLocationCapture>();
	}
}

void UDSCameraSubsystem::StartCaptureActor()
{
	if (UWorld* World = GetWorld())
	{
		if (CaptureActor)
		{
			CaptureActor->StartCapture();
		}
		else
		{
			CaptureActor = World->SpawnActor<AMultiLocationCapture>();
			if (CaptureActor)
			{
				CaptureActor->StartCapture();
			}
		}
	}
}

FString UDSCameraSubsystem::CreateCpatureByModel(UDSBaseModel* InModel)
{
	if (!IsValid(InModel))
	{
		return "";
	}
	FVector Center; FVector Extents; FQuat Rotation;
	TArray<UPrimitiveComponent*> ShowOnlyComponnets;
	TArray<AActor*> ShowOnlyActors;
	InModel->GetModelCollectionCaptureComponentsAndActors(ShowOnlyComponnets, ShowOnlyActors);
	InModel->GetModelOrientedBoundingBox(Center,Extents,Rotation);
	if (UWorld* World = GetWorld())
	{
		if (!CollectionCaptureActor)
		{
			CollectionCaptureActor = World->SpawnActor<ACollectionSceneCapture2D>();
		}
		return CollectionCaptureActor->CaptureSpecificActors(ShowOnlyComponnets, ShowOnlyActors, InModel->GetOwnedView(), Center,Extents, Rotation);
	}
	return "";
}

void UDSCameraSubsystem::SavePreCameraState(const ECameraType InCameraType, bool bMiniMap)
{
	if (!bMiniMap)
	{
		auto Pawn = ADesignStationController::Get()->GetDSPawn();
		if (Pawn)
		{
			Pawn->GetCameraState(PreCameraStates[InCameraType]);
		}
	}
	else
	{
		//TODO:
		/*获取小地图渲染相机数据设置到这里*/
		if (MiniMapCapture)
		{
			MiniMapCapture->GetCameraState(PreCameraStates[InCameraType]);
		}
	}
}

//相机视口户型居中
void UDSCameraSubsystem::AdjustViewportToCenter()
{
	FVector RoomCenter = FVector::ZeroVector;
	float OtrhZoom = 0.0f;

	if (CurCameraType == ECameraType::EDollHouse || CurCameraType == ECameraType::EWalk)
	{
		FVector pos = FVector::ZeroVector;
		float maxArea = 0;
		for (auto& R : UDSMVCSubsystem::GetInstance()->GetModels(EDSModelType::E_House_Area))
		{
			if (R)
			{
				UDSHouseAreaModel* Room = Cast<UDSHouseAreaModel>(R);
				//float area, perimeter;
			}
		}
		return;
	}

	if (GetHomeCenter(RoomCenter, OtrhZoom))
	{
		FCameraState InNewState = FCameraState::GetXYPlanDefaultCameraState();
		InNewState.Location = RoomCenter;
		InNewState.OrthWidth = OtrhZoom;
		// 将图框居中
		ADesignStationController::Get()->GetDSPawn()->ApplyNewCameraState(InNewState);
	}
}

//  获得户型中心点
bool UDSCameraSubsystem::GetHomeCenter(FVector& OutPos, float& OrthoZoom)
{
	FBox RoomBound(ForceInit);

	for (auto& R : UDSMVCSubsystem::GetInstance()->GetModels(EDSModelType::E_House_Area))
	{
		if (R)
		{
			UDSHouseAreaModel* Room = Cast<UDSHouseAreaModel>(R);
			FBox Bound(ForceInit);
			for (auto& FOL : Room->GetOutlineInfo().BottomOutline)
			{
				Bound += FOL;
			}

			RoomBound += Bound;
		}
	}

	if (!RoomBound.IsValid)
	{
		return false;
	}

	OutPos = RoomBound.GetCenter();
	OutPos.Z = 10000;

	//1、获取视口大小
	FVector2D ViewportSize;
	GetWorld()->GetGameViewport()->GetViewportSize(ViewportSize);

	// 计算相机zoom
	float Offset = 100.f;
	float MinY = RoomBound.Min.Y - Offset;
	float MaxY = RoomBound.Max.Y + Offset;
	float MinX = RoomBound.Min.X - Offset;
	float MaxX = RoomBound.Max.X + Offset;

	float YScale = (FMath::Abs(MaxY - MinY)) / ViewportSize.Y;
	float XScale = (FMath::Abs(MaxX - MinX)) / ViewportSize.X;
	float OrthoZoomScale = YScale > XScale ? ViewportSize.Y : ViewportSize.X;
	float TargetOrthZoom = YScale > XScale ? FMath::Abs(MaxY - MinY) : FMath::Abs(MaxX - MinX);

	if (TargetOrthZoom != 0 && OrthoZoomScale != 0)
	{
		OrthoZoom = (TargetOrthZoom) * FMath::Max(ViewportSize.X, ViewportSize.Y) / OrthoZoomScale;
	}
	else
	{
		return false;
	}

	return true;
}
