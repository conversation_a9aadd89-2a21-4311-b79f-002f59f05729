#pragma once

#include "CoreMinimal.h"
#include "WorldSubsystem.h"
#include "DSPipeSubsystem.generated.h"

UENUM(BlueprintType)
enum class EPipeMessageType : uint8
{
	Init = 0,
	RefreshToken,
	RefreshFrameTitle
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnReceivedRefreshTokenMessageDelegate, const FString&, NewToken);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnReceivedUploadConfigMessageDelegate, const FString&, BucketName, const FString&, AccessKey, const FString&, SecretKey);

UCLASS()
class DESIGNSTATION_API UDSPipeSubsystem : public UTickableWorldSubsystem
{
	GENERATED_BODY()

public:
	UDSPipeSubsystem();

	static UDSPipeSubsystem* GetInstance();

	virtual void Initialize(FSubsystemCollectionBase& Collection) override;
	virtual void Deinitialize() override;

	virtual void Tick(float DeltaTime) override;
	virtual TStatId GetStatId() const override;

	UFUNCTION(BlueprintCallable)
	void RefreshFrameTitle(const FString& NewTitle);

	UFUNCTION(BlueprintCallable)
	void WriteMessageToServer(const FString& Message);

	FOnReceivedRefreshTokenMessageDelegate& OnReceivedRefreshTokenMessageEvent();
	FOnReceivedUploadConfigMessageDelegate& OnReceivedUploadConfigMessageEvent();

protected:
	void ProcessServerMessage(const FString& Message);
	
protected:
	void* ClientPipe;

	TArray<FString> PendingMessage;

	FString ClientId;
	FString ServerPipeName;

	UPROPERTY(BlueprintAssignable)
	FOnReceivedRefreshTokenMessageDelegate OnReceivedRefreshTokenMessage;

	UPROPERTY(BlueprintAssignable)
	FOnReceivedUploadConfigMessageDelegate OnReceivedUploadConfigMessage;

private:
	static UDSPipeSubsystem* Instance;
};
