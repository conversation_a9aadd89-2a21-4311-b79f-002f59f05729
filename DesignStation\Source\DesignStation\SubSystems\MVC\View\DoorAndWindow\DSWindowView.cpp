﻿#pragma once

#include "DSWindowView.h"
#include "MaterialInstance.h"
#include "MaterialInstanceDynamic.h"
#include "SubSystems/MVC/Core/Macro.h"
#include "SubSystems/MVC/Library/DSToolLibrary.h"
#include "ProceduralMeshComponent.h"
#include "SubSystems/MVC/Core/Property/CommonProperty.h"
#include "SubSystems/Drawing/DSDrawingSubsystem.h"
#include "SubSystems/MVC/Core/Property/DoorAndWindowProperty.h"
#include "SubSystems/MVC/Model/DoorAndWindow/DSWindowModel.h"
#include "SubSystems/MVC/View/Abstruct/DSPlaneView.h"

extern const FLinearColor sRGB_585858;
extern const FLinearColor sRGB_4191F8;
extern const FLinearColor sRGB_f96466;

extern const FString MESH_WINDOW_BODY_2D;
extern const FString MESH_DOOR_BOARD_2D;
extern const FString MESH_WINDOW_BAY_2D;

ADSWindowView::ADSWindowView()
{
	BoardComponent = CreateDefaultSubobject<UProceduralMeshComponent>(TEXT("BoardComponent"));
	BoardComponent->AttachToComponent(RootComponent, FAttachmentTransformRules::KeepRelativeTransform);
	BoardComponent->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);

	BoardComponent1 = CreateDefaultSubobject<UProceduralMeshComponent>(TEXT("BoardComponent1"));
	BoardComponent1->AttachToComponent(RootComponent, FAttachmentTransformRules::KeepRelativeTransform);
	BoardComponent1->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);

	Z2D = 500.f;
}

void ADSWindowView::RealSpawnViewLogic(UDSBaseModel* InModel)
{
	Super::RealSpawnViewLogic(InModel);

	if (Model)
	{
		RefreshTopOutlineView();
		auto Property = static_cast<FDSDoorAndWindowProperty*>(Model->GetProperty());
		if (Model->GetMeshInfo().MeshDatas.Contains(MESH_WINDOW_BODY_2D))
		{
			auto Mesh = Model->GetMeshInfo().MeshDatas[MESH_WINDOW_BODY_2D];
			TArray<FProcMeshTangent> Tangents;
			Tangents.SetNumZeroed(Mesh.Vertex.Num());
			TArray<FColor> VertexColor;
			MeshComponent->ClearMeshSection(0);
			MeshComponent->CreateMeshSection(0, Mesh.Vertex, Mesh.Indices, Mesh.Normals, Mesh.Uvs, VertexColor, Tangents, true);
			MeshComponent->SetRelativeLocation(FVector(0, 0, Z2D));

			BoardComponent->ClearMeshSection(0);
			if (Property->DoorAndWindowType == EDoorAndWindowType::E_Window_Bay && Model->GetMeshInfo().MeshDatas.Contains(MESH_WINDOW_BAY_2D))
			{
				auto BoardMesh = Model->GetMeshInfo().MeshDatas[MESH_WINDOW_BAY_2D];
				BoardComponent->CreateMeshSection(0, BoardMesh.Vertex, BoardMesh.Indices, BoardMesh.Normals, BoardMesh.Uvs, VertexColor, Tangents, true);
				BoardComponent->SetRelativeLocation(FVector(0, 0, Z2D));
			}
		}

		RefreshAllMaterial();

		UDSDrawingSubsystem::GetInstance()->AddModel(Model, FLinearColor::Red);
	}
}

void ADSWindowView::RealUpdateViewLogic(UDSBaseModel* InModel)
{
	UDSWindowModel* NewModel = Cast<UDSWindowModel>(InModel);
	if (OBJECT_VALID_FOR_USE(NewModel) && OBJECT_VALID_FOR_USE(Model))
	{
		checkf(Model->GetUUID().Equals(NewModel->GetUUID()), TEXT("ADSMoldingCeilingView::UpdateView --- No Equal Model"));

		Model->ShallowCopy(NewModel);

		RefreshMesh();

		GetModel()->GetProperty()->SetTransformProperty(InModel->GetProperty()->GetTransformProperty());
		SetActorTransform(GetModel()->GetProperty()->GetTransformProperty().ToUETransform());

		RefreshCupboard();
		RefreshAllMaterial();
	}
}

void ADSWindowView::RealTransformViewLogic(UDSBaseModel* InModel)
{
	Super::RealTransformViewLogic(InModel);

	RefreshMesh();
	RefreshCupboardTransform();
}

void ADSWindowView::RealHoverViewLogic(UDSBaseModel* InModel)
{
	AttachedActorHandleAction(InModel, FDSModelExecuteType::ExecuteHover);
	FLinearColor Color = UDSToolLibrary::ModelVaild(InModel) ? sRGB_4191F8 : sRGB_f96466;
	//UDSDrawingSubsystem::GetInstance()->UpdateModel(Model, Color);
	UDSDrawingSubsystem::GetInstance()->Drawing();
	RefreshAllMaterial();
}

void ADSWindowView::RealUnHoverViewLogic(UDSBaseModel* InModel)
{
	AttachedActorHandleAction(InModel, FDSModelExecuteType::ExecuteUnHover);

	FLinearColor Color = UDSToolLibrary::ModelVaild(InModel) ? sRGB_585858 : sRGB_f96466;
	//UDSDrawingSubsystem::GetInstance()->UpdateModel(Model, Color);
	UDSDrawingSubsystem::GetInstance()->Drawing();
	RefreshAllMaterial();
}

void ADSWindowView::RealSelectViewLogic(UDSBaseModel* InModel)
{
	AttachedActorHandleAction(InModel, FDSModelExecuteType::ExecuteSelect);
	FLinearColor Color = UDSToolLibrary::ModelVaild(InModel) ? sRGB_4191F8 : sRGB_f96466;
	UDSDrawingSubsystem::GetInstance()->Drawing();
	RefreshAllMaterial();
}

void ADSWindowView::RealUnSelectViewLogic(UDSBaseModel* InModel)
{
	AttachedActorHandleAction(InModel, FDSModelExecuteType::ExecuteUnSelect);
	RefreshAllMaterial();
}

void ADSWindowView::RealRefreshViewMaterialLogic(UDSBaseModel* InModel)
{
	TArray<AActor*> AttachedActors;
	this->GetAttachedActors(AttachedActors);
	for (auto& Cmp : AttachedActors)
	{
		auto Plane = static_cast<ADSPlaneView*>(Cmp);
		if (Plane)
		{
			Plane->RealRefreshViewMaterialLogic(Plane->GetModel());
		}
	}

	//FLinearColor Color = UDSToolLibrary::ModelVaild(InModel) ? sRGB_585858 : sRGB_f96466;
	//UDSDrawingSubsystem::GetInstance()->UpdateModel(InModel, Color);
}

void ADSWindowView::RealHiddenViewLogic(UDSBaseModel* InModel)
{
	MeshComponent->SetVisibility(false);
	BoardComponent->SetVisibility(false);
	BoardComponent1->SetVisibility(false);
}

void ADSWindowView::RealUnHiddenViewLogic(UDSBaseModel* InModel)
{
	MeshComponent->SetVisibility(true);
	BoardComponent->SetVisibility(true);
	BoardComponent1->SetVisibility(true);
}

void ADSWindowView::RealGenerateViewMeshLogic(UDSBaseModel* InModel)
{
	/*auto CT = UDSToolLibrary::GetCameraType();
	MeshComponent->SetVisibility(CT == ECameraType::EXYPlan2D);
	BoardComponent->SetVisibility(CT == ECameraType::EXYPlan2D);
	BoardComponent1->SetVisibility(CT == ECameraType::EXYPlan2D);*/
}

void ADSWindowView::RefreshTopOutlineView()
{
}

void ADSWindowView::RefreshFrameOutlineView()
{
}


void ADSWindowView::RefreshMesh()
{
	if (OBJECT_VALID_FOR_USE(Model))
	{
		auto Property = static_cast<FDSDoorAndWindowProperty*>(Model->GetProperty());
		auto Mesh = Model->GetMeshInfo().MeshDatas[MESH_WINDOW_BODY_2D];
		TArray<FProcMeshTangent> Tangents;
		Tangents.SetNumZeroed(Mesh.Vertex.Num());
		TArray<FColor> VertexColor;
		MeshComponent->ClearMeshSection(0);
		MeshComponent->CreateMeshSection(0, Mesh.Vertex, Mesh.Indices, Mesh.Normals, Mesh.Uvs, VertexColor, Tangents, true);
		MeshComponent->SetRelativeLocation(FVector(0, 0, Z2D));

		BoardComponent->ClearMeshSection(0);
		if (Property->DoorAndWindowType == EDoorAndWindowType::E_Window_Bay)
		{
			auto BoardMesh = Model->GetMeshInfo().MeshDatas[MESH_WINDOW_BAY_2D];
			BoardComponent->CreateMeshSection(0, BoardMesh.Vertex, BoardMesh.Indices, BoardMesh.Normals, BoardMesh.Uvs, VertexColor, Tangents, true);
			BoardComponent->SetRelativeLocation(FVector(0, 0, Z2D));
		}
	}
}

void ADSWindowView::RefreshMaterial(const int32& InIndex)
{
	Super::RefreshMaterial(InIndex);
	ECameraType CameraType = UDSToolLibrary::GetCameraType();
	if (MeshComponent && InIndex == 0)
	{
		MeshComponent->SetMaterial(0, GetMaterialInstanceByType(InIndex, CameraType == ECameraType::EXYPlan2D || CameraType == ECameraType::EXYPlan2D_Ceil));
	}
	if (BoardComponent && InIndex == 1)
	{
		BoardComponent->SetMaterial(0, GetMaterialInstanceByType(InIndex, CameraType == ECameraType::EXYPlan2D || CameraType == ECameraType::EXYPlan2D_Ceil));
	}
	if (BoardComponent1 && InIndex == 2)
	{
		BoardComponent1->SetMaterial(0, GetMaterialInstanceByType(InIndex, CameraType == ECameraType::EXYPlan2D || CameraType == ECameraType::EXYPlan2D_Ceil));
	}
}

void ADSWindowView::RefreshAllMaterial()
{
	RefreshMaterial(0);
	RefreshMaterial(1);
	RefreshMaterial(2);
}

void ADSWindowView::Init()
{
}

void ADSWindowView::RealDeleteViewLogic(UDSBaseModel* InModel)
{
	Super::RealDeleteViewLogic(InModel);

	if (BoardComponent)
	{
		BoardComponent->SetCollisionEnabled(ECollisionEnabled::NoCollision);
		BoardComponent->SetVisibility(false);
	}

	if (BoardComponent1)
	{
		BoardComponent1->SetCollisionEnabled(ECollisionEnabled::NoCollision);
		BoardComponent1->SetVisibility(false);
	}
}
