// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "DSConstructionFrame.generated.h"

/**
 * 
 */
UCLASS()
class DESIGNSTATION_API UDSConstructionFrame : public UObject
{
	GENERATED_BODY()

public:
	//static const FName Frame_PlaneBaseCabinet;   //平面地柜图框
	//static const FName Frame_PlaneWallCabinet;   //平面吊柜图框
	//static const FName Frame_FrontConstructionCabinet;   //立面柜体结构图框
	//static const FName Frame_FrontDoorCabinet;   //立面门板图框

	static const FName Frame_Default;   //我乐默认图框

	//static const TSet<FName> AllFrameNames;

	static const TArray<FString> DefaultFrameKeys;

public:
	UDSConstructionFrame();
	UDSConstructionFrame(const FName& InType) : FrameType(InType)
	{

	}

	//void InitAttDefs(const TSet<FName>& InAttDefKeys);

	void SetAttDefs(const TMap<FString, FString>& InAttDefs)
	{
		AttDefs = InAttDefs;
	}

	FVector GetFrameContentSizeInCAD() const
	{
		return FrameContentSizeInCAD;
	}

	FVector GetFrameActiveContentSizeInCAD() const
	{
		return FrameContentSizeInCAD * ScaleInCAD;
	}

	void SetFrameOuterSizeInCAD(const FVector& InSize)
	{
		FrameOuterSizeInCAD = InSize;
	}

	void SetFrameScaleInCAD(const FVector& InScaleInCAD)
	{
		ScaleInCAD = InScaleInCAD;
	}

	FVector GetFrameActiveOuterSizeInCAD() const
	{
		return FrameOuterSizeInCAD * ScaleInCAD;
	}

	FVector GetFrameMinOffsetInCAD() const
	{
		return FVector(-21.5458, -354.7525, 0.0f);
	}

public:
	FName FrameType = NAME_None;
	TMap<FString, FString> AttDefs;  //图框属性定义

	FVector ScaleInCAD    = FVector(1, 1, 1);     //图框缩放
	FVector FrameContentSizeInCAD = FVector(4132, 3131, 0); //图框默认大小
	FVector FrameOuterSizeInCAD = FVector(4132, 3131, 0); //图框默认大小
};
