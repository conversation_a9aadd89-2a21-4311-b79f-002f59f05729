﻿// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "XmlNode.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "Subsystems/MVC/Core/Property/HouseAreaProperty.h"
#include "SubSystems/MVC/Model/Custom/DSCupboardModel.h"
#include "DSXmlLibrary.generated.h"

/**
 * 
 */
UCLASS()
class DESIGNSTATION_API UXmlLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()

public:
	UFUNCTION(Category = "XmlFile")
	static void ExportDataToXml(const FString& FilePath);

	static void CollectGroupElement(FXmlNode*& ParentNode, const TSharedPtr<FMultiComponentDataItem>& ComponentDataItem, FString RootTag, FString RootMaterialId, const FString& CabinetNo);

	static void DoorAddPlusValue(const TSharedPtr<FMultiComponentDataItem>& ComponentDataItem, TMap<FString, FString>& MustValueMap);

	static void CollectAccessoryElement(FXmlNode*& ParentNode, const FDSAccessoryResInfo& AccessoryResInfo);

	static void AddNextLevelTagS(const TSharedPtr<FMultiComponentDataItem>& ComponentDataItem, FXmlNode* ParentNode, const TPair<FString, FString>& TagName, const FString& RootTag, const FString& RootMaterialId, const FString& CabinetNo);

	static TPair<FString, FString> GetStringFromModelType(const int32& NumericID);

	static bool IsPointInPolygon(const FVector2D& Point, const TArray<FVector2D>& Polygon);

	static FString FormatNumericString(const FString& InputString, int32 MaxDecimalPlaces);

	static void TestFormatNumericString();
};
