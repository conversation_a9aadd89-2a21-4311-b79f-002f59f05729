﻿// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"

#include "Lexer/ExpressionLexerToken.h"
#include "AstNode.h"

/**
 * 
 */
class CATALOGEXPRESSION_API FExpressionParser
{
public:
	FExpressionParser();
	virtual ~FExpressionParser() = default;

	bool Parse(const FString& InContent, TArray<FAstNodePtr>& OutAstTree, FString& OutError);

	const TArray<FExpressionLexerToken>& GetTokens() const;

protected:
	void SkipWhitespace();

	const FExpressionLexerToken& PeekToken();
	const FExpressionLexerToken& ConsumeToken();

	bool MatchType(EExpressionLexerTokenType InType);
	bool MatchValue(const FString& InValue);

	FAstNodePtr ParseExpression();
	FAstNodePtr ParseLogicalOr();
	FAstNodePtr ParseLogicalAnd();
	FAstNodePtr ParseEquality();
	FAstNodePtr ParseComparision();
	FAstNodePtr ParseTerm();
	FAstNodePtr ParseFactor();
	FAstNodePtr ParseExponent();
	FAstNodePtr ParseUnary();
	FAstNodePtr ParsePrimary();
	FAstNodePtr ParseFunctionCall(const FString& InName);
	
protected:
	int32 Offset;
	TArray<FExpressionLexerToken> Tokens;
};
