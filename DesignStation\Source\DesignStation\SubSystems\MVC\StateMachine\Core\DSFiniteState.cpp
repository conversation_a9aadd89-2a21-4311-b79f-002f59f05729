﻿// Fill out your copyright notice in the Description page of Project Settings.
#include "DSFiniteState.h"
#include "BasicClasses/DesignStationController.h"
#include "Subsystems/Camera/DSCameraSubsystem.h"
#include "SubSystems/MVC/Library/DSToolLibrary.h"
#include "SubSystems/Pendant/DSPendantSubsystem.h"
#include "SubSystems/Snap/DSSnapSubsystem.h"
#include "Subsystems/Drawing/DSDrawingSubsystem.h"
#include "Subsystems/MVC/Core/Property/DashedProperty.h"
#include "Subsystems/MVC/Core/Macro.h"
#include "SubSystems/MVC/Model/Group/DSMultiModel.h"
#include "SubSystems/UI/DSUISubsystem.h"
#include "SubSystems/UI/Widget/WindowLayoutWidget.h"
#include "SubSystems/UI/Widget/FreeDrawingCanvas/FreeDrawingCanvas.h"
#include "SubSystems/UI/Widget/MainDesignPage/MainDesignPageWidget.h"
#include "SubSystems/Undo/DSRevokeSubsystem.h"
#include "SubSystems/MVC/Commands/Public/DSGenericCommands.h"
#include "SubSystems/Undo/Library/DSRevokeLibrary.h"
#include "SubSystems/MVC/Model/Group/DSGroupModel.h"
#include "SubSystems/MVC/StateMachine/DragDropOps/DSCameraDragDropOperation.h"
#include "Subsystems/MVC/StateMachine/DragDropOps/DSModelDragDropOperation.h"
#include "Subsystems/UI/DragDropOperations/ResourceItemDragDropOperation.h"
#include "Subsystems/UI/DragDropOperations/HouseButtonDragDropOperation.h"
#include "Subsystems/UI/DragDropOperations/CollectionItemDragDropOperation.h"
#include "Subsystems/UI/Widget/Common/ResourceItem/ResourceItem.h"
#include "SubSystems/MVC/Model/Custom/Library/DSCupboardLibrary.h"
#include "SubSystems/Resource/DSResourceSubsystem.h"
#include "Subsystems/UI/Widget/Common/CollectionItem/CollectionItem.h"
#include "Subsystems/MVC/DSMVCSubsystem.h"
#include "SubSystems/UI/DragDropOperations/SpotLightDragDropOperation.h"
#include "SubSystems/UI/Widget/MainDesignPage/HouseDesignPage/HouseDesignPage.h"
#include "Subsystems/UI/Widget/Ruler/RulerInputWidget.h"
#include "SubSystems/MVC/Library/DSCustomLibrary.h"
#include "SlateUser.h"
#include "UMGDragDropOp.h"
#include "SObjectWidget.h"
#include "SViewport.h"
#include "SubSystems/Library/DSXmlLibrary.h"
#include "SubSystems/MVC/Library/CupBoardDoorLibrary.h"
#include "Subsystems/MVC/Model/Custom/Library/DSWallBoardLibrary.h"
#include "SubSystems/ModelDependencySubsystem/DSModelDependencySubsystem.h"
#include "SubSystems/MVC/Model/Custom/Library/DSHandleFreeLibrary.h"
#include <SubSystems/Library/DesignStationFunctionLibrary.h>

DEFINE_LOG_CATEGORY(DSFiniteStateLog);

extern const TArray<EDSModelType> ConsiderAsHouseType;

UDSFiniteState::UDSFiniteState()
	: ActionFactory(nullptr)
	, SelectType(EDSSelectType::E_Whole_Select)
	, CachedGroupModel(nullptr)
{
	CommandList = MakeShareable(new FUICommandList);
}

void UDSFiniteState::BeginDestroy()
{
	Super::BeginDestroy();
}

void UDSFiniteState::SetMouseSelectType(const EDSSelectType& InType)
{
	EDSSelectType OldSelectType = SelectType;
	SelectType = InType;
	OnSelectTypeChange(OldSelectType, SelectType);
}

void UDSFiniteState::ResetDefaultSelectType()
{
	EDSSelectType OldSelectType = SelectType;
	SelectType = EDSSelectType::E_Whole_Select; 
	OnSelectTypeChange(OldSelectType, SelectType);
}

void UDSFiniteState::PostInitProperties()
{
	Super::PostInitProperties();

	Init_ActionFactory();

	DS_FSM_BeginPlay();
}

void UDSFiniteState::DS_FSM_BeginPlay()
{
}

void UDSFiniteState::BindGenericInputCommands()
{
	if (!CommandList.IsValid())
	{
		return;
	}

	CommandList->MapAction(FDSGenericCommands::Get().Delete, FExecuteAction::CreateUObject(this, &ThisClass::OnExecuteGenericInputCommand_Delete), FCanExecuteAction::CreateUObject(this, &ThisClass::CanExecuteGenericInputCommand_Delete));
	CommandList->MapAction(FDSGenericCommands::Get().SelectAll, FExecuteAction::CreateUObject(this, &ThisClass::OnExecuteGenericInputCommand_SelectAll), FCanExecuteAction::CreateUObject(this, &ThisClass::CanExecuteGenericInputCommand_SelectAll));
	CommandList->MapAction(FDSGenericCommands::Get().SwitchCameraMode, FExecuteAction::CreateUObject(this, &ThisClass::OnExecuteGenericInputCommand_SwitchCameraMode), FCanExecuteAction::CreateUObject(this, &ThisClass::CanExecuteGenericInputCommand_SwitchCameraMode));
	CommandList->MapAction(FDSGenericCommands::Get().Escape, FExecuteAction::CreateUObject(this, &ThisClass::OnExecuteGenericInputCommand_Escape), FCanExecuteAction::CreateUObject(this, &ThisClass::CanExecuteGenericInputCommand_Escape));
	CommandList->MapAction(FDSGenericCommands::Get().CopyModel, FExecuteAction::CreateUObject(this, &ThisClass::OnExecuteGenericInputCommand_Copy), FCanExecuteAction::CreateUObject(this, &ThisClass::CanExecuteGenericInputCommand_Copy));
	CommandList->MapAction(FDSGenericCommands::Get().OpenAndCloseDoor, FExecuteAction::CreateUObject(this, &ThisClass::OnExecuteGenericInputCommand_OpenAndCloseDoor));
	CommandList->MapAction(FDSGenericCommands::Get().ExportXmlFile, FExecuteAction::CreateUObject(this, &ThisClass::OnExecuteGenericInputCommand_ExportXmlFile));
}

bool UDSFiniteState::CanExecuteGenericInputCommand_Delete()
{
	auto CurrentModel = UDSMVCSubsystem::GetInstance()->GetCurrentModel();
	if (CurrentModel != nullptr)
	{
		if (SelectType == EDSSelectType::E_Whole_Part_Select)
		{
			
			if (CurrentModel->GetModelType() == EDSModelType::E_MultiSelect)
			{
				TSet<FString> MultiUUIDs;
				auto MultiSelectModel = Cast<UDSMultiModel>(CurrentModel);
				for (auto& Iter : MultiSelectModel->GetIncludeModel())
				{
					if (Iter->IsSelected())
					{
						if (!Iter->GetGroupUUID().IsEmpty())
						{
							MultiUUIDs.Add(Iter->GetGroupUUID());
						}
					}
				}

				if (MultiUUIDs.Num() == 1)
				{
					return true;
				}
			}
			else if (CurrentModel->IsInGroup())
			{
				return true;
			}
		}
	}
	return IsWholeSelectState();
}

bool UDSFiniteState::CanExecuteGenericInputCommand_Copy()
{
	return UDSMVCSubsystem::GetInstance()->GetCurrentModel() != nullptr;
}

bool UDSFiniteState::CanExecuteGenericInputCommand_SelectAll()
{
	return true;
}

bool UDSFiniteState::CanExecuteGenericInputCommand_SwitchCameraMode()
{
	return true;
}

bool UDSFiniteState::CanExecuteGenericInputCommand_Escape()
{
	bool bIsCan = true;
#if	WITH_EDITOR
	bIsCan = false;
#endif
	return bIsCan;
}

void UDSFiniteState::OnExecuteGenericInputCommand_Delete()
{
	DeleteCommandCoroutine();
}

void UDSFiniteState::OnExecuteGenericInputCommand_OpenAndCloseDoor()
{
	TArray<UDSCupboardModel*> AllDoorModels;
	TArray<UDSCupboardModel*> ClosedDoorModels;
	TArray<UDSCupboardModel*> ClosedDrawerModels;

	bool HasClosedDoor = UDSCupBoardDoorLibrary::CheckHasClosedDoor(GetFiniteStateMachine()->GetCurrentModel(), AllDoorModels, ClosedDoorModels);
	bool HasClosedDrawer = UDSCupBoardDoorLibrary::CheckHasClosedDrawer(GetFiniteStateMachine()->GetCurrentModel(), ClosedDrawerModels);

	if (!HasClosedDrawer && HasClosedDoor)
	{
		UDSCupBoardDoorLibrary::OpenOrCloseDoor(GetFiniteStateMachine()->GetCurrentModel(), AllDoorModels, ClosedDoorModels);
	}
	else
	{
		UDSCupboardLibrary::ToggleDrawerState(GetFiniteStateMachine()->GetCurrentModel());
		UDSCupBoardDoorLibrary::OpenOrCloseDoor(GetFiniteStateMachine()->GetCurrentModel(), AllDoorModels, ClosedDoorModels);
	}
}

void UDSFiniteState::OnExecuteGenericInputCommand_ExportXmlFile()
{
	UDSCupBoardDoorLibrary::OnPriceListClickAndXml();
}

void UDSFiniteState::OnExecuteGenericInputCommand_Copy()
{

	OnPreExecuteGenericInputCommand_Copy(UDSMVCSubsystem::GetInstance()->GetCurrentModel());


	UDSBaseModel* CopyModel = UDSMVCSubsystem::GetInstance()->GetCurrentModel()->OnCopy();
	if (CopyModel == nullptr)
	{
		return;
	}
	
	FTransform OldTransform = CopyModel->GetProperty()->GetActualTransform();
	FVector Size = CopyModel->GetProperty()->SizeProperty.ToVector();
	FVector ShiftLoc = OldTransform.GetRotation().GetForwardVector() * Size.X * 0.1;
	CopyModel->GetPropertySharedPtr()->TransformProperty.Location += ShiftLoc;
	CopyModel->OnExecuteAction(FDSModelExecuteType::ExecuteTransform, FDSBroadcastMarkData::OnlyOutlineBroadcastMark);

	UDSMVCSubsystem::GetInstance()->SetCurrentModel(CopyModel);
	UDSUISubsystem::GetInstance()->ProcessStateEvent(CopyModel, EUIOperationType::Selected);

	if (CopyModel->GetModelType() == EDSModelType::E_Custom_LayoutDoor)
	{
		UDSCustomLibrary::RefreshLayoutDoorOnWalls(CopyModel);
	}
	else if (CopyModel->GetModelType() == EDSModelType::E_MultiSelect)
	{
		auto MultiSelectModel = Cast<UDSMultiModel>(CopyModel);
		for (auto & Iter : MultiSelectModel->GetIncludeModel())
		{
			if (Iter->GetModelType() == EDSModelType::E_Custom_LayoutDoor)
			{
				UDSCustomLibrary::RefreshLayoutDoorOnWalls();
			}
		}
	}
	else if (CopyModel->GetModelType() == EDSModelType::E_Group)
	{
		auto GroupModel = Cast<UDSGroupModel>(CopyModel);
		for (auto & Iter : GroupModel->GetIncludeModel())
		{
			if (Iter->GetModelType() == EDSModelType::E_Custom_LayoutDoor)
			{
				UDSCustomLibrary::RefreshLayoutDoorOnWalls();
			}
		}
	}

	
	OnPostExecuteGenericInputCommand_Copy(CopyModel);
}

void UDSFiniteState::OnExecuteGenericInputCommand_SelectAll()
{
}

void UDSFiniteState::OnExecuteGenericInputCommand_SwitchCameraMode()
{
	UDSCameraSubsystem::GetInstance()->SwitchCameraToNext();
}

void UDSFiniteState::OnExecuteGenericInputCommand_Escape()
{
	//ESC和右击要清理选中状态和UI
	if (UDSMVCSubsystem::GetInstance()->GetCurrentModel() != nullptr)
	{
		//取消虚线框的显示
		ModelShowStateChange(GetFiniteStateMachine()->GetCurrentModelRef(), nullptr, false, nullptr);
		
		UDSMVCSubsystem::GetInstance()->SetCurrentModel(nullptr);
		UDSUISubsystem::GetInstance()->ProcessStateEvent(UDSMVCSubsystem::GetInstance()->GetCurrentModel(), EUIOperationType::Unselected, GetState());
	}
}

void UDSFiniteState::UpdatePropertyWidgetAfterDelete()
{
	if (GetState() != EDSFSMState::FSM_Rendering)
	{
		UDSUISubsystem::GetInstance()->HiddenUI();
	}
}

void UDSFiniteState::Tick(float DeltaTime)
{
	const FVector2D& CameraMoveSpeed = UDSCameraSubsystem::GetInstance()->GetMoveSpeed();
	for (const FKey& Key : PressedMoveKeys)
	{
		if (Key == EKeys::W)
		{
			UDSCameraSubsystem::GetInstance()->OnMoveForwardAndBack(CameraMoveSpeed.X * DeltaTime);
		}

		if (Key == EKeys::S)
		{
			UDSCameraSubsystem::GetInstance()->OnMoveForwardAndBack(-CameraMoveSpeed.X * DeltaTime);
		}

		if (Key == EKeys::A)
		{
			UDSCameraSubsystem::GetInstance()->OnMoveLeftRight(CameraMoveSpeed.Y * DeltaTime);
		}

		if (Key == EKeys::D)
		{
			UDSCameraSubsystem::GetInstance()->OnMoveLeftRight(-CameraMoveSpeed.Y * DeltaTime);
		}

		if (Key == EKeys::Q)
		{
			UDSCameraSubsystem::GetInstance()->OnMoveUpDown(CameraMoveSpeed.X * DeltaTime);
		}

		if (Key == EKeys::E)
		{
			UDSCameraSubsystem::GetInstance()->OnMoveUpDown(-CameraMoveSpeed.X * DeltaTime);
		}
	}
}

TStatId UDSFiniteState::GetStatId() const
{
	RETURN_QUICK_DECLARE_CYCLE_STAT(UDSFiniteState, STATGROUP_Tickables);
}

FReply UDSFiniteState::OnPreviewMouseButtonDown(const FGeometry& MyGeometry, const FPointerEvent& MouseEvent)
{
	return FReply::Unhandled();
}

FReply UDSFiniteState::OnMouseButtonDown(const FGeometry& MyGeometry, const FPointerEvent& MouseEvent)
{
	if (UDSBaseModel* PickedModel = GetFiniteStateMachine()->GetDragDetectedModel(MouseEvent.GetEffectingButton()))
	{
		if (UDSActionExecuteBase* Action = ActionFactory->GetAction(PickedModel, MouseEvent))
		{
			Action->OnMouseButtonDown(MyGeometry, MouseEvent);
		}
	}
	
	if (MouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
	{
		return FReply::Handled().DetectDrag(UDSUISubsystem::GetInstance()->GetUserInputDispatcher().ToSharedRef(), EKeys::LeftMouseButton);
	}
	else if (MouseEvent.GetEffectingButton() == EKeys::RightMouseButton)
	{
		return FReply::Handled().DetectDrag(UDSUISubsystem::GetInstance()->GetUserInputDispatcher().ToSharedRef(), EKeys::RightMouseButton);
	}
	else if (MouseEvent.GetEffectingButton() == EKeys::MiddleMouseButton)
	{
		return FReply::Handled().DetectDrag(UDSUISubsystem::GetInstance()->GetUserInputDispatcher().ToSharedRef(), EKeys::MiddleMouseButton);
	}
	
	return FReply::Handled();
}

FReply UDSFiniteState::OnMouseButtonUp(const FGeometry& MyGeometry, const FPointerEvent& MouseEvent)
{
	return FReply::Handled();
}

FReply UDSFiniteState::OnMouseButtonClick(const FGeometry& MyGeometry, const FPointerEvent& InMouseEvent, UDSBaseModel* InModel)
{
	if (InMouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
	{
		return OnMouseButtonClick_LeftMouse(MyGeometry, InMouseEvent, InModel);
	}
	else if (InMouseEvent.GetEffectingButton() == EKeys::MiddleMouseButton)
	{
		return OnMouseButtonClick_MiddleMouse(MyGeometry, InMouseEvent, InModel);
	}
    else if (InMouseEvent.GetEffectingButton() == EKeys::RightMouseButton)
	{
		return OnMouseButtonClick_RightMouse(MyGeometry, InMouseEvent, InModel);
	}

	return FReply::Unhandled();
}

FReply UDSFiniteState::OnMouseButtonClick_LeftMouse(const FGeometry& MyGeometry, const FPointerEvent& MouseEvent, UDSBaseModel* InModel)
{
	if (MouseEvent.GetModifierKeys().IsShiftDown())
	{
		return MultiSelectOperator(MyGeometry, MouseEvent, InModel);
	}
	else
	{
		EDSSelectType NewSelectType;
		if (SwitchSelectType_Click(InModel, NewSelectType))
		{
			SwitchStateSelectType(NewSelectType);
			ClearHoverData(MouseEvent);
		}
	}
	return FReply::Handled();
}

FReply UDSFiniteState::OnMouseButtonClick_MiddleMouse(const FGeometry& MyGeometry, const FPointerEvent& MouseEvent, UDSBaseModel* InModel)
{
	return FReply::Handled();
}

FReply UDSFiniteState::OnMouseButtonClick_RightMouse(const FGeometry& MyGeometry, const FPointerEvent& MouseEvent, UDSBaseModel* InModel)
{
	GetCommandList()->TryExecuteAction(FDSGenericCommands::Get().Escape.ToSharedRef());
	return FReply::Handled();
}

FReply UDSFiniteState::OnMouseButtonDoubleClick(const FGeometry& InMyGeometry, const FPointerEvent& InMouseEvent)
{
	if (InMouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
	{
        return OnMouseButtonDoubleClick_LeftMouse(InMyGeometry, InMouseEvent);
	}
    else if (InMouseEvent.GetEffectingButton() == EKeys::MiddleMouseButton)
	{
        return OnMouseButtonDoubleClick_MiddleMouse(InMyGeometry, InMouseEvent);
	}
	else if (InMouseEvent.GetEffectingButton() == EKeys::RightMouseButton)
	{
        return OnMouseButtonDoubleClick_RightMouse(InMyGeometry, InMouseEvent);
	}

	return FReply::Handled();
}

FReply UDSFiniteState::OnMouseButtonDoubleClick_LeftMouse(const FGeometry& MyGeometry, const FPointerEvent& MouseEvent)
{
	EDSSelectType NewSelectType;
	UDSBaseModel* CurPickData = PickComponentUnderMouse(MyGeometry, MouseEvent);

	if (SwitchSelectType_DoubleClick(CurPickData, NewSelectType))
	{
		SwitchStateSelectType(NewSelectType);
		ClearHoverData(MouseEvent);
		UDSUISubsystem::GetInstance()->ProcessStateEvent(GetFiniteStateMachine()->GetCurrentModel(), EUIOperationType::Selected, GetState());
		UDSPendantSubsystem::GetInstance()->RefreshPendant(UDSMVCSubsystem::GetInstance()->GetCurrentModel(), FDSModelExecuteType::ExecuteSelect, FDSBroadcastMarkData::SelectBroadcastMark);
	}
	return FReply::Handled();
}

FReply UDSFiniteState::OnMouseButtonDoubleClick_MiddleMouse(const FGeometry& MyGeometry, const FPointerEvent& MouseEvent)
{
	return FReply::Handled();
}

FReply UDSFiniteState::OnMouseButtonDoubleClick_RightMouse(const FGeometry& MyGeometry, const FPointerEvent& MouseEvent)
{
	return FReply::Handled();
}

bool UDSFiniteState::SwitchSelectType_Click(UDSBaseModel* InModel, EDSSelectType& NeedChangeSelectType)
{
	if (IsWholePartSelectState())
	{
		UDSBaseModel* CurrentSelect = GetFiniteStateMachine()->GetCurrentModel();
		if (ADesignStationController::Get()->Is2DScene())
		{
			UDSBaseModel* InParentModel = UDSToolLibrary::GetOwnerModelRecursion(InModel);
			if (UDSToolLibrary::IsInGroup(CurrentSelect))
			{
				if (UDSToolLibrary::IsInSameGroup(CurrentSelect, InParentModel))
				{
					UDSBaseModel* GroupModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(EDSModelType::E_Group, CurrentSelect->GetGroupUUID());
					ModelShowStateChange(GetFiniteStateMachine()->GetCurrentModelRef(), InParentModel, true, GroupModel);
				}
				else if (UDSToolLibrary::IsInGroup(InParentModel))
				{
					UDSBaseModel* GroupModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(EDSModelType::E_Group, InParentModel->GetGroupUUID());
					ModelShowStateChange(GetFiniteStateMachine()->GetCurrentModelRef(), GroupModel, false, nullptr);
					NeedChangeSelectType = EDSSelectType::E_Whole_Select;
					return true;
				}
				else
				{
					ModelShowStateChange(GetFiniteStateMachine()->GetCurrentModelRef(), UDSToolLibrary::GetOwnerModelRecursion(InModel), false, nullptr);
					NeedChangeSelectType = EDSSelectType::E_Whole_Select;
					return true;
				}
			}
			else
			{
				if (UDSToolLibrary::IsInGroup(InParentModel))
				{
					UDSBaseModel* GroupModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(EDSModelType::E_Group, InParentModel->GetGroupUUID());
					ModelShowStateChange(GetFiniteStateMachine()->GetCurrentModelRef(), GroupModel, false, nullptr);
					NeedChangeSelectType = EDSSelectType::E_Whole_Select;
					return true;
				}
				else
				{
					ModelShowStateChange(GetFiniteStateMachine()->GetCurrentModelRef(), UDSToolLibrary::GetOwnerModelRecursion(InModel), false, nullptr);
					NeedChangeSelectType = EDSSelectType::E_Whole_Select;
					return true;
				}
			}
		}

		if (CurrentSelect == nullptr)
		{
			UDSBaseModel* PickOwnerModel = UDSToolLibrary::GetOwnerModelRecursion(InModel);
			ModelShowStateChange(GetFiniteStateMachine()->GetCurrentModelRef(), PickOwnerModel, false, nullptr);
			NeedChangeSelectType = EDSSelectType::E_Whole_Select;
			return true;
		}
		else
		{
			if (InModel != nullptr)
			{
				UDSBaseModel* OldOwnerModel = UDSToolLibrary::GetOwnerModelRecursion(CurrentSelect);
				UDSBaseModel* NewOwnerModel = UDSToolLibrary::GetOwnerModelRecursion(InModel);
				if (UDSToolLibrary::IsInGroup(OldOwnerModel) && UDSToolLibrary::IsInGroup(NewOwnerModel))
				{
					if (OldOwnerModel->GetGroupUUID().Equals(NewOwnerModel->GetGroupUUID()))
					{//相同组
						if (UDSBaseModel* GroupModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(EDSModelType::E_Group, NewOwnerModel->GetGroupUUID()))
						{
							ModelShowStateChange(GetFiniteStateMachine()->GetCurrentModelRef(), NewOwnerModel, true, GroupModel);
						}
					}
					else
					{
						if (UDSBaseModel* GroupModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(EDSModelType::E_Group, NewOwnerModel->GetGroupUUID()))
						{
							ModelShowStateChange(GetFiniteStateMachine()->GetCurrentModelRef(), GroupModel, false, nullptr);
							NeedChangeSelectType = EDSSelectType::E_Whole_Select;
							return true;
						}
					}
				}
				else if (UDSToolLibrary::IsInGroup(OldOwnerModel) || UDSToolLibrary::IsInGroup(NewOwnerModel))
				{
					UDSBaseModel* NewParentModel = nullptr;
					bool bIsInGroup = UDSToolLibrary::IsInGroup(NewOwnerModel);
					if (bIsInGroup)
					{
						NewParentModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(EDSModelType::E_Group, NewOwnerModel->GetGroupUUID());
					}

					if (bIsInGroup)
					{
						ModelShowStateChange(GetFiniteStateMachine()->GetCurrentModelRef(), NewParentModel, false, nullptr);
					}
					else
					{
						ModelShowStateChange(GetFiniteStateMachine()->GetCurrentModelRef(), NewOwnerModel, false, nullptr);
					}
					NeedChangeSelectType = EDSSelectType::E_Whole_Select;
					return true;
				}
				else
				{
					if (OldOwnerModel != NewOwnerModel)
					{
						ModelShowStateChange(GetFiniteStateMachine()->GetCurrentModelRef(), NewOwnerModel, false, nullptr);
						NeedChangeSelectType = EDSSelectType::E_Whole_Select;
						return true;
					}
					else
					{
						bool bNewModelIsFunctional = UDSCupboardLibrary::IsFunctionalCupboardModel(InModel);

						UDSBaseModel* OldFunctionalOwner = UDSCupboardLibrary::IsFunctionalCupboardModel(CurrentSelect) ? CurrentSelect : UDSCupboardLibrary::FindNearestFunctionalCupboardModel(CurrentSelect);
						UDSBaseModel* NewFunctionalOwner = bNewModelIsFunctional ? InModel : UDSCupboardLibrary::FindNearestFunctionalCupboardModel(InModel);

						if (OldFunctionalOwner != nullptr)
						{
							if (NewFunctionalOwner != nullptr)
							{
								if (bNewModelIsFunctional || OldFunctionalOwner != NewFunctionalOwner)
								{
									ModelShowStateChange(GetFiniteStateMachine()->GetCurrentModelRef(), NewFunctionalOwner, false, nullptr);
									NeedChangeSelectType = EDSSelectType::E_Whole_Select;
									return true;
								}
								else
								{
									ModelShowStateChange(GetFiniteStateMachine()->GetCurrentModelRef(), InModel, true, NewFunctionalOwner);
								}
							}
							else
							{
								ModelShowStateChange(GetFiniteStateMachine()->GetCurrentModelRef(), NewOwnerModel, false, nullptr);
								NeedChangeSelectType = EDSSelectType::E_Whole_Select;
								return true;
							}
						}
						else
						{
							if (NewFunctionalOwner != nullptr)
							{
								ModelShowStateChange(GetFiniteStateMachine()->GetCurrentModelRef(), NewFunctionalOwner, false, nullptr);
								NeedChangeSelectType = EDSSelectType::E_Whole_Select;
								return true;
							}
							else
							{
								ModelShowStateChange(GetFiniteStateMachine()->GetCurrentModelRef(), InModel, true, NewOwnerModel);
							}
						}
					}
				}
			}
			else
			{
				ModelShowStateChange(GetFiniteStateMachine()->GetCurrentModelRef(), nullptr, false, nullptr);
				NeedChangeSelectType = EDSSelectType::E_Whole_Select;
				return true;
			}
		}
	}
	else
	{
		ModelShowStateChange(GetFiniteStateMachine()->GetCurrentModelRef(), InModel, false, nullptr);
	}
	return false;
}

bool UDSFiniteState::SwitchSelectType_DoubleClick(UDSBaseModel* InModel, EDSSelectType& NeedChangeSelectType)
{
	if (IsWholeSelectState())
	{
		return SwitchSelectType_DoubleClick_Whole(InModel, NeedChangeSelectType);
	}
	else if (IsWholePartSelectState())
	{
		return SwitchSelectType_DoubleClick_WholePart(InModel, NeedChangeSelectType);
	}

	return false;
}

bool UDSFiniteState::SwitchSelectType_DoubleClick_Whole(UDSBaseModel* InModel, EDSSelectType& NeedChangeSelectType)
{
	if (InModel != nullptr)
	{
		UDSBaseModel* PickOwnerModel = UDSToolLibrary::GetOwnerModelRecursion(InModel);
		if (UDSToolLibrary::IsInGroup(PickOwnerModel))
		{
			if (UDSBaseModel* GroupModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(EDSModelType::E_Group, PickOwnerModel->GetGroupUUID()))
			{
				ModelShowStateChange(GetFiniteStateMachine()->GetCurrentModelRef(), PickOwnerModel, true, GroupModel);
				NeedChangeSelectType = EDSSelectType::E_Whole_Part_Select;
				return true;
			}
			
		}
		else
		{
			if (ADesignStationController::Get()->Is2DScene())
			{
				ModelShowStateChange(GetFiniteStateMachine()->GetCurrentModelRef(), PickOwnerModel, false, nullptr);
			}
			else
			{
				if (InModel != PickOwnerModel && !UDSToolLibrary::IsRoomWindDoor(PickOwnerModel))
				{
					ModelShowStateChange(GetFiniteStateMachine()->GetCurrentModelRef(), InModel, true, PickOwnerModel);
					NeedChangeSelectType = EDSSelectType::E_Whole_Part_Select;
					return true;
				}
				else
				{
					ModelShowStateChange(GetFiniteStateMachine()->GetCurrentModelRef(), PickOwnerModel, false, nullptr);
				}
			}
		}
	}
	else
	{
		ModelShowStateChange(GetFiniteStateMachine()->GetCurrentModelRef(), nullptr, false, nullptr);
	}

	return false;
}

bool UDSFiniteState::SwitchSelectType_DoubleClick_WholePart(UDSBaseModel* InModel, EDSSelectType& NeedChangeSelectType)
{
	if (InModel != nullptr)
	{
		UDSBaseModel* PickOwnerModel = UDSToolLibrary::GetOwnerModelRecursion(InModel);
		if (UDSToolLibrary::IsInGroup(PickOwnerModel))
		{
			if (UDSBaseModel* GroupModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(EDSModelType::E_Group, PickOwnerModel->GetGroupUUID()))
			{
				//判断当前选中的是否为同一Group
				UDSBaseModel* CurrentSelect = GetFiniteStateMachine()->GetCurrentModel();
				if (UDSToolLibrary::IsInGroup(CurrentSelect) && PickOwnerModel->GetGroupUUID().Equals(CurrentSelect->GetGroupUUID()))
				{

					if (ADesignStationController::Get()->Is2DScene())
					{
						ModelShowStateChange(GetFiniteStateMachine()->GetCurrentModelRef(), PickOwnerModel, true, GroupModel);
					}
					else
					{
						ModelShowStateChange(GetFiniteStateMachine()->GetCurrentModelRef(), InModel, true, PickOwnerModel);
					}
				}
				else
				{
					ModelShowStateChange(GetFiniteStateMachine()->GetCurrentModelRef(), PickOwnerModel, true, GroupModel);
				}
			}
		}
		else
		{
			if (ADesignStationController::Get()->Is2DScene())
			{
				ModelShowStateChange(GetFiniteStateMachine()->GetCurrentModelRef(), PickOwnerModel, false, nullptr);
				NeedChangeSelectType = EDSSelectType::E_Whole_Select;
				return true;
			}
			else
			{
				if (InModel != PickOwnerModel)
				{
					ModelShowStateChange(GetFiniteStateMachine()->GetCurrentModelRef(), InModel, true, PickOwnerModel);
				}
				else
				{
					ModelShowStateChange(GetFiniteStateMachine()->GetCurrentModelRef(), PickOwnerModel, false, nullptr);
					NeedChangeSelectType = EDSSelectType::E_Whole_Select;
					return true;
				}
			}
		}
	}
	else
	{
		ModelShowStateChange(GetFiniteStateMachine()->GetCurrentModelRef(), nullptr, false, nullptr);
		NeedChangeSelectType = EDSSelectType::E_Whole_Select;
		return true;
	}

	return false;
}

bool UDSFiniteState::SwitchSelectType_DoubleClick_Part(UDSBaseModel* InModel, EDSSelectType& NeedChangeSelectType)
{
	ModelShowStateChange(GetFiniteStateMachine()->GetCurrentModelRef(), InModel, false, nullptr);
	return false;
}

void UDSFiniteState::ModelShowStateChange(UDSBaseModel*& OldModel, UDSBaseModel* NewModel, bool IsComponent, UDSBaseModel* ParentModel)
{
	if (OldModel == NewModel && NewModel != nullptr && NewModel->GetModelType() == EDSModelType::E_MultiSelect)
	{
		return;
	}
	
	//unselect
	if(OldModel != nullptr)
	{
		if(UDSToolLibrary::IsInGroup(OldModel))
		{
			if(UDSBaseModel* GroupModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(EDSModelType::E_Group, OldModel->GetGroupUUID()))
			{
				GroupModel->OnExecuteAction(FDSModelExecuteType::ExecuteUnSelectComponent);
			}
		}

		if (NewModel != nullptr && (NewModel->GetModelType() == EDSModelType::E_Gizmo || NewModel->GetModelType() == EDSModelType::E_Scale2DForPath))
		{
			if (NewModel->GetTargetModel() == OldModel)
			{
				// 坐标轴不允许作为选中Model
				return;
			}
		}

		UDSBaseModel* OwnerModel = OldModel->GetOwnerModel();
		while (OwnerModel != nullptr && OwnerModel->IsHasModelFlag(EModelState::E_SelectComponent))
		{
			OwnerModel->OnExecuteAction(FDSModelExecuteType::ExecuteUnSelectComponent, FDSBroadcastMarkData::OnlyOutlineBroadcastMark);

            OwnerModel = OwnerModel->GetOwnerModel();
		}

		/*UDSBaseModel* OldParentModel = UDSToolLibrary::GetOwnerModelRecursion(OldModel);
		if (OldParentModel && OldParentModel->IsHasModelFlag(EModelState::E_SelectComponent))
		{
			OldParentModel->OnExecuteAction(FDSModelExecuteType::ExecuteUnSelectComponent, FDSBroadcastMarkData::OnlyOutlineBroadcastMark);
		}*/
		OldModel->OnExecuteAction(FDSModelExecuteType::ExecuteUnSelect);
	}

	if (IsComponent)
	{//部件选择
		if (ParentModel != nullptr)
		{
			if (ParentModel->GetModelType() == EDSModelType::E_House_Wall 
				|| ParentModel->GetModelType() == EDSModelType::E_House_Pillar
				|| ParentModel->GetModelType() == EDSModelType::E_House_Beam
				|| ParentModel->GetModelType() == EDSModelType::E_House_Platform)
			{
				//墙体部件选择，不修改状态
			}
			else
			{
				ParentModel->OnExecuteAction(FDSModelExecuteType::ExecuteSelectComponent);
			}
		}
	}
	
	if (NewModel != nullptr)
	{
		NewModel->OnExecuteAction(FDSModelExecuteType::ExecuteSelect);
	}

	UDSUISubsystem::GetInstance()->ProcessStateEvent(OldModel, EUIOperationType::Unselected, GetState());
	
	OldModel = NewModel;

	UDSUISubsystem::GetInstance()->ProcessStateEvent(NewModel, EUIOperationType::Selected, GetState());
	ExitLeftReplace();
}

void UDSFiniteState::ModelHoverStateChange(UDSBaseModel*& OldModel, UDSBaseModel* NewModel)
{
	if (OldModel == NewModel && NewModel != nullptr)
	{
		return;
	}

	//unhover
	if (OldModel != nullptr)
	{
		OldModel->OnExecuteAction(FDSModelExecuteType::ExecuteUnHover, FDSBroadcastMarkData::OnlyOutlineBroadcastMark);
	}

	//hover
	if (NewModel != nullptr)
	{
        NewModel->OnExecuteAction(FDSModelExecuteType::ExecuteHover, FDSBroadcastMarkData::OnlyOutlineBroadcastMark);
	}
	OldModel = NewModel;
}

void UDSFiniteState::ClearHoverData(const FPointerEvent& MouseEvent)
{
	if (UDSActionExecuteBase* Action = ActionFactory->GetAction(nullptr, MouseEvent))
	{
		Action->LeaveAction();
	}
}

void UDSFiniteState::OnFocusLost(const FFocusEvent& InFocusEvent)
{
	PressedMoveKeys.Empty();
}

FReply UDSFiniteState::OnPreviewKeyDown(const FGeometry& MyGeometry, const FKeyEvent& InKeyEvent)
{
	return FReply::Unhandled();
}

FReply UDSFiniteState::OnKeyDown(const FGeometry& MyGeometry, const FKeyEvent& InKeyEvent)
{
	FReply EventReply = FReply::Unhandled();

	TArray<FKey> MoveKeyFilter = { EKeys::W, EKeys::S, EKeys::A, EKeys::D, EKeys::Q, EKeys::E };
	if (MoveKeyFilter.Contains(InKeyEvent.GetKey()))
	{
		PressedMoveKeys.AddUnique(InKeyEvent.GetKey());
		EventReply = FReply::Handled();
	}

	UDSBaseModel* CurrentModel = GetFiniteStateMachine()->GetCurrentModel();
	UDSActionExecuteBase* Action = ActionFactory->GetAction(CurrentModel, FPointerEvent());
	if (CurrentModel != nullptr && Action != nullptr)
	{
		FReply ActionReply =Action->OnKeyDown(MyGeometry, InKeyEvent);
		if (!EventReply.IsEventHandled())
		{
			EventReply = ActionReply;
		}
	}
	
	return EventReply;
}

FReply UDSFiniteState::OnKeyUp(const FGeometry& MyGeometry, const FKeyEvent& InKeyEvent)
{
	PressedMoveKeys.Remove(InKeyEvent.GetKey());

	UDSBaseModel* CurrentModel = GetFiniteStateMachine()->GetCurrentModel();
	UDSActionExecuteBase* Action = ActionFactory->GetAction(CurrentModel, FPointerEvent());
	if (CurrentModel != nullptr && Action != nullptr)
	{
		return Action->OnKeyUp(MyGeometry, InKeyEvent);
	}

	return FReply::Unhandled();
}

FReply UDSFiniteState::OnKeyChar(const FGeometry& MyGeometry, const FCharacterEvent& InCharacterEvent)
{
	FReply EventReply = FReply::Unhandled();

	TArray<TCHAR> MoveKeyFilter = { TCHAR('W'), TCHAR('S'), TCHAR('A'), TCHAR('D'), TCHAR('Q'), TCHAR('E') };

	TCHAR UpperInput = FChar::ToUpper(InCharacterEvent.GetCharacter());
	if (MoveKeyFilter.Contains(UpperInput))
	{
		EventReply = FReply::Handled();
		FSlateApplication::Get().SetKeyboardFocus(UDSUISubsystem::GetInstance()->GetUserInputDispatcher());
	}

	UDSBaseModel* CurrentModel = GetFiniteStateMachine()->GetCurrentModel();
	UDSActionExecuteBase* Action = ActionFactory->GetAction(CurrentModel, FPointerEvent());
	if (CurrentModel != nullptr && Action != nullptr)
	{
		FReply ActionReply = Action->OnKeyChar(MyGeometry, InCharacterEvent);
		if (!EventReply.IsEventHandled())
		{
			EventReply = ActionReply;
		}
	}

	return EventReply;
}

FReply UDSFiniteState::OnMouseMove(const FGeometry& MyGeometry, const FPointerEvent& MouseEvent)
{
	UDSBaseModel* SelectedModel = PickDataUnderMouse(MyGeometry, MouseEvent);
	//UE_LOG(LogTemp, Warning, TEXT("UDSFiniteState::OnMouseMove --- [%s]"), (SelectedModel == nullptr ? TEXT("NULL Hover") : *SelectedModel->GetName()));
	if (UDSActionExecuteBase* Action = ActionFactory->GetAction(nullptr, MouseEvent))
	{
		Action->InitAction(SelectedModel, MouseEvent);
	}
	return FReply::Handled();
}

FReply UDSFiniteState::OnMouseWheel(const FGeometry& MyGeometry, const FPointerEvent& MouseEvent)
{
    UDSCameraSubsystem::GetInstance()->OnMouseWheelScroll(-MouseEvent.GetWheelDelta());
	return FReply::Handled();
}

void UDSFiniteState::OnMouseEnter(const FGeometry& MyGeometry, const FPointerEvent& MouseEvent)
{
}

void UDSFiniteState::OnMouseLeave(const FPointerEvent& MouseEvent)
{
}

void UDSFiniteState::OnDragEnter(const FGeometry& MyGeometry, const FDragDropEvent& DragDropEvent)
{
	if (TSharedPtr<FDSModelDragDropOperation> ModelOperation = DragDropEvent.GetOperationAs<FDSModelDragDropOperation>())
	{
		UDSFiniteStateMachine* Machine = Cast<UDSFiniteStateMachine>(GetOuter());
		if (UDSActionExecuteBase* Action = ActionFactory->GetAction(Machine->GetCurrentModel(), DragDropEvent, true))
		{
			Action->OnDragEnter(MyGeometry, DragDropEvent);
		}
	}
	else if (TSharedPtr<FUMGDragDropOp> UMGOperation = DragDropEvent.GetOperationAs<FUMGDragDropOp>())
	{
		ProcessUMGDragEnter(MyGeometry, DragDropEvent, UMGOperation);
	}
}

void UDSFiniteState::OnDragLeave(const FDragDropEvent& DragDropEvent)
{
	FWidgetPath WidgetPath = FSlateApplication::Get().LocateWindowUnderMouse(DragDropEvent.GetScreenSpacePosition(), FSlateApplication::Get().GetInteractiveTopLevelWindows(), false, 0);
	if (WidgetPath.IsValid())
	{
		for (int32 Index = 0; Index < WidgetPath.Widgets.Num(); ++Index)
		{
			TSharedPtr<SWidget> WidgetPtr = WidgetPath.Widgets[Index].Widget.ToSharedPtr();
			if (WidgetPtr->GetTypeAsString() == TEXT("SObjectWidget"))
			{
				TSharedPtr<SObjectWidget> ObjectWidget = StaticCastSharedPtr<SObjectWidget>(WidgetPtr);
				UUserWidget* UserWidget = ObjectWidget->GetWidgetObject();
				if (UserWidget != nullptr && UserWidget->IsA<URulerInputWidget>())
				{
					return;
				}
			}
		}
	}

	if (TSharedPtr<FDSModelDragDropOperation> ModelOperation = DragDropEvent.GetOperationAs<FDSModelDragDropOperation>())
	{
		UDSFiniteStateMachine* Machine = Cast<UDSFiniteStateMachine>(GetOuter());
		if (UDSActionExecuteBase* Action = ActionFactory->GetAction(Machine->GetCurrentModel(), DragDropEvent, true))
		{
			Action->OnDragLeave(DragDropEvent);
		}
	}
	else if (TSharedPtr<FUMGDragDropOp> UMGOperation = DragDropEvent.GetOperationAs<FUMGDragDropOp>())
	{
		ProcessUMGDragLeave(DragDropEvent, UMGOperation);
	}
}

FReply UDSFiniteState::OnDragDetected(const FGeometry& MyGeometry, const FPointerEvent& MouseEvent)
{
	if (MouseEvent.GetPressedButtons().IsEmpty())
	{
		return FReply::Handled();
	}
	
	FKey ActionKey = *MouseEvent.GetPressedButtons().begin();
	if (ActionKey == EKeys::LeftMouseButton)
	{
		UDSFiniteStateMachine* Machine = Cast<UDSFiniteStateMachine>(GetOuter());

		UDSBaseModel* ActionModel = Machine->GetDragDetectedModel(ActionKey);
		if (ActionModel != nullptr && IsPickedModelCanDrag(MyGeometry, MouseEvent, ActionModel))
		{
			Machine->SetCurrentModel(ActionModel);
			if (UDSActionExecuteBase* Action = ActionFactory->GetAction(Machine->GetCurrentModel(), MouseEvent, true))
			{
				Action->EnterAction(FDSModelExecuteType::ExecuteTransform, FDSBroadcastMarkData::NotBroadcastToMVCMark);
				return Action->OnDragDetected(MyGeometry, MouseEvent);	
			}
		}
		
		if (UDSActionExecuteBase* Action = ActionFactory->GetAction(nullptr, MouseEvent))
		{
			Action->InitAction(nullptr, MouseEvent);
		}

		return FReply::Handled().BeginDragDrop(FDSCameraDragDropOperation::NewMove(MouseEvent));
	}
	else if (ActionKey == EKeys::MiddleMouseButton)
	{
		if (UDSActionExecuteBase* Action = ActionFactory->GetAction(nullptr, MouseEvent))
		{
			Action->InitAction(nullptr, MouseEvent);
		}
		return FReply::Handled().BeginDragDrop(FDSCameraDragDropOperation::NewMove(MouseEvent));
	}
	else if (ActionKey == EKeys::RightMouseButton)
	{
		if (UDSActionExecuteBase* Action = ActionFactory->GetAction(nullptr, MouseEvent))
		{
			Action->InitAction(nullptr, MouseEvent);
		}
		UDSCameraSubsystem::GetInstance()->OnBeginRotateCamera(MyGeometry, MouseEvent);
		return FReply::Handled().BeginDragDrop(FDSCameraDragDropOperation::NewRotate(MouseEvent));
	}
	
	return FReply::Handled();
}

FReply UDSFiniteState::OnDragOver(const FGeometry& MyGeometry, const FDragDropEvent& DragDropEvent)
{
	if (TSharedPtr<FDSModelDragDropOperation> ModelOperation = DragDropEvent.GetOperationAs<FDSModelDragDropOperation>())
	{
		UDSFiniteStateMachine* Machine = Cast<UDSFiniteStateMachine>(GetOuter());
		if (UDSActionExecuteBase* Action = ActionFactory->GetAction(Machine->GetCurrentModel(), DragDropEvent, true))
		{
			return Action->OnDragOver(MyGeometry, DragDropEvent);	
		}
	}
	else if (TSharedPtr<FDSCameraDragDropOperation> CameraOperation = DragDropEvent.GetOperationAs<FDSCameraDragDropOperation>())
	{
		if (CameraOperation->IsRotating())
		{
			UDSCameraSubsystem::GetInstance()->OnRotateCamera(MyGeometry, DragDropEvent);
		}
		else
		{
			UDSCameraSubsystem::GetInstance()->OnMouseDragCamera(DragDropEvent);
		}
	}
	else if (TSharedPtr<FUMGDragDropOp> UMGOperation = DragDropEvent.GetOperationAs<FUMGDragDropOp>())
	{
		ProcessUMGDragOver(MyGeometry, DragDropEvent, UMGOperation);
	}
	
	return FReply::Handled();
}

FReply UDSFiniteState::OnDrop(const FGeometry& MyGeometry, const FDragDropEvent& DragDropEvent)
{
	if (TSharedPtr<FDSModelDragDropOperation> ModelOperation = DragDropEvent.GetOperationAs<FDSModelDragDropOperation>())
	{
		UDSFiniteStateMachine* Machine = Cast<UDSFiniteStateMachine>(GetOuter());
		if (UDSActionExecuteBase* Action = ActionFactory->GetAction(Machine->GetCurrentModel(), DragDropEvent, true))
		{
			Action->ResetAction();
			FReply DropHandle = Action->OnDrop(MyGeometry, DragDropEvent);
			return DropHandle;
		}
	}
	else if (TSharedPtr<FDSCameraDragDropOperation> CameraOperation = DragDropEvent.GetOperationAs<FDSCameraDragDropOperation>())
	{
		if (CameraOperation->IsRotating())
		{
			UDSCameraSubsystem::GetInstance()->OnEndRotateCamera(MyGeometry, DragDropEvent);
		}
	}
	else if (TSharedPtr<FUMGDragDropOp> UMGOperation = DragDropEvent.GetOperationAs<FUMGDragDropOp>())
	{
		if (DragDropEvent.GetEffectingButton() == EKeys::LeftMouseButton)
		{
			ProcessUMGDrop(MyGeometry, DragDropEvent, UMGOperation);
		}
		else if (DragDropEvent.GetEffectingButton() == EKeys::RightMouseButton)
		{
			ProcessUMGDragCancelled(MyGeometry, DragDropEvent, UMGOperation);
		}
	}
	
	return FReply::Handled();
}

FReply UDSFiniteState::OnInterruptDrop(const FDragDropEvent& DragDropEvent)
{
	if (DragDropEvent.GetOperation()->IsOfType<FDSModelDragDropOperation>())
	{
		TSharedPtr<FDSModelDragDropOperation> ModelDragDropOperation = StaticCastSharedPtr<FDSModelDragDropOperation>(DragDropEvent.GetOperation());
		UDSBaseModel* ActionModel = ModelDragDropOperation->GetActionModel();
		if (ActionModel == nullptr)
			return FReply::Handled();

		if (ActionModel->IsNewGenerate() || ActionModel->IsContinueGenerate() || ActionModel->IsCopyGenerate())
		{
			UDSMVCSubsystem::GetInstance()->SetCurrentModel(nullptr);
			ActionModel->OnExecuteAction(FDSModelExecuteType::ExecuteDelete);
			UDSDrawingSubsystem::GetInstance()->DrawingSnap(FDSVisionDataInfo());

			UHouseDesignPage* HousePage = Cast<UHouseDesignPage>(UDSUISubsystem::GetInstance()->GetWindowLayoutWidget()->GetMainDesignPageWidget()->GetModePageWidget());
			if (HousePage != nullptr)
			{
				HousePage->UnselectAllButtons();
			}
		}
		else
		{
			if (UDSActionExecuteBase* Action = ActionFactory->GetAction(ActionModel, DragDropEvent))
			{
				UGameViewportClient* ViewportClient = GetWorld()->GetGameViewport();
				if (ViewportClient != nullptr && ViewportClient->GetGameViewportWidget().IsValid())
				{
					TSharedPtr<SViewport> Viewport = ViewportClient->GetGameViewportWidget();
					FGeometry Geometry = Viewport->GetCachedGeometry();
					Action->OnDrop(Geometry, DragDropEvent);
				}
			}
			ActionModel->OnExecuteAction(FDSModelExecuteType::ExecuteUnHidden);
			UDSUISubsystem::GetInstance()->ProcessStateEvent(ActionModel, EUIOperationType::Selected, GetState());
			//UDSUISubsystem::GetInstance()->RefreshUI_ToolBar(ActionModel);
		}
	}

	return FReply::Handled();
}

FCursorReply UDSFiniteState::OnCursorQuery(const FGeometry& MyGeometry, const FPointerEvent& CursorEvent) const
{
	return FCursorReply::Unhandled();
}

TOptional<TSharedRef<SWidget>> UDSFiniteState::OnMapCursor(const FCursorReply& CursorReply) const
{
	return TOptional<TSharedRef<SWidget>>();
}

FReply UDSFiniteState::OnMouseButtonClick_UI(const FGeometry& MyGeometry, const FPointerEvent& MouseEvent)
{
	return FReply::Unhandled();
}

FReply UDSFiniteState::OnMouseMove_UI(const FGeometry& MyGeometry, const FPointerEvent& MouseEvent)
{
	return FReply::Handled();
}

UDSBaseModel* UDSFiniteState::PickDataUnderMouse(const FGeometry& MyGeometry, const FPointerEvent& MouseEvent)
{
	if (SelectType == EDSSelectType::E_Whole_Select || SelectType == EDSSelectType::E_Point_Capture)
	{
		return PickModelUnderMouse(MyGeometry, MouseEvent);
	}
	else if (SelectType == EDSSelectType::E_Whole_Part_Select || SelectType == EDSSelectType::E_Part_Select)
	{
		UDSBaseModel* PartModel = PickComponentUnderMouse(MyGeometry, MouseEvent);

		UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(PartModel);
		if (SelectType == EDSSelectType::E_Whole_Part_Select && CupboardModel != nullptr)
		{
			UDSCupboardModel* RootModel = CupboardModel->GetRootCupboardModel();
			if (UDSToolLibrary::IsInGroup(RootModel))
			{
				return RootModel;
			}
			else
			{
				return CupboardModel;
			}
		}
		else
		{
			return PartModel;
		}
	}
	return nullptr;
}

UDSBaseModel* UDSFiniteState::PickModelUnderMouse(const FGeometry& MyGeometry, const FPointerEvent& MouseEvent)
{
	FVector2D ViewportSpacePos = MyGeometry.AbsoluteToLocal(MouseEvent.GetScreenSpacePosition());

	FVector WorldLoc, WorldDir;
	if (!ADesignStationController::Get()->DeprojectScreenPositionToWorld(ViewportSpacePos.X, ViewportSpacePos.Y, WorldLoc, WorldDir))
	{
		return nullptr;
	}

	TArray<FHitResult> HitResults;
	FCollisionQueryParams Params = FCollisionQueryParams::DefaultQueryParam;
	Params.AddIgnoredActors(UDSResourceSubsystem::GetInstance()->GetAllEnvironmentActors());
	GetWorld()->LineTraceMultiByChannel(HitResults, WorldLoc, WorldLoc + WorldDir * 1000000.0f, ECollisionChannel::ECC_Visibility, Params);

	HitResults.Sort([](const FHitResult& A, const FHitResult& B) { return A.Distance < B.Distance; });

	static const TArray<EDSModelType> HighPriorityTools = 
	{ 
		EDSModelType::E_Gizmo, EDSModelType::E_Scale2D, EDSModelType::E_Scale2DForPath
	};
	
	static const TArray<EDSModelType> IgnoreTypesFor2D = { EDSModelType::E_Custom_Sink, EDSModelType::E_Custom_Stove, EDSModelType::E_Custom_RangeHood, EDSModelType::E_WaterTap };
	
	bool bIs2DScene = ADesignStationController::Get()->Is2DScene();
	TArray<UDSBaseModel*> IntersectedModels;
	for (auto& HitIter : HitResults)
	{
		AActor* OwnerActor = HitIter.GetActor();
		if (OwnerActor->ActorHasTag(FName(TEXT("BP"))) || OwnerActor->GetFName().ToString().Contains((TEXT("BP"))))
		{
			while (OwnerActor->GetOwner() != nullptr)
			{
				OwnerActor = OwnerActor->GetOwner();
			}
		}

		ADSBaseView* DSActor = Cast<ADSBaseView>(OwnerActor);
		if (DSActor == nullptr)
		{
			continue;
		}

		UDSBaseModel* CurrentModel = DSActor->GetModel();
		if (CurrentModel == nullptr)
		{
			continue;
		}

		while (CurrentModel->ShouldUseParentModel())
		{
			if (UDSToolLibrary::IsCustomDoorType(CurrentModel->GetModelType()))
			{
				break;
			}

			UDSBaseModel* ParentModel = CurrentModel->GetOwnerModel();
			if (ParentModel == nullptr)
			{
				break;
			}

			CurrentModel = ParentModel;
		}

		if (HighPriorityTools.Contains(CurrentModel->GetModelType()))
		{
			return CurrentModel;
		}

		EDSModelType CurrentModelType = CurrentModel->GetModelType();

		// 烟机、灶具、水槽、龙头在2D下不允许选中
		if (bIs2DScene && IgnoreTypesFor2D.Contains(CurrentModelType))
		{
			continue;
		}

		// 查找PickedModel是否属于某个功能件，如果属于某个功能件返回功能件
		if (UDSBaseModel* NearestFunctionalModel = UDSCupboardLibrary::FindNearestFunctionalCupboardModel(CurrentModel))
		{
			CurrentModel = NearestFunctionalModel;
		}

		IntersectedModels.Add(CurrentModel);
	}

	UDSBaseModel*  PickedModel = nullptr;
	if(IntersectedModels.IsValidIndex(0))
	{//组合判断

		static TArray<EDSModelType> HighPriorityModels = {
			EDSModelType::E_VirtualLight_IES, EDSModelType::E_VirtualLight_Line, EDSModelType::E_VirtualLight_Rect, EDSModelType::E_VirtualLight_Spherical, EDSModelType::E_VirtualSunLight,
			EDSModelType::E_House_Window, EDSModelType::E_House_Door, EDSModelType::E_House_Area_Label
		};

		int32 HighPriorityModelPos = IntersectedModels.IndexOfByPredicate([&](UDSBaseModel* InModel){ return HighPriorityModels.Contains(InModel->GetModelType()); });
		if (HighPriorityModelPos == INDEX_NONE)
		{
			PickedModel = IntersectedModels[0];
		}
		else
		{
			PickedModel = IntersectedModels[HighPriorityModelPos];
		}

		while (true)
		{
			if (UDSToolLibrary::IsInGroup(PickedModel))
			{
				PickedModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(EDSModelType::E_Group, PickedModel->GetGroupUUID());
			}
			else if (UDSToolLibrary::IsInMultiSelect(PickedModel))
			{
				PickedModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(EDSModelType::E_MultiSelect, PickedModel->GetMultiUUID());
			}
			else
			{
				break;
			}
		}
	}

	return PickedModel;
}

UDSBaseModel* UDSFiniteState::PickModelToDropMaterial(const FGeometry& MyGeometry, const FPointerEvent& MouseEvent, FHitResult& OutHitResult)
{
	FVector2D ViewportSpacePos = MyGeometry.AbsoluteToLocal(MouseEvent.GetScreenSpacePosition());

	FVector WorldLoc, WorldDir;
	if (!ADesignStationController::Get()->DeprojectScreenPositionToWorld(ViewportSpacePos.X, ViewportSpacePos.Y, WorldLoc, WorldDir))
	{
		return nullptr;
	}

	FCollisionQueryParams QueryParams = FCollisionQueryParams::DefaultQueryParam;
	QueryParams.bTraceComplex = true;
	QueryParams.bReturnFaceIndex = true;

	TArray<FHitResult> HitResults;
	GetWorld()->LineTraceMultiByChannel(HitResults, WorldLoc, WorldLoc + WorldDir * 1000000.0f, ECollisionChannel::ECC_Visibility, QueryParams);

	HitResults.Sort([](const FHitResult& A, const FHitResult& B) { return A.Distance < B.Distance; });

	TArray<EDSModelType> FilterTypes = { EDSModelType::E_Furniture_HouseFurniture, EDSModelType::E_House_Wall };
	
	for (auto& HitIter : HitResults)
	{
		AActor* OwnerActor = HitIter.GetActor();
		if (OwnerActor->ActorHasTag(FName(TEXT("BP"))) || OwnerActor->GetFName().ToString().Contains((TEXT("BP"))))
		{
			while (OwnerActor->GetOwner() != nullptr)
			{
				OwnerActor = OwnerActor->GetOwner();
			}
		}


		ADSBaseView* DSActor = Cast<ADSBaseView>(OwnerActor);
		if (DSActor == nullptr)
		{
			continue;
		}

		UDSBaseModel* CurrentModel = DSActor->GetModel();
		if (CurrentModel == nullptr)
		{
			continue;
		}

		while (CurrentModel->ShouldUseParentModel())
		{
			UDSBaseModel* ParentModel = CurrentModel->GetOwnerModel();
			if (ParentModel == nullptr)
			{
				break;
			}
			CurrentModel = ParentModel;
		}

		if (FilterTypes.Contains(CurrentModel->GetModelType()))
		{
			OutHitResult = HitIter;
			return CurrentModel;
		}
	}

	return nullptr;
}

UDSBaseModel* UDSFiniteState::PickComponentUnderMouse(const FGeometry& MyGeometry, const FPointerEvent& MouseEvent)
{
	FVector2D ViewportSpacePos = MyGeometry.AbsoluteToLocal(MouseEvent.GetScreenSpacePosition());

	FVector WorldLoc, WorldDir;
	if (!ADesignStationController::Get()->DeprojectScreenPositionToWorld(ViewportSpacePos.X, ViewportSpacePos.Y, WorldLoc, WorldDir))
	{
		return nullptr;
	}

	TArray<FHitResult> HitResults;
	FCollisionQueryParams Params = FCollisionQueryParams::DefaultQueryParam;
	Params.AddIgnoredActors(UDSResourceSubsystem::GetInstance()->GetAllEnvironmentActors());
	GetWorld()->LineTraceMultiByChannel(HitResults, WorldLoc, WorldLoc + WorldDir * 1000000.0f, ECollisionChannel::ECC_Visibility, Params);

	HitResults.Sort([](const FHitResult& A, const FHitResult& B) { return A.Distance < B.Distance; });
	
	for (auto& HitIter : HitResults)
	{
		AActor* OwnerActor = HitIter.GetActor();
		
		while (OwnerActor != nullptr && OwnerActor->GetOwner() != nullptr && OwnerActor->GetOwner()->ActorHasTag(FName(TEXT("Cmp"))))
		{
			OwnerActor = OwnerActor->GetOwner();
		}
		
		if (OwnerActor != nullptr)
		{
			UDSBaseModel* Model = nullptr;
			if (ADSBaseView* DSActor = Cast<ADSBaseView>(OwnerActor))
			{
				Model = DSActor->GetModel();	
			}
			else if (ADSBaseView* ParentView = Cast<ADSBaseView>(OwnerActor->GetOwner()))
			{
				Model = ParentView->GetModel();
			}

			if (UDSToolLibrary::IsInMultiSelect(Model))
			{
				UDSBaseModel* MultiModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(EDSModelType::E_MultiSelect, Model->GetMultiUUID());
				return MultiModel;
			}
			return Model;
		}
	}

	return nullptr;
}

UDSBaseModel* UDSFiniteState::PickModelUnderMouseExceptMulti(const FGeometry& MyGeometry, const FPointerEvent& MouseEvent)
{
	FVector2D ViewportSpacePos = MyGeometry.AbsoluteToLocal(MouseEvent.GetScreenSpacePosition());

	FVector WorldLoc, WorldDir;
	if (!ADesignStationController::Get()->DeprojectScreenPositionToWorld(ViewportSpacePos.X, ViewportSpacePos.Y, WorldLoc, WorldDir))
	{
		return nullptr;
	}

	TArray<FHitResult> HitResults;
	GetWorld()->LineTraceMultiByChannel(HitResults, WorldLoc, WorldLoc + WorldDir * 1000000.0f, ECollisionChannel::ECC_Visibility);

	HitResults.Sort([](const FHitResult& A, const FHitResult& B) { return A.Distance < B.Distance; });

	TArray<EDSModelType> HighPriorityTypes = { EDSModelType::E_Gizmo, EDSModelType::E_Scale2D, EDSModelType::E_Scale2DForPath, EDSModelType::E_House_Window, EDSModelType::E_House_Door, EDSModelType::E_House_Area_Label ,EDSModelType::E_VirtualSunLight };

	TArray<UDSBaseModel*> IntersectedModels;
	for (auto& HitIter : HitResults)
	{
		AActor* OwnerActor = HitIter.GetActor();
		if (OwnerActor->ActorHasTag(FName(TEXT("BP"))) || OwnerActor->GetFName().ToString().Contains((TEXT("BP"))))
		{
			while (OwnerActor->GetOwner() != nullptr)
			{
				OwnerActor = OwnerActor->GetOwner();
			}
		}

		ADSBaseView* DSActor = Cast<ADSBaseView>(OwnerActor);
		if (DSActor == nullptr)
		{
			continue;
		}

		UDSBaseModel* CurrentModel = DSActor->GetModel();
		if (CurrentModel == nullptr)
		{
			continue;
		}

		while (CurrentModel->ShouldUseParentModel())
		{
			UDSBaseModel* ParentModel = CurrentModel->GetOwnerModel();
			if (ParentModel == nullptr)
			{
				break;
			}
			CurrentModel = ParentModel;
		}

		if (HighPriorityTypes.Contains(CurrentModel->GetModelType()))
		{
			return CurrentModel;
		}

		IntersectedModels.Add(CurrentModel);
	}

	UDSBaseModel* PickedModel = nullptr;
	if (IntersectedModels.IsValidIndex(0))
	{//组合判断
		PickedModel = IntersectedModels[0];
		if (UDSToolLibrary::IsInGroup(PickedModel))
		{
			PickedModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(EDSModelType::E_Group, PickedModel->GetGroupUUID());
		}
	}

	return PickedModel;
}

bool UDSFiniteState::IsPickedModelCanDrag(const FGeometry& MyGeometry, const FPointerEvent& MouseEvent, UDSBaseModel* PickedModel)
{
	EDSModelType ModelType = PickedModel->GetModelType();
	if (PickedModel == nullptr)
	{
		return false;
	}

	TArray<EDSModelType> FilterTypes;
	
	if (ADesignStationController::Get()->Is2DScene())
	{
		FilterTypes = { EDSModelType::E_House_Area, EDSModelType::E_House_Area_Label };
	}
	else
	{
		FilterTypes = { EDSModelType::E_House_Area, EDSModelType::E_House_Pillar, EDSModelType::E_House_Platform, EDSModelType::E_House_Beam, EDSModelType::E_House_Area_Label };
	}

	return !FilterTypes.Contains(ModelType);
}

FReply UDSFiniteState::MultiSelectOperator(const FGeometry& MyGeometry, const FPointerEvent& MouseEvent, UDSBaseModel* InModel)
{
	UDSBaseModel* CurrentSelectModel = GetFiniteStateMachine()->GetCurrentModel();
	
	bool IsCurrentMultiModel = UDSToolLibrary::IsMultiOperator(CurrentSelectModel);
	/*bool IsCurrentCanMulti = IsCurrentMultiModel ? true :
		(CurrentSelectModel != nullptr ?
			(CurrentSelectModel->IsCanMultiSelect() && CanModelAddToMultiInThisState(CurrentSelectModel)) : false);
	bool IsUnderCanMulti = InModel != nullptr ? (InModel->IsCanMultiSelect() && CanModelAddToMultiInThisState(InModel)) : false;*/

	bool IsCurrrentInGroup = false;
	bool CanMulti = CanMultiOperator(CurrentSelectModel, InModel, IsCurrrentInGroup);
	if(CanMulti)
	{//可多选
		UDSMultiModel* MultiModel = nullptr;
		if (IsCurrentMultiModel)
		{
			MultiModel = Cast<UDSMultiModel>(CurrentSelectModel); 
		}
		else
		{
			MultiModel = UDSMultiModel::CreateMultiModel();
		}

		bool IsOnlyData = UDSToolLibrary::GetOwnerModelRecursion(InModel) != InModel;
		if (IsCurrrentInGroup)
		{
			InModel = UDSToolLibrary::GetOwnerModelRecursion(InModel);
			IsOnlyData = true;
		}

		TArray<UDSBaseModel*> AddToMulti = { InModel };
		if(!IsCurrentMultiModel)
		{
			AddToMulti.Insert(CurrentSelectModel, 0);
		}

		MultiModel->AddSelect(AddToMulti, IsOnlyData, true);
		ModelShowStateChange(GetFiniteStateMachine()->GetCurrentModelRef(), MultiModel, false, nullptr);
		
	}
	else
	{
		if (SelectType == EDSSelectType::E_Whole_Part_Select)
		{
			UDSBaseModel* CurrentSelectModel = GetFiniteStateMachine()->GetCurrentModel();
			UDSBaseModel* CurrentParentModel = UDSToolLibrary::GetOwnerModelRecursion(CurrentSelectModel);
			UDSBaseModel* InParentModel = UDSToolLibrary::GetOwnerModelRecursion(InModel);
			//相同上层或相同组
			if ((CurrentParentModel == InParentModel) || UDSToolLibrary::IsInSameGroup(CurrentParentModel, InParentModel))
			{
				ModelShowStateChange(GetFiniteStateMachine()->GetCurrentModelRef(), InModel, true, InParentModel);
			}
			else
			{
				if (UDSToolLibrary::IsInGroup(InParentModel))
				{
					if (UDSBaseModel* GroupModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(EDSModelType::E_Group, InParentModel->GetGroupUUID()))
					{
						ModelShowStateChange(GetFiniteStateMachine()->GetCurrentModelRef(), GroupModel, false, nullptr);
					}
				}
				else
				{
					ModelShowStateChange(GetFiniteStateMachine()->GetCurrentModelRef(), InParentModel, false, nullptr);
				}
				SwitchStateSelectType(EDSSelectType::E_Whole_Select);
			}
			
		}
		else
		{
			if (InModel != nullptr)
			{
				UDSBaseModel* ParentModel = UDSToolLibrary::GetOwnerModelRecursion(InModel);
				ModelShowStateChange(GetFiniteStateMachine()->GetCurrentModelRef(), InModel, ParentModel != InModel, ParentModel);
			}
			else
			{
				ModelShowStateChange(GetFiniteStateMachine()->GetCurrentModelRef(), nullptr, false, nullptr);
			}
		}
	}
	
	return FReply::Handled();
	
}

bool UDSFiniteState::CanMultiOperator(UDSBaseModel* CurrentModel, UDSBaseModel* UnderModel, bool& IsOriginInGroup)
{
	bool IsCurrentCanMulti = false;
	bool IsUnderCanMulti = false;
	if (UDSToolLibrary::IsMultiOperator(CurrentModel))
	{
		IsCurrentCanMulti = true;
		IsOriginInGroup = UDSToolLibrary::IsMultiIncludeInGroupNoRet(CurrentModel);
		IsUnderCanMulti = UnderModel != nullptr ? (UnderModel->IsCanMultiSelect() && CanModelAddToMultiInThisState(CurrentModel, UnderModel)) : false;
	}
	else
	{
		if (UDSToolLibrary::IsInGroup(CurrentModel))
		{
			IsOriginInGroup = true;
			if (SelectType == EDSSelectType::E_Whole_Part_Select)
			{
				UnderModel = UDSToolLibrary::GetOwnerModelRecursion(UnderModel);
			}
			FString	CurrentGroupUUID = CurrentModel->GetGroupUUID();
			FString UnderModelGroup = UnderModel != nullptr ? UnderModel->GetGroupUUID() : TEXT("");
			if (CurrentGroupUUID.Equals(UnderModelGroup))
			{
				IsCurrentCanMulti = CurrentModel != nullptr ? (CurrentModel->IsCanMultiSelect() && CanModelAddToMultiInThisState(CurrentModel, CurrentModel)) : false;
				IsUnderCanMulti = UnderModel != nullptr ? (UnderModel->IsCanMultiSelect() && CanModelAddToMultiInThisState(CurrentModel, UnderModel)) : false;
			}
			else
			{
				return false;
			}
		}
		else
		{
			IsCurrentCanMulti = CurrentModel != nullptr ? (CurrentModel->IsCanMultiSelect() && CanModelAddToMultiInThisState(CurrentModel, CurrentModel)) : false;
			IsUnderCanMulti = UnderModel != nullptr ? (UnderModel->IsCanMultiSelect() && CanModelAddToMultiInThisState(CurrentModel, UnderModel)) : false;
		}
	}

	return IsCurrentCanMulti && IsUnderCanMulti;

}

bool UDSFiniteState::CanModelAddToMultiInThisState(UDSBaseModel* CurModel, UDSBaseModel* InModel)
{
	if (InModel != nullptr)
	{
		if (SelectType == EDSSelectType::E_Whole_Part_Select)
		{
			if (UDSToolLibrary::IsMultiSelectType(CurModel))
			{
				FString MultiIncludeInnerGroup;
				if (UDSToolLibrary::IsMultiIncludeInGroup(CurModel, MultiIncludeInnerGroup))
				{//组合内部多选, 需考虑是否同一组合
					UDSBaseModel* ConsiderParent = UDSToolLibrary::GetOwnerModelRecursion(InModel);
					return MultiIncludeInnerGroup.Equals(ConsiderParent->GetGroupUUID());
				}
				else
				{//部件编辑多选，需考虑是否同一根
					UDSBaseModel* OwnerIncludeOwner = UDSToolLibrary::GetMultiIncludeOwner(CurModel);
					UDSBaseModel* InParent = UDSToolLibrary::GetOwnerModelRecursion(InModel);
					return OwnerIncludeOwner != nullptr && InParent != nullptr && OwnerIncludeOwner == InParent;
				}
			}
			else
			{
				UDSBaseModel* CurParent = UDSToolLibrary::GetOwnerModelRecursion(CurModel);
				UDSBaseModel* InParent = UDSToolLibrary::GetOwnerModelRecursion(InModel);
				if (UDSToolLibrary::IsInGroup(CurParent) && UDSToolLibrary::IsInGroup(InParent))
				{
					return CurParent->GetGroupUUID().Equals(InParent->GetGroupUUID());
				}
				else
				{
					return CurModel != nullptr && InParent != nullptr && CurParent == InParent;
				}
			}
		}
		else
		{
			EDSModelType ModelType = InModel->GetModelType();
			return ModelType >= EDSModelType::E_Furniture_MoldingCeiling;
		}
		
	}

	return false;
}

bool UDSFiniteState::ConsiderStateToChange(const EDSStateConsiderEventType& ConsiderEvent, const FGeometry& MyGeometry, const FPointerEvent& CursorEvent)
{
	switch (ConsiderEvent)
	{
	case EDSStateConsiderEventType::E_Click: return ConsiderStateToChange_Click(MyGeometry, CursorEvent);
	case EDSStateConsiderEventType::E_DoubleClick: return ConsiderStateToChange_DoubleClick(MyGeometry, CursorEvent);
	default: return true;
	}
}

bool UDSFiniteState::ConsiderStateToChange_Click(const FGeometry& MyGeometry, const FPointerEvent& CursorEvent)
{
	return true;
}

bool UDSFiniteState::ConsiderStateToChange_DoubleClick(const FGeometry& MyGeometry, const FPointerEvent& CursorEvent)
{
	return true;
}

void UDSFiniteState::ProcessUMGDragEnter(const FGeometry& MyGeometry, const FDragDropEvent& DragDropEvent, const TSharedPtr<FUMGDragDropOp>& Operation)
{
	UDSBaseModel* ActionModel = nullptr;
	if (Operation->GetOperation()->IsA<UResourceItemDragDropOperation>())
	{
		UResourceItemDragDropOperation* ResourceOp = Cast<UResourceItemDragDropOperation>(Operation->GetOperation());
		ActionModel = ResourceOp->ActionModel;
	}
	else if (Operation->GetOperation()->IsA<UHouseButtonDragDropOperation>())
	{
		UHouseButtonDragDropOperation* HouseOp = Cast<UHouseButtonDragDropOperation>(Operation->GetOperation());
		ActionModel = HouseOp->ActionModel;
	}
	else if (Operation->GetOperation()->IsA<UCollectionItemDragDropOperation>())
	{
		UCollectionItemDragDropOperation* DragDropOp = Cast<UCollectionItemDragDropOperation>(Operation->GetOperation());
		ActionModel = DragDropOp->ActionModel;
	}
	else if (Operation->GetOperation()->IsA<USpotLightDragDropOperation>())
	{
		USpotLightDragDropOperation* DragDropOp = Cast<USpotLightDragDropOperation>(Operation->GetOperation());
		ActionModel = DragDropOp->ActionModel;
	}

	if (ActionModel == nullptr)
	{
		return;
	}

	Operation->GetOperation()->DefaultDragVisual->SetVisibility(ESlateVisibility::Hidden);
	ActionModel->OnExecuteAction(FDSModelExecuteType::ExecuteUnHidden);

	ActionModel->GetPropertySharedPtr()->SetIsMoving(true);

	UDSMVCSubsystem::GetInstance()->SetCurrentModel(ActionModel);
	UDSActionExecuteBase* Action = ActionFactory->GetAction(ActionModel, DragDropEvent, true);
	if (Action == nullptr)
	{
		return;
	}

	Action->OnDragDetected(MyGeometry, DragDropEvent);
}

void UDSFiniteState::ProcessUMGDragLeave(const FDragDropEvent& DragDropEvent, const TSharedPtr<FUMGDragDropOp>& Operation)
{
	UDSBaseModel* ActionModel = nullptr;
	if (Operation->GetOperation()->IsA<UResourceItemDragDropOperation>())
	{
		UResourceItemDragDropOperation* ResourceOp = Cast<UResourceItemDragDropOperation>(Operation->GetOperation());
		ActionModel = ResourceOp->ActionModel;
	}
	else if (Operation->GetOperation()->IsA<UHouseButtonDragDropOperation>())
	{
		UHouseButtonDragDropOperation* HouseOp = Cast<UHouseButtonDragDropOperation>(Operation->GetOperation());
		ActionModel = HouseOp->ActionModel;
	}
	else if (Operation->GetOperation()->IsA<UCollectionItemDragDropOperation>())
	{
		UCollectionItemDragDropOperation* HouseOp = Cast<UCollectionItemDragDropOperation>(Operation->GetOperation());
		ActionModel = HouseOp->ActionModel;
	}
	else if (Operation->GetOperation()->IsA<USpotLightDragDropOperation>())
	{
		USpotLightDragDropOperation* HouseOp = Cast<USpotLightDragDropOperation>(Operation->GetOperation());
		ActionModel = HouseOp->ActionModel;
	}
	
	if (ActionModel == nullptr)
	{
		return;
	}

	Operation->GetOperation()->DefaultDragVisual->SetVisibility(ESlateVisibility::Visible);
	ActionModel->OnExecuteAction(FDSModelExecuteType::ExecuteHidden);

	ActionModel->GetPropertySharedPtr()->SetIsMoving(false);
}

void UDSFiniteState::ProcessUMGDragOver(const FGeometry& MyGeometry, const FDragDropEvent& DragDropEvent, const TSharedPtr<FUMGDragDropOp>& Operation)
{
	UDSBaseModel* ActionModel = nullptr;
	if (Operation->GetOperation()->IsA<UResourceItemDragDropOperation>())
	{
		UResourceItemDragDropOperation* ResourceOp = Cast<UResourceItemDragDropOperation>(Operation->GetOperation());
		ActionModel = ResourceOp->ActionModel;
	}
	else if (Operation->GetOperation()->IsA<UHouseButtonDragDropOperation>())
	{
		UHouseButtonDragDropOperation* HouseOp = Cast<UHouseButtonDragDropOperation>(Operation->GetOperation());
		ActionModel = HouseOp->ActionModel;
	}
	else if (Operation->GetOperation()->IsA<UCollectionItemDragDropOperation>())
	{
		UCollectionItemDragDropOperation* HouseOp = Cast<UCollectionItemDragDropOperation>(Operation->GetOperation());
		ActionModel = HouseOp->ActionModel;
	}
	else if (Operation->GetOperation()->IsA<USpotLightDragDropOperation>())
	{
		USpotLightDragDropOperation* HouseOp = Cast<USpotLightDragDropOperation>(Operation->GetOperation());
		ActionModel = HouseOp->ActionModel;
	}
	
	if (ActionModel == nullptr)
	{
		return;
	}
	
	UDSActionExecuteBase* Action = ActionFactory->GetAction(ActionModel, DragDropEvent, false);
	if (Action == nullptr)
	{
		return;
	}
	
	Action->OnDragOver(MyGeometry, DragDropEvent);
}

void UDSFiniteState::ProcessUMGDrop(const FGeometry& MyGeometry, const FDragDropEvent& DragDropEvent, const TSharedPtr<FUMGDragDropOp>& Operation)
{
	UCollectionItem* CollectionItemWidget = nullptr;
	UResourceItem* ItemWidget = nullptr;
	UDSBaseModel* ActionModel = nullptr;
	
	if (Operation->GetOperation()->IsA<UResourceItemDragDropOperation>())
	{
		UResourceItemDragDropOperation* ResourceOp = Cast<UResourceItemDragDropOperation>(Operation->GetOperation());
		ActionModel = ResourceOp->ActionModel;
		ItemWidget = Cast<UResourceItem>(ResourceOp->Payload);
		if (ActionModel)
		{
			UDSMVCSubsystem::GetInstance()->OnRefreshUIPropertyWidget(ActionModel);
		}
	}
	else if (Operation->GetOperation()->IsA<UCollectionItemDragDropOperation>())
	{
		UCollectionItemDragDropOperation* HouseOp = Cast<UCollectionItemDragDropOperation>(Operation->GetOperation());
		ActionModel = HouseOp->ActionModel;
		CollectionItemWidget = Cast<UCollectionItem>(HouseOp->Payload);
		if (ActionModel)
		{
			UDSMVCSubsystem::GetInstance()->OnRefreshUIPropertyWidget(ActionModel);
		}
	}
	else if (Operation->GetOperation()->IsA<USpotLightDragDropOperation>())
	{
		USpotLightDragDropOperation* HouseOp = Cast<USpotLightDragDropOperation>(Operation->GetOperation());
		ActionModel = HouseOp->ActionModel;
		CollectionItemWidget = Cast<UCollectionItem>(HouseOp->Payload);
		if (ActionModel)
		{
			UDSMVCSubsystem::GetInstance()->OnRefreshUIPropertyWidget(ActionModel);
		}
	}
	else if (Operation->GetOperation()->IsA<UHouseButtonDragDropOperation>())
	{
		UHouseButtonDragDropOperation* HouseOp = Cast<UHouseButtonDragDropOperation>(Operation->GetOperation());
		ActionModel = HouseOp->ActionModel;
		if (ActionModel)
		{
			UDSMVCSubsystem::GetInstance()->OnRefreshUIPropertyWidget(ActionModel);
		}
	}
	else
	{
		return;
	}

	if (CollectionItemWidget != nullptr)
	{
		auto& CollectionInfo = CollectionItemWidget->GetCollectionInfo();
		if (CollectionInfo.Type == EDSCollectionCategory::Material && !CollectionInfo.IsGroup())
		{
			FHitResult HitResult;
			UDSBaseModel* SelectedModel = PickModelToDropMaterial(MyGeometry, DragDropEvent, HitResult);
			if (SelectedModel == nullptr)
			{
				return;
			}

			if (CollectionItemWidget->GetResourceInfos().IsEmpty())
			{
				return;
			}
			
			const FDSResourceInfo& MaterialResourceInfo = CollectionItemWidget->GetResourceInfos()[0];
			ProcessUMGDropMaterialOnModel(MaterialResourceInfo, SelectedModel, HitResult);
		}
	}
	else if (ItemWidget != nullptr)
	{
		if (ItemWidget->GetResourceInfo().Type == EDSResourceType::Material)
		{
			FHitResult HitResult;
			UDSBaseModel* SelectedModel = PickModelToDropMaterial(MyGeometry, DragDropEvent, HitResult);
			if (SelectedModel == nullptr)
			{
				return;
			}

			ProcessUMGDropMaterialOnModel(ItemWidget->GetResourceInfo(), SelectedModel, HitResult);
		}
	}

	if (ActionModel != nullptr)
	{
		bool bHasGlobalRevokeUUID = !UDSRevokeSubsystem::GetInstance()->GetGlobalCommandUUID().IsEmpty();
		if (!bHasGlobalRevokeUUID)
		{
			UDSRevokeSubsystem::GetInstance()->SetGlobalCommandUUID(FGuid::NewGuid().ToString());
		}
	
		if (UDSMVCSubsystem::IsInitialized())
		{
			if (UDSActionExecuteBase* Action = UDSMVCSubsystem::GetInstance()->GetActionByModel(ActionModel, DragDropEvent, false))
			{
				//drop前重置坐标轴状态
				if (UDSPendantSubsystem::IsInitialized())
				{
					UDSPendantSubsystem::GetInstance()->SetGizmoIsEnabled(true);
				}
				
				Action->OnDrop(MyGeometry, DragDropEvent);
			}
		}

		if (ItemWidget != nullptr && ItemWidget->GetResourceInfo().Type == EDSResourceType::Custom)
		{
			UDSUISubsystem::GetInstance()->GetWindowLayoutWidget()->GetMainDesignPageWidget()->ApplyCustomCupboardStyle(ActionModel, DragDropEvent.GetScreenSpacePosition(), !bHasGlobalRevokeUUID);
		}
		else if (CollectionItemWidget != nullptr)
		{
			if (CollectionItemWidget->GetCollectionInfo().IsGroup())
			{

			}
			else if (CollectionItemWidget->GetCollectionInfo().CollectionType == EDSCollectionType::Custom
					 || CollectionItemWidget->GetCollectionInfo().CollectionType == EDSCollectionType::CustomIstance)
			{
				UDSUISubsystem::GetInstance()->GetWindowLayoutWidget()->GetMainDesignPageWidget()->ApplyCustomCupboardStyle(ActionModel, DragDropEvent.GetScreenSpacePosition(), !bHasGlobalRevokeUUID);
			}
		}
		else
		{
			if (!bHasGlobalRevokeUUID)
			{
				UDSRevokeSubsystem::GetInstance()->SetGlobalCommandUUID(TEXT(""));
			}
		}
	}
}

void UDSFiniteState::ProcessUMGDragCancelled(const FGeometry& MyGeometry, const FDragDropEvent& DragDropEvent, const TSharedPtr<class FUMGDragDropOp>& Operation)
{
	UDSBaseModel* ActionModel = nullptr;
	UDSBaseModel* CurrentModel = UDSMVCSubsystem::GetInstance()->GetCurrentModel();

	if (Operation->GetOperation()->IsA<UResourceItemDragDropOperation>())
	{
		UResourceItemDragDropOperation* ResourceOp = Cast<UResourceItemDragDropOperation>(Operation->GetOperation());
		ActionModel = ResourceOp->ActionModel;
	}
	else if (Operation->GetOperation()->IsA<UHouseButtonDragDropOperation>())
	{
		UHouseButtonDragDropOperation* HouseOp = Cast<UHouseButtonDragDropOperation>(Operation->GetOperation());
		ActionModel = HouseOp->ActionModel;
	}

	if (ActionModel == nullptr)
	{
		return;
	}

	if (UDSPendantSubsystem::IsInitialized())
	{
		UDSPendantSubsystem::GetInstance()->SetGizmoIsEnabled(true);
	}

	if (UDSCupboardLibrary::IsFunctionalCupboardModel(ActionModel))
	{
		UDSBaseModel* OwnerModel = ActionModel->GetOwnerModel();
		if (OwnerModel != nullptr && OwnerModel->IsA<UDSCupboardModel>())
		{
			Cast<UDSCupboardModel>(OwnerModel)->RemoveFunctionalCupboardModel(ActionModel);
		}
		//功能件取消Drag时移除上一次的Undo
		UDSRevokeSubsystem::GetInstance()->PopUndoCommandWithNoExecute(GetStateRevokePoolMark());
	}

	ActionModel->OnExecuteAction(FDSModelExecuteType::ExecuteDelete);

	UDSUISubsystem::GetInstance()->HiddenUI();
	UDSMVCSubsystem::GetInstance()->SetCurrentModel(nullptr);
}

void UDSFiniteState::ProcessUMGDropMaterialOnModel(const FDSResourceInfo& MaterialResource, UDSBaseModel* SelectedModel, const FHitResult& HitResult)
{
	TSharedPtr<FDSBaseProperty> ModelProperty = SelectedModel->GetPropertySharedPtr();
	if (!ModelProperty)
	{
		UE_LOG(LogTemp, Warning, TEXT("%s - Can not get model's property."), __FUNCTIONW__);
		return;
	}

	int32 SectionIndex = -1;
	if (SelectedModel->GetModelType() == EDSModelType::E_House_Wall)
	{
		ADSBaseView* View = Cast<ADSBaseView>(HitResult.GetActor());
		TSharedPtr<FDSBaseProperty> PlaneProperty = View->GetModel()->GetPropertySharedPtr();
		if (PlaneProperty->ProductProperty.Name.Equals(TEXT("Bottom")))
		{
			SectionIndex = 1;
		}
		else if (PlaneProperty->ProductProperty.Name.Equals(TEXT("Top")))
		{
			SectionIndex = 2;
		}
		else
		{
			FString SegmentIndex;
			if (PlaneProperty->ProductProperty.Name.Split(TEXT("Seg"), nullptr, &SegmentIndex))
			{
				SectionIndex = FCString::Atoi(*SegmentIndex) + 3;
			}
		}
	}
	else
	{
		if (SelectedModel->GetModelType() == EDSModelType::E_Furniture_HouseFurniture)
		{
			if (TSharedPtr<FDSSoftFurnitureProperty> SoftProp = StaticCastSharedPtr<FDSSoftFurnitureProperty>(ModelProperty))
			{
				if (!SoftProp->BusinessInfo.IsSelf.IsEmpty())
				{
					int32 IsSelf = FCString::Atoi(*SoftProp->BusinessInfo.IsSelf);
					if (IsSelf == 0)
					{
						return;
					}
				}
			}
		}

		HitResult.GetComponent()->GetMaterialFromCollisionFaceIndex(HitResult.FaceIndex, SectionIndex);
	}

	FString ResourceFileMD5 = MaterialResource.GetResourceFile(EDSResourceQuality::Low).MD5;

	ModelProperty->MaterialProperty.SetSectionMaterial(
		SectionIndex,
		ResourceFileMD5,
		MaterialResource.Id,
		false, true, FCString::Atod(*MaterialResource.Width), FCString::Atod(*MaterialResource.Height)
	);

	ModelProperty->MaterialProperty.SetSectionMaterial(
		SectionIndex,
		ResourceFileMD5,
		MaterialResource.Id,
		true, true, FCString::Atod(*MaterialResource.Width), FCString::Atod(*MaterialResource.Height)
	);

	if (UDSResourceSubsystem::GetInstance()->FindMaterialFromCache(ResourceFileMD5) != nullptr)
	{
		SelectedModel->OnExecuteAction(FDSModelExecuteType::ExecuteRefreshMaterialForce);
	}
	else if (SelectedModel->SupportsLazyLoad())
	{
		if (SelectedModel->GetModelType() == EDSModelType::E_House_Wall)
		{
			SelectedModel->OnExecuteAction(FDSModelExecuteType::ExecuteRefreshMaterial);
		}

		SelectedModel->StopObserveResourceStatusEvent();
		SelectedModel->ObserveResourceStatusEvent();
	}
}

UDSFiniteStateMachine* UDSFiniteState::GetFiniteStateMachine()
{
	if (GetOuter() && Cast<UDSFiniteStateMachine>(GetOuter()))
	{
		return Cast<UDSFiniteStateMachine>(GetOuter());
	}
	return nullptr;
}

void UDSFiniteState::UnSelectModel(UDSBaseModel* ModifyModel)
{
	if (DS_MODEL_VALID_FOR_USE(ModifyModel))
	{
		if (UDSToolLibrary::IsGroupType(ModifyModel))
		{
			//group
			ModifyModel->OnExecuteAction(FDSModelExecuteType::ExecuteUnSelect);
		}
		else if (UDSToolLibrary::IsInGroup(ModifyModel))
		{
			//group item
			if (UDSBaseModel* GroupModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(EDSModelType::E_Group, ModifyModel->GetGroupUUID()))
			{
				GroupModel->OnExecuteAction(FDSModelExecuteType::ExecuteUnSelectComponent);
			}
			ModifyModel->OnExecuteAction(FDSModelExecuteType::ExecuteUnSelect);
		}
		else
		{
			//item
			UDSBaseModel* OwnerModel = ModifyModel->ShouldUseParentModel() ? ModifyModel->GetOwnerModel() : ModifyModel->GetTopLevelOwnerModel();
			if (OwnerModel)
			{
				OwnerModel->OnExecuteAction(FDSModelExecuteType::ExecuteUnSelect);
			}
			if (ModifyModel != OwnerModel)
			{
				ModifyModel->OnExecuteAction(FDSModelExecuteType::ExecuteUnSelect);
			}
		}
	}

	UDSUISubsystem::GetInstance()->ProcessStateEvent(ModifyModel, EUIOperationType::Unselected, GetState());
}

void UDSFiniteState::EnterState(UDSBaseModel* InModel, const int32& InAdditionState)
{
	UDSUISubsystem::GetInstance()->RefreshUI_Property_WithState(InModel, GetState());

	if (UDSUISubsystem::GetInstance()->GetFunctionBarBoxWidget() && UDSUISubsystem::GetInstance()->GetFunctionBarBoxWidget()->IsVisible())
	{
		UDSUISubsystem::GetInstance()->GetFunctionBarBoxWidget()->SwitchButtons(UDSMVCSubsystem::GetInstance()->GetCurrentModel(), false);
	}
}

UDSBaseModel* UDSFiniteState::GetSplitAreaLine(const FVector& InLoc) const
{
	auto SplitLines = UDSMVCSubsystem::GetInstance()->GetModels(EDSModelType::E_House_Area_Split_Line);
	UDSBaseModel* SL = nullptr;
	auto MinDist = MAX_dbl;
	for (auto& Iter : SplitLines)
	{
		auto Prop = static_cast<FDSDashedProperty*>(Iter->GetProperty());
		auto Start = Prop->SegmentStart;
		auto End = Prop->SegmentEnd;
		auto Dist = FMath::PointDistToSegment(InLoc, Start, End);
		if (Dist < MinDist)
		{
			MinDist = Dist;
			SL = Iter;
		}
	}

	if (MinDist < 5.f)
	{
		return SL;
	}

	return nullptr;
}

void UDSFiniteState::ExitLeftReplace()
{
	UMainDesignPageWidget* MainDesignPage = UDSUISubsystem::GetInstance()->GetWindowLayoutWidget()->GetMainDesignPageWidget();
	if (MainDesignPage == nullptr)
	{
		return;
	}

	bool bIsReplace = MainDesignPage->GetIsReplace();
	if (bIsReplace)
	{
		MainDesignPage->ShowReplace(false, nullptr);
	}
}

FDSVoidCoroutine UDSFiniteState::DeleteCommandCoroutine()
{
	UDSBaseModel* CurrentModel = UDSMVCSubsystem::GetInstance()->GetCurrentModel();
	if (CurrentModel != nullptr && (CurrentModel->GetModelType() == EDSModelType::E_Gizmo || CurrentModel->GetModelType() == EDSModelType::E_Scale2DForPath))
	{
		CurrentModel = CurrentModel->GetTargetModel();
	}
	
	if (DS_MODEL_VALID_FOR_USE(CurrentModel))
	{
		EDSModelType EditType = CurrentModel->GetModelType();

		bool IsMultiAndCT = EditType == EDSModelType::E_MultiSelect
			&& Cast<UDSMultiModel>(CurrentModel)->HasTypes(
				{
					EDSModelType::E_Generated_CounterTop,
					EDSModelType::E_Generated_SideCounterTop,
					EDSModelType::E_Generated_CounterTop_Line,
					EDSModelType::E_Generated_CounterTop_Point
				});

		if (ConsiderAsHouseType.Contains(EditType))
		{
			//非2D不能删区域和墙，并且不进undo和redo
			if (!ADesignStationController::Get()->Is2DScene() && (EditType == EDSModelType::E_House_Wall || EditType == EDSModelType::E_House_Area || EditType == EDSModelType::E_House_Area_Split_Line))
			{
				co_return;
			}

			if (EditType == EDSModelType::E_House_Area_Split_Line)
			{
				co_await UDSUISubsystem::GetInstance()->PresentModalDialog_Coroutine(TEXT("删除分割线后，将重新生成房间和地面分割线，确认删除分割线？"), TEXT(""), false, TEXT("警告"), ETextJustify::Left);
			}
			else if (EditType == EDSModelType::E_House_Area)
			{
				co_await UDSUISubsystem::GetInstance()->PresentModalDialog_Coroutine(TEXT("删除房间后可能导致部分墙体结构变化，确认删除？"), TEXT(""), false, TEXT("警告"), ETextJustify::Left);
			}

			FDSRevokePushData PushData(EDSPushDataType::E_Home, FDSModelExecuteType::ExecuteDelete, true);
			PushData.SetData(FDSRoomPushData({}, FDSModelExecuteType::ExecuteDelete, CurrentModel, nullptr));
			UDSMVCSubsystem::GetInstance()->OnExecuteFromFunctionBarOrPropertyWidgetUnion(
				CurrentModel, FDSModelExecuteType::ExecuteDelete, PushData, TEXT(""));
		}
		else if (EditType == EDSModelType::E_MultiSelect && (Cast<UDSMultiModel>(CurrentModel))->HasSelectedSameType(EDSModelType::E_Generated_LineOrnamentBase_Line))
		{
			co_return;
			//多选定制角线类忽略删除
		}
		else if (EditType == EDSModelType::E_Generated_CounterTop || EditType == EDSModelType::E_Generated_SideCounterTop ||
			EditType == EDSModelType::E_Generated_CounterTop_Line || EditType == EDSModelType::E_Generated_CounterTop_Point || IsMultiAndCT)
		{
			UDSToolLibrary::ModelDialog(
				CurrentModel, FDSModelExecuteType::ExecuteDelete, FString(TEXT("当前关联侧台面，是否继续删除")), FString(TEXT("删除台面后会删除所有关联侧台面")),
				FDSFunction::GetCTDeleteSureFunction(), FDSFunction::GetNormalCancelFunction());
		}
		else if (EditType == EDSModelType::E_Custom_Sink)
		{
			FString RevokeUUID = FGuid::NewGuid().ToString();
			UDSRevokeSubsystem::GetInstance()->SetGlobalCommandUUID(RevokeUUID);
			
			FDSRevokePushData PushData(EDSPushDataType::E_Sink, FDSModelExecuteType::ExecuteDelete);
			UDSRevokeLibrary::UpdatePushDataPropertyUnion(CurrentModel, FDSModelExecuteType::ExecuteDelete, {}, PushData);
			UDSMVCSubsystem::GetInstance()->OnExecuteFromFunctionBarOrPropertyWidgetUnion(CurrentModel, FDSModelExecuteType::ExecuteDelete, PushData, RevokeUUID);
			
			UDSRevokeSubsystem::GetInstance()->SetGlobalCommandUUID(TEXT(""));
		}
		else if (EditType == EDSModelType::E_Custom_Stove || EditType == EDSModelType::E_Custom_RangeHood)
		{
			FString RevokeUUID = FGuid::NewGuid().ToString();
			UDSRevokeSubsystem::GetInstance()->SetGlobalCommandUUID(RevokeUUID);
			
			FDSRevokePushData PushData(EDSPushDataType::E_StoveRangeHood, FDSModelExecuteType::ExecuteDelete);
			UDSRevokeLibrary::UpdatePushDataPropertyUnion(CurrentModel, FDSModelExecuteType::ExecuteDelete, {}, PushData);
			UDSMVCSubsystem::GetInstance()->OnExecuteFromFunctionBarOrPropertyWidgetUnion(CurrentModel, FDSModelExecuteType::ExecuteDelete, PushData, RevokeUUID);

			UDSRevokeSubsystem::GetInstance()->SetGlobalCommandUUID(TEXT(""));
		}
		else if (EditType == EDSModelType::E_Group || EditType == EDSModelType::E_MultiSelect)
		{
			UDSMultiModel* MultiModel = Cast<UDSMultiModel>(CurrentModel);
			TArray<EDSModelType> Types = MultiModel->GetSelectedModelTypes();
			if (Types.Num() == 1 && Types[0] == EDSModelType::E_Custom_LayoutDoor_Board)
			{
				//处理组合或多选中有护墙板的情况
				TArray<UDSBaseModel*> RootModels;
				TArray<UDSBaseModel*> AllModels = MultiModel->GetIncludeModel();
				for (auto Ite : AllModels)
				{
					RootModels.AddUnique(UDSToolLibrary::GetRootOwner(Ite));
				}

				FString UUID = FGuid::NewGuid().ToString();

				UDSRevokeSubsystem::GetInstance()->SetGlobalCommandUUID(UUID);
				
				for (auto& Ite : RootModels)
				{
					FDSRevokePushData PushData(EDSPushDataType::E_Custom, FDSModelExecuteType::ExecuteUpdateSelf, false);
					PushData.SetData(FDSCustomPushData(FDSModelExecuteType::ExecuteUpdateSelf, Ite, nullptr));
					UDSRevokeSubsystem::GetInstance()->InsertCommandDataUnion(
						Ite, FDSModelExecuteType::ExecuteUpdateSelf, PushData, UDSMVCSubsystem::GetInstance()->GetRevokeMark(), UUID);
				}
				for (auto Ite : AllModels)
				{
					UDSWallBoardLibrary::HideRealBoard(Ite);
				}

				UDSRevokeSubsystem::GetInstance()->SetGlobalCommandUUID(TEXT(""));
			}
			else
			{
				FString GUIDStr;
				if (UDSHandleFreeLibrary::SplitHandleFreeMulti(Cast<UDSMultiModel>(CurrentModel), &GUIDStr))
				{
					UDSUISubsystem::GetInstance()->SetToastState(EToastState::EWarning, TEXT("选中的合并拉手已进行拆分"));
				}

				if (GUIDStr.IsEmpty())
				{
					GUIDStr = FGuid::NewGuid().ToString();
				}

				UDSRevokeSubsystem::GetInstance()->SetGlobalCommandUUID(GUIDStr);

				FDSRevokePushData PushData(EDSPushDataType::E_Multi_Group, FDSModelExecuteType::ExecuteDelete);
				UDSRevokeLibrary::UpdatePushDataPropertyUnion(CurrentModel, FDSModelExecuteType::ExecuteDelete, {}, PushData);
				UDSMVCSubsystem::GetInstance()->OnExecuteFromFunctionBarOrPropertyWidgetUnion(CurrentModel, FDSModelExecuteType::ExecuteDelete, PushData, GUIDStr);

				UDSRevokeSubsystem::GetInstance()->SetGlobalCommandUUID(TEXT(""));
			}
		}
		else if (UDSToolLibrary::IsGeneratedLineType(EditType))
		{
			if (GetState() == EDSFSMState::FSM_Line)
			{
				CurrentModel->OnExecuteAction(FDSModelExecuteType::ExecuteDelete);
			}
			else
			{
				FString RevokeUUID = FGuid::NewGuid().ToString();
				UDSRevokeSubsystem::GetInstance()->SetGlobalCommandUUID(RevokeUUID);
				
				FDSRevokePushData PushData(EDSPushDataType::E_GeneratedLine, FDSModelExecuteType::ExecuteDelete);
				UDSRevokeLibrary::UpdatePushDataPropertyUnion(CurrentModel, FDSModelExecuteType::ExecuteDelete, {}, PushData);
				UDSMVCSubsystem::GetInstance()->OnExecuteFromFunctionBarOrPropertyWidgetUnion(CurrentModel, FDSModelExecuteType::ExecuteDelete, PushData, RevokeUUID);

				UDSRevokeSubsystem::GetInstance()->SetGlobalCommandUUID(TEXT(""));
			}
		}
		else if (EditType == EDSModelType::E_Generated_LineEntity)
		{
			CurrentModel->OnExecuteAction(FDSModelExecuteType::ExecuteDelete);
		}
		else if (EditType == EDSModelType::E_Generated_LineOrnamentBase_Line || EditType == EDSModelType::E_Generated_LineOrnamentBase_Point)
		{
			CurrentModel->OnExecuteAction(FDSModelExecuteType::ExecuteDelete);
		}
		else if (EditType == EDSModelType::E_Furniture_HouseFurniture)
		{
			FString RevokeUUID = FGuid::NewGuid().ToString();
			UDSRevokeSubsystem::GetInstance()->SetGlobalCommandUUID(RevokeUUID);
			
			FDSRevokePushData PushData(EDSPushDataType::E_SoftFurniture, FDSModelExecuteType::ExecuteDelete);
			UDSRevokeLibrary::UpdatePushDataPropertyUnion(CurrentModel, FDSModelExecuteType::ExecuteDelete, {}, PushData);
			UDSMVCSubsystem::GetInstance()->OnExecuteFromFunctionBarOrPropertyWidgetUnion(CurrentModel, FDSModelExecuteType::ExecuteDelete, PushData, RevokeUUID);

			UDSRevokeSubsystem::GetInstance()->SetGlobalCommandUUID(TEXT(""));
		}
		else if (EditType == EDSModelType::E_Furniture_MoldingCeiling)
		{
			FString RevokeUUID = FGuid::NewGuid().ToString();
			UDSRevokeSubsystem::GetInstance()->SetGlobalCommandUUID(RevokeUUID);
			
			FDSRevokePushData PushData(EDSPushDataType::E_ModelCeiling, FDSModelExecuteType::ExecuteDelete);
			UDSRevokeLibrary::UpdatePushDataPropertyUnion(CurrentModel, FDSModelExecuteType::ExecuteDelete, {}, PushData);
			UDSMVCSubsystem::GetInstance()->OnExecuteFromFunctionBarOrPropertyWidgetUnion(CurrentModel, FDSModelExecuteType::ExecuteDelete, PushData, RevokeUUID);

			UDSRevokeSubsystem::GetInstance()->SetGlobalCommandUUID(TEXT(""));
		}
		else if (UDSToolLibrary::IsCustomCupboardType(EditType))
		{
			auto OwnerModel = CurrentModel->GetOwnerModel();
			if (OwnerModel && OwnerModel->GetModelType() == EDSModelType::E_Custom_LayoutDoor)
			{
				co_return;
			}

			if (EditType == EDSModelType::E_Custom_CabinetBoard)
			{
				co_return;
			}

			if (EditType == EDSModelType::E_Custom_Board && !UDSCupboardLibrary::IsFunctionalCupboardModel(CurrentModel))
			{
				co_return; 
			}
			if (EditType == EDSModelType::E_Custom_LayoutDoor_Board && 
				CurrentModel->GetTopLevelOwnerModel()->GetModelType() == EDSModelType::E_Custom_WallBoardCabinet)   //护墙板
			{
				co_return;
			}
			if (UDSCupboardModel* DoorModel = Cast<UDSCupboardModel>(CurrentModel))//部件模式门不能删除
			{
				if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), DoorModel->GetModelInfoRef().ComponentTreeData->ModelType) && IsPartSelectState())
				{
					co_return;
				}
			}
			
			FString GUIDStr = FGuid::NewGuid().ToString();
			UDSRevokeSubsystem::GetInstance()->SetGlobalCommandUUID(GUIDStr);
			
			//处理五金免拉合并
			if (UDSToolLibrary::IsCustomCabinetType(EditType) && UDSModelDependencySubsystem::GetInstance()->IsHaveModelHandleFree(CurrentModel->GetUUID()))
			{
				UDSUISubsystem::GetInstance()->SetToastState(EToastState::EWarning, TEXT("选中的合并拉手已进行拆分"));
				UDSHandleFreeLibrary::SplitHandleFree(CurrentModel, true, &GUIDStr);
			}
			if (Cast<UDSCupboardModel>(CurrentModel) 
				&& UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), Cast<UDSCupboardModel>(CurrentModel)->GetModelInfoRef().ComponentTreeData->ModelType)
				|| UDSCupboardLibrary::IsFunctionalCupboardModel(CurrentModel))
			{
				if (UDSBaseModel* TopModel = CurrentModel->GetTopLevelOwnerModel())
				{
					FDSRevokePushData PushData(EDSPushDataType::E_Custom, FDSModelExecuteType::ExecuteUpdateSelf, false);
					PushData.SetData(FDSCustomPushData(FDSModelExecuteType::ExecuteUpdateSelf, TopModel, nullptr));
					UDSRevokeSubsystem::GetInstance()->InsertCommandDataUnion(
						TopModel,
						FDSModelExecuteType::ExecuteUpdateSelf,
						PushData,
						UDSMVCSubsystem::GetInstance()->GetRevokeMark(),
						GUIDStr
					);

					//删除门需要解除功能件内缩
					UDSCupBoardDoorLibrary::UpdateFunctionalBoardsByDoor({ Cast<UDSCupboardModel>(CurrentModel)->GetModelInfoRef().ComponentTreeData }, false);
					UDSMVCSubsystem::GetInstance()->ExecuteAfterInsertData(CurrentModel, FDSModelExecuteType::ExecuteDelete, FDSRevokePushData());
				}
			}
			else
			{
				FDSRevokePushData PushData(EDSPushDataType::E_Custom, FDSModelExecuteType::ExecuteDelete, false);
				PushData.SetData(FDSCustomPushData(FDSModelExecuteType::ExecuteDelete, CurrentModel, nullptr));
				UDSMVCSubsystem::GetInstance()->OnExecuteFromFunctionBarOrPropertyWidgetUnion(
					CurrentModel, FDSModelExecuteType::ExecuteDelete, PushData, GUIDStr);
			}

			UDSRevokeSubsystem::GetInstance()->SetGlobalCommandUUID(TEXT(""));

		}
		else
		{
			FString RevokeUUID = FGuid::NewGuid().ToString();
			UDSRevokeSubsystem::GetInstance()->SetGlobalCommandUUID(RevokeUUID);
			
			FDSRevokePushData PushData(EDSPushDataType::E_Single, FDSModelExecuteType::ExecuteDelete, false);
			PushData.SetData(FDSBasicPushData(FDSModelExecuteType::ExecuteDelete, CurrentModel, nullptr));
			UDSMVCSubsystem::GetInstance()->OnExecuteFromFunctionBarOrPropertyWidgetUnion(CurrentModel, FDSModelExecuteType::ExecuteDelete, PushData, RevokeUUID);

			UDSRevokeSubsystem::GetInstance()->SetGlobalCommandUUID(TEXT(""));
		}
	}
	
	UDSMVCSubsystem::GetInstance()->SetCurrentModel(nullptr);
	UpdatePropertyWidgetAfterDelete();
}

void UDSFiniteState::OnSelectTypeChange(EDSSelectType InOldSelectType, EDSSelectType InNewSelectType)
{
}

void UDSFiniteState::ExitState()
{
	UDSUISubsystem::GetInstance()->ExitStateResetUI();
	UDSUISubsystem::GetInstance()->GetWindowLayoutWidget()->GetFreeDrawingCanvas()->FollowingType(EFollowingType::FT_None, FVector3f::ZeroVector);

	ClearStateRunningThing();

	//进入和离开都要刷新工具条
	if (UDSUISubsystem::GetInstance()->GetFunctionBarBoxWidget() && UDSUISubsystem::GetInstance()->GetFunctionBarBoxWidget()->IsVisible())
	{
		UDSUISubsystem::GetInstance()->GetFunctionBarBoxWidget()->SwitchButtons(UDSMVCSubsystem::GetInstance()->GetCurrentModel(), false);
	}
}

void UDSFiniteState::SwitchStateSelectType(const EDSSelectType& InType)
{
	if (SelectType == InType)
	{
		return;
	}
	EDSSelectType OldSelectType = SelectType;
	SelectType = InType;
	OnSelectTypeChange(OldSelectType, SelectType);
}

void UDSFiniteState::ClearStateRunningThing()
{
	// hover data
	if (auto Action = ActionFactory->GetAction(nullptr, FPointerEvent()))
	{
		Action->LeaveActionBySwitchState();
	}
}

bool UDSFiniteState::IsModelDragging(UDSBaseModel* InModel) const
{
	if (FSlateApplication::Get().IsDragDropping())
	{
		TSharedPtr<FDSModelDragDropOperation> Operation = StaticCastSharedPtr<FDSModelDragDropOperation>(FSlateApplication::Get().GetDragDroppingContent());
		return Operation && Operation->GetActionModel() == InModel;
	}
	
	return false;
}

void UDSFiniteState::HiddenLeftWidget()
{
	UWindowLayoutWidget* WindowLayout = UDSUISubsystem::GetInstance()->GetWindowLayoutWidget();
	if (WindowLayout == nullptr) return;

	UMainDesignPageWidget* MainDesignWidget = WindowLayout->GetMainDesignPageWidget();
	if (MainDesignWidget == nullptr) return;

	MainDesignWidget->PresentLeftContentWidget(nullptr);
}

void UDSFiniteState::ShowLeftWidget()
{
	UWindowLayoutWidget* WindowLayout = UDSUISubsystem::GetInstance()->GetWindowLayoutWidget();
	if (WindowLayout == nullptr) return;

	UMainDesignPageWidget* MainDesignWidget = WindowLayout->GetMainDesignPageWidget();
	if (MainDesignWidget == nullptr) return;

	MainDesignWidget->PopLeftContentWidget();
}
