﻿// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Serialization/JsonWriter.h"
#include "Policies/CondensedJsonPrintPolicy.h"
#include "ParameterEnumTableData.generated.h"

class FJsonObject;

USTRUCT(BlueprintType)
struct LOCALCACHE_API FParameterEnumTableData
{
	GENERATED_USTRUCT_BODY()

public:

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		FString id = TEXT("");//主键

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		FString value = TEXT("");//值

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		FString expression = TEXT("");//表达式

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		FString name_for_display = TEXT("");//显示名称

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		FString image_for_display = TEXT("");//显示的图片路径

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		FString visibility = TEXT("");//可见性

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		FString visibility_exp = TEXT("");//可见性表达式

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		FString priority = TEXT("");//优先级

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		FString main_id = TEXT("");//所属的变量主键

public:

	virtual ~FParameterEnumTableData() { }

	friend FArchive& operator<<(FArchive& Ar, struct FParameterEnumTableData& ParameterTableDataToSave)
	{
		Ar << ParameterTableDataToSave.id;
		Ar << ParameterTableDataToSave.value;
		Ar << ParameterTableDataToSave.name_for_display;
		Ar << ParameterTableDataToSave.image_for_display;
		Ar << ParameterTableDataToSave.visibility;
		Ar << ParameterTableDataToSave.priority;
		Ar << ParameterTableDataToSave.visibility_exp;
		Ar << ParameterTableDataToSave.expression;
		return Ar;
	}

	bool operator!=(const FParameterEnumTableData& InData) const
	{
		bool ValueNoEqual = FParameterEnumTableData::StringValueNoEqual(value, InData.value);
		bool PriorityValueNoEqual = FParameterEnumTableData::StringValueNoEqual(priority, InData.priority);

		return id != InData.id ||
			name_for_display != InData.name_for_display ||
			ValueNoEqual/*value != InData.value*/ ||
			PriorityValueNoEqual/*priority != InData.priority*/ ||
			visibility_exp != InData.visibility_exp ||
			expression != InData.expression;
	}

	bool Equal_Precise(const FParameterEnumTableData& InData) const;

	void operator=(const FParameterEnumTableData& InData)
	{
		id = InData.id;
		name_for_display = InData.name_for_display;
		value = InData.value;
		priority = InData.priority;
		visibility_exp = InData.visibility_exp;
		expression = InData.expression;
		image_for_display = InData.image_for_display;
	}

	void CopyData(const FParameterEnumTableData& InData)
	{
		id = InData.id;
		value = InData.value;
		name_for_display = InData.name_for_display;
		visibility = InData.visibility;
		priority = InData.priority;
		visibility_exp = InData.visibility_exp;
		expression = InData.expression;
		image_for_display = InData.image_for_display;
	}

	bool operator == (const FParameterEnumTableData& InData) const
	{
		if (/*name_for_display == InData.name_for_display && */id == InData.id)
		{
			return true;
		}
		return false;
	}

	bool CompareData(const FParameterEnumTableData& InData)
	{
		return value == InData.value && name_for_display == InData.name_for_display &&
			visibility == InData.visibility && priority == InData.priority &&
			visibility_exp == InData.visibility_exp && expression == InData.expression;
	}

	bool UseParameterName(const FString& InName) const;

	bool IsVisibility() const
	{
		double VisiValue = FCString::Atod(*visibility);
		return !FMath::IsNearlyZero(VisiValue, 0.01);
	}

	static const FParameterEnumTableData& EmptyEnumData();

	static bool StringValueNoEqual(const FString& V1, const FString& V2)
	{
		float Value1 = FCString::Atof(*V1);
		float Value2 = FCString::Atof(*V2);
		return !FMath::IsNearlyEqual(Value1, Value2, 0.01f);
	}

	//序列化和反序列化
	void Serialization(TSharedRef<TJsonWriter<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>> JsonWriter);
	void Deserialization(const TSharedPtr<FJsonObject>& InJsonData);
};

