#include "DSFileSubsystem.h"
#include "BaseNetworkFileTask.h"
#include "DSPipeSubsystem.h"
#include "EasyNetworkFileSubsystem.h"
#include "IHttpResponse.h"
#include "ImageUtils.h"
#include "NetworkFileUploadTask.h"
#include "TextureResource.h"
#include "Components/WidgetComponent.h"
#include "Misc/FileHelper.h"
#include "Slate/DeferredCleanupSlateBrush.h"
#include "Subsystems/DSNetworkSubsystem.h"
#include "SubSystems/Camera/DSCameraSubsystem.h"
#include "SubSystems/Library/DesignStationFunctionLibrary.h"
#include "Subsystems/ModelDependencySubsystem/DSModelDependencySubsystem.h"
#include "SubSystems/MVC/DSMVCSubsystem.h"
#include "SubSystems/MVC/Model/CounterTop/CounterTopModel.h"
#include "SubSystems/MVC/Model/Custom/DownloadTaskPayload/CustomRelativeResourcePayload.h"
#include "SubSystems/MVC/Model/Custom/DownloadTaskPayload/SoftFurnitureResourcePayload.h"
#include "Subsystems/Resource/DSResourceSubsystem.h"
#include "SubSystems/UI/DSUISubsystem.h"
#include "SubSystems/UI/Widget/Facsimile/FacsimileActor.h"
#include "SubSystems/UI/Widget/Facsimile/SDSImportImage.h"
#include "SubSystems/UI/Widget/Toast/ToastWidget.h"

DEFINE_LOG_CATEGORY(DSFileSubsystemLog);

UDSFileSubsystem* UDSFileSubsystem::Instance = nullptr;

UDSFileSubsystem::UDSFileSubsystem()
	: CurPlanId(0),
	  CurrentFacsimileImageTexture(nullptr),
	  NeedsDownloadResourceCount(0),
	  DownloadedResourceCount(0),
	  bUploadThumbnailTaskComplete(false),
	  bUploadFacsimileImageTaskComplete(false),
	  bUploadPlanFileTaskComplete(false),
	  bDownloadPlanFileTaskComplete(false),
	  bDownloadFacsimileImageTaskComplete(false) {}

UDSFileSubsystem* UDSFileSubsystem::GetInstance()
{
	return Instance;
}

void UDSFileSubsystem::Initialize(FSubsystemCollectionBase& Collection)
{
	CurProjectInfo = FDSProjectInfo();
	Super::Initialize(Collection);

	Instance = this;
}

void UDSFileSubsystem::Deinitialize()
{
	Super::Deinitialize();

	Instance = nullptr;
}

void UDSFileSubsystem::OnWorldBeginPlay(UWorld& InWorld)
{
	FString PlanId;
	if (FParse::Value(FCommandLine::Get(), TEXT("PlanId="), PlanId))
	{
		QueryPlanInfoByPlanId(PlanId);
	}
}

FString UDSFileSubsystem::LoadJsonFileToStr(const FString& AbsoluteFilePath)
{
	if (!AbsoluteFilePath.IsEmpty() && FPaths::FileExists(AbsoluteFilePath))
	{
		FString JsonStr;
		if (!FFileHelper::LoadFileToString(JsonStr, *AbsoluteFilePath))
		{
			UE_LOG(DSFileSubsystemLog, Error, TEXT("Load Json [%s] Error"), *AbsoluteFilePath);
		}
		return JsonStr;
	}

	checkf(false, TEXT("Json File Path Or File Is NULL"));
	return TEXT("");
}

void UDSFileSubsystem::PreSavedProject()
{
	Reset();

	//截取头图，并取消所有物体的选中状态
	//先居中视口
	UDSCameraSubsystem::GetInstance()->AdjustViewportToCenter();
	//再截图,并绑定上传
	FString ThumbnailUploadFileName = FString::Printf(TEXT("%d_%s.jpg"), CurPlanId, *GetTimeStampString());
	ThumbnailUrl = UDSNetworkSubsystem::GetInstance()->GetUploadDomain() / UDSNetworkSubsystem::GetInstance()->GetUploadBucketName() / TEXT("DesignStationPlanFile/ProjectThumbnails") / ThumbnailUploadFileName;

	GScreenshotResolutionX = 1280;
	GScreenshotResolutionY = 720;
	GIsHighResScreenshot = true;
	ScreenshotHandle = GetWorld()->GetGameViewport()->OnScreenshotCaptured().AddLambda([this](int32 Width, int32 Height, const TArray<FColor>& Colors)
	{
		TArray<uint8> CompressedData;
		FImageUtils::ThumbnailCompressImageArray(1280, 720, Colors, CompressedData);

		UBaseNetworkFileTask* UploadThumbnailTask = UEasyNetworkFileSubsystem::GetInstance()->CreateTask(ENetworkFileOperation::ENFO_Upload);
		Cast<UNetworkFileUploadTask>(UploadThumbnailTask)->SetUploadFileContent(CompressedData);
		UploadThumbnailTask->SetUrl(this->ThumbnailUrl);
		UploadThumbnailTask->OnTaskCompleteEvent().AddDynamic(this, &ThisClass::OnUploadThumbnailTaskComplete);
		UploadThumbnailTask->StartProcess();

		GetWorld()->GetGameViewport()->OnScreenshotCaptured().Remove(ScreenshotHandle);
	});

	if (CurrentFacsimileImageTexture)
	{
		if (!ExportTextureToImage(CurrentFacsimileImageTexture))
		{
			SaveResult(false);
			UDSUISubsystem::GetInstance()->SetToastState(EToastState::EWarning, TEXT("临摹图保存失败"), TEXT(""), true);
			return;
		}
		FacsimileImageUrl = UDSNetworkSubsystem::GetInstance()->GetUploadDomain() / UDSNetworkSubsystem::GetInstance()->GetUploadBucketName() / TEXT("DesignStationPlanFile/FacsimileImages") / FacsimileImageName;
		UBaseNetworkFileTask* UploadFacsimileImageTask = UEasyNetworkFileSubsystem::GetInstance()->CreateTask(ENetworkFileOperation::ENFO_Upload);
		UploadFacsimileImageTask->SetFilePath(FacsimileImagePath);
		UploadFacsimileImageTask->SetUrl(FacsimileImageUrl);
		UploadFacsimileImageTask->OnTaskCompleteEvent().AddDynamic(this, &ThisClass::OnUploadFacsimileImageTaskComplete);
		UploadFacsimileImageTask->StartProcess();
	}
	else
	{
		//没有底图，状态设置为成功
		bUploadFacsimileImageTaskComplete = true;
	}

	/*其他操作全部完成后，开始写数据，写数据过程中不操作任何其他计算函数，单纯写数据，其他操作在之前就准备好*/
	/******************************************************************************************/
	TArray<uint8> AllData;
	FString JStr;
	TSharedRef<TJsonWriter<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>> JsonWriter = TJsonWriterFactory<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>::Create(&JStr);

	JsonWriter->WriteObjectStart();
	{
		//保存临摹图数据
		SaveFacsimile(JsonWriter);
		//转化model数据为Json
		UDSMVCSubsystem::GetInstance()->SerializeToJsonFromAllModels(JsonWriter);
	}
	JsonWriter->WriteObjectEnd();
	JsonWriter->Close();
	/******************************************************************************************/
	FTCHARToUTF8 Converter(*JStr);
	AllData.SetNum(Converter.Length());
	FMemory::Memcpy(AllData.GetData(), Converter.Get(), Converter.Length());

	CurPlanFileMD5 = FMD5::HashBytes(AllData.GetData(), AllData.Num());
	CurPlanFilePath = FPaths::ProjectSavedDir() + FString::Printf(TEXT("ProjectFiles/%d_%s.json"), CurPlanId, *CurPlanFileMD5);
	FString UploadPlanFileName = FString::Printf(TEXT("%d_%s.json"), CurPlanId, *CurPlanFileMD5);
	CurPlanFileUrl = UDSNetworkSubsystem::GetInstance()->GetUploadDomain() / UDSNetworkSubsystem::GetInstance()->GetUploadBucketName() / TEXT("DesignStationPlanFile/ProjectFiles") / UploadPlanFileName;

	if (FFileHelper::SaveArrayToFile(AllData, *CurPlanFilePath))
	{
		UNetworkFileUploadTask* UploadPlanFileTask = Cast<UNetworkFileUploadTask>(UEasyNetworkFileSubsystem::GetInstance()->CreateTask(ENetworkFileOperation::ENFO_Upload));
		UploadPlanFileTask->SetUploadFileContent(AllData);
		UploadPlanFileTask->SetUrl(CurPlanFileUrl);
		UploadPlanFileTask->OnTaskCompleteEvent().AddDynamic(this, &ThisClass::OnUploadPlanFileTaskComplete);

		UploadPlanFileTask->StartProcess();
	}
	else
	{
		SaveResult(false);
		UDSUISubsystem::GetInstance()->SetToastState(EToastState::EWarning, TEXT("方案文件保存失败"), TEXT(""), true);
	}
}

void UDSFileSubsystem::SaveProject()
{
	if (CurProjectInfo.Id == 0)
	{
		UDSUISubsystem::GetInstance()->GenerateCreateProjectDialog();
	}
	else
	{
		PreSavedProject();
	}
}

void UDSFileSubsystem::OnUploadThumbnailTaskComplete(const FString& TaskId, bool bSucceed)
{
	if (bSucceed)
	{
		bUploadThumbnailTaskComplete = true;
	}
	else
	{
		SaveResult(false);
		UDSUISubsystem::GetInstance()->SetToastState(EToastState::EWarning, TEXT("方案头图上传失败"), TEXT(""), true);
	}
	CheckToUp();
}

void UDSFileSubsystem::OnUploadFacsimileImageTaskComplete(const FString& TaskId, bool bSucceed)
{
	if (bSucceed)
	{
		bUploadFacsimileImageTaskComplete = true;
	}
	else
	{
		SaveResult(false);
		UDSUISubsystem::GetInstance()->SetToastState(EToastState::EWarning, TEXT("方案临摹图上传失败"), TEXT(""), true);
	}
	CheckToUp();
}

void UDSFileSubsystem::OnUploadPlanFileTaskComplete(const FString& TaskId, bool bSucceed)
{
	if (bSucceed)
	{
		bUploadPlanFileTaskComplete = true;
	}
	else
	{
		SaveResult(false);
		UDSUISubsystem::GetInstance()->SetToastState(EToastState::EWarning, TEXT("方案文件上传失败"), TEXT(""), true);
	}
	CheckToUp();
}

void UDSFileSubsystem::CheckToUp()
{
	if (bUploadThumbnailTaskComplete && bUploadFacsimileImageTaskComplete && bUploadPlanFileTaskComplete)
	{
		ReqSavePlan();
	}
}

void UDSFileSubsystem::ReqSavePlan()
{
	FString SplitPart = UDSNetworkSubsystem::GetInstance()->GetUploadBucketName() + TEXT("/");
	if (!CurPlanFileUrl.Split(SplitPart, nullptr, &CurProjectInfo.BkPlanFile.FileUrl))
	{
		SaveResult(false);
		UDSUISubsystem::GetInstance()->SetToastState(EToastState::EMistake, TEXT("方案编辑失败"), TEXT(""), true);
		UDSUISubsystem::GetInstance()->RemoveModalDialog();
		return;
	}

	if (!ThumbnailUrl.Split(SplitPart, nullptr, &CurProjectInfo.Thumbnail))
	{
		SaveResult(false);
		UDSUISubsystem::GetInstance()->SetToastState(EToastState::EMistake, TEXT("方案编辑失败"), TEXT(""), true);
		UDSUISubsystem::GetInstance()->RemoveModalDialog();
		return;
	}
	
	CurProjectInfo.BkPlanFile.Md5 = CurPlanFileMD5;

	int32 FilePos = CurProjectInfo.PlanFile.IndexOfByPredicate([](const FDSPlanFile& InFile) { return InFile.FileType == EProjectFileType::EFacsimile; });
	if (FilePos != INDEX_NONE)
	{
		if (FacsimileImageUrl.IsEmpty())
		{
			CurProjectInfo.PlanFile[FilePos].FileUrl = FacsimileImageUrl;
		}
		else
		{
			if (!FacsimileImageUrl.Split(SplitPart, nullptr, &CurProjectInfo.PlanFile[FilePos].FileUrl))
			{
				SaveResult(false);
				UDSUISubsystem::GetInstance()->SetToastState(EToastState::EMistake, TEXT("方案编辑失败"), TEXT(""), true);
				UDSUISubsystem::GetInstance()->RemoveModalDialog();
				return;
			}
		}
		
		CurProjectInfo.PlanFile[FilePos].FileName = FacsimileImageName;
		CurProjectInfo.PlanFile[FilePos].Md5 = FacsimileImageMD5;
		CurProjectInfo.PlanFile[FilePos].FilePath = TEXT("FacsimileImage") / FacsimileImageName;
	}
	else
	{
		FDSPlanFile ImagePlanFile;
		ImagePlanFile.FileType = EProjectFileType::EFacsimile;
		ImagePlanFile.FileUrl = FacsimileImageUrl;
		ImagePlanFile.FileName = FacsimileImageName;
		ImagePlanFile.Md5 = FacsimileImageMD5;
		ImagePlanFile.FilePath = TEXT("FacsimileImage") / FacsimileImageName;
		CurProjectInfo.PlanFile.Add(ImagePlanFile);
	}

	FString QueryParamsStr;
	TSharedRef<TJsonWriter<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>> JsonWriter = TJsonWriterFactory<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>::Create(&QueryParamsStr);
	CurProjectInfo.SerializeToJson(JsonWriter);
	JsonWriter->Close();

	FHttpRequestPtr HttpRequestHandle = UDSNetworkSubsystem::GetInstance()->CreatePostRequest(TEXT("bk-design/api/designPlan/editOrAddPlanByBk"), QueryParamsStr);
	HttpRequestHandle->OnProcessRequestComplete().BindUObject(this, &ThisClass::ResSavePlan);
	HttpRequestHandle->ProcessRequest();
}

void UDSFileSubsystem::ResSavePlan(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bConnectedSuccessfully)
{
	bool bSuccess = true;
	if (!bConnectedSuccessfully || !EHttpResponseCodes::IsOk(Response->GetResponseCode()))
	{
		bSuccess = false;
	}

	TSharedPtr<FJsonObject> ResponseObj;
	TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(Response->GetContentAsString());

	if (!FJsonSerializer::Deserialize(Reader, ResponseObj))
	{
		bSuccess = false;
	}

	if (!ResponseObj->GetBoolField(TEXT("success")))
	{
		bSuccess = false;
	}

	if (!ResponseObj->HasTypedField<EJson::Object>(TEXT("resp")))
	{
		bSuccess = false;
	}

	TSharedPtr<FJsonObject> Resp = ResponseObj->GetObjectField(TEXT("resp"));

	if (bSuccess)
	{
		FDSProjectInfo ProjectInfo;
		ProjectInfo.Deserialization(Resp);

		bool bIsNewProject = CurPlanId == 0;
		CurProjectInfo = ProjectInfo;
		CurPlanId = CurProjectInfo.Id;

		UDSUISubsystem::GetInstance()->SetToastState(EToastState::ESuccess, TEXT("方案编辑成功"), TEXT(""), true);

#if WITH_EDITOR
		UE_LOG(LogTemp, Log, TEXT("[Save Project] Project id : %d, Plane name : %s"), CurPlanId, *CurProjectInfo.PlanName);
#endif

		if (bIsNewProject)
		{
			OnNewProjectCompleted.Broadcast(CurPlanId);
			UDSPipeSubsystem::GetInstance()->RefreshFrameTitle(CurProjectInfo.PlanName);
		}
		SaveResult(true);
	}
	else
	{
		SaveResult(false);
		UDSUISubsystem::GetInstance()->SetToastState(EToastState::EMistake, TEXT("方案编辑失败"), TEXT(""), true);
	}
	UDSUISubsystem::GetInstance()->RemoveModalDialog();
}

void UDSFileSubsystem::SetProjectInfoToSave(const FProjectInfoData& ProjectData) {}

void UDSFileSubsystem::SetProjectInfoToSave(const int32& InUserId, const int32& InCustomId, const FString& InProjectName) {}

void UDSFileSubsystem::OpenProject(const FString& FilePath)
{
	FString OutStr;
	if (LoadFileFromPath(FilePath, OutStr))
	{
		if (FJsonSerializer::Deserialize(TJsonReaderFactory<TCHAR>::Create(OutStr), CurrentProjectJsonObject))
		{
			QueryResourcesInfoByFolderIds();
		}
	}
}

void UDSFileSubsystem::QueryResourcesInfoByFolderIds()
{
	//打开文档以后可能会有很多要查询接口的地方，一次性在这里用NetworkSubsystem一起处理
	UDSNetworkSubsystem* NetworkSubsystem = UDSNetworkSubsystem::GetInstance();
	if (NetworkSubsystem == nullptr)
	{
		return;
	}

	TMap<FString, EDSResourceType> QueryMap;
	//保存的时候，最好定制也能全集展开，平铺成一个ID数组，一次性下载
	const TArray<TSharedPtr<FJsonValue>>& FilesArray = CurrentProjectJsonObject->GetArrayField(TEXT("FileList"));
	for (const auto& It : FilesArray)
	{
		TSharedPtr<FJsonObject> JsonData = It->AsObject();
		EDSResourceType type = static_cast<EDSResourceType>(JsonData->GetIntegerField(TEXT("type")));
		const TArray<TSharedPtr<FJsonValue>>& IdsArray = JsonData->GetArrayField(TEXT("resourceIds"));
		for (const auto& Id : IdsArray)
		{
			if (type != EDSResourceType::Custom) //排除掉只有ID没有FoldID的
			{
				if (Id->AsString().Contains(TEXT("Id_")))
				{
					continue;
				}
			}
			QueryMap.Add(Id->AsString(), type);
		}
	}

	if (QueryMap.IsEmpty())
	{
		QueryResourcesInfoByIds();
	}
	else
	{
		FOnQueryResourceListByFolderIdsCompletedDelegate CallBack;
		CallBack.BindDynamic(this, &ThisClass::OnQueryResourcesInfoComplete);
		NetworkSubsystem->SendQueryResourceListByFolderIdsRequest(QueryMap, CallBack);
	}
}

void UDSFileSubsystem::DownloadResourceByItems(const TArray<FDSResourceInfo>& ItemList)
{
	UWorld* World = GetWorld();
	if (World == nullptr)
	{
		return;
	}

	UEasyNetworkFileSubsystem* EasyNetworkFileSubsystem = World->GetSubsystem<UEasyNetworkFileSubsystem>();
	if (EasyNetworkFileSubsystem == nullptr)
	{
		return;
	}

	NeedsDownloadResourceCount = ItemList.Num();
	DownloadedResourceCount = 0;

	for (const FDSResourceInfo& ItemInfo : ItemList)
	{
		if (ItemInfo.Type == EDSResourceType::Custom)
		{
			UDSResourceSubsystem::GetInstance()->AddResourceCache(ItemInfo);
			continue;
		}

		FDSResourceFile FileInfo = ItemInfo.GetResourceFile(EDSResourceQuality::Low);
		if (FileInfo.FilePath.IsEmpty())
		{
			continue;
		}

		UBaseNetworkFileTask* Task = EasyNetworkFileSubsystem->CreateTask(ENetworkFileOperation::ENFO_Download);
		Task->SetUrl(FileInfo.FilePath);
		Task->SetChecksum(FileInfo.MD5);
		Task->SetFilePath(FPaths::Combine(FPaths::ProjectSavedDir(), TEXT("Resources"), FileInfo.ComputeLocalFileName()));

		USoftFurnitureResourcePayload* Payload = NewObject<USoftFurnitureResourcePayload>(Task);
		Payload->ResourceInfo = ItemInfo;
		Task->SetPayload(Payload);

		Task->OnTaskCompleteEvent().AddDynamic(this, &ThisClass::OnDownloadResourcesTaskComplete);
		Task->StartProcess();
	}
}

void UDSFileSubsystem::OnQueryResourcesInfoComplete(const TArray<FDSResourceInfo>& ItemList)
{
	UWorld* World = GetWorld();
	if (World == nullptr)
	{
		return;
	}

	UEasyNetworkFileSubsystem* EasyNetworkFileSubsystem = World->GetSubsystem<UEasyNetworkFileSubsystem>();
	if (EasyNetworkFileSubsystem == nullptr)
	{
		return;
	}

	TArray<FDSResourceInfo> NonCustomItemList = ItemList.FilterByPredicate([](const FDSResourceInfo& InItem) { return InItem.Type != EDSResourceType::Custom; });

	if (ItemList.IsEmpty())
	{
		//既然查了接口，肯定是入参查询了，返回就不可能为空
		UDSUISubsystem::GetInstance()->SetToastState(EToastState::EWarning, TEXT("方案信息查询失败"), TEXT(""), true);
	}

	FolderIdSItemList = ItemList;

	QueryResourcesInfoByIds();
}

void UDSFileSubsystem::QueryResourcesInfoByIds()
{
	UDSNetworkSubsystem* NetworkSubsystem = UDSNetworkSubsystem::GetInstance();
	if (NetworkSubsystem == nullptr)
	{
		return;
	}

	TMap<FString, EDSResourceType> QueryMap;

	const TArray<TSharedPtr<FJsonValue>>& FilesArray = CurrentProjectJsonObject->GetArrayField(TEXT("FileList"));
	for (const auto& It : FilesArray)
	{
		TSharedPtr<FJsonObject> JsonData = It->AsObject();
		EDSResourceType type = static_cast<EDSResourceType>(JsonData->GetIntegerField(TEXT("type")));

		if (type == EDSResourceType::Custom)
		{
			continue;
		}

		const TArray<TSharedPtr<FJsonValue>>& IdsArray = JsonData->GetArrayField(TEXT("resourceIds"));
		for (const auto& Id : IdsArray)
		{
			if (!Id->AsString().Contains(TEXT("Id_")))
			{
				continue;
			}
			FString RightString;
			Id->AsString().Split(TEXT("_"), nullptr, &RightString);
			QueryMap.Add(RightString, type);
		}
	}

	if (QueryMap.IsEmpty()) //如果方案没有任何模型资源，直接调过接口查询，加载方案
	{
		LoadProject(CurrentProjectJsonObject);
		if (FolderIdSItemList.IsEmpty())
		{
			UDSCameraSubsystem::GetInstance()->StartCaptureActor();
		}
		else
		{
			DownloadResourceByItems(FolderIdSItemList);
		}
	}
	else
	{
		FOnQueryResourceListByIdsCompletedDelegate CallBack;
		CallBack.BindDynamic(this, &ThisClass::OnQueryResourcesInfoByIdsComplete);
		NetworkSubsystem->SendQueryResourceListByIdsRequest(QueryMap, CallBack);
	}
}

void UDSFileSubsystem::OnQueryResourcesInfoByIdsComplete(const TArray<FDSResourceInfo>& ItemList)
{
	if (ItemList.IsEmpty())
	{
		//既然查了接口，肯定是入参查询了，返回就不可能为空
		UDSUISubsystem::GetInstance()->SetToastState(EToastState::EWarning, TEXT("方案信息查询失败"), TEXT(""), true);
	}

	TArray<FDSResourceInfo> NonCustomItemList = ItemList.FilterByPredicate([](const FDSResourceInfo& InItem) { return InItem.Type != EDSResourceType::Custom; });

	//合在一起下载;
	NonCustomItemList.Append(FolderIdSItemList);

	DownloadResourceByItems(NonCustomItemList);

	//直接进方案，异步下载
	LoadProject(CurrentProjectJsonObject);
}

void UDSFileSubsystem::OnDownloadResourcesTaskComplete(const FString& TaskId, bool bSucceed)
{
	if (!bSucceed)
	{
		return;
	}

	DownloadedResourceCount++;

	UWorld* World = GetWorld();
	if (World == nullptr)
	{
		return;
	}

	UEasyNetworkFileSubsystem* EasyNetworkFileSubsystem = World->GetSubsystem<UEasyNetworkFileSubsystem>();
	if (!UDSResourceSubsystem::IsInitialized() || EasyNetworkFileSubsystem == nullptr)
	{
		return;
	}

	UBaseNetworkFileTask* Task = EasyNetworkFileSubsystem->FindTask(TaskId);

	if (Cast<USoftFurnitureResourcePayload>(Task->GetPayload()))
	{
		USoftFurnitureResourcePayload* Payload = Cast<USoftFurnitureResourcePayload>(Task->GetPayload());
		UDSResourceSubsystem::GetInstance()->AddResourceCache(Payload->ResourceInfo); //这个key用的是Folder，成品应该不能用，先留着

		switch (Payload->ResourceInfo.Type)
		{
		case EDSResourceType::Material:
			{
				UDSResourceSubsystem::GetInstance()->RegisterMaterialResource(Task->GetFilePath());
			}
			break;
		case EDSResourceType::Model:
			{
				UDSResourceSubsystem::GetInstance()->MountPakFile(Task->GetFilePath(), false);
			}
			break;
		default:
			break;
		}

		if (!bSucceed)
		{
			UE_LOG(DSFileSubsystemLog, Warning, TEXT("Download resource '%s'_'%s' failure."), *Payload->ResourceInfo.Id, *Payload->ResourceInfo.Name);
			//return;下载方案哪怕有失败也不能return，宁可丢失也不能卡加载
		}
	}

	if (DownloadedResourceCount == NeedsDownloadResourceCount)
	{
		UDSCameraSubsystem::GetInstance()->StartCaptureActor();
	}
}

void UDSFileSubsystem::LoadProject(const TSharedPtr<FJsonObject>& JsonObject)
{
	//加载本地临摹图，本地没有才下载远程
	if (!LoadFacsimile(JsonObject))
	{
		UDSUISubsystem::GetInstance()->SetToastState(EToastState::EWarning, TEXT("临摹图加载失败"), TEXT(""), true);
	}

	// Load all model dependencies.
	//UDSModelDependencySubsystem::GetInstance()->Deserialization(JsonObject);

	//加文件字符串转换成Models数据
	if (!UDSMVCSubsystem::GetInstance()->DeserializationJsonToAllModels(JsonObject))
	{
		UDSUISubsystem::GetInstance()->SetToastState(EToastState::EWarning, TEXT("模型数据转换失败"), TEXT(""), true);
	}

	// Refresh all counter top models when model dependencies are ready.
	TArray<UDSBaseModel*> SideCounterTops = UDSMVCSubsystem::GetInstance()->GetModels(EDSModelType::E_Generated_SideCounterTop);
	for (UDSBaseModel* Model : SideCounterTops)
	{
		Model->OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf);
	}

	TArray<UDSBaseModel*> CounterTops = UDSMVCSubsystem::GetInstance()->GetModels(EDSModelType::E_Generated_CounterTop);
	for (UDSBaseModel* Model : CounterTops)
	{
		auto CT = Cast<UDSCounterTopModel>(Model);
		CT->bShouldGenerateByProperties = false;
		Model->OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf);
	}

	//隐藏吊顶
	TArray<UDSBaseModel*> AllCeilingAreaModels = UDSMVCSubsystem::GetInstance()->GetModels(EDSModelType::E_RoofArea);
	TArray<UDSBaseModel*> AllMoldingCeilingModels = UDSMVCSubsystem::GetInstance()->GetModels(EDSModelType::E_Furniture_MoldingCeiling);
	for (auto Ite : AllCeilingAreaModels)
	{
		Ite->OnExecuteAction(FDSModelExecuteType::ExecuteViewTypeHidden);
	}

	for (auto Ite : AllMoldingCeilingModels)
	{
		Ite->OnExecuteAction(FDSModelExecuteType::ExecuteViewTypeHidden);
	}
}

void UDSFileSubsystem::SetProjectInfoToOpen(const FProjectInfoData& ProjectData) {}

void UDSFileSubsystem::CreateNewProject() {}

void UDSFileSubsystem::SaveAsProject() {}

void UDSFileSubsystem::SaveToLocal(const FString& FilePath)
{
	FString LocalFacsimileImagePath;
	FString LocalFacsimileImageFileMD5;
	FString LocalFacsimileImageFileName;

	if (CurrentFacsimileImageTexture)
	{
		FColor* RawData = static_cast<FColor*>(CurrentFacsimileImageTexture->GetPlatformData()->Mips[0].BulkData.Lock(LOCK_READ_WRITE));
		TArray<FColor> ImageData(RawData, CurrentFacsimileImageTexture->GetSizeX() * CurrentFacsimileImageTexture->GetSizeY());
		TArray<uint8> CompressedData;
		FImageUtils::ThumbnailCompressImageArray(CurrentFacsimileImageTexture->GetSizeX(), CurrentFacsimileImageTexture->GetSizeY(), ImageData, CompressedData);
		CurrentFacsimileImageTexture->GetPlatformData()->Mips[0].BulkData.Unlock();

		LocalFacsimileImageFileMD5 = FMD5::HashBytes(CompressedData.GetData(), CompressedData.Num());
		LocalFacsimileImageFileName = FString::Printf(TEXT("Facsimile_%s.jpg"), *LocalFacsimileImageFileMD5);
		LocalFacsimileImagePath = FPaths::ProjectSavedDir() / TEXT("FacsimileImage") / LocalFacsimileImageFileName;
		if (!FFileHelper::SaveArrayToFile(CompressedData, *LocalFacsimileImagePath))
		{
			UDSUISubsystem::GetInstance()->SetToastState(EToastState::ESuccess, TEXT("临摹图导出失败成功"), TEXT(""), true);
			return;
		}
	}
	/*其他操作全部完成后，开始写数据，写数据过程中不操作任何其他计算函数，单纯写数据，其他操作在之前就准备好*/
	/******************************************************************************************/
	TArray<uint8> AllData;
	FString JStr;
	TSharedRef<TJsonWriter<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>> JsonWriter = TJsonWriterFactory<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>::Create(&JStr);

	JsonWriter->WriteObjectStart();
	{
		//保存临摹图数据
		TArray<uint8> CompressedData;
		FString OutBase64String;

		if (FFileHelper::LoadFileToArray(CompressedData, *LocalFacsimileImagePath))
		{
			OutBase64String = FBase64::Encode(CompressedData);
		}

		JsonWriter->WriteObjectStart(TEXT("Facsimile"));
		{
			//保存本地路径
			JsonWriter->WriteValue(TEXT("LocalFacsimileImagePath"), TEXT("FacsimileImage") / LocalFacsimileImageFileName);
			//保存临摹图的MD5
			JsonWriter->WriteValue(TEXT("LocalFacsimileImageMD5"), LocalFacsimileImageFileMD5);
			//记录底图Actor的缩放值
			JsonWriter->WriteValue(TEXT("FacsimileActorScale"), UDSUISubsystem::GetInstance()->GetFacsimileActor()->GetActorScale3D().ToString());
			//临摹base64
			JsonWriter->WriteValue(TEXT("FacsimileImageBase64"), OutBase64String);
		}
		JsonWriter->WriteObjectEnd();

		//转化model数据为Json
		UDSMVCSubsystem::GetInstance()->SerializeToJsonFromAllModels(JsonWriter);
		// Save model dependencies
		UDSModelDependencySubsystem::GetInstance()->Serialization(JsonWriter);
	}
	JsonWriter->WriteObjectEnd();
	JsonWriter->Close();
	/******************************************************************************************/
	FTCHARToUTF8 Converter(*JStr);
	AllData.SetNum(Converter.Length());
	FMemory::Memcpy(AllData.GetData(), Converter.Get(), Converter.Length());

	if (FFileHelper::SaveArrayToFile(AllData, *FilePath))
	{
		UDSUISubsystem::GetInstance()->SetToastState(EToastState::ESuccess, TEXT("方案文件保存成功"), TEXT(""), true);
	}
	else
	{
		UDSUISubsystem::GetInstance()->SetToastState(EToastState::EWarning, TEXT("方案文件保存失败"), TEXT(""), true);
	}
}

void UDSFileSubsystem::SetProjectInfoToSaveAs(const FProjectInfoData& ProjectData) {}

bool UDSFileSubsystem::LoadFileFromPath(const FString& AbsoluteFilePath, FString& PlanDataStr)
{
	PlanDataStr = LoadJsonFileToStr(AbsoluteFilePath);
	if (PlanDataStr.IsEmpty())
	{
		UDSUISubsystem::GetInstance()->SetToastState(EToastState::EWarning, TEXT("方案文件为空"), TEXT(""), true);
		return false;
	}
	return true;
}

void UDSFileSubsystem::SaveFacsimile(TSharedRef<TJsonWriter<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>> JsonWriter)
{
	JsonWriter->WriteObjectStart(TEXT("Facsimile"));
	{
		//保存本地路径
		JsonWriter->WriteValue(TEXT("LocalFacsimileImagePath"), CurrentFacsimileImageTexture ? (TEXT("FacsimileImage") / FacsimileImageName) : TEXT(""));
		//保存临摹图的MD5
		JsonWriter->WriteValue(TEXT("LocalFacsimileImageMD5"), FacsimileImageMD5);
		//记录底图Actor的缩放值
		JsonWriter->WriteValue(TEXT("FacsimileActorScale"), UDSUISubsystem::GetInstance()->GetFacsimileActor()->GetActorScale3D().ToString());
		//临摹图base64清空
		JsonWriter->WriteValue(TEXT("FacsimileImageBase64"),TEXT(""));
	}
	JsonWriter->WriteObjectEnd();
}

bool UDSFileSubsystem::LoadFacsimile(const TSharedPtr<FJsonObject>& InJsonData)
{
	const TSharedPtr<FJsonObject> FacsimileObj = InJsonData->GetObjectField(TEXT("Facsimile"));
	if (FacsimileObj->GetStringField(TEXT("LocalFacsimileImagePath")).IsEmpty())
	{
		return true; //如果没有存临摹图，返回加载成
	}

	FacsimileImagePath = FPaths::ProjectSavedDir() / FacsimileObj->GetStringField(TEXT("LocalFacsimileImagePath"));

	//打开本地文件，临摹图可能不存在文件，要从base64还原
	if (!FPaths::FileExists(FacsimileImagePath) && !FacsimileObj->GetStringField(TEXT("FacsimileImageBase64")).IsEmpty())
	{
		FString Base64String = FacsimileObj->GetStringField(TEXT("FacsimileImageBase64"));
		TArray<uint8> ImageData;
		if (!FBase64::Decode(Base64String, ImageData))
		{
			UE_LOG(LogTemp, Error, TEXT("Failed to decode Base64 string."));
			return false;
		}
		if (!FFileHelper::SaveArrayToFile(ImageData, *FacsimileImagePath))
		{
			return false;
		}
	}

	FVector ActorScale;
	ActorScale.InitFromString(FacsimileObj->GetStringField(TEXT("FacsimileActorScale")));

	if (FPaths::FileExists(FacsimileImagePath))
	{
		CurrentFacsimileImageTexture = UDesignStationFunctionLibrary::LoadTextureFromFile(FacsimileImagePath);
		if (CurrentFacsimileImageTexture)
		{
			auto Size = FVector2D(CurrentFacsimileImageTexture->GetSizeX(), CurrentFacsimileImageTexture->GetSizeY());
			FacsimileImageBrush = FDeferredCleanupSlateBrush::CreateBrush(CurrentFacsimileImageTexture, Size);
			UDSUISubsystem::GetInstance()->GetFacsimileActor()->FacsimileImage->SetImage(FacsimileImageBrush->GetSlateBrush());
			HasSetFacsimile = true;
			UDSUISubsystem::GetInstance()->GetFacsimileActor()->Facsimile_WidgetComponent->SetDrawSize(Size);
			UDSUISubsystem::GetInstance()->GetFacsimileActor()->SetActorScale3D(ActorScale);
			UDSUISubsystem::GetInstance()->ShowFacsimileActor(true);
			return true;
		}
		return false;
	}

	return false;
}

void UDSFileSubsystem::ShowCutDialog(UTexture2D* ImageTexture)
{
	if (!ImportWidget)
	{
		SAssignNew(ImportWidget, SDSImportImage).OnManualRecognize_Lambda([this]()
		{
			SetFacsimileTexture();
		});
	}
	if (ImportWidget)
	{
		ImportWidget->SetImage(ImageTexture);
		UDSUISubsystem::GetInstance()->PresentModalDialog(
			TEXT("导入底图"),
			FSimpleDelegate::CreateLambda([this]()
			{
				UDSUISubsystem::GetInstance()->RemoveModalDialog();
			}),
			ImportWidget.ToSharedRef(),
			true);
	}
}

void UDSFileSubsystem::RemoveCutDialog()
{
	if (ImportWidget)
	{
		UDSUISubsystem::GetInstance()->RemoveModalDialog();
	}
}

void UDSFileSubsystem::SetFacsimileTexture()
{
	if (ImportWidget->GetImage())
	{
		CurrentFacsimileImageTexture = CutImage(ImportWidget->GetRotatedImage(), ImportWidget->GetCutBox());
		auto Size = FVector2D(CurrentFacsimileImageTexture->GetSizeX(), CurrentFacsimileImageTexture->GetSizeY());
		FacsimileImageBrush = FDeferredCleanupSlateBrush::CreateBrush(CurrentFacsimileImageTexture, Size);
		UDSUISubsystem::GetInstance()->GetFacsimileActor()->FacsimileImage->SetImage(FacsimileImageBrush->GetSlateBrush());
		HasSetFacsimile = true;
		UDSUISubsystem::GetInstance()->GetFacsimileActor()->Facsimile_WidgetComponent->SetDrawSize(Size);
		UDSUISubsystem::GetInstance()->ShowFacsimileActor(true);
		UDSUISubsystem::GetInstance()->RemoveModalDialog();
		UDSUISubsystem::GetInstance()->GenerateRulerDialog();
	}
}

UTexture2D* UDSFileSubsystem::CutImage(UTexture2D* Source, FBox2D CutBox)
{
	FVector2D OrignalSize(Source->GetSizeX(), Source->GetSizeY());
	FBox2D PiexlBox(OrignalSize * CutBox.Min, OrignalSize * CutBox.Max);

	PiexlBox.Min.X = FMath::Clamp(static_cast<int32>(PiexlBox.Min.X), 0, static_cast<int32>(OrignalSize.X));
	PiexlBox.Max.X = FMath::Clamp(static_cast<int32>(PiexlBox.Max.X), 0, static_cast<int32>(OrignalSize.X));

	PiexlBox.Min.Y = FMath::Clamp(static_cast<int32>(PiexlBox.Min.Y), 0, static_cast<int32>(OrignalSize.Y));
	PiexlBox.Max.Y = FMath::Clamp(static_cast<int32>(PiexlBox.Max.Y), 0, static_cast<int32>(OrignalSize.Y));

	FIntPoint TrimedSize(PiexlBox.Max.X - PiexlBox.Min.X, PiexlBox.Max.Y - PiexlBox.Min.Y);

	int32 Channels = Source->HasAlphaChannel() ? 4 : 3;
	uint8* NewTextureData = new uint8[TrimedSize.X * TrimedSize.Y * Channels];

	UTexture2D* TrimedTexture = UTexture2D::CreateTransient(TrimedSize.X, TrimedSize.Y);

	uint8* SourceData = static_cast<uint8*>(Source->GetPlatformData()->Mips[0].BulkData.Lock(LOCK_READ_WRITE));

	for (int32 Row = PiexlBox.Min.Y; Row < PiexlBox.Max.Y; Row++)
	{
		FMemory::Memcpy(&NewTextureData[static_cast<int32>(Channels * ((Row - PiexlBox.Min.Y) * TrimedSize.X))], &SourceData[static_cast<int32>(Channels * (Row * OrignalSize.X + PiexlBox.Min.X))],
		                Channels * TrimedSize.X);
	}
	Source->GetPlatformData()->Mips[0].BulkData.Unlock();
	WriteRawToTexture(TrimedTexture, NewTextureData, false);

	delete NewTextureData;

	return TrimedTexture;
}

UTexture2D* UDSFileSubsystem::RotateTexture(UTexture2D* Source, ETextureRotateMethod RotateType)
{
	FVector2D OrignalSize(Source->GetSizeX(), Source->GetSizeY());
	FBox2D PiexlBox(FVector2D(0, 0), OrignalSize);

	FIntPoint NewSize(OrignalSize.Y, OrignalSize.X);

	int32 Channels = Source->HasAlphaChannel() ? 4 : 3;
	uint8* NewTextureData = new uint8[NewSize.X * NewSize.Y * Channels];

	UTexture2D* NewTexture = UTexture2D::CreateTransient(NewSize.X, NewSize.Y);

	uint8* SourceData = static_cast<uint8*>(Source->GetPlatformData()->Mips[0].BulkData.Lock(LOCK_READ_WRITE));
	for (int32 Row = PiexlBox.Min.Y; Row < PiexlBox.Max.Y; Row++)
	{
		for (int32 Col = PiexlBox.Min.X; Col < PiexlBox.Max.X; Col++)
		{
			for (int32 Channel = 0; Channel < Channels; Channel++)
			{
				switch (RotateType)
				{
				case ETextureRotateMethod::ClockWise90:
					NewTextureData[Channels * ((Col) * NewSize.X + (NewSize.X - 1 - Row)) + Channel] = SourceData[static_cast<int32>(Channels * (Row * OrignalSize.X + Col)) + Channel];
					break;
				case ETextureRotateMethod::AntiClockWise90:
					NewTextureData[Channels * ((NewSize.Y - Col - 1) * NewSize.X + Row) + Channel] = SourceData[static_cast<int32>(Channels * (Row * OrignalSize.X + Col)) + Channel];
					break;
				default:
					break;
				}
			}
		}
	}
	Source->GetPlatformData()->Mips[0].BulkData.Unlock();
	WriteRawToTexture(NewTexture, NewTextureData, false);
	delete NewTextureData;

	return NewTexture;
}

bool UDSFileSubsystem::WriteRawToTexture(UTexture2D* NewTexture2D, const TArray<uint8>& RawData, bool bGray, bool bUseSRGB/* = true*/)
{
	return WriteRawToTexture(NewTexture2D, RawData.GetData(), bGray, bUseSRGB);
}

bool UDSFileSubsystem::WriteRawToTexture(UTexture2D* NewTexture2D, const uint8* RawData, bool bGray, bool bUseSRGB)
{
	int32 Height = NewTexture2D->GetSizeY();
	int32 Width = NewTexture2D->GetSizeX();

	bool bValidTexture = false;
	// Fill in the base mip for the texture we created
	uint8* MipData = static_cast<uint8*>(NewTexture2D->GetPlatformData()->Mips[0].BulkData.Lock(LOCK_READ_WRITE));
	for (int32 y = 0; y < Height; y++)
	{
		if (bGray)
		{
			uint8* DestPtr = &MipData[y * Width * sizeof(FColor)];
			const uint8* SrcPtr = &(RawData)[(Height - 1 - y) * Width];
			for (int32 x = 0; x < Width; x++)
			{
				//如果有一个像素值不为0 则该图片为有效图片
				if (*SrcPtr != 0)
				{
					bValidTexture = true;
				}
				*DestPtr++ = *SrcPtr;
				*DestPtr++ = *SrcPtr;
				*DestPtr++ = *SrcPtr;
				*DestPtr++ = 255;
				SrcPtr++;
			}
		}
		else
		{
			uint8* DestPtr = &MipData[(Height - 1 - y) * Width * sizeof(FColor)];
			const FColor* SrcPtr = &((FColor*)(RawData))[(Height - 1 - y) * Width];
			for (int32 x = 0; x < Width; x++)
			{
				//如果有一个像素值不为0 则该图片为有效图片
				if (SrcPtr->B != 0 || SrcPtr->G != 0 || SrcPtr->R != 0)
				{
					bValidTexture = true;
				}
				*DestPtr++ = SrcPtr->B;
				*DestPtr++ = SrcPtr->G;
				*DestPtr++ = SrcPtr->R;
				*DestPtr++ = SrcPtr->A;
				SrcPtr++;
			}
		}
	}
	NewTexture2D->GetPlatformData()->Mips[0].BulkData.Unlock();

	// Set options
	NewTexture2D->SRGB = bUseSRGB;
#if WITH_EDITORONLY_DATA
	NewTexture2D->CompressionNone = true;
	NewTexture2D->MipGenSettings = TMGS_NoMipmaps;
#endif
	NewTexture2D->CompressionSettings = TC_EditorIcon;

	NewTexture2D->UpdateResource();

	return bValidTexture;
}

bool UDSFileSubsystem::ExportTextureToImage(UTexture2D* InTexture)
{
	if (InTexture)
	{
		FColor* RawData = static_cast<FColor*>(InTexture->GetPlatformData()->Mips[0].BulkData.Lock(LOCK_READ_WRITE));
		TArray<FColor> ImageData(RawData, InTexture->GetSizeX() * InTexture->GetSizeY());
		TArray<uint8> CompressedData;
		FImageUtils::ThumbnailCompressImageArray(InTexture->GetSizeX(), InTexture->GetSizeY(), ImageData, CompressedData);
		InTexture->GetPlatformData()->Mips[0].BulkData.Unlock();

		FString FileMD5 = FMD5::HashBytes(CompressedData.GetData(), CompressedData.Num());
		FString FileName = FString::Printf(TEXT("Facsimile_%s.jpg"), *FileMD5);
		FString FilePath = FPaths::ProjectSavedDir() / TEXT("FacsimileImage") / FileName;
		if (FFileHelper::SaveArrayToFile(CompressedData, *FilePath))
		{
			FacsimileImageName = FileName;
			FacsimileImagePath = FilePath;
			FacsimileImageMD5 = FileMD5;
			return true;
		}
	}
	return false;
}

FString UDSFileSubsystem::GetTimeStampString()
{
	int64 ts = (FDateTime::Now().GetTicks() - FDateTime(1970, 1, 1, 8).GetTicks()) / 10000;
	char buf[64];
	_i64toa_s(ts, buf, 64, 10);
	FString str = UTF8_TO_TCHAR(buf);
	return str;
}

void UDSFileSubsystem::SaveResultBeforeRender(bool Result)
{
	SaveResult(Result);
}

void UDSFileSubsystem::QueryPlanInfoByPlanId(const FString& PlanId)
{
	bDownloadPlanFileTaskComplete = false;
	bDownloadFacsimileImageTaskComplete = false;

	if (FCString::Atoi(*PlanId) == 0)
	{
		return;
	}
	CurPlanId = FCString::Atoi(*PlanId);

	const FString URL = FString::Printf(TEXT("%s/bk-design/api/designPlan/infoPlan?id=%d"), *UDSNetworkSubsystem::GetInstance()->GetRequestDomain(), CurPlanId);

	UDSNetworkSubsystem* NetworkSubsystem = UDSNetworkSubsystem::GetInstance();
	if (NetworkSubsystem == nullptr)
	{
		return;
	}
	FHttpRequestPtr HttpRequestHandle = NetworkSubsystem->CreateGetRequest(URL);
	HttpRequestHandle->OnProcessRequestComplete().BindUObject(this, &ThisClass::OnQuertPlanInfoCompleted);
	HttpRequestHandle->ProcessRequest();
}

void UDSFileSubsystem::OnQuertPlanInfoCompleted(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bConnectedSuccessfully)
{
	if (!bConnectedSuccessfully || !EHttpResponseCodes::IsOk(Response->GetResponseCode()))
	{
		UE_LOG(LogTemp, Error, TEXT("Query folder resource informations failure."));
		return;
	}

	TSharedPtr<FJsonObject> ResponseObj;
	TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(Response->GetContentAsString());

	if (!FJsonSerializer::Deserialize(Reader, ResponseObj))
	{
		UE_LOG(LogTemp, Error, TEXT("Parse response json failure."));
		return;
	}

	if (!ResponseObj->GetBoolField(TEXT("success")))
	{
		UE_LOG(LogTemp, Error, TEXT("%s : The success field is false, msg is '%s'."), __FUNCTIONW__, *ResponseObj->GetStringField(TEXT("msg")));
		return;
	}

	if (!ResponseObj->HasTypedField<EJson::Object>(TEXT("resp")))
	{
		UE_LOG(LogTemp, Error, TEXT("%s : Not found 'resp' field."), __FUNCTIONW__);
		return;
	}

	UWorld* World = GetWorld();
	if (World == nullptr)
	{
		return;
	}

	UEasyNetworkFileSubsystem* EasyNetworkFileSubsystem = World->GetSubsystem<UEasyNetworkFileSubsystem>();
	if (EasyNetworkFileSubsystem == nullptr)
	{
		return;
	}

	TArray<TSharedPtr<FJsonValue>> FacsimileJsonObject = ResponseObj->GetObjectField(TEXT("resp"))->GetArrayField(TEXT("planFileList"));
	for (auto& It : FacsimileJsonObject)
	{
		if (It->AsObject()->GetIntegerField(TEXT("fileType")) == 4)
		{
			FacsimileImageUrl = It->AsObject()->GetStringField(TEXT("fileUrl"));
			FacsimileImageMD5 = It->AsObject()->GetStringField(TEXT("md5"));
			FacsimileImageName = It->AsObject()->GetStringField(TEXT("fileName"));
			FacsimileImagePath = FPaths::ProjectSavedDir() / TEXT("FacsimileImage") / FacsimileImageName;
			break;
		}
	}

	if (!FacsimileImageUrl.IsEmpty())
	{
		UBaseNetworkFileTask* NewFacsimileDownloadTask = EasyNetworkFileSubsystem->CreateTask(ENetworkFileOperation::ENFO_Download);
		NewFacsimileDownloadTask->SetUrl(FacsimileImageUrl);
		NewFacsimileDownloadTask->SetFilePath(FacsimileImagePath);
		NewFacsimileDownloadTask->SetChecksum(FacsimileImageMD5);

		UCustomRelativeResourcePayload* NewFacsimilePayload = NewObject<UCustomRelativeResourcePayload>(NewFacsimileDownloadTask);
		NewFacsimileDownloadTask->SetPayload(NewFacsimilePayload);

		NewFacsimileDownloadTask->OnTaskCompleteEvent().AddDynamic(this, &ThisClass::OnNewFacsimileImageDownloadCompleted);
		NewFacsimileDownloadTask->StartProcess();
	}
	else
	{
		bDownloadFacsimileImageTaskComplete = true;
	}

	TSharedPtr<FJsonObject> PlanFileJsonObject = ResponseObj->GetObjectField(TEXT("resp"))->GetObjectField(TEXT("bkPlanFile"));
	if (PlanFileJsonObject == nullptr)
	{
		UE_LOG(LogTemp, Error, TEXT("%s : Not found 'bkPlanFile' field."), __FUNCTIONW__);
		OpenProject(TEXT(""));
		return;
	}

	CurProjectInfo.Deserialization(ResponseObj->GetObjectField(TEXT("resp")));

	CurPlanFileMD5 = PlanFileJsonObject->GetStringField(TEXT("md5"));
	CurPlanFilePath = FString::Printf(TEXT("%s/ProjectFiles/%s_%s.json"), *FPaths::ProjectSavedDir(), *FString::FromInt(CurPlanId), *CurPlanFileMD5);

	UBaseNetworkFileTask* NewTask = EasyNetworkFileSubsystem->CreateTask(ENetworkFileOperation::ENFO_Download);
	NewTask->SetUrl(PlanFileJsonObject->GetStringField(TEXT("fileUrl")));
	NewTask->SetFilePath(CurPlanFilePath);
	NewTask->SetChecksum(CurPlanFileMD5);

	UCustomRelativeResourcePayload* Payload = NewObject<UCustomRelativeResourcePayload>(NewTask);
	NewTask->SetPayload(Payload);

	NewTask->OnTaskCompleteEvent().AddDynamic(this, &ThisClass::OnPlanFileDownloadCompleted);
	NewTask->StartProcess();
}

void UDSFileSubsystem::OnPlanFileDownloadCompleted(const FString& TaskId, bool bSucceed)
{
	UWorld* World = GetWorld();
	if (World == nullptr)
	{
		return;
	}

	UEasyNetworkFileSubsystem* EasyNetworkFileSubsystem = World->GetSubsystem<UEasyNetworkFileSubsystem>();
	if (!UDSResourceSubsystem::IsInitialized() || EasyNetworkFileSubsystem == nullptr)
	{
		return;
	}

	UBaseNetworkFileTask* Task = EasyNetworkFileSubsystem->FindTask(TaskId);
	UCustomRelativeResourcePayload* Payload = Cast<UCustomRelativeResourcePayload>(Task->GetPayload());
	if (Payload == nullptr)
	{
		return;
	}

	if (!bSucceed)
	{
		UE_LOG(LogTemp, Warning, TEXT("Download relative resource '%s' failure."), *Payload->ResourceInfo.Id);
		UDSUISubsystem::GetInstance()->SetToastState(EToastState::EWarning, TEXT("方案文件下载失败"), TEXT(""), true);
		return;
	}

	bDownloadPlanFileTaskComplete = true;
	CheckToOpen();
}

void UDSFileSubsystem::OnNewFacsimileImageDownloadCompleted(const FString& TaskId, bool bSucceed)
{
	UWorld* World = GetWorld();
	if (World == nullptr)
	{
		return;
	}

	UEasyNetworkFileSubsystem* EasyNetworkFileSubsystem = World->GetSubsystem<UEasyNetworkFileSubsystem>();
	if (!UDSResourceSubsystem::IsInitialized() || EasyNetworkFileSubsystem == nullptr)
	{
		return;
	}

	UBaseNetworkFileTask* Task = EasyNetworkFileSubsystem->FindTask(TaskId);
	UCustomRelativeResourcePayload* Payload = Cast<UCustomRelativeResourcePayload>(Task->GetPayload());
	if (Payload == nullptr)
	{
		return;
	}

	if (!bSucceed)
	{
		UE_LOG(LogTemp, Warning, TEXT("Download relative resource '%s' failure."), *Payload->ResourceInfo.Id);
		UDSUISubsystem::GetInstance()->SetToastState(EToastState::EWarning, TEXT("临摹图下载失败"), TEXT(""), true);
		return;
	}

	bDownloadFacsimileImageTaskComplete = true;
	CheckToOpen();
}

void UDSFileSubsystem::CheckToOpen()
{
	if (bDownloadPlanFileTaskComplete && bDownloadFacsimileImageTaskComplete)
	{
		OnProjectFileDownloaded.Broadcast();
		OpenProject(CurPlanFilePath);
	}
}

void UDSFileSubsystem::SaveResult(bool Result)
{
	bUploadThumbnailTaskComplete = false;
	bUploadFacsimileImageTaskComplete = false;
	bUploadPlanFileTaskComplete = false;

	OnSaveResultDelegate.Broadcast(Result);
}

void UDSFileSubsystem::Reset()
{
	bUploadThumbnailTaskComplete = false;
	bUploadFacsimileImageTaskComplete = false;
	bUploadPlanFileTaskComplete = false;
	FacsimileImageUrl = TEXT("");
	FacsimileImageName = TEXT("");
	FacsimileImagePath = TEXT("");
	FacsimileImageMD5 = TEXT("");
	ThumbnailUrl = TEXT("");
	CurPlanFileUrl = TEXT("");
	CurPlanFileMD5 = TEXT("");
	CurPlanFilePath = TEXT("");
}
