﻿// Fill out your copyright notice in the Description page of Project Settings.


#include "DPIDeviceScalingRule.h"

float UDPIDeviceScalingRule::GetDPIScaleBasedOnSize(FIntPoint Size) const
{
#if WITH_EDITOR
	return Super::GetDPIScaleBasedOnSize(Size);
#else
	TSharedPtr<SWindow> ActiveWindow = GEngine->GameViewport->GetWindow();
	if (!ActiveWindow)
	{
		return Super::GetDPIScaleBasedOnSize(Size);
	}

	TSharedPtr<FGenericWindow> NativeWindow = ActiveWindow->GetNativeWindow();
	if (!NativeWindow)
	{
		return Super::GetDPIScaleBasedOnSize(Size);
	}

	return NativeWindow->GetDPIScaleFactor();
#endif
}
