﻿#include "DSPipeSubsystem.h"

#include "Windows/WindowsHWrapper.h"

#include "Subsystems/MVC/Library/DSToolLibrary.h"

UDSPipeSubsystem* UDSPipeSubsystem::Instance = nullptr;

DECLARE_LOG_CATEGORY_CLASS(LogDSPipeSubsystem, Log, All);

static void VerifyWinResult(BOOL bResult, const TCHAR* InMessage)
{
	if (!bResult)
	{
		TCHAR ErrorBuffer[MAX_SPRINTF];
		uint32 Error = GetLastError();
		const TCHAR* Buffer = FWindowsPlatformMisc::GetSystemErrorMessage(<PERSON>rrorBuffer, MAX_SPRINTF, Error);
		FString Message = FString::Printf(TEXT("FAILED (%s) with GetLastError() %d: %s!\n"), InMessage, <PERSON>rror, <PERSON><PERSON>rBuffer);
		FPlatformMisc::LowLevelOutputDebugStringf(*Message);
		UE_LOG(LogDSPipeSubsystem, Fatal, TEXT("%s"), *Message);
	}
	verify(bResult != 0);
}

UDSPipeSubsystem::UDSPipeSubsystem()
	: ClientPipe(nullptr)
{
}

UDSPipeSubsystem* UDSPipeSubsystem::GetInstance()
{
	return Instance;
}

void UDSPipeSubsystem::Initialize(FSubsystemCollectionBase& Collection)
{
	Super::Initialize(Collection);

	Instance = this;

	if (FParse::Value(FCommandLine::Get(), TEXT("ClientId="), ClientId) && FParse::Value(FCommandLine::Get(), TEXT("ServerPipeName="), ServerPipeName))
	{
		uint32 Flags = FILE_ATTRIBUTE_NORMAL;
		ClientPipe = CreateFile(*ServerPipeName,
								GENERIC_READ | GENERIC_WRITE,
								FILE_SHARE_READ | FILE_SHARE_WRITE,
								NULL,
								OPEN_EXISTING,
								Flags,
								NULL);

		if (ClientPipe == nullptr || ClientPipe == INVALID_HANDLE_VALUE)
		{
			ClientPipe = nullptr;
			UE_LOG(LogDSPipeSubsystem, Error, TEXT("Create client pipe of name '%s' failed."), *ServerPipeName);
			return;
		}

		UE_LOG(LogDSPipeSubsystem, Log, TEXT("Connect to server pipe '%s' successfully."), *ServerPipeName);

		// Send init message to server
		WriteMessageToServer(FString::Printf(TEXT("{\"type\":%d,\"clientId\":\"%s\"}"), static_cast<int32>(EPipeMessageType::Init), *ClientId));
	}
	else
	{
		UE_LOG(LogDSPipeSubsystem, Log, TEXT("Startup as developer, skip pipe operation."));
	}
}

void UDSPipeSubsystem::Deinitialize()
{
	Super::Deinitialize();

	Instance = nullptr;

	if (ClientPipe != nullptr)
	{
		FlushFileBuffers(ClientPipe);
		DisconnectNamedPipe(ClientPipe);
		CloseHandle(ClientPipe);

		ClientId.Empty();
		ServerPipeName.Empty();
		ClientPipe = nullptr;
	}
}

void UDSPipeSubsystem::Tick(float DeltaTime)
{
	if (ClientPipe == nullptr)
	{
		return;
	}

	Windows::DWORD BytesToRead = 0;
	BOOL Result = PeekNamedPipe(ClientPipe, NULL, 0, NULL, &BytesToRead, NULL);
	if (!Result)
	{
		VerifyWinResult(Result, TEXT("PeekNamedPipe"));
		return;
	}

	if (BytesToRead > 0)
	{
		DWORD ReadedBytes;
		TArray<uint8> ReadBuffer;
		ReadBuffer.SetNumZeroed(BytesToRead);
		Result = ReadFile(ClientPipe, (void*)ReadBuffer.GetData(), BytesToRead, &ReadedBytes, NULL);
		if (Result)
		{
			FString Message = UTF8_TO_TCHAR(ReadBuffer.GetData());

			if (!Message.IsEmpty())
			{
				UE_LOG(LogDSPipeSubsystem, Log, TEXT("Received message from server pipe: %s"), *Message);
				ProcessServerMessage(Message);
			}
		}
		else
		{
			VerifyWinResult(Result, TEXT("ReadFile"));
			return;
		}
	}

	if (PendingMessage.Num() > 0)
	{
		const FString& Message = PendingMessage.Last();

		int32 MessageLength = FTCHARToUTF8_Convert::ConvertedLength(*Message, Message.Len());

		TArray<uint8> MessageBuffer;
		MessageBuffer.SetNumUninitialized(MessageLength);
		FTCHARToUTF8_Convert::Convert((UTF8CHAR*)MessageBuffer.GetData(), MessageBuffer.Num(), *Message, Message.Len());

		DWORD BytesWritten;
		Result = WriteFile(ClientPipe, MessageBuffer.GetData(), MessageBuffer.Num(), &BytesWritten, NULL);
		if (!Result)
		{
			VerifyWinResult(Result, TEXT("WriteFile"));
			return;
		}

		PendingMessage.Pop();
	}
}

TStatId UDSPipeSubsystem::GetStatId() const
{
	RETURN_QUICK_DECLARE_CYCLE_STAT(UDSPipeSubsystem, STATGROUP_Tickables);
}

void UDSPipeSubsystem::RefreshFrameTitle(const FString& NewTitle)
{
	WriteMessageToServer(FString::Printf(TEXT("{\"type\":%d, \"clientId\": \"%s\",\"title\":\"%s\"}"), static_cast<int>(EPipeMessageType::RefreshFrameTitle), *ClientId, *NewTitle));
}

void UDSPipeSubsystem::WriteMessageToServer(const FString& Message)
{
	if (Message.IsEmpty())
	{
		return;
	}

	PendingMessage.Add(Message);
}

FOnReceivedRefreshTokenMessageDelegate& UDSPipeSubsystem::OnReceivedRefreshTokenMessageEvent()
{
	return OnReceivedRefreshTokenMessage;
}

FOnReceivedUploadConfigMessageDelegate& UDSPipeSubsystem::OnReceivedUploadConfigMessageEvent()
{
	return OnReceivedUploadConfigMessage;
}

void UDSPipeSubsystem::ProcessServerMessage(const FString& Message)
{
	TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(Message);

	TSharedPtr<FJsonObject> MessageObj;
	if (!FJsonSerializer::Deserialize(Reader, MessageObj))
	{
		UE_LOG(LogDSPipeSubsystem, Error, TEXT("Received incorrect message from pipe server: %s"), *Message);
		return;
	}

	EPipeMessageType MessageType = static_cast<EPipeMessageType>(MessageObj->GetIntegerField(TEXT("type")));
	switch (MessageType)
	{
	case EPipeMessageType::Init:
		{
			FString PrivatePem;
			if (!FFileHelper::LoadFileToString(PrivatePem, *FPaths::Combine(FPaths::ProjectContentDir(), TEXT("SSL/private.pem"))))
			{
				UE_LOG(LogDSPipeSubsystem, Error, TEXT("Can not load private.pem from disk."));
				break;
			}

			FString EncryptedConfig = MessageObj->GetStringField(TEXT("uploadConfig"));
			if (EncryptedConfig.IsEmpty())
			{
				UE_LOG(LogDSPipeSubsystem, Error, TEXT("Received empty upload config from server."));
				break;
			}

			try
			{
				FString DecryptedConfig = UDSToolLibrary::RSA_OAEP_SHA256_Decrypt_Base64(EncryptedConfig, PrivatePem);

				TArray<FString> UploadConfigs;
				DecryptedConfig.ParseIntoArray(UploadConfigs, TEXT("\r\n"));
				if (UploadConfigs.Num() != 3)
				{
					UE_LOG(LogDSPipeSubsystem, Error, TEXT("Received invalid upload config from server."));
					break;
				}

				OnReceivedUploadConfigMessage.Broadcast(UploadConfigs[0], UploadConfigs[1], UploadConfigs[2]);
			}
			catch (std::exception& Err)
			{
				UE_LOG(LogDSPipeSubsystem, Error, TEXT("Decrypt upload config failed, %hs."), Err.what());
			}
		}
		break;
	case EPipeMessageType::RefreshToken:
		{
			OnReceivedRefreshTokenMessage.Broadcast(MessageObj->GetStringField(TEXT("token")));
		}
		break;
	default:
		UE_LOG(LogDSPipeSubsystem, Warning, TEXT("Received invalid message type: %d"), MessageType);
		break;
	}
}
