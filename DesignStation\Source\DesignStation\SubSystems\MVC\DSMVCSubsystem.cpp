﻿// Fill out your copyright notice in the Description page of Project Settings.
#include "DSMVCSubsystem.h"

#include "FileHelper.h"
#include "BasicClasses/DesignStationController.h"
#include "Subsystems/Camera/DSCameraSubsystem.h"
#include "Subsystems/Pendant/DSPendantSubsystem.h"
#include "SubSystems/Resource/DSResourceSubsystem.h"
#include "SubSystems/MVC/Library/DSPathLibrary.h"
#include "Subsystems/MVC/Core/Property/DoorAndWindowProperty.h"
#include "SubSystems/MVC/CatalogSupport/Library/RefRelationFunction.h"
#include "SubSystems/MVC/CatalogSupport/Core/RefToParamDataLibrary.h"
#include "Subsystems/StatusFlag/StatusFlag.h"
#include "ImageProcess.h"
#include "TimerManager.h"
#include "Core/DataPool/ResourceData.h"
#include "Commands/Public/DSCustomFloatingToolBarCommands.h"
#include "Commands/Public/DSGenericCommands.h"
#include "Library/DSToolLibrary.h"
#include "Model/DoorAndWindow/DSDoorAndWindowBaseModel.h"
#include "Model/House/Area/DSHouseAreaModel.h"
#include "Model/House/Wall/DSHouseWallModel.h"
#include "SubSystems/Snap/DSSnapSubsystem.h"
#include "SubSystems/UI/DSUISubsystem.h"
#include "SubSystems/Undo/DSRevokeSubsystem.h"
#include "SubSystems/MVC/Core/Property/HouseAreaProperty.h"
#include "Widgets/SViewport.h"
#include "SubSystems/Undo/Library/DSRevokeLibrary.h"
#include "Library/CounterTopLibrary.h"
#include "Subsystems/File/Core/ProtoDataConvert/ProtobufOperatorFunctionLibrary.h"
#include "Subsystems/MVC/StateMachine/State/Public/DSFSMCounterTop.h"
#include "SubSystems/MVC/Core/Property/CeilingAreaProperty.h"
#include "Kismet/GameplayStatics.h"
#include "SubSystems/MVC/Library/CeilingLibrary.h"
#include "Subsystems/MVC/Model/Custom/Library/DSWallBoardLibrary.h"
#include "SubSystems/UI/Widget/WindowLayoutWidget.h"

DEFINE_LOG_CATEGORY(DSMVCSubsystemLog);

extern const TArray<EDSModelType> NoBroadcastNoModifyType;

extern const TArray<EDSModelType> ConsiderAsHouseType = {
	EDSModelType::E_House_Wall,
	EDSModelType::E_House_Area,
	EDSModelType::E_House_Platform,
	EDSModelType::E_House_Beam,
	EDSModelType::E_House_Area_Split_Line
};

extern const TArray<EDSModelType> RevokeConsiderAsHouseType = {
	EDSModelType::E_House_Wall,
	EDSModelType::E_House_Platform,
	EDSModelType::E_House_Beam,
	EDSModelType::E_House_Area_Split_Line
};

extern const TArray<EDSModelType> RevokeConsiderType = {
	EDSModelType::E_House_Pillar,
	EDSModelType::E_House_Door,
	EDSModelType::E_House_Window,
	EDSModelType::E_Custom_UpperCabinet,
	EDSModelType::E_Custom_BaseCabinet,
	EDSModelType::E_Custom_CornerCabinet,
	EDSModelType::E_Custom_TallCabinet,
	EDSModelType::E_Custom_WallCabinet
};


extern const TArray<EDSModelType> RevokeNoRecheckType = {
	EDSModelType::E_House_Pillar,
	EDSModelType::E_House_Door,
	EDSModelType::E_House_Window
};

UDSMVCSubsystem* UDSMVCSubsystem::Instance = nullptr;

UDSMVCSubsystem::UDSMVCSubsystem()
{
	Instance = this;
}

void UDSMVCSubsystem::Initialize(FSubsystemCollectionBase& Collection)
{
	Super::Initialize(Collection);

	GenericCommandList = MakeShareable(new FUICommandList);
	
	RegisterInGameCommands();

	BindGenericInputCommands();

	FSlateApplication::Get().OnFocusChanging().AddUObject(this, &ThisClass::OnKeyboardFocusChanging);

	ResourceData = NewObject<UResourceData>();

	DS_FSM = NewObject<UDSFiniteStateMachine>(this);
	if (DS_FSM)
	{
		DS_FSM->Initialize();
	}

	GlobalDisPlayParams = FGlobalDisPlayParameter();

	//处理相机类型切换
	CameraTypeHandle = UDSCameraSubsystem::GetInstance()->CameraTypeDelegate.AddLambda([&](int32 NewType)
		{
			ECameraType Type = static_cast<ECameraType>(NewType);
			if (Type == ECameraType::EDollHouse || Type == ECameraType::EWalk)
			{
				GetWorld()->GetTimerManager().SetTimer(TimerHandle, this, &UDSMVCSubsystem::OnHideCeilinTimer, 1.0f, true);
				if (AllDefaultGridMeshs.IsEmpty())
				{
					TArray<AActor*> AllActors;
					UGameplayStatics::GetAllActorsWithTag(GetWorld(), FName(TEXT("DefaultGridMesh")), AllActors);
					for (auto Ite : AllActors)
					{
						AllDefaultGridMeshs.Add(Ite);
					}
				}
			}
			else
			{
				GetWorld()->GetTimerManager().ClearTimer(TimerHandle);
			}
			HideCeilingByCameraType(Type);

			//处理Model选择状态
			VerifyCurrentSelectedModelByCameraType(Type);
			//显示隐藏Model
			ShowHideModelByCameraType(Type);
			if (UDSUISubsystem::GetInstance()->IsInitialized()&& UDSUISubsystem::GetInstance()->GetWindowLayoutWidget())
			{
				UDSUISubsystem::GetInstance()->GetWindowLayoutWidget()->CloseAllFakeWindows();
			}
		});
}

void UDSMVCSubsystem::Deinitialize()
{
	if (OBJECT_VALID_FOR_USE(ResourceData))
	{
		ResourceData->Clear();
		ResourceData = nullptr;
	}

	DS_FSM = nullptr;

	UnregisterInGameCommands();

	GConfig->UnloadFile(IDSCommandsConfigIni::GetConfigFilename());

	Super::Deinitialize();

	Instance = nullptr;
}

void UDSMVCSubsystem::OnWorldBeginPlay(UWorld& InWorld)
{
	if (UGameViewportClient* Client = InWorld.GetGameViewport())
	{
		if (FEngineShowFlags* ShowFlags = Client->GetEngineShowFlags())
		{
			ShowFlags->SetSpecular(false);
		}
	}
}

void UDSMVCSubsystem::OnLayoutRefresh(UDSBaseModel* InModel)
{
	//UE_LOG(DSMVCSubsystemLog, Warning, TEXT("OnLayoutRefresh -- Model"));

	if (InModel)
	{
		auto Type = InModel->GetModelType();
		if (Type == EDSModelType::E_House_Wall || Type == EDSModelType::E_HouseWallPathPoint)
		{
			UDSPathLibrary::GeneratePaths(EDSModelType::E_House_Wall);
		}
		else if (Type == EDSModelType::E_House_Door || Type == EDSModelType::E_House_Window)
		{
			auto Walls = GetModels(EDSModelType::E_House_Wall);
			auto Prop = static_cast<FDSDoorAndWindowProperty*>(InModel->GetProperty());
			bool bVaild = false;
			for (auto& Wall : Walls)
			{
				auto Outline = Wall->GetBottomOutline();
				auto DWLeft = Prop->GetLeftSegment();
				auto DWRight = Prop->GetRightSegment();

				auto LeftStart = DWLeft->SegmentStart;
				auto LeftEnd = DWLeft->SegmentEnd;
				auto RightStart = DWRight->SegmentStart;
				auto RightEnd = DWRight->SegmentEnd;

				if (FImageProcessModule::Get()->PointInPolygon(LeftStart, Outline, true, 1.f) &&
					FImageProcessModule::Get()->PointInPolygon(LeftEnd, Outline, true, 1.f) &&
					FImageProcessModule::Get()->PointInPolygon(RightStart, Outline, true, 1.f) &&
					FImageProcessModule::Get()->PointInPolygon(RightEnd, Outline, true, 1.f))
				{
					Cast<UDSHouseWallModel>(Wall)->AddDoorAndWindow(InModel);
					Cast<UDSDoorAndWindowBaseModel>(InModel)->AddLinkWall(Wall);
					Wall->OnExecuteAction(FDSModelExecuteType::ExecuteAll);
					break;
				}
			}
			if (bVaild)
			{
				UDSDrawingSubsystem::GetInstance()->Drawing();
			}
			else
			{
				UDSDrawingSubsystem::GetInstance()->Drawing();
			}
		}
		else if (Type == EDSModelType::E_House_Beam || Type == EDSModelType::E_BeamPathPoint)
		{
			UDSPathLibrary::GeneratePaths(EDSModelType::E_House_Beam);
		}
		else if (Type == EDSModelType::E_House_Platform || Type == EDSModelType::E_PlatformPathPoint)
		{
			UDSPathLibrary::GeneratePaths(EDSModelType::E_House_Platform);
		}
	}
	else
	{
		//UE_LOG(DSMVCSubsystemLog, Warning, TEXT("OnLayoutRefresh -- Model -- Refresh All"));

		UDSPathLibrary::GeneratePaths(EDSModelType::E_House_Wall);
		UDSPathLibrary::GeneratePaths(EDSModelType::E_House_Beam);
		UDSPathLibrary::GeneratePaths(EDSModelType::E_House_Platform);
		FDSVisionDataInfo VisionInfo;
		UDSDrawingSubsystem::GetInstance()->SetDrawingSelect(VisionInfo);
	}
}

void UDSMVCSubsystem::OnLayoutRefresh(const EDSModelType& InType)
{
	//UE_LOG(DSMVCSubsystemLog, Warning, TEXT("OnLayoutRefresh -- Type"));

	if (InType == EDSModelType::E_House_Wall || InType == EDSModelType::E_HouseWallPathPoint)
	{
		UDSPathLibrary::GeneratePaths(EDSModelType::E_House_Wall);
	}
	else if (InType == EDSModelType::E_House_Beam || InType == EDSModelType::E_BeamPathPoint)
	{
		UDSPathLibrary::GeneratePaths(EDSModelType::E_House_Beam);
	}
	else if (InType == EDSModelType::E_House_Platform || InType == EDSModelType::E_PlatformPathPoint)
	{
		UDSPathLibrary::GeneratePaths(EDSModelType::E_House_Platform);
	}
	else if (InType == EDSModelType::E_House_Area_Split_Line)
	{
		UDSPathLibrary::GenerateAreas();
	}
}

void UDSMVCSubsystem::OnExecuteRefreshShowForAreaSelect(UDSBaseModel* InModel, const FDSModelExecuteType& ExecuteType)
{
	//如果当前是房间选择状态 ，判断移动的对象是否在显示区域内 --- mini map
	if (SelectedShowingAreaModel)
	{
		if (ExecuteType.BaseExecuteType ==	EDSModelExecuteBaseType::E_MET_Transform)
		{
			UDSBaseModel* OwnerModel = InModel->GetOwnerModel();
			if (!OwnerModel)
			{
				bool bAreaHidden = InModel->GetIsAreaHidden();
				const FVector& Location = InModel->GetProperty()->GetTransformPropertyRef().Location;
				const FOutlineInfo& AreaOutLine = SelectedShowingAreaModel->GetOutlineInfo();
				bool bInArea = FImageProcessModule::Get()->PointInPolygon(Location, AreaOutLine.BottomOutline);
				if (!bInArea && !bAreaHidden)
				{
					InModel->OnExecuteAction(FDSModelExecuteType::ExecuteAreaHidden);
				}
				else if (bInArea && bAreaHidden)
				{
					InModel->OnExecuteAction(FDSModelExecuteType::ExecuteUnAreaHidden);
				}
			}
		}
		else if (ExecuteType.BaseExecuteType == EDSModelExecuteBaseType::E_MET_Delete)
		{
			if (InModel->GetUUID() == SelectedShowingAreaModel->GetUUID())
			{
				ShowSingleAreaModel("");
			}
		}
	}
}

void UDSMVCSubsystem::OnExecuteRefreshDrawing(UDSBaseModel* InModel, const FDSModelExecuteType& ExecuteType)
{
	UDSDrawingSubsystem::GetInstance()->HandleModelExecutedAction(InModel, ExecuteType);
}

void UDSMVCSubsystem::OnClearSnapLine()
{
	UDSSnapSubsystem::GetInstance()->ClearVisionDrawing();
	UDSDrawingSubsystem::GetInstance()->DrawingSnap(FDSVisionDataInfo());
}

void UDSMVCSubsystem::OnRevokeExecute(bool IsUndo)
{
	bool RevokeExecute = false;
	if (IsUndo)
	{
		RevokeExecute = UDSRevokeSubsystem::GetInstance()->Undo(GetRevokeMark());
	}
	else
	{
		RevokeExecute = UDSRevokeSubsystem::GetInstance()->Redo(GetRevokeMark());
	}

	if (!RevokeExecute)
		return;

	UDSBaseModel* CurrentModel = GetCurrentModel();
	if (UDSGizmoModel* GizmoModel = Cast<UDSGizmoModel>(CurrentModel))
	{
		CurrentModel = GizmoModel->GetTargetModel();
	}
	
	if (CurrentModel == nullptr || CurrentModel->GetModelType() != EDSModelType::E_MultiSelect)
	{
		ResetAllState();
	}

	//
	if (GetState() != nullptr)
	{
		GetState()->ExecuteAfterRevoke();
	}
}

//void UDSMVCSubsystem::OnExecuteFromFunctionBarOrPropertyWidget(UDSBaseModel* EditModel, const EActionCommandType& InCommandType, const TSharedPtr<FDSBaseProperty>& ChangePropertyPtr)
//{
//	if (DS_MODEL_VALID_FOR_USE(EditModel) && EditModel->GetPropertySharedPtr().IsValid())
//	{
//		if (ConsiderAsHouseType.Contains(EditModel->GetModelType()))
//		{
//			UDSRevokeSubsystem::GetInstance()->PushCommand_House(
//				{},
//				InCommandType,
//				GetRevokeMark());
//		}
//		else if (UDSToolLibrary::IsMultiGroupOperatorForRevoke(EditModel))
//		{
//			UDSRevokeSubsystem::GetInstance()->PushCommand_Multi(
//				EditModel,
//				InCommandType,
//				GetRevokeMark());
//		}
//		else if (UDSToolLibrary::IsCustomType(EditModel))
//		{
//			UDSRevokeSubsystem::GetInstance()->PushCommand_Custom(
//				EditModel,
//				InCommandType,
//				nullptr,
//				GetRevokeMark());
//		}
//		else
//		{
//			UDSRevokeSubsystem::GetInstance()->PushCommand_Single(
//				EditModel,
//				InCommandType,
//				GetRevokeMark());
//		}
//
//		if (ChangePropertyPtr.IsValid())
//		{
//			//需要修改属性
//			UDSToolLibrary::ParsePropertyCopy(EditModel, ChangePropertyPtr);
//		}
//
//		if (InCommandType == EActionCommandType::E_Delete)
//		{
//			auto ModelType = EditModel->GetModelType();
//			if (ModelType == EDSModelType::E_House_Wall
//				|| ModelType == EDSModelType::E_House_Beam
//				|| ModelType == EDSModelType::E_House_Platform)
//			{
//				SetCurrentModel(nullptr);
//
//				TArray<UDSMoldingCeilingModel*> MoldingCeilings;
//				//if (EditModel->GetModelType() == EDSModelType::E_House_Wall)
//				//{
//				//	auto Outline = EditModel->GetWorldTopOutline();
//
//				//	auto ModelCeilings = GetModels(EDSModelType::E_Furniture_MoldingCeiling);
//				//	for (auto& C : ModelCeilings)
//				//	{
//				//		auto CModel = Cast<UDSMoldingCeilingModel>(C);
//				//		if (!CModel)
//				//		{
//				//			continue;
//				//		}
//				//		auto CeilingPoints = CModel->GetOutlineInfo().TopOutline;
//
//				//		if (FGeometryLibrary::DoContoursOverlap(Outline, CeilingPoints,1) && CModel)
//				//		{
//				//			MoldingCeilings.Add(Cast<UDSMoldingCeilingModel>(CModel));
//				//		}
//				//	}
//				//}
//
//				//EditModel->OnExecuteAction(InCommandType, FDSBroadcastMarkData(true));
//				OnLayoutRefresh(ModelType);
//				UDSPendantSubsystem::GetInstance()->RefreshLayoutRuler(true);
//				if (!MoldingCeilings.IsEmpty())
//				{
//					for (auto & Ceiling : MoldingCeilings)
//					{
//						UDSCeilingLibrary::CeilingSelfAdaption(Ceiling, true);
//					}
//				}
//				ResetAllState();
//			}
//			else if (EditModel->GetModelType() == EDSModelType::E_House_Area_Split_Line)
//			{
//				UDSUISubsystem::GetInstance()->PresentModalDialog(
//					TEXT("删除分割线后，将重新生成房间和地面分割线，确认删除分割线？"),
//					FSimpleDelegate::CreateLambda([this]()
//						{
//							auto CurrentModel = GetCurrentModel();
//							if (CurrentModel->GetModelType() == EDSModelType::E_House_Area_Split_Line)
//							{
//								CurrentModel->OnExecuteAction(FDSModelExecuteType::ExecuteDelete);
//								SetCurrentModel(nullptr);
//								OnLayoutRefresh(EDSModelType::E_House_Area_Split_Line);
//								UDSPendantSubsystem::GetInstance()->RefreshLayoutRuler(true);
//								ResetAllState();
//							}
//						}),
//					FSimpleDelegate::CreateLambda(
//						[this]()
//						{
//							UDSUISubsystem::GetInstance()->RemoveModalDialog();
//							UDSRevokeSubsystem::GetInstance()->PopUndoCommandWithNoExecute(GetRevokeMark());
//						}
//					),
//					true,
//					TEXT("警告"),
//					ETextJustify::Type::Left);
//			}
//			else if (IsCustomCupboardType(EditModel->GetModelType()))
//			{
//				UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(EditModel);
//				UDSCupboardModel* ParentModel = Cast<UDSCupboardModel>(CupboardModel->GetOwnerModel());
//				if (ParentModel != nullptr)
//				{
//					TArray<UDSBaseModel*> ModelsForUpdate = CupboardModel->RemoveSelfFromDataTree();
//
//					for (UDSBaseModel* ModelForUpdate : ModelsForUpdate)
//					{
//						ModelForUpdate->OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf);
//					}
//				}
//
//				SetCurrentModel(nullptr);
//				//EditModel->OnExecuteAction(InCommandType, FDSBroadcastMarkData(true));
//				UDSPendantSubsystem::GetInstance()->RefreshLayoutRuler(true);
//				ResetAllState();
//			}
//			else
//			{
//				SetCurrentModel(nullptr);
//				//EditModel->OnExecuteAction(InCommandType, FDSBroadcastMarkData(true));
//				UDSPendantSubsystem::GetInstance()->RefreshLayoutRuler(true);
//				ResetAllState();
//			}
//		}
//		else
//		{
//			//EditModel->OnExecuteAction(InCommandType, FDSBroadcastMarkData(true));
//		}
//	}
//	else
//	{
//		if (InCommandType == EActionCommandType::E_FlipX)
//		{
//			FlipLayout(true);
//		}
//		else if (InCommandType == EActionCommandType::E_FlipY)
//		{
//			FlipLayout(false);
//		}
//	}
//}

//void UDSMVCSubsystem::OnExecuteFromFunctionBarOrPropertyWidget_Custom(UDSBaseModel* EditModel, const EActionCommandType& InCommandType, const TSharedPtr<FMultiComponentDataItem>& ChangePropertyPtr)
//{
//	if (DS_MODEL_VALID_FOR_USE(EditModel) && Cast<UDSCupboardModel>(EditModel))
//	{
//		if(EditModel->GetModelType() == EDSModelType::E_Generated_CounterTop
//			|| EditModel->GetModelType() == EDSModelType::E_Generated_CounterTop_Point
//			|| EditModel->GetModelType() == EDSModelType::E_Generated_CounterTop_Line
//			|| EditModel->GetModelType() == EDSModelType::E_Generated_SideCounterTop)
//		{//CT 、SideCT -- FunctionBar Property 
//			
//		}
//		else
//		{
//			//door
//			TSharedPtr<FDSCustomComponentRevokeData> NewRevokeData = MakeShared<FDSCustomComponentRevokeData>();
//			NewRevokeData->SetComponentData(ChangePropertyPtr, true);
//			NewRevokeData->SetComponentCommandType(EDSRevokeComponentType::E_Component_Update);
//			NewRevokeData->SetNodePath(TEXT(""));
//			UDSRevokeSubsystem::GetInstance()->PushCommand_Custom(
//					EditModel,
//					InCommandType,
//					NewRevokeData,
//					GetRevokeMark());
//
//			EditModel->OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf);
//		}
//	}
//}

//void UDSMVCSubsystem::OnExecuteFromFunctionBarOrPropertyWidget_CT(UDSBaseModel* EditModel, const EActionCommandType& InCommandType, const FDSCounterTopPushData& InPushData, FString InCommandUUID, bool NeedExecuteAction)
//{
//	if (IsValid(EditModel))
//	{
//		if(InCommandUUID.IsEmpty())
//		{
//			InCommandUUID = FGuid::NewGuid().ToString();	
//		}
//		
//		EDSModelType ModelType = EditModel->GetModelType();
//		if(ModelType == EDSModelType::E_Generated_CounterTop || ModelType == EDSModelType::E_Generated_SideCounterTop)
//		{
//			FDSCounterTopPushData OldPushData(InPushData.GetRevokeType());
//			UDSRevokeLibrary::UpdatePushDataProperty(EditModel, OldPushData);
//			UDSRevokeSubsystem::GetInstance()->InsertCommandData(EditModel, InCommandType, GetRevokeMark(), OldPushData, InCommandUUID);
//
//			if(InPushData.GetRevokeType() != EDSCTRevokeType::E_Delete)
//			{
//				if (ModelType == EDSModelType::E_Generated_CounterTop)
//				{
//					UDSToolLibrary::ParsePropertyCopy(EditModel, InPushData.GetCTProperty());
//				}
//				else if(ModelType == EDSModelType::E_Generated_SideCounterTop)
//				{
//					UDSToolLibrary::ParsePropertyCopy(EditModel, InPushData.GetSideCTProperty());
//				}
//			}
//			if (NeedExecuteAction)
//			{
//				if (Cast<UDSCounterTopBaseModel>(EditModel))
//				{
//					Cast<UDSCounterTopBaseModel>(EditModel)->bShouldGenerateByProperties = InPushData.GetNeedGenerateByProperty();
//				}
//				//EditModel->OnExecuteAction(InCommandType);
//				if (InPushData.GetRevokeType() == EDSCTRevokeType::E_ReGenerateLinePoint)
//				{
//					UDSCounterTopLibrary::RegenerateCounterTopEditPointsAndLines(EditModel);
//				}
//			}
//		}
//		else if(ModelType == EDSModelType::E_Generated_CounterTop_Line || ModelType == EDSModelType::E_Generated_CounterTop_Point)
//		{
//			TSet<TWeakObjectPtr<UDSBaseModel>> LinkModels = EditModel->GetLinkModels();
//
//			if (InPushData.GetRevokeType() != EDSCTRevokeType::E_Delete)
//			{
//				//line point push data
//				FDSCounterTopPushData OldPushData(InPushData.GetRevokeType());
//				UDSRevokeLibrary::UpdatePushDataProperty(EditModel, OldPushData);
//				UDSRevokeSubsystem::GetInstance()->InsertCommandData(EditModel, InCommandType, GetRevokeMark(), OldPushData, InCommandUUID);
//
//				//push link model data
//				FDSCounterTopPushData PushData;
//				PushData.CT_RevokeType = EDSCTRevokeType::E_Update;
//				for (auto& LM : LinkModels)
//				{
//					if (IsValid(LM.Get()))
//					{
//						UDSRevokeSubsystem::GetInstance()->InsertCommandData(LM.Get(), InCommandType, GetRevokeMark(), PushData, InCommandUUID);
//					}
//				}
//
//				//apply new data to edit model
//				if (ModelType == EDSModelType::E_Generated_CounterTop_Line)
//				{
//					UDSToolLibrary::ParsePropertyCopy(EditModel, InPushData.GetCTLineProperty());
//				}
//				else if (ModelType == EDSModelType::E_Generated_CounterTop_Point)
//				{
//					UDSToolLibrary::ParsePropertyCopy(EditModel, InPushData.GetCTPointProperty());
//				}
//				if (NeedExecuteAction)
//				{
//					//EditModel->OnExecuteAction(UDSRevokeLibrary::GetOppositeCommandTypeFromCT(InPushData.GetRevokeType()));
//				}
//			}
//			else
//			{//if delete, model refresh data and regenerate point line
//
//				//temp save this command uuid
//				UDSRevokeSubsystem::GetInstance()->SetPrePushCommandUUID(InCommandUUID);
//				for (auto& LM : LinkModels)
//				{
//					if (IsValid(LM.Get()))
//					{
//						FDSCounterTopPushData PushData(EDSCTRevokeType::E_ReGenerateLinePoint);
//						UDSRevokeLibrary::UpdatePushDataProperty(LM.Get(), PushData);
//						UDSRevokeSubsystem::GetInstance()->InsertCommandData(LM.Get(), InCommandType, GetRevokeMark(), PushData, InCommandUUID);
//					}
//				}
//
//				if (NeedExecuteAction)
//				{
//					//EditModel->OnExecuteAction(UDSRevokeLibrary::GetOppositeCommandTypeFromCT(InPushData.GetRevokeType()));
//				}
//			}
//		}
//
//	}
//}

void UDSMVCSubsystem::OnExecuteFromFunctionBarOrPropertyWidgetUnion(
	UDSBaseModel* EditModel,
	const FDSModelExecuteType& InExecuteType,
	const FDSRevokePushData& InPushData,
	const FString& InCommandUUID,
	bool NeedExecuteAction)
{
    if (InPushData.bIsSnapshot)
    {
    	UDSRevokeSubsystem::GetInstance()->InsertCommandDataUnion(EditModel, InExecuteType, InPushData, GetRevokeMark(), InCommandUUID);
		if (NeedExecuteAction)
		{
			ExecuteAfterSnapShot(InPushData);
		}
    }
    else
    {
    	if (!IsValid(EditModel))
    	{		
    		return;
    	}

    	UE_LOG(DSMVCSubsystemLog, Log, TEXT("OnExecuteFromFunctionBarOrPropertyWidgetUnion --- Name[%s], Execute[%s], NeedExecute[%d]"), *EditModel->GetName(), *InExecuteType.ToString(), NeedExecuteAction);
    	
    	FDSRevokePushData OldPushData(InPushData.DataType, InExecuteType);
    	UDSRevokeLibrary::UpdatePushDataPropertyUnion(EditModel, InExecuteType, InPushData, OldPushData);
    	UDSRevokeSubsystem::GetInstance()->InsertCommandDataUnion(EditModel, InExecuteType, OldPushData, GetRevokeMark(), InCommandUUID);

     	if (NeedExecuteAction)
    	{
    		ExecuteAfterInsertData(EditModel, InExecuteType, InPushData);	
    	}
    }
}

void UDSMVCSubsystem::ExecuteAfterSnapShot(const FDSRevokePushData& InPushData)
{
	if(InPushData.DataType == EDSPushDataType::E_Home)
	{//户型
		UDSBaseModel* EditModel = InPushData.RoomData.EditModel;
		TSharedPtr<FDSBaseProperty> ChangePropertyPtr = InPushData.GetProperty();
		FDSModelExecuteType ExecuteType = InPushData.RoomData.ExecuteType;
		if(ExecuteType == FDSModelExecuteType::ExecuteFlipX || ExecuteType == FDSModelExecuteType::ExecuteFlipY)
		{//户型翻转
			FlipLayout(ExecuteType == FDSModelExecuteType::ExecuteFlipX);
		}
		else
		{
			if(EditModel != nullptr)
			{
				//需要修改属性
				if (!ExecuteType.IsDeleteExecute() && ChangePropertyPtr.IsValid())
				{
					UDSToolLibrary::ParsePropertyCopy(EditModel, ChangePropertyPtr);
				}

				if(ExecuteType == FDSModelExecuteType::ExecuteDelete)
				{
					EDSModelType ModelType = EditModel->GetModelType();
					if (ModelType == EDSModelType::E_House_Wall || ModelType == EDSModelType::E_House_Beam || ModelType == EDSModelType::E_House_Platform)
					{
						EditModel->OnExecuteAction(FDSModelExecuteType::ExecuteDelete, FDSBroadcastMarkData::BroadcastToMVCMark);

						TArray<UDSMoldingCeilingModel*> MoldingCeilings;
						
						UDSPendantSubsystem::GetInstance()->RefreshLayoutRuler(true);
						if (!MoldingCeilings.IsEmpty())
						{
							for (auto & Ceiling : MoldingCeilings)
							{
								UDSCeilingLibrary::CeilingSelfAdaption(Ceiling, true);
							}
						}

						OnLayoutRefresh(ModelType);
						UDSPendantSubsystem::GetInstance()->RefreshLayoutRuler(true);
						ResetAllState();
					}
					else if (EditModel->GetModelType() == EDSModelType::E_House_Area_Split_Line)
					{
						EditModel->OnExecuteAction(FDSModelExecuteType::ExecuteDelete);
						OnLayoutRefresh(EDSModelType::E_House_Area_Split_Line);
						UDSPendantSubsystem::GetInstance()->RefreshLayoutRuler(true);
						ResetAllState();
					}
					else if (EditModel->GetModelType() == EDSModelType::E_House_Area)
					{
						EditModel->OnExecuteAction(FDSModelExecuteType::ExecuteDelete);
						OnLayoutRefresh(EDSModelType::E_House_Wall);
						UDSPendantSubsystem::GetInstance()->RefreshLayoutRuler(true);
						ResetAllState();
					}
					else
					{
						EditModel->OnExecuteAction(FDSModelExecuteType::ExecuteDelete, FDSBroadcastMarkData::OnlyOutlineBroadcastMark);
						UDSPendantSubsystem::GetInstance()->RefreshLayoutRuler(true);
						ResetAllState();
					}
				}
				else
				{
					EditModel->OnExecuteAction(ExecuteType, FDSBroadcastMarkData::SelectBroadcastMark);
				}
			}
		}
	}
}

void UDSMVCSubsystem::ExecuteAfterInsertData(UDSBaseModel* EditModel, const FDSModelExecuteType& InExecuteType, const FDSRevokePushData& InPushData)
{
	if(EditModel == nullptr) return;
	
	if(InPushData.DataType == EDSPushDataType::E_CounterTop)
	{
		EDSModelType ModelType = EditModel->GetModelType();
		if(ModelType == EDSModelType::E_Generated_CounterTop || ModelType == EDSModelType::E_Generated_SideCounterTop)
		{
			if(InPushData.CounterTopData.GetRevokeType() != EDSCTRevokeType::E_Delete)
			{
				if (ModelType == EDSModelType::E_Generated_CounterTop)
				{
					UDSToolLibrary::ParsePropertyCopy(EditModel, InPushData.CounterTopData.GetCTProperty());
				}
				else if(ModelType == EDSModelType::E_Generated_SideCounterTop)
				{
					UDSToolLibrary::ParsePropertyCopy(EditModel, InPushData.CounterTopData.GetSideCTProperty());
				}
			}
			if (Cast<UDSCounterTopBaseModel>(EditModel))
			{
				Cast<UDSCounterTopBaseModel>(EditModel)->bShouldGenerateByProperties = InPushData.CounterTopData.GetNeedGenerateByProperty();
			}
			EditModel->OnExecuteAction(InExecuteType);
			if (InPushData.CounterTopData.GetRevokeType() == EDSCTRevokeType::E_ReGenerateLinePoint)
			{
				UDSCounterTopLibrary::RegenerateCounterTopEditPointsAndLines(EditModel);
			}
		}
		else if(ModelType == EDSModelType::E_Generated_CounterTop_Line || ModelType == EDSModelType::E_Generated_CounterTop_Point)
		{
			TSet<TWeakObjectPtr<UDSBaseModel>> LinkModels = EditModel->GetLinkModels();

			if (InPushData.CounterTopData.GetRevokeType() != EDSCTRevokeType::E_Delete)
			{
				//apply new data to edit model
				if (ModelType == EDSModelType::E_Generated_CounterTop_Line)
				{
					UDSToolLibrary::ParsePropertyCopy(EditModel, InPushData.CounterTopData.GetCTLineProperty());
				}
				else if (ModelType == EDSModelType::E_Generated_CounterTop_Point)
				{
					UDSToolLibrary::ParsePropertyCopy(EditModel, InPushData.CounterTopData.GetCTPointProperty());
				}
				EditModel->OnExecuteAction(UDSRevokeLibrary::GetOppositeExecuteTypeFromCT(InPushData.CounterTopData.GetRevokeType()));
			}
			else
			{//if delete, model refresh data and regenerate point line

				EditModel->OnExecuteAction(UDSRevokeLibrary::GetOppositeExecuteTypeFromCT(InPushData.CounterTopData.GetRevokeType()));
			}
		}
	}
	else if (UDSToolLibrary::IsCustomType(EditModel))
	{

		if (InExecuteType == FDSModelExecuteType::ExecuteDelete)
		{
			if (UDSCupboardLibrary::IsFunctionalCupboardModel(EditModel))
			{
				UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(EditModel);
				UDSCupboardModel* RootModel = Cast<UDSCupboardModel>(CupboardModel->GetRootCupboardModel());
				RootModel->GetSubFunctionalNodeDependencyMap()->RemoveDependencyInfo(CupboardModel->GetComponentTreeDataRef()->UUID);
			}
			UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(EditModel);
			UDSCupboardModel* ParentModel = Cast<UDSCupboardModel>(CupboardModel->GetOwnerModel());
			if (ParentModel != nullptr)
			{
				TArray<UDSBaseModel*> ModelsForUpdate = CupboardModel->RemoveSelfFromDataTree();
				if (CupboardModel->GetModelType() == EDSModelType::E_Custom_FunctionalDrawer)
				{
					ModelsForUpdate.Empty();
				}
				for (UDSBaseModel* ModelForUpdate : ModelsForUpdate)
				{
					ModelForUpdate->OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf, FDSBroadcastMarkData::OnlyOutlineBroadcastMark);
				}
			}

		}
		else if(InExecuteType == FDSModelExecuteType::ExecuteHidden)
		{
			UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(EditModel);
			CupboardModel->GetComponentTreeDataRef()->bHiden = true;
		}
		else if (InExecuteType == FDSModelExecuteType::ExecuteUnHidden)
		{
			UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(EditModel);
			CupboardModel->GetComponentTreeDataRef()->bHiden = false;

		}
		//删除时需要刷新挂件
		EditModel->OnExecuteAction(InExecuteType/*, FDSBroadcastMarkData::OnlyOutlineBroadcastMark*/);
	}
	else if (EditModel->GetModelType() == EDSModelType::E_MultiSelect)
	{
		if (InExecuteType == FDSModelExecuteType::ExecuteHidden)
		{
			UDSMultiModel* MM = Cast<UDSMultiModel>(EditModel);
			if (MM->HasTypes({ EDSModelType::E_Custom_LayoutDoor_Board }))
			{
				TArray<UDSBaseModel*> AllModels = MM->GetIncludeModel();
				for (auto& ModelIte : AllModels)
				{
					if (ModelIte->GetModelType() == EDSModelType::E_Custom_LayoutDoor_Board && UDSWallBoardLibrary::IsRealWallBoard(ModelIte))
					{
						UDSWallBoardLibrary::HideRealBoard(ModelIte);
					}
					else
					{
						EditModel->OnExecuteAction(InExecuteType);
					}
				}

				return;
			}
			for (auto IncludeModel : MM->GetIncludeModel())
			{
				if (UDSToolLibrary::IsCustomCupboardType(IncludeModel->GetModelType()))
				{
					if (InExecuteType == FDSModelExecuteType::ExecuteHidden)
					{
						UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(IncludeModel);
						CupboardModel->GetComponentTreeDataRef()->bHiden = true;

						CupboardModel->OnExecuteAction(InExecuteType, FDSBroadcastMarkData::OnlyOutlineBroadcastMark);
					}
				}
			}
		}
		else if (InExecuteType == FDSModelExecuteType::ExecuteUnHidden)
		{
			UDSMultiModel* MM = Cast<UDSMultiModel>(EditModel);
			if (MM->HasTypes({ EDSModelType::E_Custom_LayoutDoor_Board }))
			{
				TArray<UDSBaseModel*> AllModels = MM->GetIncludeModel();
				for (auto& ModelIte : AllModels)
				{
					if (ModelIte->GetModelType() == EDSModelType::E_Custom_LayoutDoor_Board && UDSWallBoardLibrary::IsRealWallBoard(ModelIte))
					{
						UDSWallBoardLibrary::HideRealBoard(ModelIte);
					}
					else
					{
						EditModel->OnExecuteAction(InExecuteType);
					}
				}

				return;
			}
			for (auto IncludeModel : MM->GetIncludeModel())
			{
				if (UDSToolLibrary::IsCustomCupboardType(IncludeModel->GetModelType()))
				{
					UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(IncludeModel);
					CupboardModel->GetComponentTreeDataRef()->bHiden = false;
					CupboardModel->OnExecuteAction(InExecuteType, FDSBroadcastMarkData::OnlyOutlineBroadcastMark);
				}
			}
		}
		else if (InExecuteType == FDSModelExecuteType::ExecuteDelete)
		{
			TArray<UDSBaseModel*> UpdateCupboards;
			UDSMultiModel* MM = Cast<UDSMultiModel>(EditModel);
			for (auto IncludeModel : MM->GetIncludeModel())
			{
				if (UDSToolLibrary::IsCustomCupboardType(IncludeModel->GetModelType()))
				{
					if (UDSCupboardLibrary::IsFunctionalCupboardModel(EditModel))
					{
						UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(EditModel);
						UDSCupboardModel* RootModel = Cast<UDSCupboardModel>(CupboardModel->GetRootCupboardModel());
						RootModel->GetSubFunctionalNodeDependencyMap()->RemoveDependencyInfo(CupboardModel->GetComponentTreeDataRef()->UUID);
					}

					UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(IncludeModel);
					UDSCupboardModel* ParentModel = Cast<UDSCupboardModel>(CupboardModel->GetOwnerModel());
					if (ParentModel != nullptr)
					{
						TArray<UDSBaseModel*> ModelsForUpdate  =CupboardModel->RemoveSelfFromDataTree();

						if (IncludeModel->GetModelType() == EDSModelType::E_Custom_FunctionalDrawer)
						{
							continue;
						}
						
						for (auto Iter: ModelsForUpdate)
						{
							UpdateCupboards.AddUnique(Iter);
						}
					}
				}
			}
			for (UDSBaseModel* ModelForUpdate : UpdateCupboards)
			{
				ModelForUpdate->OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf, FDSBroadcastMarkData::OnlyOutlineBroadcastMark);
			}
		}

		EditModel->OnExecuteAction(InExecuteType/*, FDSBroadcastMarkData::OnlyOutlineBroadcastMark*/);
	}
	else
	{
		 if (InExecuteType != FDSModelExecuteType::ExecuteDelete)
		{
			UDSToolLibrary::ParsePropertyCopy(EditModel, InPushData.GetProperty());
		}

		EditModel->OnExecuteAction(InExecuteType);
	}
	
}

void UDSMVCSubsystem::RecheckIsOnlyClick(UDSBaseModel* EditModel)
{
	if (DS_MODEL_VALID_FOR_USE(EditModel))
	{
		/*
		 *  @@ if left mouse only click ( press、Release at same place )
		 *  @@ remove select action
		 */
		if (ADesignStationController::Get()->IsLeftMouseOnlyClick()
			&& GetStateType() == EDSFSMState::FSM_Normal
			&& !RevokeNoRecheckType.Contains(EditModel->GetModelType()))
		{
			UDSRevokeSubsystem::GetInstance()->PopUndoCommandWithNoExecute(GetRevokeMark());
		}
	}
}

void UDSMVCSubsystem::OnRefreshUIPropertyWidget(UDSBaseModel* EditModel)
{
	UDSUISubsystem::GetInstance()->ProcessStateEvent(EditModel, EUIOperationType::Selected, GetStateType());
}

bool UDSMVCSubsystem::IsInitialized()
{
	return Instance != nullptr;
}

UDSMVCSubsystem* UDSMVCSubsystem::GetInstance()
{
	return Instance;
}

TSharedPtr<FUICommandList> UDSMVCSubsystem::GetGenericCommandList() const
{
	return GenericCommandList;
}

bool UDSMVCSubsystem::ProcessCommandEvent(const FKeyEvent& InKeyEvent)
{
	if (GenericCommandList->ProcessCommandBindings(InKeyEvent))
	{
		return true;
	}

	UDSFiniteState* FiniteState = GetState();
	if (FiniteState == nullptr)
	{
		return false;
	}

	TSharedPtr<FUICommandList> StateCommandList = FiniteState->GetCommandList();
	if (!StateCommandList)
	{
		return false;
	}

	return StateCommandList->ProcessCommandBindings(InKeyEvent);
}

void UDSMVCSubsystem::RegisterInGameCommands()
{
	FDSGenericCommands::Register();
	FDSCustomFloatingToolBarCommands::Register();
}

void UDSMVCSubsystem::UnregisterInGameCommands()
{
	FDSGenericCommands::Unregister();
	FDSCustomFloatingToolBarCommands::Unregister();
}

void UDSMVCSubsystem::BindGenericInputCommands()
{
	if (!GenericCommandList)
	{
		return;
	}

	// Undo
	GenericCommandList->MapAction(FDSGenericCommands::Get().Undo, FExecuteAction::CreateUObject(this, &ThisClass::OnExecuteGenericInputCommand_Undo), FCanExecuteAction::CreateUObject(this, &ThisClass::CanExecuteGenericInputCommand_Undo));

	// Redo
	GenericCommandList->MapAction(FDSGenericCommands::Get().Redo, FExecuteAction::CreateUObject(this, &ThisClass::OnExecuteGenericInputCommand_Redo), FCanExecuteAction::CreateUObject(this, &ThisClass::CanExecuteGenericInputCommand_Redo));

	// HideModel
	GenericCommandList->MapAction(FDSGenericCommands::Get().HideModel, FExecuteAction::CreateUObject(this, &ThisClass::OnExecuteGenericInputCommand_HideModel), FCanExecuteAction::CreateUObject(this, &ThisClass::CanExecuteGenericInputCommand_HideModel));

	// CopyModel
	GenericCommandList->MapAction(FDSGenericCommands::Get().CopyModel, FExecuteAction::CreateUObject(this, &ThisClass::OnExecuteGenericInputCommand_CopyModel), FCanExecuteAction::CreateUObject(this, &ThisClass::CanExecuteGenericInputCommand_CopyModel));

	// CreateGroup
	GenericCommandList->MapAction(FDSGenericCommands::Get().CreateGroup, FExecuteAction::CreateUObject(this, &UDSMVCSubsystem::OnExecuteGenericInputCommand_CreateGroup), FCanExecuteAction::CreateUObject(this, &ThisClass::CanExecuteGenericInputCommand_CreateGroup));

	// BreakGroup
	GenericCommandList->MapAction(FDSGenericCommands::Get().BreakGroup, FExecuteAction::CreateUObject(this, &ThisClass::OnExecuteGenericInputCommand_BreakGroup), FCanExecuteAction::CreateUObject(this, &ThisClass::CanExecuteGenericInputCommand_BreakGroup));

	// SaveCameraView
	GenericCommandList->MapAction(FDSGenericCommands::Get().SaveCameraView, FExecuteAction::CreateUObject(this, &ThisClass::OnExecuteGenericInputCommand_SaveCameraView), FCanExecuteAction::CreateUObject(this, &ThisClass::CanExecuteGenericInputCommand_SaveCameraView));

	// ResetCamera
	GenericCommandList->MapAction(FDSGenericCommands::Get().ResetCamera, FExecuteAction::CreateUObject(this, &ThisClass::OnExecuteGenericInputCommand_ResetCamera), FCanExecuteAction::CreateUObject(this, &ThisClass::CanExecuteGenericInputCommand_ResetCamera));
}

bool UDSMVCSubsystem::CanExecuteGenericInputCommand_Undo()
{
	return GetStateType() != EDSFSMState::FSM_Rendering && GetStateType() != EDSFSMState::FSM_Line && GetStateType() != EDSFSMState::FSM_HandleFree;
}

bool UDSMVCSubsystem::CanExecuteGenericInputCommand_Redo()
{
	return GetStateType() != EDSFSMState::FSM_Rendering && GetStateType() != EDSFSMState::FSM_Line && GetStateType() != EDSFSMState::FSM_HandleFree;
}

bool UDSMVCSubsystem::CanExecuteGenericInputCommand_HideModel()
{
	return GetCurrentModel() != nullptr && GetStateType() != EDSFSMState::FSM_CounterTop && GetStateType() != EDSFSMState::FSM_Rendering;;
}

bool UDSMVCSubsystem::CanExecuteGenericInputCommand_CopyModel()
{
	return GetStateType() != EDSFSMState::FSM_Rendering;
}

bool UDSMVCSubsystem::CanExecuteGenericInputCommand_CreateGroup()
{
	return GetCurrentModel() != nullptr && UDSToolLibrary::IsMultiOperator(GetCurrentModel()) && GetCurrentModel()->IsCanGroup() && GetStateType() != EDSFSMState::FSM_CounterTop;
}

bool UDSMVCSubsystem::CanExecuteGenericInputCommand_BreakGroup()
{
	return UDSToolLibrary::IsGroupType(GetCurrentModel()) && GetStateType() != EDSFSMState::FSM_CounterTop;
}

bool UDSMVCSubsystem::CanExecuteGenericInputCommand_SaveCameraView()
{
	return GetStateType() != EDSFSMState::FSM_Rendering;
}

bool UDSMVCSubsystem::CanExecuteGenericInputCommand_ResetCamera()
{
	return GetStateType() != EDSFSMState::FSM_Rendering;
}

void UDSMVCSubsystem::OnExecuteGenericInputCommand_Undo()
{
	OnRevokeExecute(true);
}

void UDSMVCSubsystem::OnExecuteGenericInputCommand_Redo()
{
	OnRevokeExecute(false);
}

void UDSMVCSubsystem::OnExecuteGenericInputCommand_HideModel()
{
	UDSSelectOperateSubsystem::GetInstance()->Hide(GetCurrentModel());
}

void UDSMVCSubsystem::OnExecuteGenericInputCommand_CopyModel()
{
	if (GetStateType() != EDSFSMState::FSM_CounterTop)
	{
		UDSFiniteState* FiniteState = GetState();
		if (FiniteState == nullptr)
		{
			return;
		}
		
		if (FiniteState->CanExecuteGenericInputCommand_Copy())
		{
			FiniteState->OnExecuteGenericInputCommand_Copy();
		}
	}
}

void UDSMVCSubsystem::OnExecuteGenericInputCommand_CreateGroup()
{
	UDSGroupModel* Group = ConvertMultiToGroup(GetCurrentModel());
	if (Group != nullptr)
	{
		SetCurrentModel(Group);
		
	}
}

void UDSMVCSubsystem::OnExecuteGenericInputCommand_BreakGroup()
{
	ConvertGroupToMulti(GetCurrentModel());
}

void UDSMVCSubsystem::OnExecuteGenericInputCommand_SaveCameraView()
{
	UDSCameraSubsystem::GetInstance()->SaveCurrentInfo();
}

void UDSMVCSubsystem::OnExecuteGenericInputCommand_ResetCamera()
{
	
}

void UDSMVCSubsystem::SetGlobalStyle(const FApplyStyleData& InNewStyle)
{
	GlobalStyleData = InNewStyle;
	// TODO: Should we need to broadcast a event of style changed?
}

const TMap<FString, FParameterData>& UDSMVCSubsystem::GetGlobalParamsMap()
{
	if (GlobalParams.IsEmpty())
	{
		FRefParamData RefParamData;
		if (URefRelationFunction::GetGlobalParamsRefRelationFromFile(RefParamData))
		{
			GlobalParams = RefParamData.GenerateParamsMap();
		}
		else
		{
			UE_LOG(DSMVCSubsystemLog, Error, TEXT("Load Param Ref File Error!"));
		}
	}

	return GlobalParams;
}

TArray<FParameterData> UDSMVCSubsystem::GetGlobalParamsArr()
{
	TArray<FParameterData> Result;
	TMap<FString, FParameterData> ResultMap = GetGlobalParamsMap();
	ResultMap.GenerateValueArray(Result);
	return Result;
}

TMap<FString, FParameterData> UDSMVCSubsystem::GetGlobalStyleParamsMap() const
{
	return TMap<FString, FParameterData>();
}

TArray<FParameterData> UDSMVCSubsystem::GetGlobalStyleParamsArr() const
{
	return TArray<FParameterData>();
}

const FRefToStyleFile& UDSMVCSubsystem::GetOriginalStyleData()
{
	if (OriginalStyleData.style_datas.IsEmpty() && OriginalStyleData.content_datas.IsEmpty())
	{
		FProtobufOperatorFunctionLibrary::LoadRelationFromFile(URefToStyleDataLibrary::GetStyleFileAddress(), OriginalStyleData);
	}

	return OriginalStyleData;
}

const FApplyStyleData& UDSMVCSubsystem::GetGlobalStyleData()
{
	return GlobalStyleData;
}

bool UDSMVCSubsystem::HasGlobalStyle() const
{
	return GlobalStyleData.IsValid();
}

void UDSMVCSubsystem::SwitchMultiGroup(UDSBaseModel* EditModel)
{
	if (UDSToolLibrary::IsMultiOperator(EditModel))
	{
		ConvertMultiToGroup(EditModel);
	}
	else if (UDSToolLibrary::IsGroupOperator(EditModel))
	{
		ConvertGroupToMulti(EditModel);
	}
}

UDSGroupModel* UDSMVCSubsystem::ConvertMultiToGroup(UDSBaseModel* EditModel)
{
	//multi select combine to group
	if (UDSToolLibrary::IsMultiOperator(EditModel))
	{
		bool CanGroup = true;
		FString WarningMsg = TEXT("");
		UDSMultiModel* MultiModel = Cast<UDSMultiModel>(EditModel);
		if (MultiModel != nullptr)
		{
			if (MultiModel->HasTypes({ EDSModelType::E_Custom_Sink }))
			{
				CanGroup = false;
				WarningMsg = TEXT("水槽不能组合");
			}
		}

		if (CanGroup)
		{
			FDSRevokePushData PushData(EDSPushDataType::E_Multi_Group, FDSModelExecuteType::ExecuteGroupCombine);
			UDSRevokeLibrary::UpdatePushDataPropertyUnion(EditModel, FDSModelExecuteType::ExecuteGroupCombine, {}, PushData);
			UDSRevokeSubsystem::GetInstance()->InsertCommandDataUnion(
				EditModel, FDSModelExecuteType::ExecuteGroupCombine,
				PushData, GetRevokeMark(), TEXT(""));

			UDSGroupModel* NewGroup = UDSGroupModel::CreateGroupModel();
			NewGroup->AddItem(Cast<UDSMultiModel>(EditModel)->GetIncludeModel());
			Cast<UDSMultiModel>(EditModel)->ReleaseSelect();
			NewGroup->CombineGroupBroadcast();
			this->SetCurrentModel(NewGroup);

			EditModel->OnExecuteAction(FDSModelExecuteType::ExecuteDeleteSelf, FDSBroadcastMarkData::NotBroadcastToMVCMark);

			UDSUISubsystem::GetInstance()->ProcessStateEvent(NewGroup, EUIOperationType::Selected, GetStateType());

			return NewGroup;
		}
		else
		{
			UDSUISubsystem::GetInstance()->SetToastState(EToastState::EWarning, WarningMsg);
		}
	}

	return nullptr;
}

void UDSMVCSubsystem::ConvertGroupToMulti(UDSBaseModel* EditModel)
{
	//group release to multi
	if (UDSToolLibrary::IsGroupOperator(EditModel))
	{
		FDSRevokePushData PushData(EDSPushDataType::E_Multi_Group, FDSModelExecuteType::ExecuteGroupRelease);
        UDSRevokeLibrary::UpdatePushDataPropertyUnion(EditModel, FDSModelExecuteType::ExecuteGroupRelease, {}, PushData);
		UDSRevokeSubsystem::GetInstance()->InsertCommandDataUnion(
			EditModel, FDSModelExecuteType::ExecuteGroupRelease,
			PushData, GetRevokeMark(), TEXT(""));

		UDSMultiModel* NewMulti = UDSMultiModel::CreateMultiModel();
		TArray<UDSBaseModel*> IncludeModels = Cast<UDSGroupModel>(EditModel)->ReleaseGroupItem();
		NewMulti->AddSelect(IncludeModels);
		this->SetCurrentModel(NewMulti);

		EditModel->OnExecuteAction(FDSModelExecuteType::ExecuteGroupRelease, FDSBroadcastMarkData::OnlyOutlineBroadcastMark);
		//NewMulti->OnExecuteAction(FDSModelExecuteType::ExecuteUnSelect);
		SetCurrentModel(nullptr);
		UDSUISubsystem::GetInstance()->ProcessStateEvent(nullptr, EUIOperationType::Selected, GetStateType());
	}
}

ADSBaseView* UDSMVCSubsystem::SpawnViewUnion(UDSBaseModel* ToGenerateModel, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr, bool bShouldSelect)
{
	ADSBaseView* View = nullptr;
	if (OBJECT_VALID_FOR_USE(ResourceData))
	{
		View = ResourceData->SpawnDisplayModelReturnView(ToGenerateModel);
		ToGenerateModel->SetOwnedView(View);
		ToGenerateModel->OnExecuteAction(FDSModelExecuteType::ExecuteSpawn, BroadcastMarkPtr);
		for (auto& Cmp : ToGenerateModel->GetComponentModels())
		{
			Cmp->SetNoNewGenerate();
			const auto V = GetInstance()->SpawnViewUnion(Cmp, BroadcastMarkPtr, false);
			V->Tags.Add(FName(TEXT("Cmp")));
			V->AttachToActor(View, FAttachmentTransformRules::KeepRelativeTransform);
			V->SetOwner(View);
		}

		if(bShouldSelect)
		{
			DS_FSM->RefreshSelectFromMVC(ToGenerateModel);
		}
	}

	return View;
}

void UDSMVCSubsystem::OnGenerateAction(UDSBaseModel* GenerateModel, bool bShouldSelect)
{
	checkf(GenerateModel->IsValid(), TEXT("UDSMVCSubsystem::OnGenerateAction --- Generate New View Need Valid Data"));

	if (OBJECT_VALID_FOR_USE(ResourceData))
	{
		SpawnViewUnion(GenerateModel, FDSBroadcastMarkData::SpawnBroadcastMark, bShouldSelect);
		
		// GenerateModel->OnExecuteAction(FDSModelExecuteType::ExecuteSpawn);
		// ADSBaseView* OutView = ResourceData->SpawnDisplayModelReturnView(GenerateModel);
		// GenerateModel->SetOwnedView(OutView);
		// if (bShouldSelect)
		// {
		// 	DS_FSM->RefreshSelectFromMVC(GenerateModel);
		// }
		// OnModelExecuteCommandComplete(GenerateModel, FDSModelExecuteType::ExecuteSpawn);
	}
}

UDSBaseModel* UDSMVCSubsystem::OnCopy(UDSBaseModel* Model, const FDSMouseData& MouseDataWhenGenerate)
{
	if (ResourceData != nullptr && Model != nullptr)
	{
		UDSBaseModel* NewModel = Model->CopyModel();

		double Height = Model->GetProperty()->TransformProperty.Location.Z;

		FVector WorldLoc, WorldDir;
		ADesignStationController::Get()->DeprojectMousePositionToWorld(WorldLoc, WorldDir);

		NewModel->GetProperty()->TransformProperty.Location = FMath::RayPlaneIntersection(WorldLoc, WorldDir, FPlane(FVector(0, 0, Height), FVector::UpVector));

		SpawnViewUnion(NewModel, FDSBroadcastMarkData::SpawnBroadcastMark, true);
		InitAction_Trigger(NewModel, FPointerEvent());

		// OnGenerateView_NeedConfirm(NewModel, MouseDataWhenGenerate);
		// InitAction_Trigger(NewModel, FPointerEvent());
		// DS_FSM->RefreshSelectFromMVC(NewModel);
		
		return NewModel;
	}
	return nullptr;
}

void UDSMVCSubsystem::InitAction_Trigger(UDSBaseModel* InModel, const FPointerEvent& InMouseEvent)
{
	DS_FSM->InitAction_Trigger(InModel, InMouseEvent);
}

TArray<UDSBaseModel*> UDSMVCSubsystem::GetAllModels()
{
	return ResourceData->GetAllModels();
}

void UDSMVCSubsystem::SerializeToJsonFromAllModels(TSharedRef<TJsonWriter<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>> JsonWriter)
{
	//收集所有model数据
	ResourceData->SerializeToJsonFromAllModels(JsonWriter);
}

bool UDSMVCSubsystem::DeserializationJsonToAllModels(const TSharedPtr<FJsonObject>& InJsonData)
{
	//加文件字符串转换成Models数据
	return ResourceData->DeserializationJsonToAllModels(InJsonData);
}

TArray<UDSBaseModel*> UDSMVCSubsystem::GetModels(const EDSModelType& InType)
{
	return ResourceData->GetModels(InType);
}

TArray<UDSBaseModel*> UDSMVCSubsystem::GetModels(const TArray<EDSModelType>& InType)
{
	TArray<UDSBaseModel*> Res;
	for (const auto& IT : InType)
	{
		auto Temp = GetModels(IT);
		Res.Append(Temp);
	}
	return Res;
}

UDSBaseModel* UDSMVCSubsystem::GetModelByID(const FString& InModelID)
{
	if (InModelID.IsEmpty())
	{
		return nullptr;
	}
	TArray<UDSBaseModel*> TypeModels =GetAllModels();
	for (size_t i = 0; i < TypeModels.Num(); i++)
	{
		UDSBaseModel* TargetModel = TypeModels[i];
		if (TargetModel)
		{
			if (TargetModel->GetUUID().Equals(InModelID))
			{
				return TargetModel;
			}
		}
	}
	return nullptr;
}

TArray<UDSBaseModel*> UDSMVCSubsystem::GetAllCustomModels()
{
	TArray<EDSModelType> ModelTypes;
	for (int32 TypeId = static_cast<int32>(EDSModelType::E_Custom_UpperCabinet); TypeId < static_cast<int32>(EDSModelType::E_Custom_Furniture_Range_End); TypeId++)
	{
		ModelTypes.Add(static_cast<EDSModelType>(TypeId));
	}

	return GetModels(ModelTypes);
}

UDSBaseModel* UDSMVCSubsystem::GetModelsByTypeAndID(const EDSModelType& InType, const FString& InModelID)
{
	if (InModelID.IsEmpty())
	{
		return nullptr;
	}
	TArray<UDSBaseModel*> TypeModels = GetModels(InType);
	for (size_t i = 0; i < TypeModels.Num(); i++)
	{
		UDSBaseModel* TargetModel = TypeModels[i];
		if (TargetModel)
		{
			if (TargetModel->GetUUID().Equals(InModelID))
			{
				return TargetModel;
			}
		}
	}
	return nullptr;
}

UDSBaseModel* UDSMVCSubsystem::GetCustomModelById(const FString& InModelID, TFunction<bool(EDSModelType)> FilterFunc)
{
	for (int32 TypeId = static_cast<int32>(EDSModelType::E_Custom_UpperCabinet); TypeId < static_cast<int32>(EDSModelType::E_Custom_Furniture_Range_End); TypeId++)
	{
		if (FilterFunc && !FilterFunc(static_cast<EDSModelType>(TypeId)))
		{
			continue;
		}

		TArray<UDSBaseModel*> Models = ResourceData->GetModels(static_cast<EDSModelType>(TypeId));
		int32 ModelPos = Models.IndexOfByPredicate([&](UDSBaseModel* InModel) { return InModel->GetUUID().Equals(InModelID); });
		if (ModelPos != INDEX_NONE)
		{
			return Models[ModelPos];
		}
	}
	return nullptr;
}

UDSBaseModel* UDSMVCSubsystem::SpawnModelAndViewByType(const EDSModelType& InType, FString UUID)
{
	if (ResourceData != nullptr)
	{
		return ResourceData->SpawnModelAndViewByType(InType, UUID);
	}

	return nullptr;
}

EDSModelType UDSMVCSubsystem::GetCustomModelTypeByUUID(const FString& InModelId)
{
	for (int32 TypeId = static_cast<int32>(EDSModelType::E_Custom_UpperCabinet); TypeId < static_cast<int32>(EDSModelType::E_Custom_Furniture_Range_End); TypeId++)
	{
		TArray<UDSBaseModel*> Models = ResourceData->GetModels(static_cast<EDSModelType>(TypeId));
		int32 ModelPos = Models.IndexOfByPredicate([&](UDSBaseModel* InModel) { return InModel->GetUUID().Equals(InModelId); });
		if (ModelPos != INDEX_NONE)
		{
			return static_cast<EDSModelType>(TypeId);
		}
	}

	return EDSModelType::E_None;
}

bool UDSMVCSubsystem::ClearModelsByTypeRetIfPoolHas(const EDSModelType& InType)
{
	if(ResourceData != nullptr)
	{
		return ResourceData->ClearModelByTypeRetIfHas(InType);
	}
	return false;
}

void UDSMVCSubsystem::SwitchCameraType(bool bIs2d)
{
	//改为Model绑定回调执行
	//ResourceData->Refresh();
	DS_FSM->SwitchCameraType(bIs2d);
}

void UDSMVCSubsystem::SwitchState(const EDSFSMState& InState)
{
	if (DS_FSM)
	{
		DS_FSM->SwitchState(InState);
	}
}

void UDSMVCSubsystem::SwitchState(const EDSFSMState& InState, const int32& AdditionState1)
{
	if (DS_FSM)
	{
		DS_FSM->SwitchState(InState, AdditionState1);
	}
}

UDSFiniteState* UDSMVCSubsystem::GetPreState()
{
	return DS_FSM->GetPreState();
}

EDSFSMState UDSMVCSubsystem::GetPreStateType() const
{
	return DS_FSM->GetPreStateType();
}

UDSFiniteState* UDSMVCSubsystem::GetState()
{
	return DS_FSM->GetState();
}

UDSFiniteState* UDSMVCSubsystem::GetState(EDSFSMState InType)
{
	return DS_FSM->GetState(InType);
}

UDSFiniteStateMachine* UDSMVCSubsystem::GetStateMachine()
{
	return DS_FSM;
}

EDSFSMState UDSMVCSubsystem::GetStateType()
{
	return DS_FSM->GetStateType();
}

FDSRevokeMark UDSMVCSubsystem::GetRevokeMark() const
{
	if (DS_FSM != nullptr)
	{
		return DS_FSM->GetRevokePoolMark();
	}
	return FDSRevokeMark();
}

UDSActionExecuteBase* UDSMVCSubsystem::GetActionByModel(UDSBaseModel* InModel, const FPointerEvent& InMouseEvent, bool bShouldInit)
{
	if (DS_FSM == nullptr)
	{
		return nullptr;
	}

	UDSFiniteState* FiniteState = DS_FSM->GetState();
	if (FiniteState == nullptr)
	{
		return nullptr;
	}

	UDSActionFactoryBase* ActionFactory = FiniteState->GetStateActionFactory();
	if (ActionFactory == nullptr)
	{
		return nullptr;
	}

	return ActionFactory->GetAction(InModel, InMouseEvent, bShouldInit);
}

void UDSMVCSubsystem::SetCurrentModel(UDSBaseModel* InModel)
{
	DS_FSM->SetCurrentModel(InModel);
}

UDSBaseModel* UDSMVCSubsystem::GetCurrentModel()
{
	return DS_FSM->GetCurrentModel();
}

ADSBaseView* UDSMVCSubsystem::GetView(UDSBaseModel* InModel)
{
	return ResourceData->GetView(InModel);
}

void UDSMVCSubsystem::ShowSingleAreaModel(const FString& AreaModelID)
{
	if (AreaModelID.IsEmpty())
	{
		//TODO:显示所有
		TArray<UDSBaseModel*> AllModels = GetAllModels();
		for (auto TargetModel : AllModels)
		{
			if (TargetModel->GetIsAreaHidden())
			{
				TargetModel->OnExecuteAction(FDSModelExecuteType::ExecuteUnAreaHidden);
			}
		}
		SelectedShowingAreaModel = nullptr;
		if (OnSingleHouseAreaShowing_Handle.IsBound())
		{
			OnSingleHouseAreaShowing_Handle.Broadcast(false);
		}
	}
	else
	{
		SelectedShowingAreaModel = Cast<UDSHouseAreaModel>(GetModelsByTypeAndID(EDSModelType::E_House_Area, AreaModelID));
		if (!SelectedShowingAreaModel)
		{
			return;
		}
		TArray<UDSBaseModel*> AllModels = GetAllModels();
		TMap<UDSBaseModel*, bool> AreaWallModels = SelectedShowingAreaModel->GetWallModels();

		for (size_t i = 0; i < AllModels.Num(); i++)
		{
			auto TargetModel = AllModels[i];
			if (!TargetModel)
			{
				continue;
			}
			bool bAreaHidden = TargetModel->GetIsAreaHidden();

			if (TargetModel->GetModelType() == EDSModelType::E_House_Wall)
			{
				bool bIsAreaWall = AreaWallModels.Contains(TargetModel);

				if (!bIsAreaWall && !bAreaHidden)
				{
					TargetModel->OnExecuteAction(FDSModelExecuteType::ExecuteAreaHidden);
				}
				else if (bIsAreaWall && bAreaHidden)
				{
					TargetModel->OnExecuteAction(FDSModelExecuteType::ExecuteUnAreaHidden);
				}
			}
			else
			{
				UDSBaseModel* OwnerModel = TargetModel->GetOwnerModel();
				if (OwnerModel)
				{
					continue;
				}
				const FVector& Location = TargetModel->GetProperty()->GetTransformPropertyRef().Location;
				const FOutlineInfo& AreaOutLine = SelectedShowingAreaModel->GetOutlineInfo();
				bool bInArea = FImageProcessModule::Get()->PointInPolygon(Location, AreaOutLine.BottomOutline);
				if (!bInArea && !bAreaHidden)
				{
					TargetModel->OnExecuteAction(FDSModelExecuteType::ExecuteAreaHidden);
				}
				else if (bInArea && bAreaHidden)
				{
					TargetModel->OnExecuteAction(FDSModelExecuteType::ExecuteUnAreaHidden);
				}
			}
		}

		FBox2D AreaBox;
		AreaBox.Init();
		const FOutlineInfo& AreaOutLine = SelectedShowingAreaModel->GetOutlineInfo();
		for (const FVector& OutLinePoint : AreaOutLine.BottomOutline)
		{
			// From 3d coordinate to screen coordinate.
			FVector2D OutLinePoint2D(OutLinePoint.X, OutLinePoint.Y);
			AreaBox += OutLinePoint2D;
		}
		FVector2D BoxCenter, BoxExtents;
		AreaBox.GetCenterAndExtents(BoxCenter, BoxExtents);
		BoxExtents *= 1.5f;
		UDSCameraSubsystem::GetInstance()->UpdateCameraDataByCenterAndExtents(BoxCenter, BoxExtents);
		if (OnSingleHouseAreaShowing_Handle.IsBound())
		{
			OnSingleHouseAreaShowing_Handle.Broadcast(true);
		}
	}
}

void UDSMVCSubsystem::ResetAllState()
{
	OnModelExecuteCommandComplete(nullptr, FDSModelExecuteType::ExecuteUndo, MakeShared<FDSBroadcastMarkData>(true));

	FDSRevokeMark CurrentMark = GetRevokeMark();
	if (CurrentMark.GetStateType() == static_cast<uint8>(EDSFSMState::FSM_Rendering))
	{
		UDSUISubsystem::GetInstance()->ProcessStateEvent(
			nullptr,
			EUIOperationType::Selected,
			EDSFSMState::FSM_Rendering
		);
	}
	else if (CurrentMark.GetStateType() != static_cast<uint8>(EDSFSMState::FSM_CounterTop))
	{
		UDSUISubsystem::GetInstance()->ProcessStateEvent(nullptr, EUIOperationType::Selected, static_cast<EDSFSMState>(CurrentMark.GetStateType()));
		UDSDrawingSubsystem::GetInstance()->SetDrawingSelect(FDSVisionDataInfo());
		SetCurrentModel(nullptr);
	}
}

void UDSMVCSubsystem::OnModelExecuteCommandComplete(UDSBaseModel* InModel, const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& InMarkData)
{
	if (!InMarkData || !InMarkData->ShouldBroadcastToMVC())
	{
		return;
	}

	OnExecuteRefreshShowForAreaSelect(InModel, InExecuteType);

	if (InMarkData->ShouldRefreshPendant())
	{
		UDSPendantSubsystem::GetInstance()->RefreshPendant(InModel, InExecuteType, InMarkData);
	}

	if (InMarkData->ShouldRefreshScreenLine())
	{
		UDSDrawingSubsystem::GetInstance()->RefreshScreenLine(InModel, InExecuteType, InMarkData);
	}

	if (InMarkData->ShouldBroadcastToCamera())
	{
		UDSCameraSubsystem::GetInstance()->OnModelExecuteCommandComplete(InModel,  InExecuteType);		
	}

	if (OnModelExecuteCommandComplete_Handle.IsBound())
	{
		OnModelExecuteCommandComplete_Handle.Broadcast(InModel, InExecuteType,InMarkData);
	}
}

void UDSMVCSubsystem::OnModelStateFlagChange(UDSBaseModel* InModel, FModelStatusFlag StateFlag)
{
	UDSDrawingSubsystem::GetInstance()->HandleModelStateChange(InModel, StateFlag);
}

UDSBaseModel* UDSMVCSubsystem::GetTopModel(UDSBaseModel* InModel)
{
	if (InModel->GetOwnerModel())
	{
		return GetTopModel(InModel->GetOwnerModel());
	}
	return InModel;
}

void UDSMVCSubsystem::FlipLayout(bool bDirX)
{
	TArray<TPair<FVector, FVector>> SplitLines{};
	for (auto& S : GetInstance()->GetModels(EDSModelType::E_House_Area_Split_Line))
	{
		auto SProp = static_cast<FDSSegmentProperty*>(S->GetProperty());
		SplitLines.Add({ SProp->SegmentStart, SProp->SegmentEnd });
		S->OnExecuteAction(FDSModelExecuteType::ExecuteDeleteSelf);
	}

	TMap<FVector, FDSHouseAreaProperty> AreaMap{}; //Center and Property
	for (auto& Iter : GetInstance()->GetModels(EDSModelType::E_House_Area))
	{
		auto AreaProp = static_cast<FDSHouseAreaProperty*>(Iter->GetProperty());
		auto Cen = FGeometryLibrary::GetNormalCenter(AreaProp->Points);
		AreaMap.Add(Cen, *AreaProp);
		Iter->OnExecuteAction(FDSModelExecuteType::ExecuteDeleteSelf);
	}

	TArray<UDSBaseModel*> AllDW{};
	AllDW.Append(GetModels(EDSModelType::E_House_Door));
	AllDW.Append(GetModels(EDSModelType::E_House_Window));

	TArray<UDSBaseModel*> AllPath{};
	AllPath.Append(GetModels(EDSModelType::E_House_Wall));
	AllPath.Append(GetModels(EDSModelType::E_House_Beam));
	AllPath.Append(GetModels(EDSModelType::E_House_Platform));

	TArray<FVector> AllPoints{};

	for (auto& P : AllPath)
	{
		auto Prop = static_cast<FDSHousePathProperty*>(P->GetProperty());
		auto Start = Prop->SegmentStart;
		auto End = Prop->SegmentEnd;
		AllPoints.Add(Start);
		AllPoints.Add(End);
	}

	auto Cen = FGeometryLibrary::GetNormalCenter(AllPoints);
	auto AxisStart = Cen + (bDirX ? FVector::YAxisVector : FVector::XAxisVector) * 10;
	auto AxisEnd = Cen - (bDirX ? FVector::YAxisVector : FVector::XAxisVector) * 10;

	for (auto& P : AllPath)
	{
		auto Prop = static_cast<FDSHousePathProperty*>(P->GetProperty());
		// auto Points = Prop->GetBottomOutline();
		FGeometryLibrary::PointFlip(Prop->SegmentStart, { AxisStart, AxisEnd });
		FGeometryLibrary::PointFlip(Prop->SegmentEnd, { AxisStart, AxisEnd });
		//FGeometryLibrary::PointFlip(Points, { AxisStart ,AxisEnd });
		Prop->RefreshHousePath();
		Prop->InitPathOutline();
		if (P->GetModelType() == EDSModelType::E_House_Wall)
		{
			Cast<UDSHouseWallModel>(P)->ClearDoorAndWindow();
		}
		//P->OnExecuteAction(FDSModelExecuteType::ExecuteAll);
	}
	OnLayoutRefresh(nullptr);

	for (auto& P : GetInstance()->GetModels(EDSModelType::E_House_Pillar))
	{
		auto& Loc = P->GetProperty()->GetTransformPropertyRef().Location;
		FGeometryLibrary::PointFlip(Loc, { AxisStart, AxisEnd });
		P->OnExecuteAction(FDSModelExecuteType::ExecuteTransform);
	}

	for (auto& S : SplitLines)
	{
		FGeometryLibrary::PointFlip(S.Key, { AxisStart, AxisEnd });
		FGeometryLibrary::PointFlip(S.Value, { AxisStart, AxisEnd });
	}

	for (auto& A : GetInstance()->GetModels(EDSModelType::E_House_Area))
	{
		auto AreaProp = static_cast<FDSHouseAreaProperty*>(A->GetProperty());
		auto Points = AreaProp->Points;

		TArray<TPair<FVector, FVector>> Temp{};
		for (auto& S : SplitLines)
		{
			if (FImageProcessModule::Get()->PointInPolygon(S.Key, Points, true, 2) && FImageProcessModule::Get()->PointInPolygon(S.Value, Points, true, 2))
			{
				Temp.Add(S);
			}
		}
		UDSPathLibrary::SplitArea(A, Temp);
	}

	for (auto& A : GetInstance()->GetModels(EDSModelType::E_House_Area))
	{
		if (!Cast<UDSHouseAreaModel>(A)->GetParentPlane() && !Cast<UDSHouseAreaModel>(A)->GetChildPlanes().IsEmpty())
		{
			continue;
		}
		auto AreaProp = static_cast<FDSHouseAreaProperty*>(A->GetProperty());
		auto Points = AreaProp->Points;
		auto TempPoints = Points;
		FGeometryLibrary::PointFlip(TempPoints, { AxisStart, AxisEnd });
		for (auto& Iter : AreaMap)
		{
			if (Iter.Value.GetStateProperty().bHidden)
			{
				continue;
			}
			if (FGeometryLibrary::ComparePolygon(TempPoints, Iter.Value.Points))
			{
				AreaProp->CopyData(&Iter.Value);
				break;
			}
		}
		AreaProp->Points = Points;
		A->OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf);
		UDSPathLibrary::UpdateAreaLabel(A);
	}

	for (auto& DW : AllDW)
	{
		auto DWProp = static_cast<FDSDoorAndWindowProperty*>(DW->GetProperty());
		FGeometryLibrary::PointFlip(DWProp->SegmentStart, { AxisStart, AxisEnd });
		FGeometryLibrary::PointFlip(DWProp->SegmentEnd, { AxisStart, AxisEnd });

		auto Temp = DWProp->SegmentStart;
		DWProp->SegmentStart = DWProp->SegmentEnd;
		DWProp->SegmentEnd = Temp;

		DWProp->RefreshDoorAndWindow();
		DW->OnExecuteAction(FDSModelExecuteType::ExecuteAll);
	}

	for (auto& W : GetInstance()->GetModels(EDSModelType::E_House_Wall))
	{
		bool bHasDW = false;
		for (auto& DW : AllDW)
		{
			if (UDSPathLibrary::PathInPath(DW, W))
			{
				Cast<UDSHouseWallModel>(W)->AddDoorAndWindow(DW);
				Cast<UDSDoorAndWindowBaseModel>(DW)->AddLinkWall(W);
				bHasDW = true;
				//break;
			}
		}

		if (bHasDW)
		{
			W->OnExecuteAction(FDSModelExecuteType::ExecuteAll);
		}
	}
	//UDSMVCSubsystem::GetInstance()->SetCurrentModel(nullptr);

	ResetAllState();
	UDSPendantSubsystem::GetInstance()->RefreshLayoutRuler(true);
}

bool UDSMVCSubsystem::DoesSupportWorldType(const EWorldType::Type WorldType) const
{
	return WorldType == EWorldType::Game || WorldType == EWorldType::PIE;
}

void UDSMVCSubsystem::OnKeyboardFocusChanging(const FFocusEvent& FocusEvent, const FWeakWidgetPath& OldPath, const TSharedPtr<SWidget>& OldWidget, const FWidgetPath& NewPath,
	const TSharedPtr<SWidget>& NewWidget)
{
	if (!NewWidget)
	{
		GetWorld()->GetTimerManager().SetTimerForNextTick([&]()
			{
				FSlateApplication::Get().SetKeyboardFocus(FSlateApplication::Get().GetGameViewport());
			});
	}
}

void UDSMVCSubsystem::VerifyCurrentSelectedModelByCameraType(ECameraType InCameraType)
{
	if (InCameraType == ECameraType::EXYPlan2D_Ceil)
	{
		UDSBaseModel* BaseModel = UDSMVCSubsystem::GetInstance()->GetCurrentModel();
		if (BaseModel != nullptr)
		{
			if (BaseModel->GetModelType() == EDSModelType::E_Group)
			{
				if (!UDSToolLibrary::GroupHasPlaceCeilingModel(Cast<UDSGroupModel>(BaseModel)))
				{
					UDSMVCSubsystem::GetInstance()->SetCurrentModel(nullptr);
				}
			}
			else if (BaseModel->GetModelType() == EDSModelType::E_MultiSelect)
			{
				if (!UDSToolLibrary::GroupHasPlaceCeilingModel(Cast<UDSMultiModel>(BaseModel)))
				{
					UDSMVCSubsystem::GetInstance()->SetCurrentModel(nullptr);
				}
			}
			else if (UDSToolLibrary::IsGroupComponent(BaseModel))
			{
				FString GroupUUID = BaseModel->GetGroupUUID();
				UDSBaseModel* GroupModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(EDSModelType::E_Group, GroupUUID);
				if (!UDSToolLibrary::GroupHasPlaceCeilingModel(Cast<UDSGroupModel>(GroupModel)))
				{
					UDSMVCSubsystem::GetInstance()->SetCurrentModel(nullptr);
				}
			}
			else if (UDSToolLibrary::IsInMultiSelect(BaseModel))
			{
				FString NultiUUID = BaseModel->GetMultiUUID();
				UDSBaseModel* GroupModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(EDSModelType::E_MultiSelect, NultiUUID);
				if (!UDSToolLibrary::GroupHasPlaceCeilingModel(Cast<UDSGroupModel>(GroupModel)))
				{
					UDSMVCSubsystem::GetInstance()->SetCurrentModel(nullptr);
				}
			}
			else if (!UDSToolLibrary::IsPlaceCeilingModel(BaseModel))
			{
				UDSMVCSubsystem::GetInstance()->SetCurrentModel(nullptr);
			}
		}
	}
	else if (InCameraType == ECameraType::EXYPlan2D)
	{
		UDSBaseModel* BaseModel = UDSMVCSubsystem::GetInstance()->GetCurrentModel();
		if (BaseModel != nullptr)
		{
			if (BaseModel->GetModelType() == EDSModelType::E_Furniture_MoldingCeiling)
			{
				UDSMVCSubsystem::GetInstance()->SetCurrentModel(nullptr);
			}
		}
	}

}


void UDSMVCSubsystem::ShowHideModelByCameraType(ECameraType InCameraType)
{
	if (InCameraType == ECameraType::EXYPlan2D_Ceil)
	{
		TArray<UDSBaseModel*> AllSoftModels = GetAllModels();
		for (auto Ite : AllSoftModels)
		{
			if (Ite->GetModelType() == EDSModelType::E_Furniture_HouseFurniture)
			{
				if (!UDSToolLibrary::IsPlaceCeilingModel(Ite))
				{
					FBox BoundBox(Ite->GetModelOrientedBoundingBox());
					FVector Max = BoundBox.Max;
					FVector Min = BoundBox.Min;
					const double XExtend = (Max - Min).X;
					const double YExtend = (Max - Min).Y;
					const double ZExtend = (Max - Min).Z;

					TArray<FVector> BoxEightPoint = {
						Min + FVector(0.0, 0.0, ZExtend),
						Min + FVector(XExtend, 0.0, ZExtend),
						Max,
						Min + FVector(0.0, YExtend, ZExtend)
					};

					if (!UDSCeilingLibrary::CheckPolygonIntersectionAnyCeilingArea(BoxEightPoint))
					{
						if (!Ite->GetViewHidden())
							Ite->OnExecuteAction(FDSModelExecuteType::ExecuteViewTypeHidden);
					}
				}
			}
			else if (UDSToolLibrary::IsCustomCabinetType(Ite->GetModelType())
				|| UDSToolLibrary::IsGeneratedLineType(Ite->GetModelType()))
			{
				if (!Ite->GetViewHidden())
					Ite->OnExecuteAction(FDSModelExecuteType::ExecuteViewTypeHidden);
			}

		}
	}
	else
	{
		TArray<UDSBaseModel*> AllSoftModels = GetAllModels();
		for (auto Ite : AllSoftModels)
		{
			if (Ite->GetModelType() == EDSModelType::E_Furniture_HouseFurniture)
			{
				if (Ite->GetViewHidden())
					Ite->OnExecuteAction(FDSModelExecuteType::ExecuteUnViewTypeHidden);
			}
			else if (UDSToolLibrary::IsCustomCabinetType(Ite->GetModelType())
				|| UDSToolLibrary::IsGeneratedLineType(Ite->GetModelType()))
			{
				if (Ite->GetViewHidden())
					Ite->OnExecuteAction(FDSModelExecuteType::ExecuteUnViewTypeHidden);
			}

		}
	}
}

void UDSMVCSubsystem::ShowHideHouseAreaModels(bool bShow)
{
	TArray<UDSBaseModel*> AllHouseAreaModels = GetModels(EDSModelType::E_House_Area);
	if (bShow)
	{
		for (auto Ite : AllHouseAreaModels)
		{
			if (Ite->GetViewHidden())
				Ite->OnExecuteAction(FDSModelExecuteType::ExecuteUnViewTypeHidden);
		}
	}
	else
	{
		for (auto Ite : AllHouseAreaModels)
		{
			if (!Ite->GetViewHidden())
				Ite->OnExecuteAction(FDSModelExecuteType::ExecuteViewTypeHidden);
		}
	}

	for (auto Ite : AllDefaultGridMeshs)
	{
		if (Ite.IsValid())
		{
			Ite->SetActorHiddenInGame(bShow ? false : true);
			Ite->SetActorEnableCollision(bShow ? true : false);
		}
	}
}

void UDSMVCSubsystem::OnHideCeilinTimer()
{
	FRotator CameraR = UDSCameraSubsystem::GetInstance()->GetCameraRotation();
	FVector  CameraLocation = UDSCameraSubsystem::GetInstance()->GetCameraLocation();

	TArray<UDSBaseModel*> AllCeilingAreaModels = GetModels(EDSModelType::E_RoofArea);
	for (auto Ite : AllCeilingAreaModels)
	{
		float DistanceToFloor = Ite->GetTypedProperty<FDSCeilingAreaProperty>()->DistanceToFloor;
		if (CameraLocation.Z < DistanceToFloor)
		{
			//if (CameraR.Pitch > 0 && CameraR.Pitch < 90)
			{
				if (Ite->IsViewTypeHidden())
					Ite->OnExecuteAction(FDSModelExecuteType::ExecuteUnViewTypeHidden, FDSBroadcastMarkData::NotBroadcastToMVCMark);
			}

			//else
				//Ite->OnExecuteAction(FDSModelExecuteType::ExecuteHidden);
		}
		else
		{
			if (!Ite->IsViewTypeHidden())
			{
				Ite->OnExecuteAction(FDSModelExecuteType::ExecuteViewTypeHidden, FDSBroadcastMarkData::NotBroadcastToMVCMark);
				//if (Ite->IsSelected())
				//	Ite->OnExecuteAction(FDSModelExecuteType::ExecuteUnSelect);
				if (GetCurrentModel() == Ite)
				{
					SetCurrentModel(nullptr);
				}

			}
				
		}
	}

	TArray<UDSBaseModel*> AllMoldingCeilingModels = GetModels(EDSModelType::E_Furniture_MoldingCeiling);
	for (auto Ite : AllMoldingCeilingModels)
	{
		float DistanceToFloor = Ite->GetTypedProperty<FDSMoldingCeilingProperty>()->GetTransformProperty().GetLocation().Z;
		if (CameraLocation.Z < DistanceToFloor)
		{
			if (Ite->IsViewTypeHidden())
				Ite->OnExecuteAction(FDSModelExecuteType::ExecuteUnViewTypeHidden, FDSBroadcastMarkData::NotBroadcastToMVCMark);
		}
		else
		{
			if (!Ite->IsViewTypeHidden())
			{
				Ite->OnExecuteAction(FDSModelExecuteType::ExecuteViewTypeHidden, FDSBroadcastMarkData::NotBroadcastToMVCMark);
				//if (Ite->IsSelected())
				//	Ite->OnExecuteAction(FDSModelExecuteType::ExecuteUnSelect);

				if (GetCurrentModel() == Ite)
				{
					SetCurrentModel(nullptr);
				}
			}
		}
	}
	
	if (CameraLocation.Z < 0 && CameraR.Pitch > 0 && CameraR.Pitch < 90)
	{
		ShowHideHouseAreaModels(false);
	}
	else
	{
		ShowHideHouseAreaModels(true);
	}
}

void UDSMVCSubsystem::HideCeilingByCameraType(ECameraType InCameraType)
{
	TArray<UDSBaseModel*> AllCeilingAreaModels = GetModels(EDSModelType::E_RoofArea);
	TArray<UDSBaseModel*> AllMoldingCeilingModels = GetModels(EDSModelType::E_Furniture_MoldingCeiling);
	if (InCameraType == ECameraType::EXYPlan2D)
	{
		for (auto Ite : AllCeilingAreaModels)
		{
			Ite->OnExecuteAction(FDSModelExecuteType::ExecuteViewTypeHidden);
		}

		for (auto Ite : AllMoldingCeilingModels)
		{
			Ite->OnExecuteAction(FDSModelExecuteType::ExecuteViewTypeHidden);
		}

		TArray<UDSBaseModel*> AllHouseAreaModels = GetModels(EDSModelType::E_House_Area);
		for (auto Ite : AllHouseAreaModels)
		{
			if (Ite->GetViewHidden())
				Ite->OnExecuteAction(FDSModelExecuteType::ExecuteUnViewTypeHidden);
		}
	}
	else if (InCameraType == ECameraType::EXYPlan2D_Ceil)
	{
		for (auto Ite : AllCeilingAreaModels)
		{
			Ite->OnExecuteAction(FDSModelExecuteType::ExecuteUnViewTypeHidden);
		}

		for (auto Ite : AllMoldingCeilingModels)
		{
			Ite->OnExecuteAction(FDSModelExecuteType::ExecuteUnViewTypeHidden);
		}

		TArray<UDSBaseModel*> AllHouseAreaModels = GetModels(EDSModelType::E_House_Area);
		for (auto Ite : AllHouseAreaModels)
		{
			if (Ite->GetViewHidden())
				Ite->OnExecuteAction(FDSModelExecuteType::ExecuteUnViewTypeHidden);
		}
	}
	else
	{
		OnHideCeilinTimer();
	}
}

void UDSMVCSubsystem::ReplaceUUID(const UDSBaseModel* InModel, const FString& OldUUID, const FString& InUUID)
{
	ResourceData->CheckUUID(InModel, OldUUID, InUUID);
}
