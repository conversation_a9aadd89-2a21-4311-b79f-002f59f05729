// Fill out your copyright notice in the Description page of Project Settings.


#include "DSConstructionDimensionGenerator.h"
#include "SubSystems/Construction/DSConstructionCore.h"
#include "SubSystems/Construction/DSConstructionSubsystem.h"

DSConstructionDimensionGenerator::DSConstructionDimensionGenerator()
{
	HorizontalDimensionSets = MakeShared<FDSAxisDimensionData>(true);
	VerticalDimensionSets = MakeShared<FDSAxisDimensionData>(false);
}

DSConstructionDimensionGenerator::~DSConstructionDimensionGenerator()
{
}

void DSConstructionDimensionGenerator::GenerateDimension(const TArray<FVector>& InInternalOutLine ,const TMap<FString, TSharedPtr<FDSConstructionData>> InConstructDatas)
{
	// 1. 计算整体包围盒
	GetGlobalBox(InConstructDatas);
	HorizontalDimensionSets->Min = GlobalAABB.Min;
	HorizontalDimensionSets->Max = GlobalAABB.Max;
	VerticalDimensionSets->Max = GlobalAABB.Max;
	VerticalDimensionSets->Min = GlobalAABB.Min;
	// 2. 标注基准位置（上下左右）
	FVector UpPos = FVector(GlobalAABB.Min.X, GlobalAABB.Max.Y, 0); // 上
	FVector DownPos = FVector(GlobalAABB.Min.X, GlobalAABB.Min.Y, 0); // 下
	FVector LeftPos = FVector(GlobalAABB.Min.X, GlobalAABB.Min.Y, 0); // 左
	FVector RightPos = FVector(GlobalAABB.Max.X, GlobalAABB.Min.Y, 0); // 右


	InternalOutLine = InInternalOutLine;
	FVector InternalOutlineMax(-FLT_MAX, -FLT_MAX,0.f);
	FVector InternalOutlineMin(FLT_MAX, FLT_MAX, 0.f);

	for (auto Point: InInternalOutLine)
	{
		InternalOutlineMin.X = FMath::Min(InternalOutlineMin.X, Point.X);
		InternalOutlineMin.Y = FMath::Min(InternalOutlineMin.Y, Point.Y);

		InternalOutlineMax.X = FMath::Max(InternalOutlineMax.X, Point.X);
		InternalOutlineMax.Y = FMath::Max(InternalOutlineMax.Y, Point.Y);
	}


	TArray<TSharedPtr<FDSDimensionEntryData>> Entrys;
	//图纸投影方向(ABCD)
	for (auto Iter : InConstructDatas)
	{
		auto ConstructData = Iter.Value;
		if (!ConstructData.IsValid())
			continue;

		FVector MinPoint, MaxPoint;
		ConstructData->GetAABB(MinPoint, MaxPoint);

		//每个对象要投影到垂直和水平两个方向
		TSharedPtr<FDSDimensionEntryData> Entry = MakeShared<FDSDimensionEntryData>();
		Entry->UUID = ConstructData->GetUUID();
		Entry->Max = MaxPoint;
		Entry->Min = MinPoint;
		Entry->ModelType = ConstructData->GetModelType();



		// 计算到四个方向的距离
		double DistUp = FMath::Abs(MaxPoint.Y - InternalOutlineMax.Y);
		double DistDown = FMath::Abs(MinPoint.Y - InternalOutlineMin.Y);
		Entry->bHorizontalPositive = DistUp < DistDown;

		double DistLeft = FMath::Abs(MinPoint.X - InternalOutlineMin.X);
		double DistRight = FMath::Abs(MaxPoint.X - InternalOutlineMax.X);
		Entry->bVerticalPositive = DistRight < DistLeft;

		Entrys.Add(Entry);
	}




	//HorizontalDimensionSets->Insert(Entry);
	//VerticalDimensionSets->Insert(Entry);

	//对每个对象求出宽高投影到的边
	//每条边根据垂直方向是否重叠进行分组，

	//求出InConstructDatas中所有构件的外包矩形


	// 通过墙就能确定整体包围盒
	//获得最大合并矩形
	//获得四个方向上的标注位置
}

void DSConstructionDimensionGenerator::GenerateDimension(const TArray<TSharedPtr<FDSConstructionData>>& InConstructDatas)
{
	//计算整体包围盒
	GetGlobalBox(InConstructDatas);
	HorizontalDimensionSets->Min = GlobalAABB.Min;
	HorizontalDimensionSets->Max = GlobalAABB.Max;
	VerticalDimensionSets->Max = GlobalAABB.Max;
	VerticalDimensionSets->Min = GlobalAABB.Min;
	//标注基准位置（上下左右）
	FVector UpPos = FVector(GlobalAABB.Max.X, GlobalAABB.Min.Y, 0); // 上
	FVector DownPos = FVector(GlobalAABB.Min.X, GlobalAABB.Max.Y, 0); // 下
	FVector LeftPos = FVector(GlobalAABB.Min.X, GlobalAABB.Min.Y, 0); // 左
	FVector RightPos = FVector(GlobalAABB.Max.X, GlobalAABB.Max.Y, 0); // 右

	TArray<TSharedPtr<FDSDimensionEntryData>> Entrys;

	TArray<EDSModelType> LayoutTypes =
	{
		EDSModelType::E_House_Wall
	};

	//标注物体box数据
	TArray<TArray<FVector>> LayoutBoxPoints;
	TArray<TArray<FVector>> ConstructBoxPoints;
	for (const auto& Iter : InConstructDatas)
	{
		FVector MinPoint;
		FVector MaxPoint;
		Iter->GetAABB(MinPoint, MaxPoint);

		//0->1左
		//1->2上
		//2->3右
		//3->0下
		TArray<FVector> BoxPoints;
		BoxPoints.Add(FVector(MinPoint.X, MinPoint.Y, 0));
		BoxPoints.Add(FVector(MaxPoint.X, MinPoint.Y, 0));
		BoxPoints.Add(FVector(MaxPoint.X, MaxPoint.Y, 0));
		BoxPoints.Add(FVector(MinPoint.X, MaxPoint.Y, 0));
		if (LayoutTypes.Contains(Iter->GetModelType()))
		{
			LayoutBoxPoints.Add(BoxPoints);
		}
		else
		{
			ConstructBoxPoints.Add(BoxPoints);
		}
	}

	//选择投影边

	auto CalculateProjection = [&](const TArray<TArray<FVector>>& BoxPoints, TMap<int32, TMap<int32, double>>& OutMap)
		{
			OutMap = { {0,{}},{1,{}},{2,{}},{3,{}} }; // <投影边索引(墙), <投影物体索引,距离>>  左上右下
			for (int32 i = 0; i < BoxPoints.Num(); i++)
			{
				TPair<int32, double> YMinDistanceSquared_Up = { -1,MAX_dbl }; //Y轴最小距离上
				TPair<int32, double> YMinDistanceSquared_Down = { -1,MAX_dbl }; //Y轴最小距离下

				TPair<int32, double> XMinDistanceSquared_Left = { -1,MAX_dbl }; //X轴最小距离左
				TPair<int32, double> XMinDistanceSquared_Right = { -1,MAX_dbl }; //X轴最小距离右

				auto LeftDist = FMath::PointDistToSegmentSquared(BoxPoints[i][0], LeftPos, UpPos);
				auto UpDist = FMath::PointDistToSegmentSquared(BoxPoints[i][1], UpPos, RightPos);
				auto RightDist = FMath::PointDistToSegmentSquared(BoxPoints[i][2], RightPos, DownPos);
				auto DownDist = FMath::PointDistToSegmentSquared(BoxPoints[i][3], DownPos, LeftPos);

				//Y轴投影
				if (UpDist < YMinDistanceSquared_Up.Value)
				{
					YMinDistanceSquared_Up.Key = 1;
					YMinDistanceSquared_Up.Value = UpDist;
				}

				if (DownDist < YMinDistanceSquared_Down.Value)
				{
					YMinDistanceSquared_Down.Key = 3;
					YMinDistanceSquared_Down.Value = DownDist;
				}

				//X轴投影
				if (LeftDist < XMinDistanceSquared_Left.Value)
				{
					XMinDistanceSquared_Left.Key = 0;
					XMinDistanceSquared_Left.Value = LeftDist;
				}
				if (RightDist < XMinDistanceSquared_Right.Value)
				{
					XMinDistanceSquared_Right.Key = 2;
					XMinDistanceSquared_Right.Value = RightDist;
				}

				if (YMinDistanceSquared_Up.Key != -1 && YMinDistanceSquared_Down.Key != -1 && XMinDistanceSquared_Left.Key != -1 && XMinDistanceSquared_Right.Key != -1)
				{
					if (FMath::IsNearlyEqual(YMinDistanceSquared_Up.Value, YMinDistanceSquared_Down.Value))
					{
						OutMap[YMinDistanceSquared_Up.Key].Add(i, YMinDistanceSquared_Up.Value);
						OutMap[YMinDistanceSquared_Down.Key].Add(i, YMinDistanceSquared_Down.Value);
					}
					else if (YMinDistanceSquared_Up.Value < YMinDistanceSquared_Down.Value)
					{
						OutMap[YMinDistanceSquared_Up.Key].Add(i, YMinDistanceSquared_Up.Value);
						YMinDistanceSquared_Down.Key = -1; //不投影到下边
					}
					else
					{
						OutMap[YMinDistanceSquared_Down.Key].Add(i, YMinDistanceSquared_Down.Value);
						YMinDistanceSquared_Up.Key = -1; //不投影到上边
					}

					if (FMath::IsNearlyEqual(XMinDistanceSquared_Left.Value, XMinDistanceSquared_Right.Value))
					{
						OutMap[XMinDistanceSquared_Left.Key].Add(i, XMinDistanceSquared_Left.Value);
						OutMap[XMinDistanceSquared_Right.Key].Add(i, XMinDistanceSquared_Right.Value);
					}
					else if (XMinDistanceSquared_Left.Value < XMinDistanceSquared_Right.Value)
					{
						OutMap[XMinDistanceSquared_Left.Key].Add(i, XMinDistanceSquared_Left.Value);
						XMinDistanceSquared_Right.Key = -1; //不投影到右边
					}
					else
					{
						OutMap[XMinDistanceSquared_Right.Key].Add(i, XMinDistanceSquared_Right.Value);
						XMinDistanceSquared_Left.Key = -1; //不投影到左边
					}
				}
			}

			//投影边物体按照距离进行排序
			for (auto& Iter : OutMap)
			{
				Iter.Value.ValueSort([](const double& A, const double& B) {return A < B; });
			}
		};

	TMap<int32, TMap<int32, double>> LayoutProjectionMap;
	CalculateProjection(LayoutBoxPoints, LayoutProjectionMap);
	TMap<int32, TMap<int32, double>> BoxProjectionMap;
	CalculateProjection(ConstructBoxPoints, BoxProjectionMap);

	//计算墙的投影
	//0:左
	auto LeftProjection = LayoutProjectionMap[0];
	TArray<FVector> LeftProjectionPoints;
	for (const auto& Iter : LeftProjection)
	{
		int32 Index = Iter.Key;
		if (Index < 0 || Index >= LayoutBoxPoints.Num())
			continue;
		LeftProjectionPoints.Add(FMath::ClosestPointOnSegment(LayoutBoxPoints[Index][0], LeftPos, UpPos)); // 左边投影点起点
		LeftProjectionPoints.Add(FMath::ClosestPointOnSegment(LayoutBoxPoints[Index][1], LeftPos, UpPos)); // 左边投影点终点
	}
	LeftProjectionPoints.Sort([](const FVector& A, const FVector& B) { return A.X < B.X; });

	//1:上
	auto UpProjection = LayoutProjectionMap[1];
	TArray<FVector> UpProjectionPoints;
	for (const auto& Iter : UpProjection)
	{
		int32 Index = Iter.Key;
		if (Index < 0 || Index >= LayoutBoxPoints.Num())
			continue;
		UpProjectionPoints.Add(FMath::ClosestPointOnSegment(LayoutBoxPoints[Index][1], UpPos, RightPos)); // 上边投影点起点
		UpProjectionPoints.Add(FMath::ClosestPointOnSegment(LayoutBoxPoints[Index][2], UpPos, RightPos)); // 上边投影点终点
	}
	UpProjectionPoints.Sort([](const FVector& A, const FVector& B) { return A.Y < B.Y; });

	//2:右
	auto RightProjection = LayoutProjectionMap[2];
	TArray<FVector> RightProjectionPoints;
	for (const auto& Iter : RightProjection)
	{
		int32 Index = Iter.Key;
		if (Index < 0 || Index >= LayoutBoxPoints.Num())
			continue;
		RightProjectionPoints.Add(FMath::ClosestPointOnSegment(LayoutBoxPoints[Index][2], RightPos, DownPos)); // 右边投影点起点
		RightProjectionPoints.Add(FMath::ClosestPointOnSegment(LayoutBoxPoints[Index][3], RightPos, DownPos)); // 右边投影点终点
	}
	RightProjectionPoints.Sort([](const FVector& A, const FVector& B) { return A.X < B.X; });

	//3:下
	auto DownProjection = LayoutProjectionMap[3];
	TArray<FVector> DownProjectionPoints;
	for (const auto& Iter : DownProjection)
	{
		int32 Index = Iter.Key;
		if (Index < 0 || Index >= LayoutBoxPoints.Num())
			continue;
		DownProjectionPoints.Add(FMath::ClosestPointOnSegment(LayoutBoxPoints[Index][3], DownPos, LeftPos)); // 下边投影点起点
		DownProjectionPoints.Add(FMath::ClosestPointOnSegment(LayoutBoxPoints[Index][0], DownPos, LeftPos)); // 下边投影点终点
	}
	DownProjectionPoints.Sort([](const FVector& A, const FVector& B) { return A.Y < B.Y; });

	//计算每条边的物体投影，并分组，保证标注不相交
	//每条边的投影物体分组，保存索引
	TArray<TArray<int32>> LeftProjectionGroups; // 左边投影物体分组
	TArray<TArray<int32>> UpProjectionGroups; // 上边投影物体分组
	TArray<TArray<int32>> RightProjectionGroups; // 右边投影物体分组
	TArray<TArray<int32>> DownProjectionGroups; // 下边投影物体分组

	//判断两点是否在另外两点之间
	auto IsBetween = [](const FVector& A, const FVector& B, const FVector& C, const FVector& D)
		{
			// 判断点A和点B是否在点C和点D之间
			return (A.X >= FMath::Min(C.X, D.X) && A.X <= FMath::Max(C.X, D.X) &&
				A.Y >= FMath::Min(C.Y, D.Y) && A.Y <= FMath::Max(C.Y, D.Y)) &&
				(B.X >= FMath::Min(C.X, D.X) && B.X <= FMath::Max(C.X, D.X) &&
					B.Y >= FMath::Min(C.Y, D.Y) && B.Y <= FMath::Max(C.Y, D.Y));
		};

	auto CalculateProjectionGroups = [&](const TArray<FVector>& InProjectionPoints, const TMap<int32, double>& InProjectionMap
		, const int32& DirType, TArray<TArray<TPair<FVector, FVector>>>& OutProjectionLineGroups) //DirType 0:左, 1:上, 2:右, 3:下
		{
			auto TempProjectionPoints = InProjectionPoints;
			auto ProjectionMap = InProjectionMap;
			while (!ProjectionMap.IsEmpty())
			{
				TSet<int32> RemoveIndex;
				TArray<TPair<FVector, FVector>> LineGroup;

				int32 StartIndex = 0;
				int32 EndIndex = ProjectionMap.Num() - 1;

				int32 DirStart = DirType;
				int32 DirEnd = (DirType + 1) % 4; // 下一个方向

				FVector ProjectStartPos = LeftPos; // 投影起点
				if (DirType == 1) // 上
				{
					ProjectStartPos = UpPos;
				}
				else if (DirType == 2) // 右
				{
					ProjectStartPos = RightPos;
				}
				else if (DirType == 3) // 下
				{
					ProjectStartPos = DownPos;
				}

				FVector ProjectEndPos = UpPos; // 投影终点
				if (DirType == 1) // 左
				{
					ProjectEndPos = RightPos;
				}
				else if (DirType == 2) // 右
				{
					ProjectEndPos = DownPos;
				}
				else if (DirType == 3) // 下
				{
					ProjectEndPos = LeftPos;
				}

				for (auto& Iter : ProjectionMap)
				{
					auto StartProjection = FMath::ClosestPointOnSegment(ConstructBoxPoints[Iter.Key][DirStart], ProjectStartPos, ProjectEndPos);
					auto EndProjection = FMath::ClosestPointOnSegment(ConstructBoxPoints[Iter.Key][DirEnd], ProjectStartPos, ProjectEndPos);

					bool bIsBetween = false;
					for (int32 i = 0; i < TempProjectionPoints.Num() - 1; i++)
					{
						if (IsBetween(StartProjection, EndProjection, TempProjectionPoints[i], TempProjectionPoints[i + 1]))
						{
							RemoveIndex.Add(Iter.Key);
							bIsBetween = true;
							break;
						}
					}

					//如果在某个投影边之间，则加入分组，并添加到点组中
					if (bIsBetween)
					{
						//保证点序X和Y均由小到大
						if (DirType == 2 && StartProjection.X > EndProjection.X)
						{
							auto Temp = StartProjection;
							StartProjection = EndProjection;
							EndProjection = Temp;
						}
						else if (DirType == 3 && StartProjection.Y > EndProjection.Y)
						{
							auto Temp = StartProjection;
							StartProjection = EndProjection;
							EndProjection = Temp;
						}

						LineGroup.Add(TPair<FVector, FVector>(StartProjection, EndProjection));
						TempProjectionPoints.Add(StartProjection);
						TempProjectionPoints.Add(EndProjection);
						//重新排序
						TempProjectionPoints.Sort([&](const FVector& A, const FVector& B)
							{ return DirType == 0 || DirType == 2 ? A.X < B.X : A.Y < B.Y; });
					}
				}

				//取已有线段的相邻线段
				TArray<TPair<FVector, FVector>> NewLines;
				for (auto& Iter : LineGroup)
				{
					int32 PreIdnex = -1;
					if (TempProjectionPoints.Find(Iter.Key, PreIdnex))
					{
						//如果前一条标注不存在，则增加前一条标注
						auto Find = LineGroup.FindByPredicate([&](const TPair<FVector, FVector>& Pair)
							{return  TempProjectionPoints[PreIdnex].Equals(Pair.Value); });
						if (Find == nullptr)
						{
							if (PreIdnex > 0)
							{
								--PreIdnex;
								if (!TempProjectionPoints[PreIdnex].Equals(Iter.Key))
								{
									NewLines.Add(TPair<FVector, FVector>(TempProjectionPoints[PreIdnex], Iter.Key));
								}
							}
						}
					}

					int32 NextIndex = -1;
					if (TempProjectionPoints.Find(Iter.Value, NextIndex))
					{
						//如果后一条标注不存在，则增加后一条标注
						auto Find = LineGroup.FindByPredicate([&](const TPair<FVector, FVector>& Pair)
							{return  TempProjectionPoints[NextIndex].Equals(Pair.Key); });
						if (Find == nullptr)
						{
							if (NextIndex < TempProjectionPoints.Num() - 1)
							{
								++NextIndex;
								if (!TempProjectionPoints[NextIndex].Equals(Iter.Value))
								{
									NewLines.Add(TPair<FVector, FVector>(Iter.Value, TempProjectionPoints[NextIndex]));
								}
							}
						}
					}

				}
				LineGroup.Append(NewLines);
				//保存分组
				if (LineGroup.Num() > 0)
				{
					OutProjectionLineGroups.Add(LineGroup);
				}

				//移除已经分组的投影
				for (auto& Iter : RemoveIndex)
				{
					ProjectionMap.Remove(Iter);
				}

				//初始化投影点
				TempProjectionPoints.Empty();
				TempProjectionPoints = InProjectionPoints;
			}

		};

	//左侧投影分组
	LeftProjectionLineGroups.Empty();
	CalculateProjectionGroups(LeftProjectionPoints, BoxProjectionMap[0], 0, LeftProjectionLineGroups);
	//上侧投影分组
	UpProjectionLineGroups.Empty();
	CalculateProjectionGroups(UpProjectionPoints, BoxProjectionMap[1], 1, UpProjectionLineGroups);
	//右侧投影分组
	RightProjectionLineGroups.Empty();
	CalculateProjectionGroups(RightProjectionPoints, BoxProjectionMap[2], 2, RightProjectionLineGroups);
	//下侧投影分组
	DownProjectionLineGroups.Empty();
	CalculateProjectionGroups(DownProjectionPoints, BoxProjectionMap[3], 3, DownProjectionLineGroups);

	//偏移左侧投影分组
	for (int32 i = 0; i < LeftProjectionLineGroups.Num(); i++)
	{
		auto Offset = FVector(0, -(i + 1) * 50, 0); // 每组投影点偏移
		for (auto& Line : LeftProjectionLineGroups[i])
		{
			Line.Key += Offset; // 添加偏移
			Line.Value += Offset; // 添加偏移
		}
	}

	//偏移上侧投影分组
	for (int32 i = 0; i < UpProjectionLineGroups.Num(); i++)
	{
		auto Offset = FVector((i + 1) * 50, 0, 0); // 每组投影点偏移
		for (auto& Line : UpProjectionLineGroups[i])
		{
			Line.Key += Offset; // 添加偏移
			Line.Value += Offset; // 添加偏移
		}
	}

	//偏移右侧投影分组
	for (int32 i = 0; i < RightProjectionLineGroups.Num(); i++)
	{
		auto Offset = FVector(0, (i + 1) * 50, 0); // 每组投影点偏移
		for (auto& Line : RightProjectionLineGroups[i])
		{
			Line.Key += Offset; // 添加偏移
			Line.Value += Offset; // 添加偏移
		}
	}

	//偏移下侧投影分组
	for (int32 i = 0; i < DownProjectionLineGroups.Num(); i++)
	{
		auto Offset = FVector(-(i + 1) * 50, 0, 0); // 每组投影点偏移
		for (auto& Line : DownProjectionLineGroups[i])
		{
			Line.Key += Offset; // 添加偏移
			Line.Value += Offset; // 添加偏移
		}
	}
}

void DSConstructionDimensionGenerator::DrawDimension(const FVector& PaperOffset)
{
	//左侧标注
	for (auto& G : GetLeftProjectionLineGroups())
	{
		for (auto& D : G)
		{
			UDSConstructionSubsystem::GetInstance()->DrawDimension(D.Key, D.Value, PaperOffset, false);
		}
	}

	//上侧标注
	for (auto& G : GetUpProjectionLineGroups())
	{
		for (auto& D : G)
		{
			UDSConstructionSubsystem::GetInstance()->DrawDimension(D.Key, D.Value, PaperOffset, true);
		}
	}

	//右侧标注
	for (auto& G : GetRightProjectionLineGroups())
	{
		for (auto& D : G)
		{
			UDSConstructionSubsystem::GetInstance()->DrawDimension(D.Key, D.Value, PaperOffset, false);
		}
	}

	//下侧标注
	for (auto& G : GetDownProjectionLineGroups())
	{
		for (auto& D : G)
		{
			UDSConstructionSubsystem::GetInstance()->DrawDimension(D.Key, D.Value, PaperOffset, true);
		}
	}

}

void DSConstructionDimensionGenerator::GetGlobalBox(const TMap<FString, TSharedPtr<FDSConstructionData>>& InConstructDatas)
{

	GlobalAABB.Max = FVector2D(-FLT_MAX, -FLT_MAX);
	GlobalAABB.Min = FVector2D(FLT_MAX, FLT_MAX);

	for (const auto& Iter : InConstructDatas)
	{
		auto ConstructData = Iter.Value;
		if (!ConstructData.IsValid())
			continue;

		FVector MinPoint, MaxPoint;
		ConstructData->GetAABB(MinPoint, MaxPoint);

		GlobalAABB.Min.X = FMath::Min(GlobalAABB.Min.X, MinPoint.X);
		GlobalAABB.Min.Y = FMath::Min(GlobalAABB.Min.Y, MinPoint.Y);

		GlobalAABB.Max.X = FMath::Max(GlobalAABB.Max.X, MaxPoint.X);
		GlobalAABB.Max.Y = FMath::Max(GlobalAABB.Max.Y, MaxPoint.Y);
	}
}

void DSConstructionDimensionGenerator::GetGlobalBox(const TArray<TSharedPtr<FDSConstructionData>>& InConstructDatas)
{
	GlobalAABB.Max = FVector2D(-FLT_MAX, -FLT_MAX);
	GlobalAABB.Min = FVector2D(FLT_MAX, FLT_MAX);
	for (const auto& ConstructData : InConstructDatas)
	{
		if (!ConstructData.IsValid())
			continue;
		FVector MinPoint, MaxPoint;
		ConstructData->GetAABB(MinPoint, MaxPoint);
		GlobalAABB.Min.X = FMath::Min(GlobalAABB.Min.X, MinPoint.X);
		GlobalAABB.Min.Y = FMath::Min(GlobalAABB.Min.Y, MinPoint.Y);
		GlobalAABB.Max.X = FMath::Max(GlobalAABB.Max.X, MaxPoint.X);
		GlobalAABB.Max.Y = FMath::Max(GlobalAABB.Max.Y, MaxPoint.Y);
	}
}

void FDSAxisDimensionData::Insert(const TSharedPtr<FDSDimensionEntryData>& InEntryData)
{
	for (auto& Iter: DimensionSetDatas)
	{
		if (Iter->Add(InEntryData))
		{
			return;
		}
	}
	// 计算到四个方向的距离
	bool bPositive = false;
	if (bHorizontal)
	{
		double DistUp = FMath::Abs(InEntryData->Max.Y - Max.Y);
		double DistDown = FMath::Abs(InEntryData->Min.Y - Min.Y);

		bPositive = DistUp < DistDown;
	}
	else
	{
		double DistLeft = FMath::Abs(InEntryData->Min.X - Min.X);
		double DistRight = FMath::Abs(InEntryData->Max.X - Max.X);
		bPositive = DistRight < DistLeft;
	}

	TSharedPtr<FDSDimensionSet> NewSet = MakeShared<FDSDimensionSet>(bPositive);
	NewSet->Add(InEntryData);
	DimensionSetDatas.Add(NewSet);
}

void FDSDimensionSet::CalculateLinkRelationship(bool bHorizontal)
{
	// 假设 DimensionEntryDataMap 保存所有标注对象
	for (auto& EntryPair : DimensionEntryDataMap)
	{
		TSharedPtr<FDSDimensionEntryData> SourceEntry = EntryPair.Value;
		if (!SourceEntry.IsValid())
			continue;
	}
}