// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Subsystems/DSCore.h"
#include "LocalCache/Component/MultiComponentDataDefine.h"
#include "DSConstructionCore.generated.h"
UENUM()
enum class E_ConstructionPaperType : uint8
{
	PaperType_None = 0, //无图纸
	PaperType_SingleRoomPlane,  //单屋平面图纸
	PaperType_MutilRoomPlane,   //全屋平面图纸
	PaperType_Front_Construction, //柜体结构图纸
	PaperType_Front_Door  //柜门结构图
};


UENUM()
enum class EDSPrimitiveType : uint8
{
	PrimitiveType_None = 0, // 无类型
	PrimitiveType_Point,
	PrimitiveType_Line,
	PrimitiveType_Polygon,   // 多线段
	PrimitiveType_MPolygon,   // 多线段
	PrimitiveType_Polygon3D,
	PrimitiveType_MPolygon3D,
	PrimitiveType_MPolygon_Hatch
};

namespace DSFrameAttDefName
{
	extern const FString Name_DRAWING_NAME;
	extern const FString Name_room_name;
	extern const FString Name_style_name;
	extern const FString Name_USERNAME;
	extern const FString Name_USERPHONE;
	extern const FString Name_DESIGNERNAME;
	extern const FString Name_DESIGNERPHONE;
	extern const FString Name_CITYNAMEBUILDNAMEANDMODELNAME;
	extern const FString Name_most_door_board_material_name;
	extern const FString Name_most_door_board_material_name2;
	extern const FString Name_most_door_board_material_name3;
	extern const FString Name_most_door_board_name;
	extern const FString Name_most_glass_core_name;
	extern const FString Name_most_knob_name;
	extern const FString Name_most_knob_name2;
	extern const FString Name_most_cabinet_board_material_name;
	extern const FString Name_most_cabinet_board_material_name2;
	extern const FString Name_most_cabinet_board_material_name3;
	extern const FString Name_most_hinge_name;
	extern const FString Name_most_down_rail_name;
	extern const FString Name_table_surface_mateiralvo;
	extern const FString Name_front_section_name1;
	extern const FString Name_back_section_name1;
};

struct FDSPrimitiveBase
{
	FDSPrimitiveBase() {}
	virtual ~FDSPrimitiveBase() {}
	FDSPrimitiveBase(EDSPrimitiveType InPrimitiveType) :PrimitiveType(InPrimitiveType){}
	EDSPrimitiveType PrimitiveType = EDSPrimitiveType::PrimitiveType_None;

	int32 ColorID = 256; //颜色ID
	FString Linetype = FString(TEXT("BYLAYER"));
	int32 LineWeight = -1; //线宽，-1表示BYLAYER

	void CopyCADAttribute(const FDSPrimitiveBase* InOther)
	{
		ColorID = InOther->ColorID;
		Linetype = InOther->Linetype;
		LineWeight = InOther->LineWeight;
	}

	virtual bool IsVaild() const { return false; }
	virtual float GetArea() const { return 0.0f; }
	virtual FBox GetBox() const = 0;
	virtual void Transform(const FTransform& InTransform) = 0;
	virtual TSharedPtr<FDSPrimitiveBase> NewCopy() = 0;
};

struct FDSPrimitive_Line : public FDSPrimitiveBase
{
	FDSPrimitive_Line() : FDSPrimitiveBase(EDSPrimitiveType::PrimitiveType_Line) {};

	FVector Start = FVector::ZeroVector;
	FVector End = FVector::ZeroVector;

	bool IsVaild() const override
	{
		return true;
	}

	virtual FBox GetBox() const override
	{
		return FBox({ Start ,End});
	}

	virtual void Transform(const FTransform& InTransform) override
	{
		Start = InTransform.TransformPosition(Start);
		End = InTransform.TransformPosition(End);
	}

	virtual TSharedPtr<FDSPrimitiveBase> NewCopy() override
	{
		FDSPrimitive_Line* NewPrimitive = new FDSPrimitive_Line();
		*NewPrimitive = *this;
		return TSharedPtr<FDSPrimitive_Line>(NewPrimitive);
	}
};

struct FDSPrimitive_Polygon : public FDSPrimitiveBase
{
	FDSPrimitive_Polygon() : FDSPrimitiveBase(EDSPrimitiveType::PrimitiveType_Polygon) {};

	TArray<FVector> Points; //多边形顶点列表

	bool bIsClose = true;   //是否闭合

	bool IsVaild() const override
	{
		return Points.Num() > 0;
	}

	virtual FBox GetBox() const override
	{
		return FBox(Points);
	}
	virtual void Transform(const FTransform& InTransform) override
	{
		for (auto& Point : Points)
		{
			Point = InTransform.TransformPosition(Point);
		}
	}

	virtual TSharedPtr<FDSPrimitiveBase> NewCopy() override
	{
		FDSPrimitive_Polygon* NewPrimitive = new FDSPrimitive_Polygon();
		*NewPrimitive = *this;
		return TSharedPtr<FDSPrimitive_Polygon>(NewPrimitive);
	}
};

struct FDSPrimitive_MPolygon : public FDSPrimitiveBase
{
	FDSPrimitive_MPolygon() : FDSPrimitiveBase(EDSPrimitiveType::PrimitiveType_MPolygon) {};

	TArray<TArray<FVector>> Points; //多边形顶点列表

	bool IsVaild() const override
	{
		return Points.Num() > 0 && Points[0].Num() >= 3;
	}

	virtual FBox GetBox() const override
	{
		FBox TotalBox;
		for (auto& Ite : Points)
		{
			TotalBox += FBox(Ite);
		}
		return TotalBox;
	}

	virtual void Transform(const FTransform& InTransform) override
	{
		for (auto& Polygon : Points)
		{
			for (auto& Point : Polygon)
			{
				Point = InTransform.TransformPosition(Point);
			}
		}
	}

	virtual TSharedPtr<FDSPrimitiveBase> NewCopy() override
	{
		FDSPrimitive_MPolygon* NewPrimitive = new FDSPrimitive_MPolygon();
		*NewPrimitive = *this;
		return TSharedPtr<FDSPrimitive_MPolygon>(NewPrimitive);
	}
};

struct FDSPrimitive_Polygon3D : public FDSPrimitiveBase
{
	FDSPrimitive_Polygon3D() : FDSPrimitiveBase(EDSPrimitiveType::PrimitiveType_Polygon3D) {};

	TArray<FVector> Outter;

	TArray<TArray<FVector>> Holes;

	bool IsVaild() const override
	{
		return Outter.Num() > 0;
	}

	virtual FBox GetBox() const override
	{
		return FBox(Outter);
	}

	virtual void Transform(const FTransform& InTransform) override
	{
		for (auto& Point : Outter)
		{
			Point = InTransform.TransformPosition(Point);
		}
		for (auto& Hole : Holes)
		{
			for (auto& Point : Hole)
			{
				Point = InTransform.TransformPosition(Point);
			}
		}
	}

	virtual TSharedPtr<FDSPrimitiveBase> NewCopy() override
	{
		FDSPrimitive_Polygon3D* NewPrimitive = new FDSPrimitive_Polygon3D();
		*NewPrimitive = *this;
		return TSharedPtr<FDSPrimitive_Polygon3D>(NewPrimitive);
	}

};

struct FDSPrimitive_MPolygon3D : public FDSPrimitiveBase
{
	FDSPrimitive_MPolygon3D() : FDSPrimitiveBase(EDSPrimitiveType::PrimitiveType_MPolygon3D) {};

	TArray<FDSPrimitive_Polygon3D> Polygons;

	FVector FaceNormal = FVector::ZeroVector;

	int32 FaceIndex = 0;   //调试数据

	bool IsVaild() const override
	{
		return Polygons.Num() > 0 && Polygons[0].IsVaild();
	}

	virtual FBox GetBox() const override
	{
		FBox TotalBox;
		for (auto& Ite : Polygons)
		{
			TotalBox += Ite.GetBox();
		}
		return TotalBox;
	}

	void GetPoints(TArray<TArray<FVector>>& OutPoints)
	{
		for (auto& Ite : Polygons)
		{
			OutPoints.Add(Ite.Outter);
			for (auto& HoleIte : Ite.Holes)
			{
				OutPoints.Add(HoleIte);
			}
		}
	}

	virtual void Transform(const FTransform& InTransform) override
	{
		for (auto& Polygon : Polygons)
		{
			Polygon.Transform(InTransform);
		}
		FaceNormal = InTransform.TransformVector(FaceNormal).GetSafeNormal();
	}

	virtual TSharedPtr<FDSPrimitiveBase> NewCopy() override
	{
		FDSPrimitive_MPolygon3D* NewPrimitive = new FDSPrimitive_MPolygon3D();
		*NewPrimitive = *this;
		return TSharedPtr<FDSPrimitive_MPolygon3D>(NewPrimitive);
	}
};


struct FDSPrimitive_MPolygonHatch : public FDSPrimitiveBase
{
	FDSPrimitive_MPolygonHatch() : FDSPrimitiveBase(EDSPrimitiveType::PrimitiveType_MPolygon_Hatch) {};

	FDSPrimitive_MPolygon MPolygon; //多边形顶点列表

	bool IsVaild() const override
	{
		return MPolygon.IsVaild();
	}

	virtual FBox GetBox() const override
	{
		return MPolygon.GetBox();
	}

	virtual void Transform(const FTransform& InTransform) override
	{
		MPolygon.Transform(InTransform);
	}

	virtual TSharedPtr<FDSPrimitiveBase> NewCopy() override
	{
		FDSPrimitive_MPolygonHatch* NewPrimitive = new FDSPrimitive_MPolygonHatch();
		*NewPrimitive = *this;
		return TSharedPtr<FDSPrimitive_MPolygonHatch>(NewPrimitive);
	}
};

struct FDSConstructionData
{
public:
	FString ParentUUID;   //定制柜根节点的Model的UUID
	FString SelfUUID;   //Model的UUID或者NodeID
	TWeakObjectPtr<class UDSBaseModel> Model;        //模型指针，可能为空，一些柜子的子部件没有Model
	EDSModelType ModelType = EDSModelType::E_None;
	TSharedPtr<FMultiComponentDataItem> Node;
	FTransform LocalToWorld;
	//FVector DepthDirInProj = FVector::ZeroVector;    //在图纸投影空间中，子部件的深度朝向
	FString AreaUUID;   //所在区域的UUID

	TArray<TSharedPtr<FDSPrimitiveBase>> Primitives; //图形原始数据

	FDSConstructionData& DeepCopy(const FDSConstructionData& InB)
	{
		ParentUUID = InB.ParentUUID;
		SelfUUID = InB.SelfUUID;
		Model = InB.Model;
		ModelType = InB.ModelType;
		Node = InB.Node;
		LocalToWorld = InB.LocalToWorld;
		//DepthDirInProj = InB.DepthDirInProj;
		AreaUUID = InB.AreaUUID;

		Primitives.Empty();
		for (auto BIte : InB.Primitives)
		{
			Primitives.Add(BIte->NewCopy());
		}

		return *this;
	}

	/*bool HasMPolygonPrimitive() const;*/
	//TSharedPtr<FDSPrimitive_MPolygon> GetMPolygonPrimitive() const;
	float GetPrimitiveMaxZ() const;   //得到图元最大的Z值

	float GetPrimitiveArea() const;   //得到图元总面积

	bool IsHasVaildPrimitive() const;

	bool bIsCupboardDoor() const;  //是不是柜门节点

public:
	const FString& GetUUID() const { return SelfUUID; }

	const EDSModelType GetModelType() const { return ModelType; }

	void GetAABB(FVector& MinPoint, FVector& MaxPoint);

	FBox GetAABB() const;
};


