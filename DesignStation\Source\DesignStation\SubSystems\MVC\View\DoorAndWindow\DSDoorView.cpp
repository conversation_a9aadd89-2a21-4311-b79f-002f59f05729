﻿#pragma once

#include "DSDoorView.h"

#include "MaterialInstance.h"
#include "MaterialInstanceDynamic.h"
#include "SubSystems/MVC/Core/Macro.h"
#include "SubSystems/MVC/Library/DSToolLibrary.h"
#include "ProceduralMeshComponent.h"
#include "Engine/World.h"
#include "SubSystems/MVC/Core/Property/CommonProperty.h"
#include "SubSystems/MVC/Core/Property/DoorAndWindowProperty.h"
#include "SubSystems/Drawing/DSDrawingSubsystem.h"
#include "SubSystems/MVC/Model/DoorAndWindow/DSDoorModel.h"
#include "SubSystems/MVC/View/Abstruct/DSPlaneView.h"

extern const FString PARAM_H_STR;
extern const FString PARAM_W_STR;
extern const FString PARAM_D_STR;
extern const FString PARAM_LDH_STR;

extern const FLinearColor sRGB_585858;
extern const FLinearColor sRGB_4191F8;
extern const FLinearColor sRGB_f96466;

extern const FString MESH_DOOR_BODY_2D;
extern const FString MESH_DOOR_BOARD_2D;
extern const FString MESH_DOOR_BOARD_2D_1;

ADSDoorView::ADSDoorView()
{
	BoardComponent = CreateDefaultSubobject<UProceduralMeshComponent>(TEXT("BoardComponent"));
	BoardComponent->AttachToComponent(RootComponent, FAttachmentTransformRules::KeepRelativeTransform);
	BoardComponent->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);

	BoardComponent1 = CreateDefaultSubobject<UProceduralMeshComponent>(TEXT("BoardComponent1"));
	BoardComponent1->AttachToComponent(RootComponent, FAttachmentTransformRules::KeepRelativeTransform);
	BoardComponent1->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);

	Z2D = 500.f;
}

void ADSDoorView::RealSpawnViewLogic(UDSBaseModel* InModel)
{
	Super::RealSpawnViewLogic(InModel);

	Model = InModel;
	auto Property = static_cast<FDSDoorAndWindowProperty*>(Model->GetProperty());

	auto Mesh = Model->GetMeshInfo().MeshDatas[MESH_DOOR_BODY_2D];

	TArray<FProcMeshTangent> Tangents;
	Tangents.SetNumZeroed(Mesh.Vertex.Num());
	TArray<FColor> VertexColor;
	MeshComponent->ClearMeshSection(0);
	MeshComponent->CreateMeshSection(0, Mesh.Vertex, Mesh.Indices, Mesh.Normals, Mesh.Uvs, VertexColor, Tangents, true);

	MeshComponent->SetRelativeLocation(FVector(0, 0, Z2D));
	BoardComponent->ClearMeshSection(0);
	BoardComponent1->ClearMeshSection(0);

	MeshComponent->SetVisibility(true);
	BoardComponent->SetVisibility(true);
	BoardComponent1->SetVisibility(true);

	if (Property->DoorAndWindowType == EDoorAndWindowType::E_Door_One || Property->DoorAndWindowType == EDoorAndWindowType::E_Door_Two)
	{
		auto BoardMesh = Model->GetMeshInfo().MeshDatas[MESH_DOOR_BOARD_2D];

		BoardComponent->CreateMeshSection(0, BoardMesh.Vertex, BoardMesh.Indices, BoardMesh.Normals, BoardMesh.Uvs, VertexColor, Tangents, true);
		BoardComponent->SetRelativeLocation(FVector(0, 0, Z2D));
	}

	if (Property->DoorAndWindowType == EDoorAndWindowType::E_Door_Two)
	{
		auto BoardMesh1 = Model->GetMeshInfo().MeshDatas[MESH_DOOR_BOARD_2D_1];

		Tangents.SetNumZeroed(Mesh.Vertex.Num());
		BoardComponent1->CreateMeshSection(0, BoardMesh1.Vertex, BoardMesh1.Indices, BoardMesh1.Normals, BoardMesh1.Uvs, VertexColor, Tangents, true);
		BoardComponent1->SetRelativeLocation(FVector(0, 0, Z2D));
	}
	RefreshAllMaterial();
	UDSDrawingSubsystem::GetInstance()->AddModel(Model, sRGB_f96466);
}

void ADSDoorView::RealUpdateViewLogic(UDSBaseModel* InModel)
{
	UDSDoorModel* NewModel = Cast<UDSDoorModel>(InModel);
	if (OBJECT_VALID_FOR_USE(NewModel) && OBJECT_VALID_FOR_USE(Model))
	{
		checkf(Model->GetUUID().Equals(NewModel->GetUUID()), TEXT("ADSMoldingCeilingView::UpdateView --- No Equal Model"));

		Model->ShallowCopy(NewModel);

		RefreshMesh();

		GetModel()->GetProperty()->SetTransformProperty(InModel->GetProperty()->GetTransformProperty());
		SetActorTransform(GetModel()->GetProperty()->GetTransformProperty().ToUETransform());

		RefreshCupboard();
		RefreshAllMaterial();
	}
}

void ADSDoorView::RealTransformViewLogic(UDSBaseModel* InModel)
{
	Super::RealTransformViewLogic(InModel);

	RefreshMesh();
	RefreshCupboardTransform();
	RefreshAllMaterial();
}

void ADSDoorView::RealHoverViewLogic(UDSBaseModel* InModel)
{
	RefreshAllMaterial();
}

void ADSDoorView::RealUnHoverViewLogic(UDSBaseModel* InModel)
{
	RefreshAllMaterial();
}

void ADSDoorView::RealSelectViewLogic(UDSBaseModel* InModel)
{
	RefreshAllMaterial();
}

void ADSDoorView::RealUnSelectViewLogic(UDSBaseModel* InModel)
{
	RefreshAllMaterial();
}

void ADSDoorView::RealRefreshViewMaterialLogic(UDSBaseModel* InModel)
{
	TArray<AActor*> AttachedActors;
	this->GetAttachedActors(AttachedActors);
	for (auto& Cmp : AttachedActors)
	{
		auto Plane = static_cast<ADSPlaneView*>(Cmp);
		if (Plane)
		{
			Plane->RealRefreshViewMaterialLogic(Plane->GetModel());
		}
	}
}

void ADSDoorView::RealGenerateViewMeshLogic(UDSBaseModel* InModel)
{
}

void ADSDoorView::RealHiddenViewLogic(UDSBaseModel* InModel)
{
	MeshComponent->SetVisibility(false);
	BoardComponent->SetVisibility(false);
	BoardComponent1->SetVisibility(false);
}

void ADSDoorView::RealUnHiddenViewLogic(UDSBaseModel* InModel)
{
	MeshComponent->SetVisibility(true);
	BoardComponent->SetVisibility(true);
	BoardComponent1->SetVisibility(true);
}

void ADSDoorView::RefreshMesh()
{
	if (OBJECT_VALID_FOR_USE(Model))
	{
		auto Property = static_cast<FDSDoorAndWindowProperty*>(Model->GetProperty());
		auto Mesh = Model->GetMeshInfo().MeshDatas[MESH_DOOR_BODY_2D];
		SetActorLocation(Property->GetTransformProperty().Location);
		TArray<FProcMeshTangent> Tangents;
		Tangents.SetNumZeroed(Mesh.Vertex.Num());
		TArray<FColor> VertexColor;
		MeshComponent->ClearMeshSection(0);
		MeshComponent->CreateMeshSection(0, Mesh.Vertex, Mesh.Indices, Mesh.Normals, Mesh.Uvs, VertexColor, Tangents, true);

		MeshComponent->SetRelativeLocation(FVector(0, 0, Z2D));
		BoardComponent->ClearMeshSection(0);
		BoardComponent1->ClearMeshSection(0);
		if (Property->DoorAndWindowType == EDoorAndWindowType::E_Door_One || Property->DoorAndWindowType == EDoorAndWindowType::E_Door_Two)
		{
			auto BoardMesh = Model->GetMeshInfo().MeshDatas[MESH_DOOR_BOARD_2D];
			BoardComponent->CreateMeshSection(0, BoardMesh.Vertex, BoardMesh.Indices, BoardMesh.Normals, BoardMesh.Uvs, VertexColor, Tangents, true);
			BoardComponent->SetRelativeLocation(FVector(0, 0, Z2D));
		}
		if (Property->DoorAndWindowType == EDoorAndWindowType::E_Door_Two)
		{
			auto BoardMesh1 = Model->GetMeshInfo().MeshDatas[MESH_DOOR_BOARD_2D_1];

			Tangents.SetNumZeroed(Mesh.Vertex.Num());

			BoardComponent1->CreateMeshSection(0, BoardMesh1.Vertex, BoardMesh1.Indices, BoardMesh1.Normals, BoardMesh1.Uvs, VertexColor, Tangents, true);
			BoardComponent1->SetRelativeLocation(FVector(0, 0, Z2D));
		}

		RefreshAllMaterial();
	}
}

void ADSDoorView::RefreshMaterial(const int32& InIndex)
{
	Super::RefreshMaterial(InIndex);
	ECameraType CameraType = UDSToolLibrary::GetCameraType();
	if (MeshComponent && InIndex == 0)
	{
		MeshComponent->SetMaterial(0, GetMaterialInstanceByType(InIndex, CameraType == ECameraType::EXYPlan2D || CameraType == ECameraType::EXYPlan2D_Ceil));
	}
	if (BoardComponent && InIndex == 1)
	{
		BoardComponent->SetMaterial(0, GetMaterialInstanceByType(InIndex, CameraType == ECameraType::EXYPlan2D || CameraType == ECameraType::EXYPlan2D_Ceil));
	}
	if (BoardComponent1 && InIndex == 2)
	{
		BoardComponent1->SetMaterial(0, GetMaterialInstanceByType(InIndex, CameraType == ECameraType::EXYPlan2D || CameraType == ECameraType::EXYPlan2D_Ceil));
	}
}

void ADSDoorView::RefreshAllMaterial()
{
	RefreshMaterial(0);
	RefreshMaterial(1);
	RefreshMaterial(2);
}

void ADSDoorView::Init()
{
}

void ADSDoorView::RealDeleteViewLogic(UDSBaseModel* InModel)
{
	Super::RealDeleteViewLogic(InModel);

	if (BoardComponent)
	{
		BoardComponent->SetCollisionEnabled(ECollisionEnabled::NoCollision);
		BoardComponent->SetVisibility(false);
	}
}
