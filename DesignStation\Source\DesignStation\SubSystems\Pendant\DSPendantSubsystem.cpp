﻿#include "DSPendantSubsystem.h"

#include "BasicClasses/DesignStationController.h"
#include "Engine/World.h"
#include "SubSystems/MVC/DSMVCSubsystem.h"
#include "SubSystems/MVC/Core/Macro.h"

#include "Subsystems/MVC/Model/Gizmo/DSGizmoModel.h"

#include "Subsystems/MVC/Core/Property/GizmoProperty.h"
#include "Subsystems/MVC/Core/Property/HouseAreaProperty.h"
#include "Subsystems/MVC/Core/Property/HousePathProperty.h"
#include "Subsystems/MVC/Core/Property/RulerProperty.h"
#include "Subsystems/MVC/Model/Pendant/Ruler/DSRulerDisplayerModel.h"
#include "SubSystems/MVC/Model/Pendant/Scale/DSScale2DForPathModel.h"
#include "SubSystems/MVC/View/Pendant/Scale/DSScale2DForPathView.h"

#include "SubSystems/MVC/Library/DSPathLibrary.h"
#include "SubSystems/MVC/Model/Custom/Library/DSCupboardLibrary.h"
#include "SubSystems/MVC/Model/Pendant/Ruler/DSRulerModel.h"
#include "SubSystems/MVC/StateMachine/Core/DSSMCore.h"
#include "SubSystems/MVC/StateMachine/State/Public/DSFSMCounterTop.h"

extern const double RULER_OFFSET;

UDSPendantSubsystem* UDSPendantSubsystem::Instance = nullptr;

#define DEINIT_DS_MODEL_VIEW(Model, View) \
if (DS_MODEL_VALID_FOR_USE(Model)) \
{ \
	Model->UnBindDelegates(); \
	Model->Clear(); \
	Model = nullptr; \
} \
if (IS_ACTOR_PTR_VALID(View)) \
{ \
	View->SetActorHiddenInGame(true); \
	View->SetActorEnableCollision(false); \
	View->Destroy(); \
}

#define INIT_DS_MODEL(Model, ModelClassType, ModelType) \
Model = NewObject<ModelClassType>(); \
Model->SetModelType(ModelType); \
Model->OnExecuteAction(FDSModelExecuteType::ExecuteSpawn)

#define INIT_DS_MODEL_VIEW(Model, View, ViewType) \
if (View == nullptr) \
{ \
	View = GWorld->SpawnActor<ViewType>(); \
	Model->GetModelViewExecuteDelegate().BindUFunction(View, FName(TEXT("OnModelExecuteHandle"))); \
}\
//else\
//{ \
//	Model##->RefreshComponents(); \
//}

#define REFRESH_DS_VIEW_DATA_SHOW(TargetModel, Model, View, bShow) \
Model->SetTargetModel(bShow ? TargetModel : nullptr); \
Model->RefreshComponents(); \
if (View) \
{ \
	View->Show(bShow); \
	View->SetCollision(bShow); \
} \
if(bShow)\
{\
	View->RealSpawnViewLogic(Model); \
}

UDSPendantSubsystem::UDSPendantSubsystem()
{
	Instance = this;
	bIsGizmoEnabled = true;
}

bool UDSPendantSubsystem::IsInitialized()
{
	return Instance != nullptr;
}

UDSPendantSubsystem* UDSPendantSubsystem::GetInstance()
{
	return Instance;
}

void UDSPendantSubsystem::Deinitialize()
{
	DeinitPendant();

	Super::Deinitialize();
}

void UDSPendantSubsystem::InitPendant()
{
	if (UDSMVCSubsystem::IsInitialized())
	{
		GizmoModel = NewObject<UDSGizmoModel>();
		UDSMVCSubsystem::GetInstance()->SpawnViewUnion(GizmoModel);

		GizmoModel->SetNoNewGenerate();
	}

	//ruler
	InitRuler();
}

UDSGizmoModel* UDSPendantSubsystem::GetGizmoModel() const
{
	return GizmoModel;
}

void UDSPendantSubsystem::SwitchGizmoType(const EDSGizmoModeType& InType)
{
	if (IsValid(GizmoModel) && IsValid(GizmoModel->GetTargetModel()) && bIsGizmoEnabled)
	{
		GizmoRecordData.SetPostRecordData(GizmoModel->GetTargetModel(), InType);
		GizmoModel->SwitchGizmoType(InType);
	}
}

void UDSPendantSubsystem::RecoverGizmoShow(UDSBaseModel* InModel)
{
	if (IsValid(GizmoModel) && bIsGizmoEnabled)
	{
		GizmoRecordData.ResetRecordData();
		TPair<UDSBaseModel*, EDSGizmoModeType> RecoverData = GizmoRecordData.GetPostRecordData();
		if (IsValid(RecoverData.Key) && RecoverData.Key == InModel)
		{
			GizmoModel->SetTargetModel(RecoverData.Key);
			GizmoModel->SwitchGizmoType(RecoverData.Value);
		}
	}
}

void UDSPendantSubsystem::SetGizmoIsEnabled(bool bIsEnabled)
{
	if (bIsGizmoEnabled && !bIsEnabled)
	{
		RefreshGizmo(nullptr);
	}

	bIsGizmoEnabled = bIsEnabled;
}

bool UDSPendantSubsystem::DoesSupportWorldType(const EWorldType::Type WorldType) const
{
	return WorldType == EWorldType::Game || WorldType == EWorldType::PIE;
}

void UDSPendantSubsystem::RefreshPendant(UDSBaseModel* InModel, const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& InMarkData)
{
	UDSFiniteState* FiniteState = UDSMVCSubsystem::GetInstance()->GetState();
	if (FiniteState == nullptr)
	{
		return;
	}

	if (InExecuteType.IsDeleteExecute())
	{
		HidePendant(true, true, true);
		return;
	}

	switch (FiniteState->GetState())
	{
	case EDSFSMState::FSM_CounterTop:
		{
			RefreshLayoutRuler(false);
			UDSFSMCounterTop* CounterTopState = Cast<UDSFSMCounterTop>(FiniteState);
			if (CounterTopState == nullptr)
			{
				break;
			}

			if (!FDSModelExecuteType::IgnoreExecuteForCounterTop(InExecuteType))
			{
				bool bIsCounterTopOrOperator = (InModel != nullptr) && (InModel->GetModelType() == EDSModelType::E_Generated_CounterTop ||
												InModel->GetModelType() == EDSModelType::E_Generated_CounterTop_Line || InModel->GetModelType() == EDSModelType::E_Generated_CounterTop_Point);
				
				if (InModel != nullptr && bIsCounterTopOrOperator && CounterTopState->GetPaintingType() == ECounterTopPaintingType::Free && CounterTopState->GetFreePaintingMode() == ECounterTopFreePaintingMode::None)
				{
					if (InExecuteType.IsUnHoverExecute())
					{
						InModel = UDSMVCSubsystem::GetInstance()->GetCurrentModel();
					}
					else
					{
						InModel = FDSModelExecuteType::ResetExecuteForCounterTop(InExecuteType) ? nullptr : InModel;		
					}
					
					OnPendantEntrance_Show(InModel, InMarkData, ADesignStationController::Get()->Is2DScene());
				}
			}
		}
		break;
	case EDSFSMState::FSM_Sink:
	case EDSFSMState::FSM_StoveEdit:
		{
			RefreshLayoutRuler(false);
			break;
		}
	default:
		{
			if (FDSModelExecuteType::IgnorePendantExecute(InExecuteType))
			{
				break;
			}
			
			if (UDSToolLibrary::IsCustomType(InModel))
			{
				TSharedPtr<FDSBroadcastMarkData> CopiedMarkData = MakeShared<FDSBroadcastMarkData>(*InMarkData);
				InModel = FDSModelExecuteType::ResetPendantExecute(InExecuteType) ? nullptr : InModel;
				if (InModel != nullptr && !UDSToolLibrary::ModelIsSelf(InModel))
				{
					RefreshGizmo(nullptr);
					CopiedMarkData->DisablePendantRefresh(ERefreshPendantFlags::RPF_RefreshGizmo);
				}

				OnPendantEntrance_Show(InModel, CopiedMarkData, ADesignStationController::Get()->Is2DScene());

				RefreshLayoutRuler(InModel == nullptr && ADesignStationController::Get()->Is2DScene() && !FSlateApplication::Get().IsDragDropping());
			}
			else if (UDSToolLibrary::ModelIsSelf(InModel) && !UDSToolLibrary::ModelIsComponent(InModel))
			{
				InModel = FDSModelExecuteType::ResetPendantExecute(InExecuteType) ? nullptr : InModel;
				OnPendantEntrance_Show(InModel, InMarkData, ADesignStationController::Get()->Is2DScene());

				RefreshLayoutRuler(InModel == nullptr && ADesignStationController::Get()->Is2DScene() && !FSlateApplication::Get().IsDragDropping());
			}
		}
		break;
	}
}

void UDSPendantSubsystem::HidePendant(bool bHideGizmo, bool bHidePathScaler, bool bHideRuler)
{
	if (bHideGizmo)
	{
		RefreshGizmo(nullptr, true);
	}

	if (bHidePathScaler)
	{
		RefreshScaleForPath(nullptr);
	}

	if (bHideRuler)
	{
		RefreshLayoutRuler(false);
	}
}

void UDSPendantSubsystem::OnPendantEntrance_Move(UDSBaseModel* InModel, const TSharedPtr<FDSBroadcastMarkData>& InMarkData, bool Is2D)
{
	TargetModel = InModel;

	// Ignore initialization of GizmoModel.
	if (bIsGizmoEnabled && InMarkData->ShouldRefreshGizmo() && InModel != GizmoModel)
	{
		RefreshGizmo(InModel);
	}

	if (InMarkData->ShouldRefreshRuler())
	{
		if (InModel != nullptr && InModel->GetTargetModel() != nullptr)
		{
			TargetModel = InModel->GetTargetModel();
		}

		RefreshRuler(TargetModel, Is2D);
	}
}

void UDSPendantSubsystem::OnPendantEntrance_Show(UDSBaseModel* InModel, const TSharedPtr<FDSBroadcastMarkData>& InMarkData, bool Is2D, bool IsForceRefresh /*= false*/)
{
	UE_LOG(LogTemp, Verbose, TEXT("OnPendantEntrance_Show"));

	static TArray<EDSModelType> HouseModelFilter = { EDSModelType::E_House_Wall, EDSModelType::E_House_Beam, EDSModelType::E_House_Platform, EDSModelType::E_House_Door, EDSModelType::E_House_Window };
	
	if (InModel != nullptr && InMarkData->ShouldRefreshPathScaler() && HouseModelFilter.Contains(InModel->GetModelType()))
	{
		RefreshScaleForPath(InModel);
	}
	else if (bIsGizmoEnabled && InMarkData->ShouldRefreshGizmo() && InModel != GizmoModel)
	{
		// Ignore initialization of GizmoModel.
		RefreshGizmo(InModel, IsForceRefresh);
		RefreshScaleForPath(nullptr);
	}
	
	TargetModel = InModel;

	if (InMarkData->ShouldRefreshRuler())
	{
		if (InModel != nullptr && InModel->GetTargetModel() != nullptr)
		{
			TargetModel = InModel->GetTargetModel();
		}

		//ruler
		if (TargetModel != GizmoModel)
		{
			RefreshRuler(TargetModel, Is2D);
		}
	}
}

void UDSPendantSubsystem::DeinitPendant()
{
	//ruler
	DeinitRuler();
}

void UDSPendantSubsystem::RefreshGizmo(UDSBaseModel* InModel, bool bForceRefresh /*= false*/)
{
	TSharedPtr<FDSGizmoProperty> GizmoProperty = StaticCastSharedPtr<FDSGizmoProperty>(GizmoModel->GetPropertySharedPtr());
	if (!GizmoProperty)
	{
		return;
	}

	if (GizmoProperty->OperationType != EDSGizmoOperationType::OT_None && (InModel != GizmoModel->GetTargetModel()))
	{
		return;
	}

	if (InModel != nullptr && (GizmoProperty->OperationType == EDSGizmoOperationType::OT_None || GizmoProperty->ModeType ==  EDSGizmoModeType::GMT_Rotate_Scale_2D || GizmoProperty->ModeType ==  EDSGizmoModeType::GMT_Scale_2D))
	{
		if (TSharedPtr<FDSBaseProperty> TargetProperty = InModel->GetPropertySharedPtr())
		{
			FTransform TargetTransform = TargetProperty->GetActualTransform();
			GizmoProperty->TransformProperty.Location = TargetTransform.TransformPosition(TargetProperty->GizmoStatusProperty.PivotOffset);
			GizmoModel->OnExecuteAction(FDSModelExecuteType::ExecuteTransform, FDSBroadcastMarkData::NotBroadcastToMVCMark);
		}
	}

	UDSBaseModel* PreModel = GizmoModel->GetTargetModel();
	GizmoModel->SetTargetModel(InModel);
	auto ModelProperty = InModel != nullptr ? InModel->GetTypedProperty<FDSBaseProperty>() : nullptr;
	ADesignStationController* Controller = ADesignStationController::Get();
	if (Controller == nullptr || InModel == nullptr)
	{
		GizmoProperty->ModeType = EDSGizmoModeType::GMT_None;
	}
	else
	{
		if (bForceRefresh)
		{
			GizmoProperty->ModeType = InModel->GetDefaultGizmoModeType(Controller->Is2DScene());
			GizmoRecordData.SetPostRecordData(InModel, GizmoProperty->ModeType);
		}
		else if (InModel->IsHasModelFlag(EModelState::E_Dragging))
		{
			GizmoProperty->ModeType = EDSGizmoModeType::GMT_None;
		}
		else
		{
			if (PreModel != InModel)
			{
				GizmoProperty->ModeType = InModel->GetDefaultGizmoModeType(Controller->Is2DScene());
				GizmoRecordData.SetPostRecordData(InModel, GizmoProperty->ModeType);
			}
			else
			{
				auto& PostData = GizmoRecordData.GetPostRecordDataRef();
				if (PostData.Key == nullptr || PostData.Value == EDSGizmoModeType::GMT_None)
				{
					PostData.Key = InModel;
					PostData.Value = InModel->GetDefaultGizmoModeType(Controller->Is2DScene());
				}
				GizmoProperty->ModeType = PostData.Value;
			}
		}
	}
	GizmoModel->OnExecuteAction(FDSModelExecuteType::ExecuteAll, FDSBroadcastMarkData::NotBroadcastToMVCMark);
}

void UDSPendantSubsystem::RefreshScaleForPath(UDSBaseModel* InModel)
{
	if (!Scale2DModel)
	{
		Scale2DModel = NewObject<UDSScale2DForPathModel>();
		Scale2DModel->SetModelType(EDSModelType::E_Scale2DForPath);
		Scale2DModel->OnExecuteAction(FDSModelExecuteType::ExecuteSpawn, FDSBroadcastMarkData::NotBroadcastToMVCMark);
	}

	if (Scale2DView == nullptr)
	{
		Scale2DView = GetWorld()->SpawnActor<ADSScale2DForPathView>();
		Scale2DModel->GetModelViewExecuteDelegate().BindUFunction(Scale2DView, FName(TEXT("OnModelExecuteHandle")));
	}

	ADesignStationController* Controller = ADesignStationController::Get();

	Scale2DModel->SetTargetModel(InModel != nullptr ? InModel : nullptr);
	//Scale2DModel->RefreshComponents();
	if (Scale2DView)
	{
		Scale2DView->Show(InModel != nullptr && !InModel->IsHasModelFlag(EModelState::E_Dragging) && Controller->Is2DScene());
		Scale2DView->SetCollision(InModel != nullptr && Controller->Is2DScene());
	}

	if (InModel != nullptr)
	{
		Scale2DView->RealSpawnViewLogic(Scale2DModel);
	}

}

void UDSPendantSubsystem::InitRuler()
{
	RulerModel = NewObject<UDSRulerDisplayerModel>();
	RulerModel->SetModelType(EDSModelType::E_RulerDisplayer);
	RulerModel->OnExecuteAction(FDSModelExecuteType::ExecuteSpawn, FDSBroadcastMarkData::NotBroadcastToMVCMark);

	RulerView = GetWorld()->SpawnActor<ADSRulerDisplayerView>();
	RulerModel->GetModelViewExecuteDelegate().BindUFunction(RulerView, FName(TEXT("OnModelExecuteHandle")));
	RulerView->RealSpawnViewLogic(RulerModel);
	RulerView->Show(false);
}

void UDSPendantSubsystem::DeinitRuler()
{
	if (RulerModel) 
	{ 
		RulerModel->UnBindDelegates(); 
		RulerModel->Clear(); 
		RulerModel = nullptr; 
	}
	
	if (RulerView) 
	{ 
		RulerView->SetActorHiddenInGame(true); 
		RulerView->SetActorEnableCollision(false); 
		RulerView->Destroy(); 
	}
}

void UDSPendantSubsystem::RefreshRuler(UDSBaseModel* InTargetModel, bool Is2D)
{
	//InitRuler();
	const EDSRulerState RulerState = DS_MODEL_VALID_FOR_USE(InTargetModel)
		                                 ? (Is2D
			                                    ? (InTargetModel->Has2DRuler() ? EDSRulerState::E_Show : EDSRulerState::E_None)
			                                    : (InTargetModel->Has3DRuler() ? EDSRulerState::E_Show : EDSRulerState::E_None))
		                                 : EDSRulerState::E_None;
	const bool bShow = RulerState == EDSRulerState::E_Show;
	RefreshRuler_Inner(InTargetModel, bShow);
}

void UDSPendantSubsystem::RefreshRuler_Inner(UDSBaseModel* InModel, bool IsShow)
{
	RulerModel->SetTargetModel(InModel);

	if (RulerView)
	{
		RulerView->RealUpdateViewLogic(RulerModel);

		RulerView->Show(IsShow);
		RulerView->SetCollision(IsShow);	
	}
}

void UDSPendantSubsystem::RefreshLayoutRuler(bool bShow)
{
	for (UDSBaseModel* LayoutRuler : LayoutRulers)
	{
		LayoutRuler->OnExecuteAction(FDSModelExecuteType::ExecuteHidden, FDSBroadcastMarkData::NotBroadcastToMVCMark);
	}

	EDSFSMState CurrentState = UDSMVCSubsystem::GetInstance()->GetStateType();

	if (!bShow || !ADesignStationController::Get()->Is2DScene() || CurrentState == EDSFSMState::FSM_CounterTop || CurrentState == EDSFSMState::FSM_StoveEdit || CurrentState == EDSFSMState::FSM_Sink)
	{
		return;
	}

	int32 LayoutRulerIndex = 0;

	TArray<UDSBaseModel*> LayoutModels = UDSMVCSubsystem::GetInstance()->GetModels({ EDSModelType::E_House_Wall, EDSModelType::E_House_Beam, EDSModelType::E_House_Platform });
	for (UDSBaseModel* LayoutModel : LayoutModels)
	{
		UDSBaseModel* CurrentRuler = nullptr;
		if (LayoutRulers.IsValidIndex(LayoutRulerIndex))
		{
			CurrentRuler = LayoutRulers[LayoutRulerIndex];
		}

		if (LayoutModel->GetModelType() == EDSModelType::E_House_Wall)
		{
			TSharedPtr<FDSHousePathProperty> WallProperty = LayoutModel->GetTypedProperty<FDSHousePathProperty>();
			TSharedPtr<FDSSegmentProperty> LeftSeg = WallProperty->GetLeftSegment();
			TSharedPtr<FDSSegmentProperty> RightSeg = WallProperty->GetRightSegment();
			TSharedPtr<FDSSegmentProperty> Seg = LeftSeg->GetLength() <= RightSeg->GetLength() ? LeftSeg : RightSeg;
			FVector Start = Seg->SegmentStart;
			FVector End = Seg->SegmentEnd;

			if (CurrentRuler == nullptr)
			{
				CurrentRuler = NewObject<UDSRulerModel>(this);
				LayoutRulers.Add(CurrentRuler);

				UDSMVCSubsystem::GetInstance()->SpawnViewUnion(CurrentRuler);
			}

			TSharedPtr<FDSRulerProperty> RulerProperty = CurrentRuler->GetTypedProperty<FDSRulerProperty>();

			RulerProperty->bOnlyRead = true;

			FVector Dir = (End - Start).GetSafeNormal();
			FVector Nor = FVector::CrossProduct(FVector::ZAxisVector, Dir).GetSafeNormal();
			Nor = Nor * (LeftSeg->GetLength() <= RightSeg->GetLength() ? 1 : -1);

			RulerProperty->SegmentStart = Start - Nor * RULER_OFFSET;
			RulerProperty->SegmentEnd = End - Nor * RULER_OFFSET;
			RulerProperty->bShow = RulerProperty->GetLength() >= 50;
		}
		else if (LayoutModel->GetModelType() == EDSModelType::E_House_Beam || LayoutModel->GetModelType() == EDSModelType::E_House_Platform)
		{
			TSharedPtr<FDSHousePathProperty> PathProperty = LayoutModel->GetTypedProperty<FDSHousePathProperty>();
			if (PathProperty->GetIsHidden())
			{
				continue;
			}

			if (CurrentRuler == nullptr)
			{
				CurrentRuler = NewObject<UDSRulerModel>(this);
				LayoutRulers.Add(CurrentRuler);

				UDSMVCSubsystem::GetInstance()->SpawnViewUnion(CurrentRuler);
			}

			TSharedPtr<FDSRulerProperty> RulerProperty = CurrentRuler->GetTypedProperty<FDSRulerProperty>();

			TSharedPtr<FDSSegmentProperty> LeftSeg = PathProperty->GetLeftSegment();
			TSharedPtr<FDSSegmentProperty> RightSeg = PathProperty->GetRightSegment();
			TSharedPtr<FDSSegmentProperty> Seg = LeftSeg->GetLength() <= RightSeg->GetLength() ? LeftSeg : RightSeg;

			RulerProperty->bOnlyRead = true;

			FVector Start = Seg->SegmentStart;
			FVector End = Seg->SegmentEnd;

			FVector Dir = (End - Start).GetSafeNormal();
			FVector Nor = FVector::CrossProduct(FVector::ZAxisVector, Dir).GetSafeNormal();
			Nor = Nor * (LeftSeg->GetLength() <= RightSeg->GetLength() ? 1 : -1);

			RulerProperty->SegmentStart = Start - Nor * RULER_OFFSET;
			RulerProperty->SegmentEnd = End - Nor * RULER_OFFSET;
			RulerProperty->bShow = RulerProperty->GetLength() >= 50;
		}

		++LayoutRulerIndex;

		CurrentRuler->OnExecuteAction(FDSModelExecuteType::ExecuteUnHidden, FDSBroadcastMarkData::NotBroadcastToMVCMark);
		CurrentRuler->OnExecuteAction(FDSModelExecuteType::ExecuteAll, FDSBroadcastMarkData::NotBroadcastToMVCMark);
	}

	while (LayoutRulers.IsValidIndex(LayoutRulerIndex))
	{
		UDSBaseModel* CurrentRuler = LayoutRulers.Pop();
		CurrentRuler->OnExecuteAction(FDSModelExecuteType::ExecuteDelete, FDSBroadcastMarkData::NotBroadcastToMVCMark);
	}
}
