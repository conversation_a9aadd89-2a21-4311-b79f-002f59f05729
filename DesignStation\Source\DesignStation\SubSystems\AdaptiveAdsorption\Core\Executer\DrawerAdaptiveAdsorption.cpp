
#include "DrawerAdaptiveAdsorption.h"
#include "SubSystems/AdaptiveAdsorption/Core/Operator/FunctionalAdaptationOperator.h"
#include "SubSystems/AdaptiveAdsorption/Core/Operator/DrawerAdaptationOperator.h"
#include "SubSystems/MVC/Model/Custom/DSCupboardModel.h"


DEFINE_LOG_CATEGORY(DrawerAdaptiveAdsorptionLog);

void FDrawerAdaptiveAdsorption::HandleAdaptive(const TArray<TSharedPtr<FIntersectionDynamicMesh>>& Env, const TSharedPtr<FIntersectionDynamicMesh>& HitEnv, FOrientedBox3d& OutExecutedBox)
{
	//FDynamicMeshAdaptiveAdsorption::HandleAdaptive(Env, HitEnv, OutExecutedBox);
}

void FDrawerAdaptiveAdsorption::GenerateRay(FVector& RayStart, FVector& RayDir)
{
	const FOrientedBox3d& OriBox = AdaptationOriBox;
	RayDir = -OriBox.AxisY();
	RayStart = OriBox.Center();

	FVector TempRay = RayStart;
	RayStart += (-OriBox.AxisX() * OriBox.Extents.X * 0.5 - OriBox.AxisY() * OriBox.Extents.Y * 0.5);
	UE_LOG(DrawerAdaptiveAdsorptionLog, Warning, TEXT("GenerateRay --- Ray Start1 [%s]"), *RayStart.ToString());
	if (TSharedPtr<FFunctionalExecuterInitializedData> InitData = StaticCastSharedPtr<FFunctionalExecuterInitializedData>(InitializedData))
	{
		FOrientedBox3d CabinetOriBox = InitData->CabinetOriBox;
		if (!CabinetOriBox.Contains(RayStart))
		{
			FVector CabinetCenter = CabinetOriBox.Center();
			FVector Shift = CabinetCenter - OriBox.Center();

			RayStart += (FVector::DotProduct(Shift, OriBox.AxisX()) * OriBox.AxisX() - CabinetOriBox.AxisX() * CabinetOriBox.Extents.X * 0.5);
			RayStart += (FVector::DotProduct(Shift, OriBox.AxisY()) * OriBox.AxisY() - CabinetOriBox.AxisY() * CabinetOriBox.Extents.Y * 0.5);

			UE_LOG(DrawerAdaptiveAdsorptionLog, Warning, TEXT("GenerateRay --- Ray Start2 [%s]"), *RayStart.ToString());
		}
		
	}

	//DrawDebugLine(GWorld, RayStart, RayStart + RayDir * 1000.f, FColor::Green);
}

FAdaptationEvenInfo FDrawerAdaptiveAdsorption::ExecuteEven(const TArray<TSharedPtr<FIntersectionDynamicMesh>>& Env)
{
	const FOrientedBox3d& OutAdaptiveOriBox = OutAdaptationData->OriBox;
	FAdaptationEvenInfo EvenInfo;
	EvenInfo.SourceExtents = OutAdaptiveOriBox.Extents;
    EvenInfo.SourceBox = OutAdaptiveOriBox;

	bool bAdaptiveValid = XNegativeAdsorption.IsValid() && XPositiveAdsorption.IsValid() 
		&& ZNegativeAdsorption.IsValid() && ZPositiveAdsorption.IsValid();
	bool bRealAdaptiveValid = RealXNegativeAdsorption.IsValid() && RealXPositiveAdsorption.IsValid()
		&& RealZNegativeAdsorption.IsValid() && RealZPositiveAdsorption.IsValid();
	//if (bAdaptiveValid && bRealAdaptiveValid)
	//{	//real space box 
	//	PreHandleAdaptiveOriBox({}, FRayHitResault(), EvenInfo.SpaceBox);

	//}
	//else
	{
		FVector StartPoint = OutAdaptiveOriBox.Frame.Origin;
		FRayHitResault HitRes;
		HitRes.HitTarget = nullptr;
		HitRes.HitPoint = StartPoint;
		HitRes.HitNormal = OutAdaptiveOriBox.Frame.Y();

		EDrawerAdaptiveRelation TempXNegRelation = XNegRelation;
		EDrawerAdaptiveRelation TempXPosRelation = XPosRelation;
		EDrawerAdaptiveRelation TempZNegRelation = ZNegRelation;
		EDrawerAdaptiveRelation TempZPosRelation = ZPosRelation;

		FVector AdaptiveLoc;
		CaculateAdsorptionPoint(AdaptiveLoc, HitRes, InitializedData->Extents, Env);
		SelfSpaceAdaptive(EvenInfo.SpaceBox);
		//PreHandleAdaptiveOriBox(Env, HitRes, EvenInfo.SpaceBox);

		if (TempXNegRelation != EDrawerAdaptiveRelation::DAR_NONE)
		{
			XNegRelation = TempXNegRelation;
		}
		if (TempXPosRelation != EDrawerAdaptiveRelation::DAR_NONE)
		{
			XPosRelation = TempXPosRelation;
		}
		if (TempZNegRelation != EDrawerAdaptiveRelation::DAR_NONE)
		{
			ZNegRelation = TempZNegRelation;
		}
		if (TempZPosRelation != EDrawerAdaptiveRelation::DAR_NONE)
		{
			ZPosRelation = TempZPosRelation;
		}

	}


	//左E_Backward 右E_Forward
	EvenInfo.AddAroundEnv(EAdaptationDirection::E_Backward, RealXNegativeAdsorption.Pin());
	EvenInfo.AddAroundEnv(EAdaptationDirection::E_Forward, RealXPositiveAdsorption.Pin());

	//下E_Down  上E_UP
	EvenInfo.AddAroundEnv(EAdaptationDirection::E_Down, RealZNegativeAdsorption.Pin());
	EvenInfo.AddAroundEnv(EAdaptationDirection::E_Up, RealZPositiveAdsorption.Pin());

	return EvenInfo;
	//return FDynamicMeshAdaptiveAdsorption::ExecuteEven(Env);
}

FOrientedBox3d FDrawerAdaptiveAdsorption::GetAdaptationOriBoxWithoutAdaptiveRulerOffset()
{
	/*FOrientedBox3d OriBox = GetAdaptationOriBox();
	if (TSharedPtr<FFunctionalExecuterInitializedData> InitData = StaticCastSharedPtr<FFunctionalExecuterInitializedData>(InitializedData))
	{
		FVector ShiftOffset = FVector(1.8, 0.0, 1.8);
		OriBox.Frame.Origin = OriBox.Frame.FromFramePoint(ShiftOffset);
	}
	return OriBox;*/
	return FDynamicMeshAdaptiveAdsorption::GetAdaptationOriBoxWithoutAdaptiveRulerOffset();
}

bool FDrawerAdaptiveAdsorption::HandleAdaptiveAndAdsorptionWithDependent(const TArray<TSharedPtr<FIntersectionDynamicMesh>>& AdaptationEnvs, const TSharedPtr<struct FFunctionalDependencyInfo>& SelfDependencyInfo)
{
	return HandleAdaptiveAndAdsorption(AdaptationEnvs);
}

void FDrawerAdaptiveAdsorption::SetDrawerOperator(const TSharedPtr<FModelAdaptationOperatorBase>& InDrawerOperator)
{
	BelongOperator = StaticCastSharedPtr<FDrawerAdaptationOperator>(InDrawerOperator);
}

bool FDrawerAdaptiveAdsorption::IsDrawerNoOriginToEmbedded(const EAdaptationDirection& InDirection) const
{
	TSharedPtr<FIntersectionDynamicMesh> ConsiderTarget = nullptr;
	if (InDirection == EAdaptationDirection::E_Up)
	{//z
		ConsiderTarget = ZPositiveAdsorption.Pin();
	}
	else if (InDirection == EAdaptationDirection::E_Down)
	{//-z
        ConsiderTarget = ZNegativeAdsorption.Pin();
	}
	else if (InDirection == EAdaptationDirection::E_Forward)
	{//x
        ConsiderTarget = XPositiveAdsorption.Pin();
	}
	else if (InDirection == EAdaptationDirection::E_Backward)
	{//-x
        ConsiderTarget = XNegativeAdsorption.Pin();
	}

	if (ConsiderTarget.IsValid() && ConsiderTarget->GetLinkModelBaseInfo().OwnerModelPtr.Pin().IsValid())
	{
		if (UDSCupboardModel* DrawerModel = Cast<UDSCupboardModel>(ConsiderTarget->GetLinkModelBaseInfo().OwnerModelPtr.Get()))
		{
			if (FParameterData* Param_MYGLX = DrawerModel->GetModelInfoRef().ComponentTreeData->ComponentParameters.FindByPredicate(
				[](const FParameterData& InParam) 
				{ 
					return InParam.Data.name == TEXT("MYGLX"); 
				}
			))
			{
				bool bOriginEmbedde = FCString::Atoi(*Param_MYGLX->Data.visibility) == 0;
				bool bEmbedded = FCString::Atoi(*Param_MYGLX->Data.value) == 1;

				return bEmbedded && !bOriginEmbedde;
			}
		}
	}

	return false;
}

void FDrawerAdaptiveAdsorption::PreHandleAdaptiveOriBox(const TArray<TSharedPtr<FIntersectionDynamicMesh>>& Env, const FRayHitResault& RayHitResault, FOrientedBox3d& OutOriBox)
{
	TSharedPtr<FFunctionalExecuterInitializedData> FunctionalInitializedData = StaticCastSharedPtr<FFunctionalExecuterInitializedData>(InitializedData);
	const FOrientedBox3d IntersectionOriBox = SourceIntersectionData->GetOriBox();
	const auto& SelfFrame = IntersectionOriBox.Frame;

	FVector OutCenter = IntersectionOriBox.Center();
	FVector OutExtents = IntersectionOriBox.Extents;

	FVector MinYPoint = -UseExtents;
	FVector MaxYPoint = UseExtents;

	if (!XNegativeAdsorption.IsValid() || !XPositiveAdsorption.IsValid() || !ZNegativeAdsorption.IsValid() || !ZPositiveAdsorption.IsValid())
	{
		UE_LOG(DrawerAdaptiveAdsorptionLog, Error, TEXT("PreHandleAdaptiveOriBox -- XNeg / XPos / ZNeg / ZPos InValid"));
		return;
	}
	if(!RealXNegativeAdsorption.IsValid() || !RealXPositiveAdsorption.IsValid() || !RealZNegativeAdsorption.IsValid() || !RealZPositiveAdsorption.IsValid())
	{
		UE_LOG(DrawerAdaptiveAdsorptionLog, Error, TEXT("PreHandleAdaptiveOriBox -- RealXNeg / RealXPos / RealZNeg / RealZPos InValid"));
		return;
	}

	//X Axis
	FOrientedBox3d XNegOriBox = RealXNegativeAdsorption.Pin()->GetOriBox();  //真实OBB
	FOrientedBox3d XPosOriBox = RealXPositiveAdsorption.Pin()->GetOriBox();
	if (AdaptiveRule.XAxisRule.bAdaptived && InitializedData->bExtentsXEnableAdaptation)
	{
		/*
		 *  左右影响Y方向，后取大值，前取大值（抽屉盖在最前面）
		 *  当前延伸时，取空间前，同时关系为embedded
		 */
		if (AdaptiveRule.YAxisRule.bAdaptived && InitializedData->bExtentsYEnableAdaptation)
		{
			//XNeg
			FVector XNegMinPoint = XNegOriBox.Frame.FromFramePoint(-XNegOriBox.Extents);
			FVector XNegMaxPoint = XNegOriBox.Frame.FromFramePoint(XNegOriBox.Extents);
			XNegMinPoint = SelfFrame.ToFramePoint(XNegMinPoint);
			XNegMaxPoint = SelfFrame.ToFramePoint(XNegMaxPoint);
			if(RealXNegativeAdsorption != XNegativeAdsorption)
			{
				FOrientedBox3d SpaceXNegOriBox = XNegativeAdsorption.Pin()->GetOriBox();
				FVector SpaceXNegMinPoint = SpaceXNegOriBox.Frame.FromFramePoint(-SpaceXNegOriBox.Extents);
				FVector SpaceXNegMaxPoint = SpaceXNegOriBox.Frame.FromFramePoint(SpaceXNegOriBox.Extents);
				SpaceXNegMinPoint = SelfFrame.ToFramePoint(SpaceXNegMinPoint);
				SpaceXNegMaxPoint = SelfFrame.ToFramePoint(SpaceXNegMaxPoint);

				if((SpaceXNegMaxPoint.Y < XNegMaxPoint.Y) && !FMath::IsNearlyEqual(SpaceXNegMaxPoint.Y, XNegMaxPoint.Y, ADAPTIVE_TOLERANCE_TWO_DIGITAL))
				{//实际Y大于空间Y,有前延伸，取空间Y
					XNegMaxPoint = SpaceXNegMaxPoint;
					XNegRelation = EDrawerAdaptiveRelation::DAR_Embedded;
				}
			}

			//XPos
			FVector XPosMinPoint = XPosOriBox.Frame.FromFramePoint(-XPosOriBox.Extents);
			FVector XPosMaxPoint = XPosOriBox.Frame.FromFramePoint(XPosOriBox.Extents);
			XPosMinPoint = SelfFrame.ToFramePoint(XPosMinPoint);
			XPosMaxPoint = SelfFrame.ToFramePoint(XPosMaxPoint);
			if(RealXPositiveAdsorption != XPositiveAdsorption)
			{
				FOrientedBox3d SpaceXPosOriBox = XPositiveAdsorption.Pin()->GetOriBox();
				FVector SpaceXPosMinPoint = SpaceXPosOriBox.Frame.FromFramePoint(-SpaceXPosOriBox.Extents);
				FVector SpaceXPosMaxPoint = SpaceXPosOriBox.Frame.FromFramePoint(SpaceXPosOriBox.Extents);
				SpaceXPosMinPoint = SelfFrame.ToFramePoint(SpaceXPosMinPoint);
				SpaceXPosMaxPoint = SelfFrame.ToFramePoint(SpaceXPosMaxPoint);

				if((SpaceXPosMaxPoint.Y < XPosMaxPoint.Y) && !FMath::IsNearlyEqual(SpaceXPosMaxPoint.Y, XPosMaxPoint.Y, ADAPTIVE_TOLERANCE_TWO_DIGITAL))
				{//实际Y大于空间Y,有前延伸，取空间Y
					XPosMaxPoint = SpaceXPosMaxPoint;
					XPosRelation = EDrawerAdaptiveRelation::DAR_Embedded;
				}
			}

			MinYPoint.Y = FMath::Max3(MinYPoint.Y, XNegMinPoint.Y, XPosMinPoint.Y);
			MaxYPoint.Y = FMath::Max(XNegMaxPoint.Y, XPosMaxPoint.Y); //左右限制前沿
		}

		FVector XNegRightPoint = XNegOriBox.Frame.FromFramePoint(XNegOriBox.Extents);//左侧依赖的右表面点
		MinYPoint.X = SelfFrame.ToFramePoint(XNegRightPoint).X;

		FVector XPosLeftPoint = XPosOriBox.Frame.FromFramePoint(-XPosOriBox.Extents); //右侧依赖的左表面点
		MaxYPoint.X = SelfFrame.ToFramePoint(XPosLeftPoint).X;

		OutExtents.X = ((MaxYPoint - MinYPoint) * 0.5).X;
	}
	

	//Z Axis
	if (AdaptiveRule.ZAxisRule.bAdaptived/* && InitializedData->bExtentsZEnableAdaptation*/)
	{
		FVector TempMinYPoint = MinYPoint;
        FVector TempMaxYPoint = MaxYPoint;

		//抽屉依赖Y后口使用空间
		const FOrientedBox3d& ZNegOBB = RealZNegativeAdsorption.Pin()->GetIntersectionType() != EIntersectionDataType::E_Drawer ? RealZNegativeAdsorption.Pin()->GetOriBox() : ZNegativeAdsorption.Pin()->GetOriBox();
		const FOrientedBox3d& ZPosOBB = RealZPositiveAdsorption.Pin()->GetIntersectionType() != EIntersectionDataType::E_Drawer ? RealZPositiveAdsorption.Pin()->GetOriBox() : ZPositiveAdsorption.Pin()->GetOriBox();

		if (AdaptiveRule.YAxisRule.bAdaptived && InitializedData->bExtentsYEnableAdaptation)
		{
			//Neg
			FVector NegMinPoint = ZNegOBB.Frame.FromFramePoint(-ZNegOBB.Extents);
			FVector NegMaxPoint = ZNegOBB.Frame.FromFramePoint(ZNegOBB.Extents);
			NegMinPoint = SelfFrame.ToFramePoint(NegMinPoint);
			NegMaxPoint = SelfFrame.ToFramePoint(NegMaxPoint);

			if ((MaxYPoint.Y < NegMaxPoint.Y) && !FMath::IsNearlyZero(MaxYPoint.Y - NegMaxPoint.Y, ADAPTIVE_TOLERANCE_TWO_DIGITAL)
				&& ZNegativeAdsorption.Pin()->GetIntersectionType() != EIntersectionDataType::E_Drawer)
			{
				ZNegRelation = (ZNegRelation != EDrawerAdaptiveRelation::DAR_Hange) ? EDrawerAdaptiveRelation::DAR_Embedded : EDrawerAdaptiveRelation::DAR_Embedded_Hange;
			}

			//Pos
			FVector PosMinPoint = ZPosOBB.Frame.FromFramePoint(-ZPosOBB.Extents);
			FVector PosMaxPoint = ZPosOBB.Frame.FromFramePoint(ZPosOBB.Extents);
			PosMinPoint = SelfFrame.ToFramePoint(PosMinPoint);
			PosMaxPoint = SelfFrame.ToFramePoint(PosMaxPoint);

			MinYPoint.Y = FMath::Max3(MinYPoint.Y, NegMinPoint.Y, PosMinPoint.Y);

			if ((MaxYPoint.Y < PosMaxPoint.Y) && !FMath::IsNearlyZero(MaxYPoint.Y - PosMaxPoint.Y, ADAPTIVE_TOLERANCE_TWO_DIGITAL)
				&& ZPositiveAdsorption.Pin()->GetIntersectionType() != EIntersectionDataType::E_Drawer)
			{
				ZPosRelation = (ZPosRelation != EDrawerAdaptiveRelation::DAR_Hange) ? EDrawerAdaptiveRelation::DAR_Embedded : EDrawerAdaptiveRelation::DAR_Embedded_Hange;
			}
			//MaxYPoint.Y = FMath::Max3(MaxYPoint.Y, NegMaxPoint.Y, PosMaxPoint.Y); 
		}

		FVector ZNegUpPoint = ZNegOBB.Frame.FromFramePoint(ZNegOBB.Extents);					//下侧依赖的上表面点
		double Min_ZNegZ = SelfFrame.ToFramePoint(ZNegUpPoint).Z;
		FVector XNegDownPoint = XNegOriBox.Frame.FromFramePoint(-XNegOriBox.Extents);			//左侧依赖的下表面点
        double Min_XNegZ = SelfFrame.ToFramePoint(XNegDownPoint).Z;
        FVector XPosDownPoint = XPosOriBox.Frame.FromFramePoint(-XPosOriBox.Extents);			//右侧依赖的下表面点
		double Min_XPosZ = SelfFrame.ToFramePoint(XPosDownPoint).Z;
		MinYPoint.Z = FMath::Max3(Min_ZNegZ, Min_XNegZ, Min_XPosZ);

		FVector ZPosDownPoint = ZPosOBB.Frame.FromFramePoint(-ZPosOBB.Extents);					//上侧依赖的下表面点
        double Max_ZPosZ = SelfFrame.ToFramePoint(ZPosDownPoint).Z;
		FVector XNegUpPoint = XNegOriBox.Frame.FromFramePoint(XNegOriBox.Extents);				//左侧依赖的上表面点
        double Max_XNegZ = SelfFrame.ToFramePoint(XNegUpPoint).Z;
        FVector XPosUpPoint = XPosOriBox.Frame.FromFramePoint(XPosOriBox.Extents);				//右侧依赖的上表面点
        double Max_XPosZ = SelfFrame.ToFramePoint(XPosUpPoint).Z;
		MaxYPoint.Z = FMath::Min3(Max_ZPosZ, Max_XNegZ, Max_XPosZ);

		if (!FMath::IsNearlyZero(InitializedData->MaxExtents.Z, ADAPTIVE_TOLERANCE_TWO_DIGITAL))
		{//有极值限制时，需校验是否上表面接触
			double AdaptiveMaxZ = MinYPoint.Z + InitializedData->MaxExtents.Z * 2.0;
			if ((AdaptiveMaxZ < Max_ZPosZ) && !FMath::IsNearlyEqual(Max_ZPosZ, AdaptiveMaxZ, ADAPTIVE_TOLERANCE_TWO_DIGITAL))
			{
				ZPosRelation = EDrawerAdaptiveRelation::DAR_Normal;
			}
		}

		if (!InitializedData->bExtentsZEnableAdaptation)
		{
			MinYPoint = TempMinYPoint;
			MaxYPoint = TempMaxYPoint;
		}

		OutExtents.Z = ((MaxYPoint - MinYPoint) * 0.5).Z;
	}
	
	//柜体极值限制包围盒
	FVector CarbinetRelativeMaxPoint = SelfFrame.ToFramePoint(FunctionalInitializedData->CabinetOriBox.Frame.FromFramePoint(FunctionalInitializedData->CabinetOriBox.Extents));


	if (AdaptiveRule.XAxisRule.bAdaptived && InitializedData->bExtentsXEnableAdaptation)
		MaxYPoint.X = FMath::Min(MaxYPoint.X, CarbinetRelativeMaxPoint.X);
	if (AdaptiveRule.YAxisRule.bAdaptived && InitializedData->bExtentsYEnableAdaptation)
		MaxYPoint.Y = FMath::Min(MaxYPoint.Y, CarbinetRelativeMaxPoint.Y);
	if (AdaptiveRule.ZAxisRule.bAdaptived && InitializedData->bExtentsZEnableAdaptation)
		MaxYPoint.Z = FMath::Min(MaxYPoint.Z, CarbinetRelativeMaxPoint.Z);

	OutExtents = ((MaxYPoint - MinYPoint) / 2);

	OutCenter = SelfFrame.FromFramePoint(((MaxYPoint + MinYPoint) * 0.5));
	OutOriBox.Extents = OutExtents;
	OutOriBox.Frame.Origin = OutCenter;
	SourceIntersectionData->UpdateExtentsAndLocation(OutExtents, OutCenter);

	//UE_LOG(DrawerAdaptiveAdsorptionLog, Log, TEXT("PreHandleAdaptiveOriBox -- Center [%s] Extents [%s]"), *OutCenter.ToString(), *OutExtents.ToString());
}

void FDrawerAdaptiveAdsorption::SelfSpaceAdaptive(FOrientedBox3d& OutOriBox)
{
	TSharedPtr<FFunctionalExecuterInitializedData> FunctionalInitializedData = StaticCastSharedPtr<FFunctionalExecuterInitializedData>(InitializedData);
	const FOrientedBox3d IntersectionOriBox = SourceIntersectionData->GetOriBox();
	const auto& SelfFrame = IntersectionOriBox.Frame;

	FVector OutCenter = IntersectionOriBox.Center();
	FVector OutExtents = IntersectionOriBox.Extents;

	FVector MinYPoint = -UseExtents;
	FVector MaxYPoint = UseExtents;

	if (!XNegativeAdsorption.IsValid() || !XPositiveAdsorption.IsValid() || !ZNegativeAdsorption.IsValid() || !ZPositiveAdsorption.IsValid())
	{
		UE_LOG(DrawerAdaptiveAdsorptionLog, Error, TEXT("SelfSpaceAdaptive -- XNeg / XPos / ZNeg / ZPos InValid"));
		return;
	}

	//X Axis
	FOrientedBox3d XNegOriBox = XNegativeAdsorption.Pin()->GetOriBox();  //真实OBB
	FOrientedBox3d XPosOriBox = XPositiveAdsorption.Pin()->GetOriBox();
	//if (AdaptiveRule.XAxisRule.bAdaptived && InitializedData->bExtentsXEnableAdaptation)
	{
		/*
		 *  左右影响Y方向，后取大值，前取大值（抽屉盖在最前面）
		 *  当前延伸时，取空间前，同时关系为embedded
		 */
		if (AdaptiveRule.YAxisRule.bAdaptived && InitializedData->bExtentsYEnableAdaptation)
		{
			//XNeg
			FVector XNegMinPoint = XNegOriBox.Frame.FromFramePoint(-XNegOriBox.Extents);
			FVector XNegMaxPoint = XNegOriBox.Frame.FromFramePoint(XNegOriBox.Extents);
			XNegMinPoint = SelfFrame.ToFramePoint(XNegMinPoint);
			XNegMaxPoint = SelfFrame.ToFramePoint(XNegMaxPoint);

			//XPos
			FVector XPosMinPoint = XPosOriBox.Frame.FromFramePoint(-XPosOriBox.Extents);
			FVector XPosMaxPoint = XPosOriBox.Frame.FromFramePoint(XPosOriBox.Extents);
			XPosMinPoint = SelfFrame.ToFramePoint(XPosMinPoint);
			XPosMaxPoint = SelfFrame.ToFramePoint(XPosMaxPoint);

			MinYPoint.Y = FMath::Max3(MinYPoint.Y, XNegMinPoint.Y, XPosMinPoint.Y);
			MaxYPoint.Y = FMath::Max(XNegMaxPoint.Y, XPosMaxPoint.Y); //左右限制前沿
		}

		FVector XNegRightPoint = XNegOriBox.Frame.FromFramePoint(XNegOriBox.Extents);//左侧依赖的右表面点
		MinYPoint.X = SelfFrame.ToFramePoint(XNegRightPoint).X;

		FVector XPosLeftPoint = XPosOriBox.Frame.FromFramePoint(-XPosOriBox.Extents); //右侧依赖的左表面点
		MaxYPoint.X = SelfFrame.ToFramePoint(XPosLeftPoint).X;

		OutExtents.X = ((MaxYPoint - MinYPoint) * 0.5).X;
	}


	//Z Axis
	//if (AdaptiveRule.ZAxisRule.bAdaptived && InitializedData->bExtentsZEnableAdaptation)
	{
		//抽屉依赖Y后口使用空间
		const FOrientedBox3d& ZNegOBB = ZNegativeAdsorption.Pin()->GetOriBox();
		const FOrientedBox3d& ZPosOBB = ZPositiveAdsorption.Pin()->GetOriBox();

		if (AdaptiveRule.YAxisRule.bAdaptived && InitializedData->bExtentsYEnableAdaptation)
		{
			//Neg
			FVector NegMinPoint = ZNegOBB.Frame.FromFramePoint(-ZNegOBB.Extents);
			FVector NegMaxPoint = ZNegOBB.Frame.FromFramePoint(ZNegOBB.Extents);
			NegMinPoint = SelfFrame.ToFramePoint(NegMinPoint);
			NegMaxPoint = SelfFrame.ToFramePoint(NegMaxPoint);

			//Pos
			FVector PosMinPoint = ZPosOBB.Frame.FromFramePoint(-ZPosOBB.Extents);
			FVector PosMaxPoint = ZPosOBB.Frame.FromFramePoint(ZPosOBB.Extents);
			PosMinPoint = SelfFrame.ToFramePoint(PosMinPoint);
			PosMaxPoint = SelfFrame.ToFramePoint(PosMaxPoint);

			MinYPoint.Y = FMath::Max3(MinYPoint.Y, NegMinPoint.Y, PosMinPoint.Y);
		}

		FVector ZNegUpPoint = ZNegOBB.Frame.FromFramePoint(ZNegOBB.Extents);					//下侧依赖的上表面点
		double Min_ZNegZ = SelfFrame.ToFramePoint(ZNegUpPoint).Z;
		FVector XNegDownPoint = XNegOriBox.Frame.FromFramePoint(-XNegOriBox.Extents);			//左侧依赖的下表面点
		double Min_XNegZ = SelfFrame.ToFramePoint(XNegDownPoint).Z;
		FVector XPosDownPoint = XPosOriBox.Frame.FromFramePoint(-XPosOriBox.Extents);			//右侧依赖的下表面点
		double Min_XPosZ = SelfFrame.ToFramePoint(XPosDownPoint).Z;
		MinYPoint.Z = FMath::Max3(Min_ZNegZ, Min_XNegZ, Min_XPosZ);

		FVector ZPosDownPoint = ZPosOBB.Frame.FromFramePoint(-ZPosOBB.Extents);					//上侧依赖的下表面点
		double Max_ZPosZ = SelfFrame.ToFramePoint(ZPosDownPoint).Z;
		FVector XNegUpPoint = XNegOriBox.Frame.FromFramePoint(XNegOriBox.Extents);				//左侧依赖的上表面点
		double Max_XNegZ = SelfFrame.ToFramePoint(XNegUpPoint).Z;
		FVector XPosUpPoint = XPosOriBox.Frame.FromFramePoint(XPosOriBox.Extents);				//右侧依赖的上表面点
		double Max_XPosZ = SelfFrame.ToFramePoint(XPosUpPoint).Z;
		MaxYPoint.Z = FMath::Min3(Max_ZPosZ, Max_XNegZ, Max_XPosZ);

		OutExtents.Z = ((MaxYPoint - MinYPoint) * 0.5).Z;
	}

	//柜体极值限制包围盒
	FVector CarbinetRelativeMaxPoint = SelfFrame.ToFramePoint(FunctionalInitializedData->CabinetOriBox.Frame.FromFramePoint(FunctionalInitializedData->CabinetOriBox.Extents));


	if (AdaptiveRule.XAxisRule.bAdaptived && InitializedData->bExtentsXEnableAdaptation)
		MaxYPoint.X = FMath::Min(MaxYPoint.X, CarbinetRelativeMaxPoint.X);
	if (AdaptiveRule.YAxisRule.bAdaptived && InitializedData->bExtentsYEnableAdaptation)
		MaxYPoint.Y = FMath::Min(MaxYPoint.Y, CarbinetRelativeMaxPoint.Y);
	if (AdaptiveRule.ZAxisRule.bAdaptived && InitializedData->bExtentsZEnableAdaptation)
		MaxYPoint.Z = FMath::Min(MaxYPoint.Z, CarbinetRelativeMaxPoint.Z);

	OutExtents = ((MaxYPoint - MinYPoint) / 2);

	OutCenter = SelfFrame.FromFramePoint(((MaxYPoint + MinYPoint) * 0.5));
	OutOriBox.Extents = OutExtents;
	OutOriBox.Frame.Origin = OutCenter;

	UE_LOG(DrawerAdaptiveAdsorptionLog, Log, TEXT("Self Space OriBox -- Center [%s] Extents [%s]"), *OutCenter.ToString(), *OutExtents.ToString());

}

void FDrawerAdaptiveAdsorption::CalculateAdsorptionPoint_X(FVector& OutAdsorptionPoint, const FRayHitResault& HitResault, const FVector& InUseExtent, const TArray<TSharedPtr<FIntersectionDynamicMesh>>& Env)
{
	TSharedPtr<FFunctionalExecuterInitializedData> FunctionalInitializedData = StaticCastSharedPtr<FFunctionalExecuterInitializedData>(InitializedData);

	double XMinDistance = MAX_dbl;
	FVector StartNormal = GetIntersectionData().GetSelfFrame().ToFrameVector(HitResault.HitNormal);
	//将射线向射线方向偏移防止检测不到对象
	FVector StartPoint = HitResault.HitPoint + HitResault.HitNormal * ADAPTIVE_TOLERANCE;


	XNegRelation = EDrawerAdaptiveRelation::DAR_Normal;
	XPosRelation = EDrawerAdaptiveRelation::DAR_Normal;
	if (FMath::IsNearlyZero(StartNormal.X, ADAPTIVE_TOLERANCE))
	{//射线不在左右板上

		//XNeg
		FRayHitResault XNegResault;
		bool bNegHit = FindHitNearestPoint(StartPoint, -GetIntersectionFrame().X(), Env, XNegResault, HitResault.HitTarget);
		if (bNegHit)
		{
			SetXNegativeDependence(XNegResault.HitTarget);
			SyncRealDependence(XNegResault.HitTarget, RealXNegativeAdsorption);
			FVector XOffset = GetIntersectionFrame().FromFrameVector(FVector(-XNegResault.Distance, 0, 0) + FVector(InUseExtent.X, 0, 0));
			OutAdsorptionPoint += XOffset;
		}
		else
		{
			UE_LOG(DrawerAdaptiveAdsorptionLog, Error, TEXT("Drawer XNeg Hit NULL"));
		}


		//XPos
		FRayHitResault XPosResault;
		bool bPosHit = FindHitNearestPoint(StartPoint, GetIntersectionFrame().X(), Env, XPosResault, HitResault.HitTarget);
		if (bPosHit)
		{
			SetXPositiveDependence(XPosResault.HitTarget);
			SyncRealDependence(XPosResault.HitTarget,RealXPositiveAdsorption);
		}
		else
		{
			UE_LOG(DrawerAdaptiveAdsorptionLog, Error, TEXT("Drawer XPos Hit NULL"));
		}

	}
	else if (StartNormal.X > 0.0)
    {//射点在左板上 

		SetXNegativeDependence(HitResault.HitTarget);
		SyncRealDependence(HitResault.HitTarget, RealXNegativeAdsorption);
		FVector XOffset = GetIntersectionFrame().FromFrameVector(FVector(InUseExtent.X, 0, 0));
		OutAdsorptionPoint += XOffset;

		FRayHitResault XPosResault;
		bool bPosHit = FindHitNearestPoint(StartPoint, GetIntersectionFrame().X(), Env, XPosResault, HitResault.HitTarget);
		if (bPosHit)
		{
			SetXPositiveDependence(XPosResault.HitTarget);
			SyncRealDependence(XPosResault.HitTarget, RealXPositiveAdsorption);
		}
		else
		{
			UE_LOG(DrawerAdaptiveAdsorptionLog, Error, TEXT("Drawer XPos Hit NULL"));
		}
    }
	else if (StartNormal.X < 0.0)
	{//射点在右板上
		SetXPositiveDependence(HitResault.HitTarget);
		SyncRealDependence(HitResault.HitTarget, RealXPositiveAdsorption);

		FRayHitResault XNegResault;
		bool bNegHit = FindHitNearestPoint(StartPoint, -GetIntersectionFrame().X(), Env, XNegResault, HitResault.HitTarget);
		if (bNegHit)
		{
			SetXNegativeDependence(XNegResault.HitTarget);
			SyncRealDependence(XNegResault.HitTarget, RealXNegativeAdsorption);
			FVector XOffset = GetIntersectionFrame().FromFrameVector(FVector(-XNegResault.Distance, 0, 0) + FVector(InUseExtent.X, 0, 0));
			OutAdsorptionPoint += XOffset;
		}
		else
		{
			UE_LOG(DrawerAdaptiveAdsorptionLog, Error, TEXT("Drawer XNeg Hit NULL"));
		}
	}
	else
    {
		FString ErrorMsg = FString::Printf(TEXT("Drawer CalculateAdsorptionPoint_X -- Error StartNormal = %s"), *StartNormal.ToString());
		checkf(false, TEXT("%s"), *ErrorMsg);
    }

	if (!XNegativeAdsorption.IsValid())
	{
		XNegRelation = EDrawerAdaptiveRelation::DAR_NONE;
	}
	if (!XPositiveAdsorption.IsValid())
	{
		XPosRelation = EDrawerAdaptiveRelation::DAR_NONE;
	}
}

void FDrawerAdaptiveAdsorption::CalculateAdsorptionPoint_Y(FVector& OutAdsorptionPoint, const FRayHitResault& HitResault, const FVector& InUseExtent, const TArray<TSharedPtr<FIntersectionDynamicMesh>>& Env)
{
	TSharedPtr<FFunctionalExecuterInitializedData> FunctionalInitializedData = StaticCastSharedPtr<FFunctionalExecuterInitializedData>(InitializedData);
	FVector StartNormal = GetIntersectionData().GetSelfFrame().ToFrameVector(HitResault.HitNormal);

	//将射线向射线方向偏移防止检测不到对象
	FVector StartPoint = HitResault.HitPoint + HitResault.HitNormal * ADAPTIVE_TOLERANCE;

	YNegRelation = EDrawerAdaptiveRelation::DAR_Normal;
	YPosRelation = EDrawerAdaptiveRelation::DAR_Normal; 
	if (FMath::IsNearlyZero(StartNormal.Y, ADAPTIVE_TOLERANCE))
	{
		FRayHitResault NegResault;
		bool bHit = FindHitNearestPoint(StartPoint, -GetIntersectionFrame().Y(), Env, NegResault, HitResault.HitTarget);
		if (bHit)
		{
			SetYNegativeDependence(NegResault.HitTarget);

			FVector YOffset = GetIntersectionFrame().FromFrameVector(FVector(0, -NegResault.Distance, 0) + FVector(0, InUseExtent.Y, 0));
			OutAdsorptionPoint += YOffset;
		}
		else
		{
			UE_LOG(DrawerAdaptiveAdsorptionLog, Error, TEXT("Drawer YNeg Hit NULL"));
		}
	}
	else if (StartNormal.Y > 0.0)
	{
		SetYNegativeDependence(HitResault.HitTarget);

		FVector YOffset = GetIntersectionFrame().FromFrameVector(FVector(0, InUseExtent.Y, 0));
		OutAdsorptionPoint += YOffset;
	}
}

void FDrawerAdaptiveAdsorption::CalculateAdsorptionPoint_Z(FVector& OutAdsorptionPoint, const FRayHitResault& HitResault, const FVector& InUseExtent, const TArray<TSharedPtr<FIntersectionDynamicMesh>>& Env)
{
	TSharedPtr<FFunctionalExecuterInitializedData> FunctionalInitializedData = StaticCastSharedPtr<FFunctionalExecuterInitializedData>(InitializedData);

	FVector StartNormal = GetIntersectionData().GetSelfFrame().ToFrameVector(HitResault.HitNormal);
	//将射线向射线方向偏移防止检测不到对象
	FVector StartPoint = HitResault.HitPoint + HitResault.HitNormal * ADAPTIVE_TOLERANCE;


	double XNegPosDownMaxZ = MIN_dbl;	//左右依赖下表面最大Z轴
	double XNegPosUpperMinZ = MAX_dbl;	//左右依赖上表面最小Z轴
	if (XNegativeAdsorption.IsValid())
	{
		FVector MinPoint = XNegativeAdsorption.Pin()->GetOriBox().Frame.Origin - XNegativeAdsorption.Pin()->GetOriBox().Extents;
		FVector MaxPoint = XNegativeAdsorption.Pin()->GetOriBox().Frame.Origin + XNegativeAdsorption.Pin()->GetOriBox().Extents;
		if (XNegPosDownMaxZ <= MinPoint.Z)
		{
			XNegPosDownMaxZ = MinPoint.Z;
		}
		if (XNegPosUpperMinZ >= MaxPoint.Z)
		{
            XNegPosUpperMinZ = MaxPoint.Z;
		}
	}
	if (XPositiveAdsorption.IsValid())
	{
		FVector MinPoint = XPositiveAdsorption.Pin()->GetOriBox().Frame.Origin - XPositiveAdsorption.Pin()->GetOriBox().Extents;
        FVector MaxPoint = XPositiveAdsorption.Pin()->GetOriBox().Frame.Origin + XPositiveAdsorption.Pin()->GetOriBox().Extents;
		if (XNegPosDownMaxZ <= MinPoint.Z)
		{
			XNegPosDownMaxZ = MinPoint.Z;
		}
		if (XNegPosUpperMinZ >= MaxPoint.Z)
		{
            XNegPosUpperMinZ = MaxPoint.Z;
		}
	}

	ZNegRelation = EDrawerAdaptiveRelation::DAR_Normal;
	ZPosRelation = EDrawerAdaptiveRelation::DAR_Normal;
	if (FMath::IsNearlyZero(StartNormal.Z, ADAPTIVE_TOLERANCE))
	{//射点不在上下板件上

		//neg
		FRayHitResault NegResult;
		bool bNegHit = FindHitNearestPoint(StartPoint, -GetIntersectionFrame().Z(), Env, NegResult, HitResault.HitTarget);
		if (bNegHit)
		{
			double ShiftDistance = NegResult.Distance;
			if ((XNegPosDownMaxZ > NegResult.HitPoint.Z) && !FMath::IsNearlyEqual(XNegPosDownMaxZ, NegResult.HitPoint.Z, ADAPTIVE_TOLERANCE))
			{
				ShiftDistance -= (XNegPosDownMaxZ - NegResult.HitPoint.Z);
				ZNegRelation = EDrawerAdaptiveRelation::DAR_Hange;
			}

			SetZNegativeDependence(NegResult.HitTarget);
			SyncRealDependence(NegResult.HitTarget, RealZNegativeAdsorption);
			FVector ZOffset = GetIntersectionFrame().FromFrameVector(FVector(0, 0, -ShiftDistance) + FVector(0, 0, InUseExtent.Z));
			OutAdsorptionPoint += ZOffset;
		}
		else
		{
			UE_LOG(DrawerAdaptiveAdsorptionLog, Error, TEXT("Drawer ZNeg Hit NULL"));
		}

		//pos
		FRayHitResault PosResult;
		bool bPosHit = FindHitNearestPoint(StartPoint, GetIntersectionFrame().Z(), Env, PosResult, HitResault.HitTarget);
        if (bPosHit)
		{ 
            SetZPositiveDependence(PosResult.HitTarget);
        	SyncRealDependence(PosResult.HitTarget, RealZPositiveAdsorption);
			if ((PosResult.HitPoint.Z > XNegPosUpperMinZ) && !FMath::IsNearlyEqual(PosResult.HitPoint.Z, XNegPosUpperMinZ, ADAPTIVE_TOLERANCE_TWO_DIGITAL))
			{
				ZPosRelation = EDrawerAdaptiveRelation::DAR_Hange;
			}
		}
		else
		{
			UE_LOG(DrawerAdaptiveAdsorptionLog, Error, TEXT("Drawer ZPos Hit NULL"));
		}
	}
	else if (StartNormal.Z > 0.0)
	{//射点在下板
        SetZNegativeDependence(HitResault.HitTarget);
		SyncRealDependence(HitResault.HitTarget, RealZNegativeAdsorption);
		FVector ZOffset = GetIntersectionFrame().FromFrameVector(FVector(0, 0, InUseExtent.Z));
		OutAdsorptionPoint += ZOffset;

		FRayHitResault PosResult;
		bool bPosHit = FindHitNearestPoint(StartPoint, GetIntersectionFrame().Z(), Env, PosResult, HitResault.HitTarget);
		if (bPosHit)
		{
			SetZPositiveDependence(PosResult.HitTarget);
			SyncRealDependence(PosResult.HitTarget, RealZPositiveAdsorption);
			if ((PosResult.HitPoint.Z > XNegPosUpperMinZ) && !FMath::IsNearlyEqual(PosResult.HitPoint.Z, XNegPosUpperMinZ, ADAPTIVE_TOLERANCE_TWO_DIGITAL))
			{
				ZPosRelation = EDrawerAdaptiveRelation::DAR_Hange;
			}
		}
		else
		{
			UE_LOG(DrawerAdaptiveAdsorptionLog, Error, TEXT("Drawer ZPos Hit NULL"));
		}
	}
	else if (StartNormal.Z < 0.0)
	{//射点在上板
		SetZPositiveDependence(HitResault.HitTarget);
		SyncRealDependence(HitResault.HitTarget, RealZPositiveAdsorption);

		FRayHitResault NegResult;
		bool bNegHit = FindHitNearestPoint(StartPoint, -GetIntersectionFrame().Z(), Env, NegResult, HitResault.HitTarget);
		if (bNegHit)
		{
			SetZNegativeDependence(NegResult.HitTarget);
			SyncRealDependence(NegResult.HitTarget, RealZNegativeAdsorption);
			double ShiftDistance = NegResult.Distance;
			if ((XNegPosDownMaxZ > NegResult.HitPoint.Z) && !FMath::IsNearlyEqual(XNegPosDownMaxZ, NegResult.HitPoint.Z, ADAPTIVE_TOLERANCE_TWO_DIGITAL))
			{
				ShiftDistance -= (XNegPosDownMaxZ - NegResult.HitPoint.Z);
				ZNegRelation = EDrawerAdaptiveRelation::DAR_Hange;
			}

			FVector ZOffset = GetIntersectionFrame().FromFrameVector(FVector(0, 0, -ShiftDistance) + FVector(0, 0, InUseExtent.Z));
			OutAdsorptionPoint += ZOffset;
		}
		else
		{
			UE_LOG(DrawerAdaptiveAdsorptionLog, Error, TEXT("Drawer ZNeg Hit NULL"));
		}
	}

	if (!ZNegativeAdsorption.IsValid())
	{
		ZNegRelation = EDrawerAdaptiveRelation::DAR_NONE;
	}
	if (!ZPositiveAdsorption.IsValid())
	{
		ZPosRelation = EDrawerAdaptiveRelation::DAR_NONE;
	}
}

void FDrawerAdaptiveAdsorption::SetYNegativeDependence(const TSharedPtr<FIntersectionDynamicMesh>& InTarget)
{
	if (YNegativeAdsorption != InTarget)
	{
        YNegativeAdsorption = InTarget;
	}
}

void FDrawerAdaptiveAdsorption::SetYPositiveDependence(const TSharedPtr<FIntersectionDynamicMesh>& InTarget)
{
	if (YPositiveAdsorption != InTarget)
	{
        YPositiveAdsorption = InTarget;
	}
}

void FDrawerAdaptiveAdsorption::SyncRealDependence(const TSharedPtr<FIntersectionDynamicMesh>& InEnv, TWeakPtr<FIntersectionDynamicMesh>& ToSyncEnv)
{
	if (BelongOperator.IsValid())
	{
		TArray<TSharedPtr<FIntersectionDynamicMesh>> RealEnv = BelongOperator.Pin()->GetRealEnvironments();
		int32 SyncIndex = RealEnv.IndexOfByPredicate([InEnv](const TSharedPtr<FIntersectionDynamicMesh>& RE) {
			if(InEnv.IsValid() && RE.IsValid())
			{
				return InEnv->GetLinkModelBaseInfo().UUID.Equals(RE->GetLinkModelBaseInfo().UUID);
			}
			return false;
		});
		if (SyncIndex != INDEX_NONE)
		{
			ToSyncEnv = RealEnv[SyncIndex];
		}
		else
		{
			ToSyncEnv = InEnv;
		}
	}
	else
	{
		ToSyncEnv = InEnv;
	}
}

void FDrawerAdaptiveAdsorption::SetRealXNegativeDependence(const TSharedPtr<FIntersectionDynamicMesh>& InTarget)
{
	if (RealXNegativeAdsorption != InTarget)
	{
        RealXNegativeAdsorption = InTarget;
	}
}

void FDrawerAdaptiveAdsorption::SetRealXPositiveDependence(const TSharedPtr<FIntersectionDynamicMesh>& InTarget)
{
    if (RealXPositiveAdsorption != InTarget)
    {
        RealXPositiveAdsorption = InTarget;
    }
}

void FDrawerAdaptiveAdsorption::SetRealZNegativeDependence(const TSharedPtr<FIntersectionDynamicMesh>& InTarget)
{
    if (RealZNegativeAdsorption != InTarget)
    {
        RealZNegativeAdsorption = InTarget;
    }
}

void FDrawerAdaptiveAdsorption::SetRealZPositiveDependence(const TSharedPtr<FIntersectionDynamicMesh>& InTarget)
{
    if (RealZPositiveAdsorption != InTarget)
    {
        RealZPositiveAdsorption = InTarget;
    }
}
