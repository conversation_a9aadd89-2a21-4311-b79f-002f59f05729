// Fill out your copyright notice in the Description page of Project Settings.

#include "DesignStationFunctionLibrary.h"
#include "RHICommandList.h"
#include "BasicClasses/DesignStationController.h"
#include "Blueprint/SlateBlueprintLibrary.h"
#include "Engine/Texture2DDynamic.h"
#include "Internationalization/Regex.h"
#include "Runtime/Core/Public/HAL/PlatformFilemanager.h"
#include "Runtime/Core/Public/Misc/FileHelper.h"
#include "Runtime/Core/Public/Misc/Paths.h"
#include "Runtime/Core/Public/Modules/ModuleManager.h"
#include "Runtime/Engine/Classes/Engine/Engine.h"
#include "Runtime/Engine/Classes/Engine/GameViewportClient.h"
#include "Runtime/Engine/Classes/GameFramework/GameUserSettings.h"
#include "Runtime/ImageWrapper/Public/IImageWrapper.h"
#include "Runtime/ImageWrapper/Public/IImageWrapperModule.h"
#include "Runtime/Slate/Public/Framework/Application/SlateApplication.h"
#include "Runtime/SlateCore/Public/Widgets/SWindow.h"
#include "Runtime/UMG/Public/Components/EditableText.h"

/******************************************/
#include <shlobj.h>
#include "AllowWindowsPlatformTypes.h"
#include "HideWindowsPlatformTypes.h"
/******************************************/

#include "COMPointer.h"
#include "GenericPlatformFile.h"
#include "ImageUtils.h"
#include "JsonObjectConverter.h"
#include "KismetProceduralMeshLibrary.h"
#include "Developer/DesktopPlatform/Public/IDesktopPlatform.h"
#include "Engine/TextureCube.h"
#include "GameFramework/Pawn.h"
#include "SubSystems/MVC/Core/Property/FurnitureProperty.h"
#include "Subsystems/Resource/DSResourceSubsystem.h"

const FVector2D TopUISize = FVector2D(1920.0f, 70.0f);

const FVector2D LeftUISize = FVector2D(350.0f, 880.0f);

const FVector2D RightUISize = FVector2D(240.0f, 880.0f);

extern const float UNIT_MM_TO_CM;

extern const float UNIT_CM_TO_MM;

namespace EOpenFileDialogFlags
{
	enum Type
	{
		None = 0x00,
		// No flags
		Multiple = 0x01 // Allow multiple file selections
	};
}

static FLinearColor Bilerp(
	const FLinearColor& Sample00,
	const FLinearColor& Sample10,
	const FLinearColor& Sample01,
	const FLinearColor& Sample11,
	float FracX, float FracY)
{
	//FMath::Lerp is :
	// return (T)(A + Alpha * (B-A));
	// must match that exactly

	VectorRegister4Float V00 = VectorLoad(&Sample00.Component(0));
	VectorRegister4Float V10 = VectorLoad(&Sample10.Component(0));
	VectorRegister4Float V01 = VectorLoad(&Sample01.Component(0));
	VectorRegister4Float V11 = VectorLoad(&Sample11.Component(0));

	VectorRegister4Float S0 = VectorAdd(V00, VectorMultiply(VectorSetFloat1(FracX), VectorSubtract(V10, V00)));
	VectorRegister4Float S1 = VectorAdd(V01, VectorMultiply(VectorSetFloat1(FracX), VectorSubtract(V11, V01)));
	VectorRegister4Float SS = VectorAdd(S0, VectorMultiply(VectorSetFloat1(FracY), VectorSubtract(S1, S0)));
	FLinearColor Out;
	VectorStore(SS, &Out.Component(0));

	/*
	// FLinearColor has math operators but they are scalar, not vector
	FLinearColor Sample0 = FMath::Lerp(Sample00, Sample10, FracX);
	FLinearColor Sample1 = FMath::Lerp(Sample01, Sample11, FracX);
	FLinearColor Ret = FMath::Lerp(Sample0, Sample1, FracY);
	check( Out == Ret );
	*/

	return Out;
}

/**
 * 2D view into one slice of an image.
 */
struct FImageView2D
{
	/** Pointer to colors in the slice. */
	FLinearColor* SliceColors;

	/** Width of the slice. */
	int64 SizeX;

	/** Height of the slice. */
	int64 SizeY;

	FImageView2D() : SliceColors(nullptr), SizeX(0), SizeY(0) {}

	/** Initialization constructor. */
	FImageView2D(FImage& Image, int32 SliceIndex)
	{
		SizeX = Image.SizeX;
		SizeY = Image.SizeY;
		check(SliceIndex < Image.NumSlices);
		SliceColors = (&Image.AsRGBA32F()[0]) + SliceIndex * SizeY * SizeX;
	}

	/** Access a single texel. */
	FLinearColor& Access(int32 X, int32 Y)
	{
		return SliceColors[X + Y * SizeX];
	}

	/** Const access to a single texel. */
	const FLinearColor& Access(int32 X, int32 Y) const
	{
		return SliceColors[X + Y * SizeX];
	}

	bool IsValid() const { return SliceColors != nullptr; }

	static const FImageView2D ConstructConst(const FImage& Image, int32 SliceIndex)
	{
		return FImageView2D(const_cast<FImage&>(Image), SliceIndex);
	}
};

/*------------------------------------------------------------------------------
	Angular Filtering for HDR Cubemaps.
------------------------------------------------------------------------------*/

/**
 * View in to an image that allows access by converting a direction to longitude and latitude.
 */
struct FImageViewLongLat
{
	/** Image colors. */
	FLinearColor* ImageColors;

	/** Width of the image. */
	int64 SizeX;

	/** Height of the image. */
	int64 SizeY;

	/** Initialization constructor. */
	explicit FImageViewLongLat(FImage& Image, int32 SliceIndex)
	{
		SizeX = Image.SizeX;
		SizeY = Image.SizeY;
		ImageColors = (&Image.AsRGBA32F()[0]) + SliceIndex * SizeY * SizeX;
	}

	/** Const access to a texel. */
	const FLinearColor& Access(int32 X, int32 Y) const
	{
		return ImageColors[X + Y * SizeX];
	}

	/** Makes a filtered lookup. */
	FLinearColor LookupFiltered(float X, float Y) const
	{
		// X is in (0,SizeX) (exclusive)
		// Y is in (0,SizeY)
		checkSlow(X > 0.f && X < SizeX);
		checkSlow(Y > 0.f && Y < SizeY);

		int32 X0 = static_cast<int32>(X);
		int32 Y0 = static_cast<int32>(Y);

		float FracX = X - static_cast<float>(X0);
		float FracY = Y - static_cast<float>(Y0);

		int32 X1 = X0 + 1;
		int32 Y1 = Y0 + 1;

		// wrap X :
		checkSlow(X0 >= 0 && X0 < SizeX);
		if (X1 >= SizeX)
		{
			X1 = 0;
		}

		// clamp Y :
		// clamp should only ever change SizeY to SizeY -1 ?
		checkSlow(Y0 >= 0 && Y0 < SizeY);
		if (Y1 >= SizeY)
		{
			Y1 = SizeY - 1;
		}

		const FLinearColor& CornerRGB00 = Access(X0, Y0);
		const FLinearColor& CornerRGB10 = Access(X1, Y0);
		const FLinearColor& CornerRGB01 = Access(X0, Y1);
		const FLinearColor& CornerRGB11 = Access(X1, Y1);

		return Bilerp(CornerRGB00, CornerRGB10, CornerRGB01, CornerRGB11, FracX, FracY);
	}

	/** Makes a filtered lookup using a direction. */
	// see http://gl.ict.usc.edu/Data/HighResProbes
	// latitude-longitude panoramic format = equirectangular mapping

	// using floats saves ~6% of the total time
	//	but would change output
	// cycles_float = 135 cycles_double = 143
	FLinearColor LookupLongLatFloat(const FVector& NormalizedDirection) const
	{
		// atan2 returns in [-PI,PI]
		// acos returns in [0,PI]
		const FVector3f NormalizedDirectionFloat{NormalizedDirection};

		constexpr float invPI = 1.f / PI;
		float X = (1.f + atan2f(NormalizedDirectionFloat.X, -NormalizedDirectionFloat.Z) * invPI) * 0.5f * static_cast<float>(SizeX);
		float Y = acosf(NormalizedDirectionFloat.Y) * invPI * static_cast<float>(SizeY);

		return LookupFiltered(X, Y);
	}

	FLinearColor LookupLongLatDouble(const FVector& NormalizedDirection) const
	{
		// this does the math in doubles then stores to floats :
		//	that was probably a mistake, but leave it to avoid patches
		float X = static_cast<float>((1 + atan2(NormalizedDirection.X, -NormalizedDirection.Z) / PI) / 2 * static_cast<float>(SizeX));
		float Y = static_cast<float>(acos(NormalizedDirection.Y) / PI * static_cast<float>(SizeY));

		return LookupFiltered(X, Y);
	}
};

void UDesignStationFunctionLibrary::ResetResolutionSize(const EResSizeType& CurrentResType)
{
	if (GEngine)
	{
		UGameUserSettings* UserSetting = GEngine->GameUserSettings;
		if (UserSetting == nullptr)
		{
			UE_LOG(LogTemp, Error, TEXT("Get User Settings Error, Check DefaultGameUserSettings.ini file"));
			return;
		}
		FIntPoint ChangeResolution;
		switch (CurrentResType)
		{
		case EResSizeType::ELoginWindow:
			{
				ChangeResolution.X = 1000;
				ChangeResolution.Y = 470;
				UserSetting->SetWindowPosition(0, 0);
				UserSetting->SetScreenResolution(ChangeResolution);
				UserSetting->SetFullscreenMode(EWindowMode::Type::Windowed);
				break;
			}
		case EResSizeType::EEditWindow:
			{
				ChangeResolution.X = 1920;
				ChangeResolution.Y = 1040;
				UserSetting->SetScreenResolution(ChangeResolution);
				UserSetting->SetFullscreenMode(EWindowMode::Type::Windowed);
				break;
			}
		case EResSizeType::EMaxWindow:
			{
				/*const FVector2D ViewportSize = GEngine->GameViewport->Viewport->GetSizeXY();
				ChangeResolution.X = ViewportSize.X;
				ChangeResolution.Y = ViewportSize.Y;
				UserSetting->SetScreenResolution(ChangeResolution);
				UserSetting->SetFullscreenMode(EWindowMode::Type::WindowedFullscreen);*/
				TSharedPtr<SWindow> GameWindow = GEngine->GameViewport->GetWindow();
				if (GameWindow.IsValid())
				{
					if (GameWindow->IsWindowMaximized())
					{
						GameWindow->Restore();
					}
					else
					{
						GameWindow->Maximize();
					}
					ADesignStationController::Get()->Modify2DCamera(GameWindow->IsWindowMaximized());
				}
				break;
			}
		case EResSizeType::EMinWindow:
			{
				TSharedPtr<SWindow> GameWindow = GEngine->GameViewport->GetWindow();
				if (GameWindow.IsValid())
				{
					GameWindow->Minimize();
				}
				break;
			}
		case EResSizeType::EToMaxWindow:
			{
				TSharedPtr<SWindow> GameWindow = GEngine->GameViewport->GetWindow();
				if (GameWindow.IsValid())
				{
					GameWindow->Maximize();
				}
				break;
			}
		default:
			break;
		}
		//UserSetting->SaveSettings();
		//UserSetting->ApplySettings(false);
		//UserSetting->ApplyResolutionSettings(false);
		//UserSetting->ApplyNonResolutionSettings();
	}
}

void UDesignStationFunctionLibrary::FlipGameWindowType()
{
	if (GEngine)
	{
		UGameUserSettings* UserSetting = GEngine->GameUserSettings;
		if (UserSetting == nullptr)
		{
			UE_LOG(LogTemp, Error, TEXT("Get User Settings Error, Check DefaultGameUserSettings.ini file"));
		}
	}
}

bool UDesignStationFunctionLibrary::IsFullGameWindowMode()
{
	if (GEngine)
	{
		UGameUserSettings* UserSetting = GEngine->GameUserSettings;
		if (UserSetting == nullptr)
		{
			UE_LOG(LogTemp, Error, TEXT("Get User Settings Error, Check DefaultGameUserSettings.ini file"));
			return true;
		}
	}
	return true;
}

void UDesignStationFunctionLibrary::MoveGameWindow(const FVector2D& TargetPos)
{
	if (GEngine)
	{
		TSharedPtr<SWindow> GameWindow = GEngine->GameViewport->GetWindow();
		if (GameWindow.IsValid())
		{
			GameWindow->MoveWindowTo(TargetPos);
		}
		else
		{
			UE_LOG(LogTemp, Error, TEXT("Get gameWindow ptr error when try to move!!"));
		}
	}
}

void UDesignStationFunctionLibrary::ReshapeGameWindow(const FVector2D& NewPosition, const FVector2D& NewSize)
{
	if (GEngine)
	{
		TSharedPtr<SWindow> GameWindow = GEngine->GameViewport->GetWindow();
		if (GameWindow.IsValid())
		{
			GameWindow->ReshapeWindow(NewPosition, NewSize);
		}
		else
		{
			UE_LOG(LogTemp, Error, TEXT("Get gameWindow ptr error when try to reshape!!"));
		}
	}
}

FVector2D UDesignStationFunctionLibrary::GetGameWindowPosition()
{
	if (GEngine)
	{
		TSharedPtr<SWindow> GameWindow = GEngine->GameViewport->GetWindow();
		if (GameWindow.IsValid())
		{
			return GameWindow->GetPositionInScreen();
		}
		UE_LOG(LogTemp, Error, TEXT("Get gameWindow ptr error when try to get window position!!"));
		return FVector2D();
	}
	return FVector2D();
}

void UDesignStationFunctionLibrary::ChangeMouseCursor(const EMouseCourseType& InType, UUserWidget* CursorWidget)
{
	if (ADesignStationController::Get()->bShowMouseCursor == false)
	{
		ADesignStationController::Get()->bShowMouseCursor = true;
	}
	switch (InType)
	{
	case EMouseCourseType::EArrow:
		{
			ADesignStationController::Get()->CurrentMouseCursor = EMouseCursor::Type::Default;
			break;
		}
	case EMouseCourseType::EHand:
		{
			ADesignStationController::Get()->CurrentMouseCursor = EMouseCursor::Type::Hand;
			break;
		}
	case EMouseCourseType::ENone:
		{
			ADesignStationController::Get()->bShowMouseCursor = false;
			break;
		}
	case EMouseCourseType::EGrabHand:
		{
			ADesignStationController::Get()->CurrentMouseCursor = EMouseCursor::Type::GrabHand;
			break;
		}
	case EMouseCourseType::ECustom:
		{
			ADesignStationController::Get()->SetMouseCursorWidget(EMouseCursor::Type::Default, CursorWidget);
			break;
		}
	default:
		break;
	}
}

void UDesignStationFunctionLibrary::ReshapeViewport(const EReshapeViewportType& InType)
{
	GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].OriginX = 0.0f;
	GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].OriginY = 0.0f;
	GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].SizeX = 1.0f;
	GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].SizeY = 1.0f;
	//
	//if (GWorld && GWorld->WorldType == EWorldType::Type::Game && GEngine && GEngine->GameViewport)
	//{
	//	FVector2D ViewportOringinSize;
	//	GEngine->GameViewport->GetViewportSize(ViewportOringinSize);
	//	FVector2D ViewportResolution = FVector2D(GSystemResolution.ResX, GSystemResolution.ResY);

	//	/*UE_LOG(LogTemp, Warning, TEXT("**************************** BEGIN *****************************"));
	//	UE_LOG(LogTemp, Log, TEXT("viewport origin size:%f,%f"), ViewportOringinSize.X, ViewportOringinSize.Y);
	//	UE_LOG(LogTemp, Log, TEXT("viewport resolution size:%f,%f"), ViewportResolution.X, ViewportResolution.Y);*/

	//	if (ViewportOringinSize.X > 0 && ViewportOringinSize.Y > 0 && ViewportResolution.X > 0 && ViewportResolution.Y > 0)
	//	{
	//		FVector2D ToolSizeRelativeToViewport = TopUISize / ViewportOringinSize;
	//		FVector2D FolderSizeRelativeToViewport = LeftUISize / ViewportOringinSize;
	//		FVector2D PropertySizeRelativeToViewport = RightUISize / ViewportOringinSize;
	//		/*UE_LOG(LogTemp, Warning, TEXT("ToolBar Relative size:%f,%f"), ToolSizeRelativeToViewport.X, ToolSizeRelativeToViewport.Y);
	//		UE_LOG(LogTemp, Warning, TEXT("folder Relative size:%f,%f"), FolderSizeRelativeToViewport.X, FolderSizeRelativeToViewport.Y);
	//		UE_LOG(LogTemp, Warning, TEXT("property Relative size:%f,%f"), PropertySizeRelativeToViewport.X, PropertySizeRelativeToViewport.Y);

	//		UE_LOG(LogTemp, Warning, TEXT("viewport TL Point by view : %f,%f"), FolderSizeRelativeToViewport.X * ViewportOringinSize.X
	//			, ToolSizeRelativeToViewport.Y * ViewportOringinSize.Y);
	//		UE_LOG(LogTemp, Warning, TEXT("viewport TL Point by Res : %f,%f"), FolderSizeRelativeToViewport.X * ViewportResolution.X
	//			, ToolSizeRelativeToViewport.Y * ViewportResolution
	//			.Y);*/

	//		switch (InType)
	//		{
	//		case EReshapeViewportType::AllUI:
	//		{
	//			GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].OriginX = FolderSizeRelativeToViewport.X;
	//			GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].OriginY = ToolSizeRelativeToViewport.Y;
	//			GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].SizeX = 1.0f - FolderSizeRelativeToViewport.X - PropertySizeRelativeToViewport.X;
	//			GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].SizeY = 1.0f;
	//			break;
	//		}
	//		case EReshapeViewportType::OnlyTopUI:
	//		{
	//			GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].OriginX = 0.0f;
	//			GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].OriginY = ToolSizeRelativeToViewport.Y;
	//			GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].SizeX = 1.0f;
	//			GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].SizeY = 1.0f;
	//			break;
	//		}
	//		case EReshapeViewportType::NoRightUI:
	//		{
	//			GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].OriginX = FolderSizeRelativeToViewport.X;
	//			GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].OriginY = ToolSizeRelativeToViewport.Y;
	//			GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].SizeX = 1.0f - FolderSizeRelativeToViewport.X;
	//			GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].SizeY = 1.0f;
	//			break;
	//		}
	//		case EReshapeViewportType::NoLeftUI:
	//		{
	//			GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].OriginX = 0.0f;
	//			GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].OriginY = ToolSizeRelativeToViewport.Y;
	//			GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].SizeX = 1.0f - -PropertySizeRelativeToViewport.X;
	//			GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].SizeY = 1.0f;
	//			break;
	//		}
	//		case EReshapeViewportType::Origin:
	//		{
	//			GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].OriginX = 0.0f;
	//			GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].OriginY = 0.0f;
	//			GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].SizeX = 1.0f;
	//			GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].SizeY = 1.0f;
	//			break;
	//		}
	//		case EReshapeViewportType::Initial:
	//		{
	//			GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].OriginX = FolderSizeRelativeToViewport.X - .210f;
	//			GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].OriginY = ToolSizeRelativeToViewport.Y - .035f;
	//			GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].SizeX = 1.0f;
	//			GEngine->GameViewport->SplitscreenInfo[0].PlayerData[0].SizeY = 1.0f;
	//			break;
	//		}
	//		default:
	//			break;
	//		}

	//		/*FVector2D ViewportResizeSize;
	//		GEngine->GameViewport->GetViewportSize(ViewportResizeSize);
	//		UE_LOG(LogTemp, Warning, TEXT("viewport resize size:%f,%f"), ViewportResizeSize.X, ViewportResizeSize.Y);
	//		ViewportResolution = FVector2D(GSystemResolution.ResX, GSystemResolution.ResY);
	//		int32 ViewportX;
	//		int32 ViewportY;
	//		ADesignStationController::Get()->GetViewportSize(ViewportX, ViewportY);
	//		UE_LOG(LogTemp, Warning, TEXT("current viewport size:%d,%d"), ViewportX, ViewportY);
	//		UE_LOG(LogTemp, Warning, TEXT("viewport resolution size:%f,%f"), ViewportResolution.X, ViewportResolution.Y);
	//		UE_LOG(LogTemp, Warning, TEXT("**************************** END *****************************"));*/
	//	}
	//}
}

void UDesignStationFunctionLibrary::GetGameViewportSize(FVector2D& OutSize)
{
	OutSize = FVector2D::ZeroVector;
	if (GEngine && GEngine->GameViewport)
	{
		GEngine->GameViewport->GetViewportSize(OutSize);
	}
}

void UDesignStationFunctionLibrary::SetEditableTextFocus(UEditableText* EdtText)
{
	/*APlayerController *PC = UGameplayStatics::GetPlayerController(GWorld, 0);
	EdtText->SetUserFocus(PC);*/
	if (EdtText)
	{
		EdtText->SetUserFocus(ADesignStationController::Get());
	}
}

FVector UDesignStationFunctionLibrary::GetScaleRatio()
{
	float CurrentRatio = ADesignStationController::Get()->GetCurrentCameraRatio();
	return FVector(CurrentRatio, CurrentRatio, CurrentRatio);
}

bool UDesignStationFunctionLibrary::IsNumberIntType(float InNumber)
{
	return FMath::IsNearlyZero(InNumber - static_cast<int32>(InNumber));
}

int32 UDesignStationFunctionLibrary::FormatValueToUI(float InValue)
{
	return (InValue - static_cast<int32>(InValue)) >= 0.5f ? static_cast<int32>(InValue) + 1 : static_cast<int32>(InValue);
}

bool UDesignStationFunctionLibrary::IsInputSaftyLength(const FString& InputValue)
{
	float NewValue = FCString::Atof(*InputValue);
	return NewValue >= 50.0f;
}

FString UDesignStationFunctionLibrary::GetContentStr(FString InStr, TCHAR FlagChar)
{
	int32 StartIndex = 0;
	int32 EndIndex = InStr.Len() - 1;
	bool SFind = InStr.FindChar(FlagChar, StartIndex);
	bool EFind = InStr.FindLastChar(FlagChar, EndIndex);
	if (SFind)
	{
		StartIndex += 1;
	}
	if (EFind)
	{
		EndIndex -= 1;
	}

	return InStr.Mid(StartIndex, EndIndex - StartIndex);
}

UTexture2D* UDesignStationFunctionLibrary::LoadTextureFromFile(const FString& InTexturePath)
{
	FString FileABPath = FPaths::ProjectContentDir() + InTexturePath;
	FileABPath = FPaths::ConvertRelativePathToFull(FileABPath);
	return LoadTextureFromJPG(InTexturePath);
}

UTexture2D* UDesignStationFunctionLibrary::LoadTextureFromJPG(const FString& InTextureAbsoluteFilePath)
{
	// Represents the entire file in memory.
	TArray<uint8> RawFileData;
	if (!FPaths::FileExists(InTextureAbsoluteFilePath))
	{
		return nullptr;
	}

	if (FFileHelper::LoadFileToArray(RawFileData, *InTextureAbsoluteFilePath))
	{
		if (RawFileData.IsEmpty())
		{
			return nullptr;
		}
		IImageWrapperModule& ImageWrapperModule = FModuleManager::LoadModuleChecked<IImageWrapperModule>(FName("ImageWrapper"));

		TSharedPtr<IImageWrapper> ImageWrapper;
		EImageFormat OtherFormat = EImageFormat::JPEG;
		FString fileExt = FPaths::GetExtension(InTextureAbsoluteFilePath);
		if (fileExt.Equals("png", ESearchCase::IgnoreCase))
		{
			// Note: PNG format.  Other formats are supported
			ImageWrapper = ImageWrapperModule.CreateImageWrapper(EImageFormat::PNG);
			OtherFormat = EImageFormat::PNG;
		}
		else if (fileExt.Equals("jpg", ESearchCase::IgnoreCase) || fileExt.Equals("jpeg", ESearchCase::IgnoreCase))
		{
			// Note: PNG format.  Other formats are supported
			ImageWrapper = ImageWrapperModule.CreateImageWrapper(EImageFormat::JPEG);
			OtherFormat = EImageFormat::JPEG;
		}
		else if (fileExt.Equals("bmp", ESearchCase::IgnoreCase))
		{
			// Note: PNG format.  Other formats are supported
			ImageWrapper = ImageWrapperModule.CreateImageWrapper(EImageFormat::BMP);
			OtherFormat = EImageFormat::BMP;
		}
		else if (fileExt.Equals("ico", ESearchCase::IgnoreCase))
		{
			// Note: PNG format.  Other formats are supported
			ImageWrapper = ImageWrapperModule.CreateImageWrapper(EImageFormat::ICO);
			OtherFormat = EImageFormat::ICO;
		}
		else if (fileExt.Equals("exr", ESearchCase::IgnoreCase))
		{
			// Note: PNG format.  Other formats are supported
			ImageWrapper = ImageWrapperModule.CreateImageWrapper(EImageFormat::EXR);
			OtherFormat = EImageFormat::EXR;
		}
		else
		{
			return nullptr;
		}

		bool bRightFormat = true;
		if (!(ImageWrapper.IsValid() && ImageWrapper->SetCompressed(RawFileData.GetData(), RawFileData.Num())))
		{
			ImageWrapper = ImageWrapperModule.CreateImageWrapper(OtherFormat);
			bRightFormat = false;
			if (ImageWrapper.IsValid() && ImageWrapper->SetCompressed(RawFileData.GetData(), RawFileData.Num()))
			{
				bRightFormat = true;
			}
		}

		if (bRightFormat)
		{
			// Select the texture's source format
			ETextureSourceFormat TextureFormat = TSF_Invalid;
			int32 BitDepth = ImageWrapper->GetBitDepth();
			ERGBFormat Format = ImageWrapper->GetFormat();
			if (Format == ERGBFormat::Gray)
			{
				if (BitDepth <= 8)
				{
					TextureFormat = TSF_G8;
					Format = ERGBFormat::Gray;
					BitDepth = 8;
				}
			}
			else if (Format == ERGBFormat::RGBA || Format == ERGBFormat::BGRA)
			{
				if (BitDepth <= 8)
				{
					TextureFormat = TSF_BGRA8;
					Format = ERGBFormat::BGRA;
					BitDepth = 8;
				}
			}
			if (TextureFormat == TSF_Invalid)
			{
				return nullptr;
			}
			TArray<uint8> UncompressedRGBA;
			if (ImageWrapper->GetRaw(Format, BitDepth, UncompressedRGBA))
			{
				UTexture2D* MyTexture = UTexture2D::CreateTransient(ImageWrapper->GetWidth(), ImageWrapper->GetHeight(), PF_B8G8R8A8);
				if (nullptr == MyTexture)
				{
					return nullptr;
				}
				//MyTexture->MipGenSettings = TextureMipGenSettings::TMGS_SimpleAverage;
				//MyTexture->SRGB = BitDepth < 16;
				//MyTexture->NeverStream = 1;
				// Fill in the source data from the file
				void* TextureData = MyTexture->GetPlatformData()->Mips[0].BulkData.Lock(LOCK_READ_WRITE);
				FMemory::Memcpy(TextureData, UncompressedRGBA.GetData(), UncompressedRGBA.Num());
				MyTexture->GetPlatformData()->Mips[0].BulkData.Unlock();
				MyTexture->UpdateResource();

				return MyTexture;
			}
		}
	}

	return nullptr;
}

UTexture* UDesignStationFunctionLibrary::ConvertEquirectangularToCube(const TArray<uint8>& InRaw)
{
	FImage Image;
	if (!FImageUtils::DecompressImage(InRaw.GetData(), InRaw.Num(), Image))
	{
		return nullptr;
	}

	Image.ChangeFormat(ERawImageFormat::RGBA32F, EGammaSpace::Linear);

	uint32 Extent = 1U << FMath::FloorLog2(Image.SizeX / 2);
	Extent = Extent < 32 ? 32 : Extent;

	float InvExtent = 1.0f / static_cast<float>(Extent);

	FImage FinalImage;
	FinalImage.Init(Extent, Extent, Image.NumSlices * 6, ERawImageFormat::RGBA32F, EGammaSpace::Linear);

	for (int32 Slice = 0; Slice < Image.NumSlices; ++Slice)
	{
		FImageViewLongLat LongLatView(Image, Slice);

		// Parallel on the 6 faces :
		ParallelFor(TEXT("Texture.CubeMipFromLongLat.PF"), 6, 1, [&](int32 Face)
		{
			FImageView2D MipView(FinalImage, Slice * 6 + Face);
			for (uint32 y = 0; y < Extent; ++y)
			{
				for (uint32 x = 0; x < Extent; ++x)
				{
					FVector DirectionWS = ComputeWSCubeDirectionAtTexelCenter(Face, x, y, InvExtent);
					MipView.Access(x, y) = LongLatView.LookupLongLatDouble(DirectionWS);
				}
			}
		}, EParallelForFlags::Unbalanced);
	}

	UTextureCube* CubeMap = NewObject<UTextureCube>(GetTransientPackage(), NAME_None, RF_Transient);

	CubeMap->SRGB = false;
	CubeMap->CompressionSettings = TC_HDR;
	//CubeMap->MipGenSettings = TMGS_NoMipmaps;

	FTexturePlatformData* PlatformData = new FTexturePlatformData();
	CubeMap->SetPlatformData(PlatformData);

	PlatformData->SizeX = FinalImage.SizeX;
	PlatformData->SizeY = FinalImage.SizeY;
	PlatformData->PixelFormat = PF_FloatRGBA;
	PlatformData->SetIsCubemap(true);
	PlatformData->SetNumSlices(6);

	FTexture2DMipMap* Mip = new FTexture2DMipMap();
	PlatformData->Mips.Add(Mip);

	Mip->SizeX = FinalImage.SizeX;
	Mip->SizeY = FinalImage.SizeY;
	Mip->BulkData.Lock(LOCK_READ_WRITE);

	FFloat16Color* MipData = reinterpret_cast<FFloat16Color*>(Mip->BulkData.Realloc(6 * Extent * Extent * sizeof(FFloat16Color)));

	const FLinearColor* SrcData = reinterpret_cast<const FLinearColor*>(FinalImage.RawData.GetData());
	for (int32 FaceIndex = 0; FaceIndex < 6; ++FaceIndex)
	{
		for (uint32 Index = 0; Index < (Extent * Extent); ++Index)
		{
			MipData[FaceIndex * Extent * Extent + Index] = FFloat16Color(SrcData[FaceIndex * Extent * Extent + Index]);
		}
	}

	Mip->BulkData.Unlock();

	CubeMap->UpdateResource();

	return CubeMap;
}

UTexture2DDynamic* UDesignStationFunctionLibrary::CreateTexture2DFromMemory(int32 Width, int32 Height,
                                                                            const TArray<FColor>& InColor)
{
	if (UTexture2DDynamic* Texture = UTexture2DDynamic::Create(Width, Height))
	{
		Texture->SRGB = true;
		Texture->UpdateResource();

		TArray<uint8> RawColorData;
		RawColorData.AddDefaulted(InColor.Num() * 4);
		FMemory::Memcpy(RawColorData.GetData(), InColor.GetData(), RawColorData.Num());

		FTexture2DDynamicResource* TextureResource = static_cast<FTexture2DDynamicResource*>(Texture->GetResource());
		if (TextureResource)
		{
			ENQUEUE_RENDER_COMMAND(FWriteRawDataToTexture)(
				[TextureResource, RawData = MoveTemp(RawColorData)](FRHICommandListImmediate& RHICmdList)
				{
					TextureResource->WriteRawToTexture_RenderThread(RawData);
				});
		}

		return Texture;
	}

	return nullptr;
}

bool UDesignStationFunctionLibrary::OpenFolderDirectory(const FString& DefaultPath, FString& OutFolderName)
{
	const void* ParentWindowHandle = FSlateApplication::Get().FindBestParentWindowHandleForDialogs(nullptr);
	const FString DialogTitle = TEXT("选择文件夹");

	//FScopedSystemModalMode SystemModalScope;

	bool bSuccess = false;

	TComPtr<IFileOpenDialog> FileDialog;
	if (SUCCEEDED(::CoCreateInstance(CLSID_FileOpenDialog, nullptr, CLSCTX_INPROC_SERVER, IID_PPV_ARGS(&FileDialog))))
	{
		// Set this up as a folder picker
		{
			DWORD dwFlags = 0;
			FileDialog->GetOptions(&dwFlags);
			FileDialog->SetOptions(dwFlags | FOS_PICKFOLDERS);
		}

		// Set up common settings
		FileDialog->SetTitle(*DialogTitle);
		if (!DefaultPath.IsEmpty())
		{
			// SHCreateItemFromParsingName requires the given path be absolute and use \ rather than / as our normalized paths do
			FString DefaultWindowsPath = FPaths::ConvertRelativePathToFull(DefaultPath);
			DefaultWindowsPath.ReplaceInline(TEXT("/"), TEXT("\\"), ESearchCase::CaseSensitive);

			TComPtr<IShellItem> DefaultPathItem;
			if (SUCCEEDED(::SHCreateItemFromParsingName(*DefaultWindowsPath, nullptr, IID_PPV_ARGS(&DefaultPathItem))))
			{
				FileDialog->SetFolder(DefaultPathItem);
			}
		}

		// Show the picker
		if (SUCCEEDED(FileDialog->Show((HWND)ParentWindowHandle)))
		{
			TComPtr<IShellItem> Result;
			if (SUCCEEDED(FileDialog->GetResult(&Result)))
			{
				PWSTR pFilePath = nullptr;
				if (SUCCEEDED(Result->GetDisplayName(SIGDN_FILESYSPATH, &pFilePath)))
				{
					bSuccess = true;

					OutFolderName = pFilePath;
					FPaths::NormalizeDirectoryName(OutFolderName);

					CoTaskMemFree(pFilePath);
				}
			}
		}
	}

	return bSuccess;
}

void UDesignStationFunctionLibrary::OpenFileDialogForImage(FString& OutputString)
{
	TArray<FString> OutFileNames;

	//FString FileTypes = TEXT("Image File (*.jpg)|*.jpg|(*.png)|*.png|(*.jpeg)|*.jpeg");
	FString FileTypes = TEXT("JPG, BMP, PNG, JPEG files | *.jpg; *.png; *.bmp; *.jpeg");

	const void* ParentWindowHandle = FSlateApplication::Get().FindBestParentWindowHandleForDialogs(nullptr);

	int32 DummyFilterIndex;
	FileDialogShared(
		false,
		ParentWindowHandle,
		NSLOCTEXT("", "SelectImageTitle", "Select image file").ToString(),
		TEXT(""),
		TEXT(""),
		FileTypes,
		0,
		OutFileNames,
		DummyFilterIndex);
	if (!OutFileNames.Num())
	{
		return;
	}
	OutputString = OutFileNames[0];
}

void UDesignStationFunctionLibrary::OpenFileDialogForImage(TArray<FString>& OutputString)
{
	TArray<FString> OutFileNames;

	//FString FileTypes = TEXT("Image File (*.jpg)|*.jpg|(*.png)|*.png|(*.jpeg)|*.jpeg");
	FString FileTypes = TEXT("JPG, BMP, PNG, JPEG files | *.jpg; *.png; *.bmp; *.jpeg");

	const void* ParentWindowHandle = FSlateApplication::Get().FindBestParentWindowHandleForDialogs(nullptr);

	int32 DummyFilterIndex;
	FileDialogShared(
		false,
		ParentWindowHandle,
		NSLOCTEXT("", "SelectImageTitle", "Select image file").ToString(),
		TEXT(""),
		TEXT(""),
		FileTypes,
		EOpenFileDialogFlags::Multiple,
		OutFileNames,
		DummyFilterIndex);
	if (0 == OutFileNames.Num())
	{
		return;
	}
	OutputString = OutFileNames;
}

bool UDesignStationFunctionLibrary::FileDialogShared(bool bSave, const void* ParentWindowHandle, const FString& DialogTitle, const FString& DefaultPath, const FString& DefaultFile,
                                                     const FString& FileTypes, uint32 Flags, TArray<FString>& OutFilenames, int32& OutFilterIndex)
{
	FScopedSystemModalMode SystemModalScope;

	bool bSuccess = false;

	TComPtr<IFileDialog> FileDialog;
	if (SUCCEEDED(
		::CoCreateInstance(bSave ? CLSID_FileSaveDialog : CLSID_FileOpenDialog, nullptr, CLSCTX_INPROC_SERVER, bSave ? IID_IFileSaveDialog : IID_IFileOpenDialog, IID_PPV_ARGS_Helper(&FileDialog))))
	{
		if (bSave)
		{
			// Set the default "filename"
			if (!DefaultFile.IsEmpty())
			{
				FileDialog->SetFileName(*FPaths::GetCleanFilename(DefaultFile));
			}
		}
		else
		{
			// Set this up as a multi-select picker
			if (Flags & EOpenFileDialogFlags::Multiple)
			{
				DWORD dwFlags = 0;
				FileDialog->GetOptions(&dwFlags);
				FileDialog->SetOptions(dwFlags | FOS_ALLOWMULTISELECT);
			}
		}

		// Set up common settings
		FileDialog->SetTitle(*DialogTitle);
		if (!DefaultPath.IsEmpty())
		{
			// SHCreateItemFromParsingName requires the given path be absolute and use \ rather than / as our normalized paths do
			FString DefaultWindowsPath = FPaths::ConvertRelativePathToFull(DefaultPath);
			DefaultWindowsPath.ReplaceInline(TEXT("/"), TEXT("\\"), ESearchCase::CaseSensitive);

			TComPtr<IShellItem> DefaultPathItem;
			if (SUCCEEDED(::SHCreateItemFromParsingName(*DefaultWindowsPath, nullptr, IID_PPV_ARGS(&DefaultPathItem))))
			{
				FileDialog->SetFolder(DefaultPathItem);
			}
		}

		// Set-up the file type filters
		TArray<FString> UnformattedExtensions;
		TArray<COMDLG_FILTERSPEC> FileDialogFilters;
		{
			// Split the given filter string (formatted as "Pair1String1|Pair1String2|Pair2String1|Pair2String2") into the Windows specific filter struct
			FileTypes.ParseIntoArray(UnformattedExtensions, TEXT("|"), true);

			if (UnformattedExtensions.Num() % 2 == 0)
			{
				FileDialogFilters.Reserve(UnformattedExtensions.Num() / 2);
				for (int32 ExtensionIndex = 0; ExtensionIndex < UnformattedExtensions.Num();)
				{
					COMDLG_FILTERSPEC& NewFilterSpec = FileDialogFilters[FileDialogFilters.AddDefaulted()];
					NewFilterSpec.pszName = *UnformattedExtensions[ExtensionIndex++];
					NewFilterSpec.pszSpec = *UnformattedExtensions[ExtensionIndex++];
				}
			}
		}
		FileDialog->SetFileTypes(FileDialogFilters.Num(), FileDialogFilters.GetData());

		// Show the picker
		if (SUCCEEDED(FileDialog->Show((HWND)ParentWindowHandle)))
		{
			OutFilterIndex = 0;
			if (SUCCEEDED(FileDialog->GetFileTypeIndex((UINT*)&OutFilterIndex)))
			{
				OutFilterIndex -= 1; // GetFileTypeIndex returns a 1-based index
			}

			auto AddOutFilename = [&OutFilenames](const FString& InFilename)
			{
				FString& OutFilename = OutFilenames[OutFilenames.Add(InFilename)];
				OutFilename = IFileManager::Get().ConvertToRelativePath(*OutFilename);
				FPaths::NormalizeFilename(OutFilename);
			};

			if (bSave)
			{
				TComPtr<IShellItem> Result;
				if (SUCCEEDED(FileDialog->GetResult(&Result)))
				{
					PWSTR pFilePath = nullptr;
					if (SUCCEEDED(Result->GetDisplayName(SIGDN_FILESYSPATH, &pFilePath)))
					{
						bSuccess = true;

						// Apply the selected extension if the given filename doesn't already have one
						FString SaveFilePath = pFilePath;
						if (FileDialogFilters.IsValidIndex(OutFilterIndex))
						{
							// May have multiple semi-colon separated extensions in the pattern
							const FString ExtensionPattern = FileDialogFilters[OutFilterIndex].pszSpec;
							TArray<FString> SaveExtensions;
							ExtensionPattern.ParseIntoArray(SaveExtensions, TEXT(";"));

							// Build a "clean" version of the selected extension (without the wildcard)
							FString CleanExtension = SaveExtensions[0];
							if (CleanExtension == TEXT("*.*"))
							{
								CleanExtension.Reset();
							}
							else
							{
								int32 WildCardIndex = INDEX_NONE;
								if (CleanExtension.FindChar(TEXT('*'), WildCardIndex))
								{
									CleanExtension = CleanExtension.RightChop(WildCardIndex + 1);
								}
							}

							// We need to split these before testing the extension to avoid anything within the path being treated as a file extension
							FString SaveFileName = FPaths::GetCleanFilename(SaveFilePath);
							SaveFilePath = FPaths::GetPath(SaveFilePath);

							// Apply the extension if the file name doesn't already have one
							if (FPaths::GetExtension(SaveFileName).IsEmpty() && !CleanExtension.IsEmpty())
							{
								SaveFileName = FPaths::SetExtension(SaveFileName, CleanExtension);
							}

							SaveFilePath /= SaveFileName;
						}
						AddOutFilename(SaveFilePath);

						CoTaskMemFree(pFilePath);
					}
				}
			}
			else
			{
				IFileOpenDialog* FileOpenDialog = static_cast<IFileOpenDialog*>(FileDialog.Get());

				TComPtr<IShellItemArray> Results;
				if (SUCCEEDED(FileOpenDialog->GetResults(&Results)))
				{
					DWORD NumResults = 0;
					Results->GetCount(&NumResults);
					for (DWORD ResultIndex = 0; ResultIndex < NumResults; ++ResultIndex)
					{
						TComPtr<IShellItem> Result;
						if (SUCCEEDED(Results->GetItemAt(ResultIndex, &Result)))
						{
							PWSTR pFilePath = nullptr;
							if (SUCCEEDED(Result->GetDisplayName(SIGDN_FILESYSPATH, &pFilePath)))
							{
								bSuccess = true;
								AddOutFilename(pFilePath);
								CoTaskMemFree(pFilePath);
							}
						}
					}
				}
			}
		}
	}

	return bSuccess;
}

bool UDesignStationFunctionLibrary::IsFileExists(FString InFilePath)
{
	FString FileAbPath = FPaths::ProjectContentDir() + InFilePath;
	FileAbPath = FPaths::ConvertRelativePathToFull(FileAbPath);
	return FPaths::FileExists(FileAbPath);
}

bool UDesignStationFunctionLibrary::CalculateSuitShowSize(UTexture2D* InPicutre, const FVector2D& MaxShowSize, FVector2D& OutSize)
{
	if (!InPicutre)
	{
		return false;
	}
	int32 PictureSizeX = InPicutre->GetSizeX();
	int32 PictureSizeY = InPicutre->GetSizeY();
	float RatioX = PictureSizeX / MaxShowSize.X;
	float RatioY = PictureSizeY / MaxShowSize.Y;
	if (RatioX <= 1.0f && RatioY <= 1.0f)
	{
		OutSize = FVector2D(PictureSizeX, PictureSizeY);
	}
	else if (RatioX <= 1.0f && RatioY > 1.0f)
	{
		OutSize = FVector2D(PictureSizeX / RatioY, MaxShowSize.Y);
	}
	else if (RatioX > 1.0f && RatioY <= 1.0f)
	{
		OutSize = FVector2D(MaxShowSize.X, PictureSizeY / RatioX);
	}
	else
	{
		if (RatioX >= RatioY)
		{
			OutSize = FVector2D(MaxShowSize.X, PictureSizeY / RatioX);
		}
		else
		{
			OutSize = FVector2D(PictureSizeX / RatioY, MaxShowSize.Y);
		}
	}

	return true;
}

bool UDesignStationFunctionLibrary::CreateDirectoryRecursively(FString DirectoryToMake)
{
	constexpr int32 MAX_LOOP_ITR = 3000; //limit of 3000 directories in the structure

	// Normalize all / and \ to TEXT("/") and remove any trailing TEXT("/") 
	//if the character before that is not a TEXT("/") or a colon
	FPaths::NormalizeDirectoryName(DirectoryToMake);

	//Normalize removes the last "/", but my algorithm wants it
	DirectoryToMake += "/";

	FString Base;
	FString Left;
	FString Remaining;

	//Split off the Root
	DirectoryToMake.Split(TEXT("/"), &Base, &Remaining);
	Base += "/"; //add root text to Base

	int32 LoopItr = 0;
	while (Remaining != "" && LoopItr < MAX_LOOP_ITR)
	{
		Remaining.Split(TEXT("/"), &Left, &Remaining);

		//Add to the Base
		Base += Left + FString("/"); //add left and split text to Base

		//Create Incremental Directory Structure!
		FPlatformFileManager::Get().GetPlatformFile().CreateDirectory(*Base);

		LoopItr++;
	}
	return true;
}

ECopyFileErrorCode UDesignStationFunctionLibrary::CopyFileTo(const FString& AbsoluteSourcePath, const FString& AbsoluteDestinationPath, bool bStillCopyWhileExits)
{
	if (!FPaths::FileExists(AbsoluteSourcePath))
	{
		return ECopyFileErrorCode::ESourceFileNotExits;
	}
	if (FPaths::FileExists(AbsoluteDestinationPath))
	{
		if (bStillCopyWhileExits)
		{
			if (!FPlatformFileManager::Get().GetPlatformFile().DeleteFile(*AbsoluteDestinationPath))
			{
				return ECopyFileErrorCode::EUnkonwnError;
			}
		}
		else
		{
			return AbsoluteSourcePath == AbsoluteDestinationPath ? ECopyFileErrorCode::ESuccess : ECopyFileErrorCode::EUnkonwnError;
		}
	}
	//生成目录，防止目录不存在造成的失败
	CreateDirectoryRecursively(FPaths::GetPath(AbsoluteDestinationPath));
	if (!FPlatformFileManager::Get().GetPlatformFile().CopyFile(*AbsoluteDestinationPath, *AbsoluteSourcePath))
	{
		return ECopyFileErrorCode::ECanNotDeleteDestinationFile;
	}
	return ECopyFileErrorCode::ESuccess;
}

bool UDesignStationFunctionLibrary::DeleteFile(const FString& AbsoluteSourcePath)
{
	return FPlatformFileManager::Get().GetPlatformFile().DeleteFile(*AbsoluteSourcePath);
}

bool UDesignStationFunctionLibrary::WriteDataToFile(const FString& FilePath, TArray<uint8> InData)
{
	DeleteFile(FilePath);
	CreateDirectoryRecursively(FPaths::GetPath(FilePath));
	FArchive* writer = IFileManager::Get().CreateFileWriter(*FilePath, FILEWRITE_Append);

	if (!writer)
	{
		return false;
	}

	writer->Seek(writer->TotalSize());
	writer->Serialize(InData.GetData(), InData.Num());
	writer->Close();
	delete writer;
	InData.Empty();
	return true;
}

bool UDesignStationFunctionLibrary::PhraseResourceTypeAndPackagePathFromRefPath(const FString& InRefPath, FString& ResourceType, FString& PackagePath)
{
	if (InRefPath.IsEmpty())
	{
		return false;
	}
	FString LefStr(TEXT(""));
	FString RightStr(TEXT(""));
	bool Res = InRefPath.Split(TEXT("."), &LefStr, &RightStr);
	if (!Res)
	{
		return false;
	}
	FString LefPathStr(TEXT(""));
	FString RightPathStr(TEXT(""));
	Res = RightStr.Split(TEXT("'"), &LefPathStr, &RightPathStr);
	if (!Res)
	{
		return false;
	}
	if (LefPathStr.Equals(TEXT("Blueprint"), ESearchCase::IgnoreCase))
	{
		//引用路径以Blueprint开头说明是蓝图类
		PackagePath = InRefPath;
		PackagePath.InsertAt(PackagePath.Len() - 1, TEXT("_C"));
	}
	else
	{
		PackagePath = RightPathStr;
	}
	ResourceType = LefPathStr;
	return true;
}

bool UDesignStationFunctionLibrary::IsInteger(const FString& InStr, bool bForcePositive)
{
	if (InStr.IsEmpty())
	{
		return false;
	}

	FRegexPattern Pattern(bForcePositive ? TEXT("^\\d+$") : TEXT("^-?\\d+$"));
	FRegexMatcher Matcher(Pattern, InStr);

	return Matcher.FindNext();
}

EDSResourcePlacementRule UDesignStationFunctionLibrary::ConvertPlacementRuleToEnum(const FString& InRuleName)
{
	TMap<FString, EDSResourcePlacementRule> PlacementRuleMap = {
		{TEXT("ZYFZ"), EDSResourcePlacementRule::BottomCenter},
		{TEXT("LDZYYD"), EDSResourcePlacementRule::BottomCenter},
		{TEXT("LDZYANG"), EDSResourcePlacementRule::CenterCenter},
		{TEXT("LDTQYD"), EDSResourcePlacementRule::BackBottomCenter},
		{TEXT("DJFZ"), EDSResourcePlacementRule::BottomCenter},
		{TEXT("XD"), EDSResourcePlacementRule::TopCenter},
		{TEXT("QS"), EDSResourcePlacementRule::BackCenter},
		{TEXT("CustomCupboard"), EDSResourcePlacementRule::BackBottomLeft}
	};

	return PlacementRuleMap.Contains(InRuleName) ? PlacementRuleMap[InRuleName] : EDSResourcePlacementRule::BottomCenter;
}

void UDesignStationFunctionLibrary::CreateLazyLoadBoxMesh(UProceduralMeshComponent* InMeshComponent, const TSharedPtr<FDSFurnitureBaseProperty>& InProperty)
{
	if (InMeshComponent == nullptr || !InProperty)
	{
		return;
	}

	InMeshComponent->ClearAllMeshSections();

	FVector BoxExtent;
	BoxExtent.X = InProperty->SizeProperty.Width;
	BoxExtent.Y = InProperty->SizeProperty.Depth;
	BoxExtent.Z = InProperty->SizeProperty.Height;

	BoxExtent *= 0.1f;

	TArray<FVector> Vertices;
	TArray<int32> Triangles;
	TArray<FVector> Normals;
	TArray<FVector2D> UVs;
	TArray<FVector2D> EmptyArray;
	TArray<FProcMeshTangent> Tangents;
	TArray<FColor> VertexColors;

	UKismetProceduralMeshLibrary::GenerateBoxMesh(BoxExtent * 0.5f, Vertices, Triangles, Normals, UVs, Tangents);

	InMeshComponent->CreateMeshSection(0, Vertices, Triangles, Normals, UVs, EmptyArray, EmptyArray, EmptyArray, VertexColors, Tangents, true);
	InMeshComponent->TranslucencySortPriority = 10;

	if (UDSResourceSubsystem::IsInitialized())
	{
		InMeshComponent->SetMaterial(0, UDSResourceSubsystem::GetInstance()->FindMaterialFromCache(TEXT("LazyLoadMeshDefault")));
	}

	EDSResourcePlacementRule ActualRule = ConvertPlacementRuleToEnum(InProperty->BusinessInfo.PlacementRules);

	switch (ActualRule)
	{
	case EDSResourcePlacementRule::BackCenter:
		InMeshComponent->SetRelativeLocation(FVector(0.0f, BoxExtent.Y * 0.5f, 0.0f));
		break;
	case EDSResourcePlacementRule::BottomCenter:
		InMeshComponent->SetRelativeLocation(FVector(0.0f, 0.0f, BoxExtent.Z * 0.5f));
		break;
	case EDSResourcePlacementRule::CenterCenter:
		InMeshComponent->SetRelativeLocation(FVector(0.0f));
		break;
	case EDSResourcePlacementRule::TopCenter:
		InMeshComponent->SetRelativeLocation(FVector(0.0f, 0.0f, BoxExtent.Z * -0.5f));
		break;
	case EDSResourcePlacementRule::BackBottomCenter:
		InMeshComponent->SetRelativeLocation(FVector(0.0f, BoxExtent.Y * 0.5f, BoxExtent.Z * 0.5f));
		break;
	case EDSResourcePlacementRule::BackBottomLeft:
		InMeshComponent->SetRelativeLocation(FVector(BoxExtent.X * 0.5f, BoxExtent.Y * 0.5f, BoxExtent.Z * 0.5f));
		break;
	}

	FVector BoxOffset = FVector::ZeroVector;
	if (BoxOffset.InitFromString(InProperty->BusinessInfo.BoxOffset))
	{
		FVector NewLocation = InMeshComponent->GetRelativeLocation() + BoxOffset;
		InMeshComponent->SetRelativeLocation(NewLocation);
	}
}

void UDesignStationFunctionLibrary::CreateLazyLoadBoxMeshForCustom(UDSCupboardModel* InCustomModel, UProceduralMeshComponent* InMeshComponent)
{
	if (InMeshComponent == nullptr || InCustomModel == nullptr)
	{
		return;
	}

	InMeshComponent->ClearAllMeshSections();
	
	FVector BoxExtent = UDSCupboardLibrary::GetFixedSizeParameter(InCustomModel);
	BoxExtent *= 0.5f;

	TArray<FVector> Vertices;
	TArray<int32> Triangles;
	TArray<FVector> Normals;
	TArray<FVector2D> UVs;
	TArray<FVector2D> EmptyArray;
	TArray<FProcMeshTangent> Tangents;
	TArray<FColor> VertexColors;

	UKismetProceduralMeshLibrary::GenerateBoxMesh(BoxExtent, Vertices, Triangles, Normals, UVs, Tangents);

	InMeshComponent->CreateMeshSection(0, Vertices, Triangles, Normals, UVs, EmptyArray, EmptyArray, EmptyArray, VertexColors, Tangents, true);
	InMeshComponent->TranslucencySortPriority = 10;

	if (UDSResourceSubsystem::IsInitialized())
	{
		InMeshComponent->SetMaterial(0, UDSResourceSubsystem::GetInstance()->FindMaterialFromCache(TEXT("LazyLoadMeshDefault")));
	}

	InMeshComponent->SetRelativeLocation(BoxExtent + UDSCupboardLibrary::GetFixedOffsetParameter(InCustomModel));
}

void UDesignStationFunctionLibrary::ComputePlacementTransform(UObject* WorldContext, const FVector2D& MouseScreenPosition, EDSResourcePlacementRule Rule, const TArray<AActor*>& IgnoreActors,
                                                              FVector& Location, FRotator& Rotation)
{
	if (WorldContext == nullptr)
	{
		return;
	}

	UWorld* World = WorldContext->GetWorld();
	if (World == nullptr)
	{
		return;
	}

	APlayerController* Controller = World->GetFirstPlayerController();
	if (Controller == nullptr)
	{
		return;
	}

	FVector WorldPos, WorldDir;

	Controller->DeprojectScreenPositionToWorld(MouseScreenPosition.X, MouseScreenPosition.Y, WorldPos, WorldDir);

	FCollisionQueryParams QueryParams;
	QueryParams.AddIgnoredActor(Cast<AActor>(Controller->GetPawn()));
	QueryParams.AddIgnoredActors(IgnoreActors);
	QueryParams.bTraceComplex = true;

	FHitResult HitResult;
	if (!World->LineTraceSingleByChannel(HitResult, WorldPos, WorldPos + WorldDir * 100000.0f, ECC_Visibility, QueryParams))
	{
		return;
	}

	Location = HitResult.ImpactPoint;
	float FloorHeight = UDSConfigSubsystem::GetInstance()->GetValue_FlootHeight();

	if (Location.Z < FloorHeight)
	{
		Location.Z = FloorHeight;
	}
	switch (Rule)
	{
	case EDSResourcePlacementRule::BackBottomCenter:
	case EDSResourcePlacementRule::BackCenter:
		{
			AActor* OwnerActor = HitResult.GetActor();
			if (OwnerActor == nullptr)
			{
				return;
			}

			if (OwnerActor->ActorHasTag(TEXT("Cmp")))
			{
				while (OwnerActor->GetOwner() != nullptr)
				{
					OwnerActor = OwnerActor->GetOwner();
				}
			}

			ADSBaseView* ViewActor = Cast<ADSBaseView>(OwnerActor);
			if (ViewActor == nullptr)
			{
				break;
			}

			EDSModelType ModelType = ViewActor->GetModelType();
			if (ModelType != EDSModelType::E_House_Wall)
			{
				break;
			}

			Rotation = HitResult.ImpactNormal.ToOrientationRotator();
		}
		break;
	default:
		break;
	}
}

void UDesignStationFunctionLibrary::ParseJsonToResourceList(const TArray<TSharedPtr<FJsonValue>>& ResourceItemList,
                                                            TArray<FDSResourceInfo>& OutList)
{
	for (const TSharedPtr<FJsonValue>& ItemValue : ResourceItemList)
	{
		const TSharedPtr<FJsonObject>& ItemObj = ItemValue->AsObject();
		if (!ItemObj)
		{
			continue;
		}

		FDSResourceInfo NewItem;
		if (ItemObj->HasTypedField<EJson::Number>(TEXT("type")))
		{
			NewItem.Type = static_cast<EDSResourceType>(ItemObj->GetIntegerField(TEXT("type")));
		}

		ItemObj->TryGetStringField(TEXT("id"), NewItem.Id);
		ItemObj->TryGetStringField(TEXT("name"), NewItem.Name);
		ItemObj->TryGetStringField(TEXT("width"), NewItem.Width);
		ItemObj->TryGetStringField(TEXT("height"), NewItem.Height);
		ItemObj->TryGetStringField(TEXT("depth"), NewItem.Depth);
		ItemObj->TryGetStringField(TEXT("boxOffset"), NewItem.BoxOffset);
		ItemObj->TryGetStringField(TEXT("brand"), NewItem.Brand);
		ItemObj->TryGetStringField(TEXT("productImg"), NewItem.ProductImg);
		ItemObj->TryGetStringField(TEXT("folderId"), NewItem.FolderId);
		ItemObj->TryGetStringField(TEXT("folderCode"), NewItem.FolderCode);
		ItemObj->TryGetStringField(TEXT("code"), NewItem.Code);
		ItemObj->TryGetStringField(TEXT("placementRules"), NewItem.PlacementRules);
		ItemObj->TryGetStringField(TEXT("categoryId"), NewItem.CategoryId);
		ItemObj->TryGetStringField(TEXT("isSelf"), NewItem.IsSelf);

		if (ItemObj->HasTypedField<EJson::Number>(TEXT("isCollect")))
		{
			NewItem.IsCollect = ItemObj->GetIntegerField(TEXT("isCollect")) == 0;
		}
		else
		{
			NewItem.IsCollect = false;
		}

		if (ItemObj->HasTypedField<EJson::Array>(TEXT("associationList")))
		{
			const TArray<TSharedPtr<FJsonValue>>& AssociationList = ItemObj->GetArrayField(TEXT("associationList"));
			for (const TSharedPtr<FJsonValue>& AssociationValue : AssociationList)
			{
				const TSharedPtr<FJsonObject>& AssociationObj = AssociationValue->AsObject();
				if (!AssociationObj)
				{
					continue;
				}

				FString CodeName, CodeValue;
				AssociationObj->TryGetStringField(TEXT("dictName"), CodeName);
				AssociationObj->TryGetStringField(TEXT("dictValue"), CodeValue);

				NewItem.RelativeCodes.Add(CodeName, CodeValue);
			}
		}

		if (ItemObj->HasTypedField<EJson::Array>(TEXT("resourceList")))
		{
			const TArray<TSharedPtr<FJsonValue>>& ResourceFiles = ItemObj->GetArrayField(TEXT("resourceList"));
			for (const TSharedPtr<FJsonValue>& ResourceFile : ResourceFiles)
			{
				const TSharedPtr<FJsonObject>& ResourceFileObj = ResourceFile->AsObject();
				if (!ResourceFileObj)
				{
					continue;
				}
				FDSResourceFile& NewResourceFile = NewItem.ResourceList.AddDefaulted_GetRef();
				FJsonObjectConverter::JsonObjectToUStruct(ResourceFileObj.ToSharedRef(), &NewResourceFile);
			}
		}
		if (ItemObj->HasTypedField<EJson::Array>(TEXT("tagList")))
		{
			const TArray<TSharedPtr<FJsonValue>>& ResourceFiles = ItemObj->GetArrayField(TEXT("tagList"));
			for (const TSharedPtr<FJsonValue>& ResourceFile : ResourceFiles)
			{
				const TSharedPtr<FJsonObject>& ResourceFileObj = ResourceFile->AsObject();
				if (!ResourceFileObj)
				{
					continue;
				}
				FDSTagInfo& NewTagInfo = NewItem.TagList.AddDefaulted_GetRef();
				FJsonObjectConverter::JsonObjectToUStruct(ResourceFileObj.ToSharedRef(), &NewTagInfo);
			}
		}
		OutList.Add(NewItem);
	}
}

void UDesignStationFunctionLibrary::ParseJsonToCollectionList(const TArray<TSharedPtr<FJsonValue>>& ResourceItemList, TArray<FDSCollectionInfo>& OutList)
{
	for (const TSharedPtr<FJsonValue>& ItemValue : ResourceItemList)
	{
		const TSharedPtr<FJsonObject>& ItemObj = ItemValue->AsObject();
		if (!ItemObj)
		{
			continue;
		}
		FDSCollectionInfo NewItem;
		if (ItemObj->HasTypedField<EJson::Number>(TEXT("type")))
		{
			NewItem.Type = static_cast<EDSCollectionCategory>(ItemObj->GetIntegerField(TEXT("type")));
		}
		if (ItemObj->HasTypedField<EJson::Number>(TEXT("resourceType")))
		{
			NewItem.CollectionType = static_cast<EDSCollectionType>(ItemObj->GetIntegerField(TEXT("resourceType")));
		}
		else
		{
			NewItem.CollectionType = static_cast<EDSCollectionType>(NewItem.Type);
		}

		ItemObj->TryGetNumberField(TEXT("id"), NewItem.Id);
		ItemObj->TryGetStringField(TEXT("resourceName"), NewItem.Name);
		ItemObj->TryGetStringField(TEXT("resourceId"), NewItem.CollectionID);
		ItemObj->TryGetStringField(TEXT("width"), NewItem.Width);
		ItemObj->TryGetStringField(TEXT("height"), NewItem.Height);
		ItemObj->TryGetStringField(TEXT("depth"), NewItem.Depth);
		ItemObj->TryGetStringField(TEXT("brand"), NewItem.Brand);
		ItemObj->TryGetStringField(TEXT("productImg"), NewItem.ProductImg);

		ItemObj->TryGetStringField(TEXT("filePath"), NewItem.ResourcePath);
		ItemObj->TryGetNumberField(TEXT("groupId"), NewItem.ClassificationId);

		if (ItemObj->HasTypedField<EJson::Array>(TEXT("resourceList")))
		{
			const auto& ResourceList = ItemObj->GetArrayField(TEXT("resourceList"));

			for (auto& Iter : ResourceList)
			{
				const TSharedPtr<FJsonObject>& IterObj = Iter->AsObject();

				TTuple<FString, EDSResourceType> Pair;
				IterObj->TryGetStringField(TEXT("resourceId"), Pair.Key);
				Pair.Value = static_cast<EDSResourceType>(IterObj->GetIntegerField(TEXT("type")));
				NewItem.ResourceList.Add(Pair);
			}
		}
		OutList.Add(NewItem);
	}
}

void UDesignStationFunctionLibrary::ParseJsonToCollectionClassificaitonList(const TArray<TSharedPtr<FJsonValue>>& ResourceItemList, TArray<FDSCollectionClassificationInfo>& OutList)
{
	for (const TSharedPtr<FJsonValue>& ItemValue : ResourceItemList)
	{
		const TSharedPtr<FJsonObject>& ItemObj = ItemValue->AsObject();
		if (!ItemObj)
		{
			continue;
		}
		FDSCollectionClassificationInfo NewItem;
		ItemObj->TryGetNumberField(TEXT("id"), NewItem.ID);
		ItemObj->TryGetStringField(TEXT("name"), NewItem.Name);
		OutList.Add(NewItem);
	}
}

EDSModelType UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(int32 InType)
{
	static TMap<int32, EDSModelType> TypeIdMap = {
		// 柜体 LXGT
		{0, EDSModelType::E_Custom_UpperCabinet}, {1, EDSModelType::E_Custom_WallCabinet}, {2, EDSModelType::E_Custom_BaseCabinet},
		{3, EDSModelType::E_Custom_TallCabinet}, {4, EDSModelType::E_Custom_CornerCabinet}, {5, EDSModelType::E_Custom_Tatami},
		{6, EDSModelType::E_Custom_DrawerBox}, {7, EDSModelType::E_Custom_CornerCutCabinet}, {8, EDSModelType::E_Custom_WallBoardCabinet},
		{9, EDSModelType::E_Custom_LayoutDoor},

		// 功能件 LXGNJ
		{21, EDSModelType::E_Custom_Board}, {22, EDSModelType::E_Custom_FunctionalDrawer}, {23, EDSModelType::E_Custom_Functional_Hardware},
		{24, EDSModelType::E_Custom_Functional_HangingRod}, {25, EDSModelType::E_Custom_Functional_Combine},

		// 门板 LXMB
		{41, EDSModelType::E_Custom_DoorPanel}, {42, EDSModelType::E_Custom_DoorPanel_Flat}, {43, EDSModelType::E_Custom_DoorPanel_SolidWood},
		{44, EDSModelType::E_Custom_DoorPanel_AluminumFrame}, {45, EDSModelType::E_Custom_DoorPanel_Glass}, {46, EDSModelType::E_Custom_DoorPanel_Fake},

		// 调整板 LXTZB
		{61, EDSModelType::E_Custom_AdjustablePanel},

		// 封板 LXFB
		{71, EDSModelType::E_Custom_TopClosurePanel}, {72, EDSModelType::E_Custom_SideClosurePanel},

		// 五金 LXWJ
		{81, EDSModelType::E_Custom_Handle}, {82, EDSModelType::E_Custom_Hinge}, {83, EDSModelType::E_Custom_Track},
		{84, EDSModelType::E_Custom_HangingRod}, {85, EDSModelType::E_Custom_Leg}, {86, EDSModelType::E_Custom_Hardware},
		{87, EDSModelType::E_Custom_HandleFree},

		// 罗马柱类型 LXLMZ
		{91, EDSModelType::E_Custom_RomanColumn},

		// 顶线类型 LXDX
		{96, EDSModelType::E_Custom_TopMolding},

		// 下拖线类型 LXXTX
		{101, EDSModelType::E_Custom_BottomTrim},

		// 踢脚线类型 LXTJX
		{106, EDSModelType::E_Custom_KickBoard},

		// 成品家具 LXCPJJ
		{111, EDSModelType::E_Custom_SoftFurniture},

		// 软装饰品 LXRZSP
		{151, EDSModelType::E_Custom_SoftDecoration},

		// 电器 LXDQ
		{201, EDSModelType::E_Custom_RangeHood}, {202, EDSModelType::E_Custom_Stove},

		// 水槽 LXSC
		{221, EDSModelType::E_Custom_Sink},

		// 龙头 LXLT
		{225, EDSModelType::E_Custom_Faucet},

		// 台面 LXTM
		{231, EDSModelType::E_Custom_CounterTop},

		// 柜体板 LXGTB
		{241, EDSModelType::E_Custom_CabinetBoard},

		// 门芯 LXMX
		{261, EDSModelType::E_Custom_DoorCore},

		// 附件配件 LXFJPJ
		{281, EDSModelType::E_Custom_Accessory},

		// 扣手 LXKS
		{301, EDSModelType::E_Custom_Buckle},

		// 成品吊顶 LXCPDD
		{311, EDSModelType::E_Furniture_MoldingCeiling},

		// 拉手 LXLS
		{321, EDSModelType::E_Custom_Knob},

		// 五金门铰方案，区别于铰链
		{331, EDSModelType::E_Custom_HingePlan},

		// 五金路轨方案
		{336, EDSModelType::E_Custom_HardwareTrackPlane},

		// 户型门板 LXHXMB
		{341, EDSModelType::E_Custom_LayoutDoor_Board},

		// 玻璃门芯 LXBLMX
		{351, EDSModelType::E_Custom_GlassDoorCore},

		//门容器
		{361, EDSModelType::E_Custom_DoorContainer}
	};

	return TypeIdMap.Contains(InType) ? TypeIdMap[InType] : EDSModelType::E_None;
}

TArray<FVector> UDesignStationFunctionLibrary::GetActorOrientedBoundingBox(AActor* InActor)
{
	TArray<FVector> Result;
	Result.AddDefaulted(8);

	if (InActor == nullptr || InActor->GetRootComponent() == nullptr)
	{
		return Result;
	}

	FBoxSphereBounds ActorBox(ForceInitToZero);

	TArray<USceneComponent*> AllChildrenComponents;
	InActor->GetRootComponent()->GetChildrenComponents(true, AllChildrenComponents);
	for (USceneComponent* ChildComponent : AllChildrenComponents)
	{
		if (UPrimitiveComponent* PrimComponent = Cast<UPrimitiveComponent>(ChildComponent))
		{
			if (PrimComponent->IsVisible() && PrimComponent->IsCollisionEnabled())
			{
				FBoxSphereBounds ComponentBox = PrimComponent->GetLocalBounds();
				ActorBox = ActorBox + ComponentBox.TransformBy(PrimComponent->GetRelativeTransform());
			}
		}
	}

	// Four top points.
	Result[0] = FVector(ActorBox.Origin.X - ActorBox.BoxExtent.X, ActorBox.Origin.Y - ActorBox.BoxExtent.Y, ActorBox.Origin.Z + ActorBox.BoxExtent.Z);
	Result[1] = FVector(ActorBox.Origin.X + ActorBox.BoxExtent.X, ActorBox.Origin.Y - ActorBox.BoxExtent.Y, ActorBox.Origin.Z + ActorBox.BoxExtent.Z);
	Result[2] = FVector(ActorBox.Origin.X + ActorBox.BoxExtent.X, ActorBox.Origin.Y + ActorBox.BoxExtent.Y, ActorBox.Origin.Z + ActorBox.BoxExtent.Z);
	Result[3] = FVector(ActorBox.Origin.X - ActorBox.BoxExtent.X, ActorBox.Origin.Y + ActorBox.BoxExtent.Y, ActorBox.Origin.Z + ActorBox.BoxExtent.Z);

	// Four bottom points.
	Result[4] = Result[0] - FVector(0.0f, 0.0f, ActorBox.Origin.Z + ActorBox.BoxExtent.Z);
	Result[5] = Result[1] - FVector(0.0f, 0.0f, ActorBox.Origin.Z + ActorBox.BoxExtent.Z);
	Result[6] = Result[2] - FVector(0.0f, 0.0f, ActorBox.Origin.Z + ActorBox.BoxExtent.Z);
	Result[7] = Result[3] - FVector(0.0f, 0.0f, ActorBox.Origin.Z + ActorBox.BoxExtent.Z);

	for (FVector& Point : Result)
	{
		Point = InActor->GetActorTransform().TransformPosition(Point);
	}

	return Result;
}

FBoxSphereBounds UDesignStationFunctionLibrary::GetActorAxisAlignedBoundingBox_Recursively(AActor* InActor)
{
	FBoxSphereBounds Result(ForceInitToZero);

	if (InActor == nullptr)
	{
		return Result;
	}

	TArray<USceneComponent*> AllChildrenComponents;
	InActor->GetRootComponent()->GetChildrenComponents(true, AllChildrenComponents);
	for (USceneComponent* ChildComponent : AllChildrenComponents)
	{
		if (UPrimitiveComponent* PrimComponent = Cast<UPrimitiveComponent>(ChildComponent))
		{
			if (PrimComponent->IsVisible() && PrimComponent->IsCollisionEnabled())
			{
				FBoxSphereBounds ComponentBox = PrimComponent->GetLocalBounds();
				Result = Result + ComponentBox.TransformBy(PrimComponent->GetRelativeTransform());
			}
		}
	}

	Result = Result.TransformBy(InActor->GetActorTransform());

	return Result;
}

TArray<FVector> UDesignStationFunctionLibrary::GetRectangularMeshFromBox(const FBoxSphereBounds& InBox, const FVector& InDir)
{
	/**
	* Vertices order : 
	* 
	*       1+++++++++++2
	*      + +         ++
	*     +  +        + +
	*    +   +       +  +
	*   0+++++++++++3   +
	*   +    +      +   +
	*   +    5++++++++++6
	*   +   +       +  +
	*   + +         + +
	*   4+++++++++++7
	*/
	TArray<FVector> Vertices = {
		InBox.Origin + FVector(-InBox.BoxExtent.X, -InBox.BoxExtent.Y, InBox.BoxExtent.Z),
		InBox.Origin + FVector(InBox.BoxExtent.X, -InBox.BoxExtent.Y, InBox.BoxExtent.Z),
		InBox.Origin + FVector(InBox.BoxExtent.X, InBox.BoxExtent.Y, InBox.BoxExtent.Z),
		InBox.Origin + FVector(-InBox.BoxExtent.X, InBox.BoxExtent.Y, InBox.BoxExtent.Z),
		InBox.Origin + FVector(-InBox.BoxExtent.X, -InBox.BoxExtent.Y, -InBox.BoxExtent.Z),
		InBox.Origin + FVector(InBox.BoxExtent.X, -InBox.BoxExtent.Y, -InBox.BoxExtent.Z),
		InBox.Origin + FVector(InBox.BoxExtent.X, InBox.BoxExtent.Y, -InBox.BoxExtent.Z),
		InBox.Origin + FVector(-InBox.BoxExtent.X, InBox.BoxExtent.Y, -InBox.BoxExtent.Z)
	};

	TArray<FVector> Result;

	if (InDir == FVector::UpVector)
	{
		Result = {Vertices[0], Vertices[1], Vertices[2], Vertices[3]};
	}
	else if (InDir == FVector::DownVector)
	{
		Result = {Vertices[4], Vertices[5], Vertices[6], Vertices[7]};
	}
	else if (InDir == FVector::LeftVector)
	{
		Result = {Vertices[4], Vertices[5], Vertices[1], Vertices[0]};
	}
	else if (InDir == FVector::ForwardVector)
	{
		Result = {Vertices[1], Vertices[5], Vertices[6], Vertices[2]};
	}
	else if (InDir == FVector::RightVector)
	{
		Result = {Vertices[2], Vertices[6], Vertices[7], Vertices[3]};
	}
	else if (InDir == FVector::BackwardVector)
	{
		Result = {Vertices[4], Vertices[0], Vertices[3], Vertices[7]};
	}

	return Result;
}

bool UDesignStationFunctionLibrary::RayTriangleIntersection(const FVector& RayOrigin, const FVector& RayDir,
                                                            const FVector& V0, const FVector& V1, const FVector& V2, FVector& OutIntersectionPoint)
{
	constexpr float EPSILON = 1e-6f; // 容差值，用于浮点比较
	FVector EdgeVector1 = V1 - V0; // 三角形的一条边
	FVector EdgeVector2 = V2 - V0; // 三角形的另一条边

	// 计算叉积向量h：射线方向和三角形的边向量EdgeVector2的叉积
	FVector CrossProductH = FVector::CrossProduct(RayDir, EdgeVector2);
	float DotProductA = FVector::DotProduct(EdgeVector1, CrossProductH);

	// 如果DotProductA接近0，表示射线与三角形平行或在同一平面上
	if (FMath::Abs(DotProductA) < EPSILON)
	{
		return false;
	}

	// 计算比例因子和向量s
	float InverseDotProductA = 1.0f / DotProductA;
	FVector VectorS = RayOrigin - V0;
	float BarycentricCoordinateU = InverseDotProductA * FVector::DotProduct(VectorS, CrossProductH);

	// 如果BarycentricCoordinateU不在[0,1]范围内，则射线不穿过三角形
	if (BarycentricCoordinateU < 0.0f || BarycentricCoordinateU > 1.0f)
	{
		return false;
	}

	// 计算叉积向量q和重心坐标v
	FVector CrossProductQ = FVector::CrossProduct(VectorS, EdgeVector1);
	float BarycentricCoordinateV = InverseDotProductA * FVector::DotProduct(RayDir, CrossProductQ);

	// 如果BarycentricCoordinateV不在[0,1]范围内或者U + V > 1，表示射线不穿过三角形
	if (BarycentricCoordinateV < 0.0f || BarycentricCoordinateU + BarycentricCoordinateV > 1.0f)
	{
		return false;
	}

	// 计算射线参数t，表示相交点在射线上的距离
	float RayParameterT = InverseDotProductA * FVector::DotProduct(EdgeVector2, CrossProductQ);

	// 如果RayParameterT > EPSILON，表示射线与三角形相交
	if (RayParameterT > EPSILON)
	{
		// 计算相交点的坐标
		OutIntersectionPoint = RayOrigin + RayParameterT * RayDir;
		return true;
	}

	// 否则没有相交
	return false;
}

bool UDesignStationFunctionLibrary::LineLineIntersection(const FVector& AStart, const FVector& AEnd,
                                                         const FVector& BStart, const FVector& BEnd, FVector& OutIntersectionPoint)
{
	// 计算两个方向向量
	FVector D1 = AEnd - AStart; // 直线A的方向向量
	FVector D2 = BEnd - BStart; // 直线B的方向向量
	FVector P21 = BStart - AStart; // 两条直线起点的差向量
	FVector CrossD1D2 = FVector::CrossProduct(D1, D2);

	// 检测方向向量是否平行
	if (CrossD1D2.SizeSquared() == 0)
	{
		return false; // 直线平行，无交点
	}

	// 计算 t 参数
	float t = FVector::DotProduct(FVector::CrossProduct(P21, D2), CrossD1D2) / CrossD1D2.SizeSquared();

	// 计算交点
	OutIntersectionPoint = AStart + t * D1;

	return true;
}

bool UDesignStationFunctionLibrary::LineLineIntersection_ConsiderDifferentPlane(const FVector& AStart, const FVector& AEnd, const FVector& BStart, const FVector& BEnd, FVector& OutIntersectionPoint,
                                                                                double Tolerance)
{
	FVector Line1Direction = AEnd - AStart;
	FVector Line2Direction = BEnd - BStart;
	FVector Line1ToLine2 = AStart - BStart;
	float a = FVector::DotProduct(Line1Direction, Line1Direction); // always >= 0
	float b = FVector::DotProduct(Line1Direction, Line2Direction);
	float c = FVector::DotProduct(Line2Direction, Line2Direction); // always >= 0
	float d = FVector::DotProduct(Line1Direction, Line1ToLine2);
	float e = FVector::DotProduct(Line2Direction, Line1ToLine2);
	float denominator = a * c - b * b; // always >= 0

	// if the lines are almost parallel
	if (denominator < Tolerance)
	{
		return false;
	}

	float sc = (b * e - c * d) / denominator;
	float tc = (a * e - b * d) / denominator;

	// get the intersection point
	FVector IntersectionPointLine1 = AStart + sc * Line1Direction;
	FVector IntersectionPointLine2 = BStart + tc * Line2Direction;

	// check if the intersection points are the same (i.e., the lines intersect)
	if (!IntersectionPointLine1.Equals(IntersectionPointLine2, Tolerance))
	{
		return false;
	}

	OutIntersectionPoint = IntersectionPointLine1;
	return true;
}

FVector UDesignStationFunctionLibrary::TransformPointToXYPlane(const FVector& Point, const FPlane& OriginalPlane)
{
	FRotator Rotation = FRotationMatrix::MakeFromZ(OriginalPlane.GetNormal()).Rotator();
	return FRotationMatrix(Rotation).InverseTransformPosition(Point);
}

bool UDesignStationFunctionLibrary::ClipLineWithRect(const FVector2D& Start, const FVector2D& End,
                                                     const FSlateRect& Rect, FVector2D& OutClippedStart, FVector2D& OutClippedEnd)
{
	float t0 = 0.0f;
	float t1 = 1.0f;
	FVector2D d = End - Start;

	// 定义四个不等式对应的 p 和 q 数值
	auto clipTest = [&](float p, float q) -> bool
	{
		if (p == 0)
		{
			// 如果线段与边平行且 q < 0，则无交点
			return q >= 0;
		}
		float t = q / p;
		if (p < 0)
		{
			// 更新 t0
			if (t > t0)
			{
				t0 = t;
			}
		}
		else
		{
			// 更新 t1
			if (t < t1)
			{
				t1 = t;
			}
		}
		return t0 <= t1;
	};

	// 分别处理左右上下边界
	// Left: x >= xmin  => -d.x * t <= Start.x - xmin
	if (!clipTest(-d.X, Start.X - Rect.Left))
	{
		return false;
	}
	// Right: x <= xmax => d.x * t <= Rect.Right - Start.X
	if (!clipTest(d.X, Rect.Right - Start.X))
	{
		return false;
	}
	// Top: y >= ymin => -d.Y * t <= Start.Y - Rect.Top
	if (!clipTest(-d.Y, Start.Y - Rect.Top))
	{
		return false;
	}
	// Bottom: y <= ymax => d.Y * t <= Rect.Bottom - Start.Y
	if (!clipTest(d.Y, Rect.Bottom - Start.Y))
	{
		return false;
	}

	// 如果t0 > t1，则线段完全在矩形外
	if (t0 > t1)
	{
		return false;
	}

	OutClippedStart = Start + d * t0;
	OutClippedEnd = Start + d * t1;
	return true;
}

FAdaptiveAdsorptionRule3D UDesignStationFunctionLibrary::GetPlaceRuleData(const FRefToLocalFileData& CatalogData)
{
	return GetPlaceRuleData(CatalogData.PlaceRuleCustomData);
}

FAdaptiveAdsorptionRule3D UDesignStationFunctionLibrary::GetPlaceRuleData(const FRefPlaceRuleCustomData& CatalogPlaceRuleData)
{
	FAdaptiveAdsorptionRule3D Res;
	Res.XAxisRule = ConvertCatalogPlaceRuleData(
		CatalogPlaceRuleData.isLeft == 0, CatalogPlaceRuleData.leftConfig,
		CatalogPlaceRuleData.isRight == 0, CatalogPlaceRuleData.rightConfig
	);
	Res.YAxisRule = ConvertCatalogPlaceRuleData(
		CatalogPlaceRuleData.isFront == 0, CatalogPlaceRuleData.frontConfig,
		CatalogPlaceRuleData.isAfter == 0, CatalogPlaceRuleData.afterConfig
	);
	Res.ZAxisRule = ConvertCatalogPlaceRuleData(
		CatalogPlaceRuleData.isDown == 0, CatalogPlaceRuleData.downConfig,
		CatalogPlaceRuleData.isUpper == 0, CatalogPlaceRuleData.upperConfig
	);
	return Res;
}

FAdaptiveAndSnapRule UDesignStationFunctionLibrary::ConvertCatalogPlaceRuleData(bool NegativeConfig, const FString& NegativeOffset, bool PositiveConfig, const FString& PositiveOffset)
{
	FAdaptiveAndSnapRule Res;
	//flag
	Res.bAdaptived = NegativeConfig && PositiveConfig;
	//rule
	if (NegativeConfig)
	{
		Res.AdsorptionRule = EAdsorptionRule::E_Negative;
	}
	else if (PositiveConfig)
	{
		Res.AdsorptionRule = EAdsorptionRule::E_Positive;
	}
	else
	{
		Res.AdsorptionRule = EAdsorptionRule::E_Free;
	}
	//offset
	Res.NegativeOffset = FCString::Atof(*NegativeOffset) * 0.1f;
	Res.PositiveOffset = FCString::Atof(*PositiveOffset) * 0.1f;

	return Res;
}

int32 UDesignStationFunctionLibrary::FormatValueToInt(const FString& InVal)
{
	float NewValue = FCString::Atof(*InVal);
	return static_cast<int32>(NewValue);
}

int32 UDesignStationFunctionLibrary::FormatValueToInt(const float& InVal)
{
	return static_cast<int32>(InVal);
}

int32 UDesignStationFunctionLibrary::FormatValueToInt(const double& InVal)
{
	return static_cast<int32>(InVal);
}

int32 UDesignStationFunctionLibrary::FormatCalValueToUIShow(const FString& InVal)
{
	float NewValue = FCString::Atof(*InVal) * UNIT_CM_TO_MM;
	return FormatValueToInt(NewValue);
}

int32 UDesignStationFunctionLibrary::FormatCalValueToUIShow(const float& InVal)
{
	return FormatValueToInt(InVal * UNIT_CM_TO_MM);
}

int32 UDesignStationFunctionLibrary::FormatCalValueToUIShow(const double& InVal)
{
	return FormatValueToInt(InVal * UNIT_CM_TO_MM);
}

float UDesignStationFunctionLibrary::FormatUIValueToCalc(const FString& InVal)
{
	float NewValue = FCString::Atof(*InVal) * UNIT_MM_TO_CM;
	return NewValue;
}

float UDesignStationFunctionLibrary::FormatUIValueToCalc(const float& InVal)
{
	return 0.0f;
}

bool UDesignStationFunctionLibrary::OpenFileDialog(const FString& DialogTitle, const FString& DefaultPath, TArray<FString>& OutFileNames, FString _FileType, bool _bMulty)
{
	const void* ParentWindowWindowHandle = FSlateApplication::Get().FindBestParentWindowHandleForDialogs(nullptr);
	int32 DummyFilterIndex;
	const bool bFolderSelected = FileDialogShared(false, ParentWindowWindowHandle, DialogTitle, DefaultPath, TEXT(""), _FileType, _bMulty ? EFileDialogFlags::Multiple : EFileDialogFlags::None,
	                                              OutFileNames, DummyFilterIndex);
	if (bFolderSelected)
	{
		return true;
	}
	return false;
}

bool UDesignStationFunctionLibrary::SaveFileDialog(const FString& DialogTitle, const FString& DefaultPath, const FString& DefaultFile, TArray<FString>& OutFileNames, const FString& FileType)
{
	const void* ParentWindowWindowHandle = FSlateApplication::Get().FindBestParentWindowHandleForDialogs(nullptr);
	int32 DummyFilterIndex = 0;
	const bool bFolderSelected = FileDialogShared(true, ParentWindowWindowHandle, DialogTitle, DefaultPath, DefaultFile, FileType, EFileDialogFlags::None, OutFileNames, DummyFilterIndex);
	if (bFolderSelected)
	{
		return true;
	}
	return false;
}

FVector UDesignStationFunctionLibrary::ComputeSSCubeDirectionAtTexelCenter(uint32 x, uint32 y, float InvSideExtent)
{
	// center of the texels
	FVector DirectionSS((static_cast<float>(x) + 0.5f) * InvSideExtent * 2 - 1, (static_cast<float>(y) + 0.5f) * InvSideExtent * 2 - 1, 1);
	DirectionSS.Normalize();
	return DirectionSS;
}

FVector UDesignStationFunctionLibrary::ComputeWSCubeDirectionAtTexelCenter(uint32 CubemapFace, uint32 x, uint32 y,
                                                                           float InvSideExtent)
{
	FVector DirectionSS = ComputeSSCubeDirectionAtTexelCenter(x, y, InvSideExtent);
	FVector DirectionWS = TransformSideToWorldSpace(CubemapFace, DirectionSS);
	return DirectionWS;
}

FVector UDesignStationFunctionLibrary::TransformSideToWorldSpace(uint32 CubemapFace, const FVector& InDirection)
{
	double x = InDirection.X, y = InDirection.Y, z = InDirection.Z;

	FVector Ret;

	// see http://msdn.microsoft.com/en-us/library/bb204881(v=vs.85).aspx
	switch (CubemapFace)
	{
	default: checkSlow(0);
	case 0: Ret = FVector(+z, -y, -x);
		break;
	case 1: Ret = FVector(-z, -y, +x);
		break;
	case 2: Ret = FVector(+x, +z, +y);
		break;
	case 3: Ret = FVector(+x, -z, -y);
		break;
	case 4: Ret = FVector(+x, -y, +z);
		break;
	case 5: Ret = FVector(-x, -y, -z);
		break;
	}

	// this makes it with the Unreal way (z and y are flipped)
	return FVector(Ret.X, Ret.Z, Ret.Y);
}
