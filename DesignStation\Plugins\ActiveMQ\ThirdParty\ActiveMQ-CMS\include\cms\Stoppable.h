/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef _CMS_STOPPABLE_H
#define _CMS_STOPPABLE_H

#include <cms/Config.h>
#include <cms/CMSException.h>

namespace cms {

    /**
     * Interface for a class that implements the stop method.
     *
     * An object that implements this interface implies that it will halt all operations
     * that result in events being propagated to external users, internally the Object
     * can continue to process data but not events will be generated to clients and methods
     * that return data will not return valid results until the object is started again.
     *
     * @since 1.0
     */
    class CMS_API Stoppable {
    public:

        virtual ~Stoppable();

        /**
         * Stops this service.
         *
         * @throws CMSException - if an internal error occurs while stopping the Service.
         */
        virtual void stop() = 0;

    };
}

#endif /*_CMS_STOPPABLE_H*/
