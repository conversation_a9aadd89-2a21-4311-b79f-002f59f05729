﻿#pragma once

#include "CoreMinimal.h"
#include "Component/MultiComponentDataDefine.h"
#include "Components/BoxComponent.h"
#include "MeshData/GeneratedMeshData.h"
#include "MeshInfo/DSCustomMeshInfo.h"
#include "SubSystems/MVC/View/DSBaseView.h"
#include "DSCupboardBaseView.generated.h"

class FParallelGenerateMeshWorker;
struct FParallelGenerateMeshResult;

DECLARE_LOG_CATEGORY_EXTERN(DSCupboardBaseViewLog, Log, All);

UCLASS()
class ADSCupboardBaseView : public ADSBaseView
{
	GENERATED_BODY()

protected:
	UPROPERTY()
	UBoxComponent* MoveMeshComponent;

	UPROPERTY()
	UBoxComponent* ReallyMeshComponent;

	UPROPERTY()
	FDSCustomMeshInfo CustomMeshInfo;

public:
	ADSCupboardBaseView();

	virtual void Init() override;

	virtual void RealSpawnViewLogic(UDSBaseModel* InModel) override;
	virtual void RealUpdateViewLogic(UDSBaseModel* InModel) override;
	virtual void DeleteView() override;

	virtual void AttachToThis()
	{
	}

	virtual void DetachFromThis()
	{
	}

	virtual FBox GetBoundBox() override;

	void SetupCollisionIgnoreActor();
	void AddDSIgnoreActors(const TArray<AActor*>& InIgnores);

	FDSCustomMeshInfo GetCustomMeshInfo() { return CustomMeshInfo; };

	virtual void AddToIgnoreDSView(ADSBaseView* ToIgnoreView) override;

	virtual bool IsGeneratingMesh() const;

	virtual	void GetModelCollectionCaptureComponentsAndActors(TArray<UPrimitiveComponent*>& ShowComponents, TArray<AActor*>& ShowActors) override;


protected:
	virtual void BeginPlay() override;

	virtual void ResizeMeshPool();
	virtual void ClearMeshData();
	virtual void GenerateMesh(const TSharedPtr<FMultiComponentDataItem>& InComponentData, FShowMultiComponentActorProperty& OutMesh, const FString& InDMValue, const TArray<FString>& NodesToDisableCollision, const FTransform& ParentTransform = FTransform());
	virtual void CreateOutlineComponent(const TArray<TPair<FVector, FVector>>& OutlinePairs, const FTransform& Transform);
	virtual void CreatePakActor(const FShowSingleComponentActorWithTransformProperty& MeshProperty, const FString& InNodeUUID, const FString& InNodeName,
		const FTransform& Transform, bool bCloseCollision);
	virtual void CreateCustomMeshComponent(int32 ComponentIndex, const FShowSingleComponentActorWithTransformProperty& MeshProperty,
		const TArray<TArray<FVector>>& MeshCollision, const FTransform& Transform,
		const FString& InNodeUUID, const FString& InNodeName, bool bCloseCollision);

	virtual void RefreshMesh(FMultiComponentDataItem& TreeData, const TArray<FString>& NodesToDisableCollision);

	virtual void RealHoverViewLogic(UDSBaseModel* InModel) override;
	virtual void RealUnHoverViewLogic(UDSBaseModel* InModel) override;

	virtual void RealSelectViewLogic(UDSBaseModel* InModel) override;
	virtual void RealUnSelectViewLogic(UDSBaseModel* InModel) override;

	virtual void RealHiddenViewLogic(UDSBaseModel* InModel) override;
	virtual void RealUnHiddenViewLogic(UDSBaseModel* InModel) override;

	virtual void RealOverlapViewLogic(UDSBaseModel* InModel) override;
	virtual void RealUnOverlapViewLogic(UDSBaseModel* InModel) override;

	virtual void RealDisableViewLogic(UDSBaseModel* InModel) override;
	virtual void RealEnableViewLogic(UDSBaseModel* InModel) override;

	virtual void RealRefreshViewMaterialLogic(UDSBaseModel* InModel) override;

	virtual void OnGenerateMeshComplete(TSharedPtr<FParallelGenerateMeshResult> GeneratedResult);
	virtual void OnGenerateMeshWorkFinished();

	UFUNCTION()
	void OnDSActorBeginOverlapHandle(AActor* OverlappedActor, AActor* OtherActor);
	UFUNCTION()
	void OnDSActorEndOverlapHandle(AActor* OverlappedActor, AActor* OtherActor);
	UFUNCTION()
	void OnDSActorHitHandle(AActor* SelfActor, AActor* OtherActor, FVector NormalImpulse, const FHitResult& Hit);

	bool IgnoreActorOverlap(AActor* OverlappedActor);
	bool IgnoreActorOverlap(AActor* OverlappedActor, AActor* OtherActor);

	//component overlap
	UFUNCTION()
	void OnThisDSComponentBeginOverlapHandle(UPrimitiveComponent* OverlappedComponent,
	                                         AActor* OtherActor,
	                                         UPrimitiveComponent* OtherComp,
	                                         int32 OtherBodyIndex,
	                                         bool bFromSweep,
	                                         const FHitResult& SweepResult);

	UFUNCTION()
	void OnThisDSComponentEndOverlapHandle(UPrimitiveComponent* OverlappedComponent,
	                                       AActor* OtherActor,
	                                       UPrimitiveComponent* OtherComp,
	                                       int32 OtherBodyIndex);

	//temp
	void OnOverlapInner();
	void OnUnOverlapInner();

	//for layout door in 2d
	void Generate2DMesh();

	bool CupboardShouldEndOverlap(ADSBaseView* OverlappedActor);

	void SetNodeUUIDInComponentTags(UActorComponent* InComponent, const FString& InNodeUUID,const FString& InNodeName);
	void SetNodeUUIDInActorTags(AActor* InActor, const FString& InNodeUUID, const FString& InNodeName);
protected:
	bool bIsSeparatePart;

	TSharedPtr<FParallelGenerateMeshWorker> GenerateMeshWorker;
	TArray<FShowMultiComponentActorProperty> ParsedComponents;

	UPROPERTY(EditAnywhere)
	UProceduralMeshComponent* Cupboard2DComponent;
};
