// Fill out your copyright notice in the Description page of Project Settings.


#include "SubSystems/Construction/DSConstructionCore.h"
#include "Subsystems/MVC/Model/Custom/Library/DSCupboardLibrary.h"

namespace DSFrameAttDefName
{
    const FString Name_DRAWING_NAME = TEXT("DRAWING_NAME");
    const FString Name_room_name = TEXT("room_name");
    const FString Name_style_name = TEXT("style_name");
    const FString Name_USERNAME = TEXT("USERNAME");
    const FString Name_USERPHONE = TEXT("USERPHONE");
    const FString Name_DESIGNERNAME = TEXT("DESIGNERNAME");
    const FString Name_DESIGNERPHONE = TEXT("DESIGNERPHONE");
    const FString Name_CITYNAMEBUILDNAMEANDMODELNAME = TEXT("CITYNAMEBUILDNAMEANDMODELNAME");
    const FString Name_most_door_board_material_name = TEXT("most_door_board_material_name");
    const FString Name_most_door_board_material_name2 = TEXT("most_door_board_material_name2");
    const FString Name_most_door_board_material_name3 = TEXT("most_door_board_material_name3");
    const FString Name_most_door_board_name = TEXT("most_door_board_name");
    const FString Name_most_glass_core_name = TEXT("most_glass_core_name");
    const FString Name_most_knob_name = TEXT("most_knob_name");
    const FString Name_most_knob_name2 = TEXT("most_knob_name2");
    const FString Name_most_cabinet_board_material_name = TEXT("most_cabinet_board_material_name");
    const FString Name_most_cabinet_board_material_name2 = TEXT("most_cabinet_board_material_name2");
    const FString Name_most_cabinet_board_material_name3 = TEXT("most_cabinet_board_material_name3");
    const FString Name_most_hinge_name = TEXT("most_hinge_name");
    const FString Name_most_down_rail_name = TEXT("most_down_rail_name");
    const FString Name_table_surface_mateiralvo = TEXT("table_surface_mateiralvo");
    const FString Name_front_section_name1 = TEXT("front_section_name1");
    const FString Name_back_section_name1 = TEXT("back_section_name1");
};

//bool FDSConstructionData::HasMPolygonPrimitive() const
//{
//    if (Primitives.Num() > 0)
//    {
//        for (auto Ite : Primitives)
//        {
//            if (Ite != nullptr && Ite->PrimitiveType == EDSPrimitiveType::PrimitiveType_MPolygon)
//                return true;
//        }
//    }
//
//    return false;
//}

//TSharedPtr<FDSPrimitive_MPolygon> FDSConstructionData::GetMPolygonPrimitive() const
//{
//    if (Primitives.Num() > 0)
//    {
//        for (auto Ite : Primitives)
//        {
//            if (Ite != nullptr && Ite->PrimitiveType == EDSPrimitiveType::PrimitiveType_MPolygon)
//                return StaticCastSharedPtr<FDSPrimitive_MPolygon>(Ite);
//        }
//    }
//
//    return TSharedPtr<FDSPrimitive_MPolygon>();
//}

float FDSConstructionData::GetPrimitiveMaxZ() const
{
    FBox Box;
    if (Primitives.Num() > 0)
    {
        for (auto Ite : Primitives)
        {
            if (Ite != nullptr)
            {
                Box += Ite->GetBox();
            }
        }
    }
    return Box.Max.Z;
}

float FDSConstructionData::GetPrimitiveArea() const
{
    float TotalArea = 0;
    if (Primitives.Num() > 0)
    {
        for (auto Ite : Primitives)
        {
            if (Ite != nullptr)
            {
                TotalArea += Ite->GetArea();
            }
        }
    }

    return TotalArea;
}

bool FDSConstructionData::IsHasVaildPrimitive() const
{
    if (Primitives.Num() > 0)
    {
        for (auto Ite : Primitives)
        {
            if (Ite != nullptr && Ite->IsVaild())
            {
                return true;
            }
        }
    }

    return false;
}

bool FDSConstructionData::bIsCupboardDoor() const
{
    if (!Node.IsValid())
        return false;

    return UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), Node->ModelType);
}


void FDSConstructionData::GetAABB(FVector& MinPoint, FVector& MaxPoint)
{
	FBox Box;
    for(auto& Iter : Primitives)
    {
        if (Iter.IsValid()&& Iter->IsVaild())
        {
            Box+=Iter->GetBox();
        }

	}
    MinPoint = Box.Min;
	MaxPoint = Box.Max;
}

FBox FDSConstructionData::GetAABB() const
{
    FBox TotalBox;
    for (auto& Ite : Primitives)
    {
        if (Ite.IsValid())
        {
            TotalBox += Ite->GetBox();
        }
    }
    return TotalBox;
}


