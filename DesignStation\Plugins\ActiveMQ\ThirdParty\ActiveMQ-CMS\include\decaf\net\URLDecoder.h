/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef _DECAF_NET_URLDECODER_H_
#define _DECAF_NET_URLDECODER_H_

#include <decaf/util/Config.h>
#include <string>

namespace decaf{
namespace net{

    class DECAF_API URLDecoder {
    private:

        URLDecoder();

    public:

        virtual ~URLDecoder() {}

        /**
         * Decodes the string argument which is assumed to be encoded in the
         * <code>x-www-form-urlencoded</code> MIME content type.
         * <p>
         * '+' will be converted to space, '%' and two following hex digit
         * characters are converted to the equivalent byte value. All other
         * characters are passed through unmodified.
         * <p>
         * e.g. "A+B+C %24%25" -> "A B C $%"
         *
         * @param value - string The encoded string.
         * @return The decoded version as a string.
         */
        static std::string decode( const std::string& value );

    };

}}

#endif /*_DECAF_NET_URLDECODER_H_*/
