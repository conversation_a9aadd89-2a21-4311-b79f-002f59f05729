#include "DecimalMath.h"

FDecimal FDecimalMath::InvSqrt(const FDecimal& InVal)
{
	return FDecimal(1) / InVal;
}

FDecimal FDecimalMath::Abs(const FDecimal& InVal)
{
	return (InVal < 0) ? -InVal : InVal;
}

FDecimal FDecimalMath::Floor(const FDecimal& InVal)
{
	FDecimal Result;
	Result.InternalValue = boost::multiprecision::floor(InVal.InternalValue);
	return Result;
}

FDecimal FDecimalMath::Ceil(const FDecimal& InVal)
{
	FDecimal Result;
	Result.InternalValue = boost::multiprecision::ceil(InVal.InternalValue);
	return Result;
}

FDecimal FDecimalMath::Round(const FDecimal& InVal)
{
	FDecimal Result;
	Result.InternalValue = boost::multiprecision::round(InVal.InternalValue);
	return Result;
}

FDecimal FDecimalMath::Sqrt(const FDecimal& InVal)
{
	FDecimal Result;
	Result.InternalValue = boost::multiprecision::sqrt(InVal.InternalValue);
	return Result;
}

FDecimal FDecimalMath::Sin(const FDecimal& InVal)
{
	FDecimal Result;
	Result.InternalValue = boost::multiprecision::sin(InVal.InternalValue);
	return Result;
}

FDecimal FDecimalMath::Cos(const FDecimal& InVal)
{
	FDecimal Result;
	Result.InternalValue = boost::multiprecision::cos(InVal.InternalValue);
	return Result;
}

FDecimal FDecimalMath::Acos(const FDecimal& InVal)
{
	FDecimal Result;
	Result.InternalValue = boost::multiprecision::asin(InVal.InternalValue);
	return Result;
}

FDecimal FDecimalMath::Atan(const FDecimal& InVal)
{
	FDecimal Result;
	Result.InternalValue = boost::multiprecision::atan(InVal.InternalValue);
	return Result;
}

FDecimal FDecimalMath::Atan2(const FDecimal& A, const FDecimal& B)
{
	FDecimal Result;
	Result.InternalValue = boost::multiprecision::atan2(A.InternalValue, B.InternalValue);
	return Result;
}

void FDecimalMath::SinCos(FDecimal& ScalarSin, FDecimal& ScalarCos, const FDecimal& Value)
{
	ScalarSin.InternalValue = boost::multiprecision::sin(Value.InternalValue);
	ScalarCos.InternalValue = boost::multiprecision::cos(Value.InternalValue);
}

void FDecimalMath::SinCos(FDecimal& ScalarSin, FDecimal& ScalarCos, float Value)
{
	ScalarSin.InternalValue = FMath::Sin(Value);
	ScalarSin.InternalValue = FMath::Cos(Value);
}

void FDecimalMath::SinCos(FDecimal& ScalarSin, FDecimal& ScalarCos, double Value)
{
	ScalarSin.InternalValue = FMath::Sin(Value);
	ScalarSin.InternalValue = FMath::Cos(Value);
}

FDecimal FDecimalMath::UnwindDegrees(FDecimal A)
{
	while (A > 180.f)
	{
		A -= 360.f;
	}

	while (A < -180.f)
	{
		A += 360.f;
	}

	return A;
}

FDecimal FDecimalMath::FMod(const FDecimal& A, const FDecimal& B)
{
	FDecimal Result;
	Result.InternalValue = boost::multiprecision::fmod(A.InternalValue, B.InternalValue);
	return Result;
}

FDecimal FDecimalMath::RemainderCustom(FDecimal A, FDecimal B)
{
	FDecimal Result;
	if (!FDecimalMath::IsNearlyZero(B, FDecimal(TEXT("0.01"))))
	{
		double AValue = FCString::Atod(*A.ToString(16));
		double BValue = FCString::Atod(*B.ToString(16));
		bool IsSigneNegetive = FMath::Sign(AValue) * FMath::Sign(BValue) < 0;
		double IntValue;
		double FractionalValue = FMath::Modf(AValue / BValue, &IntValue);
		double RemainderValue = AValue - IntValue * BValue;
		FDecimal RemainderDecimal(RemainderValue);
		if (IsSigneNegetive)
		{
			-RemainderDecimal;
		}
		//UE_LOG(LogTemp, Log, TEXT("[%s][%s][%d][%f][%f][%s]"), *A.ToString(16), *B.ToString(16), IsSigneNegetive, IntValue, FractionalValue, *RemainderDecimal.ToString(16));
		return RemainderDecimal;
	}
	return FDecimal();
}
