{"Version": 0, "AreaMat": {"Basic_Floor": "/Script/Engine.MaterialInstanceConstant'/Game/Materials/Material_Color/MT_Texture_Inst.MT_Texture_Inst'", "Basic_Floor_3D": "/Script/Engine.MaterialInstanceConstant'/Game/MapTemplates/3DSceneMaterials/MI_Floot.MI_Floot'", "Test_Floor": "/Script/Engine.MaterialInstanceConstant'/Game/Materials/Material_Color/MT_ColorNormal_Inst.MT_ColorNormal_Inst'"}, "WallMat": {"Local_Wall": "/Script/Engine.MaterialInstanceConstant'/Game/MapTemplates/3DSceneMaterials/MI_Wall.MI_Wall'", "Local_Wall_3D": "/Script/Engine.MaterialInstanceConstant'/Game/MapTemplates/3DSceneMaterials/MI_Wall3D.MI_Wall3D'", "Local_Wall_Edit": "/Script/Engine.MaterialInstanceConstant'/Game/Materials/Material_Color/MT_Color_Wall_Edit.MT_Color_Wall_Edit'"}, "StructMat": {"Local_BeamAndPlatform": "/Script/Engine.MaterialInstanceConstant'/Game/Materials/Material_Color/MT_ColorNormal_Inst.MT_ColorNormal_Inst'", "Local_BeamAndPlatform_3D": "/Script/Engine.MaterialInstanceConstant'/Game/MapTemplates/3DSceneMaterials/MI_Wall3D.MI_Wall3D'", "Local_BeamAndPlatform_Edit": "/Script/Engine.MaterialInstanceConstant'/Game/Materials/Material_Color/MT_Color_Wall_Edit.MT_Color_Wall_Edit'", "Local_Pillar": "/Script/Engine.MaterialInstanceConstant'/Game/Materials/Material_Color/MT_PillerBlack.MT_PillerBlack'", "Local_Pillar_3D": "/Script/Engine.MaterialInstanceConstant'/Game/MapTemplates/3DSceneMaterials/MI_Wall3D.MI_Wall3D'", "Local_Pillar_Edit": "/Script/Engine.MaterialInstanceConstant'/Game/Materials/Material_Color/MT_PillerBlack_Edit.MT_PillerBlack_Edit'", "Local_Pillar_Plane2D": "/Script/Engine.MaterialInstanceConstant'/Game/Materials/Material_Color/MT_PillerBlack_Plane2D.MT_PillerBlack_Plane2D'"}, "DoorAndWindow": {"DoorAndWindow_Main": "/Script/Engine.MaterialInstanceConstant'/Game/Materials/Material_Color/MT_DoorWindowOpen_Inst.MT_DoorWindowOpen_Inst'", "DoorAndWindow_Open": "/Script/Engine.MaterialInstanceConstant'/Game/Materials/Material_Color/MT_DoorWindowMain_Inst.MT_DoorWindowMain_Inst'", "DoorAndWindow_Bay": "/Script/Engine.MaterialInstanceConstant'/Game/Materials/Material_Color/MT_DoorWindowBay_Inst.MT_DoorWindowBay_Inst'", "DoorAndWindowPoint": "/Script/Engine.MaterialInstanceConstant'/Game/Materials/Material_Color/MT_DoorWindowBay_Inst.MT_DoorWindowBay_Inst'"}, "CounterTopMat": {"CounterTop_Normal": "/Script/Engine.MaterialInstanceConstant'/Game/Materials/Material_Color/MT_White_Inst.MT_White_Inst'", "CounterTop_Edit": "/Script/Engine.MaterialInstanceConstant'/Game/MapTemplates/Meshs/CountertopsMesh/Materials/MI_PlaneMesh.MI_PlaneMesh'", "CounterTop_WT_Front_Edit": "/Script/Engine.MaterialInstanceConstant'/Game/MapTemplates/Meshs/CountertopsMesh/Materials/MI_PlaneMeshQDS.MI_PlaneMeshQDS'", "CounterTop_WT_Back_Edit": "/Script/Engine.MaterialInstanceConstant'/Game/MapTemplates/Meshs/CountertopsMesh/Materials/MI_PlaneMeshHDS.MI_PlaneMeshHDS'", "CounterTop_Line_Point_Edit": "/Script/Engine.MaterialInstanceConstant'/Game/MapTemplates/Meshs/CountertopsMesh/Materials/MI_BoxMesh.MI_BoxMesh'"}, "ToolMat": {"Scaler": "/Script/Engine.MaterialInstanceConstant'/Game/Materials/Material_Color/MT_Color_Wall_Edit.MT_Color_Wall_Edit'", "Scaler2": "/Script/Engine.MaterialInstanceConstant'/Game/Materials/Material_Color/MT_RegionHover_Inst.MT_RegionHover_Inst'", "PathPoint": "/Script/Engine.MaterialInstanceConstant'/Game/Materials/Material_Color/MT_Color_PathPoint.MT_Color_PathPoint'", "Gizmo_Axis_X": "/Script/Engine.MaterialInstanceConstant'/Game/MapTemplates/Axis/Materials/MI_Axis3D_X.MI_Axis3D_X'", "Gizmo_Axis_Y": "/Script/Engine.MaterialInstanceConstant'/Game/MapTemplates/Axis/Materials/MI_Axis3D_Y.MI_Axis3D_Y'", "Gizmo_Axis_Z": "/Script/Engine.MaterialInstanceConstant'/Game/MapTemplates/Axis/Materials/MI_Axis3D_Z.MI_Axis3D_Z'", "Gizmo_Rot_Circle_X": "/Script/Engine.MaterialInstanceConstant'/Game/MapTemplates/Axis/Materials/MI_Rot_Circle_X.MI_Rot_Circle_X'", "Gizmo_Rot_Circle_Y": "/Script/Engine.MaterialInstanceConstant'/Game/MapTemplates/Axis/Materials/MI_Rot_Circle_Y.MI_Rot_Circle_Y'", "Gizmo_Rot_Circle_Z": "/Script/Engine.MaterialInstanceConstant'/Game/MapTemplates/Axis/Materials/MI_Rot_Circle_Z.MI_Rot_Circle_Z'", "Gizmo_Rot_Circle_X_P": "/Script/Engine.MaterialInstanceConstant'/Game/MapTemplates/Axis/Materials/MI_PressRotation3D_X.MI_PressRotation3D_X'", "Gizmo_Rot_Circle_Y_P": "/Script/Engine.MaterialInstanceConstant'/Game/MapTemplates/Axis/Materials/MI_PressRotation3D_Y.MI_PressRotation3D_Y'", "Gizmo_Rot_Circle_Z_P": "/Script/Engine.MaterialInstanceConstant'/Game/MapTemplates/Axis/Materials/MI_PressRotation3D_Z.MI_PressRotation3D_Z'", "Gizmo_Rot_Panel_X": "/Script/Engine.MaterialInstanceConstant'/Game/MapTemplates/Axis/Materials/MI_Rot_Circle_X_P.MI_Rot_Circle_X_P'", "Gizmo_Rot_Panel_Y": "/Script/Engine.MaterialInstanceConstant'/Game/MapTemplates/Axis/Materials/MI_Rot_Circle_Y_P.MI_Rot_Circle_Y_P'", "Gizmo_Rot_Panel_Z": "/Script/Engine.MaterialInstanceConstant'/Game/MapTemplates/Axis/Materials/MI_Rot_Circle_Z_P.MI_Rot_Circle_Z_P'", "Gizmo_Rot_Panel_X_P": "/Script/Engine.MaterialInstanceConstant'/Game/MapTemplates/Axis/Materials/MI_PressRotation3D_X.MI_PressRotation3D_X'", "Gizmo_Rot_Panel_Y_P": "/Script/Engine.MaterialInstanceConstant'/Game/MapTemplates/Axis/Materials/MI_PressRotation3D_Y.MI_PressRotation3D_Y'", "Gizmo_Rot_Panel_Z_P": "/Script/Engine.MaterialInstanceConstant'/Game/MapTemplates/Axis/Materials/MI_PressRotation3D_Z.MI_PressRotation3D_Z'", "Gizmo_Scale_Plane": "/Script/Engine.MaterialInstanceConstant'/Game/MapTemplates/Axis/Materials/MI_AxisScalePlane.MI_AxisScalePlane'", "Gizmo_AxisX_Baseline": "//Script/Engine.MaterialInstanceConstant'/Game/MapTemplates/Axis/Materials/MI_Point_X.MI_Point_X'", "Gizmo_AxisY_Baseline": "/Script/Engine.MaterialInstanceConstant'/Game/MapTemplates/Axis/Materials/MI_Point_Y.MI_Point_Y'", "Gizmo_AxisZ_Baseline": "/Script/Engine.MaterialInstanceConstant'/Game/MapTemplates/Axis/Materials/MI_Point_Z.MI_Point_Z'"}, "RoofMat": {"Local_Roof": "/Script/Engine.MaterialInstanceConstant'/Game/MapTemplates/3DSceneMaterials/MI_Wall.MI_Wall'", "Local_Roof_3D": "/Script/Engine.MaterialInstanceConstant'/Game/MapTemplates/3DSceneMaterials/MI_Wall.MI_Wall'", "Roof_Area": "/Script/Engine.MaterialInstanceConstant'/Game/Materials/Material_Color/MT_Color_RoofArea_2D.MT_Color_RoofArea_2D'", "Roof_Area_3D": "/Script/Engine.MaterialInstanceConstant'/Game/Materials/Material_Color/MT_Color_RoofArea.MT_Color_RoofArea'"}, "LazyLoadMat": {"LazyLoadMeshDefault": "/Script/Engine.MaterialInstanceConstant'/Game/Materials/Material_Color/MT_Opacity_LazyLoading.MT_Opacity_LazyLoading'"}, "GeneratedLineMat": {"GeneratedLineVisualPlane": "/Script/Engine.MaterialInstanceConstant'/Game/MapTemplates/Meshs/AngularLines/MI_AngularLine_PlaneMesh.MI_AngularLine_PlaneMesh'", "OrnamentLineVisual": "/Script/Engine.MaterialInstanceConstant'/Game/MapTemplates/Meshs/AngularLines/MI_AngularLineMesh.MI_AngularLineMesh'", "OrnamentLineVisual_InValid": "/Script/Engine.MaterialInstanceConstant'/Game/MapTemplates/Meshs/AngularLines/MI_AngularLine_BoxMeshRed.MI_AngularLine_BoxMeshRed'", "OrnamentLineVisual_Modified": "/Script/Engine.MaterialInstanceConstant'/Game/MapTemplates/Meshs/AngularLines/MI_AngularLine_BoxMeshHover.MI_AngularLine_BoxMeshHover'", "OrnamentPointVisual": "/Script/Engine.MaterialInstanceConstant'/Game/MapTemplates/Meshs/AngularLines/MI_AngularLine_BoxMesh.MI_AngularLine_BoxMesh'"}}