// Fill out your copyright notice in the Description page of Project Settings.


#include "FunctionalAdaptationOperator.h"
#include "SubSystems/AdaptiveAdsorption/Core/Executer/DynamicMeshAdaptiveAddorption.h"
#include "SubSystems/MVC/Model/Custom/Library/DSCupboardLibrary.h"
#include "SubSystems/MVC/Model/Custom/DSCupboardModel.h"	
#include "Parameter/ParameterProcLibrary.h"
#include "SubSystems/MVC/Core/Property/CupboardProperty.h"
#include "SubSystems/MVC/DSMVCSubsystem.h"
#include "SubSystems/ModelDependencySubsystem/DSModelDependencySubsystem.h"
#include "SubSystems/MVC/Library/DSToolLibrary.h"
#include "SubSystems/MVC/Library/CupBoardDoorLibrary.h"
#include "DrawerAdaptationOperator.h"
#include "SubSystems/CustomConfig/DSConfigSubsystem.h"
#include "SubSystems/UI/DSUISubsystem.h"
#include "SubSystems/AdaptiveAdsorption/Data/FunctionalDependencyInfo.h"
#include "Subsystems/AdaptiveAdsorption/Core/Executer/FunctionalCupboardAdaptationExecuter/CornerCutFunctionalAdaptationExecuter.h"
#include "Subsystems/AdaptiveAdsorption/Core/Executer/FunctionalCupboardAdaptationExecuter/CornerFunctionalAdaptationExecuter.h"
#include "SubSystems/Library/DesignStationFunctionLibrary.h"
#include "SubSystems/Undo/DSRevokeSubsystem.h"

#define PARAM_X  TEXT("W")
#define PARAM_Y TEXT("D")
#define PARAM_Z TEXT("H")


FFunctionalAdaptationOperator::FFunctionalAdaptationOperator(UDSBaseModel* InModel)
	:FModelAdaptationOperatorBase(InModel),TargetModel(nullptr)
{
}

bool FFunctionalAdaptationOperator::PrepareAdaptation(UDSBaseModel* InSourceModel)
{
	UDSCupboardModel* SourceCupboardModel = Cast<UDSCupboardModel>(InSourceModel);
	UDSCupboardModel* SourceRootModel = SourceCupboardModel->GetRootCupboardModel();
	PrepareAdaptation(InSourceModel, SourceRootModel);
	return false;
}

void FFunctionalAdaptationOperator::OnAdaptationStarting()
{
	UpdateInitializedData();

	if (!TargetModel)
	{
		return;
	}

	UDSCupboardModel* TargetCupboardModel = Cast<UDSCupboardModel>(TargetModel);
	
	auto DependencyInfo =  TargetCupboardModel->GetSubFunctionalNodeDependencyMap()->GetDependencyInfo(SourceAdaptationInfo->GetOwnerUUID());

	if (DependencyInfo.IsValid())
	{
		DependencyInfo->bEnablePassiveAdaptation = true;
	}
}

void FFunctionalAdaptationOperator::PrepareAdaptation(UDSBaseModel* InSourceMolde, UDSBaseModel* InTargetModel)
{
	SourceModel = InSourceMolde;
	TargetModel = InTargetModel;


    if (!InSourceMolde)
    {
        return;
    }
    bool bIsFunctionalModel = UDSCupboardLibrary::IsFunctionalCupboardModel(InSourceMolde);
    if (!bIsFunctionalModel)
    {
        return;
    }

	//初始化环境
	//GenerateAdaptationEnvData();
	GenerateAdapationEnvDataWithoutDenpendented();
	GenerateAdaptationSourceInfo();
}

void FFunctionalAdaptationOperator::GenerateInitializedData_Functional(const TSharedPtr<FAdaptationExecuterInitializedData>& InInitialziedData)
{

	TSharedPtr<FFunctionalExecuterInitializedData> FunctionalInitializedData = StaticCastSharedPtr<FFunctionalExecuterInitializedData>(InInitialziedData);
	//初始化待检测对象
	UDSCupboardModel* SourceCupboardModel = Cast<UDSCupboardModel>(SourceModel);

	UDSCupboardModel* TargetCupboardModel = Cast<UDSCupboardModel>(TargetModel);

	TMap<FString, FParameterData> OutParentOverriderParmeters;
	SourceCupboardModel->GetSelfComponentOverriderParametersRef(OutParentOverriderParmeters);


	//固定宽高深
	{
		if (OutParentOverriderParmeters.Contains("WIDTH") && OutParentOverriderParmeters.Contains("DEPTH") && OutParentOverriderParmeters.Contains("HEIGHT"))
		{
			FunctionalInitializedData->FixedExtent.X = FCString::Atof(*OutParentOverriderParmeters["WIDTH"].Data.value) * 0.1f;
			FunctionalInitializedData->FixedExtent.Y = FCString::Atof(*OutParentOverriderParmeters["DEPTH"].Data.value) * 0.1f;
			FunctionalInitializedData->FixedExtent.Z = FCString::Atof(*OutParentOverriderParmeters["HEIGHT"].Data.value) * 0.1f;

			FunctionalInitializedData->FixedExtent *= 0.5f;
			FunctionalInitializedData->bHasFixedExtents = true;
			if (OutParentOverriderParmeters.Contains("GDOH"))
			{
				FunctionalInitializedData->FixedOffset.Z = FCString::Atof(*OutParentOverriderParmeters["GDOH"].Data.value) * 0.1f;
			}
			if (OutParentOverriderParmeters.Contains("GDOD"))
			{
				FunctionalInitializedData->FixedOffset.Y = FCString::Atof(*OutParentOverriderParmeters["GDOD"].Data.value) * 0.1f;
			}

		}
	}


	TMap<FString, FParameterData> BaseParamsMap;
	FVector DefaultExtent, MaxExtents, MinExtents;
	if (UDSCupboardLibrary::CombineBaseDHWParameters(BaseParamsMap, OutParentOverriderParmeters))
	{
		UDSCupboardLibrary::ConvertBaseDHWParametersToFVector(DefaultExtent, BaseParamsMap);
		UDSCupboardLibrary::ConvertMaxDHWParametersToFVector(MaxExtents, BaseParamsMap);
		UDSCupboardLibrary::ConvertMinDHWParametersToFVector(MinExtents, BaseParamsMap);

		DefaultExtent *= 0.5f;
		InInitialziedData->bExtentsXEnableAdaptation = BaseParamsMap[PARAM_X].bEnableAdaptation;
		InInitialziedData->bExtentsYEnableAdaptation = BaseParamsMap[PARAM_Y].bEnableAdaptation;
		InInitialziedData->bExtentsZEnableAdaptation = BaseParamsMap[PARAM_Z].bEnableAdaptation;


		//极值不合法关闭自适应
		if (BaseParamsMap[PARAM_X].Data.is_enum || BaseParamsMap[PARAM_X].Data.min_value.IsEmpty()||BaseParamsMap[PARAM_X].Data.max_value.IsEmpty())
		{
			InInitialziedData->bExtentsXEnableAdaptation = false;
		}
		if (BaseParamsMap[PARAM_Y].Data.is_enum || BaseParamsMap[PARAM_Y].Data.min_value.IsEmpty() || BaseParamsMap[PARAM_Y].Data.max_value.IsEmpty())
		{
			InInitialziedData->bExtentsYEnableAdaptation= false;
		}
		if (BaseParamsMap[PARAM_Z].Data.is_enum || BaseParamsMap[PARAM_Z].Data.min_value.IsEmpty() || BaseParamsMap[PARAM_Z].Data.max_value.IsEmpty())
		{
			InInitialziedData->bExtentsZEnableAdaptation = false;
		}


		if (!InInitialziedData->bExtentsXEnableAdaptation)
		{
			InInitialziedData->DefaultExtents.X = DefaultExtent.X;
		}
		if (!InInitialziedData->bExtentsYEnableAdaptation)
		{
			InInitialziedData->DefaultExtents.Y = DefaultExtent.Y;
		}
		if (!InInitialziedData->bExtentsZEnableAdaptation)
		{
			InInitialziedData->DefaultExtents.Z = DefaultExtent.Z;
		}
	}
	UDSCupboardModel* SourceRootModel = SourceCupboardModel->GetRootCupboardModel();

	FTransform WorldTransform = SourceCupboardModel->GetWorldTransform();

	FVector Center = WorldTransform.GetLocation() + WorldTransform.TransformVector(DefaultExtent);

	InInitialziedData->Rotation = WorldTransform.GetRotation();

	if (SourceCupboardModel->IsNewGenerate())
	{
		//如果是新生成的模型，使用根模型的旋转

		InInitialziedData->Center = Center;
		InInitialziedData->Rotation = SourceRootModel->GetWorldTransform().GetRotation();
		InInitialziedData->Extents = DefaultExtent;

		FunctionalInitializedData->SelfRealOriBox = FOrientedBox3d(
			FFrame3d(Center, FQuaterniond::Identity()),
			DefaultExtent
		);
	}
	else
	{
		//已存在的模型使用自身OBB计算相对根节点的OBB
		const auto& RootTrans = SourceRootModel->GetWorldTransform();
		if (!RootTrans.GetRotation().Equals(WorldTransform.GetRotation(), 0.001))
		{

			FOrientedBox3d Oribox;
			Oribox.Frame.Origin = Center;
			Oribox.Frame.Rotation = FQuaterniond(WorldTransform.GetRotation());
			Oribox.Extents = DefaultExtent;

			////这里要添加固定宽高深偏移
			//if (FunctionalInitializedData->bHasFixedExtents)
			//{
			//	Oribox.Frame.Origin = Oribox.Frame.FromFramePoint(-Oribox.Extents+ FunctionalInitializedData->FixedOffset+ FunctionalInitializedData->FixedExtent);
			//	Oribox.Extents = FunctionalInitializedData->FixedExtent;
			//}

			FBox RealtiveAABB(EForceInit::ForceInit);
			Oribox.EnumerateCorners([&](const FVector3d& Corner)
				{
					RealtiveAABB += RootTrans.InverseTransformPosition(Corner);

				});

			InInitialziedData->Center = RootTrans.TransformPosition(RealtiveAABB.GetCenter());
			InInitialziedData->Extents = RealtiveAABB.GetSize() * 0.5f;
			InInitialziedData->Rotation = RootTrans.GetRotation();


			FunctionalInitializedData->SelfRealOriBox.Frame.Origin = InInitialziedData->Center - Oribox.Frame.Origin;
			FunctionalInitializedData->SelfRealOriBox.Extents = Oribox.Extents;
			FunctionalInitializedData->SelfRealOriBox.Frame.Rotation = FQuaterniond(RootTrans.GetRotation().Inverse() * WorldTransform.GetRotation());


			//如果旋转不一样 关闭自适应
			InInitialziedData->bExtentsXEnableAdaptation = false;
			InInitialziedData->bExtentsYEnableAdaptation = false;
			InInitialziedData->bExtentsZEnableAdaptation = false;
			FunctionalInitializedData->bUsedRelativedOBB = true;
		}
		else
		{
			InInitialziedData->Center = Center;
			InInitialziedData->Extents = DefaultExtent;
			InInitialziedData->Rotation = WorldTransform.GetRotation();
			FunctionalInitializedData->SelfRealOriBox = FOrientedBox3d(
				FFrame3d(InInitialziedData->Center, FQuat::Identity),
				DefaultExtent
			);
			FunctionalInitializedData->bUsedRelativedOBB = false;
		}
	}
	InInitialziedData->MaxExtents = MaxExtents * 0.5f;
	InInitialziedData->MinExtents = MinExtents * 0.5f;

	FunctionalInitializedData->AdaptativeRule = SourceCupboardModel->GetModelInfo().ComponentTreeData->AdaptationData;
	FAdaptiveAdsorptionRule3D AdaptiveRule = SourceCupboardModel->GetModelInfo().ComponentTreeData->AdaptationData;
	SourceAdaptationInfo->SetAdaptiveRule(AdaptiveRule);

	//设置吸附阈值
	float  Threshold = UDSConfigSubsystem::GetInstance()->GetValue_Custom_Float(DSSetting::Custom::ECustomType::E_DefaultSnapD);
	FunctionalInitializedData->DistanceAdsorptionThreshold = Threshold * 0.1f;
	FunctionalInitializedData->AlignedAdsorptionThreshold = FunctionalInitializedData->DistanceAdsorptionThreshold;
	
	FQuat TargetCupboardQuatation;
	TargetCupboardModel->GetModelOrientedBoundingBox(FunctionalInitializedData->CabinetOriBox.Frame.Origin, FunctionalInitializedData->CabinetOriBox.Extents, TargetCupboardQuatation);
	FunctionalInitializedData->CabinetOriBox.Frame.Rotation = FQuaterniond(TargetCupboardQuatation);
	if (TargetCupboardModel->GetModelType() == EDSModelType::E_Custom_CornerCutCabinet)
	{
		FunctionalInitializedData->CabinetType = EIntersectionDataType::E_CornerCutCabinet;
	}
	else if (TargetCupboardModel->GetModelType() == EDSModelType::E_Custom_CornerCabinet)
	{
		FunctionalInitializedData->CabinetType = EIntersectionDataType::E_CornerCabinet;
	}

}

void FFunctionalAdaptationOperator::GenerateInitializedData_CornerCut(const TSharedPtr<FAdaptationExecuterInitializedData>& InInitialziedData)
{
	TSharedPtr<FCornerCutFunctionalExecuterInitializedData> CornerCutInitializedData = StaticCastSharedPtr<FCornerCutFunctionalExecuterInitializedData>(InInitialziedData);

	FFunctionalAdaptationOperator::GenerateInitializedData_Functional(InInitialziedData);

	UDSCupboardModel* TargetCupboardModel = Cast<UDSCupboardModel>(TargetModel);

	TMap<FString, FParameterData> OutParentOverriderParmeters;
	TargetCupboardModel->GetSelfComponentOverriderParametersRef(OutParentOverriderParmeters);

	{
		if (OutParentOverriderParmeters.Contains("RT"))
		{
			CornerCutInitializedData->InternalVolumeMaxOffset.X = -FCString::Atof(*OutParentOverriderParmeters["RT"].Data.value) * 0.1f;
		}
		if (OutParentOverriderParmeters.Contains("LT"))
		{
			CornerCutInitializedData->InternalVolumeMinOffset.X = FCString::Atof(*OutParentOverriderParmeters["LT"].Data.value) * 0.1f;
		}
		if (OutParentOverriderParmeters.Contains("BO"))
		{
			CornerCutInitializedData->InternalVolumeMinOffset.Y = FCString::Atof(*OutParentOverriderParmeters["BO"].Data.value) * 0.1f;
		}
		else
		{
			CornerCutInitializedData->InternalVolumeMinOffset.Y = 0.f;
		}

		if (OutParentOverriderParmeters.Contains("BT1"))
		{
			CornerCutInitializedData->InternalVolumeMinOffset.Y += FCString::Atof(*OutParentOverriderParmeters["BT1"].Data.value) * 0.1f;
		}
	}
	if (TargetCupboardModel->GetModelType() == EDSModelType::E_Custom_CornerCutCabinet)
	{
		CornerCutInitializedData->CabinetType = EIntersectionDataType::E_CornerCutCabinet;
	}
	else if (TargetCupboardModel->GetModelType() == EDSModelType::E_Custom_CornerCabinet)
	{
		CornerCutInitializedData->CabinetType = EIntersectionDataType::E_CornerCabinet;
	}
}

void FFunctionalAdaptationOperator::GenerateInitializedData_Corner(const TSharedPtr<FAdaptationExecuterInitializedData>& InInitialziedData)
{
	TSharedPtr<FCornerCutFunctionalExecuterInitializedData> CornerCutInitializedData = StaticCastSharedPtr<FCornerCutFunctionalExecuterInitializedData>(InInitialziedData);

	FFunctionalAdaptationOperator::GenerateInitializedData_Functional(InInitialziedData);

	UDSCupboardModel* TargetCupboardModel = Cast<UDSCupboardModel>(TargetModel);

	TMap<FString, FParameterData> OutParentOverriderParmeters;
	TargetCupboardModel->GetSelfComponentOverriderParametersRef(OutParentOverriderParmeters);

	{
		if (OutParentOverriderParmeters.Contains("RT"))
		{
			CornerCutInitializedData->InternalVolumeMaxOffset.Y = -FCString::Atof(*OutParentOverriderParmeters["RT"].Data.value) * 0.1f;
		}
		if (OutParentOverriderParmeters.Contains("LT"))
		{
			CornerCutInitializedData->InternalVolumeMinOffset.X = FCString::Atof(*OutParentOverriderParmeters["LT"].Data.value) * 0.1f;
		}
		if (OutParentOverriderParmeters.Contains("BO"))
		{
			CornerCutInitializedData->InternalVolumeMinOffset.Y = FCString::Atof(*OutParentOverriderParmeters["BO"].Data.value) * 0.1f;

			CornerCutInitializedData->InternalVolumeMaxOffset.X = -FCString::Atof(*OutParentOverriderParmeters["BO"].Data.value) * 0.1f;
		}
		else
		{
			CornerCutInitializedData->InternalVolumeMinOffset.Y = 0.f;

			CornerCutInitializedData->InternalVolumeMaxOffset.X = 0.f;
		}

		if (OutParentOverriderParmeters.Contains("BT1"))
		{
			CornerCutInitializedData->InternalVolumeMinOffset.Y += FCString::Atof(*OutParentOverriderParmeters["BT1"].Data.value) * 0.1f;
			CornerCutInitializedData->InternalVolumeMaxOffset.X -= FCString::Atof(*OutParentOverriderParmeters["BT1"].Data.value) * 0.1f;
		}
	}
	if (TargetCupboardModel->GetModelType() == EDSModelType::E_Custom_CornerCutCabinet)
	{
		CornerCutInitializedData->CabinetType = EIntersectionDataType::E_CornerCutCabinet;
	}
	else if (TargetCupboardModel->GetModelType() == EDSModelType::E_Custom_CornerCabinet)
	{
		CornerCutInitializedData->CabinetType = EIntersectionDataType::E_CornerCabinet;
	}

}

void FFunctionalAdaptationOperator::GeneratorTargetModelIntersectionEnv(UDSBaseModel* InTargetMode, const FTransform& RootTrans, TArray<TSharedPtr<FIntersectionDynamicMesh>>& OutEnv, const TSharedPtr<FMultiComponentDataItem>& InComponentTreeData, const TSharedPtr<FMultiComponentDataItem>& IgnoreModelTreeData, const TSharedPtr<struct FFunctionalDependencyInfo>& InDependencyInfo)
{
	if (!InTargetMode)
	{
		return;
	}
	//是否当作整体判断
	//临时代码根据是否是抽屉类型判断,
	//后期需要从型录参数判断，即使不是分离出的Model也要支持整体包围盒判断
	{

		for (int32 i = 0; i < InComponentTreeData->ChildComponent.Num(); ++i)
		{
			FTransform RelativeRootTransform = RootTrans;
			UDSCupboardModel* TargetCupboardModel = Cast<UDSCupboardModel>(InTargetMode);

			const auto& ComponentArr = TargetCupboardModel->GetModelInfo().ComponentInfoArr;
			const auto& MultiDataIter = InComponentTreeData->ChildComponent[i];

			if (MultiDataIter == IgnoreModelTreeData || !MultiDataIter->IsVisiable())
			{
				continue;
			}


			//过滤依赖此部件的节点
			if (InDependencyInfo.IsValid())
			{
				if (InDependencyInfo->BeDependentBy(MultiDataIter->UUID))
				{
					continue;
				}
			}

			//忽略柜门
			if (UDSToolLibrary::IsCustomDoorType(UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(MultiDataIter->ModelType)))
			{
				continue;
			}

			bool bIsFunctionalCupboardModel = UDSCupboardLibrary::IsFunctionalCupboardModel(MultiDataIter->ComponentParameters);

			bool bOverallCollision = UDSCupboardLibrary::GetAdaptationIsOverallCollision(MultiDataIter->ComponentParameters, bIsFunctionalCupboardModel);

			//判断是否是可分离对象
			bool bSeparate = UDSCupboardLibrary::ComponentNeedSeparate(MultiDataIter->ComponentParameters);
			bSeparate &= true; //这里添加可移动参数值
			if (bSeparate) //需要单独构建的创建自身节点
			{
				if (MultiDataIter->UUID.IsEmpty())
				{
					continue;
				}
				UDSCupboardModel* ChildrenModel = nullptr;
				for (size_t i = 0; i < ComponentArr.Num(); i++)
				{
					const FDSComponentInfo& ComponentInfo = ComponentArr[i];
					if (ComponentInfo.ComponentUUID.Equals(MultiDataIter->UUID))
					{
						ChildrenModel = Cast<UDSCupboardModel>(ComponentInfo.ComponentModel);
						if (ChildrenModel)
						{
							TargetCupboardModel = ChildrenModel;
							RelativeRootTransform = ChildrenModel->GetRelativeTransform() * RootTrans;

							if (!bOverallCollision)
							{
								bOverallCollision = (UDSCupboardLibrary::IsDoorPanelDrawer(ChildrenModel)
									|| ChildrenModel->GetModelType() == EDSModelType::E_Custom_FunctionalDrawer
									|| ChildrenModel->GetModelType() == EDSModelType::E_Custom_CabinetBoard
									|| ChildrenModel->GetModelType() == EDSModelType::E_Custom_SideClosurePanel);
							}


						}
						break;
					}
				}
				if (!ChildrenModel)
				{
					continue;
				}
			}

			if (bOverallCollision || MultiDataIter->ComponentType == ECompType::SingleCom)
			{
				//遍历到单部件时TreeData为它上层的多部件
				const auto& UseComponentTreeData = MultiDataIter->ComponentType == ECompType::SingleCom ? InComponentTreeData : MultiDataIter;
				TMap<FString, FParameterData> ParamMap;
				if (MultiDataIter->ComponentType == ECompType::SingleCom)
				{
					for (auto& Iter : UseComponentTreeData->ComponentParameters)
					{
						ParamMap.Add(Iter.Data.name, Iter);
					}
					FParameterProcLibrary::CombineParameters(ParamMap, MultiDataIter->ComponentParameters, ParamMap);
				}
				else
				{
					for (auto& Iter : UseComponentTreeData->ComponentParameters)
					{
						ParamMap.Add(Iter.Data.name, Iter);
					}
				}


				FVector  DefaultExtent = FVector::Zero();


				bool bHasSpecialDWH = false;
				FVector SpecialExtent = FVector::Zero();
				FVector SpecialOffset = FVector::Zero();
				{
					if (ParamMap.Contains("TSW") && ParamMap.Contains("TSD") && ParamMap.Contains("TSH"))
					{
						bHasSpecialDWH = true;
						SpecialExtent.X = FCString::Atof(*ParamMap["TSW"].Data.value) * 0.1f;
						SpecialExtent.Y = FCString::Atof(*ParamMap["TSD"].Data.value) * 0.1f;
						SpecialExtent.Z = FCString::Atof(*ParamMap["TSH"].Data.value) * 0.1f;

					}

					if (/*ParamMap.Contains("TSW") && ParamMap.Contains("TSD") && */ParamMap.Contains("TSOH"))
					{
						SpecialOffset.Z = FCString::Atof(*ParamMap["TSOH"].Data.value) * 0.1f;
					}
					if (ParamMap.Contains("TSOD"))
					{
                        SpecialOffset.Y = FCString::Atof(*ParamMap["TSOD"].Data.value) * 0.1f;
					}
					if (ParamMap.Contains("TSOW"))
					{
                        SpecialOffset.X = FCString::Atof(*ParamMap["TSOW"].Data.value) * 0.1f;
					}

				}

				bool bHasFixedDWH = false;
				FVector FixedExtent = FVector::Zero();
				FVector FixedOffset = FVector::Zero();
				//固定宽高深
				{
					if (ParamMap.Contains("WIDTH") && ParamMap.Contains("DEPTH") && ParamMap.Contains("HEIGHT"))
					{
						bHasFixedDWH = true;
						FixedExtent.X = FCString::Atof(*ParamMap["WIDTH"].Data.value) * 0.1f;
						FixedExtent.Y = FCString::Atof(*ParamMap["DEPTH"].Data.value) * 0.1f;
						FixedExtent.Z = FCString::Atof(*ParamMap["HEIGHT"].Data.value) * 0.1f;

						FixedExtent *= 0.5f;
					}
					if (ParamMap.Contains("GDOH"))
					{
						FixedOffset.Z = FCString::Atof(*ParamMap["GDOH"].Data.value) * 0.1f;
					}
					if (ParamMap.Contains("GDOD"))
					{
						FixedOffset.Y = FCString::Atof(*ParamMap["GDOD"].Data.value) * 0.1f;
					}
					if (ParamMap.Contains("GDOW"))
					{
                        FixedOffset.X = FCString::Atof(*ParamMap["GDOW"].Data.value) * 0.1f;
					}
				}


				if (!bHasFixedDWH)
				{
					TMap<FString, FParameterData> BaseParamsMap;
					bool bHasDHW = UDSCupboardLibrary::CombineBaseDHWParameters(BaseParamsMap, ParamMap);
					if (bHasDHW)
					{
						UDSCupboardLibrary::ConvertBaseDHWParametersToFVector(DefaultExtent, BaseParamsMap);
					}
					//如果没有宽高深数据,跳过
					if (!bHasDHW)
					{
						UE_LOG(AdaptationExecuterLog, Warning, TEXT("Node DHW Data Lost,Please Check Data.Code: %s,UUID:%s "), *MultiDataIter->Code, *MultiDataIter->UUID);
						continue;
					}
					DefaultExtent = DefaultExtent * 0.5f;
				}



				//求板件世界位置
				FTransform ComponentRelativeTransform = RelativeRootTransform;
				TArray<TSharedPtr<FMultiComponentDataItem>> ComponentPath;
				TargetCupboardModel->CollectComponentPath_Public(TargetCupboardModel->GetModelInfo().ComponentTreeData, UseComponentTreeData, ComponentPath);

				if (ComponentPath.IsValidIndex(1))
				{
					for (int32 Index = 1; Index < ComponentPath.Num(); ++Index)
					{
						FTransform CurrentTransform;
						CurrentTransform.SetLocation(ComponentPath[Index]->ComponentLocation.GetLocation());
						CurrentTransform.SetRotation(ComponentPath[Index]->ComponentRotation.GetRotation().Quaternion());
						CurrentTransform.SetScale3D(ComponentPath[Index]->ComponentScale.GetScale());
						ComponentRelativeTransform = CurrentTransform * ComponentRelativeTransform;
					}
				}

				//获取柜体包围盒，求当前包围盒相对柜体包围盒的OBB


				//设置OBB
				FOrientedBox3d Oribox;

				Oribox.Frame.Rotation = FQuaterniond(ComponentRelativeTransform.GetRotation());

				if (bHasFixedDWH)
				{
					if (bOverallCollision && TargetCupboardModel->GetModelType() == EDSModelType::E_Custom_Functional_HangingRod)
					{
						TMap<FString, FParameterData> BaseParamsMap;
						bool bHasDHW = UDSCupboardLibrary::CombineBaseDHWParameters(BaseParamsMap, ParamMap);
						if (bHasDHW)
						{
							UDSCupboardLibrary::ConvertBaseDHWParametersToFVector(DefaultExtent, BaseParamsMap);
						}
						Oribox.Frame.Origin = ComponentRelativeTransform.TransformPosition(DefaultExtent * 0.5f);
						Oribox.Extents = DefaultExtent * 0.5f;
					}
					else
					{
						Oribox.Frame.Origin = ComponentRelativeTransform.TransformPosition(FixedExtent);
						Oribox.Frame.Origin = Oribox.Frame.FromFramePoint(FixedOffset);
						Oribox.Extents = FixedExtent;

					}
				}
				else
				{
					Oribox.Frame.Origin = ComponentRelativeTransform.TransformPosition(DefaultExtent);
					Oribox.Extents = DefaultExtent;
				}

				bool bUseRelativeOBB = !RootTrans.GetRotation().Equals(ComponentRelativeTransform.GetRotation(), 0.001);

				if (bUseRelativeOBB)
				{
					//如果旋转不一样 
					FBox RealtiveAABB(EForceInit::ForceInit);
					Oribox.EnumerateCorners([&](const FVector3d& Corner)
						{
							RealtiveAABB += RootTrans.InverseTransformPosition(Corner);

						});

					Oribox.Frame.Origin = RootTrans.TransformPosition(RealtiveAABB.GetCenter());
					Oribox.Extents = RealtiveAABB.GetSize() * 0.5f;
					Oribox.Frame.Rotation = FQuaterniond(RootTrans.GetRotation());
				}

				//判断是否是自适应功能件
				EIntersectionDataType  IntersectionDataType = EIntersectionDataType::E_Unknown;

				if (UDSCupboardLibrary::IsDoorPanelDrawer(TargetCupboardModel) || TargetCupboardModel->GetModelType() == EDSModelType::E_Custom_FunctionalDrawer)
				{
					IntersectionDataType = EIntersectionDataType::E_Drawer;
				}
				else if (UDSCupboardLibrary::IsFunctionalCupboardModel(TargetCupboardModel))
				{
					IntersectionDataType = EIntersectionDataType::E_Functional;
				}

				//如果是功能件，进行自适应规则处理
				if (bIsFunctionalCupboardModel)
				{
					FAdaptiveAdsorptionRule3D AdaptiveRule = TargetCupboardModel->GetModelInfo().ComponentTreeData->AdaptationData;

					FVector MinRelativePoint = -Oribox.Extents;
					FVector MaxRelativePoint = Oribox.Extents;

					if (!FMath::IsNearlyZero(AdaptiveRule.XAxisRule.NegativeOffset, ADAPTIVE_TOLERANCE))
					{
						MinRelativePoint.X -= AdaptiveRule.XAxisRule.NegativeOffset;
					}
					if (!FMath::IsNearlyZero(AdaptiveRule.XAxisRule.PositiveOffset, ADAPTIVE_TOLERANCE))
					{
						MaxRelativePoint.X += AdaptiveRule.XAxisRule.PositiveOffset;
					}

					if (!FMath::IsNearlyZero(AdaptiveRule.YAxisRule.NegativeOffset, ADAPTIVE_TOLERANCE))
					{
						MinRelativePoint.Y -= AdaptiveRule.YAxisRule.NegativeOffset;
					}
					if (!FMath::IsNearlyZero(AdaptiveRule.YAxisRule.PositiveOffset, ADAPTIVE_TOLERANCE))
					{
						MaxRelativePoint.Y += AdaptiveRule.YAxisRule.PositiveOffset;
					}


					if (!FMath::IsNearlyZero(AdaptiveRule.ZAxisRule.NegativeOffset, ADAPTIVE_TOLERANCE))
					{
						MinRelativePoint.Z -= AdaptiveRule.ZAxisRule.NegativeOffset;
					}
					if (!FMath::IsNearlyZero(AdaptiveRule.ZAxisRule.PositiveOffset, ADAPTIVE_TOLERANCE))
					{
						MaxRelativePoint.Z += AdaptiveRule.ZAxisRule.PositiveOffset;
					}

					FVector RelativeCenter = (MaxRelativePoint + MinRelativePoint) * 0.5f;
					Oribox.Frame.Origin = Oribox.Frame.FromFramePoint(RelativeCenter);
					Oribox.Extents = (MaxRelativePoint - MinRelativePoint) * 0.5f;
				}

				TSharedPtr<FFunctionalIntersectionMesh> ChildrenAdaptive =
					MakeShared<FFunctionalIntersectionMesh>(Oribox.Extents, Oribox.Frame.Origin, FQuat(Oribox.Frame.Rotation), IntersectionDataType);
				ChildrenAdaptive->SetLinkModelBaseInfo(TargetCupboardModel->GetComponentTreeDataRef()->UUID, UseComponentTreeData->Description, static_cast<int>(TargetCupboardModel->GetModelType()), MultiDataIter->UUID);
				ChildrenAdaptive->GetLinkModelBaseInfoRef().OwnerModelPtr = (TargetCupboardModel);
				ChildrenAdaptive->bUseRelativeOBB = bUseRelativeOBB;
				ChildrenAdaptive->bHasSpecialExtents = bHasSpecialDWH;
				ChildrenAdaptive->SpecialExtent = SpecialExtent*0.5f;
				ChildrenAdaptive->SepcialOffset = SpecialOffset;
				OutEnv.Add(ChildrenAdaptive);
			}
			else //没有单独构建的添加到数组管理
			{
				GeneratorTargetModelIntersectionEnv(TargetCupboardModel, RelativeRootTransform, OutEnv, MultiDataIter, IgnoreModelTreeData, InDependencyInfo);
			}
		}
	}
}



void FFunctionalAdaptationOperator::GenerateAdaptationEnvData()
{
	AdaptationEnvs.Reset();
	UDSCupboardModel* TopRootModel = Cast<UDSCupboardModel>(TargetModel);
	FTransform RootTrans = FTransform(TopRootModel->GetProperty()->GetTransformProperty().Rotation, TopRootModel->GetProperty()->GetTransformProperty().Location);

	UDSCupboardModel* SourceCupboardModel = Cast<UDSCupboardModel>(SourceModel);
	const TSharedPtr<FMultiComponentDataItem>& SourceComponentTreeData = SourceCupboardModel->GetComponentTreeDataRef();
	GeneratorTargetModelIntersectionEnv(TopRootModel, RootTrans, AdaptationEnvs, TopRootModel->GetComponentTreeDataRef(), SourceComponentTreeData, nullptr);
}

void FFunctionalAdaptationOperator::GenerateAdapationEnvDataWithoutDenpendented()
{
	AdaptationEnvs.Reset();
	UDSCupboardModel* TopRootModel = Cast<UDSCupboardModel>(TargetModel);
	FTransform RootTrans = FTransform(TopRootModel->GetProperty()->GetTransformProperty().Rotation, TopRootModel->GetProperty()->GetTransformProperty().Location);

	UDSCupboardModel* SourceCupboardModel = Cast<UDSCupboardModel>(SourceModel);
	const TSharedPtr<FMultiComponentDataItem>& SourceComponentTreeData = SourceCupboardModel->GetComponentTreeDataRef();

	const TSharedPtr<FFunctionalDependencyMap>& DependentedMap = TopRootModel->GetSubFunctionalNodeDependencyMap();

	TSharedPtr<struct FFunctionalDependencyInfo> DependencyInfo = DependentedMap->GetDependencyInfo(SourceComponentTreeData->UUID);
	
	if (DependentedMap)
	{
		DependencyInfo = DependentedMap->GetDependencyInfo(SourceComponentTreeData->UUID);
	}

	GeneratorTargetModelIntersectionEnv(TopRootModel, RootTrans, AdaptationEnvs, TopRootModel->GetComponentTreeDataRef(), SourceComponentTreeData, DependencyInfo);
}

void FFunctionalAdaptationOperator::GenerateAdaptationSourceInfo()
{
	if (!SourceModel || !TargetModel)
	{
		return;
	}

	//初始化待检测对象
	UDSCupboardModel* SourceCupboardModel = Cast<UDSCupboardModel>(SourceModel);

	UDSCupboardModel* TargetCupboardModel = Cast<UDSCupboardModel>(TargetModel);

	bool bCornerFunction = (SourceCupboardModel->GetComponentTreeDataRef()->IsParameterExists("ZJD") != INDEX_NONE);
	
	TSharedPtr<FFunctionalExecuterInitializedData> InitializedData = nullptr;
	if (bCornerFunction)
	{
		SourceAdaptationInfo = MakeShareable<FCornerFunctionalAdaptationExecuter>(new FCornerFunctionalAdaptationExecuter());
		InitializedData = MakeShared<FCornerCutFunctionalExecuterInitializedData>();
		GenerateInitializedData_Corner(InitializedData);
	}
	else
	{
		bool bCornerCutFunction = (SourceCupboardModel->GetComponentTreeDataRef()->IsParameterExists("QJD") != INDEX_NONE);


		if (bCornerCutFunction)
		{
			SourceAdaptationInfo = MakeShareable<FCornerCutFunctionalAdaptationExecuter>(new FCornerCutFunctionalAdaptationExecuter());
			InitializedData = MakeShared<FCornerCutFunctionalExecuterInitializedData>();
			GenerateInitializedData_CornerCut(InitializedData);
		}
		else
		{
			SourceAdaptationInfo = MakeShareable<FDynamicMeshAdaptiveAdsorption>(new FDynamicMeshAdaptiveAdsorption());
			InitializedData = MakeShared<FFunctionalExecuterInitializedData>();
			GenerateInitializedData_Functional(InitializedData);
		}
	}




	

	TAdaptationHandle Handle =
		[&](const TSharedPtr<FAdaptationData>& AdaptationData)
		{OnAdaptiveAndAdSorptionCallback(AdaptationData); };

	SourceAdaptationInfo->Initialized(InitializedData);
	SourceAdaptationInfo->SetExecuteHandle(Handle);

	const FString& SelfUUID = SourceCupboardModel->GetModelInfo().ComponentTreeData->UUID;
	SourceAdaptationInfo->GetIntersectionDataRef().SetLinkModelBaseInfo(SelfUUID, SourceCupboardModel->GetModelInfo().ComponentTreeData->ComponentName, static_cast<int>(SourceCupboardModel->GetModelType()), SelfUUID);
	SourceAdaptationInfo->GetIntersectionDataRef().GetLinkModelBaseInfoRef().OwnerModelPtr = SourceModel;
}

bool FFunctionalAdaptationOperator::HandleAdaptiveAndAdssorptionByHitPoint(const FVector& RayStartPoint, const FVector& RayDir)
{
	bool bHit = SourceAdaptationInfo->HandleAdaptiveAndAdsorptionWithRay(RayStartPoint, RayDir, AdaptationEnvs);
	return bHit;
}


void FFunctionalAdaptationOperator::CompleteAdaptation(FAdaptiveAdsorptionResault& OutResault, bool bRecalculateDependentedNode )
{

	UDSCupboardModel* SourCupboardModel = Cast<UDSCupboardModel>(SourceModel);

	UDSCupboardModel* RootModel = SourCupboardModel->GetRootCupboardModel();

	 TSharedPtr<FFunctionalDependencyMap> DependentRootInfo = RootModel->GetSubFunctionalNodeDependencyMap();

	 TSharedPtr<FFunctionalDependencyInfo> DependentInfo = nullptr;
	 if (DependentRootInfo.IsValid())
	 {
		 DependentInfo = DependentRootInfo->GetDependencyInfo(GetSourceAdapationInfo()->GetOwnerUUID());
	 }

	//重新计算依赖板件自适应
	//遍历依赖自己的节点

	HandelAdaptationResault(OutResault);
	TArray<FAdaptiveAdsorptionResault> Resaults;
	Resaults.Add(OutResault);
	if (bRecalculateDependentedNode)
	{
		ReCalculateDependenceTargetAdapationInfo(Resaults, DependentInfo, true, 2);
	}

	TArray<FString> Descriptions;
		 
	for (auto  Resault:Resaults)
	{
		if (!Resault.Owner || !Resault.Owner->IsA<UDSCupboardModel>())
		{
			continue;
		}
		TArray<AActor*> OverLapActors;
		Cast<UDSCupboardModel>(Resault.Owner)->GetOwnedView()->GetOverlappingActors(OverLapActors, ADSBaseView::StaticClass());
		FString IntersectionTargetNameStr;
		if (!OverLapActors.IsEmpty())
		{
			for (auto& Iter : OverLapActors)
			{
				ADSBaseView* OverlapView = Cast<ADSBaseView>(Iter);
				UDSBaseModel* OverlapModel = OverlapView->GetModel();
				if (!OverlapModel || !OverlapModel->IsA<UDSCupboardModel>())
				{
					continue;
				}
				FString OverLapName = Cast<UDSCupboardModel>(OverlapModel)->GetModelInfo().ComponentTreeData->Description;
				IntersectionTargetNameStr += FString::Printf(TEXT("%s、"), *OverLapName);
			}

			IntersectionTargetNameStr.RemoveAt(IntersectionTargetNameStr.Len() - 1);
			Resault.ResaultType = EAdaptiveResaultType::E_Intersection;
			Resault.Description = FString::Printf(TEXT("<Normal_Char>工艺错误！%s与%s有交叉</>"), *GetSourceAdapationInfo()->GetIntersectionData().GetDebugName(), *IntersectionTargetNameStr);
		}

		//UI显示
		switch (Resault.ResaultType)
		{
		case EAdaptiveResaultType::E_MoreThanMax:
		case EAdaptiveResaultType::E_LessThanMin:
		{
			Descriptions.Add(Resault.Description);
		}
			break;
		case EAdaptiveResaultType::E_UnkownFailed:
			break;
		case EAdaptiveResaultType::E_Intersection:
			{
			Descriptions.Add(Resault.Description);
			}
			break;
		default:
			break;
		}
	}
	if (!Descriptions.IsEmpty())
	{
		UDSUISubsystem::GetInstance()->SetToastState(EToastState::EWarning, Descriptions, TEXT(""),true,true);
	}
}

void FFunctionalAdaptationOperator::HandelAdaptationResault(FAdaptiveAdsorptionResault& OutResault)
{
	if (!SourceModel || !SourceModel->IsA<UDSCupboardModel>())
	{
		return;
	}
	OutResault.Owner = SourceModel;
	SourceAdaptationInfo->AdaptiveAndAdsorptionComplete(AdaptationEnvs,OutResault);
	if (OutResault.ResaultType == EAdaptiveResaultType::E_WithoutAdsorption)
	{
		UDSBaseModel* SourceOwnerModel = SourceModel->GetOwnerModel();
		if (SourceOwnerModel != nullptr && SourceOwnerModel->IsA<UDSCupboardModel>())
		{
			UDSCupboardModel* OwnerCupboardModel = Cast<UDSCupboardModel>(SourceOwnerModel);
			OwnerCupboardModel->RemoveFunctionalCupboardModel(SourceModel);
			SourceModel->OnExecuteAction(FDSModelExecuteType::ExecuteDelete);
		}
	}

	UDSCupboardModel* SourCupboardModel = Cast<UDSCupboardModel>(SourceModel);

	UDSCupboardModel* RootModel = SourCupboardModel->GetRootCupboardModel();

	TSharedPtr<FFunctionalDependencyMap> RootDependencyMap = RootModel->GetSubFunctionalNodeDependencyMap();



	if (OutResault.ResaultType != EAdaptiveResaultType::E_WithoutAdsorption)
	{

		auto NodeDependencyInfo = RootDependencyMap->GetDependencyInfo(OutResault.SourceUUID);
		if (!NodeDependencyInfo.IsValid())
		{
			NodeDependencyInfo = RootDependencyMap->AddDependencyInfo(OutResault.SourceUUID, true);
			NodeDependencyInfo->Name = SourceAdaptationInfo->GetIntersectionData().GetLinkModelBaseInfo().Name;
		}
		else
		{
			NodeDependencyInfo->ClearDependentNode();
			//先清空依赖，再添加依赖
		}

		for (auto& Iter : OutResault.Dependents)
		{

			TSharedPtr<FFunctionalDependencyInfo> DependencyInfo = RootDependencyMap->AddDependencyInfo(Iter.LinkModelInfo.UUID);
			DependencyInfo->Name = Iter.LinkModelInfo.Name;
			FFunctionalDependencySubNodeInfo SubNodeInfo(Iter.LinkModelInfo.UUID, Iter.LinkModelInfo.ComponentUUID, (Iter.Direction));
			NodeDependencyInfo->AddDependentNode(SubNodeInfo,DependencyInfo);
			
			DependencyInfo->AddBeDenendentNode(NodeDependencyInfo.ToSharedRef(), Iter.TargetDirection);

			//添加依赖关系
		}
		RefreshNodeParent(OutResault);
	}

	//柜门的依赖处理
	if (UDSModelDependencySubsystem::IsInitialized() && UDSToolLibrary::IsCustomBoardType(SourCupboardModel->GetModelType()))
	{
		TArray<FString> RelativeDoors = UDSModelDependencySubsystem::GetInstance()->FindDoorByDependentBoard(SourCupboardModel->GetModelInfoRef().ComponentTreeData->UUID);
		for (const FString& RelativeDoor : RelativeDoors)
		{
			UDSModelDependencySubsystem::GetInstance()->MarkDependentBoardChanged(RelativeDoor);
		}
	}
}

void FFunctionalAdaptationOperator::ReCalculateDependenceTargetAdapationInfo(TArray<FAdaptiveAdsorptionResault>& OutDependenceTargetResaults, const TSharedPtr<FFunctionalDependencyInfo>& InDependentInfo, bool bIncludeNext, int32 Level )
{
	if (Level-- <= 0)
	{
		return;
	}

	if (!SourceModel || !SourceModel->IsA<UDSCupboardModel>() || !InDependentInfo.IsValid())	
	{
		return;
	}

	UDSCupboardModel* SourCupboardModel = Cast<UDSCupboardModel>(SourceModel);
	UDSCupboardModel* RootModel = SourCupboardModel->GetRootCupboardModel();
	const TSharedPtr<FFunctionalDependencyMap>& DependentRootInfo = RootModel->GetSubFunctionalNodeDependencyMap();

	TArray<TWeakPtr<FFunctionalDependencyInfo>> DependentNodes = InDependentInfo->GetBeDependentNodesWithDepth();

	for (auto& Iter : DependentNodes)
	{
		if (!Iter.IsValid())
		{
			continue;
		}
		
		TSharedPtr<FFunctionalDependencyInfo> SubDependencyInfo = Iter.Pin();

		if (SubDependencyInfo->GetUUID().IsEmpty())
		{
			continue;
		}
		const FFunctionalDependencySubNodeInfo& SubNodeInfo =  SubDependencyInfo->GetDependencySubNode(InDependentInfo->GetUUID());

		if (!SubNodeInfo.IsValid())
		{
			continue;
		}

		UDSBaseModel* Model = UDSCupboardLibrary::GetModelByUUID(SubDependencyInfo->GetUUID(), RootModel->GetModelInfo());
		if (!Model || !Model->IsA<UDSCupboardModel>())
		{
			continue;
		}
		if (Model == SourceModel)
		{
			continue;
		}
		TSharedPtr<FFunctionalAdaptationOperator> Operator;
		if (Model->GetModelType() == EDSModelType::E_Custom_FunctionalDrawer)
		{
			Operator = MakeShared<FDrawerAdaptationOperator>(Model);
		}
		else
		{
			Operator = MakeShared<FFunctionalAdaptationOperator>(Model);
		}
		Operator->BroadcastMarkPtr = FDSBroadcastMarkData::OnlyOutlineBroadcastMark;
		Operator->PrepareAdaptation(Model, TargetModel);
		TSharedPtr<FFunctionalExecuterInitializedData> FunctionalInitializedData = StaticCastSharedPtr<FFunctionalExecuterInitializedData>(Operator->GetSourceAdapationInfo()->GetInitializedDataRef());
		FunctionalInitializedData->bEnableAlignedAdsorption = false;

		Operator->SourceAdaptationInfo->HandleAdaptiveAndAdsorptionWithDependent(Operator->AdaptationEnvs, SubDependencyInfo);

		//被依赖的节点删除依赖
		InDependentInfo->RemoveBeDependentNode(Iter.Pin().ToSharedRef());

		if (!DependentRootInfo.IsValid()) return;

		//重新计算依赖板件自适应
		//遍历依赖自己的节点
		const TSharedPtr<FFunctionalDependencyInfo>& DependentInfo = DependentRootInfo->GetDependencyInfo(SubDependencyInfo->GetUUID());
		FAdaptiveAdsorptionResault Resault;
		Operator->HandelAdaptationResault(Resault);
		OutDependenceTargetResaults.Add(Resault);
		//if (bIncludeNext)
		//{
		//	ReCalculateDependenceTargetAdapationInfo(OutDependenceTargetResaults, DependentInfo, true, Level);
		//}
	}
}

void FFunctionalAdaptationOperator::ExecuteStepAdaptation(const TSharedPtr<FDSBroadcastMarkData>& InBroadcastMarkPtr)
{
	
	ExecuteStepAdaptation(true, InBroadcastMarkPtr);
}

void FFunctionalAdaptationOperator::ExecuteStepAdaptation(bool bReCalculateBeDependency, const TSharedPtr<FDSBroadcastMarkData>& InBroadcastMarkPtr)
{

	BroadcastMarkPtr = InBroadcastMarkPtr;
	PrepareAdaptation(SourceModel);
	TSharedPtr<FFunctionalExecuterInitializedData> FunctionalInitializedData = StaticCastSharedPtr<FFunctionalExecuterInitializedData>(SourceAdaptationInfo->GetInitializedDataRef());
	FunctionalInitializedData->bEnableAlignedAdsorption = false;

	const auto  DependentRootInfo = Cast<UDSCupboardModel>(TargetModel)->GetSubFunctionalNodeDependencyMap();

	const auto& NodeDependenyInfo = DependentRootInfo->GetDependencyInfo(SourceAdaptationInfo->GetIntersectionData().GetOwnerUUID());
	//重新计算依赖板件自适应
	SourceAdaptationInfo->HandleAdaptiveAndAdsorptionWithDependent(AdaptationEnvs, NodeDependenyInfo);

	FAdaptiveAdsorptionResault Resault;
	CompleteAdaptation(Resault, bReCalculateBeDependency);

	if (BroadcastMarkPtr == FDSBroadcastMarkData::BroadcastToMVCMark)
	{
		if (Resault.ResaultType == EAdaptiveResaultType::E_WithoutAdsorption)
		{
			UDSMVCSubsystem::GetInstance()->SetCurrentModel(nullptr);
		}
		else
		{
			SourceModel->OnExecuteAction(FDSModelExecuteType::ExecuteAll, BroadcastMarkPtr);
		}
	}
}

void FFunctionalAdaptationOperator::ExecuteEvenStepAdaptation(bool bHorizontalEven, const TSharedPtr<FDSBroadcastMarkData>& InBroadcastMarkPtr)
{

	PrepareAdaptation(SourceModel);
	TSharedPtr<FFunctionalExecuterInitializedData> FunctionalInitializedData = StaticCastSharedPtr<FFunctionalExecuterInitializedData>(SourceAdaptationInfo->GetInitializedDataRef());
	FunctionalInitializedData->bEnableAlignedAdsorption = false;
	auto& AdaptiveRule = SourceAdaptationInfo->GetAdaptiveRuleRef();
	if (bHorizontalEven)
	{
		AdaptiveRule.XAxisRule.AdsorptionRule = EAdsorptionRule::E_Free;
		AdaptiveRule.XAxisRule.bAdaptived = false;
	}
	else
	{
		AdaptiveRule.ZAxisRule.AdsorptionRule = EAdsorptionRule::E_Free;
		AdaptiveRule.ZAxisRule.bAdaptived = false;
	}
	SourceAdaptationInfo->OnInitializeDataUpdate();
	const auto  DependentRootInfo = Cast<UDSCupboardModel>(TargetModel)->GetSubFunctionalNodeDependencyMap();
	const auto& NodeDependenyInfo = DependentRootInfo->GetDependencyInfo(SourceAdaptationInfo->GetIntersectionData().GetOwnerUUID());
	if (NodeDependenyInfo.IsValid())
	{
		NodeDependenyInfo->bEnablePassiveAdaptation = true;
	}
	//重新计算依赖板件自适应
	SourceAdaptationInfo->HandleAdaptiveAndAdsorptionWithDependent(AdaptationEnvs, NodeDependenyInfo);
	FAdaptiveAdsorptionResault Resault;
	CompleteAdaptation(Resault);
}

void FFunctionalAdaptationOperator::ExecuteEven(int Count)
{
	
	if (Count < 2)
	{
		return;
	}

	UDSBaseModel* RootModel = Cast<UDSCupboardModel>(SourceModel)->GetTopLevelOwnerModel();
	FDSCustomPushData CustomPushData(FDSModelExecuteType::ExecuteUpdateSelf, RootModel, nullptr);
	FDSRevokePushData PushData(EDSPushDataType::E_Custom, FDSModelExecuteType::ExecuteUpdateSelf, false);
	PushData.SetData(CustomPushData);
	UDSRevokeSubsystem::GetInstance()->InsertCommandDataUnion(
		RootModel,
		FDSModelExecuteType::ExecuteUpdateSelf,
		PushData,
		UDSMVCSubsystem::GetInstance()->GetState()->GetStateRevokePoolMark(),
		UDSRevokeSubsystem::GetInstance()->GetGlobalCommandUUID()
	);


	FAdaptationEvenInfo EvenInfo =  SourceAdaptationInfo->ExecuteEven(AdaptationEnvs);
	const auto& Rule = SourceAdaptationInfo->GetAdaptativeRule();

	FVector RelativeCenter = EvenInfo.SpaceBox.Extents;
	double IntergeDistance = 0;
	TArray<FVector> NewModelCenter;


	FVector SourceRelativeLocation = EvenInfo.SpaceBox.Frame.ToFramePoint(EvenInfo.SourceBox.Frame.FromFramePoint(-EvenInfo.SourceBox.Extents));

	bool bHorizontalEven = false;
	if (!Rule.XAxisRule.bAdaptived)
	{
		IntergeDistance = ((EvenInfo.SpaceBox.Extents.X - ((Count - 1) * EvenInfo.SourceExtents.X)) * 20.f);
		for (size_t i = 0; i < Count -1; i++)
		{
			double Delta = (i + 1) *IntergeDistance / Count / 10.f+(i+1) * EvenInfo.SourceExtents.X * 2.f;;
			FVector RelativeCenter = (FVector(EvenInfo.SpaceBox.Extents.X - Delta, SourceRelativeLocation.Y, SourceRelativeLocation.Z));
			NewModelCenter.Add(RelativeCenter);
		}
		bHorizontalEven = true;
	}
	else if (!Rule.ZAxisRule.bAdaptived)
	{
		IntergeDistance =((EvenInfo.SpaceBox.Extents.Z-((Count - 1) * EvenInfo.SourceExtents.Z))*20.f);
		for (size_t i = 0; i < Count-1; i++)
		{
			double Delta = (i + 1)*IntergeDistance / Count / 10.f+ (i+1)*EvenInfo.SourceExtents.Z*2.f;
			FVector RelativeCenter = (FVector(SourceRelativeLocation.X, SourceRelativeLocation.Y, EvenInfo.SpaceBox.Extents.Z - Delta));
			NewModelCenter.Add(RelativeCenter);
		}
	}

	auto SourceModelPropterty = StaticCastSharedPtr<FCupboardProperty>(SourceModel->GetPropertySharedPtr());

	TArray<UDSBaseModel*> NewModels;
	for (size_t i = 0; i < NewModelCenter.Num(); i++)
	{
		const FVector& RelativeCenter = NewModelCenter[i];
		FVector LocalToWorldPoint = EvenInfo.SpaceBox.Frame.FromFramePoint(RelativeCenter);
		if (i == 0)
		{
			SourceModel->GetPropertySharedPtr()->GetTransformPropertyRef().SetLocation(LocalToWorldPoint);
			SourceModel->OnExecuteAction(FDSModelExecuteType::ExecuteTransform);
		}
		else
		{
			UDSCupboardModel* SourceCupboardModel = Cast<UDSCupboardModel>(SourceModel);
			UDSCupboardModel* TargetCupboardModel = Cast<UDSCupboardModel>(SourceModel->GetOwnerModel());
			UDSCupboardModel* NewModel = Cast<UDSCupboardModel>(SourceCupboardModel->OnCopy());
			NewModel->SetOwnerModel(TargetCupboardModel);
			NewModel->SetNoNewGenerate();
			FCupboardProperty ModelProperty;
			ModelProperty.GetTransformPropertyRef().Location = LocalToWorldPoint;
			ModelProperty.GetTransformPropertyRef().Rotation = SourceModelPropterty->GetTransformProperty().Rotation;
			ModelProperty.GetTransformPropertyRef().Scale = FVector::OneVector;
			ModelProperty.BusinessInfo = SourceModelPropterty->BusinessInfo;

			ModelProperty.SizeProperty.Width = FCString::Atod(*ModelProperty.BusinessInfo.Width);
			ModelProperty.SizeProperty.Depth = FCString::Atod(*ModelProperty.BusinessInfo.Depth);
			ModelProperty.SizeProperty.Height = FCString::Atod(*ModelProperty.BusinessInfo.Height);
			NewModel->SetProperty(&ModelProperty);

			NewModel->OnExecuteAction(FDSModelExecuteType::ExecuteTransform, FDSBroadcastMarkData::OnlyOutlineBroadcastMark);
			NewModels.Add(NewModel);
		}
	}
	ExecuteEvenStepAdaptation(bHorizontalEven);

	for (auto Iter: NewModels)
	{
		TSharedPtr<FFunctionalAdaptationOperator> Operator;
		if (Iter->GetModelType() == EDSModelType::E_Custom_FunctionalDrawer)
		{
			Operator = MakeShared<FDrawerAdaptationOperator>(Iter);
		}
		else
		{
			Operator = MakeShared<FFunctionalAdaptationOperator>(Iter);
		}

		Operator->ExecuteEvenStepAdaptation(bHorizontalEven, FDSBroadcastMarkData::OnlyOutlineBroadcastMark);
	}
	SourceModel->OnExecuteAction(FDSModelExecuteType::ExecuteUnSelect);

	if (Cast<UDSCupboardModel>(RootModel))
	{
		Cast<UDSCupboardModel>(RootModel)->SetParsed(true);
		UDSCupBoardDoorLibrary::UpdateDoorOnCupboard(RootModel);
	}
}

FAdaptationEvenInfo FFunctionalAdaptationOperator::GetAdaptationEvenInfo()
{
	return SourceAdaptationInfo->ExecuteEven(AdaptationEnvs);
}

FAdaptiveAdsorptionRule3D FFunctionalAdaptationOperator::GetAdaptationRule()
{
	return SourceAdaptationInfo->GetAdaptativeRule();
}

bool FFunctionalAdaptationOperator::GetIntersectionHitPoint(FVector& Start, FVector& End, const FVector& Dir) const
{
	const auto& SourceOriBox = SourceAdaptationInfo->GetAdaptationOriBoxWithoutAdaptiveRulerOffset(); 
	
 	Start = SourceOriBox.Frame.FromFramePoint(Dir* SourceOriBox.Extents);
	FVector RayDir = SourceOriBox.Frame.FromFrameVector(Dir);
	FRayHitResault HitResualt;
	SourceAdaptationInfo->FindHitNearestPoint(Start - RayDir * 0.1f, RayDir,AdaptationEnvs ,HitResualt);
	if (HitResualt.HitTarget.IsValid())
	{
		if (HitResualt.HitTarget->GetOriBox().Contains(Start))
		{
			End = Start;
		}
		else
		{
			End = HitResualt.HitPoint;
		}
		return true;
	}
	return false;
}


const TSharedPtr<FDynamicMeshAdaptiveAdsorption>& FFunctionalAdaptationOperator::GetSourceAdapationInfo()
{
	return SourceAdaptationInfo;
	// TODO: 在此处插入 return 语句
}

void FFunctionalAdaptationOperator::ShowDebug(UObject* WorldContextObject)
{

	
	if (SourceAdaptationInfo.IsValid())
	{
		SourceAdaptationInfo->DrawOriBox(WorldContextObject, SourceAdaptationInfo->GetAdaptationOriBox(), FLinearColor::Yellow);
		SourceAdaptationInfo->DrawOriBox(WorldContextObject, SourceAdaptationInfo->GetIntersectionData().GetOriBox(), FLinearColor::Red);
		SourceAdaptationInfo->DrawOriBox(WorldContextObject, SourceAdaptationInfo->GetOutAdaptationOriBox(), FLinearColor::Blue);
		
		for (auto& Iter:AdaptationEnvs)
		{
			SourceAdaptationInfo->DrawOriBox(WorldContextObject, Iter->GetOriBox());
		}
	}
}


void FFunctionalAdaptationOperator::OnAdaptiveAndAdSorptionCallback(const TSharedPtr<FAdaptationData>& AdaptationData)
{
	auto FunctionalInitializedData = StaticCastSharedPtr<FFunctionalExecuterInitializedData>(SourceAdaptationInfo->GetInitializedDataRef());

	FVector FinalExtents = AdaptationData->OriBox.Extents * 2.f;

	if (FunctionalInitializedData->bUsedRelativedOBB)
	{
		//获取到AdaptationData的Extent是 带旋转的包围盒.先获取真实包围盒

	}
	else
	{
		//返回的是固定高、固定宽、固定深。根据吸附规则只将当前自适应方向的值传递出来。剩余值只用宽高深数据

		if (!FunctionalInitializedData->AdaptativeRule.XAxisRule.bAdaptived)
		{
			FinalExtents.X = FunctionalInitializedData->SelfRealOriBox.Extents.X * 2.f;
		}
		if (!FunctionalInitializedData->AdaptativeRule.YAxisRule.bAdaptived)
		{
			FinalExtents.Y = FunctionalInitializedData->SelfRealOriBox.Extents.Y * 2.f;
		}
		if (!FunctionalInitializedData->AdaptativeRule.ZAxisRule.bAdaptived)
		{
			FinalExtents.Z = FunctionalInitializedData->SelfRealOriBox.Extents.Z * 2.f;
		}

	}

	if (!SourceModel || !SourceModel->IsA<UDSCupboardModel>())
	{
		return;
	}

	UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(SourceModel);

	if (AdaptationData->bExtentsModified)
	{
		CupboardModel->UpdateDWHParamters(FinalExtents,BroadcastMarkPtr);
	}

	if (AdaptationData->bExtentsModified || AdaptationData->bTransformModified)
	{
		FVector LocalToWorldPoint = AdaptationData->OriBox.Frame.FromFrameVector(AdaptationData->OriBox.Extents);


		if (FunctionalInitializedData->bUsedRelativedOBB)
		{
			FVector LocalRealPoint = FunctionalInitializedData->SelfRealOriBox.Frame.FromFramePoint(-FunctionalInitializedData->SelfRealOriBox.Extents);

			LocalToWorldPoint = AdaptationData->OriBox.Frame.FromFramePoint(LocalRealPoint);
		}
		else
		{
			LocalToWorldPoint = AdaptationData->OriBox.Frame.FromFramePoint(-AdaptationData->OriBox.Extents);
		}

		CupboardModel->UpdateLocationAndRotation(LocalToWorldPoint, FQuat(AdaptationData->OriBox.Frame.Rotation * FunctionalInitializedData->SelfRealOriBox.Frame.Rotation), BroadcastMarkPtr);
	}
}


void FFunctionalAdaptationOperator::UpdateInitializedData()
{
	if (!SourceModel)
	{
		return;
	}
	UDSCupboardModel* SourceCupboardModel = Cast<UDSCupboardModel>(SourceModel);
	if (!SourceCupboardModel)
	{
		return;
	}
	auto& InitializedData = SourceAdaptationInfo->GetInitializedDataRef();
	bool bCornerFunction = (SourceCupboardModel->GetComponentTreeDataRef()->IsParameterExists("ZJD") != INDEX_NONE);
	if (bCornerFunction)
	{
		GenerateInitializedData_Corner(InitializedData);
	}
	else
	{
		bool bCornerCutFunction = (SourceCupboardModel->GetComponentTreeDataRef()->IsParameterExists("QJD") != INDEX_NONE);


		if (bCornerCutFunction)
		{
			GenerateInitializedData_CornerCut(InitializedData);
		}
		else
		{
			GenerateInitializedData_Functional(InitializedData);
		}
	}
	SourceAdaptationInfo->OnInitializeDataUpdate();
}

void FFunctionalAdaptationOperator::RefreshNodeParent(const FAdaptiveAdsorptionResault& InAdaptationResault)
{

	//根据依赖对象判断父节点
	const auto& AdaptationRule = SourceAdaptationInfo->GetAdaptativeRule();
	UDSBaseModel* NewOwnerModel = SourceModel->GetOwnerModel();
	if (AdaptationRule.XAxisRule.bAdaptived)
	{
		FString PositiveOwnerUUID;
		FString NegativeOwnerUUID;
		for (auto& DependentIter : InAdaptationResault.Dependents)
		{
			
			if (DependentIter.Direction == EAdaptationDirection::E_Forward)
			{
				PositiveOwnerUUID = DependentIter.LinkModelInfo.UUID;
			}
			else if (DependentIter.Direction == EAdaptationDirection::E_Backward)
			{
				NegativeOwnerUUID = DependentIter.LinkModelInfo.UUID;
			}
		}
		NewOwnerModel = GetEqualModelByAdaptationUUID(NegativeOwnerUUID, PositiveOwnerUUID);
	}

	if (AdaptationRule.ZAxisRule.bAdaptived)
	{
		FString PositiveOwnerUUID;
		FString NegativeOwnerUUID;
		for (auto& DependentIter : InAdaptationResault.Dependents)
		{
			if (DependentIter.Direction == EAdaptationDirection::E_Up)
			{
				PositiveOwnerUUID = DependentIter.LinkModelInfo.UUID;
			}
			else if (DependentIter.Direction == EAdaptationDirection::E_Down)
			{
				NegativeOwnerUUID = DependentIter.LinkModelInfo.UUID;
			}
		}
		UDSBaseModel* ZAxisNewOwnerModel =GetEqualModelByAdaptationUUID(NegativeOwnerUUID, PositiveOwnerUUID);
		if (ZAxisNewOwnerModel)
		{
			if (ZAxisNewOwnerModel != NewOwnerModel && NewOwnerModel != SourceModel->GetOwnerModel())
			{
				NewOwnerModel = SourceModel->GetOwnerModel();
			}
			else
			{
				NewOwnerModel = ZAxisNewOwnerModel;
			}
		}
	}
	if (NewOwnerModel&& NewOwnerModel != SourceModel->GetOwnerModel())
	{
		UDSBaseModel* LastOwnerModel = SourceModel->GetOwnerModel();
		UDSCupboardModel* LastOwnerCupboardModel = Cast<UDSCupboardModel>(LastOwnerModel);
		if (LastOwnerCupboardModel)
		{
			UDSCupboardModel* NewOwnerCupboardModel = Cast<UDSCupboardModel>(NewOwnerModel);
			LastOwnerCupboardModel->RemoveFunctionalCupboardModel(SourceModel);
			SourceModel->OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf);
			NewOwnerCupboardModel->AddFunctionalCupboardModel(SourceModel);
		}
	}
}

UDSBaseModel* FFunctionalAdaptationOperator::GetEqualModelByAdaptationUUID(const FString& NegativeOwnerUUID,const FString& PositiveOwnerUUID)
{
	UDSBaseModel* SourceOwnerModel = SourceModel->GetOwnerModel();

	UDSCupboardModel* SourceOwnerCupboardModel = Cast<UDSCupboardModel>(SourceOwnerModel);

	UDSCupboardModel* RootCupboardModel = SourceOwnerCupboardModel->GetRootCupboardModel();

	if (NegativeOwnerUUID.IsEmpty()||PositiveOwnerUUID.IsEmpty())
	{
		return SourceOwnerModel;
	}
	//左右ID不相等
	if (!PositiveOwnerUUID.Equals(NegativeOwnerUUID))
	{
		TArray<UDSBaseModel*> NegativeOwnerModelPath;
		TArray<UDSBaseModel*> PositiveOwnerModelPath;
		if (NegativeOwnerUUID.Equals(SourceOwnerCupboardModel->GetComponentTreeDataRef()->UUID))
		{
			NegativeOwnerModelPath.Add(SourceOwnerModel);
		}
		else
		{
			UDSBaseModel* NegativeModel = UDSCupboardLibrary::GetModelByUUID(NegativeOwnerUUID, RootCupboardModel->GetModelInfo());
			while (NegativeModel)
			{
				NegativeOwnerModelPath.Push(NegativeModel);
				NegativeModel = NegativeModel->GetOwnerModel();
			}
		}
		if (PositiveOwnerUUID.Equals(SourceOwnerCupboardModel->GetComponentTreeDataRef()->UUID))
		{
			PositiveOwnerModelPath.Push(SourceOwnerModel);
		}
		else
		{
			UDSBaseModel* PositiveModel = UDSCupboardLibrary::GetModelByUUID(PositiveOwnerUUID, RootCupboardModel->GetModelInfo());

			while (PositiveModel)
			{
				PositiveOwnerModelPath.Push(PositiveModel);
				PositiveModel = PositiveModel->GetOwnerModel();
			}
		}
		UDSBaseModel* LastEqualModel = SourceModel->GetOwnerModel();
		while (NegativeOwnerModelPath.Num() > 0 && PositiveOwnerModelPath.Num() > 0)
		{
			if (NegativeOwnerModelPath.Top() == PositiveOwnerModelPath.Top())
			{
				LastEqualModel = NegativeOwnerModelPath.Top();
				NegativeOwnerModelPath.Pop();
				PositiveOwnerModelPath.Pop();
				continue;
			}
			break;
		}

		return LastEqualModel;

	}
	else
	{
		UDSBaseModel* LastOwnerModel = SourceModel->GetOwnerModel();
		if (LastOwnerModel)
		{
			UDSCupboardModel* LastOwnerCupboardModel = Cast<UDSCupboardModel>(LastOwnerModel);
			if (LastOwnerCupboardModel)
			{
				if (!NegativeOwnerUUID.Equals(LastOwnerCupboardModel->GetComponentTreeDataRef()->UUID))
				{
					UDSBaseModel* NewOwnerModel = UDSCupboardLibrary::GetModelByUUID(NegativeOwnerUUID, RootCupboardModel->GetModelInfo());

					return NewOwnerModel;
				}
			}
		}

	}

	return nullptr;
}

void FFunctionalAdaptationOperator::UpdateSourceIntersectionTransform()
{
	UpdateInitializedData();
	FAdaptiveAdsorptionResault Resault;
	CompleteAdaptation(Resault);
}
