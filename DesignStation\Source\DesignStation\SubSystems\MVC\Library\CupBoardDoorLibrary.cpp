#include "CupBoardDoorLibrary.h"

#include "CounterTopLibrary.h"
#include "DSToolLibrary.h"
#include "BasicClasses/DesignStationController.h"
#include "Library/Clipper2Library.h"
#include "SubSystems/Library/DesignStationFunctionLibrary.h"
#include "SubSystems/Library/DSXmlLibrary.h"
#include "SubSystems/ModelDependencySubsystem/DSModelDependencySubsystem.h"
#include "SubSystems/MVC/DSMVCSubsystem.h"
#include "SubSystems/MVC/Core/Macro.h"
#include "SubSystems/MVC/Model/DSBaseModel.h"
#include "SubSystems/MVC/Model/Custom/DSCupboardModel.h"
#include "SubSystems/MVC/View/Custom/DSCupboardBaseView.h"
#include "SubSystems/UI/DSUISubsystem.h"
#include "SubSystems/UI/Widget/CustomCupboard/Property/CustomDoor/CustomGenerateDoorWidget.h"
#include "SubSystems/Undo/DSRevokeSubsystem.h"
#include "SubSystems/Undo/Library/DSRevokeLibrary.h"

FVector UDSCupBoardDoorLibrary::GetObbCenter(const TArray<FVector>& Obb)
{
	FVector SumOfVectors = FVector::ZeroVector;
	for (auto& Point : Obb)
	{
		SumOfVectors += Point;
	}
	return SumOfVectors / Obb.Num();
}

TArray<FCheckBoardPlane> UDSCupBoardDoorLibrary::CreateSixPlaneFromObb(const TArray<FVector>& Obb)
{
	TArray<TArray<int32>> PlanesSeq = {
		{3, 2, 1, 0}, {4, 5, 6, 7}, // Top - Down
		{2, 6, 5, 1}, {4, 7, 3, 0}, // Left - Right
		{5, 4, 0, 1}, {3, 7, 6, 2} //  Front - Back
	};

	TArray<FCheckBoardPlane> Planes = {};
	for (auto& PanelIndexS : PlanesSeq)
	{
		FCheckBoardPlane Plane;
		TArray<FVector> Points;
		for (const auto& Index : PanelIndexS)
		{
			Points.Add(Obb[Index]);
		}
		Plane.InitProperty(Points);
		Planes.Add(Plane);
	}

	return Planes;
}

FVector UDSCupBoardDoorLibrary::GetObbExtent(const TArray<FVector>& Obb)
{
	double XExtent = FVector::Distance((Obb[2] + Obb[6] + Obb[5] + Obb[1]) / 4, GetObbCenter(Obb));
	double YExtent = FVector::Distance((Obb[3] + Obb[7] + Obb[6] + Obb[2]) / 4, GetObbCenter(Obb));
	double ZExtent = FVector::Distance((Obb[3] + Obb[2] + Obb[1] + Obb[0]) / 4, GetObbCenter(Obb));

	return FVector(XExtent, YExtent, ZExtent);
}

bool UDSCupBoardDoorLibrary::IsPointOnLineSegment(const FVector& PointToTest, const FVector& LineStart, const FVector& LineEnd, const float Tolerance = KINDA_SMALL_NUMBER)
{
	const FVector VectorAP = PointToTest - LineStart;
	const FVector VectorAB = LineEnd - LineStart;

	const float CrossProductSquaredLength = FVector::CrossProduct(VectorAP, VectorAB).SizeSquared();

	if (!FMath::IsNearlyZero(CrossProductSquaredLength, Tolerance * Tolerance))
	{
		return false;
	}

	const float DotProduct_AP_AB = FVector::DotProduct(VectorAP, VectorAB);

	if (DotProduct_AP_AB < -Tolerance)
	{
		return false;
	}

	const float SegmentLengthSquared = VectorAB.SizeSquared();

	if (DotProduct_AP_AB > SegmentLengthSquared + Tolerance)
	{
		return false;
	}

	return true;
}

bool UDSCupBoardDoorLibrary::CalculatePlanarLineIntersection(const FVector& Line1Start, const FVector& Line1End, const FVector& Line2Start, const FVector& Line2End, FVector& OutIntersectionPoint, const float Tolerance = KINDA_SMALL_NUMBER)
{
	FVector DirA = Line1End - Line1Start;
	FVector DirB = Line2End - Line2Start;

	FVector Offset = Line2Start - Line1Start;

	FVector Cross_DirA_DirB = FVector::CrossProduct(DirA, DirB);
	float Denominator = Cross_DirA_DirB.SizeSquared();

	if (FMath::IsNearlyZero(Denominator, Tolerance * Tolerance))
	{
		OutIntersectionPoint = FVector::ZeroVector;
		return false;
	}

	FVector Cross_Offset_DirB = FVector::CrossProduct(Offset, DirB);
	float Numerator_t = FVector::DotProduct(Cross_Offset_DirB, Cross_DirA_DirB);

	float t = Numerator_t / Denominator;

	OutIntersectionPoint = Line1Start + t * DirA;

	return true;
}

bool UDSCupBoardDoorLibrary::CalculateDoorArea(const TArray<UDSBaseModel*>& InBoards, FDSSuitablePlane& OutPlane, bool& bIsOnlyEmbedded, TArray<TArray<UDSBaseModel*>>& DependenciesGroup, TArray<UDSBaseModel*>& DependentCabinets)
{
	if (!CheckIfAllSelectedModelsAreBoards(InBoards))
	{
		return false;
	}

	TArray<TArray<FVector>> PanelBoxS; //选中板件FBox数组
	TArray<FVector> PanelBoxCenters; //选中板件FBox的中心数组

	FVector SumOfVectors = FVector::ZeroVector; //Box中心点求和
	for (UDSBaseModel* Model : InBoards)
	{
		UDSCupboardModel* BoardModel = Cast<UDSCupboardModel>(Model);
		PanelBoxS.Add(BoardModel->GetModelFixedOrientedBoundingBox());
		PanelBoxCenters.Add(GetObbCenter(PanelBoxS.Last()));
		SumOfVectors += PanelBoxCenters.Last();
	}

	UDSCupboardModel* FirstModel = Cast<UDSCupboardModel>(InBoards[0]);
	if (!FirstModel)
	{
		return false;
	}

	FTransform FirstModelTrans = FirstModel->GetRootCupboardModel()->GetPropertySharedPtr()->GetActualTransform();

	TArray<FVector> TransPoints;
	for (auto& PB : PanelBoxCenters)
	{
		FVector Point = FirstModelTrans.InverseTransformPosition(PB);
		Point.Y = 0.0f;
		TransPoints.Add(Point);
	}

	FVector P0;
	CalculatePlanarLineIntersection(TransPoints[0], TransPoints[0] + FVector(1, 0, 0) * 10.0f, TransPoints[3], TransPoints[3] + FVector(0, 0, 1) * 10.0f, P0);
	FVector P1;
	CalculatePlanarLineIntersection(TransPoints[0], TransPoints[0] + FVector(1, 0, 0) * 10.0f, TransPoints[2], TransPoints[2] + FVector(0, 0, 1) * 10.0f, P1);
	FVector P2;
	CalculatePlanarLineIntersection(TransPoints[1], TransPoints[1] + FVector(1, 0, 0) * 10.0f, TransPoints[2], TransPoints[2] + FVector(0, 0, 1) * 10.0f, P2);
	FVector P3;
	CalculatePlanarLineIntersection(TransPoints[1], TransPoints[1] + FVector(1, 0, 0) * 10.0f, TransPoints[3], TransPoints[3] + FVector(0, 0, 1) * 10.0f, P3);

	FVector ProjectPoint = (P0 + P1 + P2 + P3) / 4.0f;
	FVector CubeCenter = FirstModelTrans.TransformPosition(ProjectPoint);

	TArray<FCheckBoardPlane> HitPlanes;
	for (const auto& Box : PanelBoxS)
	{
		for (auto& Plane : CreateSixPlaneFromObb(Box))
		{
			//找到线面交点
			FVector InterSectionPoint = FMath::LinePlaneIntersection(GetObbCenter(Box), CubeCenter, Plane.BasicPlane);
			//判断焦点在线段内，而不是线段延长线和面的交点
			if (IsPointOnLineSegment(InterSectionPoint, GetObbCenter(Box), GetObbCenter(Box) + ((CubeCenter - GetObbCenter(Box)).GetSafeNormal()) * 10.0f))
			{
				HitPlanes.Add(Plane);
				break;
			}
		}
	}

	if (HitPlanes.Num() != 4)
	{
		return false;
	}

	//DoorNormal是生成门区域的法线
	const FVector DoorNormal = FVector::CrossProduct(HitPlanes[0].Normal, HitPlanes.Last().Normal);

	//HitPlanes是4个围成面片，还需要从中找出围成的线段，用向量方向来判断
	TArray<TArray<FVector>> AreaLines;
	for (auto& HP : HitPlanes)
	{
		const int32 Num = HP.Points.Num();

		for (int i = 0; i < Num; ++i)
		{
			FVector LineCenter = (HP.Points[i] + HP.Points[(i + 1) % Num]) / 2;
			FVector CompareNormal = LineCenter - (HP.Points[0] + HP.Points[2]) / 2;

			//面中心到线中点的向量方向如果一致，那就是这条线
			const float DotProduct = FVector::DotProduct(CompareNormal.GetSafeNormal(), DoorNormal.GetSafeNormal());
			if (FMath::IsNearlyEqual(DotProduct, 1.0f, KINDA_SMALL_NUMBER))
			{
				AreaLines.Add({HP.Points[i], HP.Points[(i + 1) % Num]});
				break;
			}
		}
	}
	if (AreaLines.Num() != 4)
	{
		return false;
	}

	//线，有可能是离散的，下一步要在法线面内延伸相交
	FPlane CenterPlane(CubeCenter, DoorNormal); //法线垂直平面，所有线都往这里面投影
	TArray<TArray<FVector>> ProjectLines; //投影线数组;
	TArray<float> MoveDistance; //原始点投影到平面的距离，还要用于反投影,还要用于差值计算
	for (auto& Line : AreaLines)
	{
		float Distance1 = CenterPlane.PlaneDot(Line[0]);
		float Distance2 = CenterPlane.PlaneDot(Line[1]);

		MoveDistance.Add(Distance1);
		MoveDistance.Add(Distance2);

		TArray<FVector> NewLine;
		NewLine.Add(Line[0] - Distance1 * CenterPlane.GetNormal());
		NewLine.Add(Line[1] - Distance2 * CenterPlane.GetNormal());
		ProjectLines.Add(NewLine);
	}

	OutPlane.Points = GetFinalProjectLines(ProjectLines, DoorNormal, MoveDistance, bIsOnlyEmbedded);
	OutPlane.Normal = DoorNormal;
	OutPlane.Center = (OutPlane.Points[0] + OutPlane.Points[2]) * 0.5f;

	//判断点是不是顺时针排序
	if (!ArePointsCounterClockwise(OutPlane.Points, DoorNormal))
	{
		return false;
	}

	DependenciesGroup = GetRelatedBoardOfDoor(InBoards, OutPlane, DependentCabinets);

	return true;
}

TArray<TArray<UDSBaseModel*>> UDSCupBoardDoorLibrary::GetRelatedBoardOfDoor(const TArray<UDSBaseModel*>& InBoards, const FDSSuitablePlane& OutPlane, TArray<UDSBaseModel*>& DependentCabinets)
{
	//找出上下左右，所有板的数组，每个数组开头第一个是被选中的板
	for (auto& CustomModel : UDSMVCSubsystem::GetInstance()->GetAllCustomModels())
	{
		if (UDSToolLibrary::IsCustomCabinetType(CustomModel->GetModelType()))
		{
			if (UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(CustomModel))
			{
				if (IsRectOBBIntersectOrAdjacent(OutPlane.Points, CupboardModel->GetModelFixedOrientedBoundingBox()))
				{
					DependentCabinets.Add(CupboardModel);
				}
			}
		}
	}

	//找出所有子部件
	TArray<UDSBaseModel*> Result;
	for (auto& Every : DependentCabinets)
	{
		UDSCounterTopLibrary::GetSingleComponentModels(Every, Result);
	}

	//找出所有板件，功能件中的板件是不是IsCustomBoardType类型
	TArray<UDSBaseModel*> ResultBoard;
	for (auto& Every : Result)
	{
		if (Every->GetModelType() == EDSModelType::E_Custom_Board
			|| Every->GetModelType() == EDSModelType::E_Custom_AdjustablePanel
			|| Every->GetModelType() == EDSModelType::E_Custom_TopClosurePanel
			|| Every->GetModelType() == EDSModelType::E_Custom_SideClosurePanel
			|| Every->GetModelType() == EDSModelType::E_Custom_CabinetBoard)
		{
			ResultBoard.Add(Every);
		}
	}

	//删掉被选中的板件
	ResultBoard.RemoveAll([InBoards](UDSBaseModel* Element) { return InBoards.Contains(Element); });

	FVector TopLineNormal = FVector::CrossProduct(OutPlane.Normal, (OutPlane.Points[1] - OutPlane.Points[0]).GetSafeNormal());
	FVector TopLineCenterPoint = (OutPlane.Points[1] + OutPlane.Points[0]) / 2;
	TArray<UDSBaseModel*> TopGroup = GetEveryGroupBoards(InBoards[0], ResultBoard, TopLineNormal, TopLineCenterPoint);

	FVector BottomLineNormal = FVector::CrossProduct(OutPlane.Normal, (OutPlane.Points[3] - OutPlane.Points[2]).GetSafeNormal());
	FVector BottomLineCenterPoint = (OutPlane.Points[3] + OutPlane.Points[2]) / 2;
	TArray<UDSBaseModel*> BottomGroup = GetEveryGroupBoards(InBoards[1], ResultBoard, BottomLineNormal, BottomLineCenterPoint);

	FVector LeftLineNormal = FVector::CrossProduct(OutPlane.Normal, (OutPlane.Points[2] - OutPlane.Points[1]).GetSafeNormal());
	FVector LeftLineCenterPoint = (OutPlane.Points[2] + OutPlane.Points[1]) / 2;
	TArray<UDSBaseModel*> LeftGroup = GetEveryGroupBoards(InBoards[2], ResultBoard, LeftLineNormal, LeftLineCenterPoint);

	FVector RightLineNormal = FVector::CrossProduct(OutPlane.Normal, (OutPlane.Points[0] - OutPlane.Points[3]).GetSafeNormal());
	FVector RightLineCenterPoint = (OutPlane.Points[0] + OutPlane.Points[3]) / 2;
	TArray<UDSBaseModel*> RightGroup = GetEveryGroupBoards(InBoards[3], ResultBoard, RightLineNormal, RightLineCenterPoint);

	TArray<TArray<UDSBaseModel*>> ResultGroup;

	ResultGroup.Add(TopGroup);
	ResultGroup.Add(BottomGroup);
	ResultGroup.Add(LeftGroup);
	ResultGroup.Add(RightGroup);

	return ResultGroup;
}

TArray<UDSBaseModel*> UDSCupBoardDoorLibrary::GetEveryGroupBoards(UDSBaseModel* FirstBoard, const TArray<UDSBaseModel*>& ResultBoard, const FVector& Normal, const FVector& Center)
{
	TArray<UDSBaseModel*> Result;
	Result.Add(FirstBoard);
	for (auto& Board : ResultBoard)
	{
		if (UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(Board))
		{
			FVector BoardCenter = GetObbCenter(CupboardModel->GetModelFixedOrientedBoundingBox());
			FVector CheckNormal = (BoardCenter - Center).GetSafeNormal();
			if (FVector::DotProduct(CheckNormal, Normal) > 0.0f)
			{
				if (FMath::Abs(FVector::DotProduct(BoardCenter, Normal) - FVector::DotProduct(Center, Normal)) < 0.5)
				{
					Result.Add(Board);
				}
			}
		}
	}

	return Result;
}

void UDSCupBoardDoorLibrary::ConvertInnerPointToContainerPoint(const FVector& WidthStart, const FVector& HeightStart, const FVector& OperatePoint, double BoardWidth, double BoardHeight, const FTransform& RootTransform, FVector& OutPoint)
{
	//根据全盖转换容器点
	FVector WidthDir = OperatePoint - WidthStart;
	FVector HeightDir = OperatePoint - HeightStart;

	WidthDir.Normalize();
	HeightDir.Normalize();

	OutPoint = OperatePoint + WidthDir * BoardWidth;
	OutPoint = OutPoint + HeightDir * BoardHeight;
	OutPoint = RootTransform.InverseTransformPosition(OutPoint);
}

void UDSCupBoardDoorLibrary::ConvertInnerPointToContainerPointByCover(const FVector& WidthStart, const FVector& HeightStart, const FVector& OperatePoint, double BoardWidth, double BoardHeight, const FTransform& RootTransform,
                                                                      const EMaskingMode& MaskingModeHor, const EMaskingMode& MaskingModeVer, FVector& OutPoint)
{
	//根据实际盖值转换容器点
	FVector WidthDir = OperatePoint - WidthStart;
	FVector HeightDir = OperatePoint - HeightStart;

	WidthDir.Normalize();
	HeightDir.Normalize();

	double Width = BoardWidth;
	double Height = BoardHeight;

	if (MaskingModeHor == EMaskingMode::ECoverHalf)
	{
		Width = BoardWidth * 0.5;
	}
	else if (MaskingModeHor == EMaskingMode::ENoCover || MaskingModeHor == EMaskingMode::EEmbedded)
	{
		Width = 0.0f;
	}

	if (MaskingModeVer == EMaskingMode::ECoverHalf)
	{
		Height = BoardHeight * 0.5;
	}
	else if (MaskingModeVer == EMaskingMode::ENoCover || MaskingModeVer == EMaskingMode::EEmbedded)
	{
		Height = 0.0f;
	}

	OutPoint = OperatePoint + WidthDir * Width;
	OutPoint = OutPoint + HeightDir * Height;
	OutPoint = RootTransform.InverseTransformPosition(OutPoint);
}

TArray<FVector> UDSCupBoardDoorLibrary::GetFinalProjectLines(const TArray<TArray<FVector>>& ProjectLines, const FVector& Normal, TArray<float> MoveDistance, bool& bIsOnlyEmbedded)
{
	float FinalMoveDistance = 0.0f;
	MoveDistance.Sort([](const double& A, const double& B) { return A < B; });
	float MaxDiff = (MoveDistance.Last() - MoveDistance[0]) * 10;

	if (FMath::RoundToInt(MaxDiff) >= 16)
	{
		FinalMoveDistance = MoveDistance[0];
		bIsOnlyEmbedded = true;
	}
	else
	{
		FinalMoveDistance = MoveDistance.Last();
	}

	//投影线在平面上，依照上下左右的顺序求交点，得到的交点，再根据MoveDistance的极差范围，再确认统一反投影的距离
	FVector Top1 = FVector::ZeroVector;
	FVector Top2 = FVector::ZeroVector;
	CalculatePlanarLineIntersection(ProjectLines[0][0], ProjectLines[0][1], ProjectLines[2][0], ProjectLines[2][1], Top1);
	CalculatePlanarLineIntersection(ProjectLines[0][0], ProjectLines[0][1], ProjectLines[3][0], ProjectLines[3][1], Top2);
	FVector FinalTop = Top1 + Normal * FinalMoveDistance;

	FVector Bottom1 = FVector::ZeroVector;
	FVector Bottom2 = FVector::ZeroVector;
	CalculatePlanarLineIntersection(ProjectLines[1][0], ProjectLines[1][1], ProjectLines[3][0], ProjectLines[3][1], Bottom1);
	CalculatePlanarLineIntersection(ProjectLines[1][0], ProjectLines[1][1], ProjectLines[2][0], ProjectLines[2][1], Bottom2);
	FVector FinalBottom = Bottom1 + Normal * FinalMoveDistance;

	FVector Left1 = FVector::ZeroVector;
	FVector Left2 = FVector::ZeroVector;
	CalculatePlanarLineIntersection(ProjectLines[2][0], ProjectLines[2][1], ProjectLines[1][0], ProjectLines[1][1], Left1);
	CalculatePlanarLineIntersection(ProjectLines[2][0], ProjectLines[2][1], ProjectLines[0][0], ProjectLines[0][1], Left2);
	FVector FinalLeft = Left1 + Normal * FinalMoveDistance;

	FVector Right1 = FVector::ZeroVector;
	FVector Right2 = FVector::ZeroVector;
	CalculatePlanarLineIntersection(ProjectLines[3][0], ProjectLines[3][1], ProjectLines[0][0], ProjectLines[0][1], Right1);
	CalculatePlanarLineIntersection(ProjectLines[3][0], ProjectLines[3][1], ProjectLines[1][0], ProjectLines[1][1], Right2);
	FVector FinalRight = Right1 + Normal * FinalMoveDistance;

	TArray<FVector> NewLine;
	NewLine.Add(FinalRight);
	NewLine.Add(FinalTop);
	NewLine.Add(FinalLeft);
	NewLine.Add(FinalBottom);

	return NewLine;
}

bool UDSCupBoardDoorLibrary::CheckIfAllSelectedModelsAreBoards(const TArray<UDSBaseModel*>& InModels)
{
	if (InModels.Num() != 4)
	{
		return false;
	}

	for (UDSBaseModel* Model : InModels)
	{
		UDSCupboardModel* CastedModel = Cast<UDSCupboardModel>(Model);
		if (CastedModel == nullptr || (CastedModel->GetModelType() != EDSModelType::E_Custom_Board && CastedModel->GetModelType() != EDSModelType::E_Custom_AdjustablePanel
			&& CastedModel->GetModelType() != EDSModelType::E_Custom_TopClosurePanel && CastedModel->GetModelType() != EDSModelType::E_Custom_SideClosurePanel
			&& CastedModel->GetModelType() != EDSModelType::E_Custom_CabinetBoard))
		{
			return false;
		}
	}

	return true;
}

TArray<UDSBaseModel*> UDSCupBoardDoorLibrary::GetAllOverlapCabinets(const TArray<FVector>& Plane)
{
	FCollisionQueryParams QueryParams;
	QueryParams.bTraceComplex = true;
	QueryParams.MobilityType = EQueryMobilityType::Any;

	TArray<UDSBaseModel*> CabinetModels;
	TArray<FHitResult> HitResults;

	for (int i = 0; i < Plane.Num(); ++i)
	{
		FVector StartPoint = Plane[i];
		FVector EndPoint = StartPoint + (Plane[(i + 1) % Plane.Num()] - Plane[i]).GetSafeNormal() * 1.0f;
		ADesignStationController::Get()->GetWorld()->LineTraceMultiByChannel(HitResults, StartPoint, EndPoint, ECC_Visibility, QueryParams);
		for (auto& HitIter : HitResults)
		{
			AActor* OwnerActor = HitIter.GetActor();
			ADSBaseView* DSActor = Cast<ADSBaseView>(OwnerActor);
			if (DSActor == nullptr)
			{
				continue;
			}

			UDSBaseModel* CurrentModel = DSActor->GetModel();
			if (CurrentModel == nullptr)
			{
				continue;
			}

			if (UDSToolLibrary::IsCustomCabinetType(CurrentModel->GetTopLevelOwnerModel()->GetModelType()))
			{
				CabinetModels.AddUnique(CurrentModel->GetTopLevelOwnerModel());
			}
		}
	}

	return CabinetModels;
}

bool UDSCupBoardDoorLibrary::CheckAreaPlaneIsEnable(const TArray<FVector>& Plane, const TArray<UDSBaseModel*>& Cabinets, const FVector& DoorNormal)
{
	TArray<FCheckBoardPlane> CheckPlanes;
	for (auto& Element : Cabinets)
	{
		UDSCupboardModel* CastedModel = Cast<UDSCupboardModel>(Element);
		for (auto& Face : CreateSixPlaneFromObb(CastedModel->GetModelFixedOrientedBoundingBox()))
		{
			const float DotProduct = FVector::DotProduct(Face.Normal.GetSafeNormal(), DoorNormal.GetSafeNormal());
			if (FMath::IsNearlyEqual(DotProduct, 1.0f, KINDA_SMALL_NUMBER))
			{
				//找出和生成门法线相同的柜子面，求面积总和
				CheckPlanes.Add(Face);
				break;
			}
		}
	}

	float TotalArea = 0.0f; //柜体门方向上的总投影面积
	for (auto& CheckPlane : CheckPlanes)
	{
		TotalArea += FVector::Distance(CheckPlane.Points[0], CheckPlane.Points[1]) * FVector::Distance(CheckPlane.Points[1], CheckPlane.Points[2]);
	}

	float DoorArea = FVector::Distance(Plane[0], Plane[1]) * FVector::Distance(Plane[1], Plane[2]);

	if (TotalArea < DoorArea)
	{
		return false;
	}

	return true;
}

bool UDSCupBoardDoorLibrary::ArePointsCounterClockwise(const TArray<FVector>& InPoints, const FVector& InNormal)
{
	if (InPoints.Num() < 3)
	{
		return false;
	}

	// 确保法线是单位向量，并处理零向量情况
	FVector SafeNormal = InNormal.GetSafeNormal();
	if (SafeNormal.IsNearlyZero())
	{
		return false;
	}

	// 计算几何中心点
	FVector Center = FVector::ZeroVector;
	for (const FVector& Point : InPoints)
	{
		Center += Point;
	}
	Center /= InPoints.Num();

	// 存储第一个叉积与法线的点积结果的符号，用于后续比较
	// 使用一个非常小的容差值来处理浮点精度
	float FirstCrossDotNormal = 0.0f;
	bool bFirstSignEstablished = false;

	// 遍历相邻点对，检查叉积方向
	for (int32 i = 0; i < InPoints.Num(); ++i)
	{
		const FVector& CurrentPoint = InPoints[i];
		const FVector& NextPoint = InPoints[(i + 1) % InPoints.Num()]; // 循环到第一个点

		// 计算从中心点到当前点和下一个点的向量
		FVector VectorA = CurrentPoint - Center;
		FVector VectorB = NextPoint - Center;

		// 如果向量A或B是零向量（点与中心重合），则跳过或特殊处理
		// 这种情况下，它不能提供方向信息。如果所有点都与中心重合，则无法判断。
		if (VectorA.IsNearlyZero() || VectorB.IsNearlyZero())
		{
			continue;
		}

		FVector CrossResult = FVector::CrossProduct(VectorA, VectorB);

		// 计算叉积与法线的点积
		float CurrentCrossDotNormal = FVector::DotProduct(CrossResult, SafeNormal);

		// 处理共线或几乎共线的情况
		if (FMath::IsNearlyZero(CurrentCrossDotNormal, KINDA_SMALL_NUMBER))
		{
			continue;
		}

		// 建立第一个有效点对的方向基准
		if (!bFirstSignEstablished)
		{
			FirstCrossDotNormal = CurrentCrossDotNormal;
			bFirstSignEstablished = true;
		}
		else
		{
			// 检查当前点对的叉积方向是否与第一个点对的方向一致
			// 如果方向不一致（即一个正一个负），则不是逆时针（或顺时针）排序
			if (FMath::Sign(CurrentCrossDotNormal) != FMath::Sign(FirstCrossDotNormal))
			{
				return false; // 顺序不一致
			}
		}
	}

	// 如果 FirstCrossDotNormal > 0，则是逆时针
	// 如果 FirstCrossDotNormal < 0，则是顺时针
	return FirstCrossDotNormal < 0;
}

void UDSCupBoardDoorLibrary::DoorCoverAutoChange(const FDSDoorDependencyInfo& DependencyInfo, UDSBaseModel* SelectModel, FDSCustomDoorProperty& GenerateDoorData, const bool& bIsOnlyGetCover)
{
	//先根据净空加上下左右板件厚度，算出全盖区域大小
	//1------0
	//.      .
	//.      .
	//2------3
	TArray<FVector> WillPoints;
	FTransform SelectModelTransform = SelectModel->GetPropertySharedPtr()->TransformProperty.ToUETransform();

	WillPoints.Add(SelectModelTransform.TransformPosition(DependencyInfo.DoorContainerPlane.Points[0]));
	WillPoints.Add(SelectModelTransform.TransformPosition(DependencyInfo.DoorContainerPlane.Points[1]));
	WillPoints.Add(SelectModelTransform.TransformPosition(DependencyInfo.DoorContainerPlane.Points[2]));
	WillPoints.Add(SelectModelTransform.TransformPosition(DependencyInfo.DoorContainerPlane.Points[3]));

	FVector CubeCenter = (WillPoints[0] + WillPoints[1] + WillPoints[2] + WillPoints[3]) / 4.0f;

	//四个点，根据法线方向，向前推移门板厚度，形成上下左右4个面
	TArray<FVector> NewTopPlane = {WillPoints[0] + DependencyInfo.DoorContainerPlane.Normal * 3, WillPoints[1] + DependencyInfo.DoorContainerPlane.Normal * 3, WillPoints[1], WillPoints[0]};
	TArray<FVector> NewDownPlane = {WillPoints[2] + DependencyInfo.DoorContainerPlane.Normal * 3, WillPoints[3] + DependencyInfo.DoorContainerPlane.Normal * 3, WillPoints[3], WillPoints[2]};
	TArray<FVector> NewLeftPlane = {WillPoints[1] + DependencyInfo.DoorContainerPlane.Normal * 3, WillPoints[2] + DependencyInfo.DoorContainerPlane.Normal * 3, WillPoints[2], WillPoints[1]};
	TArray<FVector> NewRightPlane = {WillPoints[3] + DependencyInfo.DoorContainerPlane.Normal * 3, WillPoints[0] + DependencyInfo.DoorContainerPlane.Normal * 3, WillPoints[0], WillPoints[3]};

	TArray<UDSCupboardModel*> UpdateSelfDoor;

	ChangeAllDoorsCoverByBoardId(NewTopPlane, DependencyInfo, 0, GenerateDoorData, UpdateSelfDoor, bIsOnlyGetCover);
	ChangeAllDoorsCoverByBoardId(NewDownPlane, DependencyInfo, 1, GenerateDoorData, UpdateSelfDoor, bIsOnlyGetCover);
	ChangeAllDoorsCoverByBoardId(NewLeftPlane, DependencyInfo, 2, GenerateDoorData, UpdateSelfDoor, bIsOnlyGetCover);
	ChangeAllDoorsCoverByBoardId(NewRightPlane, DependencyInfo, 3, GenerateDoorData, UpdateSelfDoor, bIsOnlyGetCover);

	//和自身的抽屉做自适应盖
	TArray<UDSCupboardModel*> DrawerDoorModels;
	TArray<UDSBaseModel*> Result;
	UDSCounterTopLibrary::GetSingleComponentModels(SelectModel, Result);

	for (auto& It : Result)
	{
		if (UDSCupboardModel* DoorModel = Cast<UDSCupboardModel>(It))
		{
			if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), DoorModel->GetModelInfoRef().ComponentTreeData->ModelType))
			{
				if (UDSCupboardLibrary::FindNearestFunctionalCupboardModel(DoorModel) != nullptr)
				{
					DrawerDoorModels.AddUnique(Cast<UDSCupboardModel>(DoorModel->GetOwnerModel()));
				}
			}
		}
	}

	ChangeDrawerDoorCover(NewTopPlane, DrawerDoorModels, 0, CubeCenter, SelectModel, GenerateDoorData, bIsOnlyGetCover);
	ChangeDrawerDoorCover(NewDownPlane, DrawerDoorModels, 1, CubeCenter, SelectModel, GenerateDoorData, bIsOnlyGetCover);
	ChangeDrawerDoorCover(NewLeftPlane, DrawerDoorModels, 2, CubeCenter, SelectModel, GenerateDoorData, bIsOnlyGetCover);
	ChangeDrawerDoorCover(NewRightPlane, DrawerDoorModels, 3, CubeCenter, SelectModel, GenerateDoorData, bIsOnlyGetCover);

	TArray<UDSCupboardModel*> RootModels;
	for (auto& Trans : UpdateSelfDoor)
	{
		RootModels.AddUnique(Trans->GetRootCupboardModel());
	}
	for (auto& Root : RootModels)
	{
		Root->OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf);
	}
}

void UDSCupBoardDoorLibrary::ChangeDrawerDoorCover(const TArray<FVector>& Plane, const TArray<UDSCupboardModel*>& DrawerDoorModels, const int32& Index, const FVector& CubeCenter,
                                                   UDSBaseModel* SelectModel, FDSCustomDoorProperty& GenerateDoorData, const bool& bIsOnlyGetCover)
{
	FTransform SelectModelTransform = SelectModel->GetPropertySharedPtr()->TransformProperty.ToUETransform();
	for (auto& Drawer : DrawerDoorModels)
	{
		TArray<FVector> DrawerObb = Drawer->GetModelFixedOrientedBoundingBox();
		TArray<FCheckBoardPlane> SixPlane = CreateSixPlaneFromObb(DrawerObb);

		FVector VectorA = Plane[1] - Plane[0];
		FVector VectorB = Plane[2] - Plane[1];

		FVector PlaneNormalVector = FVector::CrossProduct(VectorA, VectorB);
		PlaneNormalVector.Normalize();

		FPlane BasicPlane(Plane[0], PlaneNormalVector);
		//找到线面交点
		FVector InterSectionPoint = FMath::LinePlaneIntersection(GetObbCenter(DrawerObb), CubeCenter, BasicPlane);
		//判断焦点在线段内，而不是线段延长线和面的交点
		if (!IsPointOnLineSegment(InterSectionPoint, GetObbCenter(DrawerObb), CubeCenter))
		{
			continue;
		}

		if (IsRectOBBIntersectOrAdjacent(Plane, DrawerObb))
		{
			FCheckBoardPlane NearestPlane;
			float Distance = 10000000.0f;
			for (auto& Face : SixPlane)
			{
				if (FVector::Parallel(PlaneNormalVector, Face.Normal, 0.01f))
				{
					if (FMath::Abs(Face.BasicPlane.PlaneDot(Plane[0])) < Distance)
					{
						Distance = FMath::Abs(Face.BasicPlane.PlaneDot(Plane[0]));
						NearestPlane = Face;
					}
				}
			}
			FVector RelativeNormal = SelectModelTransform.InverseTransformVector(NearestPlane.Normal).GetSafeNormal();

			EMaskingMode HasDrawerDoorCover;
			int32 DrawerIndex = 0;
			if (FVector::PointsAreNear(RelativeNormal, FVector(0, 0, 1), 0.01f))
			{
				HasDrawerDoorCover = GetCover(Drawer, 0);
				DrawerIndex = 0;
			}
			else if (FVector::PointsAreNear(RelativeNormal, FVector(0, 0, -1), 0.01f))
			{
				HasDrawerDoorCover = GetCover(Drawer, 1);
				DrawerIndex = 1;
			}
			else if (FVector::PointsAreNear(RelativeNormal, FVector(-1, 0, 0), 0.01f))
			{
				HasDrawerDoorCover = GetCover(Drawer, 2);
				DrawerIndex = 2;
			}
			else if (FVector::PointsAreNear(RelativeNormal, FVector(1, 0, 0), 0.01f))
			{
				HasDrawerDoorCover = GetCover(Drawer, 3);
				DrawerIndex = 3;
			}

			switch (Index)
			{
			case 0: //改新门的上盖
				{
					CompareAndChangeCoverOfDrawer(Drawer, HasDrawerDoorCover, GenerateDoorData.MaskingModeUp, bIsOnlyGetCover, DrawerIndex);
					break;
				}
			case 1: //改新门的下盖
				{
					CompareAndChangeCoverOfDrawer(Drawer, HasDrawerDoorCover, GenerateDoorData.MaskingModeDown, bIsOnlyGetCover, DrawerIndex);
					break;
				}
			case 2: //改新门的左盖
				{
					CompareAndChangeCoverOfDrawer(Drawer, HasDrawerDoorCover, GenerateDoorData.MaskingModeLeft, bIsOnlyGetCover, DrawerIndex);
					break;
				}
			case 3: //改新门的右盖
				{
					CompareAndChangeCoverOfDrawer(Drawer, HasDrawerDoorCover, GenerateDoorData.MaskingModeRight, bIsOnlyGetCover, DrawerIndex);
					break;
				}

			default: break;
			}
		}
	}
}

void UDSCupBoardDoorLibrary::CompareAndChangeCoverOfDrawer(const UDSCupboardModel* Drawer, const EMaskingMode& HasDrawerDoorCover, EMaskingMode& NewDoorCover, const bool& bIsOnlyGetCover, const int32& DrawerIndex)
{
	if (bIsOnlyGetCover)
	{
		if (HasDrawerDoorCover == EMaskingMode::ENoCover || HasDrawerDoorCover == EMaskingMode::EEmbedded)
		{
			return;
		}
		if (HasDrawerDoorCover == EMaskingMode::EOuterOver)
		{
			NewDoorCover = EMaskingMode::ENoCover;
		}
		if (HasDrawerDoorCover == EMaskingMode::ECoverHalf)
		{
			NewDoorCover = EMaskingMode::ECoverHalf;
		}
	}
	else
	{
		if (NewDoorCover == EMaskingMode::ENoCover || NewDoorCover == EMaskingMode::EEmbedded)
		{
			if (HasDrawerDoorCover != EMaskingMode::EOuterOver)
			{
				ModifyDrawerCover(Drawer, DrawerIndex, EMaskingMode::EOuterOver);
				return;
			}
		}
		if (NewDoorCover == EMaskingMode::EOuterOver)
		{
			if (HasDrawerDoorCover != EMaskingMode::ENoCover && HasDrawerDoorCover != EMaskingMode::EEmbedded)
			{
				ModifyDrawerCover(Drawer, DrawerIndex, EMaskingMode::ENoCover);
				return;
			}
		}
		if (NewDoorCover == EMaskingMode::ECoverHalf)
		{
			if (HasDrawerDoorCover != EMaskingMode::ECoverHalf)
			{
				ModifyDrawerCover(Drawer, DrawerIndex, EMaskingMode::ECoverHalf);
			}
		}
	}
}

void UDSCupBoardDoorLibrary::ModifyDrawerCover(const UDSCupboardModel* Model, const int32& Pos, const EMaskingMode& Cover)
{
	FString PName = TEXT("");
	switch (Pos)
	{
	case 0:
		{
			PName = TEXT("SBFG");
			break;
		}
	case 1:
		{
			PName = TEXT("XBFG");
			break;
		}
	case 2:
		{
			PName = TEXT("ZBFG");
			break;
		}
	case 3:
		{
			PName = TEXT("YBFG");
			break;
		}

	default: break;
	}

	FParameterData* FindData = Model->GetModelInfo().ComponentTreeData->ComponentParameters.FindByPredicate([PName](const FParameterData& Param)-> bool
	{
		return Param.Data.name.Equals(PName);
	});

	if (FindData != nullptr && FindData->Data.is_enum)
	{
		FindData->Data.value = FString::FromInt(static_cast<int32>(Cover));
		FindData->Data.expression = FString::FromInt(static_cast<int32>(Cover));
	}
}

void UDSCupBoardDoorLibrary::ChangeAllDoorsCoverByBoardId(const TArray<FVector>& Plane, const FDSDoorDependencyInfo& DependencyInfo, const int32& Index,
                                                          FDSCustomDoorProperty& GenerateDoorData, TArray<UDSCupboardModel*>& UpdateSelfDoor, const bool& bIsOnlyGetCover)
{
	//DependencyInfo当前新生成门的依赖信息
	//找出当前板件上所有相关门的UUID，但是不能通过UUID去getmodelbyid，应该已有门在记录依赖是的UUID，和柜体刷新后的UUID是不一致的，只在参数里面保留的就UUID
	const TArray<FString> DoorIds = UDSModelDependencySubsystem::GetInstance()->FindDoorByDependentGroupBoard(DependencyInfo.DependentBoards[Index].Key);
	if (DoorIds.IsEmpty())
	{
		return;
	}

	const FString SameBoardId = DependencyInfo.DependentBoards[Index].Key;
	//新门当前板的ID，找出有空间矩形和OBB碰撞的已有门后，要根据这块板在已有门中的index判断是哪个盖值
	TMap<int32, TPair<UDSCupboardModel*, int32>> NeedAutoCoverDoorContainer;
	for (auto& DoorId : DoorIds)
	{
		UDSCupboardModel* DoorModel = nullptr;
		for (auto& It : UDSMVCSubsystem::GetInstance()->GetAllCustomModels())
		{
			if (UDSCupboardModel* Model = Cast<UDSCupboardModel>(It))
			{
				if (Model->GetModelInfoRef().ComponentTreeData->UUID == DoorId)
				{
					DoorModel = Model;
					break;
				}
			}
		}

		if (DoorModel)
		{
			FDSDoorDependencyInfo HasDoorInfo = UDSModelDependencySubsystem::GetInstance()->FindDoorDependencyInfo(DoorId);
			if (HasDoorInfo.DependentBoards.IsEmpty())
			{
				continue;
			}
			FTransform OwnTrans = DoorModel->GetRootCupboardModel()->GetPropertySharedPtr()->GetActualTransform();

			TArray<FVector> WillPoints;

			WillPoints.Add(OwnTrans.TransformPosition(HasDoorInfo.DoorContainerPlane.Points[0]));
			WillPoints.Add(OwnTrans.TransformPosition(HasDoorInfo.DoorContainerPlane.Points[1]));
			WillPoints.Add(OwnTrans.TransformPosition(HasDoorInfo.DoorContainerPlane.Points[2]));
			WillPoints.Add(OwnTrans.TransformPosition(HasDoorInfo.DoorContainerPlane.Points[3]));

			TArray<FVector> CubeToObb;
			CubeToObb.Add(WillPoints[1]);
			CubeToObb.Add(WillPoints[0]);
			CubeToObb.Add(WillPoints[0] + HasDoorInfo.DoorContainerPlane.Normal * 3);
			CubeToObb.Add(WillPoints[1] + HasDoorInfo.DoorContainerPlane.Normal * 3);
			CubeToObb.Add(WillPoints[2]);
			CubeToObb.Add(WillPoints[3]);
			CubeToObb.Add(WillPoints[3] + HasDoorInfo.DoorContainerPlane.Normal * 3);
			CubeToObb.Add(WillPoints[2] + HasDoorInfo.DoorContainerPlane.Normal * 3);

			if (IsRectOBBIntersectOrAdjacent(Plane, CubeToObb))
			{
				for (int i = 0; i < HasDoorInfo.DependentGroupBoards.Num(); ++i)
				{
					for (auto& Info : HasDoorInfo.DependentGroupBoards[i])
					{
						if (Info.Key == SameBoardId)
						{
							if (i != Index) //同一块板件如果出现在两个不同门的同一位置，认为是并列的，不用考虑避让
							{
								TPair<UDSCupboardModel*, int32> RefDoorPair(DoorModel, i);
								NeedAutoCoverDoorContainer.Add(Index, RefDoorPair);
							}
						}
					}
				}
			}
		}

		for (auto& Need : NeedAutoCoverDoorContainer)
		{
			switch (Need.Key)
			{
			case 0: //改新门的上盖
				{
					CompareAndChangeCover(Need.Value, GenerateDoorData.MaskingModeUp, UpdateSelfDoor, bIsOnlyGetCover);
					break;
				}
			case 1: //改新门的下盖
				{
					CompareAndChangeCover(Need.Value, GenerateDoorData.MaskingModeDown, UpdateSelfDoor, bIsOnlyGetCover);
					break;
				}
			case 2: //改新门的左盖
				{
					CompareAndChangeCover(Need.Value, GenerateDoorData.MaskingModeLeft, UpdateSelfDoor, bIsOnlyGetCover);
					break;
				}
			case 3: //改新门的右盖
				{
					CompareAndChangeCover(Need.Value, GenerateDoorData.MaskingModeRight, UpdateSelfDoor, bIsOnlyGetCover);
					break;
				}

			default: break;
			}
		}
	}
}

void UDSCupBoardDoorLibrary::CompareAndChangeCover(const TPair<UDSCupboardModel*, int32> DoorPair, EMaskingMode& NewDoorCover, TArray<UDSCupboardModel*>& UpdateSelfDoor, const bool& bIsOnlyGetCover)
{
	const EMaskingMode HasDoorCover = GetCover(DoorPair.Key, DoorPair.Value);

	if (bIsOnlyGetCover)
	{
		if (HasDoorCover == EMaskingMode::ENoCover || HasDoorCover == EMaskingMode::EEmbedded)
		{
			return;
		}
		if (HasDoorCover == EMaskingMode::EOuterOver)
		{
			NewDoorCover = EMaskingMode::ENoCover;
		}
		if (HasDoorCover == EMaskingMode::ECoverHalf)
		{
			NewDoorCover = EMaskingMode::ECoverHalf;
		}
	}
	else
	{
		if (NewDoorCover == EMaskingMode::ENoCover || NewDoorCover == EMaskingMode::EEmbedded)
		{
			if (HasDoorCover != EMaskingMode::EOuterOver)
			{
				ModifyCover(DoorPair.Key, DoorPair.Value, EMaskingMode::EOuterOver);
				UpdateSelfDoor.AddUnique(DoorPair.Key);
				return;
			}
		}
		if (NewDoorCover == EMaskingMode::EOuterOver)
		{
			if (HasDoorCover != EMaskingMode::ENoCover && HasDoorCover != EMaskingMode::EEmbedded)
			{
				ModifyCover(DoorPair.Key, DoorPair.Value, EMaskingMode::ENoCover);
				UpdateSelfDoor.AddUnique(DoorPair.Key);
				return;
			}
		}
		if (NewDoorCover == EMaskingMode::ECoverHalf)
		{
			if (HasDoorCover != EMaskingMode::ECoverHalf)
			{
				ModifyCover(DoorPair.Key, DoorPair.Value, EMaskingMode::ECoverHalf);
				UpdateSelfDoor.AddUnique(DoorPair.Key);
			}
		}
	}
}

EMaskingMode UDSCupBoardDoorLibrary::GetCover(const UDSCupboardModel* Model, const int32& Pos)
{
	FString PName = TEXT("");
	switch (Pos)
	{
	case 0:
		{
			PName = TEXT("SBFG");
			break;
		}
	case 1:
		{
			PName = TEXT("XBFG");
			break;
		}
	case 2:
		{
			PName = TEXT("ZBFG");
			break;
		}
	case 3:
		{
			PName = TEXT("YBFG");
			break;
		}

	default: break;
	}

	FParameterData* FindData = Model->GetModelInfo().ComponentTreeData->ComponentParameters.FindByPredicate([PName](const FParameterData& Param)-> bool
	{
		return Param.Data.name.Equals(PName);
	});

	if (FindData != nullptr && FindData->Data.is_enum)
	{
		return static_cast<EMaskingMode>(FCString::Atoi(*FindData->Data.value));
	}

	return EMaskingMode::EOuterOver;
}

void UDSCupBoardDoorLibrary::ModifyCover(UDSCupboardModel* Model, const int32& Pos, const EMaskingMode& Cover)
{
	FString PName = TEXT("");
	switch (Pos)
	{
	case 0:
		{
			PName = TEXT("SBFG");
			break;
		}
	case 1:
		{
			PName = TEXT("XBFG");
			break;
		}
	case 2:
		{
			PName = TEXT("ZBFG");
			break;
		}
	case 3:
		{
			PName = TEXT("YBFG");
			break;
		}

	default: break;
	}

	FParameterData* FindData = Model->GetModelInfo().ComponentTreeData->ComponentParameters.FindByPredicate([PName](const FParameterData& Param)-> bool
	{
		return Param.Data.name.Equals(PName);
	});

	if (FindData != nullptr && FindData->Data.is_enum)
	{
		ChangeCoverByProperty(Model, PName, FindData->Data.value, FString::FromInt(static_cast<int32>(Cover)));
	}
}

bool UDSCupBoardDoorLibrary::IsRectOBBIntersectOrAdjacent(const TArray<FVector>& RectVertices, const TArray<FVector>& ObbVertices, const float& Epsilon)
{
	TArray<FVector> AxesToTest;
	TArray<FVector> ObbAxes;
	//算OBB的三条法线，用SAT计算相交相邻
	FVector ObbCenter = GetObbCenter(ObbVertices);
	ObbAxes.Add(((ObbVertices[3] + ObbVertices[2] + ObbVertices[1] + ObbVertices[0]) / 4 - ObbCenter).GetSafeNormal());
	ObbAxes.Add(((ObbVertices[2] + ObbVertices[6] + ObbVertices[5] + ObbVertices[1]) / 4 - ObbCenter).GetSafeNormal());
	ObbAxes.Add(((ObbVertices[3] + ObbVertices[7] + ObbVertices[6] + ObbVertices[2]) / 4 - ObbCenter).GetSafeNormal());

	//矩形法线
	const FVector RectNormal = FVector::CrossProduct(RectVertices[1] - RectVertices[0], RectVertices[2] - RectVertices[0]).GetSafeNormal();
	AxesToTest.Add(RectNormal);
	// 2. OBB主轴
	AxesToTest.Append(ObbAxes);
	// 3. 矩形边 × OBB主轴
	for (int i = 0; i < 4; ++i)
	{
		FVector RectEdge = RectVertices[(i + 1) % 4] - RectVertices[i];
		for (const FVector& OAxis : ObbAxes)
		{
			FVector Cross = FVector::CrossProduct(RectEdge, OAxis);
			if (!Cross.IsNearlyZero())
			{
				AxesToTest.Add(Cross.GetSafeNormal());
			}
		}
	}

	// 依次检测所有轴
	for (const FVector& Axis : AxesToTest)
	{
		float MinA, MaxA, MinB, MaxB;
		ProjectOntoAxis(RectVertices, Axis, MinA, MaxA);
		ProjectOntoAxis(ObbVertices, Axis, MinB, MaxB);

		if (MaxA + Epsilon < MinB || MaxB + Epsilon < MinA)
		{
			// 存在分离轴，不相交也不相邻
			return false;
		}
		// 判断是否相邻（MaxA == MinB 或 MaxB == MinA）
	}
	// 所有投影均重叠，相交或相邻
	return true;
}

//将点集投影到轴上
void UDSCupBoardDoorLibrary::ProjectOntoAxis(const TArray<FVector>& Points, const FVector& Axis, float& OutMin, float& OutMax)
{
	OutMin = OutMax = FVector::DotProduct(Points[0], Axis);
	for (int i = 1; i < Points.Num(); ++i)
	{
		const float Projection = FVector::DotProduct(Points[i], Axis);
		OutMin = FMath::Min(OutMin, Projection);
		OutMax = FMath::Max(OutMax, Projection);
	}
}

void UDSCupBoardDoorLibrary::UpdateDoorOnCupboard(UDSBaseModel* InCupboard, UDSCupboardModel* SelfDoor)
{
	if (!UDSToolLibrary::IsCustomCupboardType(InCupboard->GetModelType()))
	{
		return;
	}
	TSet<FString> DoorUUIDs;

	auto ModelInfo = Cast<UDSCupboardModel>(InCupboard)->GetModelInfo();

	auto Container = ModelInfo.ComponentTreeData->ChildComponent.FindByPredicate([](const TSharedPtr<FMultiComponentDataItem>& Item)
	{
		return UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(Item->ModelType) == EDSModelType::E_Custom_DoorContainer;
	});

	if (!Container)
	{
		return;
	}

	TMap<TSharedPtr<FMultiComponentDataItem>, TSharedPtr<FMultiComponentDataItem>> NeedDeleteDoors;
	for (auto& DoorContainer : ModelInfo.ComponentTreeData->ChildComponent)
	{
		if (UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(DoorContainer->ModelType) == EDSModelType::E_Custom_DoorContainer)
		{
			TArray<UDSBaseModel*> BoardModels;

			bool bIsDelete = false;
			TArray<TSharedPtr<FMultiComponentDataItem>> DoorList;
			RecursionCollectDoors(DoorContainer, DoorList);
			for (auto& Door : DoorList)
			{
				if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), Door->ModelType))
				{
					BoardModels = UDSModelDependencySubsystem::GetInstance()->FindDependentModels(Door->UUID);
					if (!BoardModels.IsEmpty() && BoardModels.Num() != 4)
					{
						NeedDeleteDoors.Add(ModelInfo.ComponentTreeData, DoorContainer);
						bIsDelete = true;
						break;
					}
				}
			}

			if (bIsDelete)
			{
				continue;
			}

			const FString DoorOpenType = DoorContainer->GetParameterValue(TEXT("MHFFX"));

			FParameterData* DoorSpilt = DoorContainer->ComponentParameters.FindByPredicate([](const FParameterData& Param)-> bool { return Param.Data.name == TEXT("MHFFX"); });

			if (!DoorSpilt)
			{
				return;
			}

			const FString DoorSpiltStr = DoorSpilt->Data.GetFormattedValue();

			//没有板可能是自带门
			if (BoardModels.IsEmpty())
			{
				continue;
			}

			FDSSuitablePlane SuitablePlane;
			bool bIsOnlyEmbedded = false;
			TArray<TArray<UDSBaseModel*>> DependenciesGroup;
			TArray<UDSBaseModel*> DependentCabinets;
			if (!CalculateDoorArea(BoardModels, SuitablePlane, bIsOnlyEmbedded, DependenciesGroup, DependentCabinets))
			{
				NeedDeleteDoors.Add(ModelInfo.ComponentTreeData, DoorContainer);
				continue;
			}
			FDSCustomDoorProperty GenerateDoorData;
			UpdateDoorTouchedBoardsThickness(BoardModels, GenerateDoorData);
			GetContainerCoverFromParams(DoorContainer, GenerateDoorData);

			if (SuitablePlane.Points.Num() > 2)
			{
				FDSCraftDoorInfo SpaceData;
				SpaceData.PlanePoints = SuitablePlane.Points;

				FTransform SelectModelTransform = InCupboard->GetPropertySharedPtr()->TransformProperty.ToUETransform();

				TArray<FVector> NewPlanePoints;

				ConvertInnerPointToContainerPointByCover(SpaceData.PlanePoints[1], SpaceData.PlanePoints[3], SpaceData.PlanePoints[0],
				                                         FCString::Atod(*GenerateDoorData.YCBJT) * 0.1f, FCString::Atod(*GenerateDoorData.SCBJT) * 0.1f, SelectModelTransform,
				                                         GenerateDoorData.MaskingModeRight, GenerateDoorData.MaskingModeUp, NewPlanePoints.AddDefaulted_GetRef());

				ConvertInnerPointToContainerPointByCover(SpaceData.PlanePoints[0], SpaceData.PlanePoints[2], SpaceData.PlanePoints[1],
				                                         FCString::Atod(*GenerateDoorData.ZCBJT) * 0.1f, FCString::Atod(*GenerateDoorData.SCBJT) * 0.1f, SelectModelTransform,
				                                         GenerateDoorData.MaskingModeLeft, GenerateDoorData.MaskingModeUp, NewPlanePoints.AddDefaulted_GetRef());

				ConvertInnerPointToContainerPointByCover(SpaceData.PlanePoints[3], SpaceData.PlanePoints[1], SpaceData.PlanePoints[2],
				                                         FCString::Atod(*GenerateDoorData.ZCBJT) * 0.1f, FCString::Atod(*GenerateDoorData.XCBJT) * 0.1f, SelectModelTransform,
				                                         GenerateDoorData.MaskingModeLeft, GenerateDoorData.MaskingModeDown, NewPlanePoints.AddDefaulted_GetRef());

				ConvertInnerPointToContainerPointByCover(SpaceData.PlanePoints[2], SpaceData.PlanePoints[0], SpaceData.PlanePoints[3],
				                                         FCString::Atod(*GenerateDoorData.YCBJT) * 0.1f, FCString::Atod(*GenerateDoorData.XCBJT) * 0.1f, SelectModelTransform,
				                                         GenerateDoorData.MaskingModeRight, GenerateDoorData.MaskingModeDown, NewPlanePoints.AddDefaulted_GetRef());

				auto WJ = FVector::Distance(NewPlanePoints[0], NewPlanePoints[1]) * 10;
				auto HJ = FVector::Distance(NewPlanePoints[1], NewPlanePoints[2]) * 10;

				double OldSize;
				const int32 HFFX = FCString::Atoi(*DoorContainer->GetParameterValue(TEXT("MHFFX")));
				if (HFFX == 0)
				{
					OldSize = FCString::Atod(*DoorContainer->GetParameterValue(TEXT("WJ")));
				}
				else
				{
					OldSize = FCString::Atod(*DoorContainer->GetParameterValue(TEXT("HJ")));
				}

				DoorContainer->SetParameter(TEXT("WJ"), FString::SanitizeFloat(WJ));
				DoorContainer->SetParameter(TEXT("HJ"), FString::SanitizeFloat(HJ));
				DoorContainer->SetParameter(TEXT("SCBJT"), GenerateDoorData.SCBJT);
				DoorContainer->SetParameter(TEXT("XCBJT"), GenerateDoorData.XCBJT);
				DoorContainer->SetParameter(TEXT("ZCBJT"), GenerateDoorData.ZCBJT);
				DoorContainer->SetParameter(TEXT("YCBJT"), GenerateDoorData.YCBJT);

				FTransform FinalTransform = FTransform(DoorContainer->ComponentRotation.GetRotation()) * FTransform(FQuat::Identity, NewPlanePoints[2] * 10.0f);
				DoorContainer->ComponentLocation = FinalTransform.GetLocation();

				RecursionRefreshDoor(DoorContainer, DoorContainer, WJ, HJ, OldSize, NeedDeleteDoors);
			}
		}
	}

	if (!NeedDeleteDoors.IsEmpty())
	{
		UDSUISubsystem::GetInstance()->SetToastState(EToastState::EWarning, TEXT("超出门的极值，已删除门"));
		for (auto& Item : NeedDeleteDoors)
		{
			Item.Key->ChildComponent.Remove(Item.Value);
		}
	}

	//切换门板类型，可能会删除，也可能不删除，要根据删除结果来判断，并重置选中状态
	bool bIsDelete = false;
	FString OldUUid;
	if (SelfDoor)
	{
		OldUUid = SelfDoor->GetModelInfoRef().ComponentTreeData->UUID;
		for (auto& Item : NeedDeleteDoors)
		{
			for (auto DoorUUid : Item.Value->ChildComponent)
			{
				if (DoorUUid->UUID == SelfDoor->GetModelInfoRef().ComponentTreeData->UUID)
				{
					bIsDelete = true;
					break;
				}
			}
		}
	}

	InCupboard->OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf, FDSBroadcastMarkData::NotBroadcastToMVCMark);

	if (bIsDelete)
	{
		UDSMVCSubsystem::GetInstance()->SetCurrentModel(nullptr);
		UDSUISubsystem::GetInstance()->ProcessStateEvent(nullptr, EUIOperationType::Selected, UDSMVCSubsystem::GetInstance()->GetStateType());
	}
	else
	{
		TArray<UDSCupboardModel*> DoorModels;
		TArray<UDSBaseModel*> Result;
		UDSCounterTopLibrary::GetSingleComponentModels(InCupboard, Result);

		for (auto& It : Result)
		{
			UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(It);
			if (!CupboardModel)
			{
				continue;
			}

			if (OldUUid == CupboardModel->GetModelInfoRef().ComponentTreeData->UUID)
			{
				UDSMVCSubsystem::GetInstance()->SetCurrentModel(CupboardModel);
				UDSUISubsystem::GetInstance()->ProcessStateEvent(CupboardModel, EUIOperationType::Selected, UDSMVCSubsystem::GetInstance()->GetStateType());
			}
		}
	}
}

void UDSCupBoardDoorLibrary::RecursionRefreshDoor(const TSharedPtr<FMultiComponentDataItem>& ParentContainer, const TSharedPtr<FMultiComponentDataItem>& DoorContainer, const float& WJ, const float& HJ, const double& TotalSize,
                                                  TMap<TSharedPtr<FMultiComponentDataItem>, TSharedPtr<FMultiComponentDataItem>>& NeedDeleteDoors)
{
	DoorContainer->SetParameter(TEXT("WJ"), FString::SanitizeFloat(WJ));
	DoorContainer->SetParameter(TEXT("HJ"), FString::SanitizeFloat(HJ));

	FParameterData* DoorSpilt = DoorContainer->ComponentParameters.FindByPredicate([](const FParameterData& Param)-> bool { return Param.Data.name == TEXT("MHFFX"); });

	if (!DoorSpilt)
	{
		return;
	}

	const int32 DoorSpiltStr = FCString::Atoi(*DoorSpilt->Data.GetFormattedValue());

	FParameterData* NumData = DoorContainer->ComponentParameters.FindByPredicate([](const FParameterData& Param)-> bool { return Param.Data.name.Equals(TEXT("MHFSL")); });

	if (!NumData)
	{
		return;
	}

	//重新计算门和子容器计算单独抽成一个函数，要根据划分枚举来计算，会很长
	if (DoorSpiltStr == 0)
	{
		TArray<TPair<TSharedPtr<FMultiComponentDataItem>, TPair<float, float>>> NewOfDoors; //float分别是划分大小，位置

		TArray<int32> StateStr;
		for (auto& Item : DoorContainer->ChildComponent)
		{
			StateStr.Add(FCString::Atoi(*Item->GetParameterValue(TEXT("MHFZT"))));
		}

		int32 State = 0;
		if (StateStr.Contains(2) || StateStr.Contains(3))
		{
			State = 2;
		}
		else if (StateStr.Contains(1))
		{
			State = 1;
		}

		double EveryChangeSize = (WJ - TotalSize) / FCString::Atoi(*NumData->Data.value); //尺寸变化给每一个元素的改变量

		switch (State)
		{
		case 0:
			for (auto& Item : DoorContainer->ChildComponent)
			{
				const int32 SeqNo = FCString::Atoi(*Item->GetParameterValue(TEXT("MBPXH"))) == 99 ? FCString::Atoi(*NumData->Data.value) - 1 : FCString::Atoi(*Item->GetParameterValue(TEXT("MBPXH")));
				float Size;
				float Loc;

				if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), Item->ModelType))
				{
					Size = FCString::Atod(*Item->GetParameterValue(TEXT("MHFDX"))) + EveryChangeSize;
					Loc = FCString::Atoi(*Item->GetParameterValue(TEXT("MSCX"))) + EveryChangeSize * SeqNo;
				}
				if (UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(Item->ModelType) == EDSModelType::E_Custom_DoorContainer)
				{
					Size = FCString::Atod(*Item->GetParameterValue(TEXT("WJ"))) + EveryChangeSize;
					Loc = (Item->ComponentLocation.GetLocation().X + EveryChangeSize * 0.1f * SeqNo) * 10.0f;
				}

				NewOfDoors.Add(TPair<TSharedPtr<FMultiComponentDataItem>, TPair<float, float>>(Item, TPair<float, float>(Size, Loc)));
			}
			break;
		case 1:
			for (auto& Item : DoorContainer->ChildComponent)
			{
				const int32 SeqNo = FCString::Atoi(*Item->GetParameterValue(TEXT("MBPXH")));
				const float MHFDX = GetSplitValue(Item, TEXT("WJ"));
				if (SeqNo == 0)
				{
					NewOfDoors.Add(TPair<TSharedPtr<FMultiComponentDataItem>, TPair<float, float>>(Item, TPair<float, float>(MHFDX / TotalSize * WJ, 0)));
				}
				else
				{
					NewOfDoors.Add(TPair<TSharedPtr<FMultiComponentDataItem>, TPair<float, float>>(Item, TPair<float, float>(MHFDX / TotalSize * WJ, WJ - MHFDX / TotalSize * WJ)));
				}
			}
			break;
		case 2:
			for (auto& Item : DoorContainer->ChildComponent)
			{
				const int32 SeqNo = FCString::Atoi(*Item->GetParameterValue(TEXT("MBPXH")));
				const float MHFDX = GetSplitValue(Item, TEXT("WJ"));
				if (SeqNo == 0)
				{
					if (FCString::Atoi(*Item->GetParameterValue(TEXT("MHFZT"))) == 2)
					{
						NewOfDoors.Add(TPair<TSharedPtr<FMultiComponentDataItem>, TPair<float, float>>(Item, TPair<float, float>(MHFDX, 0)));
					}
					else
					{
						NewOfDoors.Add(TPair<TSharedPtr<FMultiComponentDataItem>, TPair<float, float>>(Item, TPair<float, float>(MHFDX + WJ - TotalSize, 0)));
					}
				}
				else
				{
					if (FCString::Atoi(*Item->GetParameterValue(TEXT("MHFZT"))) == 2)
					{
						NewOfDoors.Add(TPair<TSharedPtr<FMultiComponentDataItem>, TPair<float, float>>(Item, TPair<float, float>(MHFDX, WJ - MHFDX)));
					}
					else
					{
						NewOfDoors.Add(TPair<TSharedPtr<FMultiComponentDataItem>, TPair<float, float>>(Item, TPair<float, float>(MHFDX + WJ - TotalSize, WJ - (MHFDX + WJ - TotalSize))));
					}
				}
			}
			break;
		default:
			break;
		}

		for (int i = 0; i < NewOfDoors.Num(); ++i)
		{
			const TPair<TSharedPtr<FMultiComponentDataItem>, TPair<float, float>>& NewOfDoor = NewOfDoors[i];
			if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), NewOfDoor.Key->ModelType))
			{
				if (FParameterData* WidthParam = NewOfDoor.Key->ComponentParameters.FindByPredicate([](const FParameterData& InParam) { return InParam.Data.name == TEXT("W"); }))
				{
					const int32 MaxValue = WidthParam->Data.max_value.IsEmpty() ? FMath::CeilToInt32(NewOfDoor.Value.Key) : FCString::Atoi(*WidthParam->Data.max_value);
					const int32 MinValue = WidthParam->Data.min_value.IsEmpty() ? 1 : FCString::Atoi(*WidthParam->Data.min_value);
					if (NewOfDoor.Value.Key < MinValue || NewOfDoor.Value.Key > MaxValue)
					{
						NeedDeleteDoors.Add(DoorContainer, NewOfDoor.Key);
						break;
					}
				}
				if (FParameterData* HighParam = NewOfDoor.Key->ComponentParameters.FindByPredicate([](const FParameterData& InParam) { return InParam.Data.name == TEXT("H"); }))
				{
					const int32 MaxValue = HighParam->Data.max_value.IsEmpty() ? FMath::CeilToInt32(HJ) : FCString::Atoi(*HighParam->Data.max_value);
					const int32 MinValue = HighParam->Data.min_value.IsEmpty() ? 1 : FCString::Atoi(*HighParam->Data.min_value);
					if (HJ < MinValue || HJ > MaxValue)
					{
						NeedDeleteDoors.Add(DoorContainer, NewOfDoor.Key);
						break;
					}
				}
				NewOfDoor.Key->SetParameter(TEXT("MHFDX"), FString::SanitizeFloat(NewOfDoor.Value.Key));
				NewOfDoor.Key->SetParameter(TEXT("MSCX"), FString::FromInt(NewOfDoor.Value.Value));
			}
			else
			{
				NewOfDoor.Key->ComponentLocation.LocationX.Value = FString::SanitizeFloat(NewOfDoor.Value.Value);
				NewOfDoor.Key->ComponentLocation.LocationX.Expression = FString::SanitizeFloat(NewOfDoor.Value.Value);
				double OldSize;
				const int32 HFFX = FCString::Atoi(*NewOfDoor.Key->GetParameterValue(TEXT("MHFFX")));
				if (HFFX == 0)
				{
					OldSize = FCString::Atod(*NewOfDoor.Key->GetParameterValue(TEXT("WJ")));
				}
				else
				{
					OldSize = FCString::Atod(*NewOfDoor.Key->GetParameterValue(TEXT("HJ")));
				}
				RecursionRefreshDoor(DoorContainer, NewOfDoor.Key, NewOfDoor.Value.Key, HJ, OldSize, NeedDeleteDoors);
			}
		}
	}
	else
	{
		TArray<TPair<TSharedPtr<FMultiComponentDataItem>, TPair<float, float>>> NewOfDoors;

		TArray<int32> StateStr;
		for (auto& Item : DoorContainer->ChildComponent)
		{
			StateStr.Add(FCString::Atoi(*Item->GetParameterValue(TEXT("MHFZT"))));
		}

		int32 State = 0;
		if (StateStr.Contains(2) || StateStr.Contains(3))
		{
			State = 2;
		}
		else if (StateStr.Contains(1))
		{
			State = 1;
		}

		double EveryChangeSize = (HJ - TotalSize) / FCString::Atoi(*NumData->Data.value); //尺寸变化给每一个元素的改变量

		switch (State)
		{
		case 0:
			for (auto& Item : DoorContainer->ChildComponent)
			{
				const int32 SeqNo = FCString::Atoi(*Item->GetParameterValue(TEXT("MBPXH"))) == 99 ? FCString::Atoi(*NumData->Data.value) - 1 : FCString::Atoi(*Item->GetParameterValue(TEXT("MBPXH")));
				float Size;
				float Loc;

				if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), Item->ModelType))
				{
					Size = FCString::Atod(*Item->GetParameterValue(TEXT("MHFDX"))) + EveryChangeSize;
					Loc = FCString::Atoi(*Item->GetParameterValue(TEXT("MSCZ"))) + EveryChangeSize * (FCString::Atoi(*NumData->Data.value) - 1 - SeqNo);
				}
				if (UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(Item->ModelType) == EDSModelType::E_Custom_DoorContainer)
				{
					Size = FCString::Atod(*Item->GetParameterValue(TEXT("HJ"))) + EveryChangeSize;
					Loc = (Item->ComponentLocation.GetLocation().Z + EveryChangeSize * 0.1f * (FCString::Atoi(*NumData->Data.value) - 1 - SeqNo)) * 10.0f;
				}
				NewOfDoors.Add(TPair<TSharedPtr<FMultiComponentDataItem>, TPair<float, float>>(Item, TPair<float, float>(Size, Loc)));
			}
			break;
		case 1:
			for (auto& Item : DoorContainer->ChildComponent)
			{
				const int32 SeqNo = FCString::Atoi(*Item->GetParameterValue(TEXT("MBPXH")));
				const float MHFDX = GetSplitValue(Item, TEXT("HJ"));
				if (SeqNo == 99)
				{
					NewOfDoors.Add(TPair<TSharedPtr<FMultiComponentDataItem>, TPair<float, float>>(Item, TPair<float, float>(MHFDX / TotalSize * HJ, 0)));
				}
				else
				{
					NewOfDoors.Add(TPair<TSharedPtr<FMultiComponentDataItem>, TPair<float, float>>(
							Item,
							TPair<float, float>(MHFDX / TotalSize * HJ, HJ - MHFDX / TotalSize * HJ))
					);
				}
			}
			break;
		case 2:
			for (auto& Item : DoorContainer->ChildComponent)
			{
				const int32 SeqNo = FCString::Atoi(*Item->GetParameterValue(TEXT("MBPXH")));
				const float MHFDX = GetSplitValue(Item, TEXT("HJ"));
				if (SeqNo == 99)
				{
					if (FCString::Atoi(*Item->GetParameterValue(TEXT("MHFZT"))) == 2)
					{
						NewOfDoors.Add(TPair<TSharedPtr<FMultiComponentDataItem>, TPair<float, float>>(Item, TPair<float, float>(MHFDX, 0)));
					}
					else
					{
						NewOfDoors.Add(TPair<TSharedPtr<FMultiComponentDataItem>, TPair<float, float>>(Item, TPair<float, float>(MHFDX + HJ - TotalSize, 0)));
					}
				}
				else
				{
					if (FCString::Atoi(*Item->GetParameterValue(TEXT("MHFZT"))) == 2)
					{
						NewOfDoors.Add(TPair<TSharedPtr<FMultiComponentDataItem>, TPair<float, float>>(Item, TPair<float, float>(MHFDX, HJ - MHFDX)));
					}
					else
					{
						NewOfDoors.Add(TPair<TSharedPtr<FMultiComponentDataItem>, TPair<float, float>>(Item, TPair<float, float>(MHFDX + HJ - TotalSize, HJ - (MHFDX + HJ - TotalSize))));
					}
				}
			}
			break;
		default:
			break;
		}

		for (int i = 0; i < NewOfDoors.Num(); ++i)
		{
			const TPair<TSharedPtr<FMultiComponentDataItem>, TPair<float, float>>& NewOfDoor = NewOfDoors[i];
			if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), NewOfDoor.Key->ModelType))
			{
				if (FParameterData* WidthParam = NewOfDoor.Key->ComponentParameters.FindByPredicate([](const FParameterData& InParam) { return InParam.Data.name == TEXT("W"); }))
				{
					const int32 MaxValue = WidthParam->Data.max_value.IsEmpty() ? FMath::CeilToInt32(WJ) : (FCString::Atoi(*WidthParam->Data.max_value));
					const int32 MinValue = WidthParam->Data.min_value.IsEmpty() ? 1 : FCString::Atoi(*WidthParam->Data.min_value);
					if (WJ < MinValue || WJ > MaxValue)
					{
						NeedDeleteDoors.Add(DoorContainer, NewOfDoor.Key);
						break;
					}
				}
				if (FParameterData* HighParam = NewOfDoor.Key->ComponentParameters.FindByPredicate([](const FParameterData& InParam) { return InParam.Data.name == TEXT("H"); }))
				{
					const int32 MaxValue = HighParam->Data.max_value.IsEmpty() ? FMath::CeilToInt32(NewOfDoor.Value.Key) : (FCString::Atoi(*HighParam->Data.max_value));
					const int32 MinValue = HighParam->Data.min_value.IsEmpty() ? 1 : FCString::Atoi(*HighParam->Data.min_value);
					if (NewOfDoor.Value.Key < MinValue || NewOfDoor.Value.Key > MaxValue)
					{
						NeedDeleteDoors.Add(DoorContainer, NewOfDoor.Key);
						break;
					}
				}
				NewOfDoor.Key->SetParameter(TEXT("MHFDX"), FString::SanitizeFloat(NewOfDoor.Value.Key));
				NewOfDoor.Key->SetParameter(TEXT("MSCZ"), FString::FromInt(NewOfDoor.Value.Value));
			}
			else
			{
				NewOfDoor.Key->ComponentLocation.LocationZ.Value = FString::SanitizeFloat(NewOfDoor.Value.Value);
				NewOfDoor.Key->ComponentLocation.LocationZ.Expression = FString::SanitizeFloat(NewOfDoor.Value.Value);
				double OldSize;
				const int32 HFFX = FCString::Atoi(*NewOfDoor.Key->GetParameterValue(TEXT("MHFFX")));
				if (HFFX == 0)
				{
					OldSize = FCString::Atod(*NewOfDoor.Key->GetParameterValue(TEXT("WJ")));
				}
				else
				{
					OldSize = FCString::Atod(*NewOfDoor.Key->GetParameterValue(TEXT("HJ")));
				}
				RecursionRefreshDoor(DoorContainer, NewOfDoor.Key, WJ, NewOfDoor.Value.Key, OldSize, NeedDeleteDoors);
			}
		}
	}
}

void UDSCupBoardDoorLibrary::UpdateDoorTouchedBoardsThickness(const TArray<UDSBaseModel*>& InBoards, FDSCustomDoorProperty& GenerateDoorData)
{
	if (InBoards.Num() != 4)
	{
		return;
	}
	GenerateDoorData.SCBJT = Cast<UDSCupboardModel>(InBoards[0])->GetModelInfo().ComponentTreeData->GetParameterValue(TEXT("HEIGHT"));
	GenerateDoorData.XCBJT = Cast<UDSCupboardModel>(InBoards[1])->GetModelInfo().ComponentTreeData->GetParameterValue(TEXT("HEIGHT"));
	GenerateDoorData.ZCBJT = Cast<UDSCupboardModel>(InBoards[2])->GetModelInfo().ComponentTreeData->GetParameterValue(TEXT("WIDTH"));
	GenerateDoorData.YCBJT = Cast<UDSCupboardModel>(InBoards[3])->GetModelInfo().ComponentTreeData->GetParameterValue(TEXT("WIDTH"));
}

void UDSCupBoardDoorLibrary::GetContainerCoverFromParams(const TSharedPtr<FMultiComponentDataItem>& Container, FDSCustomDoorProperty& GenerateDoorData)
{
	GenerateDoorData.MaskingModeUp = static_cast<EMaskingMode>(FCString::Atoi(*Container->GetParameterValue(TEXT("MRQSG"))));
	GenerateDoorData.MaskingModeDown = static_cast<EMaskingMode>(FCString::Atoi(*Container->GetParameterValue(TEXT("MRQXG"))));
	GenerateDoorData.MaskingModeLeft = static_cast<EMaskingMode>(FCString::Atoi(*Container->GetParameterValue(TEXT("MRQZG"))));
	GenerateDoorData.MaskingModeRight = static_cast<EMaskingMode>(FCString::Atoi(*Container->GetParameterValue(TEXT("MRQYG"))));
}

FTransform UDSCupBoardDoorLibrary::CalDoorWorldTransform(const UDSCupboardModel* InModel, bool bWithRotator)
{
	TSharedPtr<FMultiComponentDataItem> Item;
	for (auto& Element : InModel->GetModelInfo().ComponentTreeData->ChildComponent)
	{
		if (FCString::Atoi(*Element->ComponentVisibility.GetFormattedValue()) == 1
			&& UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(Element->ModelType) != EDSModelType::E_Custom_HingePlan
			&& UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(Element->ModelType) != EDSModelType::E_Custom_Knob
		)
		{
			Item = Element;
			break;
		}
	}

	FTransform WorldTransform;
	if (Item)
	{
		FTransform RelativeTransform = FTransform(bWithRotator ? Item->ComponentRotation.GetRotation() : FRotator::ZeroRotator, Item->ComponentLocation.GetLocation(), Item->ComponentScale.GetScale());
		FTransform RealTrans = InModel->GetPropertySharedPtr()->GetActualTransform();

		WorldTransform = RelativeTransform * RealTrans;
	}

	return WorldTransform;
}

TMap<TSharedPtr<FMultiComponentDataItem>, TPair<FTransform, TArray<FVector>>> UDSCupBoardDoorLibrary::GetHingesWorldTransform(UDSCupboardModel* InDoorModel)
{
	TMap<TSharedPtr<FMultiComponentDataItem>, TPair<FTransform, TArray<FVector>>> Hinges;
	FTransform DoorContainTransform;

	TArray<TSharedPtr<FMultiComponentDataItem>> NodePath;
	InDoorModel->GetRootCupboardModel()->CollectComponentPath_Public(InDoorModel->GetRootCupboardModel()->GetModelInfoRef().ComponentTreeData, InDoorModel->GetModelInfoRef().ComponentTreeData, NodePath);
	if (NodePath.IsValidIndex(1))
	{
		DoorContainTransform = FTransform(NodePath[1]->ComponentRotation.GetRotation(), NodePath[1]->ComponentLocation.GetLocation(), NodePath[1]->ComponentScale.GetScale());
	}

	FTransform DoorContainWorldTransform = DoorContainTransform * InDoorModel->GetRootCupboardModel()->GetPropertySharedPtr()->GetActualTransform();

	for (const auto& HingPlan : InDoorModel->GetModelInfoRef().ComponentTreeData->ChildComponent)
	{
		if (UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(HingPlan->ModelType) == EDSModelType::E_Custom_HingePlan)
		{
			for (const auto& Hinge : HingPlan->ChildComponent)
			{
				double Retraction = FCString::Atod(*HingPlan->GetParameterValue(TEXT("JLAQZ"))) * 0.1f;
				if (Hinge->ComponentVisibility.GetFormattedValue() == TEXT("1"))
				{
					//Y轴方向，要减去最大工艺内缩值
					FTransform RelativeTransform = FTransform(Hinge->ComponentRotation.GetRotation(), Hinge->ComponentLocation.GetLocation() - FVector(0, Retraction, 0), Hinge->ComponentScale.GetScale());
					FTransform WorldTransform = RelativeTransform * DoorContainWorldTransform;

					FVector Size = FVector::ZeroVector;
					FParameterData* W = Hinge->ComponentParameters.FindByPredicate([](const FParameterData& InData)
					{
						return InData.Data.name == TEXT("W");
					});
					if (W)
					{
						Size.X = FCString::Atod(*W->Data.GetRealValue()) / 10.0f;
					}
					FParameterData* D = Hinge->ComponentParameters.FindByPredicate([](const FParameterData& InData)
					{
						return InData.Data.name == TEXT("D");
					});
					if (D)
					{
						Size.Y = FCString::Atod(*D->Data.GetRealValue()) / 10.0f;
					}
					FParameterData* H = Hinge->ComponentParameters.FindByPredicate([](const FParameterData& InData)
					{
						return InData.Data.name == TEXT("H");
					});
					if (H)
					{
						Size.Z = FCString::Atod(*H->Data.GetRealValue()) / 10.0f;
					}

					TArray<FVector> ResultPoints;

					ResultPoints.Add(FVector(0, -Size.Y, Size.Z / 2));
					ResultPoints.Add(FVector(Size.X, -Size.Y, Size.Z / 2));
					ResultPoints.Add(FVector(Size.X, 0, Size.Z / 2));
					ResultPoints.Add(FVector(0, 0, Size.Z / 2));

					ResultPoints.Add(FVector(0, -Size.Y, -Size.Z / 2));
					ResultPoints.Add(FVector(Size.X, -Size.Y, -Size.Z / 2));
					ResultPoints.Add(FVector(Size.X, 0, -Size.Z / 2));
					ResultPoints.Add(FVector(0, 0, -Size.Z / 2));

					for (FVector& Point : ResultPoints)
					{
						Point = WorldTransform.TransformPosition(Point);
					}

					Hinges.Add(Hinge, {WorldTransform, ResultPoints});
				}
			}
			break;
		}
	}

	return Hinges;
}

int32 UDSCupBoardDoorLibrary::FindMostFrequentNumber(const TArray<int32>& InArray)
{
	if (InArray.Num() == 0)
	{
		return 0;
	}

	TMap<int32, int32> CountMap;
	for (int32 Number : InArray)
	{
		CountMap.FindOrAdd(Number)++;
	}

	int32 MostFrequentNumber = 0;
	int32 MaxCount = 0;

	for (const TPair<int32, int32>& Pair : CountMap)
	{
		if (Pair.Value > MaxCount)
		{
			MaxCount = Pair.Value;
			MostFrequentNumber = Pair.Key;
		}
	}

	return MostFrequentNumber;
}

bool UDSCupBoardDoorLibrary::IsPointInRectangle(const FVector& Point, const TArray<FVector>& RectVertices)
{
	if (RectVertices.Num() != 4)
	{
		return false;
	}

	const FVector V1 = RectVertices[1] - RectVertices[0];
	const FVector V2 = RectVertices[3] - RectVertices[0];

	const FVector V_P = Point - RectVertices[0];

	const float DotV1 = FVector::DotProduct(V_P, V1);
	const float DotV2 = FVector::DotProduct(V_P, V2);

	const float LengthV1Sq = V1.SizeSquared();
	const float LengthV2Sq = V2.SizeSquared();

	constexpr float Tolerance = 1e-4f;

	return (DotV1 >= -Tolerance && DotV1 <= LengthV1Sq + Tolerance) && (DotV2 >= -Tolerance && DotV2 <= LengthV2Sq + Tolerance);
}

bool UDSCupBoardDoorLibrary::IsLineIntersectingRectangle(const FVector& LineStart, const FVector& LineEnd, const TArray<FVector>& RectVertices)
{
	constexpr float IntersectionEpsilon = 1e-4f;

	if (RectVertices.Num() != 4)
	{
		return false;
	}

	if (IsPointInRectangle(LineStart, RectVertices) && IsPointInRectangle(LineEnd, RectVertices))
	{
		return true;
	}

	for (int32 i = 0; i < 4; ++i)
	{
		const FVector& RectEdgeStart = RectVertices[i];
		// 使用 (i + 1) % 4 来安全地获取下一个顶点，处理 i=3 时的情况 (3+1=4, 4%4=0)。
		const FVector& RectEdgeEnd = RectVertices[(i + 1) % 4];

		if (AreSegmentsIntersectingOrOverlapping(LineStart, LineEnd, RectEdgeStart, RectEdgeEnd, IntersectionEpsilon))
		{
			return true;
		}
	}

	return false;
}

bool UDSCupBoardDoorLibrary::AreSegmentsIntersectingOrOverlapping(const FVector& P1, const FVector& Q1, const FVector& P2, const FVector& Q2, float Epsilon)
{
	const FVector D1 = Q1 - P1; // 线段1的方向向量
	const FVector D2 = Q2 - P2; // 线段2的方向向量

	// 在XZ平面上计算2D叉积，用于判断方向和共线性
	const float CrossProduct = D1.X * D2.Y - D1.Y * D2.X;

	// 检查线段是否共线
	if (FMath::IsNearlyZero(CrossProduct, Epsilon))
	{
		const FVector P1P2 = P2 - P1;
		const float CrossProductParallel = P1P2.X * D1.Y - P1P2.Y * D1.X;

		if (!FMath::IsNearlyZero(CrossProductParallel, Epsilon))
		{
			return false; // 平行但不共线
		}

		const bool bOverlapX = FMath::Max(P1.X, Q1.X) >= FMath::Min(P2.X, Q2.X) && FMath::Max(P2.X, Q2.X) >= FMath::Min(P1.X, Q1.X);
		const bool bOverlapZ = FMath::Max(P1.Z, Q1.Z) >= FMath::Min(P2.Z, Q2.Z) && FMath::Max(P2.Z, Q2.Z) >= FMath::Min(P1.Z, Q1.Z);

		return bOverlapX && bOverlapZ;
	}

	const FVector P1P2 = P2 - P1;
	const float t = (P1P2.X * D2.Y - P1P2.Y * D2.X) / CrossProduct;
	const float u = (P1P2.X * D1.Y - P1P2.Y * D1.X) / CrossProduct;

	return (t >= -Epsilon && t <= 1.0f + Epsilon) && (u >= -Epsilon && u <= 1.0f + Epsilon);
}

bool UDSCupBoardDoorLibrary::ChangeCoverWhenGenerateHinge(UDSCupboardModel* DoorModel)
{
	TSharedPtr<FMultiComponentDataItem> CurrentDoorContainer;
	for (auto& Container : DoorModel->GetRootCupboardModel()->GetModelInfoRef().ComponentTreeData->ChildComponent)
	{
		if (UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(Container->ModelType) == EDSModelType::E_Custom_DoorContainer)
		{
			CurrentDoorContainer = RecursionFindContainOfDoor(Container, DoorModel);
		}
		if (CurrentDoorContainer.IsValid())
		{
			break;
		}
	}

	FTransform FinalTransform = DoorModel->GetRootCupboardModel()->GetPropertySharedPtr()->GetActualTransform();
	FTransform RelativeTransform = FTransform(CurrentDoorContainer->ComponentRotation.GetRotation(), CurrentDoorContainer->ComponentLocation.GetLocation(), CurrentDoorContainer->ComponentScale.GetScale());
	FTransform DoorWorldTransform = FinalTransform * RelativeTransform;

	FDSDoorDependencyInfo DependencyInfo = UDSModelDependencySubsystem::GetInstance()->FindDoorDependencyInfo(DoorModel->GetModelInfoRef().ComponentTreeData->UUID);

	TArray<UDSBaseModel*> Result;
	for (auto& CabinetId : DependencyInfo.DependentCabinets)
	{
		UDSCounterTopLibrary::GetSingleComponentModels(UDSMVCSubsystem::GetInstance()->GetModelByID(CabinetId), Result);
	}

	//可能生成后再发生旋转，为了最终只得到4个投影点，要保证投影面也门保持法线一致
	FVector RotatedNormal = FinalTransform.TransformVector(DependencyInfo.DoorContainerPlane.Normal);
	FVector TransformedPoint = FinalTransform.TransformPosition(DependencyInfo.DoorContainerPlane.Center);
	FPlane BasicPlane(TransformedPoint, RotatedNormal);

	double LocX = FCString::Atod(*DoorModel->GetModelInfoRef().ComponentTreeData->GetParameterValue(TEXT("MSCX")));
	double LocY = FCString::Atod(*DoorModel->GetModelInfoRef().ComponentTreeData->GetParameterValue(TEXT("MSCY")));
	double LocZ = FCString::Atod(*DoorModel->GetModelInfoRef().ComponentTreeData->GetParameterValue(TEXT("MSCZ")));
	FVector Loc = FVector(LocX, LocY, LocZ) * 0.1f;
	double Size = FCString::Atod(*DoorModel->GetModelInfoRef().ComponentTreeData->GetParameterValue(TEXT("MHFDX"))) * 0.1f;
	int32 HFFX = FCString::Atod(*DoorModel->GetModelInfoRef().ComponentTreeData->GetParameterValue(TEXT("MHFFX")));
	double WJ = FCString::Atod(*DoorModel->GetModelInfoRef().ComponentTreeData->GetParameterValue(TEXT("WJ"))) * 0.1f;
	double HJ = FCString::Atod(*DoorModel->GetModelInfoRef().ComponentTreeData->GetParameterValue(TEXT("HJ"))) * 0.1f;

	if (HFFX == 0)
	{
		WJ = Size;
	}
	else
	{
		HJ = Size;
	}

	const int32 OpenDir = FCString::Atoi(*DoorModel->GetModelInfoRef().ComponentTreeData->GetParameterValue(TEXT("KX")));
	const float SCBJT = FCString::Atoi(*DoorModel->GetModelInfoRef().ComponentTreeData->GetParameterValue(TEXT("SCBJT"))) * 0.1f;
	const float XCBJT = FCString::Atoi(*DoorModel->GetModelInfoRef().ComponentTreeData->GetParameterValue(TEXT("XCBJT"))) * 0.1f;
	const float ZCBJT = FCString::Atoi(*DoorModel->GetModelInfoRef().ComponentTreeData->GetParameterValue(TEXT("ZCBJT"))) * 0.1f;
	const float YCBJT = FCString::Atoi(*DoorModel->GetModelInfoRef().ComponentTreeData->GetParameterValue(TEXT("YCBJT"))) * 0.1f;

	//门的四个点
	TArray<FVector> PlanePoints;
	PlanePoints.Add(Loc + FVector(WJ, 0.0f, HJ));
	PlanePoints.Add(Loc + FVector(0, 0.0f, HJ));
	PlanePoints.Add(Loc);
	PlanePoints.Add(Loc + FVector(WJ, 0.0f, 0));

	TArray<FVector> TopLine = {PlanePoints[0] - FVector(YCBJT * 2, 0, 0), PlanePoints[1] + FVector(ZCBJT * 2, 0, 0)};
	TArray<FVector> DownLine = {PlanePoints[2] + FVector(ZCBJT * 2, 0, 0), PlanePoints[3] - FVector(ZCBJT * 2, 0, 0)};
	TArray<FVector> LeftLine = {PlanePoints[1] - FVector(0, 0, SCBJT * 2), PlanePoints[2] + FVector(0, 0, XCBJT * 2)};
	TArray<FVector> RightLine = {PlanePoints[3] + FVector(0, 0, XCBJT * 2), PlanePoints[0] - FVector(0, 0, SCBJT * 2)};

	FVector TopStart = DoorWorldTransform.TransformPosition(TopLine[0]);
	FVector TopEnd = DoorWorldTransform.TransformPosition(TopLine[1]);
	FVector DownStart = DoorWorldTransform.TransformPosition(DownLine[0]);
	FVector DownEnd = DoorWorldTransform.TransformPosition(DownLine[1]);
	FVector LeftStart = DoorWorldTransform.TransformPosition(LeftLine[0]);
	FVector LeftEnd = DoorWorldTransform.TransformPosition(LeftLine[1]);
	FVector RightStart = DoorWorldTransform.TransformPosition(RightLine[0]);
	FVector RightEnd = DoorWorldTransform.TransformPosition(RightLine[1]);

	TArray<FVector> UpProjectedLine = CalculateProjectedPoints3D(BasicPlane, {TopStart, TopEnd});
	TArray<FVector> DownProjectedLine = CalculateProjectedPoints3D(BasicPlane, {DownStart, DownEnd});
	TArray<FVector> LeftProjectedLine = CalculateProjectedPoints3D(BasicPlane, {LeftStart, LeftEnd});
	TArray<FVector> RightProjectedLine = CalculateProjectedPoints3D(BasicPlane, {RightStart, RightEnd});

	TArray<UDSCupboardModel*> UpBoards;
	TArray<UDSCupboardModel*> DownBoards;
	TArray<UDSCupboardModel*> LeftBoards;
	TArray<UDSCupboardModel*> RightBoards;

	TArray<FVector2D> DoorProjectedPoints = CalculateProjectedPoints(BasicPlane, DoorModel->GetModelFixedOrientedBoundingBox());

	for (auto& It : Result)
	{
		UDSCupboardModel* Board = Cast<UDSCupboardModel>(It);
		if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), Board->GetModelInfoRef().ComponentTreeData->ModelType))
		{
			continue;
		}
		TArray<FVector> BoardPoints = CalculateProjectedPoints3D(BasicPlane, Board->GetModelFixedOrientedBoundingBox());
		if (IsLineIntersectingRectangle(UpProjectedLine[0], UpProjectedLine[1], BoardPoints))
		{
			TArray<FVector2D> ShaftBoardPoints = CalculateProjectedPoints(BasicPlane, Board->GetModelFixedOrientedBoundingBox());
			TArray<TArray<FVector>> IntersectionPath = FClipper2Library::ComputeIntersectionContour(DoorProjectedPoints, ShaftBoardPoints);
			if (IntersectionPath.IsEmpty())
			{
				continue;
			}
			UpBoards.AddUnique(Board);
			continue;
		}
		if (IsLineIntersectingRectangle(DownProjectedLine[0], DownProjectedLine[1], BoardPoints))
		{
			TArray<FVector2D> ShaftBoardPoints = CalculateProjectedPoints(BasicPlane, Board->GetModelFixedOrientedBoundingBox());
			TArray<TArray<FVector>> IntersectionPath = FClipper2Library::ComputeIntersectionContour(DoorProjectedPoints, ShaftBoardPoints);
			if (IntersectionPath.IsEmpty())
			{
				continue;
			}
			DownBoards.AddUnique(Board);
			continue;
		}
		if (IsLineIntersectingRectangle(LeftProjectedLine[0], LeftProjectedLine[1], BoardPoints))
		{
			TArray<FVector2D> ShaftBoardPoints = CalculateProjectedPoints(BasicPlane, Board->GetModelFixedOrientedBoundingBox());
			TArray<TArray<FVector>> IntersectionPath = FClipper2Library::ComputeIntersectionContour(DoorProjectedPoints, ShaftBoardPoints);
			if (IntersectionPath.IsEmpty())
			{
				continue;
			}
			LeftBoards.AddUnique(Board);
			continue;
		}
		if (IsLineIntersectingRectangle(RightProjectedLine[0], RightProjectedLine[1], BoardPoints))
		{
			TArray<FVector2D> ShaftBoardPoints = CalculateProjectedPoints(BasicPlane, Board->GetModelFixedOrientedBoundingBox());
			TArray<TArray<FVector>> IntersectionPath = FClipper2Library::ComputeIntersectionContour(DoorProjectedPoints, ShaftBoardPoints);
			if (IntersectionPath.IsEmpty())
			{
				continue;
			}
			RightBoards.AddUnique(Board);
		}
	}

	int32 CurrentBoardThickness = 0;
	FString CurrentCover = TEXT("");
	TArray<UDSCupboardModel*> CurrentBoards;
	if (OpenDir == 0)
	{
		CurrentBoardThickness = ZCBJT;
		CurrentCover = TEXT("ZBFG");
		CurrentBoards = LeftBoards;
	}
	else if (OpenDir == 1)
	{
		CurrentBoardThickness = YCBJT;
		CurrentCover = TEXT("YBFG");
		CurrentBoards = RightBoards;
	}
	else if (OpenDir == 2)
	{
		CurrentBoardThickness = SCBJT;
		CurrentCover = TEXT("SBFG");
		CurrentBoards = UpBoards;
	}
	else if (OpenDir == 3)
	{
		CurrentBoardThickness = XCBJT;
		CurrentCover = TEXT("XBFG");
		CurrentBoards = DownBoards;
	}

	TArray<int32> StateArray;
	for (auto& ShaftBoard : CurrentBoards)
	{
		TArray<FVector2D> ShaftBoardPoints = CalculateProjectedPoints(BasicPlane, ShaftBoard->GetModelFixedOrientedBoundingBox());
		TArray<TArray<FVector>> IntersectionPath = FClipper2Library::ComputeIntersectionContour(DoorProjectedPoints, ShaftBoardPoints);
		if (IntersectionPath.IsEmpty())
		{
			//不盖或者内嵌，不用处理
			return true;
		}
		//根据交集的短边长度判断是否半盖或者全盖，<7.5为不合法
		double Distance1 = FVector::Distance(IntersectionPath[0][0], IntersectionPath[0][1]) * 10.0f;
		double Distance2 = FVector::Distance(IntersectionPath[0][1], IntersectionPath[0][2]) * 10.0f;
		double Distance = Distance1 < Distance2 ? Distance1 : Distance2;

		int32 State = 0;
		if (Distance > 0 && Distance < 7.5)
		{
			return false; //不合法
		}
		if (Distance >= 7.5 && Distance < CurrentBoardThickness * 10.0f / 2) //半盖
		{
			State = 1;
		}
		else //全盖
		{
			State = 0;
		}
		StateArray.Add(State);
	}

	DoorModel->GetModelInfoRef().ComponentTreeData->SetParameter(CurrentCover, FString::FromInt(FindMostFrequentNumber(StateArray)));

	return true;
}

TArray<FVector2D> UDSCupBoardDoorLibrary::CalculateProjectedPoints(const FPlane& BasicPlane, const TArray<FVector>& BoxVertices)
{
	TArray<FVector> ProjectedPoints;
	for (const FVector& Vertex : BoxVertices)
	{
		FVector ProjectedPoint = FVector::PointPlaneProject(Vertex, BasicPlane.GetOrigin(), BasicPlane.GetNormal());
		ProjectedPoints.Add(ProjectedPoint);
	}

	constexpr float Tolerance = 0.1f; // 容差值
	TArray<FVector> UniqueProjectedPoints;
	for (const FVector& ProjectedPoint : ProjectedPoints)
	{
		bool bIsUnique = true;
		for (const FVector& UniquePoint : UniqueProjectedPoints)
		{
			if (FVector::Dist(ProjectedPoint, UniquePoint) < Tolerance)
			{
				bIsUnique = false;
				break;
			}
		}

		if (bIsUnique)
		{
			UniqueProjectedPoints.Add(ProjectedPoint);
		}
	}

	TArray<FVector2D> Points2D;
	for (auto& It : UniqueProjectedPoints)
	{
		Points2D.Add(FVector2D(It.X, It.Z));
	}
	UDSCounterTopLibrary::ClockwiseSortPoints(Points2D);
	return Points2D;
}

TArray<FVector> UDSCupBoardDoorLibrary::CalculateProjectedPoints3D(const FPlane& BasicPlane, const TArray<FVector>& BoxVertices)
{
	TArray<FVector> ProjectedPoints;
	for (const FVector& Vertex : BoxVertices)
	{
		FVector ProjectedPoint = FVector::PointPlaneProject(Vertex, BasicPlane.GetOrigin(), BasicPlane.GetNormal());
		ProjectedPoints.Add(ProjectedPoint);
	}

	constexpr float Tolerance = 0.1f; // 容差值
	TArray<FVector> UniqueProjectedPoints;
	for (const FVector& ProjectedPoint : ProjectedPoints)
	{
		bool bIsUnique = true;
		for (const FVector& UniquePoint : UniqueProjectedPoints)
		{
			if (FVector::Dist(ProjectedPoint, UniquePoint) < Tolerance)
			{
				bIsUnique = false;
				break;
			}
		}

		if (bIsUnique)
		{
			UniqueProjectedPoints.Add(ProjectedPoint);
		}
	}

	TArray<FVector2D> Points2D;
	for (auto& It : UniqueProjectedPoints)
	{
		Points2D.Add(FVector2D(It.X, It.Z));
	}
	UDSCounterTopLibrary::ClockwiseSortPoints(Points2D);

	TArray<FVector> Points3D;
	for (auto& P : Points2D)
	{
		Points3D.Add(FVector(P.X, 0.0f, P.Y));
	}

	return Points3D;
}

bool UDSCupBoardDoorLibrary::GenerateDoorHinges()
{
	TArray<UDSCupboardModel*> NeedRefreshRootModel; //改成ParseTreeFromNode，DoorModel都要UpdateSelf
	TArray<UDSBaseModel*> AllDoorModels;
	TArray<UDSBaseModel*> AllCustomModels = UDSMVCSubsystem::GetInstance()->GetAllCustomModels();

	for (auto& Door : AllCustomModels)
	{
		if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), Cast<UDSCupboardModel>(Door)->GetModelInfoRef().ComponentTreeData->ModelType)
			|| Door->GetModelType() == EDSModelType::E_Custom_FunctionalDrawer)
		{
			AllDoorModels.Add(Door);
		}
	}

	for (auto& Element : AllDoorModels)
	{
		UDSCupboardModel* DoorModel = Cast<UDSCupboardModel>(Element);
		FParameterData* HingesInfo = DoorModel->GetModelInfoRef().ComponentTreeData->ComponentParameters.FindByPredicate([](const FParameterData& InData) { return InData.Data.name == TEXT("SCJL"); });
		if (!HingesInfo)
		{
			continue;
		}

		// 找门轴，修改门的实际盖值，门和轴的交集覆盖值小于一定条件值，认为不合法
		if (!ChangeCoverWhenGenerateHinge(DoorModel))
		{
			HingesInfo->Data.value = TEXT("0");
			HingesInfo->Data.expression = TEXT("0");
			NeedRefreshRootModel.AddUnique(DoorModel->GetRootCupboardModel());
			continue; //处理下一个门
		}

		HingesInfo->Data.value = TEXT("1");
		HingesInfo->Data.expression = TEXT("1");

		TSharedPtr<FMultiComponentDataItem> OriginalComponentData = MakeShared<FMultiComponentDataItem>();
		OriginalComponentData->DeepCopy(*DoorModel->GetModelInfoRef().ComponentTreeData);

		TMap<FString, EDSResourceType> FolderIdsForDownload;
		TArray<FDSCustomStyleReplacementNode> StylizedNodes;
		if (!UDSCupboardLibrary::ParseTreeFromNode(DoorModel->GetRootCupboardModel()->GetComponentTreeDataRef(), DoorModel->GetModelInfoRef().ComponentTreeData,
		                                           OriginalComponentData, DoorModel->GetParentComponentParams(),
		                                           FOnProcessParsedByOriginalNodeDelegate(),
		                                           DoorModel->GetComponentOverrideParamsRef(),
		                                           FolderIdsForDownload, StylizedNodes))
		{
			HingesInfo->Data.value = TEXT("0");
			HingesInfo->Data.expression = TEXT("0");
		}

		FDSDoorDependencyInfo DependencyInfo = UDSModelDependencySubsystem::GetInstance()->FindDoorDependencyInfo(DoorModel->GetModelInfoRef().ComponentTreeData->UUID);
		TMap<TSharedPtr<FMultiComponentDataItem>, TPair<FTransform, TArray<FVector>>> HingesMap = GetHingesWorldTransform(DoorModel);

		TArray<UDSBaseModel*> ResultBoard;
		//没有就是自带门，只是取所有板件的方式不同而已，其余都一样
		if (DependencyInfo.DependentBoards.IsEmpty())
		{
			ResultBoard = GetAllBoardsOfCabinetByDoor(DependencyInfo, true, DoorModel->GetRootCupboardModel());
		}
		else
		{
			ResultBoard = GetAllBoardsOfCabinetByDoor(DependencyInfo, false, nullptr);
		}

		bool bIsCanGenerateDoorHinges = false;
		for (auto& Hinge : HingesMap)
		{
			DrawDebugPoint(GWorld, Hinge.Value.Key.GetLocation(), 5.0f, FColor::Red, true);
			bIsCanGenerateDoorHinges = false;
			for (auto& Ele : ResultBoard)
			{
				UDSCupboardModel* VerBoard = Cast<UDSCupboardModel>(Ele);
				if (!VerBoard)
				{
					continue;
				}

				if (IsPointOnOBBSurface(Hinge.Value.Key.GetLocation(), VerBoard->GetPropertySharedPtr()->GetActualTransform(), GetObbExtent(VerBoard->GetModelFixedOrientedBoundingBox()), 0.01))
				{
					bIsCanGenerateDoorHinges = true;
					break;
				}
			}
			if (!bIsCanGenerateDoorHinges)
			{
				break;
			}
		}

		//相当于找不到轴，将生成的铰链还是继续生成，不避让了
		if (!bIsCanGenerateDoorHinges)
		{
			HingesInfo->Data.value = TEXT("0");
			HingesInfo->Data.expression = TEXT("0");
			NeedRefreshRootModel.AddUnique(DoorModel->GetRootCupboardModel());
			continue; //处理下一个门
		}

		//已经找到轴，检测是否与板有碰撞，无法生成
		for (auto& Hinge : HingesMap)
		{
			for (auto& HorBoard : ResultBoard)
			{
				UDSCupboardModel* HorBoardModel = Cast<UDSCupboardModel>(HorBoard);
				const FTransform OBB1_Transform = Hinge.Value.Key;
				const TArray<FVector> OBB1 = Hinge.Value.Value;
				const FTransform OBB2_Transform = HorBoardModel->GetPropertySharedPtr()->GetActualTransform();
				const TArray<FVector> OBB2 = HorBoardModel->GetModelFixedOrientedBoundingBox();

				if (AreOBBsIntersecting(OBB1_Transform, OBB1, OBB2_Transform, OBB2))
				{
					//避让一次铰链，直接改指针的值，随着门刷新而刷新，不再额外收集Map
					HingesAvoidOnceMore(DoorModel, OBB1, OBB2, Hinge.Key);
				}
			}
		}

		NeedRefreshRootModel.AddUnique(DoorModel->GetRootCupboardModel());
	}

	for (auto& RefreshRootModel : NeedRefreshRootModel)
	{
		RefreshRootModel->OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf);
	}

	return true;
}

bool UDSCupBoardDoorLibrary::CheckAllHingesIsValid()
{
	TArray<UDSBaseModel*> AllDoorModels;
	TArray<UDSBaseModel*> AllCustomModels = UDSMVCSubsystem::GetInstance()->GetAllCustomModels();

	for (auto& Door : AllCustomModels)
	{
		if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), Cast<UDSCupboardModel>(Door)->GetModelInfoRef().ComponentTreeData->ModelType)
			|| Door->GetModelType() == EDSModelType::E_Custom_FunctionalDrawer)
		{
			AllDoorModels.Add(Door);
		}
	}

	for (auto& Element : AllDoorModels)
	{
		UDSCupboardModel* DoorModel = Cast<UDSCupboardModel>(Element);
		FParameterData* HingesInfo = DoorModel->GetModelInfoRef().ComponentTreeData->ComponentParameters.FindByPredicate([](const FParameterData& InData) { return InData.Data.name == TEXT("SCJL"); });
		if (!HingesInfo)
		{
			UDSUISubsystem::GetInstance()->SetToastState(EToastState::EWarning, TEXT("门缺失铰链方案"), TEXT(""), true);
			return false;
		}

		//门没有生成铰链方案直接失败
		if (FCString::Atoi(*HingesInfo->Data.GetFormattedValue()) == 0)
		{
			FString Name = DoorModel->GetRootCupboardModel()->GetModelInfoRef().ComponentTreeData->ComponentName;
			UDSUISubsystem::GetInstance()->SetToastState(EToastState::EWarning, FString::Printf(TEXT("柜体：%s——铰链缺失门轴，无法生成"), *Name), TEXT(""), true);
			return false;
		}

		if (FCString::Atoi(*HingesInfo->Data.GetFormattedValue()) == 2)
		{
			FString Name = DoorModel->GetRootCupboardModel()->GetModelInfoRef().ComponentTreeData->ComponentName;
			UDSUISubsystem::GetInstance()->SetToastState(EToastState::EWarning, FString::Printf(TEXT("柜体：%s——铰链位置与板件冲突"), *Name), TEXT(""), true);
			return false;
		}

		FDSDoorDependencyInfo DependencyInfo = UDSModelDependencySubsystem::GetInstance()->FindDoorDependencyInfo(DoorModel->GetModelInfoRef().ComponentTreeData->UUID);

		TMap<TSharedPtr<FMultiComponentDataItem>, TPair<FTransform, TArray<FVector>>> HingesMap = GetHingesWorldTransform(DoorModel);

		TArray<UDSBaseModel*> ResultBoard;
		//没有就是自带门，只是取所有板件的方式不同而已，其余都一样
		if (DependencyInfo.DependentBoards.IsEmpty())
		{
			ResultBoard = GetAllBoardsOfCabinetByDoor(DependencyInfo, true, DoorModel->GetRootCupboardModel());
		}
		else
		{
			ResultBoard = GetAllBoardsOfCabinetByDoor(DependencyInfo, false, nullptr);
		}

		//轴不用检测，生成过程中没轴的都已经关闭铰链方案了，进入校验没有轴缺失的情况，只校验一次避让后，是否还有碰撞冲突
		for (auto& Hinge : HingesMap)
		{
			for (auto& HorBoard : ResultBoard)
			{
				UDSCupboardModel* HorBoardModel = Cast<UDSCupboardModel>(HorBoard);
				const FTransform OBB1_Transform = Hinge.Value.Key;
				const TArray<FVector> OBB1 = Hinge.Value.Value;
				const FTransform OBB2_Transform = HorBoardModel->GetPropertySharedPtr()->GetActualTransform();
				const TArray<FVector> OBB2 = HorBoardModel->GetModelFixedOrientedBoundingBox();

				if (AreOBBsIntersecting(OBB1_Transform, OBB1, OBB2_Transform, OBB2))
				{
					UDSUISubsystem::GetInstance()->SetToastState(EToastState::EWarning, TEXT("铰链与板件位置冲突"), TEXT(""), true);
					return false;
				}
			}
		}
	}

	return true;
}

void UDSCupBoardDoorLibrary::HingesAvoidOnceMore(UDSCupboardModel* DoorModel, const TArray<FVector>& OBB1, const TArray<FVector>& OBB2, const TSharedPtr<FMultiComponentDataItem>& Hinge)
{
	//OBB1是铰链，OBB2是板
	//上面已经判断过DependencyInfo，不再判断
	FDSDoorDependencyInfo DependencyInfo = UDSModelDependencySubsystem::GetInstance()->FindDoorDependencyInfo(DoorModel->GetModelInfoRef().ComponentTreeData->UUID);
	FVector UpNormal = ((DependencyInfo.DoorContainerPlane.Points[0] + DependencyInfo.DoorContainerPlane.Points[1]) / 2 - DependencyInfo.DoorContainerPlane.Center).GetSafeNormal();
	FVector RightNormal = ((DependencyInfo.DoorContainerPlane.Points[0] + DependencyInfo.DoorContainerPlane.Points[3]) / 2 - DependencyInfo.DoorContainerPlane.Center).GetSafeNormal();

	FParameterData* FindData = DoorModel->GetModelInfoRef().ComponentTreeData->ComponentParameters.FindByPredicate([](const FParameterData& Param)-> bool
	{
		return Param.Data.name.Equals(TEXT("KX"));
	});

	if (!FindData)
	{
		return;
	}

	FParameterData* bIsavoid = DoorModel->GetModelInfoRef().ComponentTreeData->ComponentParameters.FindByPredicate([](const FParameterData& InParam) -> bool
	{
		return InParam.Data.name.Equals(TEXT("ISAVOID"));
	});

	if (!bIsavoid)
	{
		return;
	}

	FParameterData* ForWardData = Hinge->ComponentParameters.FindByPredicate([](const FParameterData& Param)
	{
		return Param.Data.name.Equals(TEXT("JLBR"));
	});
	if (!ForWardData)
	{
		return;
	}

	//左开右开用UpNormal投影
	if (FCString::Atoi(*FindData->Data.GetFormattedValue()) == 0 || FCString::Atoi(*FindData->Data.GetFormattedValue()) == 1)
	{
		const float Projection1 = FVector::DotProduct(GetObbCenter(OBB1), UpNormal);
		const float Projection2 = FVector::DotProduct(GetObbCenter(OBB2), UpNormal);

		if (Projection1 <= Projection2)
		{
			ForWardData->Data.value = TEXT("2");
			ForWardData->Data.expression = TEXT("2");
			//门要标记为已发生过避让
			bIsavoid->Data.value = TEXT("1");
			bIsavoid->Data.expression = TEXT("1");
		}
		else
		{
			ForWardData->Data.value = TEXT("1");
			ForWardData->Data.expression = TEXT("1");
		}
	}
	else
	{
		const float Projection1 = FVector::DotProduct(GetObbCenter(OBB1), RightNormal);
		const float Projection2 = FVector::DotProduct(GetObbCenter(OBB2), RightNormal);

		if (Projection1 <= Projection2)
		{
			ForWardData->Data.value = TEXT("1");
			ForWardData->Data.expression = TEXT("1");
		}
		else
		{
			ForWardData->Data.value = TEXT("2");
			ForWardData->Data.expression = TEXT("2");
			//门要标记为已发生过避让
			bIsavoid->Data.value = TEXT("1");
			bIsavoid->Data.expression = TEXT("1");
		}
	}
}

void UDSCupBoardDoorLibrary::ProjectOBB(const TArray<FVector>& OBB, const FVector& Axis, float& Min, float& Max)
{
	Min = FLT_MAX;
	Max = -FLT_MAX;

	for (const FVector& Corner : OBB)
	{
		float Projection = FVector::DotProduct(Corner, Axis);
		Min = FMath::Min(Min, Projection);
		Max = FMath::Max(Max, Projection);
	}
}

bool UDSCupBoardDoorLibrary::Overlap1D(float Min1, float Max1, float Min2, float Max2, float Epsilon)
{
	// 允许 Min1 比 Max2 稍微大一点，或者 Min2 比 Max1 稍微大一点，仍然认为是重叠
	return (Max1 - Epsilon >= Min2 && Max2 - Epsilon >= Min1);
}

bool UDSCupBoardDoorLibrary::AreOBBsIntersecting(const FTransform& OBB1_Transform, const TArray<FVector>& OBB1, const FTransform& OBB2_Transform, const TArray<FVector>& OBB2)
{
	FVector OBB1_XAxis = OBB1_Transform.GetUnitAxis(EAxis::X);
	FVector OBB1_YAxis = OBB1_Transform.GetUnitAxis(EAxis::Y);
	FVector OBB1_ZAxis = OBB1_Transform.GetUnitAxis(EAxis::Z);

	FVector OBB2_XAxis = OBB2_Transform.GetUnitAxis(EAxis::X);
	FVector OBB2_YAxis = OBB2_Transform.GetUnitAxis(EAxis::Y);
	FVector OBB2_ZAxis = OBB2_Transform.GetUnitAxis(EAxis::Z);

	TArray<FVector> AxesToTest;
	AxesToTest.Add(OBB1_XAxis);
	AxesToTest.Add(OBB1_YAxis);
	AxesToTest.Add(OBB1_ZAxis);
	AxesToTest.Add(OBB2_XAxis);
	AxesToTest.Add(OBB2_YAxis);
	AxesToTest.Add(OBB2_ZAxis);

	AxesToTest.Add(FVector::CrossProduct(OBB1_XAxis, OBB2_XAxis).GetSafeNormal());
	AxesToTest.Add(FVector::CrossProduct(OBB1_XAxis, OBB2_YAxis).GetSafeNormal());
	AxesToTest.Add(FVector::CrossProduct(OBB1_XAxis, OBB2_ZAxis).GetSafeNormal());

	AxesToTest.Add(FVector::CrossProduct(OBB1_YAxis, OBB2_XAxis).GetSafeNormal());
	AxesToTest.Add(FVector::CrossProduct(OBB1_YAxis, OBB2_YAxis).GetSafeNormal());
	AxesToTest.Add(FVector::CrossProduct(OBB1_YAxis, OBB2_ZAxis).GetSafeNormal());

	AxesToTest.Add(FVector::CrossProduct(OBB1_ZAxis, OBB2_XAxis).GetSafeNormal());
	AxesToTest.Add(FVector::CrossProduct(OBB1_ZAxis, OBB2_YAxis).GetSafeNormal());
	AxesToTest.Add(FVector::CrossProduct(OBB1_ZAxis, OBB2_ZAxis).GetSafeNormal());

	for (const FVector& Axis : AxesToTest)
	{
		if (Axis.IsNearlyZero())
		{
			continue;
		}

		float Min1, Max1;
		ProjectOBB(OBB1, Axis, Min1, Max1);

		float Min2, Max2;
		ProjectOBB(OBB2, Axis, Min2, Max2);

		if (!Overlap1D(Min1, Max1, Min2, Max2, 0.1))
		{
			return false;
		}
	}

	return true;
}

bool UDSCupBoardDoorLibrary::IsPointOnOBBSurface(const FVector& PointToCheck, const FTransform& OBBTransform, const FVector& OBBExtent, const float Tolerance = KINDA_SMALL_NUMBER)
{
	const FVector LocalPoint = OBBTransform.InverseTransformPosition(PointToCheck);

	bool bIsInside = FMath::Abs(LocalPoint.X) <= (OBBExtent.X * 2 + Tolerance) && FMath::Abs(LocalPoint.Y) <= (OBBExtent.Y * 2 + Tolerance) && FMath::Abs(LocalPoint.Z) <= (OBBExtent.Z * 2 + Tolerance);

	if (!bIsInside)
	{
		return false;
	}

	bool bOnSurfaceX = FMath::IsNearlyEqual(FMath::Abs(LocalPoint.X), OBBExtent.X * 2, Tolerance);
	bool bOnSurfaceY = FMath::IsNearlyEqual(FMath::Abs(LocalPoint.Y), OBBExtent.Y * 2, Tolerance);
	bool bOnSurfaceZ = FMath::IsNearlyEqual(FMath::Abs(LocalPoint.Z), OBBExtent.Z * 2, Tolerance);

	return bOnSurfaceX || bOnSurfaceY || bOnSurfaceZ;
}

bool UDSCupBoardDoorLibrary::IsPointInOBBInside(const FVector& PointToCheck, const FTransform& OBBTransform, const FVector& OBBExtent, const float Tolerance = KINDA_SMALL_NUMBER)
{
	const FVector LocalPoint = OBBTransform.InverseTransformPosition(PointToCheck);

	const bool bIsInside = FMath::Abs(LocalPoint.X) < (OBBExtent.X * 2 + Tolerance) && FMath::Abs(LocalPoint.Y) < (OBBExtent.Y * 2 + Tolerance) && FMath::Abs(LocalPoint.Z) < (OBBExtent.Z * 2 + Tolerance);

	return bIsInside;
}

void UDSCupBoardDoorLibrary::FindDoorOfCabinetOfInit(UDSBaseModel* InModel)
{
	//不是柜子退出
	if (!UDSToolLibrary::IsCustomCabinetType(InModel->GetModelType()))
	{
		return;
	}

	const UDSCupboardModel* CabinetModel = Cast<UDSCupboardModel>(InModel);
	if (!CabinetModel)
	{
		return;
	}

	//没有门容器退出
	auto DoorContainer = CabinetModel->GetModelInfo().ComponentTreeData->ChildComponent.FindByPredicate([](const TSharedPtr<FMultiComponentDataItem>& InData)
	{
		return UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(InData->ModelType) == EDSModelType::E_Custom_DoorContainer;
	});

	if (!DoorContainer)
	{
		return;
	}

	TArray<UDSCupboardModel*> DoorModels;
	TArray<UDSCupboardModel*> OtherBoards;
	TArray<UDSBaseModel*> Result;
	TArray<FString> FoundDoorIDs;

	TArray<TSharedPtr<FMultiComponentDataItem>> DoorList;
	RecursionCollectDoors(*DoorContainer, DoorList);

	for (auto& Item : DoorList)
	{
		FoundDoorIDs.Add(Item->UUID);
	}

	UDSCounterTopLibrary::GetSingleComponentModels(InModel, Result);

	for (auto& It : Result)
	{
		UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(It);
		if (!CupboardModel)
		{
			continue;
		}

		if (FoundDoorIDs.Contains(CupboardModel->GetModelInfoRef().ComponentTreeData->UUID))
		{
			DoorModels.Add(CupboardModel);
			continue;
		}
		if (CupboardModel->GetModelType() == EDSModelType::E_Custom_Board ||
			CupboardModel->GetModelType() == EDSModelType::E_Custom_AdjustablePanel ||
			CupboardModel->GetModelType() == EDSModelType::E_Custom_TopClosurePanel ||
			CupboardModel->GetModelType() == EDSModelType::E_Custom_SideClosurePanel ||
			CupboardModel->GetModelType() == EDSModelType::E_Custom_CabinetBoard)
		{
			OtherBoards.Add(CupboardModel);
		}
	}

	//用门容器的净宽净高来找板
	double WJ = FCString::Atod(*DoorContainer->Get()->GetParameterValue(TEXT("WJ"))) * 0.1f;
	double HJ = FCString::Atod(*DoorContainer->Get()->GetParameterValue(TEXT("HJ"))) * 0.1f;
	TArray<FVector> PlanePoints;
	PlanePoints.Add(DoorContainer->Get()->ComponentLocation.GetLocation() + FVector(WJ, -10.0f, HJ));
	PlanePoints.Add(DoorContainer->Get()->ComponentLocation.GetLocation() + FVector(0, -10.0f, HJ));
	PlanePoints.Add(DoorContainer->Get()->ComponentLocation.GetLocation() + FVector(0, -10.0f, 0));
	PlanePoints.Add(DoorContainer->Get()->ComponentLocation.GetLocation() + FVector(WJ, -10.0f, 0));

	FTransform DoorContainWorldTransform = InModel->GetPropertySharedPtr()->GetActualTransform();

	FVector UpMiddlePoint = DoorContainWorldTransform.TransformPosition((PlanePoints[0] + PlanePoints[1]) / 2 + FVector(0, 0, -1));
	FVector BottomMiddlePoint = DoorContainWorldTransform.TransformPosition((PlanePoints[2] + PlanePoints[3]) / 2 + FVector(0, 0, 1));
	FVector LeftMiddlePoint = DoorContainWorldTransform.TransformPosition((PlanePoints[1] + PlanePoints[2]) / 2 + FVector(1, 0, 0));
	FVector RightMiddlePoint = DoorContainWorldTransform.TransformPosition((PlanePoints[0] + PlanePoints[3]) / 2 + FVector(-1, 0, 0));

	TArray<UDSBaseModel*> DependentBoards;
	DependentBoards.SetNum(4);

	for (auto& OtherBoard : OtherBoards)
	{
		auto Transform = OtherBoard->GetPropertySharedPtr()->GetActualTransform();
		auto Obb = OtherBoard->GetModelFixedOrientedBoundingBox();
		if (IsPointInOBBInside(UpMiddlePoint, Transform, GetObbExtent(Obb), 0.01))
		{
			DependentBoards[0] = OtherBoard;
		}
		else if (IsPointInOBBInside(BottomMiddlePoint, Transform, GetObbExtent(Obb), 0.01))
		{
			DependentBoards[1] = OtherBoard;
		}
		else if (IsPointInOBBInside(LeftMiddlePoint, Transform, GetObbExtent(Obb), 0.01))
		{
			DependentBoards[2] = OtherBoard;
		}
		else if (IsPointInOBBInside(RightMiddlePoint, Transform, GetObbExtent(Obb), 0.01))
		{
			DependentBoards[3] = OtherBoard;
		}
	}

	if (DependentBoards.Contains(nullptr))
	{
		return;
	}

	FDSDoorDependencyInfo DependencyInfo;
	//绑定依赖柜子
	DependencyInfo.DependentCabinets.Add(InModel->GetUUID());
	//绑定依赖板件
	for (auto& DependentModel : DependentBoards)
	{
		UDSCupboardModel* BoardModel = Cast<UDSCupboardModel>(DependentModel);
		if (BoardModel == nullptr)
		{
			continue;
		}

		UDSCupboardModel* RootCupboard = BoardModel->GetRootCupboardModel();
		if (RootCupboard == nullptr)
		{
			continue;
		}

		DependencyInfo.DependentBoards.Add({BoardModel->GetModelInfoRef().ComponentTreeData->UUID, RootCupboard->GetModelInfoRef().ComponentTreeData->UUID});
	}

	TArray<TArray<UDSBaseModel*>> DependenciesGroup;
	TArray<UDSBaseModel*> DependentCabinets;
	FDSSuitablePlane AreaPlane;
	bool bIsOnlyEmbedded = false;
	if (CalculateDoorArea(DependentBoards, AreaPlane, bIsOnlyEmbedded, DependenciesGroup, DependentCabinets))
	{
		DependencyInfo.DoorContainerPlane = AreaPlane;
	}

	for (auto& DoorUUID : FoundDoorIDs)
	{
		UDSModelDependencySubsystem::GetInstance()->AddDoorDependencyInfo(DoorUUID, DependencyInfo);
	}
}

TArray<UDSBaseModel*> UDSCupBoardDoorLibrary::GetAllBoardsOfCabinetByDoor(const FDSDoorDependencyInfo& DependencyInfo, bool bIsSelfDoor, UDSCupboardModel* InDoorModel)
{
	//找出所有子部件
	TArray<UDSBaseModel*> Result;
	if (bIsSelfDoor)
	{
		if (InDoorModel)
		{
			UDSCounterTopLibrary::GetSingleComponentModels(InDoorModel, Result);
		}
	}
	else
	{
		//找出门所有相关的柜子的所有板件
		TArray<UDSBaseModel*> DependentCabinets;
		for (auto& UUID : DependencyInfo.DependentCabinets)
		{
			DependentCabinets.Add(UDSMVCSubsystem::GetInstance()->GetModelByID(UUID));
		}

		for (auto& Every : DependentCabinets)
		{
			UDSCounterTopLibrary::GetSingleComponentModels(Every, Result);
		}
	}

	//找出所有板件
	TArray<UDSBaseModel*> ResultBoard;
	for (auto& Every : Result)
	{
		if (Every->GetModelType() == EDSModelType::E_Custom_Board
			|| Every->GetModelType() == EDSModelType::E_Custom_AdjustablePanel
			|| Every->GetModelType() == EDSModelType::E_Custom_TopClosurePanel
			|| Every->GetModelType() == EDSModelType::E_Custom_SideClosurePanel
			|| Every->GetModelType() == EDSModelType::E_Custom_CabinetBoard)
		{
			ResultBoard.Add(Every);
		}
	}

	return ResultBoard;
}

void UDSCupBoardDoorLibrary::OpenOrCloseDoor(UDSBaseModel* InModel, const TArray<UDSCupboardModel*>& AllDoorModels, const TArray<UDSCupboardModel*>& ClosedDoorModels)
{
	if (AllDoorModels.IsEmpty())
	{
		return;
	}

	if (ClosedDoorModels.IsEmpty())
	{
		for (auto& Every : AllDoorModels)
		{
			OpenOrCloseSingleDoor(Every);
		}
	}
	else
	{
		for (auto& Every : ClosedDoorModels)
		{
			OpenOrCloseSingleDoor(Every);
		}
	}

	if (Cast<UDSCupboardModel>(InModel) || Cast<UDSMultiModel>(InModel))
	{
		UDSUISubsystem::GetInstance()->GetFunctionBarBoxWidget()->SwitchButtons(UDSMVCSubsystem::GetInstance()->GetCurrentModel(), false);
	}
}

void UDSCupBoardDoorLibrary::OpenOrCloseSingleDoor(UDSCupboardModel* CupboardModel)
{
	if (DS_MODEL_VALID_FOR_USE(CupboardModel))
	{
		if (auto OwnerModel = CupboardModel->GetRootCupboardModel())
		{
			FDSRevokePushData PushData(EDSPushDataType::E_Custom, FDSModelExecuteType::ExecuteUpdateSelf, false);
			PushData.SetData(FDSCustomPushData(FDSModelExecuteType::ExecuteUpdateSelf, OwnerModel, nullptr));
			UDSRevokeSubsystem::GetInstance()->InsertCommandDataUnion(
				OwnerModel,
				FDSModelExecuteType::ExecuteUpdateSelf,
				PushData,
				UDSMVCSubsystem::GetInstance()->GetRevokeMark(),
				TEXT("")
			);
		}

		FParameterData* OpenData = CupboardModel->GetModelInfoRef().ComponentTreeData->ComponentParameters.FindByPredicate([](const FParameterData& InData)
		{
			return InData.Data.name == TEXT("DKJD");
		});
		if (!OpenData)
		{
			return;
		}

		if (FCString::Atoi(*OpenData->Data.GetFormattedValue()) == 0)
		{
			OpenData->Data.value = OpenData->Data.max_value;
			OpenData->Data.expression = OpenData->Data.max_value;
		}
		else
		{
			OpenData->Data.value = FString::FromInt(0);
			OpenData->Data.expression = FString::FromInt(0);
		}

		CupboardModel->OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf, FDSBroadcastMarkData::OnlyOutlineBroadcastMark);
	}
}

void UDSCupBoardDoorLibrary::UpdateDoorParams(UDSBaseModel* InCupboard)
{
	if (auto CupModel = Cast<UDSCupboardModel>(InCupboard))
	{
		auto Tree = CupModel->GetModelInfo().ComponentTreeData;
		auto KX = FCString::Atoi(*Tree->GetParameterValue(TEXT("KX")));
		auto MBLX = FCString::Atoi(*Tree->GetParameterValue(TEXT("MBLX")));
		if (MBLX == 0 && KX != 0 && KX != 1)
		{
			Tree->SetParameter(TEXT("KX"), TEXT("0"));
		}
		else if (MBLX == 1 && KX != 2 && KX != 3)
		{
			Tree->SetParameter(TEXT("KX"), TEXT("2"));
		}
	}
}

TArray<TSharedPtr<FMultiComponentDataItem>> UDSCupBoardDoorLibrary::RecursionCollectDoors(const TSharedPtr<FMultiComponentDataItem>& DoorContainer, TArray<TSharedPtr<FMultiComponentDataItem>>& DoorList)
{
	for (const auto& Item : DoorContainer->ChildComponent)
	{
		if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), Item->ModelType))
		{
			DoorList.Add(Item);
		}
		if (UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(Item->ModelType) == EDSModelType::E_Custom_DoorContainer)
		{
			RecursionCollectDoors(Item, DoorList);
		}
	}

	return DoorList;
}

TSharedPtr<FMultiComponentDataItem> UDSCupBoardDoorLibrary::RecursionFindContainOfDoor(const TSharedPtr<FMultiComponentDataItem>& Container, UDSCupboardModel* DoorModel)
{
	for (const auto& Item : Container->ChildComponent)
	{
		if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), Item->ModelType))
		{
			if (DoorModel->GetModelInfoRef().ComponentTreeData->UUID == Item->UUID)
			{
				return Container;
			}
		}
		if (UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(Item->ModelType) == EDSModelType::E_Custom_DoorContainer)
		{
			RecursionFindContainOfDoor(Item, DoorModel);
		}
	}

	return nullptr;
}

float UDSCupBoardDoorLibrary::GetSplitValue(const TSharedPtr<FMultiComponentDataItem>& Item, const FString& ParameterName)
{
	float Result = 0.0f;
	if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), Item->ModelType))
	{
		Result = FCString::Atod(*Item->GetParameterValue(TEXT("MHFDX")));
	}
	if (UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(Item->ModelType) == EDSModelType::E_Custom_DoorContainer)
	{
		Result = FCString::Atod(*Item->GetParameterValue(ParameterName));
	}
	return Result;
}

TArray<FString> UDSCupBoardDoorLibrary::GetDoorCoverBoards(const TSharedPtr<FMultiComponentDataItem>& DoorNode)
{
	TArray<FString> OutBoards;
	if (DoorNode == nullptr)
	{
		return OutBoards;
	}
	auto AllFunctionalBoards = UDSMVCSubsystem::GetInstance()->GetModels(EDSModelType::E_Custom_Board);

	auto DoorModel = UDSCupboardLibrary::GetModelByNodeUUID(DoorNode->UUID);
	if (DoorModel == nullptr)
	{
		return OutBoards;
	}
	//计算包围盒碰撞
	auto DoorObb = DoorModel->GetModelOrientedBoundingBoxWithoutRotator();

	for (auto& Iter : AllFunctionalBoards)
	{
		auto BoardModel = Cast<UDSCupboardModel>(Iter);
		if (BoardModel == nullptr)
		{
			continue;
		}

		auto TreeData = BoardModel->GetModelInfoRef().ComponentTreeData;
		int32 RetractionValue = FCString::Atoi(*TreeData->GetParameterValue(TEXT("NSZT"))); //TODO:确认板件内嵌内缩参数

		auto BoardObb = BoardModel->GetModelOrientedBoundingBox();
		auto BoardTransform = BoardModel->GetPropertySharedPtr()->GetActualTransform();
		//如果需要检测覆盖板件，则需要向正向偏移box以便检测
		auto Forward = BoardTransform.GetUnitAxis(EAxis::Y);
		double Offset = 29.0; //偏移量

		BoardObb[0] += Forward * Offset;
		BoardObb[1] += Forward * Offset;
		BoardObb[2] += Forward * Offset;
		BoardObb[3] += Forward * Offset;
		BoardObb[4] += Forward * Offset;
		BoardObb[5] += Forward * Offset;
		BoardObb[6] += Forward * Offset;
		BoardObb[7] += Forward * Offset;

		if (UDSCupboardLibrary::CupboardIntersect(DoorObb, BoardObb))
		{
			OutBoards.Add(Cast<UDSCupboardModel>(BoardModel)->GetModelInfo().ComponentTreeData->UUID);
		}
	}

	auto DependInfo = UDSModelDependencySubsystem::GetInstance()->FindDoorDependencyInfo(DoorNode->UUID);
	TSet<FString> DependentBoards;
	for (auto& G : DependInfo.DependentGroupBoards)
	{
		for (auto& I : G)
		{
			OutBoards.Remove(I.Key);
		}
	}

	return OutBoards;
}

void UDSCupBoardDoorLibrary::UpdateFunctionalBoardsByDoor(const TArray<TSharedPtr<FMultiComponentDataItem>>& DoorNodes, bool bRetraction)
{
	if (DoorNodes.IsEmpty())
	{
		return;
	}

	auto AllModels = UDSMVCSubsystem::GetInstance()->GetAllModels();
	TArray<UDSBaseModel*> OtherDoors;
	TArray<UDSBaseModel*> AllFunctionalBoards;

	for (auto& Iter : AllModels)
	{
		if (Iter->GetModelType() == EDSModelType::E_Custom_Board)
		{
			AllFunctionalBoards.Add(Iter);
		}
		else if (UDSToolLibrary::IsCustomDoorType(Iter->GetModelType()) && !bRetraction)
		{
			//在解除内缩的时候，需要判断是否有其他们影响
			auto DoorModel = Cast<UDSCupboardModel>(Iter);
			if (DoorModel == nullptr)
			{
				continue;
			}
			bool bDoorContain = DoorNodes.FindByPredicate([&](const TSharedPtr<FMultiComponentDataItem>& InDoorNode)
			{
				return InDoorNode->UUID == DoorModel->GetModelInfoRef().ComponentTreeData->UUID;
			}) != nullptr;

			if (!bDoorContain)
			{
				OtherDoors.Add(Iter);
			}
		}
	}

	TSet<FString> FunctionalBoardUUIDs;
	for (auto& DoorNode : DoorNodes)
	{
		FunctionalBoardUUIDs.Append(GetDoorCoverBoards(DoorNode));
	}

	TSet<UDSBaseModel*> FunctionalBoards;
	for (auto& UUID : FunctionalBoardUUIDs)
	{
		bool bOtherCover = false;
		for (auto OtherDoor : OtherDoors)
		{
			//auto OtherDependencyInfo = UDSModelDependencySubsystem::GetInstance()->FindDoorDependencyInfo(Cast<UDSCupboardModel>(OtherDoor)->GetModelInfoRef().ComponentTreeData->UUID);
			auto OtherCoverBoards = GetDoorCoverBoards(Cast<UDSCupboardModel>(OtherDoor)->GetModelInfo().ComponentTreeData);
			if (OtherCoverBoards.Contains(UUID))
			{
				bOtherCover = true;
				break;
			}
		}
		if (!bOtherCover)
		{
			auto Board = UDSCupboardLibrary::GetModelByNodeUUID(UUID);
			if (Board != nullptr)
			{
				FunctionalBoards.Add(Board);
			}
		}
	}

	if (FunctionalBoards.IsEmpty())
	{
		return;
	}

	for (auto& Board : FunctionalBoards)
	{
		auto FunctionalBoard = Cast<UDSCupboardModel>(Board);
		if (FunctionalBoard != nullptr)
		{
			FParameterData* ParameterData = FunctionalBoard->GetModelInfoRef().ComponentTreeData->ComponentParameters.FindByPredicate([](const FParameterData& InData)
			{
				return InData.Data.name == TEXT("NSZT");
			});
			if (ParameterData)
			{
				FString RetractionValue = bRetraction ? TEXT("1") : TEXT("0");
				ParameterData->Data.value = RetractionValue;
				ParameterData->Data.expression = RetractionValue;
				Board->OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf, FDSBroadcastMarkData::OnlyOutlineBroadcastMark);
			}
		}
	}
}

void UDSCupBoardDoorLibrary::ChangeCoverByProperty(UDSCupboardModel* DoorModel, const FString& InParamName, const FString& InOldValue, const FString& InNewValue)
{
	float Offset = (FCString::Atoi(*InOldValue) - FCString::Atoi(*InNewValue)) * 0.5f;

	TSharedPtr<FMultiComponentDataItem> RootModelInfo = DoorModel->GetRootCupboardModel()->GetModelInfoRef().ComponentTreeData;
	TSharedPtr<FMultiComponentDataItem> CurrentModelInfo = DoorModel->GetModelInfoRef().ComponentTreeData;

	TMap<TSharedPtr<FMultiComponentDataItem>, TSharedPtr<FMultiComponentDataItem>> PropertyMap; //ChildNode,ParentNode

	TFunction<void(const TSharedPtr<FMultiComponentDataItem>&, const bool&)> RecurseFindDoor = [&](const TSharedPtr<FMultiComponentDataItem>& Root, const bool& bIsRoot)
	{
		for (const TSharedPtr<FMultiComponentDataItem>& Component : Root->ChildComponent)
		{
			if (!Component.IsValid())
			{
				continue;
			}

			if (UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(Component->ModelType) == EDSModelType::E_Custom_DoorContainer)
			{
				if (bIsRoot)
				{
					PropertyMap.Add(Component, nullptr);
					RecurseFindDoor(Component, false);
				}
				else
				{
					PropertyMap.Add(Component, Root);
					RecurseFindDoor(Component, false);
				}
			}
			if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), Component->ModelType))
			{
				PropertyMap.Add(Component, Root);
			}
		}
	};

	RecurseFindDoor(RootModelInfo, true);

	TSharedPtr<FMultiComponentDataItem> CurRootContainer = nullptr;
	TFunction<void(const TSharedPtr<FMultiComponentDataItem>&)> FindSelfRootContainer = [&](const TSharedPtr<FMultiComponentDataItem>& Component)
	{
		if (PropertyMap.FindRef(Component).IsValid())
		{
			FindSelfRootContainer(PropertyMap.FindRef(Component));
		}
		else
		{
			CurRootContainer = Component;
		}
	};

	FindSelfRootContainer(CurrentModelInfo);

	if (PropertyMap.FindRef(CurrentModelInfo).IsValid())
	{
		TSharedPtr<FMultiComponentDataItem> CurContainerOfDoorModel = PropertyMap.FindRef(CurrentModelInfo);

		TFunction<void(const TSharedPtr<FMultiComponentDataItem>&)> SameLevelContainer = [&](const TSharedPtr<FMultiComponentDataItem>& Component)
		{
			int32 CurHHFX = FCString::Atoi(*Component->GetParameterValue(TEXT("MHFFX")));
			if (CurHHFX == 0)
			{
				if (InParamName == TEXT("SBFG") || InParamName == TEXT("XBFG"))
				{
					for (auto& IM : Component->ChildComponent)
					{
						if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), IM->ModelType))
						{
							IM->SetParameter(InParamName, InNewValue);
						}
						else
						{
							SameLevelContainer(IM);
						}
					}
				}
				if (InParamName == TEXT("ZBFG"))
				{
					for (auto& IM : Component->ChildComponent)
					{
						/*if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), IM->ModelType))
						{
							if (FCString::Atoi(*IM->GetParameterValue(TEXT("MBPXH"))) == 0)
							{
								IM->SetParameter(InParamName, InNewValue);
							}
						}
						else*/
						{
							SameLevelContainer(IM);
						}
					}
				}
				if (InParamName == TEXT("YBFG"))
				{
					for (auto& IM : Component->ChildComponent)
					{
						if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), IM->ModelType))
						{
							if (FCString::Atoi(*IM->GetParameterValue(TEXT("MBPXH"))) == 99)
							{
								IM->SetParameter(InParamName, InNewValue);
							}
						}
						else
						{
							SameLevelContainer(IM);
						}
					}
				}
			}
			else
			{
				if (InParamName == TEXT("ZBFG") || InParamName == TEXT("YBFG"))
				{
					for (auto& IM : Component->ChildComponent)
					{
						if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), IM->ModelType))
						{
							IM->SetParameter(InParamName, InNewValue);
						}
						else
						{
							SameLevelContainer(IM);
						}
					}
				}
				if (InParamName == TEXT("SBFG"))
				{
					for (auto& IM : Component->ChildComponent)
					{
						/*if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), IM->ModelType))
						{
							if (FCString::Atoi(*IM->GetParameterValue(TEXT("MBPXH"))) == 0)
							{
								IM->SetParameter(InParamName, InNewValue);
							}
						}
						else*/
						{
							SameLevelContainer(IM);
						}
					}
				}
				if (InParamName == TEXT("XBFG"))
				{
					for (auto& IM : Component->ChildComponent)
					{
						if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), IM->ModelType))
						{
							if (FCString::Atoi(*IM->GetParameterValue(TEXT("MBPXH"))) == 99)
							{
								IM->SetParameter(InParamName, InNewValue);
							}
						}
						else
						{
							SameLevelContainer(IM);
						}
					}
				}
			}
		};

		SameLevelContainer(CurContainerOfDoorModel);
	}

	const int32 RootSCBJT = FCString::Atoi(*CurRootContainer->GetParameterValue(TEXT("SCBJT")));
	const int32 RootXCBJT = FCString::Atoi(*CurRootContainer->GetParameterValue(TEXT("XCBJT")));
	const int32 RootZCBJT = FCString::Atoi(*CurRootContainer->GetParameterValue(TEXT("ZCBJT")));
	const int32 RootYCBJT = FCString::Atoi(*CurRootContainer->GetParameterValue(TEXT("YCBJT")));

	TSharedPtr<FMultiComponentDataItem> ParentItem = PropertyMap.FindRef(CurrentModelInfo);
	TMap<TSharedPtr<FMultiComponentDataItem>, TSharedPtr<FMultiComponentDataItem>> NeedDeleteDoors;
	RecursionPushCoverToUpLevel(ParentItem, CurrentModelInfo, RootSCBJT, RootXCBJT, RootZCBJT, RootYCBJT, Offset, InNewValue, InParamName, PropertyMap, NeedDeleteDoors);

	if (!NeedDeleteDoors.IsEmpty())
	{
		UDSUISubsystem::GetInstance()->SetToastState(EToastState::EWarning, TEXT("超出门的极值，已删除门"));
		for (auto& Item : NeedDeleteDoors)
		{
			Item.Key->ChildComponent.Remove(Item.Value);
		}
	}

	//切换门板盖值，可能会删除，也可能不删除，要根据删除结果来判断，并重置选中状态
	bool bIsDelete = false;
	FString OldUUid = DoorModel->GetModelInfoRef().ComponentTreeData->UUID;

	for (auto& Item : NeedDeleteDoors)
	{
		if (Item.Key->UUID == OldUUid)
		{
			bIsDelete = true;
			break;
		}
	}

	DoorModel->GetRootCupboardModel()->OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf, FDSBroadcastMarkData::OnlyOutlineBroadcastMark);

	if (bIsDelete)
	{
		UDSMVCSubsystem::GetInstance()->SetCurrentModel(nullptr);
		UDSUISubsystem::GetInstance()->ProcessStateEvent(nullptr, EUIOperationType::Selected, UDSMVCSubsystem::GetInstance()->GetStateType());
	}
	else
	{
		TArray<UDSCupboardModel*> DoorModels;
		TArray<UDSBaseModel*> Result;
		UDSCounterTopLibrary::GetSingleComponentModels(DoorModel->GetRootCupboardModel(), Result);

		for (auto& It : Result)
		{
			UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(It);
			if (!CupboardModel)
			{
				continue;
			}

			if (OldUUid == CupboardModel->GetModelInfoRef().ComponentTreeData->UUID)
			{
				UDSMVCSubsystem::GetInstance()->SetCurrentModel(CupboardModel);
				UDSUISubsystem::GetInstance()->ProcessStateEvent(CupboardModel, EUIOperationType::Selected, UDSMVCSubsystem::GetInstance()->GetStateType());
				break;
			}
		}
	}
}

void UDSCupBoardDoorLibrary::RecursionPushCoverToUpLevel(const TSharedPtr<FMultiComponentDataItem>& ParentItem,
                                                         const TSharedPtr<FMultiComponentDataItem>& CurrentModelInfo,
                                                         const int32 RootSCBJT,
                                                         const int32 RootXCBJT,
                                                         const int32 RootZCBJT,
                                                         const int32 RootYCBJT,
                                                         const float Offset,
                                                         const FString& InNewValue,
                                                         const FString& InParamName,
                                                         const TMap<TSharedPtr<FMultiComponentDataItem>, TSharedPtr<FMultiComponentDataItem>>& PropertyMap,
                                                         TMap<TSharedPtr<FMultiComponentDataItem>, TSharedPtr<FMultiComponentDataItem>>& NeedDeleteDoors)
{
	if (ParentItem.IsValid())
	{
		const int32 HFFX = FCString::Atoi(*ParentItem->GetParameterValue(TEXT("MHFFX")));
		if (HFFX == 0)
		{
			const int32 SeqNo = FCString::Atoi(*CurrentModelInfo->GetParameterValue(TEXT("MBPXH")));
			if (SeqNo == 0)
			{
				if (InParamName == TEXT("YBFG"))
				{
					if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), CurrentModelInfo->ModelType))
					{
						int32 OldSize = FCString::Atoi(*CurrentModelInfo->GetParameterValue(TEXT("MHFDX")));

						int32 MaxValue = 0;
						int32 MinValue = 0;
						if (FParameterData* WidthParam = CurrentModelInfo->ComponentParameters.FindByPredicate([](const FParameterData& InParam) { return InParam.Data.name == TEXT("W"); }))
						{
							MaxValue = FCString::Atoi(*WidthParam->Data.max_value);
							MinValue = FCString::Atoi(*WidthParam->Data.min_value);
						}

						if (OldSize + Offset * RootYCBJT < MinValue || OldSize + Offset * RootYCBJT > MaxValue)
						{
							NeedDeleteDoors.Add(ParentItem, CurrentModelInfo);
						}
						CurrentModelInfo->SetParameter(TEXT("MHFDX"), FString::FromInt(OldSize + Offset * RootYCBJT));
					}
					if (UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(CurrentModelInfo->ModelType) == EDSModelType::E_Custom_DoorContainer)
					{
						auto WJ = FCString::Atof(*CurrentModelInfo->GetParameterValue(TEXT("WJ")));
						auto HJ = FCString::Atof(*CurrentModelInfo->GetParameterValue(TEXT("HJ")));
						auto FX = FCString::Atoi(*CurrentModelInfo->GetParameterValue(TEXT("MHFFX")));
						float OldSize = FX == 0 ? WJ : HJ;
						RecursionRefreshDoor(nullptr, CurrentModelInfo, WJ + Offset * RootYCBJT, HJ, OldSize, NeedDeleteDoors);
					}

					auto NextNode = ParentItem->ChildComponent.FindByPredicate([&](const TSharedPtr<FMultiComponentDataItem>& InItem)
					{
						if (SeqNo + 2 == ParentItem->ChildComponent.Num())
						{
							return FCString::Atoi(*InItem->GetParameterValue(TEXT("MBPXH"))) == 99;
						}
						return FCString::Atoi(*InItem->GetParameterValue(TEXT("MBPXH"))) == SeqNo + 1;
					});

					if (NextNode)
					{
						if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), NextNode->Get()->ModelType))
						{
							int32 NextOldSize = FCString::Atoi(*NextNode->Get()->GetParameterValue(TEXT("MHFDX")));

							int32 MaxValue = 0;
							int32 MinValue = 0;
							if (FParameterData* WidthParam = NextNode->Get()->ComponentParameters.FindByPredicate([](const FParameterData& InParam) { return InParam.Data.name == TEXT("W"); }))
							{
								MaxValue = FCString::Atoi(*WidthParam->Data.max_value);
								MinValue = FCString::Atoi(*WidthParam->Data.min_value);
							}

							if (NextOldSize - Offset * RootYCBJT < MinValue || NextOldSize - Offset * RootYCBJT > MaxValue)
							{
								NeedDeleteDoors.Add(ParentItem, *NextNode);
							}

							NextNode->Get()->SetParameter(TEXT("MHFDX"), FString::FromInt(NextOldSize - Offset * RootYCBJT));
							NextNode->Get()->SetParameter(TEXT("MSCX"), FString::FromInt(FCString::Atoi(*NextNode->Get()->GetParameterValue(TEXT("MSCX"))) + Offset * RootYCBJT));
						}
						if (UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(NextNode->Get()->ModelType) == EDSModelType::E_Custom_DoorContainer)
						{
							FVector NextPos = NextNode->Get()->ComponentLocation.GetLocation() + FVector(Offset * RootYCBJT * 0.1f, 0.0f, 0.0f);
							NextNode->Get()->ComponentLocation.SetLocation(NextPos * 10.0f);
							auto WJ = FCString::Atof(*NextNode->Get()->GetParameterValue(TEXT("WJ")));
							auto HJ = FCString::Atof(*NextNode->Get()->GetParameterValue(TEXT("HJ")));
							auto FX = FCString::Atoi(*NextNode->Get()->GetParameterValue(TEXT("MHFFX")));
							float OldSize = FX == 0 ? WJ : HJ;
							RecursionRefreshDoor(nullptr, *NextNode, WJ - Offset * RootYCBJT, HJ, OldSize, NeedDeleteDoors);
						}
					}
				}
				else
				{
					RecursionPushCoverToUpLevel(PropertyMap.FindRef(ParentItem), ParentItem, RootSCBJT, RootXCBJT, RootZCBJT, RootYCBJT, Offset, InNewValue, InParamName, PropertyMap, NeedDeleteDoors);
				}
			}
			else if (SeqNo == 99)
			{
				if (InParamName == TEXT("ZBFG"))
				{
					if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), CurrentModelInfo->ModelType))
					{
						int32 OldSize = FCString::Atoi(*CurrentModelInfo->GetParameterValue(TEXT("MHFDX")));

						int32 MaxValue = 0;
						int32 MinValue = 0;
						if (FParameterData* WidthParam = CurrentModelInfo->ComponentParameters.FindByPredicate([](const FParameterData& InParam) { return InParam.Data.name == TEXT("W"); }))
						{
							MaxValue = FCString::Atoi(*WidthParam->Data.max_value);
							MinValue = FCString::Atoi(*WidthParam->Data.min_value);
						}

						if (OldSize + Offset * RootZCBJT < MinValue || OldSize + Offset * RootZCBJT > MaxValue)
						{
							NeedDeleteDoors.Add(ParentItem, CurrentModelInfo);
						}

						CurrentModelInfo->SetParameter(TEXT("MHFDX"), FString::FromInt(OldSize + Offset * RootZCBJT));
						CurrentModelInfo->SetParameter(TEXT("MSCX"), FString::FromInt(FCString::Atoi(*CurrentModelInfo->GetParameterValue(TEXT("MSCX"))) - Offset * RootZCBJT));
					}
					if (UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(CurrentModelInfo->ModelType) == EDSModelType::E_Custom_DoorContainer)
					{
						auto WJ = FCString::Atof(*CurrentModelInfo->GetParameterValue(TEXT("WJ")));
						auto HJ = FCString::Atof(*CurrentModelInfo->GetParameterValue(TEXT("HJ")));
						CurrentModelInfo->ComponentLocation.SetLocation((CurrentModelInfo->ComponentLocation.GetLocation() - FVector(Offset * RootZCBJT * 0.1f, 0.0f, 0.0f)) * 10.0f);
						auto FX = FCString::Atoi(*CurrentModelInfo->GetParameterValue(TEXT("MHFFX")));
						float OldSize = FX == 0 ? WJ : HJ;
						RecursionRefreshDoor(nullptr, CurrentModelInfo, WJ + Offset * RootZCBJT, HJ, OldSize, NeedDeleteDoors);
					}

					auto PreNode = ParentItem->ChildComponent.FindByPredicate([&](const TSharedPtr<FMultiComponentDataItem>& InItem)
					{
						if (ParentItem->ChildComponent.Num() - 2 == 0)
						{
							return FCString::Atoi(*InItem->GetParameterValue(TEXT("MBPXH"))) == 0;
						}
						return FCString::Atoi(*InItem->GetParameterValue(TEXT("MBPXH"))) == ParentItem->ChildComponent.Num() - 2;
					});
					if (PreNode)
					{
						if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), PreNode->Get()->ModelType))
						{
							int32 PreOldSize = FCString::Atoi(*PreNode->Get()->GetParameterValue(TEXT("MHFDX")));

							int32 MaxValue = 0;
							int32 MinValue = 0;
							if (FParameterData* WidthParam = PreNode->Get()->ComponentParameters.FindByPredicate([](const FParameterData& InParam) { return InParam.Data.name == TEXT("W"); }))
							{
								MaxValue = FCString::Atoi(*WidthParam->Data.max_value);
								MinValue = FCString::Atoi(*WidthParam->Data.min_value);
							}

							if (PreOldSize - Offset * RootZCBJT < MinValue || PreOldSize - Offset * RootZCBJT > MaxValue)
							{
								NeedDeleteDoors.Add(ParentItem, *PreNode);
							}

							PreNode->Get()->SetParameter(TEXT("MHFDX"), FString::FromInt(PreOldSize - Offset * RootZCBJT));
						}
						if (UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(PreNode->Get()->ModelType) == EDSModelType::E_Custom_DoorContainer)
						{
							auto WJ = FCString::Atof(*PreNode->Get()->GetParameterValue(TEXT("WJ")));
							auto HJ = FCString::Atof(*PreNode->Get()->GetParameterValue(TEXT("HJ")));
							auto FX = FCString::Atoi(*PreNode->Get()->GetParameterValue(TEXT("MHFFX")));
							float OldSize = FX == 0 ? WJ : HJ;
							RecursionRefreshDoor(nullptr, *PreNode, WJ - Offset * RootZCBJT, HJ, OldSize, NeedDeleteDoors);
						}
					}
				}
				else
				{
					RecursionPushCoverToUpLevel(PropertyMap.FindRef(ParentItem), ParentItem, RootSCBJT, RootXCBJT, RootZCBJT, RootYCBJT, Offset, InNewValue, InParamName, PropertyMap, NeedDeleteDoors);
				}
			}
			else
			{
				if (InParamName == TEXT("ZBFG"))
				{
					if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), CurrentModelInfo->ModelType))
					{
						int32 OldSize = FCString::Atoi(*CurrentModelInfo->GetParameterValue(TEXT("MHFDX")));

						int32 MaxValue = 0;
						int32 MinValue = 0;
						if (FParameterData* WidthParam = CurrentModelInfo->ComponentParameters.FindByPredicate([](const FParameterData& InParam) { return InParam.Data.name == TEXT("W"); }))
						{
							MaxValue = FCString::Atoi(*WidthParam->Data.max_value);
							MinValue = FCString::Atoi(*WidthParam->Data.min_value);
						}

						if (OldSize + Offset * RootZCBJT < MinValue || OldSize + Offset * RootZCBJT > MaxValue)
						{
							NeedDeleteDoors.Add(ParentItem, CurrentModelInfo);
						}

						CurrentModelInfo->SetParameter(TEXT("MHFDX"), FString::FromInt(OldSize + Offset * RootZCBJT));
						CurrentModelInfo->SetParameter(TEXT("MSCX"), FString::FromInt(FCString::Atoi(*CurrentModelInfo->GetParameterValue(TEXT("MSCX"))) - Offset * RootZCBJT));
					}
					if (UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(CurrentModelInfo->ModelType) == EDSModelType::E_Custom_DoorContainer)
					{
						auto WJ = FCString::Atof(*CurrentModelInfo->GetParameterValue(TEXT("WJ")));
						auto HJ = FCString::Atof(*CurrentModelInfo->GetParameterValue(TEXT("HJ")));
						CurrentModelInfo->ComponentLocation.SetLocation((CurrentModelInfo->ComponentLocation.GetLocation() - FVector(Offset * RootZCBJT * 0.1f, 0.0f, 0.0f)) * 10.0f);
						auto FX = FCString::Atoi(*CurrentModelInfo->GetParameterValue(TEXT("MHFFX")));
						float OldSize = FX == 0 ? WJ : HJ;
						RecursionRefreshDoor(nullptr, CurrentModelInfo, WJ + Offset * RootZCBJT, HJ, OldSize, NeedDeleteDoors);
					}

					auto PreNode = ParentItem->ChildComponent.FindByPredicate([&](const TSharedPtr<FMultiComponentDataItem>& InItem)
					{
						if (SeqNo - 1 == 0)
						{
							return FCString::Atoi(*InItem->GetParameterValue(TEXT("MBPXH"))) == 0;
						}
						return FCString::Atoi(*InItem->GetParameterValue(TEXT("MBPXH"))) == ParentItem->ChildComponent.Num() - SeqNo - 1;
					});
					if (PreNode)
					{
						if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), PreNode->Get()->ModelType))
						{
							int32 PreOldSize = FCString::Atoi(*PreNode->Get()->GetParameterValue(TEXT("MHFDX")));

							int32 MaxValue = 0;
							int32 MinValue = 0;
							if (FParameterData* WidthParam = PreNode->Get()->ComponentParameters.FindByPredicate([](const FParameterData& InParam) { return InParam.Data.name == TEXT("W"); }))
							{
								MaxValue = FCString::Atoi(*WidthParam->Data.max_value);
								MinValue = FCString::Atoi(*WidthParam->Data.min_value);
							}

							if (PreOldSize - Offset * RootZCBJT < MinValue || PreOldSize - Offset * RootZCBJT > MaxValue)
							{
								NeedDeleteDoors.Add(ParentItem, *PreNode);
							}

							PreNode->Get()->SetParameter(TEXT("MHFDX"), FString::FromInt(PreOldSize - Offset * RootZCBJT));
						}
						if (UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(PreNode->Get()->ModelType) == EDSModelType::E_Custom_DoorContainer)
						{
							auto WJ = FCString::Atof(*PreNode->Get()->GetParameterValue(TEXT("WJ")));
							auto HJ = FCString::Atof(*PreNode->Get()->GetParameterValue(TEXT("HJ")));
							auto FX = FCString::Atoi(*PreNode->Get()->GetParameterValue(TEXT("MHFFX")));
							float OldSize = FX == 0 ? WJ : HJ;
							RecursionRefreshDoor(nullptr, *PreNode, WJ - Offset * RootZCBJT, HJ, OldSize, NeedDeleteDoors);
						}
					}
				}
				else if (InParamName == TEXT("YBFG"))
				{
					if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), CurrentModelInfo->ModelType))
					{
						int32 OldSize = FCString::Atoi(*CurrentModelInfo->GetParameterValue(TEXT("MHFDX")));

						int32 MaxValue = 0;
						int32 MinValue = 0;
						if (FParameterData* WidthParam = CurrentModelInfo->ComponentParameters.FindByPredicate([](const FParameterData& InParam) { return InParam.Data.name == TEXT("W"); }))
						{
							MaxValue = FCString::Atoi(*WidthParam->Data.max_value);
							MinValue = FCString::Atoi(*WidthParam->Data.min_value);
						}

						if (OldSize + Offset * RootYCBJT < MinValue || OldSize + Offset * RootYCBJT > MaxValue)
						{
							NeedDeleteDoors.Add(ParentItem, CurrentModelInfo);
						}

						CurrentModelInfo->SetParameter(TEXT("MHFDX"), FString::FromInt(OldSize + Offset * RootYCBJT));
					}
					if (UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(CurrentModelInfo->ModelType) == EDSModelType::E_Custom_DoorContainer)
					{
						auto WJ = FCString::Atof(*CurrentModelInfo->GetParameterValue(TEXT("WJ")));
						auto HJ = FCString::Atof(*CurrentModelInfo->GetParameterValue(TEXT("HJ")));
						auto FX = FCString::Atoi(*CurrentModelInfo->GetParameterValue(TEXT("MHFFX")));
						float OldSize = FX == 0 ? WJ : HJ;
						RecursionRefreshDoor(nullptr, CurrentModelInfo, WJ + Offset * RootYCBJT, HJ, OldSize, NeedDeleteDoors);
					}

					auto NextNode = ParentItem->ChildComponent.FindByPredicate([&](const TSharedPtr<FMultiComponentDataItem>& InItem)
					{
						if (SeqNo + 2 == ParentItem->ChildComponent.Num())
						{
							return FCString::Atoi(*InItem->GetParameterValue(TEXT("MBPXH"))) == 99;
						}
						return FCString::Atoi(*InItem->GetParameterValue(TEXT("MBPXH"))) == SeqNo + 1;
					});

					if (NextNode)
					{
						if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), NextNode->Get()->ModelType))
						{
							int32 NextOldSize = FCString::Atoi(*NextNode->Get()->GetParameterValue(TEXT("MHFDX")));

							int32 MaxValue = 0;
							int32 MinValue = 0;
							if (FParameterData* WidthParam = NextNode->Get()->ComponentParameters.FindByPredicate([](const FParameterData& InParam) { return InParam.Data.name == TEXT("W"); }))
							{
								MaxValue = FCString::Atoi(*WidthParam->Data.max_value);
								MinValue = FCString::Atoi(*WidthParam->Data.min_value);
							}

							if (NextOldSize - Offset * RootYCBJT < MinValue || NextOldSize - Offset * RootYCBJT > MaxValue)
							{
								NeedDeleteDoors.Add(ParentItem, *NextNode);
							}

							NextNode->Get()->SetParameter(TEXT("MHFDX"), FString::FromInt(NextOldSize - Offset * RootYCBJT));
							NextNode->Get()->SetParameter(TEXT("MSCX"), FString::FromInt(FCString::Atoi(*NextNode->Get()->GetParameterValue(TEXT("MSCX"))) + Offset * RootYCBJT));
						}
						if (UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(NextNode->Get()->ModelType) == EDSModelType::E_Custom_DoorContainer)
						{
							FVector NextPos = NextNode->Get()->ComponentLocation.GetLocation() + FVector(Offset * RootYCBJT * 0.1f, 0.0f, 0.0f);
							NextNode->Get()->ComponentLocation.SetLocation(NextPos * 10.0f);
							auto WJ = FCString::Atof(*NextNode->Get()->GetParameterValue(TEXT("WJ")));
							auto HJ = FCString::Atof(*NextNode->Get()->GetParameterValue(TEXT("HJ")));
							auto FX = FCString::Atoi(*NextNode->Get()->GetParameterValue(TEXT("MHFFX")));
							float OldSize = FX == 0 ? WJ : HJ;
							RecursionRefreshDoor(nullptr, *NextNode, WJ - Offset * RootYCBJT, HJ, OldSize, NeedDeleteDoors);
						}
					}
				}
				else
				{
					RecursionPushCoverToUpLevel(PropertyMap.FindRef(ParentItem), ParentItem, RootSCBJT, RootXCBJT, RootZCBJT, RootYCBJT, Offset, InNewValue, InParamName, PropertyMap, NeedDeleteDoors);
				}
			}
		}
		else
		{
			const int32 SeqNo = FCString::Atoi(*CurrentModelInfo->GetParameterValue(TEXT("MBPXH")));
			if (SeqNo == 0)
			{
				if (InParamName == TEXT("XBFG"))
				{
					if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), CurrentModelInfo->ModelType))
					{
						int32 OldSize = FCString::Atoi(*CurrentModelInfo->GetParameterValue(TEXT("MHFDX")));

						int32 MaxValue = 0;
						int32 MinValue = 0;
						if (FParameterData* WidthParam = CurrentModelInfo->ComponentParameters.FindByPredicate([](const FParameterData& InParam) { return InParam.Data.name == TEXT("H"); }))
						{
							MaxValue = FCString::Atoi(*WidthParam->Data.max_value);
							MinValue = FCString::Atoi(*WidthParam->Data.min_value);
						}

						if (OldSize + Offset * RootXCBJT < MinValue || OldSize + Offset * RootXCBJT > MaxValue)
						{
							NeedDeleteDoors.Add(ParentItem, CurrentModelInfo);
						}

						CurrentModelInfo->SetParameter(TEXT("MHFDX"), FString::FromInt(OldSize + Offset * RootXCBJT));
						CurrentModelInfo->SetParameter(TEXT("MSCZ"), FString::FromInt(FCString::Atoi(*CurrentModelInfo->GetParameterValue(TEXT("MSCZ"))) - Offset * RootXCBJT));
					}

					if (UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(CurrentModelInfo->ModelType) == EDSModelType::E_Custom_DoorContainer)
					{
						auto WJ = FCString::Atof(*CurrentModelInfo->GetParameterValue(TEXT("WJ")));
						auto HJ = FCString::Atof(*CurrentModelInfo->GetParameterValue(TEXT("HJ")));
						CurrentModelInfo->ComponentLocation.SetLocation((CurrentModelInfo->ComponentLocation.GetLocation() - FVector(0.0f, 0.0f, Offset * RootXCBJT * 0.1f)) * 10.0f);
						auto FX = FCString::Atoi(*CurrentModelInfo->GetParameterValue(TEXT("MHFFX")));
						float OldSize = FX == 0 ? WJ : HJ;
						RecursionRefreshDoor(nullptr, CurrentModelInfo, WJ, HJ + Offset * RootXCBJT, OldSize, NeedDeleteDoors);
					}

					auto NextNode = ParentItem->ChildComponent.FindByPredicate([&](const TSharedPtr<FMultiComponentDataItem>& InItem)
					{
						if (SeqNo + 2 == ParentItem->ChildComponent.Num())
						{
							return FCString::Atoi(*InItem->GetParameterValue(TEXT("MBPXH"))) == 99;
						}

						return FCString::Atoi(*InItem->GetParameterValue(TEXT("MBPXH"))) == SeqNo + 1;
					});
					if (NextNode)
					{
						if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), NextNode->Get()->ModelType))
						{
							int32 NextOldSize = FCString::Atoi(*NextNode->Get()->GetParameterValue(TEXT("MHFDX")));

							int32 MaxValue = 0;
							int32 MinValue = 0;
							if (FParameterData* WidthParam = NextNode->Get()->ComponentParameters.FindByPredicate([](const FParameterData& InParam) { return InParam.Data.name == TEXT("H"); }))
							{
								MaxValue = FCString::Atoi(*WidthParam->Data.max_value);
								MinValue = FCString::Atoi(*WidthParam->Data.min_value);
							}

							if (NextOldSize - Offset * RootXCBJT < MinValue || NextOldSize - Offset * RootXCBJT > MaxValue)
							{
								NeedDeleteDoors.Add(ParentItem, *NextNode);
							}

							NextNode->Get()->SetParameter(TEXT("MHFDX"), FString::FromInt(NextOldSize - Offset * RootXCBJT));
						}
						if (UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(NextNode->Get()->ModelType) == EDSModelType::E_Custom_DoorContainer)
						{
							auto WJ = FCString::Atof(*NextNode->Get()->GetParameterValue(TEXT("WJ")));
							auto HJ = FCString::Atof(*NextNode->Get()->GetParameterValue(TEXT("HJ")));
							auto FX = FCString::Atoi(*NextNode->Get()->GetParameterValue(TEXT("MHFFX")));
							float OldSize = FX == 0 ? WJ : HJ;
							RecursionRefreshDoor(nullptr, *NextNode, WJ, HJ - Offset * RootXCBJT, OldSize, NeedDeleteDoors);
						}
					}
				}
				else
				{
					RecursionPushCoverToUpLevel(PropertyMap.FindRef(ParentItem), ParentItem, RootSCBJT, RootXCBJT, RootZCBJT, RootYCBJT, Offset, InNewValue, InParamName, PropertyMap, NeedDeleteDoors);
				}
			}
			else if (SeqNo == 99)
			{
				if (InParamName == TEXT("SBFG"))
				{
					if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), CurrentModelInfo->ModelType))
					{
						int32 OldSize = FCString::Atoi(*CurrentModelInfo->GetParameterValue(TEXT("MHFDX")));

						int32 MaxValue = 0;
						int32 MinValue = 0;
						if (FParameterData* WidthParam = CurrentModelInfo->ComponentParameters.FindByPredicate([](const FParameterData& InParam) { return InParam.Data.name == TEXT("H"); }))
						{
							MaxValue = FCString::Atoi(*WidthParam->Data.max_value);
							MinValue = FCString::Atoi(*WidthParam->Data.min_value);
						}

						if (OldSize + Offset * RootSCBJT < MinValue || OldSize + Offset * RootSCBJT > MaxValue)
						{
							NeedDeleteDoors.Add(ParentItem, CurrentModelInfo);
						}

						CurrentModelInfo->SetParameter(TEXT("MHFDX"), FString::FromInt(OldSize + Offset * RootSCBJT));
					}

					if (UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(CurrentModelInfo->ModelType) == EDSModelType::E_Custom_DoorContainer)
					{
						auto WJ = FCString::Atof(*CurrentModelInfo->GetParameterValue(TEXT("WJ")));
						auto HJ = FCString::Atof(*CurrentModelInfo->GetParameterValue(TEXT("HJ")));
						auto FX = FCString::Atoi(*CurrentModelInfo->GetParameterValue(TEXT("MHFFX")));
						float OldSize = FX == 0 ? WJ : HJ;
						RecursionRefreshDoor(nullptr, CurrentModelInfo, WJ, HJ + Offset * RootSCBJT, OldSize, NeedDeleteDoors);
					}

					auto PreNode = ParentItem->ChildComponent.FindByPredicate([&](const TSharedPtr<FMultiComponentDataItem>& InItem)
					{
						if (ParentItem->ChildComponent.Num() - 2 == 0)
						{
							return FCString::Atoi(*InItem->GetParameterValue(TEXT("MBPXH"))) == 0;
						}
						return FCString::Atoi(*InItem->GetParameterValue(TEXT("MBPXH"))) == ParentItem->ChildComponent.Num() - 2;
					});
					if (PreNode)
					{
						if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), PreNode->Get()->ModelType))
						{
							int32 PreOldSize = FCString::Atoi(*PreNode->Get()->GetParameterValue(TEXT("MHFDX")));

							int32 MaxValue = 0;
							int32 MinValue = 0;
							if (FParameterData* WidthParam = PreNode->Get()->ComponentParameters.FindByPredicate([](const FParameterData& InParam) { return InParam.Data.name == TEXT("H"); }))
							{
								MaxValue = FCString::Atoi(*WidthParam->Data.max_value);
								MinValue = FCString::Atoi(*WidthParam->Data.min_value);
							}

							if (PreOldSize - Offset * RootZCBJT < MinValue || PreOldSize - Offset * RootZCBJT > MaxValue)
							{
								NeedDeleteDoors.Add(ParentItem, *PreNode);
							}

							PreNode->Get()->SetParameter(TEXT("MHFDX"), FString::FromInt(PreOldSize - Offset * RootZCBJT));
							PreNode->Get()->SetParameter(TEXT("MSCZ"), FString::FromInt(FCString::Atoi(*PreNode->Get()->GetParameterValue(TEXT("MSCZ"))) + Offset * RootSCBJT));
						}
						if (UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(PreNode->Get()->ModelType) == EDSModelType::E_Custom_DoorContainer)
						{
							FVector NeWPos = PreNode->Get()->ComponentLocation.GetLocation() + FVector(0.0f, 0.0f, Offset * RootSCBJT * 0.1f);
							PreNode->Get()->ComponentLocation.SetLocation(NeWPos * 10.0f);
							auto WJ = FCString::Atof(*PreNode->Get()->GetParameterValue(TEXT("WJ")));
							auto HJ = FCString::Atof(*PreNode->Get()->GetParameterValue(TEXT("HJ")));
							auto FX = FCString::Atoi(*PreNode->Get()->GetParameterValue(TEXT("MHFFX")));
							float OldSize = FX == 0 ? WJ : HJ;
							RecursionRefreshDoor(nullptr, *PreNode, WJ, HJ - Offset * RootSCBJT, OldSize, NeedDeleteDoors);
						}
					}
				}
				else
				{
					RecursionPushCoverToUpLevel(PropertyMap.FindRef(ParentItem), ParentItem, RootSCBJT, RootXCBJT, RootZCBJT, RootYCBJT, Offset, InNewValue, InParamName, PropertyMap, NeedDeleteDoors);
				}
			}
			else
			{
				if (InParamName == TEXT("SBFG"))
				{
					if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), CurrentModelInfo->ModelType))
					{
						int32 OldSize = FCString::Atoi(*CurrentModelInfo->GetParameterValue(TEXT("MHFDX")));

						int32 MaxValue = 0;
						int32 MinValue = 0;
						if (FParameterData* WidthParam = CurrentModelInfo->ComponentParameters.FindByPredicate([](const FParameterData& InParam) { return InParam.Data.name == TEXT("H"); }))
						{
							MaxValue = FCString::Atoi(*WidthParam->Data.max_value);
							MinValue = FCString::Atoi(*WidthParam->Data.min_value);
						}

						if (OldSize + Offset * RootSCBJT < MinValue || OldSize + Offset * RootSCBJT > MaxValue)
						{
							NeedDeleteDoors.Add(ParentItem, CurrentModelInfo);
						}

						CurrentModelInfo->SetParameter(TEXT("MHFDX"), FString::FromInt(OldSize + Offset * RootSCBJT));
					}

					if (UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(CurrentModelInfo->ModelType) == EDSModelType::E_Custom_DoorContainer)
					{
						auto WJ = FCString::Atof(*CurrentModelInfo->GetParameterValue(TEXT("WJ")));
						auto HJ = FCString::Atof(*CurrentModelInfo->GetParameterValue(TEXT("HJ")));
						auto FX = FCString::Atoi(*CurrentModelInfo->GetParameterValue(TEXT("MHFFX")));
						float OldSize = FX == 0 ? WJ : HJ;
						RecursionRefreshDoor(nullptr, CurrentModelInfo, WJ, HJ + Offset * RootSCBJT, OldSize, NeedDeleteDoors);
					}

					auto PreNode = ParentItem->ChildComponent.FindByPredicate([&](const TSharedPtr<FMultiComponentDataItem>& InItem)
					{
						if (SeqNo - 1 == 0)
						{
							return FCString::Atoi(*InItem->GetParameterValue(TEXT("MBPXH"))) == 0;
						}
						return FCString::Atoi(*InItem->GetParameterValue(TEXT("MBPXH"))) == ParentItem->ChildComponent.Num() - SeqNo - 1;
					});

					if (PreNode)
					{
						if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), PreNode->Get()->ModelType))
						{
							int32 PreOldSize = FCString::Atoi(*PreNode->Get()->GetParameterValue(TEXT("MHFDX")));

							int32 MaxValue = 0;
							int32 MinValue = 0;
							if (FParameterData* WidthParam = PreNode->Get()->ComponentParameters.FindByPredicate([](const FParameterData& InParam) { return InParam.Data.name == TEXT("H"); }))
							{
								MaxValue = FCString::Atoi(*WidthParam->Data.max_value);
								MinValue = FCString::Atoi(*WidthParam->Data.min_value);
							}

							if (PreOldSize - Offset * RootZCBJT < MinValue || PreOldSize - Offset * RootZCBJT > MaxValue)
							{
								NeedDeleteDoors.Add(ParentItem, *PreNode);
							}

							PreNode->Get()->SetParameter(TEXT("MHFDX"), FString::FromInt(PreOldSize - Offset * RootZCBJT));
							PreNode->Get()->SetParameter(TEXT("MSCZ"), FString::FromInt(FCString::Atoi(*PreNode->Get()->GetParameterValue(TEXT("MSCZ"))) + Offset * RootSCBJT));
						}
						if (UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(PreNode->Get()->ModelType) == EDSModelType::E_Custom_DoorContainer)
						{
							FVector NeWPos = PreNode->Get()->ComponentLocation.GetLocation() + FVector(0.0f, 0.0f, Offset * RootSCBJT * 0.1f);
							PreNode->Get()->ComponentLocation.SetLocation(NeWPos * 10.0f);
							auto WJ = FCString::Atof(*PreNode->Get()->GetParameterValue(TEXT("WJ")));
							auto HJ = FCString::Atof(*PreNode->Get()->GetParameterValue(TEXT("HJ")));
							auto FX = FCString::Atoi(*PreNode->Get()->GetParameterValue(TEXT("MHFFX")));
							float OldSize = FX == 0 ? WJ : HJ;
							RecursionRefreshDoor(nullptr, *PreNode, WJ, HJ - Offset * RootSCBJT, OldSize, NeedDeleteDoors);
						}
					}
				}
				else if (InParamName == TEXT("XBFG"))
				{
					if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), CurrentModelInfo->ModelType))
					{
						int32 OldSize = FCString::Atoi(*CurrentModelInfo->GetParameterValue(TEXT("MHFDX")));

						int32 MaxValue = 0;
						int32 MinValue = 0;
						if (FParameterData* WidthParam = CurrentModelInfo->ComponentParameters.FindByPredicate([](const FParameterData& InParam) { return InParam.Data.name == TEXT("H"); }))
						{
							MaxValue = FCString::Atoi(*WidthParam->Data.max_value);
							MinValue = FCString::Atoi(*WidthParam->Data.min_value);
						}

						if (OldSize + Offset * RootXCBJT < MinValue || OldSize + Offset * RootXCBJT > MaxValue)
						{
							NeedDeleteDoors.Add(ParentItem, CurrentModelInfo);
						}

						CurrentModelInfo->SetParameter(TEXT("MHFDX"), FString::FromInt(OldSize + Offset * RootXCBJT));
						CurrentModelInfo->SetParameter(TEXT("MSCZ"), FString::FromInt(FCString::Atoi(*CurrentModelInfo->GetParameterValue(TEXT("MSCZ"))) - Offset * RootXCBJT));
					}

					if (UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(CurrentModelInfo->ModelType) == EDSModelType::E_Custom_DoorContainer)
					{
						auto WJ = FCString::Atof(*CurrentModelInfo->GetParameterValue(TEXT("WJ")));
						auto HJ = FCString::Atof(*CurrentModelInfo->GetParameterValue(TEXT("HJ")));
						CurrentModelInfo->ComponentLocation.SetLocation((CurrentModelInfo->ComponentLocation.GetLocation() - FVector(0.0f, 0.0f, Offset * RootXCBJT * 0.1f)) * 10.0f);
						auto FX = FCString::Atoi(*CurrentModelInfo->GetParameterValue(TEXT("MHFFX")));
						float OldSize = FX == 0 ? WJ : HJ;
						RecursionRefreshDoor(nullptr, CurrentModelInfo, WJ, HJ + Offset * RootXCBJT, OldSize, NeedDeleteDoors);
					}

					auto NextNode = ParentItem->ChildComponent.FindByPredicate([&](const TSharedPtr<FMultiComponentDataItem>& InItem)
					{
						if (SeqNo + 2 == ParentItem->ChildComponent.Num())
						{
							return FCString::Atoi(*InItem->GetParameterValue(TEXT("MBPXH"))) == 99;
						}
						return FCString::Atoi(*InItem->GetParameterValue(TEXT("MBPXH"))) == SeqNo + 1;
					});

					if (NextNode)
					{
						if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), NextNode->Get()->ModelType))
						{
							int32 NextOldSize = FCString::Atoi(*NextNode->Get()->GetParameterValue(TEXT("MHFDX")));

							int32 MaxValue = 0;
							int32 MinValue = 0;
							if (FParameterData* WidthParam = NextNode->Get()->ComponentParameters.FindByPredicate([](const FParameterData& InParam) { return InParam.Data.name == TEXT("H"); }))
							{
								MaxValue = FCString::Atoi(*WidthParam->Data.max_value);
								MinValue = FCString::Atoi(*WidthParam->Data.min_value);
							}

							if (NextOldSize - Offset * RootXCBJT < MinValue || NextOldSize - Offset * RootXCBJT > MaxValue)
							{
								NeedDeleteDoors.Add(ParentItem, *NextNode);
							}

							NextNode->Get()->SetParameter(TEXT("MHFDX"), FString::FromInt(NextOldSize - Offset * RootXCBJT));
						}
						if (UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(NextNode->Get()->ModelType) == EDSModelType::E_Custom_DoorContainer)
						{
							auto WJ = FCString::Atof(*NextNode->Get()->GetParameterValue(TEXT("WJ")));
							auto HJ = FCString::Atof(*NextNode->Get()->GetParameterValue(TEXT("HJ")));
							auto FX = FCString::Atoi(*NextNode->Get()->GetParameterValue(TEXT("MHFFX")));
							float OldSize = FX == 0 ? WJ : HJ;
							RecursionRefreshDoor(nullptr, *NextNode, WJ, HJ - Offset * RootXCBJT, OldSize, NeedDeleteDoors);
						}
					}
				}
				else
				{
					RecursionPushCoverToUpLevel(PropertyMap.FindRef(ParentItem), ParentItem, RootSCBJT, RootXCBJT, RootZCBJT, RootYCBJT, Offset, InNewValue, InParamName, PropertyMap, NeedDeleteDoors);
				}
			}
		}
	}
	else
	{
		double OldSize;
		const int32 HFFX = FCString::Atoi(*CurrentModelInfo->GetParameterValue(TEXT("MHFFX")));
		double WJ = FCString::Atod(*CurrentModelInfo->GetParameterValue(TEXT("WJ")));
		double HJ = FCString::Atod(*CurrentModelInfo->GetParameterValue(TEXT("HJ")));

		OldSize = HFFX == 0 ? WJ : HJ;

		TFunction<void(const TSharedPtr<FMultiComponentDataItem>&, const FString&, const int32&)> RecurseHorCover = [&](const TSharedPtr<FMultiComponentDataItem>& Root, const FString& CoverName, const int32& Index)
		{
			const int32 ItemHFFX = FCString::Atoi(*Root->GetParameterValue(TEXT("MHFFX")));
			if (ItemHFFX == 0)
			{
				for (auto& It : Root->ChildComponent)
				{
					if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), It->ModelType))
					{
						It->SetParameter(CoverName, InNewValue);
					}
					else
					{
						RecurseHorCover(It, CoverName, Index);
					}
				}
			}
			else
			{
				for (auto& It : Root->ChildComponent)
				{
					if (FCString::Atoi(*It->GetParameterValue(TEXT("MBPXH"))) == Index)
					{
						if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), It->ModelType))
						{
							It->SetParameter(CoverName, InNewValue);
						}
						else
						{
							RecurseHorCover(It, CoverName, Index);
						}
					}
				}
			}
		};

		TFunction<void(const TSharedPtr<FMultiComponentDataItem>&, const FString&, const int32&)> RecurseVerCover = [&](const TSharedPtr<FMultiComponentDataItem>& Root, const FString& CoverName, const int32& Index)
		{
			const int32 ItemHFFX = FCString::Atoi(*Root->GetParameterValue(TEXT("MHFFX")));
			if (ItemHFFX == 0)
			{
				for (auto& It : Root->ChildComponent)
				{
					if (FCString::Atoi(*It->GetParameterValue(TEXT("MBPXH"))) == Index)
					{
						if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), It->ModelType))
						{
							It->SetParameter(CoverName, InNewValue);
						}
						else
						{
							RecurseVerCover(It, CoverName, Index);
						}
					}
				}
			}
			else
			{
				for (auto& It : Root->ChildComponent)
				{
					if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), It->ModelType))
					{
						It->SetParameter(CoverName, InNewValue);
					}
					else
					{
						RecurseVerCover(It, CoverName, Index);
					}
				}
			}
		};

		if (HFFX == 0)
		{
			if (InParamName == TEXT("SBFG"))
			{
				for (auto& Item : CurrentModelInfo->ChildComponent)
				{
					if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), Item->ModelType))
					{
						Item->SetParameter(TEXT("SBFG"), InNewValue);
					}
					else
					{
						RecurseHorCover(Item, TEXT("SBFG"), 0);
					}
				}
			}
			if (InParamName == TEXT("XBFG"))
			{
				for (auto& Item : CurrentModelInfo->ChildComponent)
				{
					if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), Item->ModelType))
					{
						Item->SetParameter(TEXT("XBFG"), InNewValue);
					}
					else
					{
						RecurseHorCover(Item, TEXT("XBFG"), 99);
					}
				}
			}
			if (InParamName == TEXT("ZBFG"))
			{
				for (auto& Item : CurrentModelInfo->ChildComponent)
				{
					if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), Item->ModelType))
					{
						Item->SetParameter(TEXT("ZBFG"), InNewValue);
					}
					else
					{
						RecurseVerCover(Item, TEXT("ZBFG"), 0);
					}
				}
			}
			if (InParamName == TEXT("YBFG"))
			{
				for (auto& Item : CurrentModelInfo->ChildComponent)
				{
					if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), Item->ModelType))
					{
						Item->SetParameter(TEXT("YBFG"), InNewValue);
					}
					else
					{
						RecurseVerCover(Item, TEXT("YBFG"), 99);
					}
				}
			}
		}
		else
		{
			if (InParamName == TEXT("ZBFG"))
			{
				for (auto& Item : CurrentModelInfo->ChildComponent)
				{
					if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), Item->ModelType))
					{
						Item->SetParameter(TEXT("ZBFG"), InNewValue);
					}
					else
					{
						RecurseVerCover(Item, TEXT("ZBFG"), 0);
					}
				}
			}
			if (InParamName == TEXT("YBFG"))
			{
				for (auto& Item : CurrentModelInfo->ChildComponent)
				{
					if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), Item->ModelType))
					{
						Item->SetParameter(TEXT("YBFG"), InNewValue);
					}
					else
					{
						RecurseVerCover(Item, TEXT("YBFG"), 99);
					}
				}
			}
			if (InParamName == TEXT("SBFG"))
			{
				for (auto& Item : CurrentModelInfo->ChildComponent)
				{
					if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), Item->ModelType))
					{
						Item->SetParameter(TEXT("SBFG"), InNewValue);
					}
					else
					{
						RecurseHorCover(Item, TEXT("SBFG"), 0);
					}
				}
			}
			if (InParamName == TEXT("XBFG"))
			{
				for (auto& Item : CurrentModelInfo->ChildComponent)
				{
					if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), Item->ModelType))
					{
						Item->SetParameter(TEXT("XBFG"), InNewValue);
					}
					else
					{
						RecurseHorCover(Item, TEXT("XBFG"), 99);
					}
				}
			}
		}

		//最顶层门容器
		if (InParamName == TEXT("SBFG"))
		{
			CurrentModelInfo->SetParameter(TEXT("MRQSG"), InNewValue);
			RecursionRefreshDoor(CurrentModelInfo, CurrentModelInfo, WJ, HJ + Offset * RootSCBJT, OldSize, NeedDeleteDoors);
		}
		else if (InParamName == TEXT("XBFG"))
		{
			CurrentModelInfo->SetParameter(TEXT("MRQXG"), InNewValue);
			FVector NewtPos = CurrentModelInfo->ComponentLocation.GetLocation() - FVector(0.0f, 0.0f, Offset * RootZCBJT * 0.1f);
			CurrentModelInfo->ComponentLocation.SetLocation(NewtPos * 10.0f);
			RecursionRefreshDoor(CurrentModelInfo, CurrentModelInfo, WJ, HJ + Offset * RootSCBJT, OldSize, NeedDeleteDoors);
		}
		else if (InParamName == TEXT("ZBFG"))
		{
			CurrentModelInfo->SetParameter(TEXT("MRQZG"), InNewValue);
			FVector NewtPos = CurrentModelInfo->ComponentLocation.GetLocation() - FVector(Offset * RootZCBJT * 0.1f, 0.0f, 0.0f);
			CurrentModelInfo->ComponentLocation.SetLocation(NewtPos * 10.0f);
			RecursionRefreshDoor(CurrentModelInfo, CurrentModelInfo, WJ + Offset * RootZCBJT, HJ, OldSize, NeedDeleteDoors);
		}
		else if (InParamName == TEXT("YBFG"))
		{
			CurrentModelInfo->SetParameter(TEXT("MRQYG"), InNewValue);
			RecursionRefreshDoor(CurrentModelInfo, CurrentModelInfo, WJ + Offset * RootYCBJT, HJ, OldSize, NeedDeleteDoors);
		}
	}
}

void UDSCupBoardDoorLibrary::OnDoorConveringTypeChange(UDSBaseModel* InDoorModel, const FString& InNewConvertType)
{
	EMaskingType DoorConvringType = static_cast<EMaskingType>(FCString::Atoi(*InNewConvertType));

	if (!InDoorModel->IsValid())
	{
		return;
	}
	UDSCupboardModel* DoorModel = Cast<UDSCupboardModel>(InDoorModel);
	UDSCupboardModel* RootModel = DoorModel->GetRootCupboardModel();
	TArray<TSharedPtr<FMultiComponentDataItem>> OutPath;
	RootModel->CollectComponentPath_Public(RootModel->GetModelInfo().ComponentTreeData, DoorModel->GetModelInfo().ComponentTreeData, OutPath);

	if (!OutPath.IsValidIndex(1))
	{
		return;
	}
	TSharedPtr<FMultiComponentDataItem> RootDoorContainer = OutPath[1];

	TArray<UDSBaseModel*> BoardModels = UDSModelDependencySubsystem::GetInstance()->FindDependentModels(DoorModel->GetModelInfo().GetModelUUID());

	//没有板可能是自带门
	if (BoardModels.IsEmpty())
	{
		return;
	}
	FDSSuitablePlane SuitablePlane;
	bool bIsOnlyEmbedded = false;
	TArray<TArray<UDSBaseModel*>> DependenciesGroup;
	TArray<UDSBaseModel*> DependentCabinets;
	if (!CalculateDoorArea(BoardModels, SuitablePlane, bIsOnlyEmbedded, DependenciesGroup, DependentCabinets))
	{
		return;
	}
	FVector RelativeLocation = RootModel->GetProperty()->GetActualTransform().InverseTransformPosition(SuitablePlane.Points[2]) * 10.f;
	auto WJ = FVector::Distance(SuitablePlane.Points[0], SuitablePlane.Points[1]) * 10.f;
	auto HJ = FVector::Distance(SuitablePlane.Points[1], SuitablePlane.Points[2]) * 10.f;
	if (DoorConvringType == EMaskingType::EOuterOver)
	{
		double ZCBJTValue = FCString::Atod(*RootDoorContainer->GetParameterValue(TEXT("ZCBJT")));
		double YCBJTValue = FCString::Atod(*RootDoorContainer->GetParameterValue(TEXT("YCBJT")));
		double SCBJTValue = FCString::Atod(*RootDoorContainer->GetParameterValue(TEXT("SCBJT")));
		double XCBJTValue = FCString::Atod(*RootDoorContainer->GetParameterValue(TEXT("XCBJT")));
		WJ = WJ + ZCBJTValue + YCBJTValue;
		HJ = HJ + SCBJTValue + XCBJTValue;

		RelativeLocation.X -= XCBJTValue;
		RelativeLocation.Z -= ZCBJTValue;
	}

	//设置根节点位置
	RootDoorContainer->ComponentLocation.SetLocation(RelativeLocation);

	ModifyContainerByConveringChange(DoorConvringType, WJ, HJ, RootDoorContainer);
	//设置根节点大小
	RootDoorContainer->SetParameter(TEXT("WJ"), FString::SanitizeFloat(WJ));
	RootDoorContainer->SetParameter(TEXT("HJ"), FString::SanitizeFloat(HJ));

	RootModel->OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf, FDSBroadcastMarkData::OnlyOutlineBroadcastMark);
	UpdateFunctionalBoardsByDoor(RootDoorContainer->ChildComponent, DoorConvringType == EMaskingType::EEmbedded);
}

void UDSCupBoardDoorLibrary::ModifyContainerByConveringChange(EMaskingType DoorConvringType, const double& InNewWidth, const double& InNewHeight, const TSharedPtr<FMultiComponentDataItem>& InContainerNode)
{
	FString MaskingModelStr = DoorConvringType == EMaskingType::EOuterOver ? TEXT("0") : TEXT("3");

	InContainerNode->SetParameter(TEXT("MRQSG"), MaskingModelStr);
	InContainerNode->SetParameter(TEXT("MRQXG"), MaskingModelStr);
	InContainerNode->SetParameter(TEXT("MRQZG"), MaskingModelStr);
	InContainerNode->SetParameter(TEXT("MRQYG"), MaskingModelStr);

	double ParentOldWidth = FCString::Atod(*InContainerNode->GetParameterValue(TEXT("WJ")));
	double ParentOldHeight = FCString::Atod(*InContainerNode->GetParameterValue(TEXT("HJ")));
	bool bHorizontalSplit = FCString::Atoi(*InContainerNode->GetParameterValue(TEXT("MHFFX"))) == 1;
	double DeltaSize = 0.f;
	for (auto Iter : InContainerNode->ChildComponent)
	{
		int DoorSplitCount = FCString::Atoi(*InContainerNode->GetParameterValue(TEXT("MHFSL")));

		int DoorSpiltType = FCString::Atoi(*Iter->GetParameterValue(TEXT("MHFZT")));
		int Index = FCString::Atoi(*Iter->GetParameterValue(TEXT("MBPXH")));

		if (IsDoorContainerNode(Iter))
		{
			double OldWidth = FCString::Atod(*Iter->GetParameterValue(TEXT("WJ")));
			double OldHeight = FCString::Atod(*Iter->GetParameterValue(TEXT("HJ")));
			double NewWidth = OldWidth;
			double NewHeight = OldHeight;

			if (bHorizontalSplit)
			{
				NewWidth = InNewWidth;
				CalculateNewSizeBySplitType(DoorSpiltType, DoorSplitCount
				                            , NewHeight, OldHeight, ParentOldHeight, InNewHeight);
			}
			else
			{
				NewHeight = InNewHeight;
				CalculateNewSizeBySplitType(DoorSpiltType, DoorSplitCount
				                            , NewWidth, OldWidth, ParentOldWidth, InNewWidth);
			}

			FVector ComponentLocation = FVector::ZeroVector;
			if (Index == 99)
			{
				ComponentLocation = FVector(
					bHorizontalSplit ? 0.f : InNewWidth - NewWidth,
					0.f,
					0.f);
			}
			else
			{
				ComponentLocation = FVector(
					bHorizontalSplit ? 0.f : Index * NewWidth,
					0.f,
					bHorizontalSplit ? (InNewHeight - (Index + 1) * NewHeight) : 0.f);
			}

			//设置位置
			Iter->ComponentLocation.SetLocation(ComponentLocation);

			DeltaSize += bHorizontalSplit ? NewHeight : NewWidth;

			//下层调用
			ModifyContainerByConveringChange(DoorConvringType, NewWidth, NewHeight, Iter);
			//设置净宽净高
			Iter->SetParameter(TEXT("WJ"), FString::SanitizeFloat(NewWidth));
			Iter->SetParameter(TEXT("HJ"), FString::SanitizeFloat(NewHeight));
		}
		else
		{
			Iter->SetParameter(TEXT("SBFG"), MaskingModelStr);
			Iter->SetParameter(TEXT("XBFG"), MaskingModelStr);
			Iter->SetParameter(TEXT("ZBFG"), MaskingModelStr);
			Iter->SetParameter(TEXT("YBFG"), MaskingModelStr);

			Iter->SetParameter(TEXT("MYGLX"), DoorConvringType == EMaskingType::EOuterOver ? TEXT("0") : TEXT("1"));
			double OldSize = FCString::Atod(*Iter->GetParameterValue(TEXT("MHFDX")));
			double NewSize = OldSize;
			if (bHorizontalSplit)
			{
				CalculateNewSizeBySplitType(DoorSpiltType, DoorSplitCount, NewSize, OldSize, ParentOldHeight, InNewHeight);
			}
			else
			{
				CalculateNewSizeBySplitType(DoorSpiltType, DoorSplitCount, NewSize, OldSize, ParentOldWidth, InNewWidth);
			}

			Iter->SetParameter(TEXT("MHFDX"), FString::SanitizeFloat(NewSize));

			FVector ComponentLocation = FVector::ZeroVector;
			if (Index == 99)
			{
				ComponentLocation = FVector(
					bHorizontalSplit ? 0.f : InNewWidth - NewSize,
					0.f,
					0.f);
			}
			else
			{
				ComponentLocation = FVector(
					bHorizontalSplit ? 0.f : Index * NewSize,
					0.f,
					bHorizontalSplit ? (InNewHeight - (Index + 1) * NewSize) : 0.f);
			}

			Iter->SetParameter(TEXT("MSCX"), FString::SanitizeFloat(ComponentLocation.X));
			Iter->SetParameter(TEXT("MSCY"), FString::SanitizeFloat(ComponentLocation.Y));
			Iter->SetParameter(TEXT("MSCZ"), FString::SanitizeFloat(ComponentLocation.Z));

			DeltaSize += NewSize;
		}
	}
}

bool UDSCupBoardDoorLibrary::IsDoorContainerNode(const TSharedPtr<FMultiComponentDataItem>& InNode)
{
	return UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(InNode->ModelType) == EDSModelType::E_Custom_DoorContainer;
}

void UDSCupBoardDoorLibrary::CalculateNewSizeBySplitType(const int SplitType, const int SplitCount, double& NewSize, double OldSize, const double& InOldParentSize, const double& InNewParentSize)
{
	switch (SplitType)
	{
	case 0:
		{
			NewSize = InNewParentSize / SplitCount;
		}
		break;
	case 1:
		{
			NewSize = (OldSize / InOldParentSize) * InNewParentSize;
		}
		break;
	case 2:
		break;
	case 3:
		{
			NewSize = InNewParentSize - (InOldParentSize - OldSize);
		}
		break;
	default:
		break;
	}
}

void UDSCupBoardDoorLibrary::OnPriceListClickAndXml()
{
	if (GenerateDoorHinges())
	{
		if (CheckAllHingesIsValid())
		{
			UDSUISubsystem::GetInstance()->SetToastState(EToastState::ESuccess, TEXT("铰链方案校验通过"), TEXT(""), true);

			TArray<FString> OutPaths;
			FDateTime Now = FDateTime::Now();
			FString DefaultFile = FString::Printf(TEXT("Output_%s.xml"), *Now.ToString());
			FString DefaultPath = UDSConfigSubsystem::GetInstance()->GetDefaultXmlSavePath();
			if (UDesignStationFunctionLibrary::SaveFileDialog(TEXT("Select Files"), DefaultPath, DefaultFile, OutPaths, TEXT("*.xml|*.xml;")))
			{
				UXmlLibrary::ExportDataToXml(OutPaths[0]);
				UDSConfigSubsystem::GetInstance()->SetDefaultLocalSavePath(FPaths::GetPath(OutPaths[0]));
			}
		}
		else
		{
			TArray<UDSBaseModel*> AllDoorModels;
			TArray<UDSBaseModel*> AllCustomModels = UDSMVCSubsystem::GetInstance()->GetAllCustomModels();

			for (auto& Door : AllCustomModels)
			{
				if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), Cast<UDSCupboardModel>(Door)->GetModelInfoRef().ComponentTreeData->ModelType)
					|| Door->GetModelType() == EDSModelType::E_Custom_FunctionalDrawer)
				{
					AllDoorModels.Add(Door);
				}
			}
			for (auto& Element : AllDoorModels)
			{
				UDSCupboardModel* DoorModel = Cast<UDSCupboardModel>(Element);
				DoorModel->OnExecuteAction(FDSModelExecuteType::ExecuteUnOverlap);
				FParameterData* HingesInfo = DoorModel->GetModelInfoRef().ComponentTreeData->ComponentParameters.FindByPredicate([](const FParameterData& InData) { return InData.Data.name == TEXT("SCJL"); });
				if (HingesInfo)
				{
					if (HingesInfo->Data.GetFormattedValue().Equals(TEXT("2")))
					{
						DoorModel->OnExecuteAction(FDSModelExecuteType::ExecuteOverlap);
					}
				}
			}
		}
	}
}

bool UDSCupBoardDoorLibrary::CheckHasClosedDoor(UDSBaseModel* InModel, TArray<UDSCupboardModel*>& AllDoorModels, TArray<UDSCupboardModel*>& ClosedDoorModels)
{
	if (UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(InModel)) //如果是定制
	{
		if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), CupboardModel->GetModelInfoRef().ComponentTreeData->ModelType)) //如果选中的直接是门
		{
			AllDoorModels.AddUnique(CupboardModel);
		}
		else //如果是柜子
		{
			TArray<UDSBaseModel*> Result;
			TArray<UDSCupboardModel*> DoorModels;
			UDSCounterTopLibrary::GetSingleComponentModels(InModel, Result);

			for (auto& It : Result)
			{
				if (UDSCupboardModel* DoorModel = Cast<UDSCupboardModel>(It))
				{
					if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), DoorModel->GetModelInfoRef().ComponentTreeData->ModelType))
					{
						if (UDSCupboardLibrary::FindNearestFunctionalCupboardModel(DoorModel) == nullptr)
						{
							AllDoorModels.AddUnique(DoorModel);
						}
					}
				}
			}
		}
	}
	else if (UDSMultiModel* MultiModel = Cast<UDSMultiModel>(InModel)) //如果是多选或者组合
	{
		TArray<UDSBaseModel*> Result;
		for (auto& Element : MultiModel->GetIncludeModel())
		{
			UDSCupboardModel* DoorModel = Cast<UDSCupboardModel>(Element);
			if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), DoorModel->GetModelInfoRef().ComponentTreeData->ModelType))
			{
				if (UDSCupboardLibrary::FindNearestFunctionalCupboardModel(DoorModel) == nullptr)
				{
					AllDoorModels.AddUnique(DoorModel);
					continue;
				}
			}
			if (UDSToolLibrary::IsCustomType(Element))
			{
				UDSCounterTopLibrary::GetSingleComponentModels(Element, Result);
			}
		}

		for (auto& It : Result)
		{
			if (UDSCupboardModel* DoorModel = Cast<UDSCupboardModel>(It))
			{
				if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), DoorModel->GetModelInfoRef().ComponentTreeData->ModelType))
				{
					if (UDSCupboardLibrary::FindNearestFunctionalCupboardModel(DoorModel) == nullptr)
					{
						AllDoorModels.AddUnique(DoorModel);
					}
				}
			}
		}
	}
	else //空或者选中其他物体
	{
		TArray<UDSBaseModel*> Result = UDSMVCSubsystem::GetInstance()->GetAllCustomModels();
		for (auto& It : Result)
		{
			if (UDSCupboardModel* DoorModel = Cast<UDSCupboardModel>(It))
			{
				if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), DoorModel->GetModelInfoRef().ComponentTreeData->ModelType))
				{
					if (UDSCupboardLibrary::FindNearestFunctionalCupboardModel(DoorModel) == nullptr)
					{
						AllDoorModels.AddUnique(DoorModel);
					}
				}
			}
		}
	}

	for (UDSCupboardModel* DoorModel : AllDoorModels)
	{
		if (DS_MODEL_VALID_FOR_USE(DoorModel))
		{
			FParameterData* OpenData = DoorModel->GetModelInfoRef().ComponentTreeData->ComponentParameters.FindByPredicate([](const FParameterData& InData)
			{
				return InData.Data.name == TEXT("DKJD");
			});
			if (!OpenData)
			{
				continue;
			}
			if (FCString::Atoi(*OpenData->Data.GetFormattedValue()) == 0)
			{
				ClosedDoorModels.Add(DoorModel);
			}
		}
	}

	if (ClosedDoorModels.IsEmpty())
	{
		return false;
	}

	return true;
}

bool UDSCupBoardDoorLibrary::CheckHasClosedDrawer(UDSBaseModel* InModel, TArray<UDSCupboardModel*>& ClosedDrawerModels)
{
	TArray<UDSBaseModel*> DrawerModels;
	if (UDSMultiModel* MultiModel = Cast<UDSMultiModel>(InModel))
	{
		TArray<UDSBaseModel*> IncludeModels = MultiModel->GetIncludeModel();
		for (int32 Index = 0; Index < IncludeModels.Num(); Index++)
		{
			UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(IncludeModels[Index]);
			if (CupboardModel == nullptr)
			{
				continue;
			}

			if (CupboardModel->GetModelType() == EDSModelType::E_Custom_FunctionalDrawer)
			{
				DrawerModels.AddUnique(CupboardModel);
			}
			else if (CupboardModel == CupboardModel->GetRootCupboardModel())
			{
				TArray<UDSCupboardModel*> Queue = {CupboardModel};
				while (!Queue.IsEmpty())
				{
					UDSCupboardModel* CurrentModel = Queue.Pop();
					if (CurrentModel->GetModelType() == EDSModelType::E_Custom_FunctionalDrawer)
					{
						DrawerModels.AddUnique(CurrentModel);
					}

					for (const FDSComponentInfo& Info : CurrentModel->GetModelInfoRef().ComponentInfoArr)
					{
						UDSCupboardModel* ChildModel = Cast<UDSCupboardModel>(Info.ComponentModel);
						if (ChildModel != nullptr && ChildModel->GetModelType() == EDSModelType::E_Custom_FunctionalDrawer)
						{
							DrawerModels.AddUnique(ChildModel);
						}
					}
				}
			}
		}
	}
	else if (UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(InModel))
	{
		if (CupboardModel == CupboardModel->GetRootCupboardModel())
		{
			TArray<UDSBaseModel*> Result;
			UDSCounterTopLibrary::GetSingleComponentModels(CupboardModel, Result);

			for (auto& It : Result)
			{
				if (UDSCupboardModel* DoorModel = Cast<UDSCupboardModel>(It))
				{
					if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), DoorModel->GetModelInfoRef().ComponentTreeData->ModelType))
					{
						if (UDSCupboardLibrary::FindNearestFunctionalCupboardModel(DoorModel) != nullptr)
						{
							DrawerModels.AddUnique(DoorModel);
						}
					}
				}
			}
		}
		else if (CupboardModel->GetModelType() == EDSModelType::E_Custom_FunctionalDrawer)
		{
			DrawerModels.AddUnique(InModel);
		}
	}
	else if (InModel == nullptr || (InModel->GetModelType() == EDSModelType::E_House_Wall || InModel->GetModelType() == EDSModelType::E_House_Area))
	{
		DrawerModels = UDSMVCSubsystem::GetInstance()->GetModels(EDSModelType::E_Custom_FunctionalDrawer);
	}

	for (auto& It : DrawerModels)
	{
		if (DS_MODEL_VALID_FOR_USE(It))
		{
			if (!UDSCupboardLibrary::IsDrawerOpening(Cast<UDSCupboardModel>(It)))
			{
				ClosedDrawerModels.Add(Cast<UDSCupboardModel>(It));
			}
		}
	}

	if (ClosedDrawerModels.IsEmpty())
	{
		return false;
	}

	return true;
}
