#pragma once
#include "Subsystems/Construction/DSConstructionCore.h"



struct DESIGNSTATION_API FDSDimensionLinkData
{
public:
	FDSDimensionLinkData() {};
	~FDSDimensionLinkData() {};

public:
	FString SourceEntryUUID; //源构件的UUID
	FString	TargetEntryUUID; //目标构件的UUID

};


struct DESIGNSTATION_API FDSDimensionEntryData
{
public:
	FDSDimensionEntryData() {};
	~FDSDimensionEntryData() {}
public:
	FString UUID;
	EDSModelType ModelType = EDSModelType::E_None;
	FVector Min;
	FVector Max;

	TArray<TSharedPtr<FDSDimensionLinkData>> LinkNodes;

	bool bHorizontalPositive;
	bool bVerticalPositive;
};

struct DESIGNSTATION_API FDSDimensionSet
{
	FDSDimensionSet(bool bIsPositive = false)
		: bPositive(bIsPositive)
	{
		StartPoint = std::numeric_limits<double>::lowest();
		EndPoint = std::numeric_limits<double>::max();
	}

	double StartPoint;
	double EndPoint;

	TMap<FString, TSharedPtr<FDSDimensionEntryData>> DimensionEntryDataMap; //标注数据

	TArray<FString> ConstructionUUIDS;

private:
	bool bPositive = false; //水平标注

public:
	bool IsInRange(const FVector& InPoint) {

		double InStart = bPositive ? InPoint.X : InPoint.Y;
		double InEnd = bPositive ? InPoint.X : InPoint.Y;
		if (InStart < StartPoint || InEnd > EndPoint)
		{
			return false;
		}

		return true;
	};

	// 修改 Add 方法，判断新对象在当前投影方向上是否与已存在对象有重叠，没有重叠才添加
	bool Add(const TSharedPtr<FDSDimensionEntryData>& InDimensionEntryData)
	{
		double InStart = bPositive ? InDimensionEntryData->Min.X : InDimensionEntryData->Min.Y;
		double InEnd = bPositive ? InDimensionEntryData->Max.X : InDimensionEntryData->Max.Y;

		// 检查是否与已存在的对象有重叠
		for (const auto& Pair : DimensionEntryDataMap)
		{
			const TSharedPtr<FDSDimensionEntryData>& ExistingEntry = Pair.Value;
			double ExistingStart = bPositive ? ExistingEntry->Min.X : ExistingEntry->Min.Y;
			double ExistingEnd = bPositive ? ExistingEntry->Max.X : ExistingEntry->Max.Y;

			// 判断重叠：只要区间有交集则视为重叠
			if (!(InEnd < ExistingStart || InStart > ExistingEnd))
			{
				// 有重叠则不添加
				return false;
			}
		}

		if (DimensionEntryDataMap.IsEmpty())
		{
			StartPoint = InStart;
			EndPoint = InEnd;
			DimensionEntryDataMap.Add(InDimensionEntryData->UUID, InDimensionEntryData);
			return true;
		}

		if (DimensionEntryDataMap.Contains(InDimensionEntryData->UUID))
		{
			return false;
		}

		StartPoint = FMath::Min(StartPoint, InStart);
		EndPoint = FMath::Max(EndPoint, InEnd);
		DimensionEntryDataMap.Add(InDimensionEntryData->UUID, InDimensionEntryData);
		return true;
	}

	void CalculateLinkRelationship(bool bHorizontal);
};


struct DESIGNSTATION_API FDSAxisDimensionData
{
public:

	FDSAxisDimensionData(bool bIsHorizontal = true)
		: bHorizontal(bIsHorizontal)
	{
		Min = FVector2D(-FLT_MAX, -FLT_MAX);
		Max = FVector2D(FLT_MAX, FLT_MAX);
	}


	FVector2D Min;
	FVector2D Max;

	bool bHorizontal;
	TArray<TSharedPtr<FDSDimensionSet>> DimensionSetDatas;
public:
	void Insert(const TSharedPtr<FDSDimensionEntryData>& InEntryData);


	void DrawDimension() const
	{
		for (const auto& SetData : DimensionSetDatas)
		{
			if (SetData.IsValid())
			{
				SetData->CalculateLinkRelationship(bHorizontal);
			}
		}
	}
};

class FDSConstructionDimensionGeneratorBase
{
public:
	FDSConstructionDimensionGeneratorBase() {};
	virtual ~FDSConstructionDimensionGeneratorBase() {};
public:
	virtual void GenerateDimension(const TArray<TSharedPtr<FDSConstructionData>>& InConstructDatas) {};

	virtual	void DrawDimension(const FVector& PaperOffset) {};
};

