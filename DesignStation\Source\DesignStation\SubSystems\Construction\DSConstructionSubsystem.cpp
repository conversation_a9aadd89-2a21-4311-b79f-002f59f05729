// Fill out your copyright notice in the Description page of Project Settings.


#include "SubSystems/Construction/DSConstructionSubsystem.h"
#include "DxfLib/DxfTempData.h"
#include "DxfLib/DxfHeaderReader.h"
#include "DxfLib/DxfTablesReader.h"
#include "DxfLib/DxfBlocksReader.h"
#include <io.h>
#include "DxfLib/DxfWriter.h"
#include "DxfLib/DxfWriterHelper.h"
#include "DxfLib/FormatConversion.h"
#include "Subsystems/MVC/DSMVCSubsystem.h"
#include "SubSystems/MVC/Model/House/Area/DSHouseAreaModel.h"
#include "SubSystems/MVC/Core/Property/HouseAreaProperty.h"
#include "Clipper2/Library/Clipper2Library.h"
#include "Subsystems/MVC/Model/Custom/DSCupboardModel.h"
#include "DSConstructionLibrary.h"
#include "SubSystems/Library/DesignStationFunctionLibrary.h"
#include "SubSystems/Resource/DSResourceSubsystem.h"
#include "Subsystems/DSNetworkSubsystem.h"

DEFINE_LOG_CATEGORY(LogConstruction)

UDSConstructionSubsystem* UDSConstructionSubsystem::Instance = nullptr;


void UDSConstructionSubsystem::Initialize(FSubsystemCollectionBase& Collection)
{
	UDSConstructionSubsystem::Instance = this;

	DxfLib::FDxfTempData::CreateInstance();
}

void UDSConstructionSubsystem::Deinitialize()
{
	UDSConstructionSubsystem::Instance = nullptr;

	DxfLib::FDxfTempData::DeleteInstance();
}

void UDSConstructionSubsystem::ExportDxfFile(const FString& InFilePath)
{

}

void UDSConstructionSubsystem::ExportAllRoom()
{
	for (auto PaperIte : ConstructionPapers)
	{
		PaperIte->MarkAsGarbage();
	}
	ConstructionPapers.Empty();

	//加载DXF模板文件
	LoadDxfTemplateFile(FPaths::Combine(FPaths::ProjectContentDir(), TEXT("Res/Construction/FrameTemplete.dxf")));

	//所有的柜子
	TArray<UDSBaseModel*> AllCupboardModels = UDSMVCSubsystem::GetInstance()->GetModels({ EDSModelType::E_Custom_UpperCabinet,
		EDSModelType::E_Custom_BaseCabinet,EDSModelType::E_Custom_TallCabinet,EDSModelType::E_Custom_CornerCabinet,
		EDSModelType::E_Custom_Tatami,EDSModelType::E_Custom_CornerCutCabinet,EDSModelType::E_Custom_WallCabinet,
		EDSModelType::E_Custom_WallBoardCabinet
		});

	//所有的Area
	TArray<UDSBaseModel*> RoomModels = UDSMVCSubsystem::GetInstance()->GetModels({ EDSModelType::E_House_Area });
	//FVector VInCAD = UDSConstructionLibrary::UE4ToCAD(FVector(50, 100, 20));

	TMap<UDSHouseAreaModel*, TArray<UDSCupboardModel*>> RoomToCupboardMap;
	TMap<UDSHouseAreaModel*, TArray<TSharedPtr<FDSConstructionData>>> RoomToBaseCupboardDataMap;
	TMap<UDSHouseAreaModel*, TArray<TSharedPtr<FDSConstructionData>>> RoomToWallCupboardDataMap;
	TMap<UDSHouseAreaModel*, TArray<TSharedPtr<FDSConstructionData>>> RoomDataMap;
	for (auto RoomIte : RoomModels)
	{
		if (RoomIte == nullptr)
			continue;
		UDSHouseAreaModel* RoomModel = Cast<UDSHouseAreaModel>(RoomIte);
		if (RoomModel->GetParentPlane() == nullptr && !RoomModel->GetChildPlanes().IsEmpty())
		{
			//忽略根区域模型
			continue;
		}
		TSharedPtr<FDSHouseAreaProperty> RoomProperty = RoomModel->GetTypedProperty<FDSHouseAreaProperty>();
		if (RoomProperty->Points.Num() < 3)
			continue;

		//生成这个Area的图纸数据
		TArray<TSharedPtr<FDSConstructionData>> HomeConstructionDatas;
		GenHomeConstructionData(RoomModel, HomeConstructionDatas);
		RoomDataMap.FindOrAdd(RoomModel).Append(MoveTemp(HomeConstructionDatas));

		//生成这个Area中的所有柜子图纸数据
		for (auto CupboardIte : AllCupboardModels)
		{
			if (CupboardIte == nullptr)
				continue;

			UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(CupboardIte);

			TArray<TArray<FVector>> OutOutline;
			if (UDSConstructionLibrary::CalcCupboard2DOutline(CupboardModel, FTransform::Identity, OutOutline))
			{
				TArray<TArray<FVector>> Results;
				if (!FClipper2Library::PolygonDifference02({ OutOutline[0] }, { RoomProperty->Points }, Results))
				{
					RoomToCupboardMap.FindOrAdd(RoomModel).AddUnique(CupboardModel);

					//得到柜子和柜子子部件图纸数据
					TArray<TSharedPtr<FDSConstructionData>> CurConstructionDatas;
					GenRootCupboardConstructionData(CupboardModel, RoomModel, CurConstructionDatas);

					if (IsBaseCupboardType(CupboardModel))   //地柜
					{
						RoomToBaseCupboardDataMap.FindOrAdd(RoomModel).Append(MoveTemp( CurConstructionDatas));
					}
					else if (IsWallCupboardType(CupboardModel))  //吊柜
					{
						RoomToWallCupboardDataMap.FindOrAdd(RoomModel).Append(MoveTemp(CurConstructionDatas));
					}
				}
			}
		}
	}

	TArray<TSharedPtr<FDSConstructionData>> AllBaseConstructionDatas;
	TArray<TSharedPtr<FDSConstructionData>> AllWallConstructionDatas;

	const float DefaultOffsetInCAD = 200.0f;
	FVector PaperPosInCAD = FVector::ZeroVector;
	for (auto& Ite : RoomToCupboardMap)
	{
		PaperPosInCAD.X = 0.0f;
		float MaxY = 0.0f;
		//生成单屋地柜平面图纸
		{
			auto FindBaseCupboardData = RoomToBaseCupboardDataMap.Find(Ite.Key);
			if (FindBaseCupboardData != nullptr && (*FindBaseCupboardData).Num() > 0)
			{
				AllBaseConstructionDatas.Append((*FindBaseCupboardData));

				UDSConstructionPaper* NewPaper = NewObject<UDSConstructionPaper>();
				ConstructionPapers.Add(NewPaper);
				UDSConstructionPaper::FDSPaperInitData PaperData;
				PaperData.PaperName = UDSConstructionPaper::Paper_PlaneBaseCabinet;
				PaperData.PaperType = E_ConstructionPaperType::PaperType_SingleRoomPlane;
				PaperData.FrameName = UDSConstructionFrame::Frame_Default;
				PaperData.CupboardDatas = (*FindBaseCupboardData);   //柜子图纸数据
				PaperData.AreaDatas = RoomDataMap[Ite.Key];      //房间图纸数据
				PaperData.AreaModels.Add(Ite.Key);

				//PaperData.HouseModels
				PaperData.WorldToProjective = FTransform(UDSConstructionLibrary::CalcRoomBox(Ite.Key).GetCenter()).Inverse();
				NewPaper->InitPaper(PaperData);   //地柜
				NewPaper->CalcDrawingOffset(PaperPosInCAD);  //设置图纸位置
				FVector FrameSize = NewPaper->GetFrame()->GetFrameActiveOuterSizeInCAD();
				PaperPosInCAD.X += (FrameSize.X + DefaultOffsetInCAD);
				MaxY = FMath::Max(MaxY, FrameSize.Y);
			}
		}

		//生成单屋吊柜平面图纸
		{
			auto FindWallCupboardData = RoomToWallCupboardDataMap.Find(Ite.Key);
			if (FindWallCupboardData != nullptr && (*FindWallCupboardData).Num() > 0)
			{
				AllWallConstructionDatas.Append((*FindWallCupboardData));

				UDSConstructionPaper* NewPaper = NewObject<UDSConstructionPaper>();
				ConstructionPapers.Add(NewPaper);
				UDSConstructionPaper::FDSPaperInitData PaperData;
				PaperData.PaperName = UDSConstructionPaper::Paper_PlaneWallCabinet;
				PaperData.PaperType = E_ConstructionPaperType::PaperType_SingleRoomPlane;
				PaperData.FrameName = UDSConstructionFrame::Frame_Default;
				PaperData.CupboardDatas = (*FindWallCupboardData);   //柜子图纸数据
				PaperData.AreaDatas = RoomDataMap[Ite.Key];      //房间图纸数据
				PaperData.AreaModels.Add(Ite.Key);

				//PaperData.HouseModels
				PaperData.WorldToProjective = FTransform(UDSConstructionLibrary::CalcRoomBox(Ite.Key).GetCenter()).Inverse();
				NewPaper->InitPaper(PaperData);   //地柜
				NewPaper->CalcDrawingOffset(PaperPosInCAD);  //设置图纸位置
				FVector FrameSize = NewPaper->GetFrame()->GetFrameActiveOuterSizeInCAD();
				PaperPosInCAD.X += (FrameSize.X + DefaultOffsetInCAD);
				MaxY = FMath::Max(MaxY, FrameSize.Y);
			}
		}

		//生成立面图纸
		{
			TArray<TSharedPtr<FDSConstructionData>> AllConstructionDatas;
			auto BaseCupboardData = RoomToBaseCupboardDataMap.Find(Ite.Key);
			if (BaseCupboardData != nullptr)
			{
				AllConstructionDatas.Append(*BaseCupboardData);
			}
			auto WallCupboardData = RoomToWallCupboardDataMap.Find(Ite.Key);
			if (WallCupboardData != nullptr)
			{
				AllConstructionDatas.Append(*WallCupboardData);
			}

			TArray<FDSFrontGroupModels> OutGroups;
			GenFrontGroupModel(Ite.Value, OutGroups);   //打组

			for (auto& GroupIte : OutGroups)
			{
				if (GroupIte.Models.IsEmpty())
					continue;

				//根据组，创建立面结构图
				UDSConstructionPaper* NewPaper = NewObject<UDSConstructionPaper>();
				ConstructionPapers.Add(NewPaper);
				UDSConstructionPaper::FDSPaperInitData PaperData;
				PaperData.PaperName = UDSConstructionPaper::Paper_FrontConstructionCabinet;
				PaperData.PaperType = E_ConstructionPaperType::PaperType_Front_Construction;
				PaperData.FrameName = UDSConstructionFrame::Frame_Default;
				//PaperData.CupboardDatas = RoomToBaseCupboardDataMap[Ite.Key];
				PaperData.AreaModels.Add(Ite.Key);

				for (auto ModelIte : GroupIte.Models)
				{
					UDSConstructionLibrary::GetConstructionDatasByModel(ModelIte, AllConstructionDatas, PaperData.CupboardDatas);
				}

				//移除门的图纸数据
				PaperData.CupboardDatas.RemoveAll([](const TSharedPtr<FDSConstructionData>& Data) {
					return UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), Data->Node->ModelType);
					});

				//PaperData.WorldToProjective = FTransform(ZDir, XDir, YDir, FVector::ZeroVector).Inverse();
				PaperData.WorldToProjective = GroupIte.WorldToProjective;
				NewPaper->InitPaper(PaperData);
				NewPaper->CalcDrawingOffset(PaperPosInCAD);  //设置图纸位置
				FVector FrameSize = NewPaper->GetFrame()->GetFrameActiveOuterSizeInCAD();
				PaperPosInCAD.X += (FrameSize.X + DefaultOffsetInCAD);
				MaxY = FMath::Max(MaxY, FrameSize.Y);

				TArray<TSharedPtr<FDSConstructionData>> CupboardDatas;
				for (auto ModelIte : GroupIte.Models)
				{
					if (UDSConstructionLibrary::IsHasDoorNode(ModelIte->GetComponentTreeDataRef()))
					{
						UDSConstructionLibrary::GetConstructionDatasByModel(ModelIte, AllConstructionDatas, CupboardDatas);
					}
				}

				if (CupboardDatas.Num() > 0)
				{
					//有门，则生成立面门板图
					UDSConstructionPaper* NewPaper = NewObject<UDSConstructionPaper>();
					ConstructionPapers.Add(NewPaper);
					UDSConstructionPaper::FDSPaperInitData PaperData;
					PaperData.PaperName = UDSConstructionPaper::Paper_FrontDoorCabinet;
					PaperData.PaperType = E_ConstructionPaperType::PaperType_Front_Door;
					PaperData.FrameName = UDSConstructionFrame::Frame_Default;
					PaperData.CupboardDatas = CupboardDatas;
					PaperData.AreaModels.Add(Ite.Key);
					PaperData.WorldToProjective = GroupIte.WorldToProjective;
					NewPaper->InitPaper(PaperData);
					NewPaper->CalcDrawingOffset(PaperPosInCAD);  //设置图纸位置
					FVector FrameSize = NewPaper->GetFrame()->GetFrameActiveOuterSizeInCAD();
					PaperPosInCAD.X += (FrameSize.X + DefaultOffsetInCAD);
					MaxY = FMath::Max(MaxY, FrameSize.Y);
				}
			}
		}

		PaperPosInCAD.Y += (MaxY + 500);  
	}

	TArray<UDSHouseAreaModel*> AllAreas;
	RoomToCupboardMap.GenerateKeyArray(AllAreas);

	FBox AllRoomBox;
	TArray<TSharedPtr<FDSConstructionData>> AllAreaConstructionDatas;
	for (auto& RoomIte : AllAreas)
	{
		AllAreaConstructionDatas.Append(RoomDataMap[RoomIte]);
		AllRoomBox += UDSConstructionLibrary::CalcRoomBox(RoomIte);
	}

	PaperPosInCAD.X = 0.0f;

	//生成全屋地柜平面图纸
	if(AllBaseConstructionDatas.Num() > 0)
	{
		UDSConstructionPaper* NewPaper = NewObject<UDSConstructionPaper>();
		ConstructionPapers.Add(NewPaper);
		UDSConstructionPaper::FDSPaperInitData PaperData;
		PaperData.PaperName = TEXT("全局地柜平面图");
		PaperData.PaperType = E_ConstructionPaperType::PaperType_MutilRoomPlane;
		PaperData.FrameName = UDSConstructionFrame::Frame_Default;
		PaperData.CupboardDatas = AllBaseConstructionDatas;   //柜子图纸数据
		PaperData.AreaDatas = AllAreaConstructionDatas;      //房间图纸数据
		PaperData.AreaModels = AllAreas;

		//PaperData.HouseModels
		PaperData.WorldToProjective = FTransform(AllRoomBox.GetCenter()).Inverse();
		NewPaper->InitPaper(PaperData);   //地柜
		NewPaper->CalcDrawingOffset(PaperPosInCAD);  //设置图纸位置
		FVector FrameSize = NewPaper->GetFrame()->GetFrameActiveOuterSizeInCAD();
		PaperPosInCAD.X += (FrameSize.X + DefaultOffsetInCAD);
	}

    //生成全屋吊柜平面图纸
	if (AllWallConstructionDatas.Num() > 0)
	{
		UDSConstructionPaper* NewPaper = NewObject<UDSConstructionPaper>();
		ConstructionPapers.Add(NewPaper);
		UDSConstructionPaper::FDSPaperInitData PaperData;
		PaperData.PaperName = TEXT("全局顶柜平面图");
		PaperData.PaperType = E_ConstructionPaperType::PaperType_MutilRoomPlane;
		PaperData.FrameName = UDSConstructionFrame::Frame_Default;
		PaperData.CupboardDatas = AllWallConstructionDatas;   //柜子图纸数据
		PaperData.AreaDatas = AllAreaConstructionDatas;      //房间图纸数据
		PaperData.AreaModels = AllAreas;

		//PaperData.HouseModels
		PaperData.WorldToProjective = FTransform(AllRoomBox.GetCenter()).Inverse();
		NewPaper->InitPaper(PaperData);   //地柜
		NewPaper->CalcDrawingOffset(PaperPosInCAD);  //设置图纸位置
		FVector FrameSize = NewPaper->GetFrame()->GetFrameActiveOuterSizeInCAD();
		PaperPosInCAD.X += (FrameSize.X + DefaultOffsetInCAD);
	}

	TArray<TSharedPtr<FDSConstructionData>> AllConstructionDatas;
	AllConstructionDatas.Append(MoveTemp(AllBaseConstructionDatas));
	AllConstructionDatas.Append(MoveTemp(AllWallConstructionDatas));

	//计算图纸的位置
	UpdatePaperPos();

	//请求资源信息
	QueryResourecList(AllConstructionDatas);
}

void UDSConstructionSubsystem::ExportAllPapers()
{
	//绘制图纸
	for (auto PaperIte : ConstructionPapers)
	{
		PaperIte->FillFrameAttDef();
		PaperIte->DrawPaper();
	}

	//导出图纸
	FString DxfFileName = FString::Printf(TEXT("%s.dxf"), *FGuid::NewGuid().ToString());
	FString TempTargetPath = FPaths::ProjectSavedDir() + FString::Printf(TEXT("ExportDxf/%s"), *DxfFileName);
	// 确保目录存在
	IFileManager::Get().MakeDirectory(*FPaths::GetPath(TempTargetPath), true);
	DxfLib::FDxfWriterHelper::ExportDxdFile(TempTargetPath);

	for (auto PaperIte : ConstructionPapers)
	{
		PaperIte->MarkAsGarbage();
	}
	ConstructionPapers.Empty();
}


void UDSConstructionSubsystem::DrawPrimitive(const TArray<TSharedPtr<FDSPrimitiveBase>>& InPrimitives,
	const FString& LayerName, const FString& InBlockName, FVector OffsetInCAD)
{
	//std::string strLayerName = Format::FormatConversion::

	//FTransform PTOCAD(FVector::YAxisVector, FVector::XAxisVector, FVector::ZAxisVector, FVector::ZeroVector);
	//PTOCAD.SetScale3D(FVector(-1, 1, 1));

	//PTOCAD = FTransform(FVector(OffsetInCAD.X, OffsetInCAD.Y, 0)) * PTOCAD;

	DL_Attributes Attr(DxfLib::UEStrToDxfStr(LayerName), 256, -1, "BYLAYER", 1.0f);
	DxfLib::FDxfBlockData *NewBlockData = new DxfLib::FDxfBlockData(DxfLib::UEStrToDxfStr(InBlockName), FVector(0, 0, 0), Attr, "");
	DxfLib::FDxfTempData::GetInstance()->AddBlock(NewBlockData);

	for (auto Ite : InPrimitives)
	{
		DL_Attributes PrimitiveAttr(DxfLib::UEStrToDxfStr(LayerName), Ite->ColorID, Ite->LineWeight,
			DxfLib::UEStrToDxfStr(Ite->Linetype), 1.0f);

		if (Ite->PrimitiveType == EDSPrimitiveType::PrimitiveType_Polygon)
		{
			TSharedPtr<FDSPrimitive_Polygon> MP = StaticCastSharedPtr<FDSPrimitive_Polygon>(Ite);
			TArray<FVector> Points;
			for (auto& PIte : MP->Points)
			{
				Points.Add(UDSConstructionLibrary::UE4ToCAD(PIte));
			}
			DxfLib::FDxfLWPolyLineData* NewData = new DxfLib::FDxfLWPolyLineData(Points.Num(), MP->bIsClose ? 1 : 0, PrimitiveAttr);
			NewData->SetPoints(Points);

			NewBlockData->AddChildEntity(NewData);
		}
		else if (Ite->PrimitiveType == EDSPrimitiveType::PrimitiveType_MPolygon)
		{
			TSharedPtr<FDSPrimitive_MPolygon> MP = StaticCastSharedPtr<FDSPrimitive_MPolygon>(Ite);
			for (auto& Polygon : MP->Points)
			{
				TArray<FVector> Points;
				for (auto& PIte : Polygon)
				{
					Points.Add(UDSConstructionLibrary::UE4ToCAD(PIte));
				}

				DxfLib::FDxfLWPolyLineData* NewData = new DxfLib::FDxfLWPolyLineData(Points.Num(), 1, PrimitiveAttr);
				NewData->SetPoints(Points);

				NewBlockData->AddChildEntity(NewData);
			}
		}
		else if (Ite->PrimitiveType == EDSPrimitiveType::PrimitiveType_MPolygon3D)
		{
			TSharedPtr<FDSPrimitive_MPolygon3D> MP = StaticCastSharedPtr<FDSPrimitive_MPolygon3D>(Ite);
			for (auto& Polygon : MP->Polygons)
			{
				TArray<FVector> Points;
				for (auto& PIte : Polygon.Outter)
				{
					Points.Add(UDSConstructionLibrary::UE4ToCAD(PIte));
				}

				DxfLib::FDxfLWPolyLineData* NewData = new DxfLib::FDxfLWPolyLineData(Points.Num(), 1, PrimitiveAttr);
				NewData->SetPoints(Points);

				NewBlockData->AddChildEntity(NewData);
			}
		}
		else if (Ite->PrimitiveType == EDSPrimitiveType::PrimitiveType_MPolygon_Hatch)
		{
			TSharedPtr<FDSPrimitive_MPolygonHatch> MP = StaticCastSharedPtr<FDSPrimitive_MPolygonHatch>(Ite);
			
			DxfLib::FDxfHatchPatternData HatchPatternData(false, 45.0f, 10.0f, "LINE", FVector(0, 0, 0));
			DxfLib::FDxfHatchData* NewHatchData = new DxfLib::FDxfHatchData(HatchPatternData, PrimitiveAttr);

			for (auto& Polygon : MP->MPolygon.Points)
			{
				DxfLib::FDxfHatchPolyLineEdgeData* NewHatchEdge = new DxfLib::FDxfHatchPolyLineEdgeData();
				for (auto& PIte : Polygon)
				{
					NewHatchEdge->AddPoints(UDSConstructionLibrary::UE4ToCAD(PIte));
				}
				NewHatchEdge->bClose = true;

				NewHatchData->AddEdge(NewHatchEdge);
			}

			NewBlockData->AddChildEntity(NewHatchData);
		}
	}

	DxfLib::FDxfInsertData* NewInsertData = new DxfLib::FDxfInsertData(DxfLib::UEStrToDxfStr(InBlockName), OffsetInCAD,
		FVector(1, 1, 1), 0, "", Attr);

	DxfLib::FDxfTempData::GetInstance()->AddEntity(TSharedPtr<DxfLib::FDxfInsertData>(NewInsertData));
}

void UDSConstructionSubsystem::DrawFrame(const UDSConstructionFrame* InFrame, FVector OffsetInCAD)
{
	DL_Attributes Attr(DxfLib::UEStrToDxfStr(TEXT("0")), 256, -1, "BYLAYER", 1.0f);

	DxfLib::FDxfInsertData* NewInsertData = new DxfLib::FDxfInsertData(DxfLib::UEStrToDxfStr(InFrame->FrameType.ToString()),
		OffsetInCAD,
		InFrame->ScaleInCAD, 0, "", Attr);


	for (auto& Ite : InFrame->AttDefs)
	{
		NewInsertData->AddAttrib(DxfLib::UEStrToDxfStr(Ite.Key), DxfLib::UEStrToDxfStr(Ite.Value));
	}

	DxfLib::FDxfTempData::GetInstance()->AddEntity(TSharedPtr<DxfLib::FDxfInsertData>(NewInsertData));
}

void UDSConstructionSubsystem::DrawDimension(const FVector& InStart, const FVector& InEnd, const FVector& OffsetInCAD, bool bIsHorizontal)
{
	DL_Attributes Attr(DxfLib::UEStrToDxfStr(TEXT("0")), 256, -1, "BYLAYER", 1.0f);
	DxfLib::FDxfDimensionBase DimensionStyle(0);
	//FTransform PTOCAD(FVector::YAxisVector, FVector::XAxisVector, FVector::ZAxisVector, FVector::ZeroVector);
	//PTOCAD.SetScale3D(FVector(-1, 1, 1));
	//PTOCAD = FTransform(FVector(OffsetInCAD.X, OffsetInCAD.Y, 0)) * PTOCAD;

	FVector Start = UDSConstructionLibrary::UE4ToCAD(InStart) + OffsetInCAD;
	FVector End = UDSConstructionLibrary::UE4ToCAD(InEnd) + OffsetInCAD;
	DimensionStyle.TextPoint = (Start + End) * 0.5f;
	DimensionStyle.DefinitionPoint = Start;
	DimensionStyle.DimScale = 10.f;
	TSharedPtr<DxfLib::FDxfDimLinearData> NewDimension = MakeShared<DxfLib::FDxfDimLinearData>(Start,
		End, bIsHorizontal ? 0.f : 90.f,0.f,DimensionStyle, Attr);
	DxfLib::FDxfTempData::GetInstance()->AddEntity((NewDimension));
}

FVector UDSConstructionSubsystem::GetDefaultFrameOuterSizeInCAD()
{
	//const DxfLib::FDxfBlockData* FrameBlock = DxfLib::FDxfTempData::GetInstance()->GetBlockByName(UDSConstructionFrame::Frame_Default, TEXT(""));

	return FVector(4874, 3506, 0);
}
//bool UDSConstructionSubsystem::DoesSupportWorldType(const EWorldType::Type WorldType) const
//{
//	return WorldType == EWorldType::Game || WorldType == EWorldType::Editor || WorldType == EWorldType::PIE;
//}

void UDSConstructionSubsystem::LoadDxfTemplateFile(const FString& InFilePath)
{
	FString FilePath = FPaths::ConvertRelativePathToFull(InFilePath);

	DxfLib::FDxfTempData::GetInstance()->Clear();
	std::ifstream infile;

	infile.open(TCHAR_TO_ANSI(*FilePath));
	TSharedPtr<DxfLib::FDxfHeaderReader> HeaderReader = MakeShared<DxfLib::FDxfHeaderReader>();
	HeaderReader->LoadFile(infile);
	TSharedPtr<DxfLib::FDxfTablesReader> TableReader = MakeShared<DxfLib::FDxfTablesReader>();
	TableReader->LoadFile(infile);
	TSharedPtr<DxfLib::FDxfBlocksReader> BlockReader = MakeShared<DxfLib::FDxfBlocksReader>(/*"FrameTemplete"*/);
	BlockReader->LoadFile(infile);

	//for (auto NameIte : FDSConstructionFrame::AllFrameNames)
	//{
		//DxfLib::FDxfBlockData* FrameData = DxfLib::FDxfTempData::GetInstance()->GetBlockByName(NameIte, TEXT("FrameTempleta"));
		//if (FrameData != nullptr)
		//{
		//	ConstructionFrames.Add(MakeShared<FDSConstructionFrame>(NameIte));
		//}
	//}
}

void UDSConstructionSubsystem::LoadDxfFrameTemplateFile(const FString& InFilePath)
{

}

void UDSConstructionSubsystem::LoadDefaultDxfFrameTemplateFile()
{

}

//bool UDSConstructionSubsystem::BlockIsFrame(const FName& InBlockName)
//{
//	return UDSConstructionFrame::AllFrameNames.Contains(InBlockName);
//}

bool UDSConstructionSubsystem::IsBaseCupboardType(UDSCupboardModel* InModel)
{
	static const TArray<EDSModelType> BaseCupboardTypes = { EDSModelType::E_Custom_UpperCabinet,
		EDSModelType::E_Custom_BaseCabinet,EDSModelType::E_Custom_TallCabinet,EDSModelType::E_Custom_CornerCabinet,
		EDSModelType::E_Custom_Tatami,EDSModelType::E_Custom_CornerCutCabinet,EDSModelType::E_Custom_WallBoardCabinet
	};

	return InModel && BaseCupboardTypes.Contains(InModel->GetModelType());
}

bool UDSConstructionSubsystem::IsWallCupboardType(UDSCupboardModel* InModel)
{
	return InModel && InModel->GetModelType() == EDSModelType::E_Custom_WallCabinet;
}

void UDSConstructionSubsystem::GenRootCupboardConstructionData(UDSCupboardModel* InRootModel, UDSHouseAreaModel* InAreaModel, TArray<TSharedPtr<FDSConstructionData>>& OutConstructionDatas)
{
	TArray<FDSConstructionData*> ChildConstructionDatas;

	//根节点图纸数据
	FDSConstructionData* RootData = new FDSConstructionData();
	RootData->SelfUUID = InRootModel->GetUUID();
	RootData->Model = InRootModel;
	RootData->Node = InRootModel->GetComponentTreeDataRef();
	RootData->ModelType = InRootModel->GetModelType();
	RootData->LocalToWorld = InRootModel->GetProperty()->GetActualTransform();
	RootData->AreaUUID = InAreaModel ? InAreaModel->GetUUID() : FString();
	ChildConstructionDatas.Add(RootData);

	//获得根节点Model上，没有创建Model的子节点图纸数据
	TMap<TSharedPtr<FMultiComponentDataItem>, TArray<FDSPrimitive_MPolygon3D>> OutNodeData;
	UDSConstructionLibrary::GetConstructionPrimitive_NoModelNode(InRootModel, OutNodeData,
		UDSConstructionLibrary::FOnCheckIsRightNode::CreateUObject(this, &UDSConstructionSubsystem::OnCheckIsRightNode_Default));
	for (auto& Ite : OutNodeData)
	{
		if (Ite.Key == InRootModel->GetComponentTreeDataRef())
			continue;

		FDSConstructionData* NewData = new FDSConstructionData();
		NewData->ParentUUID = InRootModel->GetUUID();
		NewData->SelfUUID = Ite.Key->UUID;
		NewData->Model = nullptr;
		NewData->Node = Ite.Key;
		NewData->LocalToWorld = UDSCupboardLibrary::GetNodeTransform(InRootModel, NewData->Node);
		NewData->AreaUUID = InAreaModel ? InAreaModel->GetUUID() : FString();

		for (auto& PIte : Ite.Value)
		{
			NewData->Primitives.Add(TSharedPtr<FDSPrimitiveBase>(new FDSPrimitive_MPolygon3D(PIte)));
		}

		ChildConstructionDatas.Add(NewData);
	}

	//获取柜子的子节点图纸数据
	for (auto& ChildIte : InRootModel->GetModelInfo().ComponentInfoArr)
	{
		UDSCupboardModel* ChildCupboardModel = Cast<UDSCupboardModel>(ChildIte.ComponentModel);
		if (ChildCupboardModel == nullptr)
			continue;

		GenConstructionDataByChildNode(ChildCupboardModel, InAreaModel, ChildCupboardModel->GetComponentTreeDataRef(), ChildConstructionDatas);
	}

	if (OutNodeData.Find(RootData->Node) != nullptr)
	{
		auto Value = OutNodeData.Find(RootData->Node);
		for (auto& PIte : *Value)
		{
			RootData->Primitives.Add(TSharedPtr<FDSPrimitiveBase>(new FDSPrimitive_MPolygon3D(PIte)));
		}
	}

	for (auto Ite : ChildConstructionDatas)
	{
		OutConstructionDatas.Add(TSharedPtr<FDSConstructionData>(Ite));
	}
}

void UDSConstructionSubsystem::GenConstructionDataByChildNode(UDSCupboardModel* InModel, UDSHouseAreaModel* InAreaModel, TSharedPtr<FMultiComponentDataItem> InNode, TArray< FDSConstructionData*>& OutDatas)
{
	//UDSCupboardModel* ChildCupboardModel = UDSCupboardLibrary::GetModelByNodeUUID(InChildNode->UUID, InModel);

	FTransform WorldToProjective = FTransform::Identity;

	FDSConstructionData* NewData = new FDSConstructionData();
	NewData->ParentUUID = InModel->GetRootCupboardModel()->GetUUID();
	NewData->SelfUUID = InNode->UUID;
	NewData->Model = InModel;
	NewData->Node = InNode;
	NewData->ModelType = InModel->GetModelType();
	NewData->LocalToWorld = UDSConstructionLibrary::GetCupboardTransform(InModel);
	NewData->AreaUUID = InAreaModel ? InAreaModel->GetUUID() : FString();

	EDSModelType NodeModelType = UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(InNode->ModelType);

	if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXGTB"), InNode->ModelType))   //柜体板件
	{
		UDSConstructionLibrary::GetConstructionPrimitive_TheModel(InModel, NewData->Primitives);

		OutDatas.Add(NewData);
	}
	else if (NodeModelType == EDSModelType::E_Custom_Board && UDSConstructionLibrary::ChildNodeIsNoNoModelType(InNode))
	{
		UDSConstructionLibrary::GetConstructionPrimitive_TheModel(InModel, NewData->Primitives);

		OutDatas.Add(NewData);
	}
	else if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), InNode->ModelType))  //门板
	{
		NewData->LocalToWorld = UDSConstructionLibrary::GetCupboardTransformNoMirror(InModel);

		TArray<FDSPrimitive_MPolygon3D> OutMPolygon3D;
		UDSConstructionLibrary::CalcCupboard2DOutline(InModel, WorldToProjective, OutMPolygon3D);
		for (auto& Ite : OutMPolygon3D)
		{
			Ite.ColorID = 30;
			NewData->Primitives.Add(TSharedPtr<FDSPrimitiveBase>(new FDSPrimitive_MPolygon3D(Ite)));
		}

		OutDatas.Add(NewData);

		TMap<TSharedPtr<FMultiComponentDataItem>, TArray<FDSPrimitive_MPolygon3D>> OutNodeData;
		UDSConstructionLibrary::GetConstructionPrimitive_NoModelNode(InModel, OutNodeData, 
			UDSConstructionLibrary::FOnCheckIsRightNode::CreateLambda([](TSharedPtr<FMultiComponentDataItem> InNode) {
				return UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXLS"), InNode->ModelType);  //拉手
				}));

		//拉手
		for (auto NodeIte : InModel->GetComponentTreeDataRef()->ChildComponent)
		{
			if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXLS"), NodeIte->ModelType))  //拉手
			{
				auto TempFindNode = OutNodeData.Find(NodeIte);

				if (TempFindNode != nullptr)
				{
					FDSConstructionData* NewData = new FDSConstructionData();
					NewData->ParentUUID = InModel->GetRootCupboardModel()->GetUUID();
					NewData->SelfUUID = NodeIte->UUID;
					NewData->Model = nullptr;
					NewData->Node = NodeIte;
					NewData->LocalToWorld = UDSCupboardLibrary::GetNodeTransform(InModel->GetRootCupboardModel(), NodeIte);
					NewData->AreaUUID = InAreaModel ? InAreaModel->GetUUID() : FString();

					for (auto& Ite : *TempFindNode)
					{
						Ite.ColorID = 30;
						NewData->Primitives.Add(TSharedPtr<FDSPrimitiveBase>(new FDSPrimitive_MPolygon3D(Ite)));
					}

					OutDatas.Add(NewData);
				}
			}
		}
	}
	else if (NodeModelType == EDSModelType::E_Custom_Functional_HangingRod || NodeModelType == EDSModelType::E_Custom_LayoutDoor_Board)
	{
		NewData->LocalToWorld = UDSConstructionLibrary::GetCupboardTransformNoMirror(InModel);

		//挂衣杆 户型门板
		TArray<FDSPrimitive_MPolygon3D> OutMPolygon3D;
		UDSConstructionLibrary::CalcCupboard2DOutline(InModel, WorldToProjective, OutMPolygon3D);
		for (auto& Ite : OutMPolygon3D)
		{
			NewData->Primitives.Add(TSharedPtr<FDSPrimitiveBase>(new FDSPrimitive_MPolygon3D(Ite)));
		}
		OutDatas.Add(NewData);
	}
	else if (NodeModelType == EDSModelType::E_Custom_Leg)
	{
		//地脚
		NewData->LocalToWorld = UDSConstructionLibrary::GetCupboardTransformNoMirror(InModel);
	}
	else
	{
		TMap<TSharedPtr<FMultiComponentDataItem>, TArray<FDSPrimitive_MPolygon3D>> OutNodeData;
		UDSConstructionLibrary::GetConstructionPrimitive_NoModelNode(InModel, OutNodeData,
			UDSConstructionLibrary::FOnCheckIsRightNode::CreateUObject(this,&UDSConstructionSubsystem::OnCheckIsRightNode_Default));

		UDSCupboardModel* RootModel = InModel->GetRootCupboardModel();

		for (auto& Ite : OutNodeData)
		{
			FDSConstructionData* NewData = new FDSConstructionData();
			NewData->ParentUUID = RootModel->GetUUID();
			NewData->SelfUUID = Ite.Key->UUID;
			NewData->Model = nullptr;
			NewData->Node = Ite.Key;
			NewData->LocalToWorld = UDSCupboardLibrary::GetNodeTransform(RootModel, NewData->Node);
			NewData->AreaUUID = InAreaModel ? InAreaModel->GetUUID() : FString();

			for (auto& PIte : Ite.Value)
			{
				NewData->Primitives.Add(TSharedPtr<FDSPrimitiveBase>(new FDSPrimitive_MPolygon3D(PIte)));
			}

			OutDatas.Add(NewData);
		}
	}

	for (auto ChildModel : InModel->GetModelInfoRef().ComponentInfoArr)
	{
		if (ChildModel.ComponentModel == nullptr)
			continue;

		UDSCupboardModel* ChildCupboardModel = Cast<UDSCupboardModel>(ChildModel.ComponentModel);
		GenConstructionDataByChildNode(ChildCupboardModel,InAreaModel, ChildCupboardModel->GetComponentTreeDataRef(), OutDatas);
	}

}

void UDSConstructionSubsystem::GenHomeConstructionData(UDSHouseAreaModel* InAreaModel, TArray<TSharedPtr<FDSConstructionData>>& OutConstructionDatas)
{
	//墙
	for (auto& WallIte : InAreaModel->GetWallModels())
	{
		TSharedPtr<FDSConstructionData> ConstructionData = NewConstructionData(WallIte.Key, TEXT(""), InAreaModel->GetUUID());

		UDSConstructionLibrary::GetConstructionPrimitive_TheModel(WallIte.Key, ConstructionData->Primitives);

		if (ConstructionData->IsHasVaildPrimitive())
		{
			OutConstructionDatas.Add(ConstructionData);
		}
	}

	//房间分割线
	for (auto& SplitLineIte : InAreaModel->GetSplitLineModels())
	{
		
	}
}

TSharedPtr<FDSConstructionData> UDSConstructionSubsystem::NewConstructionData(UDSBaseModel* InModel, const FString& ParentUUID, const FString InAreaUUID)
{
	FDSConstructionData* ConstructionData = new FDSConstructionData();
	ConstructionData->SelfUUID = InModel->GetUUID();
	ConstructionData->Model = InModel;
	ConstructionData->ModelType = InModel->GetModelType();
	ConstructionData->LocalToWorld = InModel->GetProperty()->GetActualTransform();
	ConstructionData->AreaUUID = InAreaUUID;
	ConstructionData->ParentUUID = ParentUUID;
	return TSharedPtr< FDSConstructionData>(ConstructionData);
}

bool UDSConstructionSubsystem::OnCheckIsRightNode_Default(TSharedPtr<FMultiComponentDataItem> InNode)
{
	if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXGTB"), InNode->ModelType)
		|| UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(InNode->ModelType) == EDSModelType::E_Custom_Functional_HangingRod
		|| UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXGT"), InNode->ModelType))
	{
		return true;
	}

	return false;
}

void UDSConstructionSubsystem::GenFrontGroupModel(TArray<UDSCupboardModel*> InModels, TArray<FDSFrontGroupModels>& OutGroups)
{
	if (InModels.IsEmpty())
		return;

	FTransform T = InModels[0]->GetProperty()->GetActualTransform();
	FVector XDir = T.GetUnitAxis(EAxis::X);
	FVector YDir = T.GetUnitAxis(EAxis::Y);
	FVector ZDir = T.GetUnitAxis(EAxis::Z);

	FVector ForwardDir = InModels[0]->GetProperty()->GetActualTransform().GetUnitAxis(EAxis::Y);
	if (InModels.Num() == 1)
	{
		FDSFrontGroupModels GroupModels;
		GroupModels.Models.Add(InModels[0]);
		GroupModels.WroldDir = ForwardDir;
		GroupModels.WorldToProjective = FTransform(ZDir, XDir, YDir, FVector::ZeroVector).Inverse();
		OutGroups.Add(MoveTemp(GroupModels));
		return;
	}

	struct FDSTempData
	{

	};

	FDSFrontGroupModels GroupModels;
	GroupModels.Models.Add(InModels[0]);
	GroupModels.WroldDir = ForwardDir;
	GroupModels.WorldToProjective = FTransform(ZDir, XDir, YDir, FVector::ZeroVector).Inverse();

	float TempValue = FMath::Cos(FMath::DegreesToRadians(0.1));

	for (int32 i = 1; i < InModels.Num(); ++i)
	{
		FVector CurDir = InModels[i]->GetProperty()->GetActualTransform().GetUnitAxis(EAxis::Y);

		if (FVector::DotProduct(ForwardDir, CurDir) > TempValue)
		{
			GroupModels.Models.Add(InModels[i]);
		}
	}

	InModels.RemoveAll([&GroupModels](const UDSCupboardModel* InA) { return GroupModels.Models.Contains(InA); });
	OutGroups.Add(MoveTemp(GroupModels));

	GenFrontGroupModel(InModels, OutGroups);
}

void UDSConstructionSubsystem::UpdatePaperPos()
{
}


void UDSConstructionSubsystem::QueryResourecList(const TArray<TSharedPtr<FDSConstructionData>>& InConstructionDatas)
{
	TMap<FString, EDSResourceType> Params;

	for (auto CupboardIte : InConstructionDatas)
	{
		auto Node = CupboardIte->Node;
		if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), Node->ModelType))
		{
			//门板颜色
			FString ParameterValue = Node->GetParameterValue(TEXT("DZCZ"), TEXT(""));
			if (!ParameterValue.IsEmpty())
			{
				TSharedPtr<FDSResourceInfo> ResInfo = UDSResourceSubsystem::GetInstance()->FindResourceFromCache(ParameterValue);
				if (!ResInfo.IsValid())
				{
					Params.Add(ParameterValue, EDSResourceType::Material);
				}
			}

			//门型
			TSharedPtr<FDSResourceInfo> ResInfo = UDSResourceSubsystem::GetInstance()->FindResourceFromCache(Node->ComponentID.GetFormattedValue());
			if (ResInfo.IsValid())
			{
				Params.Add(Node->ComponentID.GetFormattedValue(), EDSResourceType::Custom);
			}
		}
		else if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXGTB"), Node->ModelType))
		{
			//柜体板颜色
			FString ParameterValue = Node->GetParameterValue(TEXT("DZCZ"), TEXT(""));
			if (!ParameterValue.IsEmpty())
			{
				TSharedPtr<FDSResourceInfo> ResInfo = UDSResourceSubsystem::GetInstance()->FindResourceFromCache(ParameterValue);
				if (ResInfo.IsValid())
				{
					Params.Add(ParameterValue, EDSResourceType::Material);
				}
			}
		}
		else if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXLS"), Node->ModelType))
		{
			//拉手型号
			TSharedPtr<FDSResourceInfo> ResInfo = UDSResourceSubsystem::GetInstance()->FindResourceFromCache(Node->ComponentID.GetFormattedValue());
			if (ResInfo.IsValid())
			{
				Params.Add(Node->ComponentID.GetFormattedValue(), EDSResourceType::Custom);
			}
		}
	}

	if (Params.IsEmpty())
	{
		ExportAllPapers();
		return;
	}

	FOnQueryResourceListByFolderIdsCompletedDelegate CallBack;
	CallBack.BindDynamic(this, &UDSConstructionSubsystem::OnQueryResourceListByIdsCompletedCallback);
	//UDSNetworkSubsystem::GetInstance()->SendQueryResourceListByIdsRequest(Params, CallBack);
	UDSNetworkSubsystem::GetInstance()->SendQueryResourceListByFolderIdsRequest(Params, CallBack);
}

void UDSConstructionSubsystem::OnQueryResourceListByIdsCompletedCallback(const TArray<FDSResourceInfo>& ItemList)
{
	for (auto& ResIte : ItemList)
	{
		UDSResourceSubsystem::GetInstance()->AddResourceCache(ResIte);
	}

	ExportAllPapers();
}


