﻿// Fill out your copyright notice in the Description page of Project Settings.


#include "FunctionLibrary/CatalogExpressionFunctionLibrary.h"

#include "Lexer/ExpressionLexer.h"
#include "Parser/ExpressionParser.h"

#include <chrono>

#include "Decimal.h"
#include "DecimalMath.h"

FString UCatalogExpressionFunctionLibrary::GetTokenTypeDisplayName(EExpressionLexerTokenType InType)
{
	switch (InType)
	{
	case EExpressionLexerTokenType::Unknown:				return TEXT("Unknown");
	case EExpressionLexerTokenType::Number:					return TEXT("Number");
	case EExpressionLexerTokenType::Float:					return TEXT("Float");
	case EExpressionLexerTokenType::CharLiteral:			return TEXT("CharLiteral");
	case EExpressionLexerTokenType::String:					return TEXT("String");
	case EExpressionLexerTokenType::Identifier:				return TEXT("Identifier");
	case EExpressionLexerTokenType::MonocularOperator:		return TEXT("MonocularOperator");
	case EExpressionLexerTokenType::BinocularOperator:		return TEXT("BinocularOperator");
	case EExpressionLexerTokenType::Keyword:				return TEXT("Keyword");
	case EExpressionLexerTokenType::Comment:				return TEXT("Comment");
	case EExpressionLexerTokenType::LeftParen:				return TEXT("LeftParen");
	case EExpressionLexerTokenType::RightParen:				return TEXT("RightParen");
	case EExpressionLexerTokenType::LeftBrace:				return TEXT("LeftBrace");
	case EExpressionLexerTokenType::RightBrace:				return TEXT("RightBrace");
	case EExpressionLexerTokenType::Comma:					return TEXT("Comma");
	case EExpressionLexerTokenType::Semicolon:				return TEXT("Semicolon");
	case EExpressionLexerTokenType::LineFeed:				return TEXT("LineFeed");
	case EExpressionLexerTokenType::Whitespace:				return TEXT("Whitespace");
	case EExpressionLexerTokenType::EndOfFile:				return TEXT("EndOfFile");
	default:												return TEXT("Unknown");
	}
}

EExpressionFunctionType UCatalogExpressionFunctionLibrary::GetExpressionFunctionType(const FString& InFunctionName)
{
	FString UpperName = InFunctionName.ToUpper();
	if (UpperName.Equals(TEXT("IF")))
	{
		return EExpressionFunctionType::If;
	}
	else if (UpperName.Equals(TEXT("COND")))
	{
		return EExpressionFunctionType::Condition;
	}
	else if (UpperName.Equals(TEXT("MAX")))
	{
		return EExpressionFunctionType::Max;
	}
	else if (UpperName.Equals(TEXT("MIN")))
	{
		return EExpressionFunctionType::Min;
	}
	else if (UpperName.Equals(TEXT("SIN")))
	{
		return EExpressionFunctionType::Sin;
	}
	else if (UpperName.Equals(TEXT("COS")))
	{
		return EExpressionFunctionType::Cos;
	}
	else if (UpperName.Equals(TEXT("SQRT")))
	{
		return EExpressionFunctionType::Sqrt;
	}
	else if (UpperName.Equals(TEXT("ABS")))
	{
		return EExpressionFunctionType::Abs;
	}
	else if (UpperName.Equals(TEXT("COMB")))
	{
		return EExpressionFunctionType::Combine;
	}

	return EExpressionFunctionType::Unknow;
}

FString UCatalogExpressionFunctionLibrary::EvaluateExpressionNode(const TSharedPtr<FAstNode>& Node, const TMap<FString, FString>& SymbolTables)
{
	if (!Node.IsValid())
	{
		throw std::runtime_error(TCHAR_TO_UTF8(TEXT("Invalid node.")));
	}

	switch (Node->GetType())
	{
	case EAstNodeType::Number:
		{
			TSharedPtr<FAstNodeNumber> NumberNode = StaticCastSharedPtr<FAstNodeNumber>(Node);
			return NumberNode->Value;
		}

	case EAstNodeType::Variable:
		{
			TSharedPtr<FAstNodeVariable> VariableNode = StaticCastSharedPtr<FAstNodeVariable>(Node);
			if (SymbolTables.Contains(VariableNode->Name))
			{
				return SymbolTables[VariableNode->Name];
			}
			else
			{
				throw std::runtime_error(TCHAR_TO_UTF8(*FString::Printf(TEXT("Undefined variable: %s"), *VariableNode->Name)));
			}
		}

	case EAstNodeType::UnaryOp:
		{
			TSharedPtr<FAstNodeUnaryOp> UnaryNode = StaticCastSharedPtr<FAstNodeUnaryOp>(Node);
			FString Operand = EvaluateExpressionNode(UnaryNode->Operand, SymbolTables);

			return EvaluateUnaryOperator(UnaryNode->Operator, Operand);
		}

	case EAstNodeType::BinaryOp:
		{
			TSharedPtr<FAstNodeBinaryOp> BinaryNode = StaticCastSharedPtr<FAstNodeBinaryOp>(Node);
			FString Left = EvaluateExpressionNode(BinaryNode->LeftOperand, SymbolTables);
			FString Right = EvaluateExpressionNode(BinaryNode->RightOperand, SymbolTables);

			return EvaluateBinaryOperator(Left, BinaryNode->Operator, Right);
		}

	case EAstNodeType::FunctionCall:
		{
			TSharedPtr<FAstNodeFunctionCall> FunctionNode = StaticCastSharedPtr<FAstNodeFunctionCall>(Node);
			
			EExpressionFunctionType FunctionType = GetExpressionFunctionType(FunctionNode->Name);
			if (FunctionType == EExpressionFunctionType::Unknow)
			{
				throw std::runtime_error(TCHAR_TO_UTF8(*FString::Printf(TEXT("Unsupported function: %s"), *FunctionNode->Name)));
			}
			
			switch (FunctionType)
			{
			case EExpressionFunctionType::If:
				return EvaluateFunctionCall_If(FunctionNode->Arguments, SymbolTables);
			case EExpressionFunctionType::Condition:
				return EvaluateFunctionCall_Condition(FunctionNode->Arguments, SymbolTables);
			case EExpressionFunctionType::Max:
				return EvaluateFunctionCall_Max(FunctionNode->Arguments, SymbolTables);
			case EExpressionFunctionType::Min:
				return EvaluateFunctionCall_Min(FunctionNode->Arguments, SymbolTables);
			case EExpressionFunctionType::Sin:
				return EvaluateFunctionCall_Sin(FunctionNode->Arguments, SymbolTables);
			case EExpressionFunctionType::Cos:
				return EvaluateFunctionCall_Cos(FunctionNode->Arguments, SymbolTables);
			case EExpressionFunctionType::Sqrt:
				return EvaluateFunctionCall_Sqrt(FunctionNode->Arguments, SymbolTables);
			case EExpressionFunctionType::Abs:
				return EvaluateFunctionCall_Abs(FunctionNode->Arguments, SymbolTables);
			case EExpressionFunctionType::Combine:
				return EvaluateFunctionCall_Combine(FunctionNode->Arguments, SymbolTables);
			default:
				throw std::runtime_error(TCHAR_TO_UTF8(*FString::Printf(TEXT("Unsupported function: %s"), *FunctionNode->Name)));
				break;
			}
		}

	case EAstNodeType::String:
		{
			TSharedPtr<FAstNodeString> StringNode = StaticCastSharedPtr<FAstNodeString>(Node);
			return StringNode->Value;
		}
	default:
		throw std::runtime_error(TCHAR_TO_UTF8(TEXT("Unsupported node type in evaluation.")));
	}
}

FString UCatalogExpressionFunctionLibrary::EvaluateUnaryOperator(const FString& Operator, const FString& Operand)
{
	if (Operator == TEXT("-"))
	{
		return (-FDecimal(Operand)).ToString(16);
	}
	else if (Operator == TEXT("+"))
	{
		return Operand;
	}
	else if (Operator == TEXT("!"))
	{
		return FDecimal(Operand) == FDecimal(0) ? TEXT("1") : TEXT("0");
	}
	else if (Operator == TEXT("~"))
	{
		return FString::Printf(TEXT("%lld"), ~FCString::Atoi64(*Operand));
	}

	throw std::runtime_error(TCHAR_TO_UTF8(*FString::Printf(TEXT("Unsupported unary operator: %s"), *Operator)));
}

FString UCatalogExpressionFunctionLibrary::EvaluateBinaryOperator(const FString& Left, const FString& Operator, const FString& Right)
{
	if (Operator == TEXT("+"))
	{
		return (FDecimal(Left) + FDecimal(Right)).ToString(16);
	}
	else if (Operator == TEXT("-"))
	{
		return (FDecimal(Left) - FDecimal(Right)).ToString(16);
	}
	else if (Operator == TEXT("*"))
	{
		return (FDecimal(Left) * FDecimal(Right)).ToString(16);
	}
	else if (Operator == TEXT("/"))
	{
		FDecimal RightDecimal(Right);
		if (RightDecimal.IsNearlyEqual(0))
		{
			throw std::runtime_error(TCHAR_TO_UTF8(TEXT("Dividend cannot be zero.")));
		}
		return (FDecimal(Left) / RightDecimal).ToString(16);
	}
	else if (Operator == TEXT("=="))
	{
		return FDecimal(Left) == FDecimal(Right) ? TEXT("1") : TEXT("0");
	}
	else if (Operator == TEXT("!="))
	{
		return FDecimal(Left) != FDecimal(Right) ? TEXT("1") : TEXT("0");
	}
	else if (Operator == TEXT("<"))
	{
		return FDecimal(Left) < FDecimal(Right) ? TEXT("1") : TEXT("0");
	}
	else if (Operator == TEXT(">"))
	{
		return FDecimal(Left) > FDecimal(Right) ? TEXT("1") : TEXT("0");
	}
	else if (Operator == TEXT("<="))
	{
		return FDecimal(Left) <= FDecimal(Right) ? TEXT("1") : TEXT("0");
	}
	else if (Operator == TEXT(">="))
	{
		return FDecimal(Left) >= FDecimal(Right) ? TEXT("1") : TEXT("0");
	}
	else if (Operator == TEXT("&&"))
	{
		FDecimal ZeroDecimal(0);
		return (FDecimal(Left) != ZeroDecimal && FDecimal(Right) != ZeroDecimal) ? TEXT("1") : TEXT("0");
	}
	else if (Operator == TEXT("||"))
	{
		FDecimal ZeroDecimal(0);
		return (FDecimal(Left) != ZeroDecimal || FDecimal(Right) != ZeroDecimal) ? TEXT("1") : TEXT("0");
	}
	else if (Operator == TEXT("&"))
	{
		return FString::FromInt(FCString::Atoi(*Left) & FCString::Atoi(*Right));
	}
	else if (Operator == TEXT("|"))
	{
		return FString::FromInt(FCString::Atoi(*Left) | FCString::Atoi(*Right));
	}
	else if (Operator == TEXT("^"))
	{
		return FDecimalMath::Power(FDecimal(Left), FDecimal(Right)).ToString(16);
	}
	else if (Operator == TEXT("^="))
	{
		int64 Result = FCString::Atoi64(*Left);
		Result ^= FCString::Atoi64(*Right);
		return FString::Printf(TEXT("%lld"), Result);
	}
	else if (Operator == TEXT("%"))
	{
		return FDecimalMath::RemainderCustom(FDecimal(Left), FDecimal(Right)).ToString(16);
	}
	
	throw std::runtime_error(TCHAR_TO_UTF8(*FString::Printf(TEXT("Unsupported binary operator: %s"), *Operator)));
}

FString UCatalogExpressionFunctionLibrary::EvaluateFunctionCall_If(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, FString>& SymbolTables)
{
	if (InArgs.Num() != 3)
	{
		throw std::runtime_error(TCHAR_TO_UTF8(*FString::Printf(TEXT("Wrong number of parameters of xx function. (%d)"), InArgs.Num())));
	}

	FString Expr = EvaluateExpressionNode(InArgs[0], SymbolTables);
	if (FDecimal(Expr) != 0)
	{
		return EvaluateExpressionNode(InArgs[1], SymbolTables);
	}
	else
	{
		return EvaluateExpressionNode(InArgs[2], SymbolTables);
	}
}

FString UCatalogExpressionFunctionLibrary::EvaluateFunctionCall_Condition(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, FString>& SymbolTables)
{
	if (InArgs.Num() == 3)
	{
		FString Expr = EvaluateExpressionNode(InArgs[0], SymbolTables);
		if (FDecimal(Expr) != 0)
		{
			return EvaluateExpressionNode(InArgs[1], SymbolTables);
		}
		else
		{
			return EvaluateExpressionNode(InArgs[2], SymbolTables);
		}
	}
	else if (InArgs.Num() > 3 && (InArgs.Num() & 1))
	{
		for (int32 Index = 0; Index < InArgs.Num() - 1; Index += 2)
		{
			FString Expr = EvaluateExpressionNode(InArgs[Index], SymbolTables);
			if (FDecimal(Expr) != 0)
			{
				return EvaluateExpressionNode(InArgs[Index + 1], SymbolTables);
			}
		}

		return EvaluateExpressionNode(InArgs.Last(), SymbolTables);
	}
	else
	{
		throw std::runtime_error(TCHAR_TO_UTF8(*FString::Printf(TEXT("Wrong number of parameters of xx function. (%d)"), InArgs.Num())));
	}
}

FString UCatalogExpressionFunctionLibrary::EvaluateFunctionCall_Max(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, FString>& SymbolTables)
{
	if (InArgs.Num() != 2)
	{
		throw std::runtime_error(TCHAR_TO_UTF8(*FString::Printf(TEXT("Wrong number of parameters of xx function. (%d)"), InArgs.Num())));
	}

	FString Left = EvaluateExpressionNode(InArgs[0], SymbolTables);
	FString Right = EvaluateExpressionNode(InArgs[1], SymbolTables);
	
	return FDecimal(Left) > FDecimal(Right) ? Left : Right;
}

FString UCatalogExpressionFunctionLibrary::EvaluateFunctionCall_Min(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, FString>& SymbolTables)
{
	if (InArgs.Num() != 2)
	{
		throw std::runtime_error(TCHAR_TO_UTF8(*FString::Printf(TEXT("Wrong number of parameters of xx function. (%d)"), InArgs.Num())));
	}

	FString Left = EvaluateExpressionNode(InArgs[0], SymbolTables);
	FString Right = EvaluateExpressionNode(InArgs[1], SymbolTables);
	
	return FDecimal(Left) < FDecimal(Right) ? Left : Right;
}

FString UCatalogExpressionFunctionLibrary::EvaluateFunctionCall_Sin(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, FString>& SymbolTables)
{
	if (InArgs.Num() != 1)
	{
		throw std::runtime_error(TCHAR_TO_UTF8(*FString::Printf(TEXT("Wrong number of parameters of xx function. (%d)"), InArgs.Num())));
	}

	FString Val = EvaluateExpressionNode(InArgs[0], SymbolTables);
	return FDecimalMath::Sin(FDecimal(Val)).ToString(16);
}

FString UCatalogExpressionFunctionLibrary::EvaluateFunctionCall_Cos(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, FString>& SymbolTables)
{
	if (InArgs.Num() != 1)
	{
		throw std::runtime_error(TCHAR_TO_UTF8(*FString::Printf(TEXT("Wrong number of parameters of xx function. (%d)"), InArgs.Num())));
	}

	FString Val = EvaluateExpressionNode(InArgs[0], SymbolTables);
	return FDecimalMath::Cos(FDecimal(Val)).ToString(16);
}

FString UCatalogExpressionFunctionLibrary::EvaluateFunctionCall_Sqrt(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, FString>& SymbolTables)
{
	if (InArgs.Num() != 1)
	{
		throw std::runtime_error(TCHAR_TO_UTF8(*FString::Printf(TEXT("Wrong number of parameters of xx function. (%d)"), InArgs.Num())));
	}

	FString Val = EvaluateExpressionNode(InArgs[0], SymbolTables);
	return FDecimalMath::Sqrt(FDecimal(Val)).ToString(16);
}

FString UCatalogExpressionFunctionLibrary::EvaluateFunctionCall_Abs(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, FString>& SymbolTables)
{
	if (InArgs.Num() != 1)
	{
		throw std::runtime_error(TCHAR_TO_UTF8(*FString::Printf(TEXT("Wrong number of parameters of xx function. (%d)"), InArgs.Num())));
	}

	FString Val = EvaluateExpressionNode(InArgs[0], SymbolTables);
	return FDecimalMath::Abs(FDecimal(Val)).ToString(16);
}

FString UCatalogExpressionFunctionLibrary::EvaluateFunctionCall_Combine(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, FString>& SymbolTables)
{
	FString Result;
	for (const FAstNodePtr& Arg : InArgs)
	{
		Result.Append(EvaluateExpressionNode(Arg, SymbolTables));
	}
	return Result;
}

void UCatalogExpressionFunctionLibrary::TestCatalogExpressionLexer(const FString& InExpression)
{
	UE_LOG(LogTemp, Log, TEXT("================================ Lexer Testing ================================"));
	TArray<FExpressionLexerToken> Tokens;
	FString Error;
	
	std::chrono::steady_clock::time_point StartTime = std::chrono::steady_clock::now();

	FExpressionLexer Lexer;
	Lexer.SetContent(InExpression);
	Lexer.Tokenize(Tokens, Error);

	std::chrono::nanoseconds Duration = std::chrono::duration_cast<std::chrono::nanoseconds>(std::chrono::steady_clock::now() - StartTime);

	for (const FExpressionLexerToken& Token : Tokens)
	{
		UE_LOG(LogTemp, Log, TEXT("%s"), *Token.ToString());
	}

	UE_LOG(LogTemp, Log, TEXT("One FExpressionLexer::Tokenize time - %lld ns"), Duration.count());

	UE_LOG(LogTemp, Log, TEXT("================================ Stress Testing ================================"));
	std::chrono::steady_clock::time_point StressStart = std::chrono::steady_clock::now();
	for (int32 Index = 0; Index < 10000; ++Index)
	{
		TArray<FExpressionLexerToken> TestTokens;
		FString TestError;

		FExpressionLexer TestLexer;
		TestLexer.SetContent(InExpression);
		TestLexer.Tokenize(Tokens, Error);
	}

	Duration = std::chrono::duration_cast<std::chrono::nanoseconds>(std::chrono::steady_clock::now() - StressStart);
	UE_LOG(LogTemp, Log, TEXT("FExpressionLexer::Tokenize stress testing (10000 loop) time - %lld ns"), Duration.count());

	UE_LOG(LogTemp, Log, TEXT("================================ Parser Testing ================================"));

	std::chrono::steady_clock::time_point ParseStart = std::chrono::steady_clock::now();

	FExpressionParser Parser;
	TArray<FAstNodePtr> AstNodes;
	FString ParseError;
	if (!Parser.Parse(InExpression, AstNodes, ParseError))
	{
		UE_LOG(LogTemp, Log, TEXT("Parse failed : %s"), *ParseError);
	}
	else
	{
		Duration = std::chrono::duration_cast<std::chrono::nanoseconds>(std::chrono::steady_clock::now() - ParseStart);
		UE_LOG(LogTemp, Log, TEXT("Parse expression to ast nodes takes %lld ns"), Duration.count());
		for (int32 Index = 0; Index < AstNodes.Num(); ++Index)
		{
			PrintAstNode(AstNodes[Index]);
		}
	}

	Duration = std::chrono::duration_cast<std::chrono::nanoseconds>(std::chrono::steady_clock::now() - ParseStart);
	
	std::chrono::steady_clock::time_point EvaluationStart = std::chrono::steady_clock::now();
	TMap<FString, FString> SymbolTables = { {TEXT("W"), TEXT("1000")}, {TEXT("SV"), TEXT("600005")} };
	for (int32 Index = 0; Index < AstNodes.Num(); ++Index)
	{
		std::chrono::steady_clock::time_point PerEvaluationStart = std::chrono::steady_clock::now();
		FString Value =EvaluateExpressionNode(AstNodes[Index], SymbolTables);

		Duration = std::chrono::duration_cast<std::chrono::nanoseconds>(std::chrono::steady_clock::now() - PerEvaluationStart);
		UE_LOG(LogTemp, Log, TEXT("Evaluate expression [#%d] takes %lld ns, Value : %s"), Index, Duration.count(), *Value);
	}

	Duration = std::chrono::duration_cast<std::chrono::nanoseconds>(std::chrono::steady_clock::now() - EvaluationStart);
	UE_LOG(LogTemp, Log, TEXT("Evaluate all expressions takes %lld ns"), Duration.count());
	
	UE_LOG(LogTemp, Log, TEXT("Total time - %lld ns"), Duration.count());
	UE_LOG(LogTemp, Log, TEXT("================================ End Testing ================================"));
}

void UCatalogExpressionFunctionLibrary::PrintAstNode(const TSharedPtr<FAstNode>& Node, int32 IndentLevel)
{
    const FString Indent = FString::ChrN(IndentLevel * 2, ' '); // 2 spaces per indent level

    switch (Node->GetType())
    {
    case EAstNodeType::Number:
        {
            const FAstNodeNumber* NumberNode = static_cast<FAstNodeNumber*>(Node.Get());
            UE_LOG(LogTemp, Display, TEXT("%sNumber: %s"), *Indent, *NumberNode->Value);
        }
        break;

    case EAstNodeType::String:
        {
            const FAstNodeString* StringNode = static_cast<FAstNodeString*>(Node.Get());
            UE_LOG(LogTemp, Display, TEXT("%sString: \"%s\""), *Indent, *StringNode->Value);
        }
        break;

    case EAstNodeType::Variable:
        {
            const FAstNodeVariable* VarNode = static_cast<FAstNodeVariable*>(Node.Get());
            UE_LOG(LogTemp, Display, TEXT("%sVariable: %s"), *Indent, *VarNode->Name);
        }
        break;

    case EAstNodeType::UnaryOp:
        {
            const FAstNodeUnaryOp* UnaryNode = static_cast<FAstNodeUnaryOp*>(Node.Get());
            UE_LOG(LogTemp, Display, TEXT("%sUnaryOp: %s"), *Indent, *UnaryNode->Operator);
            PrintAstNode(UnaryNode->Operand, IndentLevel + 1);
        }
        break;

    case EAstNodeType::BinaryOp:
        {
            const FAstNodeBinaryOp* BinaryNode = static_cast<FAstNodeBinaryOp*>(Node.Get());
            UE_LOG(LogTemp, Display, TEXT("%sBinaryOp: %s"), *Indent, *BinaryNode->Operator);
            PrintAstNode(BinaryNode->LeftOperand, IndentLevel + 1);
            PrintAstNode(BinaryNode->RightOperand, IndentLevel + 1);
        }
        break;

    case EAstNodeType::FunctionCall:
        {
            const FAstNodeFunctionCall* FuncNode = static_cast<FAstNodeFunctionCall*>(Node.Get());
            UE_LOG(LogTemp, Display, TEXT("%sFunctionCall: %s"), *Indent, *FuncNode->Name);
            for (const FAstNodePtr& Arg : FuncNode->Arguments)
            {
                PrintAstNode(Arg, IndentLevel + 1);
            }
        }
        break;

    case EAstNodeType::Comment:
        {
            const FAstNodeComment* CommentNode = static_cast<FAstNodeComment*>(Node.Get());
            UE_LOG(LogTemp, Display, TEXT("%sComment at (%d,%d): %s"),
                   *Indent,
                   CommentNode->Position.X,
                   CommentNode->Position.Y,
                   *CommentNode->Content);
        }
        break;

    default:
        UE_LOG(LogTemp, Display, TEXT("%sUnknown AST node type"), *Indent);
        break;
    }
}
