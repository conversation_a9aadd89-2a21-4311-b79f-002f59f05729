﻿#pragma once

#include "CoreMinimal.h"
#include "DSConfigSubsystem.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogDSCustomConfig, Log, All);

namespace DSSetting
{
	namespace Normal
	{
		enum class EGeneralType : uint8
		{
			E_GlobalUnit = 0, E_AutoSave
		};

		struct FGeneralSetting
		{
			int32 GlobalUnit; // 0 : mm ; 1 : m
			int32 AutoSave; // 5; 15; 30; -1(no auto save)

			FGeneralSetting()
				: GlobalUnit(0)
				  , AutoSave(5) {}
		};
	}

	namespace TWD
	{
		enum class ETWDType : uint8
		{
			E_WallLock = 0,
			E_SizeShow,
			E_BackgridShow,
			E_AreaNameShow,
			E_WallAngleShow,
			E_AffectSnapDis,
			E_DragSnapDistance
		};

		struct FTwoDimensionInfo
		{
			bool bWallLock;

			bool bSizeShow;

			bool bBackgridShow;

			bool bAreaNameShow;

			bool bWallAngleShow;

			float AffectSnapDis;

			float DragSnapDistance;

			FTwoDimensionInfo()
				: bWallLock(true)
				  , bSizeShow(true)
				  , bBackgridShow(true)
				  , bAreaNameShow(true)
				  , bWallAngleShow(true)
				  , AffectSnapDis(240.0f)
				  , DragSnapDistance(240.0f) {}
		};
	}

	namespace THD
	{
		enum class ETHDType : uint8
		{
			E_SLGene = 0, E_TLGene
		};

		struct FThreeDimensionInfo
		{
			bool bSBLineGenerate; // 踢脚线是否生成 Skirtline Generate
			bool bTALineGenerate; // 顶角线是否生成 Topline Generate

			FThreeDimensionInfo()
				: bSBLineGenerate(true)
				  , bTALineGenerate(true) {}
		};
	}

	namespace Wall
	{
		enum class EWallType : uint8
		{
			E_DefaultWallH = 0,
			E_OutSideWallT,
			E_InnerSideWallT,
			E_DrawWallType
		};

		struct FWallInfo
		{
			int32 DrawWallType; //0: MiddleLine 1: left *2: right
			float DefaultWallHeight;

			float OutSideWallThick;

			float InnerSideWallThick;

			FWallInfo()
				: DrawWallType(0)
				  , DefaultWallHeight(2800.0f)
				  , OutSideWallThick(240.0f)
				  , InnerSideWallThick(120.0f) {}
		};
	}

	namespace Custom
	{
		enum class ECustomType : uint8
		{
			E_DefaultSnapD = 0, 
			E_AngleShow,
			E_ShowPartParams
		};

		struct FCustomInfo
		{
			float DefaultSnapDis;
			float SnapDisMin;
			float SnapDisMax;

			bool bRotAngleShow;

			bool bShowPartParams;

			FCustomInfo()
				: DefaultSnapDis(50.0f)
				, SnapDisMin(1.0f)
				, SnapDisMax(500.0f)
				, bRotAngleShow(true)
				, bShowPartParams(true)
			{}
		};
	}

	namespace CameraSetting
	{
		struct FCameraSettingInfo
		{
			FVector2d Speed;

			bool bViewLocked;

			float InFOV;

			FCameraSettingInfo() :
				Speed(FVector2d(1000, 1000)),
				bViewLocked(false),
				InFOV(90.f) {}
		};
	}

}

UCLASS(Blueprintable)
class DESIGNSTATION_API UDSConfigSubsystem : public UGameInstanceSubsystem
{
	GENERATED_BODY()

public:
	UDSConfigSubsystem();

	virtual void Initialize(FSubsystemCollectionBase& Collection) override;

	virtual void Deinitialize() override;

	static UDSConfigSubsystem* GetInstance();

	//GETTER
	DSSetting::Custom::FCustomInfo GetCacheCustomSetting() const { return CI; }

	void FormatCurConfigWallHeightForCalculate(double& ModifyValue);

	//custom.setting.ini
	UFUNCTION(BlueprintCallable, Category = "CustomSetting | Snap Tip")
	bool GetSnapTipShow();

	UFUNCTION(BlueprintCallable, Category = "CustomSetting | Snap Tip")
	void SetSnapTipShow(bool IsShow);

#pragma region GLOBAL_SETTING

	void InitDSGlobalSetting();
#pragma region Normal
	int32 GetValue_Normal_Int(const DSSetting::Normal::EGeneralType& ValueType);

	void SetValue_Normal_Int(const DSSetting::Normal::EGeneralType& ValueType, int32 NewValue);
#pragma endregion Normal

#pragma region TwoDimension
	bool GetValue_TD_Bool(const DSSetting::TWD::ETWDType& ValueType);

	void SetValue_TD_Bool(const DSSetting::TWD::ETWDType& ValueType, bool NewVI);

	float GetValue_TD_Float(const DSSetting::TWD::ETWDType& ValueType);

	void SetValue_TD_Float(const DSSetting::TWD::ETWDType& ValueType, const float& NewVF);

	void GetTwoDimension_All(DSSetting::TWD::FTwoDimensionInfo& OutTWDI);

#pragma endregion TwoDimension

#pragma region ThreeDimension
	bool GetValue_THD_Bool(const DSSetting::THD::ETHDType& ValueType);

	void SetValue_THD_Bool(const DSSetting::THD::ETHDType& ValueType, bool NewVB);

	void GetThreeDimension_All(DSSetting::THD::FThreeDimensionInfo& OutTHDI);
#pragma endregion ThreeDimension

#pragma region Wall
	float GetValue_Wall_Float(const DSSetting::Wall::EWallType& ValueType);

	void SetValue_Wall_Float(const DSSetting::Wall::EWallType& ValueType, const float& NewVF);

	int32 GetValue_Wall_Int(const DSSetting::Wall::EWallType& ValueType);

	void SetValue_Wall_Int(const DSSetting::Wall::EWallType& ValueType, const int32& NewVF);
#pragma endregion Wall

#pragma region Custom

	void InitUseCustomSetting();

	bool GetValue_Custom_Bool(const DSSetting::Custom::ECustomType& ValueType);
	void SetValue_Custom_Bool(const DSSetting::Custom::ECustomType& ValueType, bool NewVB);

	float GetValue_Custom_Float(const DSSetting::Custom::ECustomType& ValueType);
	void SetValue_Custom_Float(const DSSetting::Custom::ECustomType& ValueType, const float& NewVF);

#pragma endregion Custom

#pragma region CameraSetting
	void GetValue_CameraSetting(DSSetting::CameraSetting::FCameraSettingInfo& SettingInfo);

	void SetValue_CameraSettingSpeed(FVector2D Speed);

	void SetValue_CameraSettingViewLocked(bool bViewLocked);

	void SetValue_CameraSettingInFOV(float InFOV);
#pragma endregion CameraSetting

#pragma region FlootHeight
	float GetValue_FlootHeight(int Level = 1);

	void SetValue_FlootHeight(float InValue);
#pragma endregion

#pragma region Light
	int32 GetWindowLightIntensity();

	float GetRectLightMinSize();

	float GetRectLightSpacing();

	float GetRectLightWallDistance();

	float GetRectLightIntensity();

	float GetRectLightDistanceToFloor();
#pragma endregion

#pragma region DefaultWallMaterial
	void InitDefaultRenderMaterial();

	void InitDefaultLocalSavePath();

	void InitIsDing();

	FString GetDefaultWallMaterialPath();

	FString GEtDefaultAreaMaterialPath();

	FString GetDefaultLocalSavePath();

	FString GetDefaultXmlSavePath();

	void SetDefaultLocalSavePath(const FString& InPath);
	
	void SetDefaultXmlSavePath(const FString& InPath);

	bool GetDefaultIsDing();

	void SetDefaultIsDing(bool InBool);

#pragma endregion GLOBAL_SETTING


#pragma region Light

	// 获取线型灯光强度极值
	UFUNCTION(BlueprintCallable, Category = "Light | Intensity")
	void GetLineLightIntensityLimit(float& OutMin, float& OutMax) const;

	// 获取IES灯光强度极值
	UFUNCTION(BlueprintCallable, Category = "Light | Intensity")
	void GetIESLightIntensityLimit(float& OutMin, float& OutMax) const;

	// 获取默认灯光强度极值
	UFUNCTION(BlueprintCallable, Category = "Light | Intensity")
	void GetDefaultLightIntensityLimit(float& OutMin, float& OutMax) const;


#pragma endregion


private:
	void InitNormal();

	void InitTwoDimensionSetting();

	void InitThreeDimensionSetting();

	void InitWallSetting();

	void InitCameraSettingInfo();

	void InitFloorHeight();

private:
	DSSetting::Normal::FGeneralSetting GS;

	DSSetting::TWD::FTwoDimensionInfo TWDI;

	DSSetting::THD::FThreeDimensionInfo THDI;

	DSSetting::Wall::FWallInfo WI;

	DSSetting::Custom::FCustomInfo CI;

	DSSetting::CameraSetting::FCameraSettingInfo CS;

	float DefaultFloorHeight;

	int32 WindowLightIntensity;

	float RectLightMinSize;

	float RectLightSpacing;

	float RectLightWallDistance;

	float RectLightIntensity;

	float RectLightDistanceToFloor;

	FString DefaultWallMaterialPath;

	FString DefaultAreaMaterialPath;

	FString DefaultLocalSavePath;

	FString DefaultXmlSavePath;

	bool DefaultIsDing;

};
