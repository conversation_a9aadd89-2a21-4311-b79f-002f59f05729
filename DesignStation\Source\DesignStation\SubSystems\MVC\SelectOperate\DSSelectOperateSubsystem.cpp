#include "DSSelectOperateSubsystem.h"
#include "BasicClasses/DesignStationController.h"
#include "SubSystems/File/DSFileSubsystem.h"
#include "SubSystems/Library/DesignStationFunctionLibrary.h"
#include "SubSystems/ModelDependencySubsystem/DSModelDependencySubsystem.h"
#include "SubSystems/MVC/DSMVCSubsystem.h"
#include "SubSystems/MVC/Commands/Public/DSGenericCommands.h"
#include "SubSystems/MVC/Core/Macro.h"
#include "SubSystems/MVC/Core/Function/DSFunction.h"
#include "Subsystems/MVC/Core/Property/DoorAndWindowProperty.h"
#include "SubSystems/MVC/Core/Property/GeneratedLineEntityProperty.h"
#include "Subsystems/MVC/Core/Property/RangeHoodProperty.h"
#include "Subsystems/MVC/Core/Property/StoveProperty.h"
#include "SubSystems/MVC/Library/DSToolLibrary.h"
#include "SubSystems/MVC/Model/CounterTop/CounterTopBaseModel.h"
#include "SubSystems/MVC/Model/CounterTop/CounterTopLineModel.h"
#include "SubSystems/MVC/Model/Custom/Library/DSHandleFreeLibrary.h"
#include "Subsystems/MVC/Model/Custom/Library/DSWallBoardLibrary.h"
#include "SubSystems/MVC/Model/Group/DSGroupModel.h"
#include "SubSystems/MVC/Model/Group/DSMultiModel.h"
#include "SubSystems/MVC/Model/House/Area/DSHouseAreaModel.h"
#include "SubSystems/MVC/Model/LineOrnament/LineOrnamentLineBaseModel.h"
#include "Subsystems/MVC/StateMachine/State/Public/DSFSMCounterTop.h"
#include "SubSystems/MVC/StateMachine/State/Public/DSFSMRoom.h"
#include "SubSystems/MVC/View/House/Area/DSHouseAreaLabelView.h"
#include "SubSystems/UI/DSUISubsystem.h"
#include "SubSystems/UI/Widget/Region/RegionLabelActor.h"
#include "SubSystems/Undo/DSRevokeSubsystem.h"
#include "SubSystems/Undo/Data/DSCounterTopRevokeData.h"
#include "SubSystems/Undo/Library/DSRevokeLibrary.h"

extern const TArray<EDSModelType> ConsiderAsHouseType;

UDSSelectOperateSubsystem::UDSSelectOperateSubsystem() {}

UDSSelectOperateSubsystem* UDSSelectOperateSubsystem::GetInstance()
{
	return ADesignStationController::Get()->GetGameInstance()->GetSubsystem<UDSSelectOperateSubsystem>();
}

void UDSSelectOperateSubsystem::Initialize(FSubsystemCollectionBase& Collection)
{
	Super::Initialize(Collection);
}

void UDSSelectOperateSubsystem::Deinitialize()
{
	Super::Deinitialize();
}

TArray<EDSOperateType> UDSSelectOperateSubsystem::SelectModelReturnOperateType(UDSBaseModel* InModel)
{
	TArray<EDSOperateType> TypeArray;
	if (DS_MODEL_VALID_FOR_USE(InModel))
	{
		//if (InModel->GetModelType() == EDSModelType::E_MultiSelect) //这里留给多选,多选要进一步判断子Model
		//{
		//	TypeArray = MultipleSelectModels(InModel);
		//}
		//else //这里是单选
		//{
		//	TypeArray.AddUnique(SingleSelectModel(InModel));
		//}

		//多选和成组不在分离，视为单个；具体内部处理
		TypeArray.AddUnique(SingleSelectModel(InModel));
	}

	return TypeArray;
}

TArray<EDSOperateType> UDSSelectOperateSubsystem::MultipleSelectModels(UDSBaseModel* InModel)
{
	TArray<EDSOperateType> TypeArray;
	//EDSOperateType ReturnType;
	for (auto& It : Cast<UDSMultiModel>(InModel)->GetIncludeModel())
	{
		/*switch (It->GetModelType())
		{
		case EDSModelType::E_Furniture_HouseFurniture:
			{
				if (ADesignStationController::Get()->Is2DScene())
				{
					ReturnType = EDSOperateType::E_Operate2D_MultipleSoftFurniture;
				}
				else
				{
					ReturnType = EDSOperateType::E_Operate3D_MultipleSoftFurniture;
				}
				break;
			}
		case EDSModelType::E_Custom_UpperCabinet:
		case EDSModelType::E_Custom_BaseCabinet:
		case EDSModelType::E_Custom_CornerCabinet:
		case EDSModelType::E_Custom_TallCabinet:
		case EDSModelType::E_Custom_WallCabinet:
			{
				if (ADesignStationController::Get()->Is2DScene())
				{
					ReturnType = EDSOperateType::E_Operate2D_MultipleCustom;
				}
				else
				{
					ReturnType = EDSOperateType::E_Operate3D_MultipleCustom;
				}
				break;
			}
		case EDSModelType::E_Custom_Board:
			{
				ReturnType = EDSOperateType::E_Operate3D_Plate;
				break;
			}
		}*/

		TypeArray.AddUnique(SingleSelectModel(It));
	}

	return TypeArray;
}

EDSOperateType UDSSelectOperateSubsystem::SingleSelectModel(UDSBaseModel* InModel)
{
	EDSOperateType ReturnType = EDSOperateType::E_Operate_None;
	switch (InModel->GetModelType())
	{
	case EDSModelType::E_Plane:
		{
			if (ADesignStationController::Get()->Is2DScene())
			{
				ReturnType = EDSOperateType::E_Operate2D_Plane;
			}
			else
			{
				ReturnType = EDSOperateType::E_Operate3D_Plane;
			}
			break;
		}
	case EDSModelType::E_House_Wall:
		{
			if (ADesignStationController::Get()->GetCurrentSceneType() == ECameraType::EXYPlan2D)
			{
				ReturnType = EDSOperateType::E_Operate2D_Wall;
			}
			else if (ADesignStationController::Get()->GetCurrentSceneType() == ECameraType::EXYPlan2D_Ceil)
			{
				ReturnType = EDSOperateType::E_Operate2D_Ceiling_Wall;
			}
			else
			{
				ReturnType = EDSOperateType::E_Operate3D_Wall;
			}
			break;
		}
	case EDSModelType::E_House_Area:
		{
			if (ADesignStationController::Get()->Is2DScene())
			{
				ReturnType = EDSOperateType::E_Operate2D_Area;
			}
			else
			{
				ReturnType = EDSOperateType::E_Operate3D_Area;
			}
			break;
		}
	case EDSModelType::E_House_Area_Split_Line:
		{
			ReturnType = EDSOperateType::E_Operate2D_AreaSplit;
			break;
		}
	case EDSModelType::E_House_Pillar:
		{
			ReturnType = EDSOperateType::E_Operate2D_Pillar;
			break;
		}
	case EDSModelType::E_House_Beam:
		{
			if (ADesignStationController::Get()->Is2DScene())
			{
				ReturnType = EDSOperateType::E_Operate2D_Beam;
			}
			else
			{
				ReturnType = EDSOperateType::E_Operate3D_Beam;
			}
			break;
		}
	case EDSModelType::E_House_Platform:
		{
			if (ADesignStationController::Get()->Is2DScene())
			{
				ReturnType = EDSOperateType::E_Operate2D_Platform;
			}
			else
			{
				ReturnType = EDSOperateType::E_Operate3D_Platform;
			}
			break;
		}
	case EDSModelType::E_House_Window:
	case EDSModelType::E_House_Door:
	case EDSModelType::E_Door_One:
	case EDSModelType::E_Door_Two:
	case EDSModelType::E_Door_Sliding:
	case EDSModelType::E_Window_Rect:
	case EDSModelType::E_Window_French:
	case EDSModelType::E_Window_Bay:
		{
			if (ADesignStationController::Get()->Is2DScene())
			{
				ReturnType = EDSOperateType::E_Operate_DoorAndWindow2D;
			}
			else
			{
				ReturnType = EDSOperateType::E_Operate_DoorAndWindow3D;
			}
			break;
		}
	case EDSModelType::E_Furniture_HouseFurniture:
		{
			if (ADesignStationController::Get()->Is2DScene())
			{
				ReturnType = EDSOperateType::E_Operate2D_SoftFurniture;
			}
			else
			{
				ReturnType = EDSOperateType::E_Operate3D_SoftFurniture;
			}
			break;
		}
	case EDSModelType::E_Custom_UpperCabinet:
	case EDSModelType::E_Custom_BaseCabinet:
	case EDSModelType::E_Custom_CornerCabinet:
	case EDSModelType::E_Custom_TallCabinet:
	case EDSModelType::E_Custom_WallCabinet:
	case EDSModelType::E_Custom_WallBoardCabinet:
	case EDSModelType::E_Custom_LayoutDoor:
	case EDSModelType::E_Custom_CornerCutCabinet:
		{
			if (ADesignStationController::Get()->Is2DScene())
			{
				ReturnType = EDSOperateType::E_Operate2D_Custom;
			}
			else
			{
				ReturnType = EDSOperateType::E_Operate3D_Custom;
			}
			break;
		}
	case EDSModelType::E_Custom_Functional_HangingRod:
	case EDSModelType::E_Custom_Functional_Combine:
	case EDSModelType::E_Custom_Board:
		{
			if (UDSCupboardLibrary::IsFunctionalCupboardModel(InModel))
			{
				if (ADesignStationController::Get()->Is2DScene())
				{
					ReturnType = EDSOperateType::E_Operate_None;
				}
				else
				{
					ReturnType = EDSOperateType::E_Operate3D_Plate;
				}
				break;
			}
		}
	case EDSModelType::E_Custom_LayoutDoor_Board:
		{
			UDSBaseModel* RootModel = UDSToolLibrary::GetRootOwner(InModel);
			if (RootModel != nullptr && RootModel->GetModelType() == EDSModelType::E_Custom_WallBoardCabinet)
			{
				ReturnType = EDSOperateType::E_Operate_WallBoardCabinet;
			}
			break;
		}
	case EDSModelType::E_Group:
		{
			//bool IsHasHouseFurniture = false;
			//bool IsHasHouseCustom = false;
			//for (auto It : Cast<UDSGroupModel>(InModel)->GetIncludeModel())
			//{
			//	if (It->GetModelType() == EDSModelType::E_Furniture_HouseFurniture)
			//	{
			//		IsHasHouseFurniture = true;
			//		continue;
			//	}
			//	if (It->GetModelType() >= EDSModelType::E_Custom_UpperCabinet && It->GetModelType() < EDSModelType::E_Custom_Furniture_Range_End)
			//	{
			//		IsHasHouseCustom = true;
			//	}
			//}
			//if (IsHasHouseFurniture && IsHasHouseCustom) //组中模型混合，取交集
			//{
			//	if (ADesignStationController::Get()->Is2DScene())
			//	{
			//		ReturnType = EDSOperateType::E_Operate2D_MixGroup;
			//	}
			//	else
			//	{
			//		ReturnType = EDSOperateType::E_Operate3D_MixGroup;
			//	}
			//}
			//else if (IsHasHouseFurniture && !IsHasHouseCustom) //纯成品组合，不含定制
			//{
			//	if (ADesignStationController::Get()->Is2DScene())
			//	{
			//		ReturnType = EDSOperateType::E_Operate2D_SoftFurnitureGroup;
			//	}
			//	else
			//	{
			//		ReturnType = EDSOperateType::E_Operate3D_SoftFurnitureGroup;
			//	}
			//}
			//else if (!IsHasHouseFurniture && IsHasHouseCustom) //纯定制组合，不含成品
			//{
			//	if (ADesignStationController::Get()->Is2DScene())
			//	{
			//		ReturnType = EDSOperateType::E_Operate2D_CustomGroup;
			//	}
			//	else
			//	{
			//		ReturnType = EDSOperateType::E_Operate3D_CustomGroup;
			//	}
			//}
			if (ADesignStationController::Get()->Is2DScene())
			{
				ReturnType = EDSOperateType::E_Operate2D_Group;
			}
			else
			{
				ReturnType = EDSOperateType::E_Operate3D_Group;
			}
			break;
		}
	case EDSModelType::E_MultiSelect:
		{
			if (ADesignStationController::Get()->Is2DScene())
			{
				ReturnType = EDSOperateType::E_Operate2D_Multi;
			}
			else
			{
				ReturnType = EDSOperateType::E_Operate3D_Multi;
			}
			break;
		}
	case EDSModelType::E_Custom_DoorPanel:
	case EDSModelType::E_Custom_DoorPanel_Flat:
	case EDSModelType::E_Custom_DoorPanel_SolidWood:
	case EDSModelType::E_Custom_DoorPanel_AluminumFrame:
	case EDSModelType::E_Custom_DoorPanel_Glass:
	case EDSModelType::E_Custom_DoorPanel_Fake:
		{
			ReturnType = EDSOperateType::E_Operate3D_CustomCabinetDoor;
			break;
		}
	case EDSModelType::E_Custom_FunctionalDrawer:
		{
			if (ADesignStationController::Get()->Is2DScene())
			{
				ReturnType = EDSOperateType::E_Operate_None;
			}
			else
			{
				ReturnType = EDSOperateType::E_Operate3D_Drawer;
			}
			
			break;
		}
	case EDSModelType::E_Generated_CounterTop:
		{
			ReturnType = EDSOperateType::E_Operate3D_CounterTop;
			break;
		}
	case EDSModelType::E_Generated_SideCounterTop:
		{
			ReturnType = EDSOperateType::E_Operate3D_SideCounterTop;
			break;
		}
	case EDSModelType::E_Generated_CounterTop_Point:
		{
			if (ADesignStationController::Get()->Is2DScene())
			{
				ReturnType = EDSOperateType::E_Operate2D_CounterTopPoint;
			}
			else
			{
				ReturnType = EDSOperateType::E_Operate3D_CounterTopPoint;
			}
			break;
		}
	case EDSModelType::E_Generated_CounterTop_Line:
		{
			if (ADesignStationController::Get()->Is2DScene())
			{
				ReturnType = EDSOperateType::E_Operate2D_CounterTopLine;
			}
			else
			{
				ReturnType = EDSOperateType::E_Operate3D_CounterTopLine;
			}
			break;
		}
	case EDSModelType::E_Custom_Sink:
		{
			ReturnType = EDSOperateType::E_Operate_Sink;
			break;
		}
	case EDSModelType::E_Custom_RangeHood:
		{
			ReturnType = EDSOperateType::E_Operate_RangeHood;
			break;
		}
	case EDSModelType::E_Custom_Stove:
		{
			ReturnType = EDSOperateType::E_Operate_Stove;
			break;
		}
	case EDSModelType::E_Generated_LineOrnamentBase_Line:
		{
			ReturnType = EDSOperateType::E_Operate_GeneratedOrnamentLine;
			break;
		}
	case EDSModelType::E_Generated_LineOrnamentBase_Point:
		{
			ReturnType = EDSOperateType::E_Operate_GeneratedOrnamentPoint;
			break;
		}
	case EDSModelType::E_Generated_CrownMoulding:
	case EDSModelType::E_Generated_LightCord:
	case EDSModelType::E_Generated_SkirtingBoard:
	case EDSModelType::E_Generated_LineEntity:
		{
			ReturnType = EDSOperateType::E_Operate_GeneratedLine;
			break;
		}
	case EDSModelType::E_RoofArea:
		{
			ReturnType = EDSOperateType::E_Operate2D_Ceiling;
			break;
		}
	case EDSModelType::E_Furniture_MoldingCeiling:
		{
			if (ADesignStationController::Get()->Is2DScene())
			{
				ReturnType = EDSOperateType::E_Operate2D_FurnitureCeiling;
			}
			else
			{
				ReturnType = EDSOperateType::E_Operate3D_FurnitureCeiling;
			}
		}
	default:
		break;
	}

	return ReturnType;
}

void UDSSelectOperateSubsystem::Hide(UDSBaseModel* InModel, bool bForced, bool bNewHidden)
{
	if (DS_MODEL_VALID_FOR_USE(InModel))
	{
		bool bHidden = !InModel->GetModelHiddenFlags().GetActiveHiddenFlag();
		if (bForced)
		{
			bHidden = bNewHidden;
		}
		FDSModelExecuteType ExcuteType = bHidden ? FDSModelExecuteType::ExecuteHidden : FDSModelExecuteType::ExecuteUnHidden;
		InModel->GetModelHiddenFlags().SetActiveHiddenFlag(bHidden);

		EDSModelType EditType = InModel->GetModelType();
		if (EditType == EDSModelType::E_Generated_CounterTop || EditType == EDSModelType::E_Generated_SideCounterTop)
		{
			FDSCounterTopPushData PushData(EDSCTRevokeType::E_Hidden);
			if (EditType == EDSModelType::E_Generated_CounterTop)
			{
				TSharedPtr<FDSCounterTopProperty> PropertyCopy = UDSToolLibrary::GetPropertyCopy<FDSCounterTopProperty>(InModel->GetPropertySharedPtr());
				if (PropertyCopy.IsValid())
				{
					PropertyCopy->SetHidden(bHidden);
					PushData.SetCTPropertyData(PropertyCopy);
				}
			}
			else if (EditType == EDSModelType::E_Generated_SideCounterTop)
			{
				TSharedPtr<FDSSideCounterTopProperty> PropertyCopy = UDSToolLibrary::GetPropertyCopy<FDSSideCounterTopProperty>(InModel->GetPropertySharedPtr());
				if (PropertyCopy.IsValid())
				{
					PropertyCopy->SetHidden(bHidden);
					PushData.SetCTPropertyData(PropertyCopy);
				}
			}

			FDSRevokePushData T_PushData(EDSPushDataType::E_CounterTop, ExcuteType, false);
			T_PushData.SetData(PushData);
			UDSMVCSubsystem::GetInstance()->OnExecuteFromFunctionBarOrPropertyWidgetUnion(InModel, ExcuteType, T_PushData, TEXT(""));
		}
		else if (EditType == EDSModelType::E_Custom_Sink)
		{
			FDSRevokePushData PushData(EDSPushDataType::E_Sink, ExcuteType);
			TSharedPtr<FDSSinkProperty> PropertyCopy = UDSToolLibrary::GetPropertyCopy<FDSSinkProperty>(InModel->GetPropertySharedPtr());
			if (PropertyCopy.IsValid())
			{
				PropertyCopy->SetHidden(bHidden);
				PushData.SetProperty(PropertyCopy);
			}

			UDSMVCSubsystem::GetInstance()->OnExecuteFromFunctionBarOrPropertyWidgetUnion(
				InModel, ExcuteType, PushData, TEXT(""));
		}
		else if (EditType == EDSModelType::E_Custom_Stove)
		{
			FDSRevokePushData PushData(EDSPushDataType::E_StoveRangeHood, ExcuteType);
			TSharedPtr<FDSStoveProperty> PropertyCopy = UDSToolLibrary::GetPropertyCopy<FDSStoveProperty>(InModel->GetPropertySharedPtr());
			if (PropertyCopy.IsValid())
			{
				PropertyCopy->SetHidden(bHidden);
				PushData.SetProperty(PropertyCopy);
			}

			UDSMVCSubsystem::GetInstance()->OnExecuteFromFunctionBarOrPropertyWidgetUnion(
				InModel, ExcuteType, PushData, TEXT(""));
		}
		else if (EditType == EDSModelType::E_Custom_RangeHood)
		{
			FDSRevokePushData PushData(EDSPushDataType::E_StoveRangeHood, ExcuteType);
			TSharedPtr<FDSRangeHoodProperty> PropertyCopy = UDSToolLibrary::GetPropertyCopy<FDSRangeHoodProperty>(InModel->GetPropertySharedPtr());
			if (PropertyCopy.IsValid())
			{
				PropertyCopy->SetHidden(bHidden);
				PushData.SetProperty(PropertyCopy);
			}

			UDSMVCSubsystem::GetInstance()->OnExecuteFromFunctionBarOrPropertyWidgetUnion(
				InModel, ExcuteType, PushData, TEXT(""));
		}
		else if (EditType == EDSModelType::E_Furniture_HouseFurniture)
		{
			//成品
			FDSRevokePushData PushData(EDSPushDataType::E_SoftFurniture, ExcuteType);
			TSharedPtr<FDSSoftFurnitureProperty> PropertyCopy = UDSToolLibrary::GetPropertyCopy<FDSSoftFurnitureProperty>(InModel->GetPropertySharedPtr());
			if (PropertyCopy.IsValid())
			{
				PropertyCopy->SetHidden(bHidden);
				PushData.SetProperty(PropertyCopy);
			}
			UDSMVCSubsystem::GetInstance()->OnExecuteFromFunctionBarOrPropertyWidgetUnion(InModel, ExcuteType, PushData, TEXT(""));
		}
		else if (EditType == EDSModelType::E_Furniture_MoldingCeiling)
		{
			FDSRevokePushData PushData(EDSPushDataType::E_ModelCeiling, ExcuteType);
			TSharedPtr<FDSMoldingCeilingProperty> PropertyCopy = UDSToolLibrary::GetPropertyCopy<FDSMoldingCeilingProperty>(InModel->GetPropertySharedPtr());
			PropertyCopy->SetHidden(bHidden);
			PushData.SetProperty(PropertyCopy);
			UDSMVCSubsystem::GetInstance()->OnExecuteFromFunctionBarOrPropertyWidgetUnion(InModel, ExcuteType, PushData, TEXT(""));
		}
		else if (EditType == EDSModelType::E_Generated_LineEntity)
		{
			FDSRevokePushData PushData(EDSPushDataType::E_LineEntity, ExcuteType);
			if (TSharedPtr<FDSGeneratedLineEntityProperty> PropertyCopy = UDSToolLibrary::GetPropertyCopy<FDSGeneratedLineEntityProperty>(InModel->GetPropertySharedPtr()))
			{
				PropertyCopy->SetHidden(bHidden);
				PushData.SetProperty(PropertyCopy);
			}

			UDSMVCSubsystem::GetInstance()->OnExecuteFromFunctionBarOrPropertyWidgetUnion(InModel, ExcuteType, PushData, TEXT(""));
		}
		else if (EditType == EDSModelType::E_MultiSelect || EditType == EDSModelType::E_Group)
		{
			FString GUIDStr = FGuid::NewGuid().ToString();
			//如果多选或组中有合并免拉五金的柜子，则在隐藏这个多选或组之前，拆分这个合并的五金免拉
			if (UDSHandleFreeLibrary::SplitHandleFreeMultiIfContain(Cast<UDSMultiModel>(InModel), &GUIDStr))   
			{
				UDSUISubsystem::GetInstance()->SetToastState(EToastState::EWarning, TEXT("选中的合并拉手已进行拆分"));
			}

			FDSRevokePushData PushData(EDSPushDataType::E_Multi_Group, FDSModelExecuteType::ExecuteUpdateSelf, false);
			UDSMVCSubsystem::GetInstance()->OnExecuteFromFunctionBarOrPropertyWidgetUnion(InModel, FDSModelExecuteType::ExecuteHidden, PushData, GUIDStr);
		}
		else if (ConsiderAsHouseType.Contains(EditType))
		{
			FDSRevokePushData PushData(EDSPushDataType::E_Home, ExcuteType, true);
			TSharedPtr<FDSBaseProperty> NewProperty = UDSToolLibrary::GetBasePropertyCopy(InModel);
			NewProperty->SetHidden(true);
			PushData.SetData(FDSRoomPushData({}, ExcuteType, InModel, NewProperty));
			UDSMVCSubsystem::GetInstance()->OnExecuteFromFunctionBarOrPropertyWidgetUnion(InModel, ExcuteType, PushData, TEXT(""));
		}
		else if (EditType == EDSModelType::E_Custom_LayoutDoor_Board)
		{
			UDSBaseModel* RootModel = InModel->GetTopLevelOwnerModel();
			if (RootModel != nullptr && RootModel->GetModelType() == EDSModelType::E_Custom_WallBoardCabinet)
			{
				FDSRevokePushData PushData(EDSPushDataType::E_Custom, FDSModelExecuteType::ExecuteUpdateSelf, false);
				PushData.SetData(FDSCustomPushData(FDSModelExecuteType::ExecuteUpdateSelf, RootModel, nullptr));
				UDSRevokeSubsystem::GetInstance()->InsertCommandDataUnion(
					RootModel, FDSModelExecuteType::ExecuteUpdateSelf, PushData, UDSMVCSubsystem::GetInstance()->GetRevokeMark(), TEXT(""));

				UDSWallBoardLibrary::HideRealBoard(InModel);
			}
		}
		else if (UDSToolLibrary::IsCustomCupboardType(EditType))
		{
			FString GUIDStr;

			//处理五金免拉合并
			if (UDSToolLibrary::IsCustomCabinetType(EditType) && UDSModelDependencySubsystem::GetInstance()->IsHaveModelHandleFree(InModel->GetUUID()))
			{
				UDSUISubsystem::GetInstance()->SetToastState(EToastState::EWarning, TEXT("选中的合并拉手已进行拆分"));
				GUIDStr = FGuid::NewGuid().ToString();
				UDSHandleFreeLibrary::SplitHandleFree(InModel, true, &GUIDStr);
			}

			TSharedPtr<FDSBaseProperty> NewProperty = UDSToolLibrary::GetBasePropertyCopy(InModel);
			if (NewProperty)
			{
				//NewProperty->SetHidden(true);
				FDSRevokePushData T_PushData(EDSPushDataType::E_Custom, ExcuteType, false);
				T_PushData.SetData(FDSBasicPushData(ExcuteType, InModel, NewProperty));
				UDSMVCSubsystem::GetInstance()->OnExecuteFromFunctionBarOrPropertyWidgetUnion(InModel, ExcuteType, T_PushData, GUIDStr);
			}
		}
		else
		{
			TSharedPtr<FDSBaseProperty> NewProperty = UDSToolLibrary::GetBasePropertyCopy(InModel);
			if (NewProperty)
			{
				//NewProperty->SetHidden(true);
				FDSRevokePushData T_PushData(EDSPushDataType::E_Single, ExcuteType, false);
				T_PushData.SetData(FDSBasicPushData(ExcuteType, InModel, NewProperty));
				UDSMVCSubsystem::GetInstance()->OnExecuteFromFunctionBarOrPropertyWidgetUnion(InModel, ExcuteType, T_PushData, TEXT(""));
			}
		}
		UDSMVCSubsystem::GetInstance()->SetCurrentModel(nullptr);

		if (UDSFSMCounterTop* FiniteState = Cast<UDSFSMCounterTop>(UDSMVCSubsystem::GetInstance()->GetState()))
		{
			if (FiniteState->GetPaintingType() == ECounterTopPaintingType::None)
			{
				UDSUISubsystem::GetInstance()->HiddenUI(false, true);
				FiniteState->UpdatePropertyWidget(nullptr);
			}
			else
			{
				UDSUISubsystem::GetInstance()->HiddenUI();
			}
		}
		else
		{
			UDSUISubsystem::GetInstance()->HiddenUI();
		}
	}
}

void UDSSelectOperateSubsystem::Lock(UDSBaseModel* InModel, bool bForced, bool bNewLock)
{
	if (DS_MODEL_VALID_FOR_USE(InModel))
	{
		TSharedPtr<FDSBaseProperty> NewProperty = UDSToolLibrary::GetBasePropertyCopy(InModel);
		if (!NewProperty)
		{
			return;
		}
		bool bLock = !InModel->GetPropertySharedPtr()->GetIsLock();

		if (bForced)
		{
			bLock = bNewLock;
		}
		FDSModelExecuteType NewActionExecuteType = bLock ? FDSModelExecuteType::ExecuteLock : FDSModelExecuteType::ExecuteUnLock;
		FDSRevokePushData PushData(EDSPushDataType::E_Single, NewActionExecuteType, false);
		PushData.SetData(FDSBasicPushData(NewActionExecuteType, InModel, NewProperty));
		UDSMVCSubsystem::GetInstance()->OnExecuteFromFunctionBarOrPropertyWidgetUnion(InModel, NewActionExecuteType, PushData, TEXT(""));
	}
}

void UDSSelectOperateSubsystem::Delete(UDSBaseModel* InModel)
{
	if (!DS_MODEL_VALID_FOR_USE(InModel))
	{
		return;
	}

	UDSFiniteState* CurrentState = UDSMVCSubsystem::GetInstance()->GetState();
	if (CurrentState == nullptr)
	{
		return;
	}

	if (CurrentState->GetCommandList()->TryExecuteAction(FDSGenericCommands::Get().Delete.ToSharedRef()))
	{
		if (UDSFSMCounterTop* FiniteState = Cast<UDSFSMCounterTop>(CurrentState))
		{
			if (FiniteState->GetPaintingType() == ECounterTopPaintingType::None)
			{
				UDSUISubsystem::GetInstance()->HiddenUI(false, true);
				FiniteState->UpdatePropertyWidget(nullptr);
			}
			else
			{
				UDSUISubsystem::GetInstance()->HiddenUI();
			}
		}
		else
		{
			UDSUISubsystem::GetInstance()->HiddenUI();
		}
	}
}

void UDSSelectOperateSubsystem::Split(UDSBaseModel* InModel)
{
	UDSMVCSubsystem::GetInstance()->SwitchState(EDSFSMState::FSM_RoomEdit);

	EDrawingRoom SplitType;
	switch (InModel->GetModelType())
	{
	case EDSModelType::E_House_Wall:
		{
			SplitType = EDrawingRoom::E_Split_Wall;
			break;
		}
	case EDSModelType::E_House_Platform:
		{
			SplitType = EDrawingRoom::E_Split_Platform;
			break;
		}
	case EDSModelType::E_House_Beam:
		{
			SplitType = EDrawingRoom::E_Split_Beam;
			break;
		}
	default:
		break;
	}

	Cast<UDSFSMRoom>(UDSMVCSubsystem::GetInstance()->GetState())->SetDrawingRoom(SplitType);

	if (const auto SelectModel = UDSMVCSubsystem::GetInstance()->GetCurrentModel())
	{
		SelectModel->OnExecuteAction(FDSModelExecuteType::ExecuteUnSelect);
		if (DS_MODEL_VALID_FOR_USE(InModel) && InModel != SelectModel)
		{
			InModel->OnExecuteAction(FDSModelExecuteType::ExecuteUnSelect);
		}
	}
	UDSMVCSubsystem::GetInstance()->ResetAllState();
	UDSMVCSubsystem::GetInstance()->SetCurrentModel(nullptr);
	UDSUISubsystem::GetInstance()->HiddenUI();
}

void UDSSelectOperateSubsystem::Copy(UDSBaseModel* InModel)
{
	if (DS_MODEL_VALID_FOR_USE(InModel))
	{
		UDSBaseModel* NewModel = InModel->OnCopy();

		//修改到Action中OnDrop时设置，充当撤销Transform/Spawn标记
		//NewModel->SetNoNewGenerate();

		NewModel->SetIsContinueGenerate(false);

		if (UDSToolLibrary::IsInGroup(InModel))
		{
			if (UDSBaseModel* GroupModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(EDSModelType::E_Group, InModel->GetGroupUUID()))
			{
				GroupModel->OnExecuteAction(FDSModelExecuteType::ExecuteUnSelectComponent, FDSBroadcastMarkData::OnlyOutlineBroadcastMark);
			}

			UDSMVCSubsystem::GetInstance()->GetState()->SwitchStateSelectType(EDSSelectType::E_Whole_Select);
		}
		UDSMVCSubsystem::GetInstance()->SetCurrentModel(NewModel);

		UDSToolLibrary::GenerateModelDragDropEvent(NewModel);

		UDSUISubsystem::GetInstance()->HiddenUI();
	}
}

void UDSSelectOperateSubsystem::ZoneSplit(UDSBaseModel* InModel)
{
	UDSMVCSubsystem::GetInstance()->SwitchState(EDSFSMState::FSM_SplitArea);
	FVector2f ThisMouse;
	ADesignStationController::Get()->GetMousePosition(ThisMouse.X, ThisMouse.Y);
	UDSUISubsystem::GetInstance()->HiddenUI();
}

void UDSSelectOperateSubsystem::RotUpAndDown(UDSBaseModel* InModel)
{
	if (InModel != nullptr)
	{
		EDSModelType ModelType = InModel->GetModelType();

		if (ModelType == EDSModelType::E_Furniture_HouseFurniture)
		{
			//成品
			FDSRevokePushData PushData(EDSPushDataType::E_SoftFurniture, FDSModelExecuteType::ExecuteTransform);
			TSharedPtr<FDSSoftFurnitureProperty> PropertyCopy = UDSToolLibrary::GetPropertyCopy<FDSSoftFurnitureProperty>(InModel->GetPropertySharedPtr());
			PropertyCopy->GetTransformPropertyRef().FlipDownRot();
			PushData.SetProperty(PropertyCopy);
			UDSMVCSubsystem::GetInstance()->OnExecuteFromFunctionBarOrPropertyWidgetUnion(InModel, FDSModelExecuteType::ExecuteUpdateSelf, PushData, TEXT(""));
		}
		else if (ModelType == EDSModelType::E_Furniture_MoldingCeiling)
		{
			FDSRevokePushData PushData(EDSPushDataType::E_ModelCeiling, FDSModelExecuteType::ExecuteTransform);
			TSharedPtr<FDSMoldingCeilingProperty> PropertyCopy = UDSToolLibrary::GetPropertyCopy<FDSMoldingCeilingProperty>(InModel->GetPropertySharedPtr());
			PropertyCopy->GetTransformPropertyRef().FlipDownRot();
			PushData.SetProperty(PropertyCopy);
			UDSMVCSubsystem::GetInstance()->OnExecuteFromFunctionBarOrPropertyWidgetUnion(InModel, FDSModelExecuteType::ExecuteUpdateSelf, PushData, TEXT(""));
		}
		else if (ModelType == EDSModelType::E_House_Door || ModelType == EDSModelType::E_House_Window)
		{
			TSharedPtr<FDSDoorAndWindowProperty> NewProperty = UDSToolLibrary::GetPropertyCopy_WindowAndDoor(InModel->GetPropertySharedPtr());
			NewProperty->GetTransformPropertyRef().FlipDownRot();
			FDSRevokePushData PushData(EDSPushDataType::E_Single, FDSModelExecuteType::ExecuteUpdateSelf, false);
			PushData.SetData(FDSBasicPushData(FDSModelExecuteType::ExecuteUpdateSelf, InModel, NewProperty));
			UDSMVCSubsystem::GetInstance()->OnExecuteFromFunctionBarOrPropertyWidgetUnion(
				InModel, FDSModelExecuteType::ExecuteUpdateSelf, PushData, TEXT(""));
		}
		else if (ModelType == EDSModelType::E_MultiSelect || ModelType == EDSModelType::E_Group)
		{
			UDSMultiModel* MultiModel = Cast<UDSMultiModel>(InModel);
			MultiModel->RotUpAndDown();
		}

		InModel->OnExecuteAction(FDSModelExecuteType::ExecuteSelect);
	}
}

void UDSSelectOperateSubsystem::RotLeftAndRight(UDSBaseModel* InModel)
{
	if (InModel != nullptr)
	{
		EDSModelType ModelType = InModel->GetModelType();

		if (ModelType == EDSModelType::E_Furniture_HouseFurniture)
		{
			//成品
			FDSRevokePushData PushData(EDSPushDataType::E_SoftFurniture, FDSModelExecuteType::ExecuteUpdateSelf);
			TSharedPtr<FDSSoftFurnitureProperty> PropertyCopy = UDSToolLibrary::GetPropertyCopy<FDSSoftFurnitureProperty>(InModel->GetPropertySharedPtr());
			PropertyCopy->GetTransformPropertyRef().FlipLeftRot();
			PushData.SetProperty(PropertyCopy);
			UDSMVCSubsystem::GetInstance()->OnExecuteFromFunctionBarOrPropertyWidgetUnion(InModel, FDSModelExecuteType::ExecuteUpdateSelf, PushData, TEXT(""));
		}
		else if (ModelType == EDSModelType::E_Furniture_MoldingCeiling)
		{
			FDSRevokePushData PushData(EDSPushDataType::E_ModelCeiling, FDSModelExecuteType::ExecuteUpdateSelf);
			TSharedPtr<FDSMoldingCeilingProperty> PropertyCopy = UDSToolLibrary::GetPropertyCopy<FDSMoldingCeilingProperty>(InModel->GetPropertySharedPtr());
			PropertyCopy->GetTransformPropertyRef().FlipLeftRot();
			PushData.SetProperty(PropertyCopy);
			UDSMVCSubsystem::GetInstance()->OnExecuteFromFunctionBarOrPropertyWidgetUnion(InModel, FDSModelExecuteType::ExecuteUpdateSelf, PushData, TEXT(""));
		}
		else if (ModelType == EDSModelType::E_House_Door || ModelType == EDSModelType::E_House_Window)
		{
			TSharedPtr<FDSDoorAndWindowProperty> NewProperty = UDSToolLibrary::GetPropertyCopy_WindowAndDoor(InModel->GetPropertySharedPtr());
			NewProperty->GetTransformPropertyRef().FlipLeftRot();
			FDSRevokePushData PushData(EDSPushDataType::E_Single, FDSModelExecuteType::ExecuteUpdateSelf, false);
			PushData.SetData(FDSBasicPushData(FDSModelExecuteType::ExecuteUpdateSelf, InModel, NewProperty));
			UDSMVCSubsystem::GetInstance()->OnExecuteFromFunctionBarOrPropertyWidgetUnion(
				InModel, FDSModelExecuteType::ExecuteUpdateSelf, PushData, TEXT(""));
		}
		else if (ModelType == EDSModelType::E_MultiSelect || ModelType == EDSModelType::E_Group)
		{
			UDSMultiModel* MultiModel = Cast<UDSMultiModel>(InModel);
			MultiModel->RotLeftAndRight();
		}

		InModel->OnExecuteAction(FDSModelExecuteType::ExecuteSelect);
	}
}

bool UDSSelectOperateSubsystem::Group(UDSBaseModel* InModel)
{
	if (UDSToolLibrary::IsMultiOperator(InModel))
	{
		UDSMVCSubsystem::GetInstance()->ConvertMultiToGroup(InModel);
		return true;
	}
	return false;
}

bool UDSSelectOperateSubsystem::UnGroup(UDSBaseModel* InModel)
{
	if (UDSToolLibrary::IsGroupType(InModel))
	{
		UDSMVCSubsystem::GetInstance()->ConvertGroupToMulti(InModel);
		return true;
	}
	return false;
}

void UDSSelectOperateSubsystem::OpenAndCloseDoor(UDSBaseModel* InModel, const int32& Degree)
{
	if (DS_MODEL_VALID_FOR_USE(InModel))
	{
		UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(InModel);
		if (CupboardModel)
		{
			//if CupboardModel is a door
			if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), CupboardModel->GetModelInfoRef().ComponentTreeData->ModelType))
			{
				auto OwnerModel = CupboardModel->GetTopLevelOwnerModel();
				if (!OwnerModel || !OwnerModel->IsA<UDSCupboardModel>())
				{
					return;
				}
				FDSRevokePushData PushData(EDSPushDataType::E_Custom, FDSModelExecuteType::ExecuteUpdateSelf, false);
				PushData.SetData(FDSCustomPushData(FDSModelExecuteType::ExecuteUpdateSelf, OwnerModel, nullptr));
				UDSRevokeSubsystem::GetInstance()->InsertCommandDataUnion(
					OwnerModel,
					FDSModelExecuteType::ExecuteUpdateSelf,
					PushData,
					UDSMVCSubsystem::GetInstance()->GetRevokeMark(),
					TEXT("")
				);
				for (auto& It : CupboardModel->GetModelInfoRef().ComponentTreeData->ComponentParameters)
				{
					if (It.Data.name == TEXT("DKJD"))
					{
						It.Data.value = FString::FromInt(Degree);
						It.Data.expression = FString::FromInt(Degree);
						break;
					}
				}
				CupboardModel->OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf);
			}
			else
			{
				FDSRevokePushData PushData(EDSPushDataType::E_Custom, FDSModelExecuteType::ExecuteUpdateSelf, false);
				PushData.SetData(FDSCustomPushData(FDSModelExecuteType::ExecuteUpdateSelf, CupboardModel, nullptr));
				UDSRevokeSubsystem::GetInstance()->InsertCommandDataUnion(
					CupboardModel,
					FDSModelExecuteType::ExecuteUpdateSelf,
					PushData,
					UDSMVCSubsystem::GetInstance()->GetRevokeMark(),
					TEXT("")
				);
				//if CupboardModel is a cabinet
				for (const auto& It : CupboardModel->GetModelInfoRef().ComponentTreeData->ChildComponent)
				{
					if (UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(It->ModelType) == EDSModelType::E_Custom_DoorContainer)
					{
						RecursionOpenAndCloseDoor(It, Degree);
					}
				}
				CupboardModel->OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf);
			}
		}
		else
		{
			if (InModel->GetModelType() == EDSModelType::E_MultiSelect || InModel->GetModelType() == EDSModelType::E_Group)
			{
				if (UDSMultiModel* MultiModel = Cast<UDSMultiModel>(InModel))
				{
					FDSRevokePushData PushData(EDSPushDataType::E_Multi_Group, FDSModelExecuteType::ExecuteUpdateSelf, false);
					UDSRevokeLibrary::UpdatePushDataPropertyUnion(MultiModel, PushData.ExecuteType, {}, PushData);
					UDSRevokeSubsystem::GetInstance()->InsertCommandDataUnion(
						MultiModel,
						FDSModelExecuteType::ExecuteUpdateSelf,
						PushData,
						UDSMVCSubsystem::GetInstance()->GetRevokeMark(),
						TEXT("")
					);

					for (auto& Door : MultiModel->GetIncludeModel())
					{
						if (UDSCupboardModel* IncludeModel = Cast<UDSCupboardModel>(Door))
						{
							if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), IncludeModel->GetModelInfoRef().ComponentTreeData->ModelType))
							{
								FParameterData* OpenData = IncludeModel->GetModelInfoRef().ComponentTreeData->ComponentParameters.FindByPredicate([](const FParameterData& InData)
								{
									return InData.Data.name == TEXT("DKJD");
								});
								if (!OpenData)
								{
									return;
								}
								OpenData->Data.value = FString::FromInt(Degree);
								OpenData->Data.expression = FString::FromInt(Degree);

								IncludeModel->OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf);
							}
							else
							{
								for (const auto& It : IncludeModel->GetModelInfoRef().ComponentTreeData->ChildComponent)
								{
									if (UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(It->ModelType) == EDSModelType::E_Custom_DoorContainer)
									{
										RecursionOpenAndCloseDoor(It, Degree);
									}
								}
								IncludeModel->OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf);
							}
						}
					}
				}
			}
		}
	}
}

void UDSSelectOperateSubsystem::OpenDrawerDoor(UDSBaseModel* InModel, bool bOpenDrawer)
{
	bool bInMultiModel = false;
	bool bNeedCreateNewMultiModel = false;
	
	TArray<UDSBaseModel*> DrawerModels;
	if (InModel != nullptr && InModel->GetModelType() == EDSModelType::E_Custom_FunctionalDrawer)
	{
		DrawerModels.Add(InModel);
	}
	else if (UDSMultiModel* MultiModel = Cast<UDSMultiModel>(InModel))
	{
		DrawerModels = MultiModel->GetIncludeModel().FilterByPredicate([](UDSBaseModel* IncludeModel){ return IncludeModel != nullptr && IncludeModel->GetModelType() == EDSModelType::E_Custom_FunctionalDrawer; });

		bInMultiModel = true;
		bNeedCreateNewMultiModel = DrawerModels.Num() == MultiModel->GetIncludeModel().Num();
	}

	FString RevokeUUID = FGuid::NewGuid().ToString();

	TArray<FString> DrawerIds;
	TSet<UDSCupboardModel*> ProcessedRootModels;
	for (UDSBaseModel* Model : DrawerModels)
	{
		UDSCupboardModel* DrawerModel = Cast<UDSCupboardModel>(Model);

		FParameterData* FoundParam = DrawerModel->GetModelInfoRef().ComponentTreeData->ComponentParameters.FindByPredicate([](const FParameterData& InParam){ return InParam.Data.name == TEXT("DKJD"); });
		if (FoundParam == nullptr)
		{
			continue;
		}

		bool bIsDrawerOpening = FCString::Atoi(*FoundParam->Data.value) != 0;
		if (bIsDrawerOpening == bOpenDrawer)
		{
			continue;
		}

		UDSCupboardModel* RootModel = DrawerModel->GetRootCupboardModel();
		if (!ProcessedRootModels.Contains(RootModel))
		{
			ProcessedRootModels.Add(RootModel);

			FDSRevokePushData PushData(EDSPushDataType::E_Custom, FDSModelExecuteType::ExecuteUpdateSelf, false);
			UDSRevokeLibrary::UpdatePushDataPropertyUnion(RootModel, PushData.ExecuteType, {}, PushData);
			UDSMVCSubsystem::GetInstance()->OnExecuteFromFunctionBarOrPropertyWidgetUnion(RootModel, FDSModelExecuteType::ExecuteUpdateSelf, PushData, RevokeUUID, false);
		}
		
		FoundParam->Data.value = bOpenDrawer ? TEXT("1") : TEXT("0");
		FoundParam->Data.expression = FoundParam->Data.value;
		
		DrawerIds.Add(DrawerModel->GetComponentTreeDataRef()->UUID);
	}

	for (UDSBaseModel* RootModel : ProcessedRootModels)
	{
		RootModel->OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf, FDSBroadcastMarkData::OnlyOutlineBroadcastMark);
	}

	TArray<UDSBaseModel*> DrawerModelsForSelect;
	TArray<UDSBaseModel*> AllDrawerModels = UDSMVCSubsystem::GetInstance()->GetModels(EDSModelType::E_Custom_FunctionalDrawer);
	for (UDSBaseModel* Model : AllDrawerModels)
	{
		UDSCupboardModel* DrawerModel = Cast<UDSCupboardModel>(Model);
		if (DrawerModel == nullptr)
		{
			continue;
		}

		if (DrawerIds.Contains(DrawerModel->GetModelInfoRef().ComponentTreeData->UUID))
		{
			DrawerModelsForSelect.Add(DrawerModel);
		}
	}

	if (DrawerModelsForSelect.Num() == 1 && !bInMultiModel)
	{
		UDSMVCSubsystem::GetInstance()->SetCurrentModel(DrawerModelsForSelect[0]);
	}
	else if (bNeedCreateNewMultiModel)
	{
		UDSMultiModel* NewMultiModel = UDSMultiModel::CreateMultiModel();
		NewMultiModel->AddSelect(DrawerModelsForSelect);
		UDSMVCSubsystem::GetInstance()->SetCurrentModel(NewMultiModel);
	}
	else if (UDSMultiModel* MultiModel = Cast<UDSMultiModel>(InModel))
	{
		MultiModel->AddSelect(DrawerModelsForSelect);
	}

	UDSUISubsystem::GetInstance()->ProcessStateEvent(UDSMVCSubsystem::GetInstance()->GetCurrentModel(), EUIOperationType::Selected, UDSMVCSubsystem::GetInstance()->GetStateType());
}

void UDSSelectOperateSubsystem::DisplayModelsByModelType(EDisplayItemType InType, bool InDisplay)
{
	switch (InType)
	{
	// TODO: Fill file operations logic.
	case EDisplayItemType::Ceiling:
		break;
	case EDisplayItemType::TopLine:
		break;
	case EDisplayItemType::SkirtingLine:
		break;
	case EDisplayItemType::TracingDiagram:
		{
			UDSMVCSubsystem::GetInstance()->GetGlobalDisPlayParams().IsTracingDiagramShow = InDisplay;
			if (UDSFileSubsystem::GetInstance()->HasSetFacsimile)
			{
				UDSUISubsystem::GetInstance()->ShowFacsimileActor(InDisplay);
			}
			else
			{
				UDSUISubsystem::GetInstance()->SetToastState(EToastState::EWarning, TEXT("暂未上传临摹图"), TEXT(""), true);
			}
		}
		break;
	case EDisplayItemType::RoomName:
		{
			UDSMVCSubsystem::GetInstance()->GetGlobalDisPlayParams().IsRoomNameShow = InDisplay;
			for (auto& It : UDSMVCSubsystem::GetInstance()->GetModels(EDSModelType::E_House_Area))
			{
				auto LabelModel = Cast<UDSHouseAreaModel>(It)->GetLabelModel();
				auto View = Cast<ADSHouseAreaLabelView>(UDSMVCSubsystem::GetInstance()->GetView(LabelModel));
				View->GetBpView()->SetNameVisiable(InDisplay);
			}
		}
		break;
	case EDisplayItemType::Area:
		{
			UDSMVCSubsystem::GetInstance()->GetGlobalDisPlayParams().IsAreaShow = InDisplay;
			for (auto& It : UDSMVCSubsystem::GetInstance()->GetModels(EDSModelType::E_House_Area))
			{
				auto LabelModel = Cast<UDSHouseAreaModel>(It)->GetLabelModel();
				auto View = Cast<ADSHouseAreaLabelView>(UDSMVCSubsystem::GetInstance()->GetView(LabelModel));
				View->GetBpView()->SetAreaVisiable(InDisplay);
			}
		}
		break;
	case EDisplayItemType::TextMark:
		break;
	case EDisplayItemType::FurnitureOnFloor:
		{
			UDSMVCSubsystem::GetInstance()->GetGlobalDisPlayParams().IsFurnitureOnFloorShow = InDisplay;
			for (auto& It : UDSMVCSubsystem::GetInstance()->GetModels(EDSModelType::E_Furniture_HouseFurniture))
			{
				if (It == UDSMVCSubsystem::GetInstance()->GetCurrentModel())
				{
					It->OnExecuteAction(FDSModelExecuteType::ExecuteUnSelect);
					UDSMVCSubsystem::GetInstance()->SetCurrentModel(nullptr);
					UDSUISubsystem::GetInstance()->HiddenUI();
				}
				It->OnExecuteAction(InDisplay ? FDSModelExecuteType::ExecuteUnHidden : FDSModelExecuteType::ExecuteHidden, FDSBroadcastMarkData::OnlyOutlineBroadcastMark);
			}
		}
		break;
	case EDisplayItemType::FurnitureOnWall:
		break;
	case EDisplayItemType::FurnitureOnRoof:
		break;
	case EDisplayItemType::CustomCupboard:
		{
			UDSMVCSubsystem::GetInstance()->GetGlobalDisPlayParams().IsCustomCupboardShow = InDisplay;

			TArray<UDSBaseModel*> AllCabinets;
			AllCabinets.Append(UDSMVCSubsystem::GetInstance()->GetModels(EDSModelType::E_Custom_UpperCabinet));
			AllCabinets.Append(UDSMVCSubsystem::GetInstance()->GetModels(EDSModelType::E_Custom_BaseCabinet));
			AllCabinets.Append(UDSMVCSubsystem::GetInstance()->GetModels(EDSModelType::E_Custom_CornerCabinet));
			AllCabinets.Append(UDSMVCSubsystem::GetInstance()->GetModels(EDSModelType::E_Custom_TallCabinet));
			AllCabinets.Append(UDSMVCSubsystem::GetInstance()->GetModels(EDSModelType::E_Custom_WallCabinet));
			AllCabinets.Append(UDSMVCSubsystem::GetInstance()->GetModels(EDSModelType::E_Custom_WallBoardCabinet));

			for (auto& It : AllCabinets)
			{
				if (It == UDSMVCSubsystem::GetInstance()->GetCurrentModel())
				{
					It->OnExecuteAction(FDSModelExecuteType::ExecuteUnSelect);
					UDSMVCSubsystem::GetInstance()->SetCurrentModel(nullptr);
					UDSUISubsystem::GetInstance()->HiddenUI();
				}
				It->OnExecuteAction(InDisplay ? FDSModelExecuteType::ExecuteUnHidden : FDSModelExecuteType::ExecuteHidden, FDSBroadcastMarkData::OnlyOutlineBroadcastMark);
			}
		}
		break;
	case EDisplayItemType::AllCustomFurniture:
		break;
	default:
		break;
	}
}

void UDSSelectOperateSubsystem::AddPoint(UDSBaseModel* InModel)
{
	if (InModel->GetModelType() == EDSModelType::E_Generated_CounterTop_Line)
	{
		auto LineModel = Cast<UDSCounterTopLineModel>(InModel);
		if (LineModel)
		{
			UDSCounterTopBaseModel* CTModel = LineModel->GetCounterTopModel();
			if (CTModel != nullptr)
			{
				FDSCounterTopPushData PushData(EDSCTRevokeType::E_ReGenerateLinePoint);
				UDSRevokeLibrary::UpdatePushDataProperty(CTModel, PushData);
				FDSRevokePushData T_PushData(EDSPushDataType::E_CounterTop, FDSModelExecuteType::ExecuteUpdateSelf, false);
				T_PushData.SetData(PushData);
				UDSMVCSubsystem::GetInstance()->OnExecuteFromFunctionBarOrPropertyWidgetUnion(
					CTModel,
					FDSModelExecuteType::ExecuteUpdateSelf,
					T_PushData,
					TEXT(""),
					false);
			}

			LineModel->InsertPoint();
		}
	}

	if (InModel->GetModelType() == EDSModelType::E_Generated_LineOrnamentBase_Line)
	{
		UDSLineOrnamentLineBaseModel* LineModel = Cast<UDSLineOrnamentLineBaseModel>(InModel);
		LineModel->InsertPoint();
	}
}

void UDSSelectOperateSubsystem::RecursionOpenAndCloseDoor(const TSharedPtr<FMultiComponentDataItem>& DoorContainer, const int32& Degree)
{
	for (const auto& Item : DoorContainer->ChildComponent)
	{
		if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), Item->ModelType))
		{
			FParameterData* OpenData = Item->ComponentParameters.FindByPredicate([](const FParameterData& InData)
			{
				return InData.Data.name == TEXT("DKJD");
			});
			if (!OpenData)
			{
				return;
			}
			OpenData->Data.value = FString::FromInt(Degree);
			OpenData->Data.expression = FString::FromInt(Degree);
			continue;
		}
		if (UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(Item->ModelType) == EDSModelType::E_Custom_DoorContainer)
		{
			RecursionOpenAndCloseDoor(Item, Degree);
		}
	}
}
