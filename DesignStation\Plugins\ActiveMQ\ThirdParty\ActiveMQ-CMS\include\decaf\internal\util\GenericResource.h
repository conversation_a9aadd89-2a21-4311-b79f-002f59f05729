/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef _DECAF_INTERNAL_UTIL_GENERICRESOURCE_H_
#define _DECAF_INTERNAL_UTIL_GENERICRESOURCE_H_

#include <decaf/util/Config.h>

#include <decaf/internal/util/Resource.h>

namespace decaf {
namespace internal {
namespace util {

    /**
     * A Generic Resource wraps some type and will delete it when the Resource itself
     * is deleted.
     *
     * @since 1.0
     */
    template<typename T>
    class GenericResource: public Resource {
    private:

        T* managed;

    private:

        GenericResource(const GenericResource&);
        GenericResource& operator=(const GenericResource&);

    public:

        explicit GenericResource(T* value) : managed(value) {
        }

        virtual ~GenericResource() {
            try {
                delete managed;
            } catch (...) {
            }
        }

        T* getManaged() const {
            return this->managed;
        }

        void setManaged(T* value) {
            this->managed = value;
        }

    };

}}}

#endif /* _DECAF_INTERNAL_UTIL_GENERICRESOURCE_H_ */
