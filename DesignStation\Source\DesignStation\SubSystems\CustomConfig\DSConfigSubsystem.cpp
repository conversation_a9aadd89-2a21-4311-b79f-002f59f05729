﻿#include "DSConfigSubsystem.h"
#include "BasicClasses/DesignStationController.h"

DEFINE_LOG_CATEGORY(LogDSCustomConfig);

const FString CustomConfig = TEXT("Custom.Setting");

const FString CustomConfigDir = FConfigCacheIni::NormalizeConfigIniPath(FPaths::ProjectContentDir() + TEXT("Config") + TEXT("/") + TEXT("CustomSetting.ini"));

#define WRITE_CONFIG_WITH_PARAM_TYPE(Section, ParamType, ParamName, ParamValue) \
if (!GConfig) return; \
GConfig->Set##ParamType(Section, ParamName, ParamValue, *CustomConfigDir); \
GConfig->Flush(false, *CustomConfigDir)

#define READ_CONFIG_WITH_PARAM_TYPE(Section, ParamType, ParamName, ParamValue) \
if (!GConfig) return; \
GConfig->Get##ParamType(Section, ParamName, ParamValue, *CustomConfigDir);

#define IS_EQUAL(V1, V2) \
if(V1 == V2) return;

extern const float UNIT_MM_TO_CM;

extern const float UNIT_CM_TO_MM;

UDSConfigSubsystem::UDSConfigSubsystem()
	: DefaultFloorHeight(0) {}

void UDSConfigSubsystem::Initialize(FSubsystemCollectionBase& Collection)
{
	//ConfigFile默认NoSave = true 不会写入文件 这里修改为true
	if (GConfig)
	{
		FConfigFile* File = GConfig->Find(*CustomConfigDir);
		if (!File)
		{
			return;
		}
		File->NoSave = false;
	}
	InitDSGlobalSetting();

	Super::Initialize(Collection);
}

void UDSConfigSubsystem::Deinitialize() {}

UDSConfigSubsystem* UDSConfigSubsystem::GetInstance()
{
	return ADesignStationController::Get()->GetGameInstance()->GetSubsystem<UDSConfigSubsystem>();
}

void UDSConfigSubsystem::FormatCurConfigWallHeightForCalculate(double& ModifyValue)
{
	ModifyValue = GetValue_Wall_Float(DSSetting::Wall::EWallType::E_DefaultWallH) * UNIT_MM_TO_CM;
}

bool UDSConfigSubsystem::GetSnapTipShow()
{
	bool IsTipShow = true;
	if (!GConfig)
	{
		return IsTipShow;
	}

	GConfig->Flush(true, *CustomConfigDir);

	GConfig->GetBool(
		*CustomConfig,
		TEXT("IsSnapTipShow"),
		IsTipShow,
		*CustomConfigDir
	);
	return IsTipShow;
}

void UDSConfigSubsystem::SetSnapTipShow(bool IsShow)
{
	if (!GConfig)
	{
		return;
	}

	GConfig->SetBool(
		*CustomConfig,
		TEXT("IsSnapTipShow"),
		IsShow,
		*CustomConfigDir
	);

	GConfig->Flush(false, *CustomConfigDir);
}

void UDSConfigSubsystem::InitDSGlobalSetting()
{
	InitNormal();
	InitTwoDimensionSetting();
	InitThreeDimensionSetting();
	InitWallSetting();
	InitUseCustomSetting();
	InitCameraSettingInfo();
	InitFloorHeight();
	InitDefaultRenderMaterial();
	InitDefaultLocalSavePath();
	InitIsDing();
}

#define NORMAL_SETTING TEXT("Custom.General")
#define CONFIG_GU_NAME TEXT("GlobalUnit")
#define CONFIG_AS_NAME TEXT("AutoSave")

void UDSConfigSubsystem::InitDefaultRenderMaterial()
{
	READ_CONFIG_WITH_PARAM_TYPE(TEXT("Custom.DefaultRenderMaterial"), String, TEXT("DefaultWallMaterial"), DefaultWallMaterialPath);
	READ_CONFIG_WITH_PARAM_TYPE(TEXT("Custom.DefaultRenderMaterial"), String, TEXT("DefaultAreaMaterial"), DefaultAreaMaterialPath);
}

void UDSConfigSubsystem::InitDefaultLocalSavePath()
{
	READ_CONFIG_WITH_PARAM_TYPE(TEXT("Custom.DefaultLocalSavePath"), String, TEXT("DefaultLocalSavePath"), DefaultLocalSavePath);
}

void UDSConfigSubsystem::InitIsDing()
{
	READ_CONFIG_WITH_PARAM_TYPE(TEXT("Custom.DefaultIsDing"), Bool, TEXT("DefaultIsDing"), DefaultIsDing);
}

FString UDSConfigSubsystem::GetDefaultWallMaterialPath()
{
	return DefaultWallMaterialPath;
}

FString UDSConfigSubsystem::GEtDefaultAreaMaterialPath()
{
	return DefaultAreaMaterialPath;
}

FString UDSConfigSubsystem::GetDefaultLocalSavePath()
{
	return DefaultLocalSavePath;
}

FString UDSConfigSubsystem::GetDefaultXmlSavePath()
{
	return DefaultXmlSavePath;
}

void UDSConfigSubsystem::SetDefaultLocalSavePath(const FString& InPath)
{
	DefaultLocalSavePath = InPath;
	WRITE_CONFIG_WITH_PARAM_TYPE(TEXT("Custom.DefaultLocalSavePath"), String, TEXT("DefaultLocalSavePath"), *InPath);
}

void UDSConfigSubsystem::SetDefaultXmlSavePath(const FString& InPath)
{
	DefaultXmlSavePath = InPath;
	WRITE_CONFIG_WITH_PARAM_TYPE(TEXT("Custom.DefaultXmlSavePath"), String, TEXT("DefaultXmlSavePath"), *InPath);
}

bool UDSConfigSubsystem::GetDefaultIsDing()
{
	return DefaultIsDing;
}

void UDSConfigSubsystem::SetDefaultIsDing(bool InBool)
{
	DefaultIsDing = InBool;
	WRITE_CONFIG_WITH_PARAM_TYPE(TEXT("Custom.DefaultIsDing"), Bool, TEXT("DefaultIsDing"), InBool);
}

void UDSConfigSubsystem::GetLineLightIntensityLimit(float& OutMin, float& OutMax) const
{
	READ_CONFIG_WITH_PARAM_TYPE(TEXT("Custom.RenderLightSeting"), Float, TEXT("LightIntensityMin_Line"), OutMin);
	READ_CONFIG_WITH_PARAM_TYPE(TEXT("Custom.RenderLightSeting"), Float, TEXT("LightIntensityMax_Line"), OutMax);
}

void UDSConfigSubsystem::GetIESLightIntensityLimit(float& OutMin, float& OutMax) const
{
	READ_CONFIG_WITH_PARAM_TYPE(TEXT("Custom.RenderLightSeting"), Float, TEXT("LightIntensityMin_IES"), OutMin);
	READ_CONFIG_WITH_PARAM_TYPE(TEXT("Custom.RenderLightSeting"), Float, TEXT("LightIntensityMax_IES"), OutMax);
}

void UDSConfigSubsystem::GetDefaultLightIntensityLimit(float& OutMin, float& OutMax) const
{
	READ_CONFIG_WITH_PARAM_TYPE(TEXT("Custom.RenderLightSeting"), Float, TEXT("LightIntensityMin_Default"), OutMin);
	READ_CONFIG_WITH_PARAM_TYPE(TEXT("Custom.RenderLightSeting"), Float, TEXT("LightIntensityMax_Default"), OutMax);
}

void UDSConfigSubsystem::InitNormal()
{
	READ_CONFIG_WITH_PARAM_TYPE(NORMAL_SETTING, Int, CONFIG_GU_NAME, GS.GlobalUnit);
	READ_CONFIG_WITH_PARAM_TYPE(NORMAL_SETTING, Int, CONFIG_AS_NAME, GS.AutoSave);
}

int32 UDSConfigSubsystem::GetValue_Normal_Int(const DSSetting::Normal::EGeneralType& ValueType)
{
	switch (ValueType)
	{
	case DSSetting::Normal::EGeneralType::E_GlobalUnit: return GS.GlobalUnit;
		break;
	case DSSetting::Normal::EGeneralType::E_AutoSave: return GS.AutoSave;
		break;
	default:
		{
			checkf(false, TEXT("current normal no more int setting"));
			break;
		}
	}

	return INDEX_NONE;
}

void UDSConfigSubsystem::SetValue_Normal_Int(const DSSetting::Normal::EGeneralType& ValueType, int32 NewValue)
{
	FString ParamName = TEXT("");
	switch (ValueType)
	{
	case DSSetting::Normal::EGeneralType::E_GlobalUnit:
		ParamName = CONFIG_GU_NAME;
		GS.GlobalUnit = NewValue;
		break;
	case DSSetting::Normal::EGeneralType::E_AutoSave:
		ParamName = CONFIG_AS_NAME;
		GS.AutoSave = NewValue;
		break;
	default:
		{
			checkf(false, TEXT("current two dimension no more bool setting"));
			break;
		}
	}

	WRITE_CONFIG_WITH_PARAM_TYPE(NORMAL_SETTING, Int, *ParamName, NewValue);
}
#undef  NORMAL_SETTING
#undef CONFIG_GU_NAME
#undef CONFIG_AS_NAME

#define TD_SETTING TEXT("Custom.TwoDimension")
#define CONFIG_WL_NAME TEXT("bWallLock")
#define CONFIG_SS_NAME TEXT("bSizeShow")
#define CONFIG_BS_NAME TEXT("bBackgridShow")
#define CONFIG_ANS_NAME TEXT("bAreaNameShow")
#define CONFIG_WAS_NAME TEXT("bWallAngleShow")
#define CONFIG_ASD_NAME TEXT("AffectSnapDis")

void UDSConfigSubsystem::InitTwoDimensionSetting()
{
	READ_CONFIG_WITH_PARAM_TYPE(TD_SETTING, Bool, CONFIG_WL_NAME, TWDI.bWallLock);
	READ_CONFIG_WITH_PARAM_TYPE(TD_SETTING, Bool, CONFIG_SS_NAME, TWDI.bSizeShow);
	READ_CONFIG_WITH_PARAM_TYPE(TD_SETTING, Bool, CONFIG_BS_NAME, TWDI.bBackgridShow);
	READ_CONFIG_WITH_PARAM_TYPE(TD_SETTING, Bool, CONFIG_ANS_NAME, TWDI.bAreaNameShow);
	READ_CONFIG_WITH_PARAM_TYPE(TD_SETTING, Bool, CONFIG_WAS_NAME, TWDI.bWallAngleShow);
	READ_CONFIG_WITH_PARAM_TYPE(TD_SETTING, Float, CONFIG_ASD_NAME, TWDI.AffectSnapDis);
	READ_CONFIG_WITH_PARAM_TYPE(TD_SETTING, Float, TEXT("DragSnapDistance"), TWDI.DragSnapDistance);
}

bool UDSConfigSubsystem::GetValue_TD_Bool(const DSSetting::TWD::ETWDType& ValueType)
{
	switch (ValueType)
	{
	case DSSetting::TWD::ETWDType::E_WallLock: return TWDI.bWallLock;
		break;
	case DSSetting::TWD::ETWDType::E_SizeShow: return TWDI.bSizeShow;
		break;
	case DSSetting::TWD::ETWDType::E_BackgridShow: return TWDI.bBackgridShow;
		break;
	case DSSetting::TWD::ETWDType::E_AreaNameShow: return TWDI.bAreaNameShow;
		break;
	case DSSetting::TWD::ETWDType::E_WallAngleShow: return TWDI.bWallAngleShow;
		break;
	default:
		{
			checkf(false, TEXT("current two dimension no more bool setting"));
			break;
		}
	}

	return false;
}

void UDSConfigSubsystem::SetValue_TD_Bool(const DSSetting::TWD::ETWDType& ValueType, bool NewVI)
{
	FString ParamName = TEXT("");
	switch (ValueType)
	{
	case DSSetting::TWD::ETWDType::E_WallLock:
		ParamName = CONFIG_WL_NAME;
		TWDI.bWallLock = NewVI;
		break;
	case DSSetting::TWD::ETWDType::E_SizeShow:
		ParamName = CONFIG_SS_NAME;
		TWDI.bSizeShow = NewVI;
		break;
	case DSSetting::TWD::ETWDType::E_BackgridShow:
		ParamName = CONFIG_BS_NAME;
		TWDI.bBackgridShow = NewVI;
		break;
	case DSSetting::TWD::ETWDType::E_AreaNameShow:
		ParamName = CONFIG_ANS_NAME;
		TWDI.bAreaNameShow = NewVI;
		break;
	case DSSetting::TWD::ETWDType::E_WallAngleShow:
		ParamName = CONFIG_WAS_NAME;
		TWDI.bWallAngleShow = NewVI;
		break;
	default:
		{
			checkf(false, TEXT("current two dimension no more bool setting"));
			break;
		}
	}

	WRITE_CONFIG_WITH_PARAM_TYPE(TD_SETTING, Bool, *ParamName, NewVI);
}

float UDSConfigSubsystem::GetValue_TD_Float(const DSSetting::TWD::ETWDType& ValueType)
{
	switch (ValueType)
	{
	case DSSetting::TWD::ETWDType::E_AffectSnapDis: return TWDI.AffectSnapDis;
		break;
	case DSSetting::TWD::ETWDType::E_DragSnapDistance:
		{
			return TWDI.DragSnapDistance;
		}
	default:
		{
			checkf(false, TEXT("current two dimension no more float setting"));
			break;
		}
	}

	return 0.0f;
}

void UDSConfigSubsystem::SetValue_TD_Float(const DSSetting::TWD::ETWDType& ValueType, const float& NewVF)
{
	FString ParamName = TEXT("");
	switch (ValueType)
	{
	case DSSetting::TWD::ETWDType::E_AffectSnapDis:
		ParamName = CONFIG_ASD_NAME;
		TWDI.AffectSnapDis = NewVF;
		break;
	case DSSetting::TWD::ETWDType::E_DragSnapDistance:
		{
			ParamName = TEXT("DragSnapDistance");
			TWDI.DragSnapDistance = NewVF;
			break;
		}
	default:
		{
			checkf(false, TEXT("current two dimension no more float setting"));
			break;
		}
	}

	WRITE_CONFIG_WITH_PARAM_TYPE(TD_SETTING, Float, *ParamName, NewVF);
}

void UDSConfigSubsystem::GetTwoDimension_All(DSSetting::TWD::FTwoDimensionInfo& OutTWDI)
{
	OutTWDI = TWDI;
}
#undef TD_SETTING
#undef CONFIG_WL_NAME
#undef CONFIG_SS_NAME
#undef CONFIG_BS_NAME
#undef CONFIG_ANS_NAME
#undef CONFIG_WAS_NAME
#undef CONFIG_ASD_NAME

#define THD_SETTING TEXT("Custom.ThreeDimension")
#define CONFIG_SBL_NAME TEXT("bSBLGenerate")
#define CONFIG_TAL_NAME TEXT("bTALGenerate")

void UDSConfigSubsystem::InitThreeDimensionSetting()
{
	READ_CONFIG_WITH_PARAM_TYPE(THD_SETTING, Bool, CONFIG_SBL_NAME, THDI.bSBLineGenerate);
	READ_CONFIG_WITH_PARAM_TYPE(THD_SETTING, Bool, CONFIG_TAL_NAME, THDI.bTALineGenerate);
}

bool UDSConfigSubsystem::GetValue_THD_Bool(const DSSetting::THD::ETHDType& ValueType)
{
	switch (ValueType)
	{
	case DSSetting::THD::ETHDType::E_SLGene: return THDI.bSBLineGenerate;
		break;
	case DSSetting::THD::ETHDType::E_TLGene: return THDI.bTALineGenerate;
		break;
	default:
		{
			checkf(false, TEXT("current three dimension no more bool setting"));
			break;
		}
	}

	return false;
}

void UDSConfigSubsystem::SetValue_THD_Bool(const DSSetting::THD::ETHDType& ValueType, bool NewVB)
{
	FString ParamName = TEXT("");
	switch (ValueType)
	{
	case DSSetting::THD::ETHDType::E_SLGene:
		ParamName = CONFIG_SBL_NAME;
		THDI.bSBLineGenerate = NewVB;
		break;
	case DSSetting::THD::ETHDType::E_TLGene:
		ParamName = CONFIG_TAL_NAME;
		THDI.bTALineGenerate = NewVB;
		break;
	default:
		{
			checkf(false, TEXT("current two dimension no more float setting"));
			break;
		}
	}

	WRITE_CONFIG_WITH_PARAM_TYPE(THD_SETTING, Bool, *ParamName, NewVB);
}

void UDSConfigSubsystem::GetThreeDimension_All(DSSetting::THD::FThreeDimensionInfo& OutTHDI)
{
	OutTHDI = THDI;
}
#undef THD_SETTING
#undef CONFIG_SBL_NAME
#undef CONFIG_TAL_NAME

#define WALL_SETTING TEXT("Custom.Wall")
#define CONFIG_WDT_NAME TEXT("DrawTypeWDT")
#define CONFIG_DWH_NAME TEXT("DefaultWH")
#define CONFIG_OWT_NAME TEXT("OutsideWT")
#define CONFIG_IWT_NAME TEXT("InnerSideWT")

void UDSConfigSubsystem::InitWallSetting()
{
	READ_CONFIG_WITH_PARAM_TYPE(WALL_SETTING, Int, CONFIG_WDT_NAME, WI.DrawWallType);
	READ_CONFIG_WITH_PARAM_TYPE(WALL_SETTING, Float, CONFIG_DWH_NAME, WI.DefaultWallHeight);
	READ_CONFIG_WITH_PARAM_TYPE(WALL_SETTING, Float, CONFIG_OWT_NAME, WI.OutSideWallThick);
	READ_CONFIG_WITH_PARAM_TYPE(WALL_SETTING, Float, CONFIG_IWT_NAME, WI.InnerSideWallThick);
}

float UDSConfigSubsystem::GetValue_Wall_Float(const DSSetting::Wall::EWallType& ValueType)
{
	switch (ValueType)
	{
	case DSSetting::Wall::EWallType::E_DefaultWallH: return WI.DefaultWallHeight;
		break;
	case DSSetting::Wall::EWallType::E_OutSideWallT: return WI.OutSideWallThick;
		break;
	case DSSetting::Wall::EWallType::E_InnerSideWallT: return WI.InnerSideWallThick;
		break;
	default:
		{
			checkf(false, TEXT("current wall no more float setting"));
			break;
		}
	}

	return 0.0f;
}

void UDSConfigSubsystem::SetValue_Wall_Float(const DSSetting::Wall::EWallType& ValueType, const float& NewVF)
{
	FString ParamName = TEXT("");
	switch (ValueType)
	{
	case DSSetting::Wall::EWallType::E_DefaultWallH:
		ParamName = CONFIG_DWH_NAME;
		WI.DefaultWallHeight = NewVF;
		break;
	case DSSetting::Wall::EWallType::E_OutSideWallT:
		ParamName = CONFIG_OWT_NAME;
		WI.OutSideWallThick = NewVF;
		break;
	case DSSetting::Wall::EWallType::E_InnerSideWallT:
		ParamName = CONFIG_IWT_NAME;
		WI.InnerSideWallThick = NewVF;
		break;
	default:
		{
			checkf(false, TEXT("current wall no more float setting"));
			break;
		}
	}

	WRITE_CONFIG_WITH_PARAM_TYPE(WALL_SETTING, Float, *ParamName, NewVF);
}

int32 UDSConfigSubsystem::GetValue_Wall_Int(const DSSetting::Wall::EWallType& ValueType)
{
	switch (ValueType)
	{
	case DSSetting::Wall::EWallType::E_DrawWallType: return WI.DrawWallType;
		break;
	default:
		{
			checkf(false, TEXT("current wall no more int setting"));
			break;
		}
	}

	return 0;
}

void UDSConfigSubsystem::SetValue_Wall_Int(const DSSetting::Wall::EWallType& ValueType, const int32& NewVF)
{
	FString ParamName = TEXT("");
	switch (ValueType)
	{
	case DSSetting::Wall::EWallType::E_DrawWallType:
		ParamName = CONFIG_WDT_NAME;
		WI.DrawWallType = NewVF;
		break;
	default:
		{
			checkf(false, TEXT("current wall no more int setting"));
			break;
		}
	}

	WRITE_CONFIG_WITH_PARAM_TYPE(WALL_SETTING, Int, *ParamName, NewVF);
}
#undef WALL_SETTING
#undef CONFIG_WDT_NAME
#undef CONFIG_DWH_NAME
#undef CONFIG_OWT_NAME
#undef CONFIG_IWT_NAME


/*--------------------- Use Custom Setting ------------------------------*/

#define USE_CUSTOM_SETTING			TEXT("Custom.UseCustom")
#define CONFIG_DSD_NAME				TEXT("DefaultSnapDis")
#define CONFIG_SDMI_NAME			TEXT("SnapDisMin")
#define CONFIG_SDMA_NAME			TEXT("SnapDisMax")
#define CONFIG_RAS_NAME				TEXT("bRotAngleShow")
#define CONFIG_PPS_NAME				TEXT("bPartParamsShow")

void UDSConfigSubsystem::InitUseCustomSetting()
{
	READ_CONFIG_WITH_PARAM_TYPE(USE_CUSTOM_SETTING, Float, CONFIG_DSD_NAME, CI.DefaultSnapDis);
	READ_CONFIG_WITH_PARAM_TYPE(USE_CUSTOM_SETTING, Float, CONFIG_SDMI_NAME, CI.SnapDisMin);
	READ_CONFIG_WITH_PARAM_TYPE(USE_CUSTOM_SETTING, Float, CONFIG_SDMA_NAME, CI.SnapDisMax);
	READ_CONFIG_WITH_PARAM_TYPE(USE_CUSTOM_SETTING, Bool, CONFIG_RAS_NAME, CI.bRotAngleShow);
	READ_CONFIG_WITH_PARAM_TYPE(USE_CUSTOM_SETTING, Bool, CONFIG_PPS_NAME, CI.bShowPartParams);
}

bool UDSConfigSubsystem::GetValue_Custom_Bool(const DSSetting::Custom::ECustomType& ValueType)
{
	switch (ValueType)
	{
	case DSSetting::Custom::ECustomType::E_AngleShow: return CI.bRotAngleShow; break;
	case DSSetting::Custom::ECustomType::E_ShowPartParams: return CI.bShowPartParams; break;
	default:
		{
			checkf(false, TEXT("current custom no more bool setting"));
			break;
		}
	}

	return false;
}

void UDSConfigSubsystem::SetValue_Custom_Bool(const DSSetting::Custom::ECustomType& ValueType, bool NewVB)
{
	FString ParamName = TEXT("");
	switch (ValueType)
	{
	case DSSetting::Custom::ECustomType::E_AngleShow: ParamName = CONFIG_RAS_NAME; CI.bRotAngleShow = NewVB;; break;
	case DSSetting::Custom::ECustomType::E_ShowPartParams: ParamName = CONFIG_PPS_NAME; CI.bShowPartParams = NewVB; break;
	default:
		{
			checkf(false, TEXT("current two dimension no more bool setting"));
			break;
		}
	}
	
	WRITE_CONFIG_WITH_PARAM_TYPE(USE_CUSTOM_SETTING, Bool, *ParamName, NewVB);
}

float UDSConfigSubsystem::GetValue_Custom_Float(const DSSetting::Custom::ECustomType& ValueType)
{
	switch (ValueType)
	{
	case DSSetting::Custom::ECustomType::E_DefaultSnapD: return CI.DefaultSnapDis;
		break;
	default:
		{
			checkf(false, TEXT("current custom no more float setting"));
			break;
		}
	}

	return 0.0f;
}

void UDSConfigSubsystem::SetValue_Custom_Float(const DSSetting::Custom::ECustomType& ValueType, const float& NewVF)
{
	FString ParamName = TEXT("");
	switch (ValueType)
	{
	case DSSetting::Custom::ECustomType::E_DefaultSnapD: ParamName = CONFIG_DSD_NAME;
		break;
	default:
		{
			checkf(false, TEXT("current two dimension no more bool setting"));
			break;
		}
	}
	CI.DefaultSnapDis = NewVF;
	WRITE_CONFIG_WITH_PARAM_TYPE(USE_CUSTOM_SETTING, Float, *ParamName, NewVF);
}

#undef USE_CUSTOM_SETTING
#undef CONFIG_DSD_NAME
#undef CONFIG_SDMI_NAME
#undef CONFIG_SDMA_NAME
#undef CONFIG_RAS_NAME
#undef CONFIG_PPS_NAME


/*--------------------- Camera Setting ------------------------------*/

#define CS_SETTING TEXT("Custom.CameraSetting")
#define CONFIG_CS_SPEED			TEXT("VtSpeed")
#define CONFIG_CS_VIEWLOCKED	TEXT("BViewLocked")
#define CONFIG_CS_FOV			TEXT("InFOV")

void UDSConfigSubsystem::InitCameraSettingInfo()
{
	READ_CONFIG_WITH_PARAM_TYPE(CS_SETTING, Vector2D, CONFIG_CS_SPEED, CS.Speed);
	READ_CONFIG_WITH_PARAM_TYPE(CS_SETTING, Bool, CONFIG_CS_VIEWLOCKED, CS.bViewLocked);
	READ_CONFIG_WITH_PARAM_TYPE(CS_SETTING, Float, CONFIG_CS_FOV, CS.InFOV);
}

/*Only Call By DSCameraSubSystem*/
void UDSConfigSubsystem::GetValue_CameraSetting(DSSetting::CameraSetting::FCameraSettingInfo& SettingInfo)
{
	SettingInfo.Speed = CS.Speed;
	SettingInfo.bViewLocked = CS.bViewLocked;
	SettingInfo.InFOV = CS.InFOV;
}

/*Only Call By DSCameraSubSystem*/
void UDSConfigSubsystem::SetValue_CameraSettingSpeed(FVector2D Speed)
{
	CS.Speed = Speed;
	FString ParamName = CONFIG_CS_SPEED;
	WRITE_CONFIG_WITH_PARAM_TYPE(CS_SETTING, Vector2D, *ParamName, Speed);
}

/*Only Call By DSCameraSubSystem*/
void UDSConfigSubsystem::SetValue_CameraSettingViewLocked(bool bViewLocked)
{
	CS.bViewLocked = bViewLocked;
	FString ParamName = CONFIG_CS_VIEWLOCKED;
	WRITE_CONFIG_WITH_PARAM_TYPE(CS_SETTING, Bool, *ParamName, bViewLocked);
}

void UDSConfigSubsystem::SetValue_CameraSettingInFOV(float InFOV)
{
	CS.InFOV = InFOV;
	FString ParamName = CONFIG_CS_FOV;
	WRITE_CONFIG_WITH_PARAM_TYPE(CS_SETTING, Float, *ParamName, InFOV);
}

#undef CS_SETTING
#undef CONFIG_CS_SPEED
#undef CONFIG_CS_VIEWLOCKED
#undef CONFIG_CS_FOV

#define FLOOR_HEIGHT_SETTING TEXT("Custom.FloorHeight")
#define DEFAUTL_FLOOOR_HEIGHT TEXT("DefaultFloorHeight")

void UDSConfigSubsystem::InitFloorHeight()
{
	READ_CONFIG_WITH_PARAM_TYPE(FLOOR_HEIGHT_SETTING, Float, DEFAUTL_FLOOOR_HEIGHT, DefaultFloorHeight);
}

float UDSConfigSubsystem::GetValue_FlootHeight(int Level)
{
	return DefaultFloorHeight * (Level - 1);
}

void UDSConfigSubsystem::SetValue_FlootHeight(float InValue)
{
	DefaultFloorHeight = InValue;
	FString ParamName = DEFAUTL_FLOOOR_HEIGHT;
	WRITE_CONFIG_WITH_PARAM_TYPE(FLOOR_HEIGHT_SETTING, Float, *ParamName, InValue);
}

int32 UDSConfigSubsystem::GetWindowLightIntensity()
{
	FConfigFile LaunchConfig;
	FString Path = FPaths::ProjectContentDir() + TEXT("Config");
	FConfigCacheIni::LoadExternalIniFile(LaunchConfig, TEXT("CustomSetting"), *FPaths::ProjectContentDir(), *Path, false);
	LaunchConfig.GetInt(TEXT("Custom.Light"), TEXT("DefaultWindowLightIntensity"), WindowLightIntensity);

	return WindowLightIntensity;
}

float UDSConfigSubsystem::GetRectLightMinSize()
{
	FConfigFile LaunchConfig;
	FString Path = FPaths::ProjectContentDir() + TEXT("Config");
	FConfigCacheIni::LoadExternalIniFile(LaunchConfig, TEXT("CustomSetting"), *FPaths::ProjectContentDir(), *Path, false);
	LaunchConfig.GetFloat(TEXT("Custom.Light"), TEXT("DefaultRectLightMinSize"), RectLightMinSize);

	return RectLightMinSize;
}

float UDSConfigSubsystem::GetRectLightSpacing()
{
	FConfigFile LaunchConfig;
	FString Path = FPaths::ProjectContentDir() + TEXT("Config");
	FConfigCacheIni::LoadExternalIniFile(LaunchConfig, TEXT("CustomSetting"), *FPaths::ProjectContentDir(), *Path, false);
	LaunchConfig.GetFloat(TEXT("Custom.Light"), TEXT("DefaultRectLightSpacing"), RectLightSpacing);

	return RectLightSpacing;
}

float UDSConfigSubsystem::GetRectLightWallDistance()
{
	FConfigFile LaunchConfig;
	FString Path = FPaths::ProjectContentDir() + TEXT("Config");
	FConfigCacheIni::LoadExternalIniFile(LaunchConfig, TEXT("CustomSetting"), *FPaths::ProjectContentDir(), *Path, false);
	LaunchConfig.GetFloat(TEXT("Custom.Light"), TEXT("DefaultRectLightWallDistance"), RectLightWallDistance);

	return RectLightWallDistance;
}

float UDSConfigSubsystem::GetRectLightIntensity()
{
	FConfigFile LaunchConfig;
	FString Path = FPaths::ProjectContentDir() + TEXT("Config");
	FConfigCacheIni::LoadExternalIniFile(LaunchConfig, TEXT("CustomSetting"), *FPaths::ProjectContentDir(), *Path, false);
	LaunchConfig.GetFloat(TEXT("Custom.Light"), TEXT("DefaultRectLightIntensity"), RectLightIntensity);

	return RectLightIntensity;
}

float UDSConfigSubsystem::GetRectLightDistanceToFloor()
{
	FConfigFile LaunchConfig;
	FString Path = FPaths::ProjectContentDir() + TEXT("Config");
	FConfigCacheIni::LoadExternalIniFile(LaunchConfig, TEXT("CustomSetting"), *FPaths::ProjectContentDir(), *Path, false);
	LaunchConfig.GetFloat(TEXT("Custom.Light"), TEXT("DefaultRectLightDistanceToFloor"), RectLightDistanceToFloor);

	return RectLightDistanceToFloor;
}
