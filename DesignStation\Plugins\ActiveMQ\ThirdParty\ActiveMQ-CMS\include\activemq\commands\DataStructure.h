/**
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef _ACTIVEMQ_COMMANDS_DATASTRUCTURE_H_
#define _ACTIVEMQ_COMMANDS_DATASTRUCTURE_H_

#include <activemq/util/Config.h>
#include <activemq/wireformat/MarshalAware.h>

namespace activemq{
namespace commands{

    class AMQCPP_API DataStructure : public wireformat::MarshalAware {
    public:

        virtual ~DataStructure() {}

        /**
         * Get the DataStructure Type as defined in CommandTypes.h
         * @return The type of the data structure
         */
        virtual unsigned char getDataStructureType() const = 0;

        /**
         * Clone this obbject and return a new instance that the
         * caller now owns, this will be an exact copy of this one
         * @return new copy of this object.
         */
        virtual DataStructure* cloneDataStructure() const = 0;

        /**
         * Copy the contents of the passed object into this objects
         * members, overwriting any existing data.
         * @return src - Source Object
         */
        virtual void copyDataStructure( const DataStructure* src ) = 0;

        /**
         * Returns a string containing the information for this DataStructure
         * such as its type and value of its elements.
         * @return formatted string useful for debugging.
         */
        virtual std::shared_ptr<std::string> toString() const = 0;

        /**
         * Compares the DataStructure passed in to this one, and returns if
         * they are equivalent.  Equivalent here means that they are of the
         * same type, and that each element of the objects are the same.
         * @return true if DataStructure's are Equal.
         */
        virtual bool equals( const DataStructure* value ) const = 0;

    };

}}

#endif /*_ACTIVEMQ_COMMANDS_DATASTRUCTURE_H_*/
