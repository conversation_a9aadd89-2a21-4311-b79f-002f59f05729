﻿// Fill out your copyright notice in the Description page of Project Settings.


#include "Lexer/ExpressionLexer.h"

#if UE_ENABLE_ICU
THIRD_PARTY_INCLUDES_START
#include "unicode/uchar.h"
THIRD_PARTY_INCLUDES_END
#endif

FExpressionLexer::FExpressionLexer()
	: Offset(0)
{
}

const FString& FExpressionLexer::GetContent() const
{
	return Content;
}

void FExpressionLexer::SetContent(const FString& InContent)
{
	Offset = 0;
	Content = InContent;
}

void FExpressionLexer::Reset()
{
	Offset = 0;
	Content.Empty();
}

FExpressionLexerToken FExpressionLexer::NextToken()
{
	EExpressionLexerState State = EExpressionLexerState::ELS_Start;
	EExpressionLexerState PreState = EExpressionLexerState::ELS_Start;

	FString LexeMe;
	int32 StartPos = Offset;
	
	while (!IsAtEnd())
	{
		TCHAR Char;
		if (!CurrentChar(Char))
		{
			break;
		}
		
		switch (State)
		{
		case EExpressionLexerState::ELS_Start:
			{
				if (FChar::IsWhitespace(Char))
				{
					LexeMe += Char;
					Advance();
					State = EExpressionLexerState::ELS_Whitespace;
				}
				else if (Char == '\'')
				{
					LexeMe += Char;
					Advance();
					State = EExpressionLexerState::ELS_CharLiteral;
				}
				else if (FChar::IsDigit(Char))
				{
					LexeMe += Char;
					Advance();
					State = EExpressionLexerState::ELS_Number;
				}
				else if (IsOperator(Char))
				{
					LexeMe += Char;
					Advance();
					State = EExpressionLexerState::ELS_Operator;
				}
				else if (Char == '\"')
				{
					LexeMe += Char;
					Advance();
					State = EExpressionLexerState::ELS_String;
				}
				else if (Char == ';')
				{
					LexeMe += Char;
					Advance();
					return FExpressionLexerToken(EExpressionLexerTokenType::Semicolon, LexeMe, FIntPoint(StartPos, Offset));
				}
				else if (Char == ',')
				{
					LexeMe += Char;
					Advance();
					return FExpressionLexerToken(EExpressionLexerTokenType::Comma, LexeMe, FIntPoint(StartPos, Offset));
				}
				else if (Char =='{')
				{
					LexeMe += Char;
					Advance();
					return FExpressionLexerToken(EExpressionLexerTokenType::LeftBrace, LexeMe, FIntPoint(StartPos, Offset));
				}
				else if (Char == '}')
				{
					LexeMe += Char;
					Advance();
					return FExpressionLexerToken(EExpressionLexerTokenType::RightBrace, LexeMe, FIntPoint(StartPos, Offset));
				}
				else if (Char == '(')
				{
					LexeMe += Char;
					Advance();
					return FExpressionLexerToken(EExpressionLexerTokenType::LeftParen, LexeMe, FIntPoint(StartPos, Offset));
				}
				else if (Char == ')')
				{
					LexeMe += Char;
					Advance();
					return FExpressionLexerToken(EExpressionLexerTokenType::RightParen, LexeMe, FIntPoint(StartPos, Offset));
				}
				else if (IsLetter(Char) || Char == '_')
				{
					LexeMe += Char;
					Advance();
					State = EExpressionLexerState::ELS_Identifier;
				}
				else if (Char == '#')
				{
					LexeMe += Char;
					Advance();
					State = EExpressionLexerState::ELS_Comment;
				}
				else
				{
					LexeMe += Char;
					throw std::runtime_error(TCHAR_TO_UTF8(*FString::Printf(TEXT("Unresolved characters: %s"), *LexeMe)));
				}
			}
			break;
		case EExpressionLexerState::ELS_Number:
			{
				if (FChar::IsDigit(Char))
				{
					LexeMe += Char;
					Advance();
				}
				else if (Char == '.')
				{
					LexeMe += Char;
					Advance();
					State = EExpressionLexerState::ELS_Float;
				}
				else if (FChar::ToLower(Char) == 'e')
				{
					LexeMe += Char;
					Advance();
					State = EExpressionLexerState::ELS_FloatExponent;
				}
				else
				{
					return FExpressionLexerToken(EExpressionLexerTokenType::Number, LexeMe, FIntPoint(StartPos, Offset));
				}
			}
			break;
		case EExpressionLexerState::ELS_Float:
			{
				if (FChar::IsDigit(Char))
				{
					LexeMe += Char;
					Advance();
				}
				else if (FChar::ToLower(Char) == 'e')
				{
					LexeMe += Char;
					Advance();
					State = EExpressionLexerState::ELS_FloatExponent;
				}
				else
				{
					return FExpressionLexerToken(EExpressionLexerTokenType::Float, LexeMe, FIntPoint(StartPos, Offset));
				}
			}
			break;
		case EExpressionLexerState::ELS_FloatExponent:
			{
				if (Char == '+' || Char == '-')
				{
					LexeMe += Char;
					Advance();
				}
				else if (FChar::IsDigit(Char))
				{
					LexeMe += Char;
					Advance();
				}
				else
				{
					FString LowerLexeMe = LexeMe.ToLower();
					if (!LowerLexeMe.EndsWith(TEXT("e")) && !LowerLexeMe.EndsWith(TEXT("e+")) && !LowerLexeMe.EndsWith(TEXT("e-")))
					{
						return FExpressionLexerToken(EExpressionLexerTokenType::Float, LexeMe, FIntPoint(StartPos, Offset));
					}
					else
					{
						throw std::runtime_error(TCHAR_TO_UTF8(*FString::Printf(TEXT("Invalid float exponent syntax: %s"), *LexeMe)));
					}
				}
			}
			break;
		case EExpressionLexerState::ELS_CharLiteral:
			{
				LexeMe += Char;
				if (Char == '\\')
				{
					Advance();

					PreState = State;
					State = EExpressionLexerState::ELS_StringEscape;
				}
				else if (IsLetterOrNumber(Char))
				{
					Advance();
				}
				else if (Char == '\'')
				{
					Advance();
					return FExpressionLexerToken(EExpressionLexerTokenType::CharLiteral, LexeMe, FIntPoint(StartPos, Offset));
				}
				else
				{
					throw std::runtime_error(TCHAR_TO_UTF8(*FString::Printf(TEXT("Invalid constant character: '%s'"), *LexeMe)));
				}
			}
			break;
		case EExpressionLexerState::ELS_String:
			{
				LexeMe += Char;
				Advance();

				if (Char == '\\')
				{
					PreState = State;
					State = EExpressionLexerState::ELS_StringEscape;
				}
				else if (Char == '"')
				{
					return FExpressionLexerToken(EExpressionLexerTokenType::String, LexeMe, FIntPoint(StartPos, Offset));
				}
			}
			break;
		case EExpressionLexerState::ELS_StringEscape:
			{
				if (Char == '\\' || Char == 't' || Char == 'r' || Char == 'b' || Char == 'n' || Char == 'f' || Char == '\'' || Char == '\"' || Char == '0')
				{
					LexeMe += Char;
				}

				if (PreState == EExpressionLexerState::ELS_CharLiteral || PreState == EExpressionLexerState::ELS_String)
				{
					Advance();
					State = PreState;
					PreState = EExpressionLexerState::ELS_StringEscape;
				}
				else
				{
					throw std::runtime_error(TCHAR_TO_UTF8(*FString::Printf(TEXT("Entered the wrong parsing process. - %s"), *LexeMe)));
				}
			}
			break;
		case EExpressionLexerState::ELS_Identifier:
			{
				if (IsLetterOrNumber(Char) || Char == '_')
				{
					LexeMe += Char;
					Advance();
				}
				else
				{
					return FExpressionLexerToken(EExpressionLexerTokenType::Identifier, LexeMe, FIntPoint(StartPos, Offset));
				}
			}
			break;
		case EExpressionLexerState::ELS_Comment:
			{
				LexeMe += Char;
				Advance();

				if (Char == '#')
				{
					return FExpressionLexerToken(EExpressionLexerTokenType::Comment, LexeMe, FIntPoint(StartPos, Offset));
				}
			}
			break;
		case EExpressionLexerState::ELS_Operator:
			{
				if (LexeMe == TEXT(">") || LexeMe == TEXT("<")
					|| LexeMe == TEXT("=") || LexeMe == TEXT("*")
					|| LexeMe == TEXT("/") || LexeMe == TEXT("%")
					|| LexeMe == TEXT("^"))
				{
					if (Char == '=')
					{
						LexeMe += Char;
						Advance();
					}

					return FExpressionLexerToken(EExpressionLexerTokenType::BinocularOperator, LexeMe, FIntPoint(StartPos, Offset));
				}
				else if (LexeMe == TEXT("-"))
				{
					if (Char == '-' || Char == '=')
					{
						LexeMe += Char;
						Advance();
						return FExpressionLexerToken(EExpressionLexerTokenType::BinocularOperator, LexeMe, FIntPoint(StartPos, Offset));
					}
					else
					{
						return FExpressionLexerToken(EExpressionLexerTokenType::MonocularOperator, LexeMe, FIntPoint(StartPos, Offset));
					}
				}
				else if (LexeMe == TEXT("+"))
				{
					if (Char == '+' || Char == '=')
					{
						LexeMe += Char;
						Advance();
						return FExpressionLexerToken(EExpressionLexerTokenType::BinocularOperator, LexeMe, FIntPoint(StartPos, Offset));
					}
					else
					{
						return FExpressionLexerToken(EExpressionLexerTokenType::MonocularOperator, LexeMe, FIntPoint(StartPos, Offset));
					}
				}
				else if (LexeMe == TEXT("!"))
				{
					if (Char == '=')
					{
						LexeMe += Char;
						Advance();
						return FExpressionLexerToken(EExpressionLexerTokenType::BinocularOperator, LexeMe, FIntPoint(StartPos, Offset));
					}
					else
					{
						return FExpressionLexerToken(EExpressionLexerTokenType::MonocularOperator, LexeMe, FIntPoint(StartPos, Offset));
					}
				}
				else if (LexeMe == TEXT("&"))
				{
					if (Char == '&' || Char == '=')
					{
						LexeMe += Char;
						Advance();
					}

					return FExpressionLexerToken(EExpressionLexerTokenType::BinocularOperator, LexeMe, FIntPoint(StartPos, Offset));
				}
				else if (LexeMe == TEXT("|"))
				{
					if (Char == '|' || Char == '=')
					{
						LexeMe += Char;
						Advance();
					}

					return FExpressionLexerToken(EExpressionLexerTokenType::BinocularOperator, LexeMe, FIntPoint(StartPos, Offset));
				}
				else
				{
					return FExpressionLexerToken(EExpressionLexerTokenType::MonocularOperator, LexeMe, FIntPoint(StartPos, Offset));
				}
			}
			break;
		case EExpressionLexerState::ELS_Whitespace:
			{
				if (FChar::IsWhitespace(Char))
				{
					LexeMe += Char;
					Advance();
				}
				else
				{
					return FExpressionLexerToken(EExpressionLexerTokenType::Whitespace, LexeMe, FIntPoint(StartPos, Offset));
				}
			}
			break;
		default:
			{
				throw std::runtime_error(TCHAR_TO_UTF8(*FString::Printf(TEXT("Unsupported morphology - %s"), *LexeMe)));
			}
			break;
		}
	}

	if (IsAtEnd() && !LexeMe.IsEmpty())
	{
		switch (State)
		{
		case EExpressionLexerState::ELS_Number:
			return FExpressionLexerToken(EExpressionLexerTokenType::Number, LexeMe, FIntPoint(StartPos, Offset));
		case EExpressionLexerState::ELS_Float:
			return FExpressionLexerToken(EExpressionLexerTokenType::Float, LexeMe, FIntPoint(StartPos, Offset));
		case EExpressionLexerState::ELS_Identifier:
			return FExpressionLexerToken(EExpressionLexerTokenType::Identifier, LexeMe, FIntPoint(StartPos, Offset));
		case EExpressionLexerState::ELS_Operator:
			return FExpressionLexerToken(EExpressionLexerTokenType::MonocularOperator, LexeMe, FIntPoint(StartPos, Offset));
		case EExpressionLexerState::ELS_Whitespace:
			return FExpressionLexerToken(EExpressionLexerTokenType::Whitespace, LexeMe, FIntPoint(StartPos, Offset));
		case EExpressionLexerState::ELS_Comment:
			return FExpressionLexerToken(EExpressionLexerTokenType::Comment, LexeMe, FIntPoint(StartPos, Offset));
		case EExpressionLexerState::ELS_CharLiteral:
			throw std::runtime_error(TCHAR_TO_UTF8(*FString::Printf(TEXT("Unclosed character constant - %s"), *LexeMe)));
		case EExpressionLexerState::ELS_String:
			throw std::runtime_error(TCHAR_TO_UTF8(*FString::Printf(TEXT("Unclosed string - %s"), *LexeMe)));
		case EExpressionLexerState::ELS_StringEscape:
			throw std::runtime_error(TCHAR_TO_UTF8(*FString::Printf(TEXT("Unclosed transfer sequence - %s"), *LexeMe)));
		default:
			break;
		}
	}

	return FExpressionLexerToken(EExpressionLexerTokenType::EndOfFile, LexeMe, FIntPoint(StartPos, Offset));
}

bool FExpressionLexer::Tokenize(TArray<FExpressionLexerToken>& OutTokens, FString& OutError)
{
	try
	{
		FExpressionLexerToken Token = NextToken();
		OutTokens.Push(Token);
		while (Token.Type != EExpressionLexerTokenType::EndOfFile)
		{
			Token = NextToken();
			OutTokens.Push(Token);
		}

		return Token.Type == EExpressionLexerTokenType::EndOfFile;
	}
	catch (std::exception& Err)
	{
		OutError = UTF8_TO_TCHAR(Err.what());
		return false;
	}
}

bool FExpressionLexer::IsOperator(const TCHAR& InChar) const
{
	static const TArray<TCHAR> Operators = {'+', '-', '*', '/', '%', '<', '>', '=', '!', '&', '|', '^', '~'};
	return Operators.Contains(InChar);
}

bool FExpressionLexer::IsLetter(const TCHAR& InChar) const
{
	return static_cast<bool>(u_isalpha(static_cast<UChar32>(InChar)));
}

bool FExpressionLexer::IsLetterOrNumber(const TCHAR& InChar) const
{
	return static_cast<bool>(u_isalnum(static_cast<UChar32>(InChar)));
}

bool FExpressionLexer::CurrentChar(TCHAR& OutChar) const
{
	if (Offset >= Content.Len())
	{
		return false;
	}

	OutChar = Content[Offset];
	return true;
}

void FExpressionLexer::Advance()
{
	if (Offset < Content.Len())
	{
		++Offset;
	}
}

bool FExpressionLexer::IsAtEnd() const
{
	return Offset >= Content.Len();
}
