﻿// Fill out your copyright notice in the Description page of Project Settings.

#include "../Public/DSFSMRendering.h"

#include "EngineUtils.h"
#include "SubSystems/Camera/DSCameraSubsystem.h"
#include "SubSystems/MVC/DSMVCSubsystem.h"
#include "SubSystems/MVC/Action/ActionFactory/Public/DSAF_Rendering.h"
#include "SubSystems/MVC/Core/Property/VirtualSunLightProperty.h"
#include "SubSystems/MVC/Library/DSToolLibrary.h"
#include "SubSystems/MVC/Model/Rendering/Public/VirtualSunLight.h"
#include "SubSystems/Render/DSRenderSubsystem.h"
#include "SubSystems/UI/DSUISubsystem.h"
#include "SubSystems/UI/Widget/WindowLayoutWidget.h"
#include "SubSystems/UI/Widget/Components/SimpleViewport/Public/SimpleViewport.h"
#include "SubSystems/UI/Widget/MainDesignPage/MainDesignPageWidget.h"
#include "SubSystems/UI/Widget/Rendering/Public/RenderingPageLeftContent.h"
#include "SubSystems/UI/Widget/Rendering/Public/RenderingPageViewpoint.h"
#include "SubSystems/UI/Widget/Rendering/Public/RenderingPageViewport.h"
#include "SubSystems/UI/Widget/Rendering/Public/RenderingTopWindowToolBarFrame.h"
#include "Subsystems/UI/Widget/WindowToolBar/WindowToolBarWidget.h"
#include "SubSystems/MVC/Model/Rendering/Public/VirtualLightLineModel.h"
#include "SubSystems/MVC/Core/Property/VirtualLightLineProperty.h"

UDSFSMRendering::UDSFSMRendering()
	: RenderingMethod(ERenderingMethod::RT_None)
	, PreviousCameraType(ECameraType::EWalk)
	, PreNearClippingPlane(-1.0f)
	, RenderingToolBarWidget(nullptr)
	, RenderingPageLeftWidget(nullptr)
	, HDRActor(nullptr)
	, SunLightModel(nullptr)
{
}

EDSFSMState UDSFSMRendering::GetState() const
{
	return EDSFSMState::FSM_Rendering;
}

void UDSFSMRendering::Init_ActionFactory()
{
	ActionFactory = NewObject<UDSAF_Rendering>(this);
}

void UDSFSMRendering::EnterState(UDSBaseModel* InModel, const int32& InAdditionState)
{
	Super::EnterState(InModel, InAdditionState);
	
	for (auto& Element : UDSMVCSubsystem::GetInstance()->GetAllModels())
	{
		if (Element->GetModelStateFlag().HasState(EModelState::E_Hovered))
		{
			Element->OnExecuteAction(FDSModelExecuteType::ExecuteUnHover);
		}
	}

	PrepareInWorldActors();

	SetupRenderingPageLeftWidget();

	SetupRenderingPageViewport();

	UDSMVCSubsystem::GetInstance()->SetCurrentModel(nullptr);
	UDSUISubsystem::GetInstance()->HiddenUI();

	ShowViewpointPanel();

	PreviousCameraType = UDSCameraSubsystem::GetInstance()->GetCameraType();

	UDSCameraSubsystem::GetInstance()->ChangeCameraType(ECameraType::EWalk);

	SwitchWindowToolBar(true);
	SwitchCameraControlBar(true);

	SwitchRenderingMethod(ERenderingMethod::RT_Normal);

	UDSRenderSubsystem::GetInstance()->SetVirtualLightVisiable(false);
	UDSRenderSubsystem::GetInstance()->GetHouseElements();
	UDSCameraSubsystem::GetInstance()->StartCaptureActor();

	if (!BlackViewportBorder)
	{
		BlackBorderBrush = *FCoreStyle::Get().GetDefaultBrush();
		BlackBorderBrush.TintColor = FLinearColor::Black;
		BlackViewportBorder = SNew(SBorder)
			.BorderImage(&BlackBorderBrush)
			.BorderBackgroundColor(FLinearColor::Black)
			.OnMouseButtonDown(FPointerEventHandler::CreateLambda([](const FGeometry&, const FPointerEvent&) { return FReply::Handled(); }))
			.OnMouseMove(FPointerEventHandler::CreateLambda([](const FGeometry&, const FPointerEvent&) { return FReply::Handled(); }))
			.OnMouseButtonUp(FPointerEventHandler::CreateLambda([](const FGeometry&, const FPointerEvent&) { return FReply::Handled(); }))
			.OnMouseDoubleClick(FPointerEventHandler::CreateLambda([](const FGeometry&, const FPointerEvent&) { return FReply::Handled(); }));
	}

	RenderingPageViewportWidget->GetWorld()->GetGameViewport()->AddViewportWidgetContent(BlackViewportBorder.ToSharedRef(), 0);

	GenerateLineLightsOnCupboard();

	if (PreNearClippingPlane != -1.0f)
	{
		GNearClippingPlane = PreNearClippingPlane;
		PreNearClippingPlane = -1.0f;
	}
}

void UDSFSMRendering::ExitState()
{
	PreNearClippingPlane = GNearClippingPlane;
	GNearClippingPlane = 1.0f;

	RenderingPageViewportWidget->GetWorld()->GetGameViewport()->RemoveViewportWidgetContent(BlackViewportBorder.ToSharedRef());

	UDSMVCSubsystem::GetInstance()->SetCurrentModel(nullptr);
	UDSUISubsystem::GetInstance()->HiddenUI();

	SwitchWindowToolBar(false);
	SwitchCameraControlBar(false);

	RecoverHDRTexture();

	RenderingMethod = ERenderingMethod::RT_None;

	if (RenderingToolBarWidget != nullptr)
	{
		RenderingToolBarWidget->ResetMethodButtons();
	}

	UMainDesignPageWidget* MainDesignPageWidget = UDSUISubsystem::GetInstance()->GetWindowLayoutWidget()->GetMainDesignPageWidget();
	MainDesignPageWidget->PopLeftContentWidget();
	MainDesignPageWidget->PopDesignAreaContentWidget();
	MainDesignPageWidget->ChangeBottomFloatingToolBarContent(nullptr);

	if (SunLightModel != nullptr)
	{
		SunLightModel->OnExecuteAction(FDSModelExecuteType::ExecuteHidden);
	}

	UDSRenderSubsystem::GetInstance()->SetVirtualLightVisiable(true);

	UDSCameraSubsystem::GetInstance()->ChangeCameraType(PreviousCameraType);

	ClearLineLightsOnCupboard();

	Super::ExitState();

	UDSRenderSubsystem::GetInstance()->RecoverWallFromAerial(UDSRenderSubsystem::GetInstance()->HasCollisionWallModels);
	UDSRenderSubsystem::GetInstance()->RecoverWallFromAerial(UDSRenderSubsystem::GetInstance()->AllInnerWalls);
	UDSRenderSubsystem::GetInstance()->ClearCollisionData();
}

bool UDSFSMRendering::CanExecuteGenericInputCommand_Delete()
{
	return true;
}

bool UDSFSMRendering::CanExecuteGenericInputCommand_SwitchCameraMode()
{
	return false;
}

void UDSFSMRendering::SwitchRenderingMethod(ERenderingMethod NewMethod)
{
	if (RenderingMethod == NewMethod)
	{
		return;
	}

	RenderingMethod = NewMethod;

	if (RenderingPageLeftWidget != nullptr)
	{
		RenderingPageLeftWidget->OnRenderingMethodChanged(RenderingMethod);
	}
}

ERenderingMethod UDSFSMRendering::GetRenderingMethod() const
{
	return RenderingMethod;
}

URenderingPageLeftContent* UDSFSMRendering::GetRenderingPageLeftWidget() const
{
	return RenderingPageLeftWidget;
}

URenderingPageViewport* UDSFSMRendering::GetRenderingPageViewport() const
{
	return RenderingPageViewportWidget;
}

AActor* UDSFSMRendering::GetHDRActor() const
{
	return HDRActor;
}

UDSVirtualSunLightModel* UDSFSMRendering::GetSunLightModel() const
{
	return SunLightModel;
}

FDSVRayRenderSunLightArgument UDSFSMRendering::CollectSunLightArgument() const
{
	TSharedPtr<FDSVirtualSunLightProperty> ModelProperty = SunLightModel->GetTypedProperty<FDSVirtualSunLightProperty>();

	FVector SunLightDir = 1000.f * ModelProperty->TransformProperty.ToUETransform().GetRotation().GetRightVector();

	FDSVRayRenderSunLightArgument SunLightArgument;
	SunLightArgument.bEnable = ModelProperty->bEnabled;
	SunLightArgument.Intensity = ModelProperty->Intensity;
	SunLightArgument.Transform = FTransform(ModelProperty->TransformProperty.Rotation, SunLightDir);
	SunLightArgument.Temperature =ModelProperty->ColorTemperature;
	SunLightArgument.Size = ModelProperty->ShadowSoftness;

	return SunLightArgument;
}

void UDSFSMRendering::SetupRenderingPageLeftWidget()
{
	if (RenderingPageLeftWidget == nullptr)
	{
		RenderingPageLeftWidget = URenderingPageLeftContent::CreateInstance(UDSUISubsystem::GetInstance()->GetWindowLayoutWidget());
	}

	if (RenderingPageLeftWidget != nullptr)
	{
		UMainDesignPageWidget* MainDesignPageWidget = UDSUISubsystem::GetInstance()->GetWindowLayoutWidget()->GetMainDesignPageWidget();
		MainDesignPageWidget->PresentLeftContentWidget(RenderingPageLeftWidget);
	}
}

void UDSFSMRendering::SetupRenderingPageViewport()
{
	if (RenderingPageViewportWidget == nullptr)
	{
		RenderingPageViewportWidget = URenderingPageViewport::CreateInstance(UDSUISubsystem::GetInstance()->GetWindowLayoutWidget());
	}

	if (RenderingPageViewportWidget != nullptr)
	{
		UMainDesignPageWidget* MainDesignPageWidget = UDSUISubsystem::GetInstance()->GetWindowLayoutWidget()->GetMainDesignPageWidget();
		MainDesignPageWidget->PresentDesignAreaContentWidget(RenderingPageViewportWidget);
	}
}

void UDSFSMRendering::PrepareInWorldActors()
{
	if (HDRActor == nullptr)
	{
		for (TActorIterator<AActor> It(UDSUISubsystem::GetInstance()->GetWindowLayoutWidget()->GetWorld()); It; ++It)
		{
			if (It->ActorHasTag(TEXT("BP_HDRI")))
			{
				HDRActor = *It;
			}
		}
	}

	if (SunLightModel == nullptr)
	{
		SunLightModel = NewObject<UDSVirtualSunLightModel>();
		UDSRenderSubsystem::GetInstance()->UpdateSunLightProperty(SunLightModel);
		UDSMVCSubsystem::GetInstance()->SpawnViewUnion(SunLightModel);
		SunLightModel->SetNoNewGenerate();
	}

	SunLightModel->OnExecuteAction(FDSModelExecuteType::ExecuteUnHidden);
}

void UDSFSMRendering::SwitchWindowToolBar(bool bIsRendering)
{
	UWindowLayoutWidget* WindowLayoutWidget = UDSUISubsystem::GetInstance()->GetWindowLayoutWidget();
	if (WindowLayoutWidget == nullptr)
	{
		return;
	}

	UWindowToolBarWidget* ToolBarWidget = WindowLayoutWidget->GetWindowToolBarWidget();
	if (ToolBarWidget == nullptr)
	{
		return;
	}

	if (bIsRendering)
	{
		if (RenderingToolBarWidget == nullptr)
		{
			RenderingToolBarWidget = URenderingTopWindowToolBarFrame::CreateInstance(ToolBarWidget);
		}

		ToolBarWidget->PresentToolBarContent(RenderingToolBarWidget);
	}
	else
	{
		ToolBarWidget->PopToolBarContent();
	}
}

void UDSFSMRendering::SwitchCameraControlBar(bool bIsRendering)
{
	UWindowLayoutWidget* WindowLayoutWidget = UDSUISubsystem::GetInstance()->GetWindowLayoutWidget();
	if (WindowLayoutWidget == nullptr)
	{
		return;
	}

	UMainDesignPageWidget* MainDesignPageWidget = WindowLayoutWidget->GetMainDesignPageWidget();
	if (MainDesignPageWidget == nullptr)
	{
		return;
	}

	MainDesignPageWidget->SetCameraControlBarVisibility(!bIsRendering);
}

void UDSFSMRendering::ShowViewpointPanel()
{
	if (RenderingPageViewpointWidget == nullptr)
	{
		RenderingPageViewpointWidget = URenderingPageViewpoint::CreateInstance(UDSUISubsystem::GetInstance()->GetWindowLayoutWidget()->GetMainDesignPageWidget());
	}

	if (RenderingPageViewpointWidget != nullptr)
	{
		UMainDesignPageWidget* MainDesignPageWidget = UDSUISubsystem::GetInstance()->GetWindowLayoutWidget()->GetMainDesignPageWidget();
		MainDesignPageWidget->ChangePropertyWidget(RenderingPageViewpointWidget);
	}
}

FReply UDSFSMRendering::OnMouseButtonDown(const FGeometry& MyGeometry, const FPointerEvent& MouseEvent)
{
	if (RenderingPageViewportWidget != nullptr && RenderingPageViewportWidget->GetCachedWidget() != nullptr)
	{
		if (UDSBaseModel* PickedModel = GetFiniteStateMachine()->GetDragDetectedModel(MouseEvent.GetEffectingButton()))
		{
			if (UDSActionExecuteBase* Action = ActionFactory->GetAction(PickedModel, MouseEvent))
			{
				Action->OnMouseButtonDown(MyGeometry, MouseEvent);
			}
		}

		if (MouseEvent.GetEffectingButton() == EKeys::LeftMouseButton)
		{
			return FReply::Handled().DetectDrag(RenderingPageViewportWidget->RenderingViewport->GetInternalViewport().ToSharedRef(), EKeys::LeftMouseButton);
		}
		else if (MouseEvent.GetEffectingButton() == EKeys::RightMouseButton)
		{
			return FReply::Handled().DetectDrag(RenderingPageViewportWidget->RenderingViewport->GetInternalViewport().ToSharedRef(), EKeys::RightMouseButton);
		}
		else if (MouseEvent.GetEffectingButton() == EKeys::MiddleMouseButton)
		{
			return FReply::Handled().DetectDrag(RenderingPageViewportWidget->RenderingViewport->GetInternalViewport().ToSharedRef(), EKeys::MiddleMouseButton);
		}
	}
	
	return Super::OnMouseButtonDown(MyGeometry, MouseEvent);
}

void UDSFSMRendering::GenerateLineLightsOnCupboard()
{
	TArray<UDSBaseModel*> AllModels = UDSMVCSubsystem::GetInstance()->GetAllModels();
	TArray<UDSBaseModel*> CabModels;
	for (auto & Iter : AllModels)
	{
		
		if (UDSToolLibrary::IsCustomCabinetType( Iter->GetModelType()))
		{
			CabModels.Add(Iter);
		}
	}

	for (auto &  Cab : CabModels)
	{
		auto CabModel = Cast<UDSCupboardModel>(Cab);
		auto ModelInfo = CabModel->GetModelInfo();
		auto Tree = ModelInfo.ComponentTreeData;
		if (Tree == nullptr)
		{
			continue;
		}
		//通过类型id获取灯带节点
		TArray<FMultiComponentDataItem> AllLightParents;
		TArray<FMultiComponentDataItem> AllLighCmps;
		Tree->GetComponentDataByType(86, AllLightParents);
		for (auto & P : AllLightParents)
		{
			auto Visible = FCString::Atod(*P.ComponentVisibility.Value) != 0;     
			if (!Visible)
			{
				continue;
			}
			for (auto & C : P.ChildComponent)
			{
				if (C->ModelType == 88)
				{
					AllLighCmps.Add(*C);
				}
			}
		}

		//Tree->GetComponentDataByID({TEXT("990801"),TEXT("990802")}, AllLighCmps);
		
		auto Location = CabModel->GetPropertySharedPtr()->GetActualTransform().GetLocation();
		auto Rotator = CabModel->GetPropertySharedPtr()->GetActualTransform().GetRotation();
		auto Transform = CabModel->GetPropertySharedPtr()->GetActualTransform();

		if (!AllLighCmps.IsEmpty())
		{
			for (auto & Cmp : AllLighCmps)
			{
				auto Visible = FCString::Atod(*Cmp.ComponentVisibility.Value) != 0;
				if (!Visible)
				{
					continue;
				}

				FTransform LightTransform = FTransform();
				if (!Tree->GetComponentDataTransform(Cmp.UUID, Transform, LightTransform))
				{
					continue;
				}

				auto W = FCString::Atod(*Cmp.GetParameterValue(TEXT("W"))) * 0.1;
				auto H = FCString::Atod(*Cmp.GetParameterValue(TEXT("H"))) * 0.1;
				auto D = FCString::Atod(*Cmp.GetParameterValue(TEXT("D"))) * 0.1;

				UDSVirtualLightLineModel* LineLightModel = NewObject<UDSVirtualLightLineModel>();
				auto Prop = LineLightModel->GetTypedProperty<FDSVirtualLightLineProperty>();

				auto Vec = LightTransform.GetRotation().Rotator().Vector();
				auto Dir = Rotator.GetAxisX();
				auto Nor = -LightTransform.GetUnitAxis(EAxis::Type::Z) * LightTransform.GetScale3D().Z;
				auto Forward = Rotator.GetAxisY();
				if (FVector::Parallel(Vec,FVector::ZAxisVector))
				{
					Dir = FVector::ZAxisVector;
				}
				Prop->bDoubleSide = true;
				Prop->LightTemperature = 4000.0f;
				Prop->LightIntensity = 20.0f;

				Prop->TransformProperty.SetLocation(LightTransform.GetLocation() + W * 0.5 * Dir + 0.5 * Nor + D * 0.5 * Forward);
				Prop->TransformProperty.SetRotation(LightTransform.Rotator());
				Prop->TransformProperty.SetScale(LightTransform.GetScale3D());

				Prop->SizeProperty.Depth = Prop->LightLength = W * 10.0f;
				Prop->SizeProperty.Width = Prop->LightWidth = D * 10.0f;
				Prop->SizeProperty.Height = 10;

				UDSMVCSubsystem::GetInstance()->SpawnViewUnion(LineLightModel,FDSBroadcastMarkData::NotBroadcastToMVCMark);

				AutoGenerateLights.Add(LineLightModel);
			}
		}
	}
}

void UDSFSMRendering::ClearLineLightsOnCupboard()
{
	for (auto& LightModel : AutoGenerateLights)
	{
		if (LightModel != nullptr)
		{
			LightModel->OnExecuteAction(FDSModelExecuteType::ExecuteDelete,FDSBroadcastMarkData::NotBroadcastToMVCMark);
		}
	}
	AutoGenerateLights.Empty();
}

void UDSFSMRendering::RecoverHDRTexture()
{
	UTexture* DefaultTexture = LoadObject<UTexture>(nullptr, TEXT("/Script/Engine.TextureCube'/Game/MapTemplates/HDRIMaps/Default.Default'"));
	if (HDRActor)
	{
		if (UStaticMeshComponent* MeshComponent = HDRActor->FindComponentByClass<UStaticMeshComponent>())
		{
			TArray<UMaterialInterface*> Materials = MeshComponent->GetMaterials();
			for (int32 Index = 0; Index < Materials.Num(); ++Index)
			{
				UMaterialInstanceDynamic* DynamicMaterial = Cast<UMaterialInstanceDynamic>(Materials[Index]);
				if (!DynamicMaterial)
				{
					DynamicMaterial = MeshComponent->CreateAndSetMaterialInstanceDynamic(Index);
				}
				DynamicMaterial->SetTextureParameterValue(TEXT("HDRI_Map"), DefaultTexture);
			}
		}
	}


	//UDSRenderSubsystem::GetInstance()->CurrentHDRId = INDEX_NONE;
}
