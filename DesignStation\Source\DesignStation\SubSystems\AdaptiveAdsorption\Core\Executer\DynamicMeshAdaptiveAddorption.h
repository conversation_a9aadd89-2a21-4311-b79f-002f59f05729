// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "OrientedBoxTypes.h"
#include "MeshAABBTree3.h"
#include "LocalCache/Adaptation/AdaptationData.h"
#include "SubSystems/AdaptiveAdsorption/Core/Executer/AdapationIntersectionExecuter.h"
#include "Subsystems/AdaptiveAdsorption/Data/AdaptationDynamicData.h"

struct FHitResult;
using FAdaptiveAdsorptionPtr = TSharedPtr<class FDynamicMeshAdaptiveAdsorption>;
using FAdaptiveAdsorptionWeakPtr = TWeakPtr<FDynamicMeshAdaptiveAdsorption>;


struct FDsCupboardModelDependModelList;
class FFunctionalAdaptationOperator;

struct FAdaptiveResault
{
	FAdaptiveResault()
		: Offset(), XAxisPoint(), YAxisPoint(), ZAxisPoint()
	{
	} ;
	FVector Offset;
	FVector XAxisPoint;
	FVector YAxisPoint;
	FVector ZAxisPoint;
	MeshIntersection::FIntersectionsQueryResult Resault;
};


struct DESIGNSTATION_API FFunctionalExecuterInitializedData :public FAdaptationExecuterInitializedData
{
	FFunctionalExecuterInitializedData() :FAdaptationExecuterInitializedData()
		,CabinetType(EIntersectionDataType::E_CupboardModel)
	,FixedExtent(FVector::ZeroVector),FixedOffset(FVector::ZeroVector)
	, FixedMinExtentOffset(FVector::ZeroVector),FixedMaxExtentOffset(FVector::ZeroVector){}


	EIntersectionDataType CabinetType;

	//柜体的包围盒
	FOrientedBox3d CabinetOriBox;

	FOrientedBox3d SelfRealOriBox;

	FVector FixedExtent;
	FVector FixedOffset;

	FVector FixedMinExtentOffset;
	FVector FixedMaxExtentOffset;

	double DistanceAdsorptionThreshold;
	double AlignedAdsorptionThreshold;

	bool bEnableAlignedAdsorption = true;


	bool bUsedRelativedOBB = false;

	bool bHasFixedExtents = false;

	FAdaptiveAdsorptionRule3D AdaptativeRule;
};


class DESIGNSTATION_API FFunctionalIntersectionMesh :public FIntersectionDynamicMesh
{
public:
	FFunctionalIntersectionMesh(const FVector& InExtent, const FVector& InCenter, const FQuat& InRotation, EIntersectionDataType InType = EIntersectionDataType::E_Unknown)
		:FIntersectionDynamicMesh(InExtent, InCenter, InRotation, InType), bUseRelativeOBB(false), bHasSpecialExtents(false), ForwardRetract(0.f), BackwardRetract(0.f) {
	};
	virtual ~FFunctionalIntersectionMesh();


public:
	float GetForwardRetract() { return ForwardRetract; };
	float GetBackwardRetract() { return BackwardRetract; }

	void SetRetract(float InForwardRetract, float InBackwardRetract);


	FOrientedBox3d GetOriBoxWithSpecialExtents();

	bool bUseRelativeOBB;
	bool bHasSpecialExtents;

	FVector SpecialExtent;
	FVector SepcialOffset;


	

private:
	float ForwardRetract;
	float BackwardRetract;


};



class DESIGNSTATION_API FDynamicMeshAdaptiveAdsorption :public FAdapationIntersectionExecuter
{
public:
	FDynamicMeshAdaptiveAdsorption();
	virtual ~FDynamicMeshAdaptiveAdsorption() override;

	// 初始化
	virtual void Initialized(const TSharedPtr<FAdaptationExecuterInitializedData>& InInitializedData);

	// 更新初始化数据
	virtual void OnInitializeDataUpdate();

	/**
	 * @@ 固定宽高深处理
	 */
    virtual void ApplyFixedExtentsOnInitialize();

	// 完成自适应和吸附
	virtual void AdaptiveAndAdsorptionComplete(const TArray<TSharedPtr<FIntersectionDynamicMesh>>& AdaptationEnvs, FAdaptiveAdsorptionResault& OutAdaptiveAdResualt);
	
	// 处理带依赖的自适应和吸附
	virtual bool HandleAdaptiveAndAdsorptionWithDependent(const TArray<TSharedPtr<FIntersectionDynamicMesh>>& AdaptationEnvs, const FString& DependentUUID =TEXT(""), EAdaptationDirection DependencyDir = EAdaptationDirection::E_None);
	virtual bool HandleAdaptiveAndAdsorptionWithDependent(const TArray<TSharedPtr<FIntersectionDynamicMesh>>& AdaptationEnvs, const TSharedPtr<struct FFunctionalDependencyInfo>& SelfDependencyInfo);


	virtual void GenerateRay(FVector& RayStart, FVector& RayDir);
	// 处理自适应和吸附
	virtual bool HandleAdaptiveAndAdsorption(const TArray<TSharedPtr<FIntersectionDynamicMesh>>& AdaptationEnvs);

	// 使用射线处理自适应和吸附
	virtual bool HandleAdaptiveAndAdsorptionWithRay(const FVector& RayStart, const FVector& RayDir, const TArray<TSharedPtr<FIntersectionDynamicMesh>>& AdaptationEnvs) override;

	

	// 根据最大包围盒半径设置相交检测包围盒位置
	virtual void HandleAddsorption(FOrientedBox3d& OutOriBox,const FRayHitResault& HitResault, const TArray<TSharedPtr<FIntersectionDynamicMesh>>& Env);

	// 处理自适应
	virtual void HandleAdaptive(const TArray<TSharedPtr<FIntersectionDynamicMesh>>& Env, const TSharedPtr<FIntersectionDynamicMesh>& HitEnv,FOrientedBox3d& OutExecutedBox);

	// 设置执行句柄
	virtual void SetExecuteHandle(const  TAdaptationHandle& InHandle) { ExecuteHandle = InHandle; };

	// 执行均分事件
	virtual FAdaptationEvenInfo ExecuteEven(const TArray<TSharedPtr<FIntersectionDynamicMesh>>& Env);

	// 根据实际状态获取依赖（offset应用）
	FAdaptationEvenInfo ExecuteEvenReal(const TArray<TSharedPtr<FIntersectionDynamicMesh>>& Env);

	// 获取自适应规则引用
	FAdaptiveAdsorptionRule3D& GetAdaptiveRuleRef() { return AdaptiveRule; };

	// 获取自适应规则
	const FAdaptiveAdsorptionRule3D& GetAdaptativeRule()const { return AdaptiveRule; };

	// 设置自适应规则
	void SetAdaptiveRule(const FAdaptiveAdsorptionRule3D& NewRule);

	// 获取相交框架
	const FFrame3d& GetIntersectionFrame();

	// 获取相交数据
	const FIntersectionDynamicMesh& GetIntersectionData() const;

	// 获取自适应包围盒
	const FOrientedBox3d& GetAdaptationOriBox() const { return AdaptationOriBox; };


	virtual FOrientedBox3d GetAdaptationOriBoxWithoutAdaptiveRulerOffset();

	//获取输出自适应包围盒	
	const FOrientedBox3d& GetOutAdaptationOriBox() const { return OutAdaptationData->OriBox; };


	bool IsInitializedDataValid() const { return InitializedData.IsValid(); };

	void SetInitializedData(const TSharedPtr<FAdaptationExecuterInitializedData>& InInitializedData) { InitializedData = InInitializedData; }
	// 获取初始化数据引用
	const TSharedPtr<FAdaptationExecuterInitializedData>& GetInitializedDataRef() { return InitializedData; };


	// 获取所有者UUID
	const FString& GetOwnerUUID() { return SourceIntersectionData->GetOwnerUUID(); };


protected:

	virtual void HandleAdaptiveAndAdsorptionWithHitResault(bool bHasHit, const FRayHitResault& HitResault, const TArray<TSharedPtr<FIntersectionDynamicMesh>>& AdaptationEnvs);


	// 应用自适应偏移到包围盒
	void ApplyAdaptationOffsetToOrientedBox(const FAdaptiveAdsorptionRule3D& AppliedRule, FOrientedBox3d& OutOriBox);

	// 预处理自适应包围盒
	virtual void PreHandleAdaptiveOriBox( const TArray<TSharedPtr<FIntersectionDynamicMesh>>& Env, const FRayHitResault& RayHitResault, FOrientedBox3d& OutOriBox);

	// 计算吸附点
	virtual void CaculateAdsorptionPoint(FVector& OutAdsorptionPoint, const FRayHitResault& HitResault, const FVector& InUseExtent,
                              const TArray<TSharedPtr<FIntersectionDynamicMesh>>& Env);

	// 计算X轴吸附点
	virtual void CalculateAdsorptionPoint_X(FVector& OutAdsorptionPoint, const FRayHitResault& HitResault, const FVector& InUseExtent,
                                 const TArray<TSharedPtr<FIntersectionDynamicMesh>>& Env);

	// 计算Y轴吸附点
	virtual void CalculateAdsorptionPoint_Y(FVector& OutAdsorptionPoint, const FRayHitResault& HitResault, const FVector& InUseExtent,
                                 const TArray<TSharedPtr<FIntersectionDynamicMesh>>& Env);

	// 计算Z轴吸附点
	virtual void CalculateAdsorptionPoint_Z(FVector& OutAdsorptionPoint, const FRayHitResault& HitResault, const FVector& InUseExtent,
                                 const TArray<TSharedPtr<FIntersectionDynamicMesh>>& Env);

	// 计算Y轴吸附
	void CalculateYAxisAdsorption( FVector& InRelativeMinPoint, FVector& InRelativeMaxPoint,FVector& OutExtents, FVector& OutCenter, const TArray<TSharedPtr<FIntersectionDynamicMesh>>& Env);

	void CalculateXZAxisAdsorption( FVector& InRelativeMinPoint,  FVector& InRelativeMaxPoint, FVector& OutExtents, FVector& OutCenter);

	// 应用吸附偏移前
	void ApplyAdsorptionOffsetBefore(FVector& OutCenter, FVector& OutExtents);

	// 应用吸附偏移后
	void ApplyAdsorptionOffsetAfter(FOrientedBox3d& OutOriBox);

	virtual void ApplyFixedExtentsAfter(FOrientedBox3d& OutOriBox);

	// 更新自适应包围盒
	void UpdateAdaptationOriBox(const FOrientedBox3d& InOriBox);

	// 设置X负依赖
	void SetXNegativeDependence(const TSharedPtr<FIntersectionDynamicMesh>& InTarget);
	
	// 设置X正依赖
	void SetXPositiveDependence(const TSharedPtr<FIntersectionDynamicMesh>& InTarget);

	// 设置Y负依赖
	virtual void SetYNegativeDependence(const TSharedPtr<FIntersectionDynamicMesh>& InTarget) {}

    // 设置Y正依赖
    virtual void SetYPositiveDependence(const TSharedPtr<FIntersectionDynamicMesh>& InTarget) {}
	
	// 设置Z负依赖
	void SetZNegativeDependence(const TSharedPtr<FIntersectionDynamicMesh>& InTarget);
	
	// 设置Z正依赖
	void SetZPositiveDependence(const TSharedPtr<FIntersectionDynamicMesh>& InTarget);

	//额外的依赖信息
	virtual void SetRealXNegativeDependence(const TSharedPtr<FIntersectionDynamicMesh>& InTarget) {}
	virtual void SetRealXPositiveDependence(const TSharedPtr<FIntersectionDynamicMesh>& InTarget) {}
	virtual void SetRealZNegativeDependence(const TSharedPtr<FIntersectionDynamicMesh>& InTarget) {}
	virtual void SetRealZPositiveDependence(const TSharedPtr<FIntersectionDynamicMesh>& InTarget) {}

	// 清除依赖目标
	void ClearDependentTarget();

	// 记录依赖
	void LogDenpendence();

	//初始化诗句
	TSharedPtr<FAdaptationExecuterInitializedData> InitializedData;
	//自适应规则
	FAdaptiveAdsorptionRule3D AdaptiveRule;
	//自适应包围盒数据
	FOrientedBox3d AdaptationOriBox;
	TSharedPtr<FAdaptationData> OutAdaptationData;
	
	TWeakPtr<FIntersectionDynamicMesh> XNegativeAdsorption = nullptr;
	TWeakPtr<FIntersectionDynamicMesh> XPositiveAdsorption = nullptr;
	TWeakPtr<FIntersectionDynamicMesh> ZNegativeAdsorption = nullptr;
	TWeakPtr<FIntersectionDynamicMesh> ZPositiveAdsorption = nullptr;
	
	FVector UseExtents;
	
	TAdaptationHandle ExecuteHandle;
};
