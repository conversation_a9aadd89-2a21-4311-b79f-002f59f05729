// Fill out your copyright notice in the Description page of Project Settings.


#include "RenderNodeConverter.h"
#include "SubSystems/MVC/Model/DSBaseModel.h"
#include "SubSystems/Render/Library/DSRenderFunctionLibrary.h"
#include "SubSystems/MVC/Core/Property/FurnitureProperty.h"
#include "SubSystems/Resource/DSResourceSubsystem.h"
#include "SubSystems/MVC/View/Custom/DSCupboardBaseView.h"
#include "SubSystems/MVC/View/CounterTop/CounterTopBaseView.h"
#include "SubSystems/MVC/Core/Property/VirtualLightSphericalProperty.h"
#include "SubSystems/MVC/Core/Property/VirtualLightRectProperty.h"
#include "SubSystems/MVC/Core/Property/VirtualLightIESProperty.h"
#include "SubSystems/Render/DSRenderSubsystem.h"
#include "SubSystems/MVC/Model/Custom/Library/DSCupboardLibrary.h"
#include "SubSystems/MVC/Model/Custom/DSCupboardModel.h"
#include "Clipper2\Library\Clipper2Library.h"
#include "SubSystems/MVC/Core/Property/HouseAreaProperty.h"
#include "ArrayOperatorLibrary.h" 
#include "SubSystems/MVC/Library/DSToolLibrary.h"
#include "SubSystems/CustomConfig/DSConfigSubsystem.h"
#include "SubSystems/MVC/View/Line/LineEntity/DSLineEntityView.h"
#include "SubSystems/MVC/Model/Line/LineEntity/DSLineEntityModel.h"
#include "Library/MaterialLibrary.h"
#include <SubSystems/MVC/Core/Property/VirtualLightLineProperty.h>

FDSVRaySceneFileObjectConverterBase::FDSVRaySceneFileObjectConverterBase(UDSBaseModel* SourceModel, EDSRenderObjectType InObjectType)
{
	if (!SourceModel)
	{
		ModelTWeakPtr = nullptr;
		ObjectType = EDSRenderObjectType::E_Unkown;
		return;
	}

	ModelTWeakPtr = SourceModel;
	ObjectType = InObjectType;

}


void FDSVRayWallConverter::Serialization(TSharedRef<TJsonWriter<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>> JsonWriter)
{

	if (!ModelTWeakPtr.IsValid())
	{
		return;
	}
	FTransform WallTransform = ModelTWeakPtr->GetProperty()->GetTransformPropertyRef().ToUETransform();
	const TSet<UDSBaseModel*> PlaneModels = ModelTWeakPtr->GetComponentModels();
	for (auto& PlaneModel : PlaneModels)
	{
		JsonWriter->WriteObjectStart();
		JsonWriter->WriteValue("ObjType", static_cast<int>(EDSRenderObjectType::E_Geometry));
		if (PlaneModel->GetProperty()->MaterialProperty.MaterialInfo3D[0].Id.IsEmpty())
		{
			JsonWriter->WriteValue(TEXT("MatID"), TEXT("WallMat"));
		}
		else
		{
			JsonWriter->WriteValue(TEXT("MatID"), FString::Printf(TEXT("ID%s"), *PlaneModel->GetProperty()->MaterialProperty.MaterialInfo3D[0].Id));
		}
		
		const auto& MeshInfo = PlaneModel->GetMeshInfo();
		UDSRenderFunctionLibrary::CollectionGeometryInfo(JsonWriter, MeshInfo);
		UDSRenderFunctionLibrary::CollectionTransformInfo(JsonWriter, WallTransform);

		JsonWriter->WriteObjectEnd();
	}
}
void FDSVRayWallConverter::CollectionNeededRequestResourInfo(TMap<FString, FDSVRayRenderResourceInfo>& ResourceInfoMap, TMap<FString, int>& NeedQueryResourceMap)
{

	const TSet<UDSBaseModel*> PlaneModels = ModelTWeakPtr->GetComponentModels();
	for (auto& PlaneModel : PlaneModels)
	{
		if (PlaneModel->GetProperty()->MaterialProperty.MaterialInfo3D[0].Id.IsEmpty())
		{
			if (!ResourceInfoMap.Contains(TEXT("WallMat")))
			{
				FString DefaultWallMaterialPath = UDSConfigSubsystem::GetInstance()->GetDefaultWallMaterialPath();
				ResourceInfoMap.Add("WallMat", FDSVRayRenderResourceInfo("WallMat", DefaultWallMaterialPath, EDSRenderResourceType::E_Material));
			}
		}
		else
		{
			NeedQueryResourceMap.Add(PlaneModel->GetProperty()->MaterialProperty.MaterialInfo3D[0].Id, static_cast<int>(EDSResourceType::Material));
		}
	}
}
void ExpandMeshAlongNormal(FDSMeshInfo& MeshInfo,  const FVector2D& UVSize, float Distance ,float VertexOffset = 0.f )
{



	for (auto& MeshData : MeshInfo.MeshDatas)
	{
		if (MeshData.Value.Vertex.Num() < 3)
		{
			return;
		}
		//FVector FirstElment = MeshData.Value.Vertex[0];
		//MeshData.Value.Vertex.Add(FirstElment);
		if (!FMath::IsNearlyZero(VertexOffset))
		{
			MeshData.Value.Vertex = FGeometryLibrary::OffsetPolygon(MeshData.Value.Vertex, -VertexOffset);
			//将顶点外扩
			//MeshData.Value.Vertex = UDSToolLibrary::CalculateCollisionConvexByNormal(MeshData.Value.Vertex,MeshData.Value)
		}
		int32 SourceVetexCount = MeshData.Value.Vertex.Num();
		int32 Index0 = MeshData.Value.Indices[0];
		int32 Index1 = MeshData.Value.Indices[1];
		int32 Index2 = MeshData.Value.Indices[2];

		// 计算法线
		FVector Normal = FVector::CrossProduct(MeshData.Value.Vertex[Index2] - MeshData.Value.Vertex[Index0],
			MeshData.Value.Vertex[Index1] - MeshData.Value.Vertex[Index0]).GetSafeNormal();

		TArray<FVector> HoleTopVertexs;
		TArray<FVector> HoleBottomVertexs;
		FVector ExpandNormal = -Normal;
		// 向法线相反方向扩展顶点
		for (const FVector& Vertex : MeshData.Value.Vertex)
		{
			HoleBottomVertexs.Add(Vertex);
			HoleTopVertexs.Add(Vertex + ExpandNormal * Distance);
		}

		if (FGeometryLibrary::clockwise(HoleTopVertexs, ExpandNormal))
		{
			FArrayOperatorLibrary::ReverseArray(HoleTopVertexs);
		}

		if (FGeometryLibrary::clockwise(HoleBottomVertexs, ExpandNormal))
		{
			FArrayOperatorLibrary::ReverseArray(HoleBottomVertexs);
		}
		FGeoMesh HoleMeshData;
		FGeometryLibrary::createMeshByTwoPolygon(HoleBottomVertexs, HoleTopVertexs, Normal, HoleMeshData);



		//// 生成体的顶点和索引
		//TArray<FVector> NewVertices = MeshData.Value.Vertex;
		//NewVertices.Append(ExpandedVertices);

		//TArray<int32> NewIndices;
		//int32 NumVertices = MeshData.Value.Vertex.Num();


		//NewIndices.Append(MeshData.Value.Indices);
		//for (size_t i = 0; i < MeshData.Value.Indices.Num(); i+=3)
		//{
		//	NewIndices.Add(MeshData.Value.Indices[i + 2] + NumVertices);
		//	NewIndices.Add(MeshData.Value.Indices[i + 1]  + NumVertices);
		//	NewIndices.Add(MeshData.Value.Indices[i] + NumVertices);
		//}

		//// 生成侧面的索引
		//for (int32 i = 0; i < NumVertices; ++i)
		//{
		//	int32 NextIndex = (i + 1) % NumVertices;

		//	NewIndices.Add(i);
		//	NewIndices.Add(NextIndex);
		//	NewIndices.Add(i + NumVertices);

		//	NewIndices.Add(NextIndex);
		//	NewIndices.Add(NextIndex + NumVertices);
		//	NewIndices.Add(i + NumVertices);
		//}

		MeshData.Value.Indices = HoleMeshData.indices;
		MeshData.Value.Vertex = HoleMeshData.vertexs;



		MeshData.Value.Normals.Empty();
		for (size_t i = 0; i < HoleMeshData.indices.Num(); i+=3)
		{
			int32 SourceVetexCount = MeshData.Value.Vertex.Num();
			int32 Index0 = MeshData.Value.Indices[i];
			int32 Index1 = MeshData.Value.Indices[i+1];
			int32 Index2 = MeshData.Value.Indices[i+2];
			// 计算法线
			FVector Normal = FVector::CrossProduct(MeshData.Value.Vertex[Index2] - MeshData.Value.Vertex[Index0],
				MeshData.Value.Vertex[Index1] - MeshData.Value.Vertex[Index0]).GetSafeNormal();

			MeshData.Value.Normals.Add(Normal);
			MeshData.Value.Normals.Add(Normal);
			MeshData.Value.Normals.Add(Normal);
		}
		MeshData.Value.Uvs.Empty();
		for (auto& iter : MeshData.Value.Vertex)
		{
			MeshData.Value.Uvs.Add(FVector2D(iter.Y,-iter.X));
		}
		FMaterialLibrary::StartZero(MeshData.Value.Uvs);
		FMaterialLibrary::AdjustUVWithWorldScale(MeshData.Value.Vertex, MeshData.Value.Uvs, UVSize.X, UVSize.Y);


		//MeshData.Value.Uvs = HoleMeshData.uv;
	}
}

void FDSVRayAreaConverter::Serialization(TSharedRef<TJsonWriter<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>> JsonWriter)
{
	if (!ModelTWeakPtr.IsValid())
	{
		return;
	}
	FTransform AreaTransform = ModelTWeakPtr->GetProperty()->GetTransformPropertyRef().ToUETransform();
	FVector2D UVSize;
	UVSize.X =  ModelTWeakPtr->GetProperty()->MaterialProperty.MaterialInfo3D[0].USize*0.1f;
	UVSize.Y = ModelTWeakPtr->GetProperty()->MaterialProperty.MaterialInfo3D[0].VSize*0.1f;
	AreaTransform = FTransform();
	JsonWriter->WriteObjectStart();
	JsonWriter->WriteValue("ObjType", static_cast<int>(EDSRenderObjectType::E_Geometry));
	FDSMeshInfo MeshInfo = ModelTWeakPtr->GetMeshInfo();

	if (UVSize.ContainsNaN() || FMath::IsNearlyZero(UVSize.X) || FMath::IsNearlyZero(UVSize.Y))
	{
		UVSize = FVector2D(100.f, 100.f);
	}
	ExpandMeshAlongNormal(MeshInfo,UVSize, 24.f, 3.f);
	UDSRenderFunctionLibrary::CollectionGeometryInfo(JsonWriter, MeshInfo,false,false);
	UDSRenderFunctionLibrary::CollectionTransformInfo(JsonWriter, AreaTransform);

	const auto& HouseAreaProperty = StaticCastSharedPtr<FDSHouseAreaProperty>(ModelTWeakPtr->GetPropertySharedPtr());

	if (HouseAreaProperty->MaterialProperty.MaterialInfo3D.IsValidIndex(0))
	{
		auto MaterialInfo = HouseAreaProperty->MaterialProperty.MaterialInfo3D[0];

		const auto ResourceId = MaterialInfo.Id.IsEmpty() ? TEXT("AreaMat") :("ID" + MaterialInfo.Id);
		JsonWriter->WriteValue("MatID", ResourceId);
	}

	JsonWriter->WriteObjectEnd();
}

void FDSVRayAreaConverter::CollectionNeededRequestResourInfo(TMap<FString, FDSVRayRenderResourceInfo>& ResourceInfoMap)
{
	
}

void FDSVRayAreaConverter::CollectionNeededRequestResourInfo(TMap<FString, FDSVRayRenderResourceInfo>& ResourceInfoMap, TMap<FString, int>& NeedQueryResourceMap)
{
	if (!ModelTWeakPtr.IsValid())
	{
		return;
	}

	const auto& HouseAreaProperty = StaticCastSharedPtr<FDSHouseAreaProperty>(ModelTWeakPtr->GetPropertySharedPtr());

	if (HouseAreaProperty->MaterialProperty.MaterialInfo3D.IsValidIndex(0))
	{
		auto MaterialInfo = HouseAreaProperty->MaterialProperty.MaterialInfo3D[0];
		const auto ResourceId = MaterialInfo.Id;
		//测试代码
		if (ResourceId.IsEmpty() && !ResourceInfoMap.Contains(TEXT("AreaMat")))
		{
			FString DefaultAreaMaterialPath = UDSConfigSubsystem::GetInstance()->GEtDefaultAreaMaterialPath();
			ResourceInfoMap.Add("AreaMat", FDSVRayRenderResourceInfo("AreaMat", DefaultAreaMaterialPath, EDSRenderResourceType::E_Material));
		}
		else if (!ResourceId.IsEmpty() &&!ResourceInfoMap.Contains(ResourceId))
		{
			NeedQueryResourceMap.Add(ResourceId, static_cast<int>(EDSResourceType::Material));
		}
	}
}

void FDSVRayHouseRoofConverter::Serialization(TSharedRef<TJsonWriter<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>> JsonWriter)
{

	if (!ModelTWeakPtr.IsValid())
	{
		return;
	}
	FTransform AreaTransform = ModelTWeakPtr->GetProperty()->GetTransformPropertyRef().ToUETransform();
	AreaTransform = FTransform();
	JsonWriter->WriteObjectStart();
	JsonWriter->WriteValue("ObjType", static_cast<int>(EDSRenderObjectType::E_Geometry));
	FDSMeshInfo MeshInfo = ModelTWeakPtr->GetMeshInfo();
	for ( auto& MeshData :MeshInfo.MeshDatas)
	{
		for(int i = 0;i<MeshData.Value.Indices.Num(); i+=3)
		{ 
			int32 Temp = MeshData.Value.Indices[i];

			MeshData.Value.Indices[i] = MeshData.Value.Indices[i + 2];
			MeshData.Value.Indices[i+2] = Temp;
		}
	}
	FVector2D UVSize;
	UVSize.X = ModelTWeakPtr->GetProperty()->MaterialProperty.MaterialInfo3D[0].USize*0.1f;
	UVSize.Y = ModelTWeakPtr->GetProperty()->MaterialProperty.MaterialInfo3D[0].VSize*0.1f;
	if (UVSize.ContainsNaN()||FMath::IsNearlyZero(UVSize.X)||FMath::IsNearlyZero(UVSize.Y))
	{
		UVSize = FVector2D(100.f, 100.f);
	}
	ExpandMeshAlongNormal(MeshInfo, UVSize,24.f,3.f);
	UDSRenderFunctionLibrary::CollectionGeometryInfo(JsonWriter, MeshInfo,false, false);
	UDSRenderFunctionLibrary::CollectionTransformInfo(JsonWriter, AreaTransform);
	JsonWriter->WriteValue("MatID", TEXT("WallMat"));
	JsonWriter->WriteObjectEnd();
}


void FDSVRayCupboardConverter::Serialization(TSharedRef<TJsonWriter<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>> JsonWriter)
{

	if (!ModelTWeakPtr.IsValid())
	{
		return;
	}
	ADSCupboardBaseView* CupboardView = Cast<ADSCupboardBaseView>(ModelTWeakPtr->GetOwnedView());
	if (!CupboardView)
	{
		return;
	}
	const auto& MeshInfo = CupboardView->GetCustomMeshInfo();
	for (auto& MeshComp : MeshInfo.MeshComps)
	{

		int SectionNum = MeshComp->GetNumSections();
		if (SectionNum <= 0)
		{
			return;
		}
		for (int SectionIndex = 0; SectionIndex < SectionNum; ++SectionIndex)
		{
			auto ComponentSection = MeshComp->GetProcMeshSection(SectionIndex);
			JsonWriter->WriteObjectStart();
			JsonWriter->WriteValue("ObjType", static_cast<int>(EDSRenderObjectType::E_Geometry));
			FTransform Transform = MeshComp->GetComponentTransform();
			UDSRenderFunctionLibrary::CollectionGeometryInfo(JsonWriter, ComponentSection, Transform.GetScale3D());
			UDSRenderFunctionLibrary::CollectionTransformInfo(JsonWriter, Transform);
			{
				TArray<FName> MaterialNames;
				MeshComp->GetUsedMaterialNames(MaterialNames);

				if (MaterialNames.IsValidIndex(0))
				{
					JsonWriter->WriteValue(TEXT("MatID"), MaterialNames[0].ToString());
				}
			}
			JsonWriter->WriteObjectEnd();
		}
	}

	UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(ModelTWeakPtr);
	if (IsValid(CupboardModel))
	{
		SerializatedImportMode(JsonWriter, CupboardModel->GetComponentTreeDataRef(), CupboardView->GetActorTransform());
	}

}

void FDSVRayCupboardConverter::SerializatedImportMode(TSharedRef<TJsonWriter<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>> JsonWriter, 
	const TSharedPtr<FMultiComponentDataItem>& InComponentTreeData, const FTransform& ParentTransform)
{

	if (InComponentTreeData->IsVisiable() == false)
	{
		return;
	}

	if (InComponentTreeData->ComponentType == ECompType::MultiCom)
	{
		//多部件递归
		for (auto ChildComponent : InComponentTreeData->ChildComponent)
		{
			if (UDSCupboardLibrary::ComponentNeedSeparate(ChildComponent->ComponentParameters)
				|| UDSCupboardLibrary::IsFunctionalCupboardModel(ChildComponent->ComponentParameters))
			{
				continue;
			}
			FTransform CurrentTransform;
			CurrentTransform.SetLocation(ChildComponent->ComponentLocation.GetLocation());
			CurrentTransform.SetScale3D(ChildComponent->ComponentScale.GetScale());
			 CurrentTransform.SetRotation(FQuat(ChildComponent->ComponentRotation.GetRotation()));
			CurrentTransform = CurrentTransform * ParentTransform;

			SerializatedImportMode(JsonWriter,ChildComponent,CurrentTransform);
		}
	}
	else if (InComponentTreeData->ComponentType == ECompType::ImportModel)
	{
		if (InComponentTreeData->SingleComponentData.IsValid()
			&& !FMath::IsNearlyZero(FCString::Atof(*InComponentTreeData->ComponentVisibility.Value)))
		{


			for (auto& SingleMeshIter : InComponentTreeData->SingleComponentData.ComponentItems)
			{
				FTransform Transform;
				Transform.SetLocation(SingleMeshIter.SingleComponentLocation.GetLocation());
				Transform.SetRotation(FQuat(SingleMeshIter.SingleComponentRotation.GetRotation()));
				Transform.SetScale3D(SingleMeshIter.SingleComponentScale.GetScale());
				Transform *= ParentTransform;

				JsonWriter->WriteObjectStart();
				JsonWriter->WriteValue("ObjType", static_cast<int>(EDSRenderObjectType::E_Model));
				JsonWriter->WriteValue("ModelID", InComponentTreeData->ComponentID.GetFormattedValue());
				UDSRenderFunctionLibrary::CollectionTransformInfo(JsonWriter, Transform);
				JsonWriter->WriteObjectEnd();
			}
		}
	}
}

void FDSVRayCupboardConverter::CollectionImportModelResourceInfo(TMap<FString, FDSVRayRenderResourceInfo>& ResourceInfoMap, const TSharedPtr<FMultiComponentDataItem>& InComponentTreeData)
{

	if (InComponentTreeData->IsVisiable() == false)
	{
		return;
	}
	FTransform CurrentTransform;
	CurrentTransform.SetLocation(InComponentTreeData->ComponentLocation.GetLocation());
	CurrentTransform.SetScale3D(InComponentTreeData->ComponentScale.GetScale());
	CurrentTransform.SetRotation(FQuat(InComponentTreeData->ComponentRotation.GetRotation()));
	if (InComponentTreeData->ComponentType == ECompType::MultiCom)
	{

		//多部件递归
		for (auto ChildComponent : InComponentTreeData->ChildComponent)
		{
			if (UDSCupboardLibrary::ComponentNeedSeparate(ChildComponent->ComponentParameters)
				||UDSCupboardLibrary::IsFunctionalCupboardModel(ChildComponent->ComponentParameters))
			{
				continue;
			}
			CollectionImportModelResourceInfo(ResourceInfoMap, ChildComponent);
		}
	}
	else if (InComponentTreeData->ComponentType == ECompType::ImportModel)
	{
		//导入模型获取信息
		if (InComponentTreeData->SingleComponentData.IsValid()
			&& !FMath::IsNearlyZero(FCString::Atof(*InComponentTreeData->ComponentVisibility.Value)))
		{

			TSharedPtr<FDSResourceInfo> ResourceInfo = UDSResourceSubsystem::GetInstance()->FindResourceFromCache(InComponentTreeData->ComponentID.GetFormattedValue());
			if (ResourceInfo.IsValid())
			{
				auto ResourceFile = ResourceInfo->GetResourceFile(EDSResourceQuality::High);
				if (!ResourceFile.FilePath.IsEmpty())
				{
					ResourceInfoMap.Add(ResourceInfo->FolderId, FDSVRayRenderResourceInfo(ResourceInfo->FolderId, ResourceFile.FilePath, EDSRenderResourceType::E_Model));
				}
			}
			
		}
	}
}

void FDSVRayCupboardConverter::CollectionNeededRequestResourInfo(TMap<FString, FDSVRayRenderResourceInfo>& ResourceInfoMap)
{

	ADSCupboardBaseView* CupboardView = Cast<ADSCupboardBaseView>(ModelTWeakPtr->GetOwnedView());

	if (!CupboardView)
	{
		return;
	}

	const auto& MeshInfo = CupboardView->GetCustomMeshInfo();
	for (auto& MeshComp : MeshInfo.MeshComps)
	{
		if (MeshComp == nullptr)
		{
			continue;
		}

		TArray<FName> MaterialNames;
		MeshComp->GetUsedMaterialNames(MaterialNames);

		if (MaterialNames.IsValidIndex(0))
		{
			const FString& MatInfoFolderID = MaterialNames[0].ToString();
				//OutResourceInfo.Add(MatInfoFolderID, static_cast<int>(EDSResourceType::Material));

				TSharedPtr<FDSResourceInfo> ResourceInfo = UDSResourceSubsystem::GetInstance()->FindResourceFromCache(MatInfoFolderID);
				if (ResourceInfo.IsValid())
				{
					auto ResourceFile = ResourceInfo->GetResourceFile(EDSResourceQuality::High);
					if (!ResourceFile.FilePath.IsEmpty())
					{
						if (!ResourceInfoMap.Contains(ResourceInfo->FolderId))
						{
							FString LStr;
							FString RStr;
							ResourceFile.FilePath.Split("/", &LStr, &RStr, ESearchCase::Type::IgnoreCase, ESearchDir::Type::FromEnd);
							LStr.Split(".com/", &LStr, &RStr);
							ResourceInfoMap.Add(ResourceInfo->FolderId, FDSVRayRenderResourceInfo(ResourceInfo->FolderId, RStr,EDSRenderResourceType::E_Material));
						}
					}
				}

		}
	}

	
	UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(ModelTWeakPtr);
	if (IsValid(CupboardModel))
	{
		CollectionImportModelResourceInfo(ResourceInfoMap, CupboardModel->GetComponentTreeDataRef());
	}
}

void FDSVRayCounterTopConverter::Serialization(TSharedRef<TJsonWriter<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>> JsonWriter)
{
	ADSCounterTopBaseView* CounterTopView = Cast<ADSCounterTopBaseView>(ModelTWeakPtr->GetOwnedView());

	if (!CounterTopView)
	{
		return;
	}
	const auto& CounterTopProperty = StaticCastSharedPtr<FDSCounterTopProperty>(ModelTWeakPtr->GetPropertySharedPtr());
	TArray<UProceduralMeshComponent*> MeshComponents;
	MeshComponents.Add(CounterTopView->GetMeshComponentRef());
	MeshComponents.Add(Cast<UProceduralMeshComponent>(CounterTopView->GetWaterContainerComponent()));
	for (auto& MeshComponent : MeshComponents)
	{

		int SectionNum = MeshComponent->GetNumSections();
		if (SectionNum <= 0)
		{
			return;
		}
		for (int SectionIndex = 0; SectionIndex < SectionNum; ++SectionIndex)
		{
			const auto& ComponentSection = MeshComponent->GetProcMeshSection(SectionIndex);

			JsonWriter->WriteObjectStart();
			JsonWriter->WriteValue("ObjType", static_cast<int>(EDSRenderObjectType::E_Geometry));
			FTransform Transform = MeshComponent->GetComponentTransform();
			UDSRenderFunctionLibrary::CollectionGeometryInfo(JsonWriter, ComponentSection, Transform.GetScale3D());
			UDSRenderFunctionLibrary::CollectionTransformInfo(JsonWriter, Transform);
			{
				if (CounterTopProperty->bIsMaterialEnabled)
				{
					JsonWriter->WriteValue(TEXT("MatID"), CounterTopProperty->MaterialFolderId);
				}
				else
				{
					//添加默认材质
					JsonWriter->WriteValue(TEXT("MatID"), TEXT("WallMat"));
				}
			}
			JsonWriter->WriteObjectEnd();
		}


	}
}

void FDSVRayCounterTopConverter::CollectionNeededRequestResourInfo(TMap<FString, FDSVRayRenderResourceInfo>& ResourceInfoMap){

	const auto& CounterTopProperty = StaticCastSharedPtr<FDSCounterTopProperty>(ModelTWeakPtr->GetPropertySharedPtr());
		TSharedPtr<FDSResourceInfo> ResourceInfo = UDSResourceSubsystem::GetInstance()->FindResourceFromCache(CounterTopProperty->MaterialFolderId);
		if (ResourceInfo.IsValid())
		{
			auto ResourceFile = ResourceInfo->GetResourceFile(EDSResourceQuality::High);
			if (!ResourceFile.FilePath.IsEmpty())
			{
				if (!ResourceInfoMap.Contains(ResourceInfo->FolderId))
				{
					FString LStr;
					FString RStr;
					ResourceFile.FilePath.Split("/", &LStr, &RStr, ESearchCase::Type::IgnoreCase, ESearchDir::Type::FromEnd);
					LStr.Split(".com/", &LStr, &RStr);
					ResourceInfoMap.Add(ResourceInfo->FolderId, FDSVRayRenderResourceInfo(ResourceInfo->FolderId, RStr,EDSRenderResourceType::E_Material));
				}
			}
		}
}


void FDSVRaySinkConverter::Serialization(TSharedRef<TJsonWriter<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>> JsonWriter)
{
	FDSVRayCupboardConverter::Serialization(JsonWriter);
}

void FDSVRaySinkConverter::CollectionNeededRequestResourInfo(TMap<FString, FDSVRayRenderResourceInfo>& ResourceInfoMap)
{
	FDSVRayCupboardConverter::CollectionNeededRequestResourInfo(ResourceInfoMap);
}

void FDSVRayFurnitureConverter::Serialization(TSharedRef<TJsonWriter<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>> JsonWriter)
{
	

	if (!ModelTWeakPtr.IsValid())
	{
		return;
	}

	auto OwnedView = ModelTWeakPtr->GetOwnedView();
	if (!IsValid(OwnedView))
	{
		return;
	}


	//bool bRendered = OwnedView->WasRecentlyRendered(0.1f);
	//if (!bRendered)
	//{
	//	return;
	//}
	TSharedPtr<FDSSoftFurnitureProperty> FurnitureProperty = StaticCastSharedPtr<FDSSoftFurnitureProperty>(ModelTWeakPtr->GetPropertySharedPtr());

	const auto& BusinessInfo = FurnitureProperty->BusinessInfo;
	JsonWriter->WriteObjectStart();
	JsonWriter->WriteValue("ObjType", static_cast<int>(EDSRenderObjectType::E_Model));
	JsonWriter->WriteValue("ModelID", FurnitureProperty->BusinessInfo.Code);

	UDSRenderFunctionLibrary::CollectionTransformInfo(JsonWriter, ModelTWeakPtr->GetPropertySharedPtr()->GetActualTransformWithRealScale());

	JsonWriter->WriteArrayStart("ReplacementMaterials");
	const auto& MaterialInfoArray = FurnitureProperty->MaterialProperty.MaterialInfo3D;
	for (const auto& MaterialItem : MaterialInfoArray)
	{
		const auto ResourceId = MaterialItem.Id;
		JsonWriter->WriteObjectStart();
		JsonWriter->WriteValue("MatID", "ID" + ResourceId);
		JsonWriter->WriteValue("MultiIndex", MaterialItem.Index);
		JsonWriter->WriteObjectEnd();
	}
	JsonWriter->WriteArrayEnd();
	JsonWriter->WriteObjectEnd();

}

void FDSVRayFurnitureConverter::CollectionNeededRequestResourInfo(TMap<FString, FDSVRayRenderResourceInfo>& ResourceInfoMap)
{
	if (!ModelTWeakPtr.IsValid())
	{
		return;
	}
	TSharedPtr<FDSSoftFurnitureProperty> FurnitureProperty = StaticCastSharedPtr<FDSSoftFurnitureProperty>(ModelTWeakPtr->GetPropertySharedPtr());

	const auto& BusinessInfo = FurnitureProperty->BusinessInfo;

	TArray<FDSVRayRenderResourceInfo> ResourceInfos;

	if (!ResourceInfoMap.Contains(BusinessInfo.Code))
	{
		ResourceInfoMap.Add(BusinessInfo.Code, FDSVRayRenderResourceInfo(BusinessInfo.Code, BusinessInfo.GetResourceFile(EDSResourceQuality::High).FilePath, EDSRenderResourceType::E_Model));
	}

	const auto& MaterialInfoArray = FurnitureProperty->MaterialProperty.MaterialInfo3D;
}

void FDSVRayFurnitureConverter::CollectionNeededRequestResourInfo(TMap<FString, FDSVRayRenderResourceInfo>& ResourceInfoMap, TMap<FString, int>& NeedQueryResourceMap)
{

	if (!ModelTWeakPtr.IsValid())
	{
		return;
	}
	TSharedPtr<FDSSoftFurnitureProperty> FurnitureProperty = StaticCastSharedPtr<FDSSoftFurnitureProperty>(ModelTWeakPtr->GetPropertySharedPtr());

	const auto& BusinessInfo = FurnitureProperty->BusinessInfo;

	TArray<FDSVRayRenderResourceInfo> ResourceInfos;

	if (!ResourceInfoMap.Contains(BusinessInfo.Code))
	{
		ResourceInfoMap.Add(BusinessInfo.Code, 
			FDSVRayRenderResourceInfo(BusinessInfo.Code, BusinessInfo.GetResourceFile(EDSResourceQuality::High).FilePath, EDSRenderResourceType::E_Model));
	}

	const auto& MaterialInfoArray = FurnitureProperty->MaterialProperty.MaterialInfo3D;
	for (const auto& MaterialItem : MaterialInfoArray)
	{


		const auto ResourceId = MaterialItem.Id;

		if (!NeedQueryResourceMap.Contains(ResourceId))
		{
			NeedQueryResourceMap.Add(ResourceId, static_cast<int>(EDSResourceType::Material));
		}
	}

}

void FDSVRaySphereLightConverter::Serialization(TSharedRef<TJsonWriter<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>> JsonWriter)
{
	if (!ModelTWeakPtr.IsValid())
	{
		return;
	}
	TSharedPtr<FDSVirtualLightSphericalProperty> LightSphericalProperty = StaticCastSharedPtr<FDSVirtualLightSphericalProperty>(ModelTWeakPtr->GetPropertySharedPtr());
	JsonWriter->WriteObjectStart();
	JsonWriter->WriteValue("ObjType", static_cast<int>(ObjectType));
	JsonWriter->WriteValue("Enable", LightSphericalProperty->bIsEnabled);
	JsonWriter->WriteValue("LightRadius", LightSphericalProperty->LightRadius*0.1f);
	JsonWriter->WriteValue("Intensity", LightSphericalProperty->LightIntensity);
	JsonWriter->WriteValue("AffectSpecular", LightSphericalProperty->bAffectSpecular);
	JsonWriter->WriteValue("UseTemperature", LightSphericalProperty->bUseTemperatureColor);
	JsonWriter->WriteValue("Temperature", LightSphericalProperty->LightTemperature);
	UDSRenderFunctionLibrary::SerializationLinearColor(JsonWriter, LightSphericalProperty->LightColor, TEXT("TemperatureColor"));
	UDSRenderFunctionLibrary::CollectionTransformInfo(JsonWriter, ModelTWeakPtr->GetPropertySharedPtr()->GetActualTransformWithRealScale());
	JsonWriter->WriteObjectEnd();
	
}

void FDSVRayRectLightConverter::Serialization(TSharedRef<TJsonWriter<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>> JsonWriter)
{
	if (!ModelTWeakPtr.IsValid())
	{
		return;
	}
	TSharedPtr<FDSVirtualLightRectProperty> LightRectProperty = StaticCastSharedPtr<FDSVirtualLightRectProperty>(ModelTWeakPtr->GetPropertySharedPtr());
	JsonWriter->WriteObjectStart();
	JsonWriter->WriteValue("ObjType", static_cast<int>(ObjectType));
	JsonWriter->WriteValue("Enable", LightRectProperty->bIsEnabled);
	JsonWriter->WriteValue("DoubleSide", LightRectProperty->bDoubleSide);
	JsonWriter->WriteValue("USize", LightRectProperty->LightLength*0.1f);
	JsonWriter->WriteValue("VSize", LightRectProperty->LightWidth*0.1f);
	JsonWriter->WriteValue("Intensity", LightRectProperty->LightIntensity);
	JsonWriter->WriteValue("AffectSpecular", LightRectProperty->bAffectSpecular);

	JsonWriter->WriteValue("UseTemperature", LightRectProperty->bUseTemperatureColor);
	JsonWriter->WriteValue("Temperature", LightRectProperty->LightTemperature);
	UDSRenderFunctionLibrary::SerializationLinearColor(JsonWriter, LightRectProperty->LightColor, TEXT("TemperatureColor"));
	UDSRenderFunctionLibrary::CollectionTransformInfo(JsonWriter, ModelTWeakPtr->GetPropertySharedPtr()->GetActualTransformWithRealScale());
	JsonWriter->WriteObjectEnd();
}
void FDSVRayLineLightConverter::Serialization(TSharedRef<TJsonWriter<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>> JsonWriter)
{
	if (!ModelTWeakPtr.IsValid())
	{
		return;
	}
	TSharedPtr<FDSVirtualLightLineProperty> LightRectProperty = StaticCastSharedPtr<FDSVirtualLightLineProperty>(ModelTWeakPtr->GetPropertySharedPtr());
	JsonWriter->WriteObjectStart();
	JsonWriter->WriteValue("ObjType", static_cast<int>(ObjectType));
	JsonWriter->WriteValue("Enable", LightRectProperty->bIsEnabled);
	JsonWriter->WriteValue("DoubleSide", LightRectProperty->bDoubleSide);
	JsonWriter->WriteValue("USize", LightRectProperty->LightLength * 0.1f);
	JsonWriter->WriteValue("VSize", LightRectProperty->LightWidth * 0.1f);
	JsonWriter->WriteValue("Intensity", LightRectProperty->LightIntensity);
	JsonWriter->WriteValue("AffectSpecular", LightRectProperty->bAffectSpecular);

	JsonWriter->WriteValue("UseTemperature", LightRectProperty->bUseTemperatureColor);
	JsonWriter->WriteValue("Temperature", LightRectProperty->LightTemperature);
	UDSRenderFunctionLibrary::SerializationLinearColor(JsonWriter, LightRectProperty->LightColor, TEXT("TemperatureColor"));
	UDSRenderFunctionLibrary::CollectionTransformInfo(JsonWriter, ModelTWeakPtr->GetPropertySharedPtr()->GetActualTransformWithRealScale());
	JsonWriter->WriteObjectEnd();
}



void FDSVRayIESLightConverter::Serialization(TSharedRef<TJsonWriter<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>> JsonWriter)
{
	if (!ModelTWeakPtr.IsValid())
	{
		return;
	}
	TSharedPtr<FDSVirtualLightIESProperty> LightIESProperty = StaticCastSharedPtr<FDSVirtualLightIESProperty>(ModelTWeakPtr->GetPropertySharedPtr());
	JsonWriter->WriteObjectStart();
	JsonWriter->WriteValue("ObjType", static_cast<int>(ObjectType));
	JsonWriter->WriteValue("Enable", LightIESProperty->bIsEnabled);
	JsonWriter->WriteValue("Intensity", LightIESProperty->LightIntensity);
	JsonWriter->WriteValue("AffectSpecular", LightIESProperty->bAffectSpecular);
	JsonWriter->WriteValue("IESKey", "IES"+FString::FromInt(LightIESProperty->Identify));

	JsonWriter->WriteValue("UseTemperature", LightIESProperty->bUseTemperatureColor);
	JsonWriter->WriteValue("Temperature", LightIESProperty->LightTemperature);
	UDSRenderFunctionLibrary::SerializationLinearColor(JsonWriter, LightIESProperty->LightColor, TEXT("TemperatureColor"));

	UDSRenderFunctionLibrary::CollectionTransformInfo(JsonWriter, ModelTWeakPtr->GetPropertySharedPtr()->GetActualTransformWithRealScale());
	JsonWriter->WriteObjectEnd();
}




void FDSVRayIESLightConverter::CollectionNeededRequestResourInfo(TMap<FString, FDSVRayRenderResourceInfo>& ResourceInfoMap)
{
	TSharedPtr<FDSVirtualLightIESProperty> LightSphericalProperty = StaticCastSharedPtr<FDSVirtualLightIESProperty>(ModelTWeakPtr->GetPropertySharedPtr());
	FDSRenderingIESInfo* IESInfo = UDSRenderSubsystem::GetInstance()->RenderingIESInfos.FindByPredicate(

		[&](const FDSRenderingIESInfo& RenderIEsInfo)
		{
			if (RenderIEsInfo.Identify == LightSphericalProperty->Identify)
			{
				return true;
			}
			return false;
		}
	);
	if (IESInfo)
	{
		FString Code = "IES"+FString::FromInt(IESInfo->Identify);
		//FString LStr;
		//FString RStr;
		//IESInfo->IESUrl.Split(".com/", &LStr, &RStr);
		ResourceInfoMap.Add(Code, FDSVRayRenderResourceInfo(Code, IESInfo->IESUrl, EDSRenderResourceType::E_IES));
	}
}

void FDSVRayImportActorConverter::Serialization(TSharedRef<TJsonWriter<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>> JsonWriter)
{
}

void FDSVRayImportActorConverter::CollectionNeededRequestResourInfo(TMap<FString, FDSVRayRenderResourceInfo>& ResourceInfoMap)
{

}

void FDSVRayCustomLineConverter::Serialization(TSharedRef<TJsonWriter<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>> JsonWriter)
{

	UDSLineEntityModel* LineEntityModel = Cast<UDSLineEntityModel>(ModelTWeakPtr.Get());
	ADSLineEntityView* LineEntityView = Cast<ADSLineEntityView>(ModelTWeakPtr->GetOwnedView());

	if (!LineEntityView)
	{
		return;
	}
	const auto& CounterTopProperty = StaticCastSharedPtr<FDSCounterTopProperty>(ModelTWeakPtr->GetPropertySharedPtr());
	TArray<UProceduralMeshComponent*> MeshComponents;
	MeshComponents.Add(LineEntityView->GetMeshComponentRef());
	for (auto& MeshComponent : MeshComponents)
	{
		int SectionNum = MeshComponent->GetNumSections();
		if (SectionNum <= 0)
		{
			return;
		}
		for (int SectionIndex = 0; SectionIndex < SectionNum; ++SectionIndex)
		{
			auto ComponentSection = MeshComponent->GetProcMeshSection(SectionIndex);
			JsonWriter->WriteObjectStart();
			JsonWriter->WriteValue("ObjType", static_cast<int>(EDSRenderObjectType::E_Geometry));
			FTransform Transform = MeshComponent->GetComponentTransform();
			UDSRenderFunctionLibrary::CollectionGeometryInfo(JsonWriter, ComponentSection, Transform.GetScale3D());
			UDSRenderFunctionLibrary::CollectionTransformInfo(JsonWriter, Transform);
			{

				TPair<FString, FString> ResourceIds = LineEntityModel->GetCrossSectionAndMaterialId();
				if (!ResourceIds.Value.IsEmpty())
				{
					//添加默认材质
					JsonWriter->WriteValue(TEXT("MatID"), ResourceIds.Value);
				}
			}
			JsonWriter->WriteObjectEnd();
		}
	}
}

void FDSVRayCustomLineConverter::CollectionNeededRequestResourInfo(TMap<FString, FDSVRayRenderResourceInfo>& ResourceInfoMap)
{
	UDSLineEntityModel* LineEntityModel = Cast<UDSLineEntityModel>(ModelTWeakPtr.Get());
	TPair<FString, FString> ResourceIds = LineEntityModel->GetCrossSectionAndMaterialId();
	if (!ResourceIds.Value.IsEmpty())
	{
		TSharedPtr<FDSResourceInfo> ResourceInfo = UDSResourceSubsystem::GetInstance()->FindResourceFromCache(ResourceIds.Value);
		if (ResourceInfo.IsValid())
		{
			auto ResourceFile = ResourceInfo->GetResourceFile(EDSResourceQuality::High);
			if (!ResourceFile.FilePath.IsEmpty())
			{
				if (!ResourceInfoMap.Contains(ResourceInfo->FolderId))
				{
					FString LStr;
					FString RStr;
					ResourceFile.FilePath.Split("/", &LStr, &RStr, ESearchCase::Type::IgnoreCase, ESearchDir::Type::FromEnd);
					LStr.Split(".com/", &LStr, &RStr);
					ResourceInfoMap.Add(ResourceInfo->FolderId, FDSVRayRenderResourceInfo(ResourceInfo->FolderId, RStr, EDSRenderResourceType::E_Material));
				}
			}
		}
	}
}

