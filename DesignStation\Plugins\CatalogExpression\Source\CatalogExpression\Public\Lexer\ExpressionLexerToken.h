﻿#pragma once

#include "CoreMinimal.h"
#include "ExpressionLexerToken.generated.h"

UENUM(BlueprintType)
enum class EExpressionLexerTokenType : uint8
{
	Unknown = 0,		// 未知
	Number,				// 整数
	Float,				// 浮点数
	CharLiteral,		// 字符字面量 'a'
	String,				// 字符串	"abc"
	Identifier,			// 标识符
	MonocularOperator,	// 单目运算符
	BinocularOperator,	// 双目运算符
	Keyword,			// 关键字
	Comment,			// 注释
	LeftParen,			// 左括号
	RightParen,			// 右括号
	LeftBrace,			// 左大括号
	RightBrace,			// 右大括号
	Comma,				// 逗号
	Semicolon,			// 分号
	LineFeed,			// 换行符
	Whitespace,			// 空白符
	EndOfFile,			// 文件结束
};

USTRUCT(BlueprintType)
struct CATALOGEXPRESSION_API FExpressionLexerToken
{
	GENERATED_BODY()
public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "ExpressionLexer | Token")
	EExpressionLexerTokenType Type;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "ExpressionLexer | Token")
	FString Value;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "ExpressionLexer | Token")
	FIntPoint Position;

	FExpressionLexerToken();
	FExpressionLexerToken(EExpressionLexerTokenType InType, const FString& InValue, const FIntPoint& InPosition);

	FString ToString() const;
};
