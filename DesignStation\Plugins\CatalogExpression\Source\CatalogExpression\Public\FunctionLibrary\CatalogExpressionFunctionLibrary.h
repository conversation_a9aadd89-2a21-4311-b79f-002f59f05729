﻿// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "Lexer/ExpressionLexerToken.h"
#include "CatalogExpressionFunctionLibrary.generated.h"

struct FAstNode;

UENUM(BlueprintType)
enum class EExpressionFunctionType : uint8
{
	Unknow = 0,
	If,				// IF(EXPR, A, B)
	Condition,		// COND(EXP, A, B)
	Max,			// MAX(A, B)
	Min,			// MIN(A, B)
	Sin,			// SIN(Val)
	Cos,			// COS(Val)
	Sqrt,			// SQRT(Val)
	Abs,			// ABS(Val)
	Combine,			// COMB(A, B)
};


/**
 * 
 */
UCLASS()
class CATALOGEXPRESSION_API UCatalogExpressionFunctionLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()
public:
	UFUNCTION(BlueprintPure, Category = "CatalogExpression | Lexer | Token")
	static FString GetTokenTypeDisplayName(EExpressionLexerTokenType InType);

	UFUNCTION(BlueprintPure, Category = "CatalogExpression | Parser")
	static EExpressionFunctionType GetExpressionFunctionType(const FString& InFunctionName);
	
	static FString EvaluateExpressionNode(const TSharedPtr<FAstNode>& Node, const TMap<FString, FString>& SymbolTables);

protected:
	static FString EvaluateUnaryOperator(const FString& Operator, const FString& Operand);
	static FString EvaluateBinaryOperator(const FString& Left, const FString& Operator, const FString& Right);
	
	static FString EvaluateFunctionCall_If(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, FString>& SymbolTables);
	static FString EvaluateFunctionCall_Condition(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, FString>& SymbolTables);
	static FString EvaluateFunctionCall_Max(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, FString>& SymbolTables);
	static FString EvaluateFunctionCall_Min(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, FString>& SymbolTables);
	static FString EvaluateFunctionCall_Sin(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, FString>& SymbolTables);
	static FString EvaluateFunctionCall_Cos(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, FString>& SymbolTables);
	static FString EvaluateFunctionCall_Sqrt(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, FString>& SymbolTables);
	static FString EvaluateFunctionCall_Abs(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, FString>& SymbolTables);
	static FString EvaluateFunctionCall_Combine(const TArray<TSharedPtr<FAstNode>>& InArgs, const TMap<FString, FString>& SymbolTables);

public:
	// Test functions
	UFUNCTION(BlueprintCallable, Category = "CatalogExpression | Lexer | Token | Test")
	static void TestCatalogExpressionLexer(const FString& InExpression);

	static void PrintAstNode(const TSharedPtr<FAstNode>& Node, int32 IndentLevel = 0);
};
