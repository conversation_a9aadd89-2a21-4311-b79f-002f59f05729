﻿#pragma once

#include "CoreMinimal.h"
#include "Interfaces/IHttpRequest.h"
#include "ModelInfo/DSCupboardModelInfo.h"
#include "SubSystems/MVC/Model/DSBaseModel.h"
#include "Subsystems/UI/Widget/Common/ResourceItem/Structures/ResourceInfo.h"
#include "Subsystems/MVC/Model/Custom/ModelInfo/DSCustomStyleInfo.h"
#include "DSCupboardModel.generated.h"

class UDSCustomCupboardLoader;
class FDynamicMeshAdaptiveAdsorption;

class FModelAdaptationOperatorBase;
class FDSCupboardNodeRecord;

DECLARE_MULTICAST_DELEGATE(FOnCupboardModelParseCompleteDel);

DECLARE_MULTICAST_DELEGATE_OneParam(FOnCupboardModelDistanceToFloorChangeDel, float);
DECLARE_MULTICAST_DELEGATE_OneParam(FOnCupboardModelApplyStyleComplete, UDSCupboardModel*);

/**
 *  
 */
UCLASS()
class UDSCupboardModel : public UDSBaseModel
{
	GENERATED_BODY()

public:
	UDSCupboardModel();

	virtual void InitConstruct(const FRefToLocalFileData& InData);
	void InitConstruct_Separate(const TSharedPtr<FMultiComponentDataItem>& InComponentData, const TMap<FString, FParameterData>& ParentParams,
		TMap<TSharedPtr<FMultiComponentDataItem>, TSharedPtr<TMap<FString, FParameterData>>>& ComponentOverrideParamsPool,
		const FTransform& InRelativeTransform = FTransform());

	void InitConstruct_Functional(const FRefToLocalFileData& InData, UDSCupboardModel* OWnerModel, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr = FDSBroadcastMarkData::BroadcastToMVCMark);


	virtual void SetProperty(FDSBaseProperty* InProperty) override;
	virtual void InitProperty() override;

	virtual void InitAdditionData() override;

	virtual UDSBaseModel* OnCopy() override;

	virtual UDSBaseModel* CopyModel() override;

	virtual bool IsCanMultiSelect() const override { return true; }
	virtual bool IsCanGroup() const override;

	virtual bool CanMove(bool Is2D) const override;

	virtual void UpdatePivotOffset() override;

	virtual void OnExecute_Model_GenerateByProperty(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr) override;

	virtual void OnExecute_Model_UpdateSelf(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr) override;
	virtual void OnExecute_Model_Transform(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr) override;

	virtual void OnExecute_Model_Hidden(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr) override;
	virtual void OnExecute_Model_UnHidden(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr) override;
	virtual void OnExecute_Model_ViewTypeHidden(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr) override;
	virtual void OnExecute_Model_UnViewTypeHidden(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr) override;

	virtual void OnExecute_Model_Delete(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr) override;

	virtual void OnExecute_Model_Select(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr) override;
	virtual void OnExecute_Model_UnSelect(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr) override;

	virtual void OnExecute_Model_Overlap(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr) override;
	virtual void OnExecute_Model_UnOverlap(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr) override;

	virtual EDSGizmoModeType GetDefaultGizmoModeType(bool bIs2DScene) const override;

	virtual bool IsValidSizeToScale(EDSGizmoOperatorType OperatorType,  const FVector& SourceSize, const FVector& NewSize) const override;
	virtual bool ClampScaleSizeToLimitation(EDSGizmoOperatorType OperatorType, const FVector& SourceSize, FVector& NewSize) override;
	bool CupboardSizeValidToScale(EDSGizmoOperatorType OperatorType, const FVector& SourceSize, const FVector& NewSize) const;
	virtual void ScaleSizeByGizmo(EDSGizmoOperatorType OperatorType, const FVector& SourceSize, const FVector& NewSize) override;
	virtual void PreScaleSizeByGizmo(EDSGizmoOperatorType OperatorType, const FVector& SourceSize, const FVector& NewSize) override;
	virtual void PostScaleSizeByGizmo(EDSGizmoOperatorType OperatorType, const FVector& SourceSize, const FVector& NewSize) override;

	virtual void PreTranslateByGizmo(const EAxis::Type& InRotAxis, const FTransform& SourceTrans, const FTransform& NewTrans) override;
	virtual void PostTranslateByGizmo(const EAxis::Type& InRotAxis, const FTransform& SourceTrans, const FTransform& NewTrans) override;

	virtual void PostRotatorByGizmo(const EAxis::Type& InRotAxis, const FTransform& SourceTrans, const FTransform& NewTrans) override;
	virtual void RotatorByGizmo(const EAxis::Type& InRotAxis, const FTransform& SourceTrans, const FTransform& NewTrans);
	virtual FVector GetCurrentSize() override;
	virtual FVector GetCurrentRealSize() override;

	virtual void SyncScale(const FVector& InScale) override;
	virtual void ReInitData() override;
	bool IsValueInRange(const FString& InName, const float& InValue);
	virtual bool CurrentSizeValidToScale(const FVector& InScale) override;

	virtual bool SupportsLazyLoad() const override;
	virtual bool ShouldUseParentModel() const override;

	virtual void OnResourceStatusInPoolChanged(const FString& Identify, EDSResourceType ResourceType, EResourceStatusEventType EventType) override;
	virtual bool CheckIfNeedsStopObserveResourceStatus() const override;

	//序列化和反序列化
	virtual void Serialization(TSharedRef<TJsonWriter<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>> JsonWriter) override;
	virtual void Deserialization(const TSharedPtr<FJsonObject>& InJsonData, bool bDeserializeID = true) override;

	virtual TArray<FVector> GetModelOrientedBoundingBox(bool bUseFixedSize = true) const override;
	virtual TArray<FVector> GetModelOrientedBoundingBoxWithoutRotator(bool bUseFixedSize = true) const override;
	virtual TArray<FVector> GetModelFixedOrientedBoundingBox()const;

	bool GetCustomLayoutDoorSize(FVector & OutSize) const;
	virtual FTransform GetParentTransform();

	virtual void GetModelOrientedBoundingBox(FVector& Center, FVector& Extents, FQuat& Rotation) const override;

	virtual FBox GetBoundBoxByPropertyCalculate() override;
	virtual void CalculateOutline() override;
	virtual void UpdateOutlineInfo() override;
	virtual void SyncChildOutline() override;
	virtual bool ExpandOutlineForComponentSelect() override;
	virtual bool RecoverOutline() override;

	virtual TArray<TArray<FVector>> GetWorldFrameOutline() override;

	bool HasParsed() const;
	bool AreRelatedResourcesLoaded() const;

	virtual UDSCupboardModel* GetRootCupboardModel();

	virtual const FTransform& GetRelativeTransform() const;

	TArray<FParameterData> GetParams() const;

	TArray<FParameterData>& GetParamsRef() const;

	void GetSelfComponentOverriderParametersRef(TMap<FString, FParameterData>& OutParentOverriderParmeters) const;

	FTransform GetWorldTransform();

	// Should call after modify parameters of this layer, recalculate tree with new parameters.
	virtual void ReplaceComponent(const FRefToLocalFileData& InFileData);
	virtual void UpdateParseStatus();

	void UpdateDirect();

	virtual void ShallowCopy(UDSBaseModel* OtherModel) override;

	EDSCustomCupboardLoadState GetLoadState() const;

	const TSharedPtr<FMultiComponentDataItem>& GetComponentTreeDataRef() { return ModelInfo.ComponentTreeData; }
	FORCEINLINE const FDSCupboardModelInfo& GetModelInfo() const { return ModelInfo; }
	FDSCupboardModelInfo& GetModelInfoRef() { return ModelInfo; }
	void SetModelInfo(const FDSCupboardModelInfo& InModelInfo) { ModelInfo = InModelInfo; }

	/*
	*  @@ 获取MYGLX参数的值
	*/
	int32 GetFGType() const;

	/*
	*  @@ 获取是否内嵌
	*/
	bool IsEmbedded() const; 

    /*
	*  @@ 获取是否外盖
	*/
	bool IsOuterCover() const;

	/*
	 *  @@ calculate model when Model Info is valid
	 */
	void CalculateModelAfterModelInfoValid();

	virtual TArray<UDSBaseModel*> RemoveSelfFromDataTree();

	/**
	 * Apply a new style for cupboard.
	 * @param InNewStyle The applied new style.
	 */
	void ApplyNewStyle(const FApplyStyleData& InNewStyle);
	void ApplyRootStyle_Functional();
	FApplyStyleData GetApplyStyleData() const;

	void SetParentParams(const TMap<FString, FParameterData>& InParentParams) { ParentComponentParams = InParentParams; }

	virtual void InterruptParse();
	
	virtual FBox GetBoundBox() override;

	virtual void PreChangeEnumParameterValueFromWidget(const FString& InParamName, const FString& InOldValue, const FString& InNewValue);
	virtual void PreChangeDecimalParameterValueFromWidget(const FString& InParamName, const FString& InOldValue, const FString& InNewValue);

	virtual void PostChangeEnumParameterValueFromWidget(const FString& InParamName, const FString& InOldValue, const FString& InNewValue);
	virtual void PostChangeDecimalParameterValueFromWidget(const FString& InParamName, const FString& InOldValue, const FString& InNewValue);

	virtual FOnCupboardModelApplyStyleComplete& OnCupboardModelApplyStyleCompleteEvent();
	
	void PreParseTreeFromNode();

	void UpdateNodesToDisableCollision();

	const TArray<FString>& GetNodesToDisableCollision() const { return NodesToDisableCollision; }

protected:
	virtual void VerifyNeedsLazyLoadResources();
	
	virtual void CollectRelatedLazyLoadResource(const TSharedPtr<FMultiComponentDataItem>& InNode);
	
	virtual void SplitOverrideParamsPoolBySubtree(const TSharedPtr<FMultiComponentDataItem>& InNode,
		TMap<TSharedPtr<FMultiComponentDataItem>, TSharedPtr<TMap<FString, FParameterData>>>& ComponentOverrideParamsPool,
		TMap<TSharedPtr<FMultiComponentDataItem>, TSharedPtr<TMap<FString, FParameterData>>>& OutComponentOverrideParams);

	virtual void ProcessNodeAfterParsed(const TSharedPtr<FMultiComponentDataItem>& InOriginalNode, const TSharedPtr<FMultiComponentDataItem>& InParsedNode);

	virtual void ProcessSeparateChildren(const TSharedPtr<FMultiComponentDataItem>& InRoot, const TSharedPtr<FMultiComponentDataItem>& InParent, const TSharedPtr<FMultiComponentDataItem>& InNode);

	virtual void CreateSeparateChild(const TSharedPtr<FMultiComponentDataItem>& InRoot, const TSharedPtr<FMultiComponentDataItem>& InParent, const TSharedPtr<FMultiComponentDataItem>& InChild);

	virtual void UpdateParameterOfComponent_Inner(const TSharedPtr<FMultiComponentDataItem>& InComponent, const FParameterData& InNewParameter);
	
	virtual void RefreshSizePropertyAfterUpdate();
	
	virtual bool ComputeTreePath(const TSharedPtr<FMultiComponentDataItem>& StartNode, const TSharedPtr<FMultiComponentDataItem>& InTarget, FString& OutPath);
	virtual bool CollectComponentPath(const TSharedPtr<FMultiComponentDataItem>& StartNode, const TSharedPtr<FMultiComponentDataItem>& InTarget, TArray<TSharedPtr<FMultiComponentDataItem>>& OutPath);

	virtual bool IsParameterEquals(const FString& ParamName, const TSharedPtr<FMultiComponentDataItem>& A, const TSharedPtr<FMultiComponentDataItem>& B);

	virtual void FindAllGeneratedDoors(const TSharedPtr<FMultiComponentDataItem>& StartNode, TArray<TSharedPtr<FMultiComponentDataItem>>& OutDoors);

	virtual void QueryRelatedResourceList(const TMap<FString, EDSResourceType>& FolderIdsForDownload);
	virtual void QueryRelatedResourceList_Callback(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bConnectedSuccessfully);

	UFUNCTION()
	virtual void OnDownloadResourceTaskComplete(const FString& TaskId, bool bSucceed);

	virtual void ApplyResourceForImportModelNode(const TSharedPtr<FDSResourceInfo>& InInfo, const TSharedPtr<FMultiComponentDataItem>& InNode);

	virtual void CollectAllDoors(const TSharedPtr<FMultiComponentDataItem>& InNode, TMap<FString, TSharedPtr<FMultiComponentDataItem>>& OutDoorNodes);
	
	virtual void InterruptQueryMaterialByStyle();
	virtual void QueryStyleReplacements(const TArray<FDSCustomStyleReplacementNode>& InStylizedNodes, const FApplyStyleData& InStyle, EDSCustomAssociationType QueryType);
	virtual void OnQueryStyleReplacementsComplete(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bConnectedSuccessfully);

	virtual void RecursiveReplaceNodeMaterial(const TSharedPtr<FMultiComponentDataItem>& InNode, const TMap<FString, FDSCustomReplaceStyleResult>& InResults);

	/**
	* 处理在设置为新风格时生成的门与拉手
	* 规则：
	*	1.新风格中没有门型和拉手，不处理门和拉手
	*	2.新风格中有门型，替换门，处理拉手
	*		1.门上参数不允许生成拉手，删除拉手
	*		2.门上参数允许生成拉手
	*			1.新风格中拉手参数存在，拉手保留参数并替换为新风格拉手
	*			2.新风格中拉手参数不存在，删除拉手
	*	3.新风格中没有门型，有拉手，替换拉手，不处理门
	*/
	virtual void ReplaceDoorAndHandleNode(const TArray<FDSCustomReplaceStyleResult>& InResults);
	virtual void ReplaceNormalNode(const TArray<FDSCustomReplaceStyleResult>& InResults);
	
	virtual void RecursiveReplaceNormalNodePattern(const TSharedPtr<FMultiComponentDataItem>& InNode, const TMap<FString, FDSCustomReplaceStyleResult>& InReplaceMap);


	virtual void RefreshChildModelActualTransform();
	
public:
	virtual bool CollectComponentPath_Public(const TSharedPtr<FMultiComponentDataItem>& StartNode, const TSharedPtr<FMultiComponentDataItem>& InTarget,
	                                         TArray<TSharedPtr<FMultiComponentDataItem>>& OutPath);

	FString GetComponentTreePath(const TSharedPtr<FMultiComponentDataItem>& InTarget);
	bool GetComponentTreePath_Recurse(const TSharedPtr<FMultiComponentDataItem>& TreeNode, const TSharedPtr<FMultiComponentDataItem>& InTarget, FString& OutPath);

	void ParseDataByNodePath(TSharedPtr<FMultiComponentDataItem> InData, FString NodePath);
	void ParseDataByNodePath_Recurse(TSharedPtr<FMultiComponentDataItem> TreeNode, const TSharedPtr<FMultiComponentDataItem>& InTarget, FString NodePath);

	void GetDataByNodePathRet(TSharedPtr<FMultiComponentDataItem> InData, FString NodePath, TSharedPtr<FMultiComponentDataItem>& OutTargetNode);
	void GetDataByNodePath_RecurseRet(TSharedPtr<FMultiComponentDataItem> TreeNode, FString NodePath, TSharedPtr<FMultiComponentDataItem>& OutTargetNode);

	virtual void BroadcastLinkModelsTransform();
	virtual void BroadcastLinkModelsUpdateSelf();
	virtual void BroadcastLinkModelsZHeight();

	TSet<FString> GetAllComponentModelsUUID(const FDSCupboardModelInfo & InInfo);
	TSet<FString> GetAllComponentModelsUUID(bool bIncludeSelf = true);

	TMap<FString, FString> RegenerateUUIDs();

	TMap<TSharedPtr<FMultiComponentDataItem>, TSharedPtr<TMap<FString, FParameterData>>>& GetComponentOverrideParamsRef() { return ComponentOverrideParams;};

	const TMap<FString, FParameterData>& GetParentComponentParams() const { return ParentComponentParams;};

	void SetParsed(bool bInParsed);

	void RecoverModelFromData(const FDSCupboardModelInfo& Info, bool bInParsed = true);
	
protected:
	UPROPERTY()
	FDSCupboardModelInfo ModelInfo;

	TSharedPtr<FMultiComponentDataItem> OriginalComponentData;

	EDSCustomCupboardLoadState LoadState;

	bool bPreParsed;

	// Indicates the depth of the current parsing hierarchy.
	bool bParsed;
	bool bResourceAllLoaded;
	TMap<FString, FParameterData> ParentComponentParams;

	TMap<TSharedPtr<FMultiComponentDataItem>, TSharedPtr<TMap<FString, FParameterData>>> ComponentOverrideParams;

	FHttpRequestPtr RequestHandle;
	TArray<FString> ProcessingTaskIds;

	FHttpRequestPtr StyleReplacementHandle;
	
	// For every layer, not for all.
	int32 NeedsDownloadResourceCount;
	int32 DownloadedResourceCount;

	TMap<FString, EDSResourceType> LazyLoadResources;

	FTransform RelativeTransform;

	TArray<FString> NodesToDisableCollision;
	
	FOnCupboardModelParseCompleteDel OnCupboardModelParseCompleteHandle;

	FOnCupboardModelDistanceToFloorChangeDel OnCupboardModelDistanceToFloorChangeHandle;

	FOnCupboardModelApplyStyleComplete OnCupboardModelApplyStyleComplete;

public:
	
	FOnCupboardModelParseCompleteDel& ParseCompleteHandle() { return OnCupboardModelParseCompleteHandle; }


	FOnCupboardModelDistanceToFloorChangeDel& OnDistanceToFloorChangeHandle() { return OnCupboardModelDistanceToFloorChangeHandle; }


	virtual void AddFunctionalCupboardModel(UDSBaseModel* InModel);

	virtual void RemoveFunctionalCupboardModel(UDSBaseModel* InModel);
	bool IsFunctionalCupboardModel() const;

	bool IsValidFunctionCupboardModel();

	virtual void OnAdaptiveAndAdSorptionCallback(bool bExtentsModified, bool bCenterModified, const FVector& OutExtents, const FVector& OutCenter);


	virtual void UpdateDWHParamters(const FVector& Size, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr = FDSBroadcastMarkData::BroadcastToMVCMark);

	virtual void UpdateLocationAndRotation(const FVector& Location, const FQuat& Rotation, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr = FDSBroadcastMarkData::BroadcastToMVCMark);

	virtual void UpdateLocation(const FVector& Location);

	virtual FVector GetLocation();

	void ResetFGInfo();

public:
	//依赖关系

	TSharedPtr<struct  FFunctionalDependencyMap> SubFunctionalNodeDependencyMap;

	const TSharedPtr<FFunctionalDependencyMap>& GetSubFunctionalNodeDependencyMap();


	void HandleChildrenFunctionalModelAdaptation();

	void UpdateAdaptationIntersectionTransform();

	void RemoveFunctionalDependency();

public:
	TSharedPtr<FModelAdaptationOperatorBase>& GetAdaptationOperator();

	TSharedPtr<FModelAdaptationOperatorBase>& CreateAdaptationOperator();

private:
	TSharedPtr<FModelAdaptationOperatorBase> AdaptationOperator;
};
