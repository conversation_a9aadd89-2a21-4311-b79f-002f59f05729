// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Subsystems/WorldSubsystem.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "DSConstructionPaper.h"
#include "DSConstructionFrame.h"
#include "Subsystems/UI/Widget/Common/ResourceItem/ResourceItem.h"
#include "DSConstructionSubsystem.generated.h"


//生成立面图时，可以在同一张立面图中显示的Model进行打组
struct FDSFrontGroupModels
{
	TArray<UDSCupboardModel*> Models;
	FVector WroldDir;
	FTransform WorldToProjective;
	FString GroupName;  //组名称
};

DECLARE_LOG_CATEGORY_EXTERN(LogConstruction, Log, All)

/**
 * 图纸子系统
 */
UCLASS()
class DESIGNSTATION_API UDSConstructionSubsystem : public UGameInstanceSubsystem
{
	GENERATED_BODY()
	

public:
	/** Implement this for initialization of instances of the system */
	virtual void Initialize(FSubsystemCollectionBase& Collection) override;

	/** Implement this for deinitialization of instances of the system */
	virtual void Deinitialize() override;
	
public:
	static UDSConstructionSubsystem* GetInstance(){ return Instance; }
	void ExportDxfFile(const FString& InFilePath);
	void ExportAllRoom();

	void DrawPrimitive(const TArray<TSharedPtr<FDSPrimitiveBase>> &InPrimitives,
		const FString& LayerName,const FString& InBlockName, FVector OffsetInCAD);
	void DrawFrame(const UDSConstructionFrame* InFrame, FVector OffsetInCAD);

	FVector GetDefaultFrameOuterSizeInCAD();


	void DrawDimension(const FVector& InStart, const FVector& InEnd, const FVector& OffsetInCAD, bool bIsHorizontal);

private:
	//virtual bool DoesSupportWorldType(const EWorldType::Type WorldType) const override;

	void LoadDxfTemplateFile(const FString& InFilePath);
	void LoadDxfFrameTemplateFile(const FString& InFilePath);
	void LoadDefaultDxfFrameTemplateFile();

	//bool BlockIsFrame(const FName& InBlockName);


	bool IsBaseCupboardType(UDSCupboardModel* InModel);   //是否是地柜类型
	bool IsWallCupboardType(UDSCupboardModel* InModel);   //是否是吊柜类型

	//生成一个柜子中所有子部件的图纸数据，包括这个柜子本身的图纸数据，放到数组开头。
	void GenRootCupboardConstructionData(UDSCupboardModel* InRootModel, UDSHouseAreaModel* InAreaModel, TArray<TSharedPtr<FDSConstructionData>>& OutConstructionDatas);
	void GenConstructionDataByChildNode(UDSCupboardModel* InModel, UDSHouseAreaModel* InAreaModel,
		TSharedPtr<FMultiComponentDataItem> InNode, TArray<FDSConstructionData*>& OutDatas);

	//生成一个区域的图纸数据
	void GenHomeConstructionData(UDSHouseAreaModel* InAreaModel, TArray<TSharedPtr<FDSConstructionData>>& OutConstructionDatas);

	TSharedPtr<FDSConstructionData> NewConstructionData(UDSBaseModel* InModel,const FString &ParentUUID, const FString InAreaUUID);

	bool OnCheckIsRightNode_Default(TSharedPtr<FMultiComponentDataItem> InNode);

	//生成立面图，对Area中Model进行打组
	void GenFrontGroupModel(TArray<UDSCupboardModel*> InModels, TArray<FDSFrontGroupModels>& OutGroups);

	//计算所有图纸的位置
	void UpdatePaperPos();

	void QueryResourecList(const TArray<TSharedPtr<FDSConstructionData>> &InConstructionDatas);

	UFUNCTION()
	void OnQueryResourceListByIdsCompletedCallback(const TArray<FDSResourceInfo>& ItemList);

	void ExportAllPapers();
private:
	UPROPERTY()
	TArray<UDSConstructionPaper*> ConstructionPapers; //图纸列表
	//TArray<TSharedPtr<FDSConstructionFrame>> ConstructionFrames; //图框列表

	static UDSConstructionSubsystem* Instance;

	
};
