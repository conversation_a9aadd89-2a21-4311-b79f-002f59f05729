#pragma once
#include "CoreMinimal.h"
//#include "GeometryAlgorithms/Public/Curve/PolygonIntersectionUtils.h"

struct FRectangle
{
	FVector BottomLeft;

	FVector BottomRight;

	FVector TopRight;

	FVector TopLeft;

	FRectangle() {};

	FRectangle(FVector bl, FVector br, FVector tr, FVector tl)
		: BottomLeft(bl), BottomRight(br), TopRight(tr), TopLeft(tl) {}
};

struct CLIPPER2_API FDSPolygon2DHasHole
{
	TArray<FVector2D> Outer;
	TArray<TArray<FVector2D>> Holes;
};

class CLIPPER2_API FClipper2Library
{
public:
	//create polygon by path
	static void CreatePolygonByPath(const FVector& InStart, const FVector& InEnd, TArray<FVector>& OutPolygon
	                                , const double& InOffset = 25.f);

	static void CreatePolygonByPaths(const TArray<TPair<FVector, FVector>>& InPaths, TArray<FVector>& OutPolygon
	                                 , const double& InOffset = 12.f);

	static bool PathAndPolygonIntersection(const FVector& Start, const FVector& End, const double& InOffset, const TArray<FVector>& InAreaOutline);

	static bool PolygonIntersection(const TArray<FVector>& InOutlineA, const TArray<FVector>& InOutlineB);

	static TArray<FVector> PolygonDifference(const TArray<FVector>& InOutlineA, const TArray<FVector>& InOutlineB);

	static bool PolygonDifference02(const TArray<TArray<FVector>>& InOutlineA, const TArray<TArray<FVector>>& InOutlineB
		, TArray<TArray<FVector>>& OutPaths, float* OutArea = nullptr, int32 InPrecision = 3);

	static TArray<FVector> PolygonUnion(const TArray<FVector>& InOutlineA, const TArray<FVector>& InOutlineB);

	static TArray<TArray<FVector>> PolygonUnion02(const TArray<TArray<FVector>>& InOutlineA,
		const TArray<TArray<FVector>>& InOutlineB, float InZ, float *OutArea = nullptr, int32 InPrecision = 3);

	static TArray<TArray<FVector2D>> PolygonUnion03(const TArray<TArray<FVector2D>>& InOutlineA,
		const TArray<TArray<FVector2D>>& InOutlineB, float* OutArea = nullptr, int32 InPrecision = 3);

	static void PolygonUnion04(const TArray<TArray<FVector2D>>& InOutlineA,
		const TArray<TArray<FVector2D>>& InOutlineB, TArray<FDSPolygon2DHasHole>& OutPaths, float* OutArea = nullptr, int32 InPrecision = 3);

	static void CreateEllipse(const FVector& InP, const double& InX, const double& InY, const int32& InStep, TArray<FVector>& OutPolygon);

	static void CreatePathsArea(const TArray<TArray<FVector>>& InPaths, TArray<TArray<FVector>>& OutPolygon);

	static void CreatePathsArea(const TArray<TPair<FVector, FVector>>& InSegments, TArray<TArray<FVector>>& OutPolygon);

	static void SplitSelfIntersection(const TArray<TPair<FVector, FVector>>& InSegments, TArray<TPair<FVector, FVector>>& OutSegments);

	static void CreatePathsAreaSelfIntersect(const TArray<TPair<FVector, FVector>>& InSegments, TArray<TArray<FVector>>& OutPolygon, TArray<TPair<FVector, FVector>>& OutEmptySegment);

	static double GetAreaSize(const TArray<FVector>& InArea);

	static double GetAreaSizeCM(const TArray<TArray<FVector>>& InArea);

	static FVector GetAreaCenter(const TArray<FVector>& InArea, const TArray<TArray<FVector>>& InHoles = TArray<TArray<FVector>>());

	static TArray<TPair<FVector, FVector>> GenerateSegments(const TArray<TArray<FVector>>& InPoint);

	static TArray<TArray<FVector>> ComputeOuterContour(const TArray<TArray<FVector2D>>& AllPaths, float Height);

	static TArray<TArray<FVector>> ComputeOuterContour(const TArray<TArray<FVector>>& AllPaths, float Height);

	static TArray<TArray<FVector>> ComputeIntersectionContour(const TArray<FVector2D>& SubjectContour, const TArray<FVector2D>& ClipContour);

	static TArray<TArray<FVector>> ComputeIntersectionContour(const TArray<FVector>& SubjectContour, const TArray<FVector>& ClipContour);

	static TArray<TArray<FVector>> ComputeDiffContour(const TArray<TArray<FVector>>& AllPaths, float Height);

	static TArray<TArray<FVector>> ComputeDiffContour(const TArray<TArray<FVector>>& SubjectContours, const TArray<TArray<FVector>>& ClipContours, float Height);

	static TArray<FVector> OffsetPolygon(const TArray<FVector>& PolygonVertices, double OffsetWidth);
};
