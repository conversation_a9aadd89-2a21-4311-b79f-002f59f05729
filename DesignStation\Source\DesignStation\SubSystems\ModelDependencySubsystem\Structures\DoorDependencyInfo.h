// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Json.h"
#include "SubSystems/MVC/Model/DSBaseModel.h"
#include "DoorDependencyInfo.generated.h"

USTRUCT(BlueprintType)
struct FDSSuitablePlane
{
	GENERATED_BODY()
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FVector Center;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FVector Normal;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FVector> Points;

	FDSSuitablePlane()
		: Center(FVector::ZeroVector)
		  , Normal(FVector::ZeroVector) {}

	void Serialize(TSharedRef<TJsonWriter<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>>& JsonWriter) const;
	void Deserialize(const TSharedPtr<FJsonObject>& InJsonData);
};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FDSDoorDependencyInfo
{
	GENERATED_BODY()
	/**
	 * The four panels that form the door area and the cabinet to which each panel belongs. (UUID means the 'UUID' field of FMultiComponentDataItem)
	 * Order of the boards is Up, Down, Left, Right.
	 * @Key		Panel's UUID.
	 * @Value	Cabinet's UUID.
	 */
	TArray<TPair<FString, FString>> DependentBoards;

	//上下左右选中板作为数组每个元素数组的第一个
	TArray<TArray<TPair<FString, FString>>> DependentGroupBoards;

	////门板内部覆盖的板件,不包含上下左右
	//TArray<FString> CoverBoards;

	// The area used by container, order of the point array is anticlockwise.
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FDSSuitablePlane DoorContainerPlane;

	// The area used by this door, order of the point array is anticlockwise.
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FDSSuitablePlane DoorPlane;

	UPROPERTY()
	TArray<FString> DependentCabinets;

	bool IsValid() const;

	void Serialize(TSharedRef<TJsonWriter<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>>& JsonWriter) const;
	void Deserialize(const TSharedPtr<FJsonObject>& InJsonData);
};
