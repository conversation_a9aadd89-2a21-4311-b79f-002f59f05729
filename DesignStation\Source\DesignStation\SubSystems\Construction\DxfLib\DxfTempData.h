﻿#pragma once

#include "pch.h"
#include <vector>
#include <map>
#include "src/dl_writer.h"
#include "GeometryCalculator.h"
//using namespace std;

class DL_Writer;
class DL_Attributes;

namespace DxfLib
{

	struct FDxfItemBase
	{
	public:
		FDxfItemBase() {};
		virtual ~FDxfItemBase() {};

		virtual void WriteToDxfFile(const DL_Writer& DW) const = 0;
		virtual void TryWriteValue(const DL_Writer& DW,const int Code, const std::string& Value, bool bWrite = true)const {
			if (bWrite)
			{
				DW.dxfString(Code, Value);
			}
		};
		virtual void TryWriteValue(const DL_Writer& DW,const int Code,const int Value, bool bWrite = true) const {
			if (bWrite)
			{
				DW.dxfInt(Code, Value);
			}
		};
		virtual void TryWriteValue(const DL_Writer& DW, const int Code, const double Value, bool bWrite = true)const {
			if (bWrite)
			{
				DW.dxfReal(Code, Value);
			}
		};
	};


	struct  FDxfHeaderItemBase :public FDxfItemBase
	{
	public:
		FDxfHeaderItemBase(const std::string& Name, const int& Code) :ItemName(Name), ItemCode(Code) {}
		virtual ~FDxfHeaderItemBase() {}
		std::string ItemName;
		int ItemCode;

		virtual void WriteToDxfFile(const DL_Writer& DW) const override {};
	};

	template<class T>
	struct TDxfHeaderItem:public FDxfHeaderItemBase
	{
	public:
		TDxfHeaderItem(const std::string& Name, const T& Value, const int& Code)
		:FDxfHeaderItemBase(Name,Code),ItemValue(Value){};
		virtual ~TDxfHeaderItem() {};


	public:
		T ItemValue;


		virtual void WriteToDxfFile(const DL_Writer& DW) const
		{
				DW.dxfString(9, ItemName);
				WriteValue(DW, ItemValue);
		};


		void WriteValue(const DL_Writer& DW, int Value) const
		{
				DW.dxfInt(ItemCode, Value);
		};

		void WriteValue(const DL_Writer& DW, const std::string& Value) const {
				DW.dxfString(ItemCode, Value);
		};

		void WriteValue(const DL_Writer& DW, const double& Value) const {
				DW.dxfReal(ItemCode, Value);
		};

		void WriteValue(const DL_Writer& DW, const FVector& Value) const
		{
			if (isnan(Value.Z))
			{
				DW.dxfReal(10, Value.X);
				DW.dxfReal(20, Value.Y);
			}
			else
			{
				DW.coord(10, Value.X, Value.Y, Value.Z);
			}
		};
	};
	
	using FDxfHeadStringItem = TDxfHeaderItem<std::string>;
	using FDxfHeadIntItem = TDxfHeaderItem<int>;
	using FDxfHeadDoubleItem = TDxfHeaderItem<double>;
	using FDxfHeadVectorItem = TDxfHeaderItem<FVector>;


	struct FDxfLineTypeData :public FDxfItemBase
	{
	public:
		FDxfLineTypeData(const std::string InName, const std::string InDes, const int& InFlags, const double& InPatternLength)
		:Flags(InFlags),PatternLength(InPatternLength),Name(InName),Description(InDes){};
		virtual ~FDxfLineTypeData() {};

		void SetPattern(const std::vector<double>& InPatterns) { Patterns = InPatterns; };
		void WriteToDxfFile(const DL_Writer& DW) const override;
	private:
		int Flags;
		double PatternLength;
		std::string Name;
		std::string Description;
		/** Patterns */
		std::vector<double> Patterns;


	};

	struct FDxfStyleData :public FDxfItemBase
	{
	public:
		FDxfStyleData(const std::string& InName,int InFlags, double InFixedTextHeight,double InWidthFactor,double InObliqueAngle,
			int InTextGenerationFlags,double InLastHeightUsed,const std::string& InPrimaryFontFile,const std::string& InBigFontFile)
		:bBold(false),bItalic(false),Flags(InFlags),TextGenerationFlags(InTextGenerationFlags),
			FixedTextHeight(InFixedTextHeight),WidthFactor(InWidthFactor),ObliqueAngle(InObliqueAngle),
		LastHeightUsed(InLastHeightUsed),Name(InName),PrimaryFontFile(InPrimaryFontFile),BigFontFile(InBigFontFile){};
		virtual ~FDxfStyleData() {};

		void WriteToDxfFile(const DL_Writer& DW) const;


		bool bBold;
		bool bItalic;
		/** Style flags */
		int Flags;
		/** Text generation flags */
		int TextGenerationFlags;
		/** Fixed text height or 0 for not fixed. */
		double FixedTextHeight;
		double WidthFactor;
		/** Oblique TextAngle */
		double ObliqueAngle;
		/** Last height used */
		double LastHeightUsed;
		/** Style name */
		std::string Name;
		/** Primary font file name */
		std::string PrimaryFontFile;
		/** Big font file name */
		std::string BigFontFile;
	};
	struct FDxfAppIDData:public FDxfItemBase
	{
	public:
		FDxfAppIDData(const std::string& InName, const int InFlags)
			:Name(InName), Flags(InFlags) {};
		virtual ~FDxfAppIDData() {};

		virtual void WriteToDxfFile(const DL_Writer& DW) const;

	public:
		std::string Name;
		int Flags;

	};

	struct  FDxfDimensionStyle:public FDxfItemBase
	{
	public:
		FDxfDimensionStyle(
			const std::string& InName,
			const std::string& InDIMPOST,
			const std::string& InDIMAPOST,
			int InFlags,
			double	InDIMSCALE,
			double	InDIMASZ,
			double	InDIMEXO,
			double	InDIMDLI,
			double	InDIMEXE,
			double	InDIMRND,
			double	InDIMDLE,
			double	InDIMTP,
			double	InDIMTM,
			double	InDIMTXT,
			double	InDIMCEN,
			double	InDIMTSZ,
			double	InDIMALTF,
			double	InDIMLFAC,
			double	InDIMTVP,
			double	InDIMTFAC,
			double	InDIMGAP,
			double	InDIMALTRN,
			int		InDIMTOL,
			int		InDIMLIM,
			int		InDIMTIH,
			int		InDIMTOH,
			int		InDIMSE1,
			int		InDIMSE2,
			int		InDIMTAD,
			int		InDIMZIN,
			int		InDIMAZIN,
			int		InDIMALT,
			int		InDIMALTD,
			int		InDIMTOFL,
			int		InDIMSAH,
			int		InDIMTIX,
			int		InDIMSOXD,
			int		InDIMCLRD,
			int		InDIMCLRE,
			int		InDIMCLRT,
			int		InDIMADEC,
			int		InDIMDEC,
			int		InDIMTDEC,
			int		InDIMALTU,
			int		InDIMALTTD,
			int		InDIMAUNIT,
			int		InDIMFRAC,
			int		InDIMLUNIT,
			int		InDIMDSEP,
			int		InDIMTMOVE,
			int		InDIMJUST,
			int		InDIMSD1,
			int		InDIMSD2,
			int		InDIMTOLJ,
			int		InDIMTZIN,
			int		InDIMALTZ,
			int		InDIMALTTZ,
			int		InDIMUPT,
			int		InDIMATFIT,
			const std::string& InStyleName
		) {
			Name = InName;
			DIMPOST = InDIMPOST;
			DIMAPOST = InDIMAPOST;
			Flags = InFlags;
			DIMSCALE = InDIMSCALE;
			DIMASZ = InDIMASZ;
			DIMEXO = InDIMEXO;
			DIMDLI = InDIMDLI;
			DIMEXE = InDIMEXE;
			DIMRND = InDIMRND;
			DIMDLE = InDIMDLE;
			DIMTP = InDIMTP;
			DIMTM = InDIMTM;
			DIMTXT = InDIMTXT;
			DIMCEN = InDIMCEN;
			DIMTSZ = InDIMTSZ;
			DIMALTF = InDIMALTF;
			DIMLFAC = InDIMLFAC;
			DIMTVP = InDIMTVP;
			DIMTFAC = InDIMTFAC;
			DIMGAP = InDIMGAP;
			DIMALTRND = InDIMALTRN;
			DIMTOL = InDIMTOL;
			DIMLIM = InDIMLIM;
			DIMTIH = InDIMTIH;
			DIMTOH = InDIMTOH;
			DIMSE1 = InDIMSE1;
			DIMSE2 = InDIMSE2;
			DIMTAD = InDIMTAD;
			DIMZIN = InDIMZIN;
			DIMAZIN = InDIMAZIN;
			DIMALT = InDIMALT;
			DIMALTD = InDIMALTD;
			DIMTOFL = InDIMTOFL;
			DIMSAH = InDIMSAH;
			DIMTIX = InDIMTIX;
			DIMSOXD = InDIMSOXD;
			DIMCLRD = InDIMCLRD;
			DIMCLRE = InDIMCLRE;
			DIMCLRT = InDIMCLRT;
			DIMADEC = InDIMADEC;
			DIMDEC = InDIMDEC;
			DIMTDEC = InDIMTDEC;
			DIMALTU = InDIMALTU;
			DIMALTTD = InDIMALTTD;
			DIMAUNIT = InDIMAUNIT;
			DIMFRAC = InDIMFRAC;
			DIMLUNIT = InDIMLUNIT;
			DIMDSEP = InDIMDSEP;
			DIMTMOVE = InDIMTMOVE;
			DIMJUST = InDIMJUST;
			DIMSD1 = InDIMSD1;
			DIMSD2 = InDIMSD2;
			DIMTOLJ = InDIMTOLJ;
			DIMTZIN = InDIMTZIN;
			DIMALTZ = InDIMALTZ;
			DIMALTTZ = InDIMALTTZ;
			DIMUPT = InDIMUPT;
			DIMATFIT = InDIMATFIT;
			StyleName = InStyleName;
		};

		virtual ~FDxfDimensionStyle() {};
		virtual void WriteToDxfFile(const DL_Writer& DW) const override;
		
	public:
		std::string	Name;
		std::string	DIMPOST;
		std::string	DIMAPOST;
		int		Flags;
		double	DIMSCALE;
		double	DIMASZ;
		double	DIMEXO;
		double	DIMDLI;
		double	DIMEXE;
		double	DIMRND;
		double	DIMDLE;
		double	DIMTP;
		double	DIMTM;
		double	DIMTXT;
		double	DIMCEN;
		double	DIMTSZ;
		double	DIMALTF;
		double	DIMLFAC;
		double	DIMTVP;
		double	DIMTFAC;
		double	DIMGAP;
		double	DIMALTRND;
		int		DIMTOL;
		int		DIMLIM;
		int		DIMTIH;
		int		DIMTOH;
		int		DIMSE1;
		int		DIMSE2;
		int		DIMTAD;
		int		DIMZIN;
		int		DIMAZIN;
		int		DIMALT;
		int		DIMALTD;
		int		DIMTOFL;
		int		DIMSAH;
		int		DIMTIX;
		int		DIMSOXD;
		int		DIMCLRD;
		int		DIMCLRE;
		int		DIMCLRT;
		int		DIMADEC;
		int		DIMDEC;
		int		DIMTDEC;
		int		DIMALTU;
		int		DIMALTTD;
		int		DIMAUNIT;
		int		DIMFRAC;
		int		DIMLUNIT;
		int		DIMDSEP;
		int		DIMTMOVE;
		int		DIMJUST;
		int		DIMSD1;
		int		DIMSD2;
		int		DIMTOLJ;
		int		DIMTZIN;
		int		DIMALTZ;
		int		DIMALTTZ;
		int		DIMUPT;
		int		DIMATFIT;
		std::string	StyleName;
	};


	struct  FDxfBaseEntityData :public FDxfItemBase
	{
	public:
		FDxfBaseEntityData():Color(0),Color24(-1),Width(0),LineTypeScale(1.0),Layer(""),LineType("BYLAYER") {};
		FDxfBaseEntityData(const DL_Attributes& Attributes);
		virtual ~FDxfBaseEntityData() {}

		int Color;
		int Color24;
		int Width;
		double LineTypeScale;
		std::string Layer;
		std::string LineType;


		virtual void WriteToDxfFile(const DL_Writer& DW)const override{};
	protected:
		void WriteBaseData(const DL_Writer& DW)const ;
	};


	struct FDxfLayer:public FDxfBaseEntityData
	{
	public:
		FDxfLayer(const std::string& InName,const int& InFlags,bool bInOff ,const DL_Attributes& Attributes)
		:FDxfBaseEntityData(Attributes), bOff(bInOff),Flags(InFlags),Name(InName){};
		virtual ~FDxfLayer() {};

		bool bOff;
		int Flags;
		std::string Name;
		virtual void WriteToDxfFile(const DL_Writer& DW) const override;
	};

	struct FDxfPointData:public FDxfBaseEntityData
	{
	public:
		FDxfPointData(const FVector& InPoint,const DL_Attributes& Attributes)
			:FDxfBaseEntityData(Attributes),Point(InPoint) {};
		virtual ~FDxfPointData() {};

		FVector Point;
		virtual void WriteToDxfFile(const DL_Writer& DW) const override;
	};

	struct  FDxfLineData:public FDxfBaseEntityData
	{
	public:
		FDxfLineData(const FVector& InStartPoint,const FVector& InEndPoint, const DL_Attributes& Attributes)
			:FDxfBaseEntityData(Attributes), StartPoint(InStartPoint),EndPoint(InEndPoint){};
		FVector StartPoint;
		FVector EndPoint;
		virtual void WriteToDxfFile(const DL_Writer& DW) const override;
		
	};

	struct  FDxfXLineData:public FDxfBaseEntityData
	{
	public:
		FDxfXLineData(const FVector& InPoint, const FVector& InDir, const DL_Attributes& Attributes)
			:FDxfBaseEntityData(Attributes), Point(InPoint), Dir(InDir) {};
		FVector Point;
		FVector Dir;

		virtual void WriteToDxfFile(const DL_Writer& DW) const override;
	};

	struct  FDxfRayData :public FDxfBaseEntityData
	{
	public:
		FDxfRayData(const FVector& InPoint, const FVector& InDir, const DL_Attributes& Attributes)
			:FDxfBaseEntityData(Attributes), Point(InPoint), Dir(InDir) {};
		
		FVector Point;
		FVector Dir;

		virtual void WriteToDxfFile(const DL_Writer& DW) const override;
	};
		
	struct  FDxfArcData :public FDxfBaseEntityData
	{
	public:
		FDxfArcData(const FVector& InCenter,const double& InRadius,const double& InStartAngle,
			const double& InEndAngle, const DL_Attributes& Attributes)
			:FDxfBaseEntityData(Attributes), Center(InCenter),Radius(InRadius),StartAngle(InStartAngle),EndAngle(InEndAngle) {};
		FVector Center;
		double Radius;
		double StartAngle;
		double EndAngle;

		virtual void WriteToDxfFile(const DL_Writer& DW) const override ;
	};

	struct FDxfCircleData :public FDxfBaseEntityData
	{
	public:
		FDxfCircleData(const FVector& InCenter, const double& InRadius, const DL_Attributes& Attributes)
			:FDxfBaseEntityData(Attributes), Center(InCenter), Radius(InRadius) {};
		FVector Center;
		double Radius;

		virtual void WriteToDxfFile(const DL_Writer& DW) const override;
	};

	struct  FDxfPolyLine:public FDxfBaseEntityData
	{
		/*! Number of vertices in this polyline. */
		uint num;
		
		/*! Number of vertices in m direction if polyline is a polygon mesh. */
		uint m;
		/*! Number of vertices in n direction if polyline is a polygon mesh. */
		uint n;

		/*! elevation of the polyline. */
		double Elevation;

		/*! Flags */
		int Flags;

		virtual void WriteToDxfFile(const DL_Writer& DW) const override {};
	};

	struct  FDxfVertexData : public FDxfBaseEntityData
	{
		FVector Point;
		double Bulge;

		virtual void WriteToDxfFile(const DL_Writer& DW) const override {};
	};

	struct  FDxfLWPolyLineData :public FDxfBaseEntityData
	{
	public:
		FDxfLWPolyLineData(const uint& InVertexNum, const int& InFlags, const DL_Attributes& InAttributes);
			virtual ~FDxfLWPolyLineData() {
				Points.Empty();
		};

		void AddPoint(const FVector& InPoint);
		void SetPoints(const TArray<FVector>& InPoints);
	private:
		uint VertexNum;
		//1封闭多线段
		int Flags;
		TArray<FVector> Points;

		virtual void WriteToDxfFile(const DL_Writer& DW) const override;
	};


	struct  FDxfTextData :public FDxfBaseEntityData
	{

	public:
		FDxfTextData(const FVector& InInsertPoint, const FVector& InAlignmentPoint,
			const double& InHeight, const double& InScaleFator, const int& InTextGenerationFlags,
			const int& InhJustificaiton, const int& InvJustification, const double& InAngle,
			const std::string& InText, const std::string& InStyle,const DL_Attributes& Attributes)
			:FDxfBaseEntityData(Attributes), InsertPoint(InInsertPoint), AlignmentPoint(InAlignmentPoint), Height(InHeight),
			XScaleFactor(InScaleFator), TextGenerationFlags(InTextGenerationFlags),
			hJustification(InhJustificaiton), vJustification(InvJustification),
			TextAngle(InAngle), Text(InText), Style(InStyle) {};
		FVector InsertPoint;

		FVector AlignmentPoint;

		double Height;

		double XScaleFactor;
		/*! 0 = default, 2 = Backwards, 4 = Upside down */
		int TextGenerationFlags;
		/**
		 * Horizontal justification.
		 *
		 * 0 = Left (default), 1 = Center, 2 = Right,
		 * 3 = Aligned, 4 = Middle, 5 = Fit
		 * For 3, 4, 5 the vertical alignment has to be 0.
		 */
		int hJustification;
		/**
		 * Vertical justification.
		 *
		 * 0 = Baseline (default), 1 = Bottom, 2 = Middle, 3= Top
		 */
		int vJustification;

		/*! Rotation TextAngle of dimension text away from default orientation. */
		double TextAngle;

		/*! Text std::string. */
		std::string Text;
		/*! Style (font). */
		std::string Style;


		virtual void WriteToDxfFile(const DL_Writer& DW) const override;
	};


	struct  FDxfMTextData:public FDxfBaseEntityData
	{
	public:
		FDxfMTextData(const FVector& InInsertPoint, const FVector& InDir, double InHeight,
			double InBoxWidth,int InAttachmentPoint,int InDrawingDir,int InLineSpacingStyle,
			double InLineSpacingFactor,const std::string& InText,const std::string& InStyle,
			double InTextAngle, const DL_Attributes& Attributes)
		:FDxfBaseEntityData(Attributes), InsertPoint(InInsertPoint), Dir(InDir), Height(InHeight), BoxWidth(InBoxWidth), AttachmentPoint(InAttachmentPoint),
			DrawingDir(InDrawingDir), LineSpacingStyle(InLineSpacingStyle), LineSpacingFactor(InLineSpacingFactor),
			Text(InText), Style(InStyle), TextAngle(InTextAngle){};

		virtual ~FDxfMTextData() {};
		virtual void WriteToDxfFile(const DL_Writer& DW) const override;
	public:
		FVector InsertPoint;
		FVector Dir;
		double Height;
		
		double BoxWidth;
		int AttachmentPoint;
		int DrawingDir;
		int LineSpacingStyle;
		double LineSpacingFactor;
		std::string Text;
		std::string Style;
		double TextAngle;
	};
	//填充边界数据
	struct  FDxfHatchEdgeData:public FDxfItemBase
	{
	public:
		FDxfHatchEdgeData(int InPathTypeFlags,int InPathType = -1) :PathTypeFlags(InPathTypeFlags),PathType(InPathType){};
		virtual ~FDxfHatchEdgeData() {};
	public:
		virtual bool IsValid() const= 0;
		int PathTypeFlags;
		int PathType;
	
	};
	struct  FDxfHatchPolyLineEdgeData:public FDxfHatchEdgeData
	{
	public:
		FDxfHatchPolyLineEdgeData():FDxfHatchEdgeData(7,0), bConvexity(false), bClose(false) {};
		virtual ~FDxfHatchPolyLineEdgeData() {};

		virtual void WriteToDxfFile(const DL_Writer& DW) const override;

		virtual bool IsValid()const override;
		void AddPoints(const FVector& InPoint);

	public:

		bool bConvexity;
		bool bClose;
		// X:X,Y:Y,Z:
		std::vector<FVector> Points;

	};
	struct  FDxfHatchPatternData
	{
	public:
		FDxfHatchPatternData() :FDxfHatchPatternData(false, 0.0, 1, "SOLID", FVector(0)) {};

		FDxfHatchPatternData(bool InbSolid,double InAngle,double InScale,const std::string& InName,const FVector& InOrigin) 
		:bSolid(InbSolid),Angle(InAngle),Scale(InScale),Name(InName),Origin(InOrigin){};
	public :
		bool bSolid;
		double Angle;
		double Scale;
		std::string Name;
		FVector Origin;
	};

	struct  FDxfHatchData:public FDxfBaseEntityData
	{
	public:
		FDxfHatchData(const FDxfHatchPatternData& InPattern, const DL_Attributes& Attributes):
		FDxfBaseEntityData(Attributes), HatchPattern(InPattern){};
		virtual ~FDxfHatchData() {
			for (size_t i = 0; i < Edges.size(); i++)
			{
				if (Edges[i])
				{
					delete Edges[i];
					Edges[i] = nullptr;
				}
			}
		};
		virtual void WriteToDxfFile(const DL_Writer& DW) const override;

		void AddEdge(FDxfHatchEdgeData* pEdge);
	public:

		FDxfHatchPatternData HatchPattern;

		std::vector<FDxfHatchEdgeData*> Edges;
	};


	struct FDxfAttDefData:public FDxfTextData
	{
	public:
		FDxfAttDefData(const FVector& InInsertPoint, const FVector& InAlignmentPoint,
			const double& InHeight, const double& InScaleFator, const int& InTextGenerationFlags,
			const int& InhJustificaiton, const int& InvJustification, const double& InAngle,
			const std::string& InText, const std::string& InStyle,const std::string& InTag, const DL_Attributes& Attributes) :
			FDxfTextData(InInsertPoint, InAlignmentPoint, InHeight, InScaleFator,
				InTextGenerationFlags, InhJustificaiton, InvJustification,
				InAngle, InText, InStyle,Attributes) ,Tag(InTag){};
		virtual ~FDxfAttDefData() {};

		std::string Tag;


		virtual void WriteToDxfFile(const DL_Writer& DW) const override;
	};


	struct FDxfBlockData: public FDxfBaseEntityData
	{
	public:
		FDxfBlockData(const std::string& InName,const FVector& InPoint,const DL_Attributes& InAttributes,const std::string& InSourceName)
		:FDxfBaseEntityData(InAttributes), SourceName(InSourceName), Name(InName), Point(InPoint) {};
		virtual ~FDxfBlockData() {
			for (size_t i = 0; i < ChildrenEntities.size(); i++)
			{
				if (ChildrenEntities[i])
				{
					delete ChildrenEntities[i];
					ChildrenEntities[i] = nullptr;
				}
			}
			for (auto item = Attdefs.begin(); item != Attdefs.end(); item++)
			{
				if (item->second)
				{
					delete item->second;
					item->second = nullptr;
				}
			}
		
		};

	public:
		void AddChildEntity(FDxfBaseEntityData* InEntity);
		void AddAttDef(const FDxfAttDefData* AttDef);
		virtual void WriteToDxfFile(const DL_Writer& DW) const override;
		
		void GetShowerName(std::string& ShowerName)const;

		const FDxfAttDefData*  GetAttDefByTag(const std::string& InTag)const;
		//unique
		std::string SourceName;
		std::string Name;
		FVector Point;
		std::vector<FDxfBaseEntityData*> ChildrenEntities;
		std::map<std::string, const FDxfAttDefData*> Attdefs;
	};

	struct  FDxfAttrib
	{
		
	public:
		FDxfAttrib() {};
		FDxfAttrib(const std::string& InTag, const std::string& InText)
			:Tag(InTag), Text(InText) {};
		std::string Tag;
		std::string Text;
	};

	struct  FDxfInsertAttrib:public FDxfTextData
	{
	public:
		FDxfInsertAttrib(const FVector& InInsertPoint, const FVector& InAlignmentPoint,
			const double& InHeight, const double& InScaleFator, const int& InTextGenerationFlags,
			const int& InhJustificaiton, const int& InvJustification, const double& InAngle,
			const std::string& InText, const std::string& InStyle, const std::string& InTag, const DL_Attributes& Attributes) :
			FDxfTextData(InInsertPoint, InAlignmentPoint, InHeight, InScaleFator,
				InTextGenerationFlags, InhJustificaiton, InvJustification,
				InAngle, InText, InStyle,Attributes), Tag(InTag) {};
		virtual ~FDxfInsertAttrib() {};

		std::string Tag;
		virtual void WriteToDxfFile(const DL_Writer& DW) const override;
	};

	struct  FDxfInsertData:public FDxfBaseEntityData
	{
	public:
		FDxfInsertData(const std::string& InName,const FVector& InPoint,const FVector& InScale,double InAngle,const std::string& InSourceName,const DL_Attributes& InAttributes)
		: FDxfBaseEntityData(InAttributes), TextAngle(InAngle), Point(InPoint), Scale(InScale), Name(InName), SourceName(InSourceName) {};
		virtual ~FDxfInsertData() {};
		double TextAngle;
		FVector Point;
		FVector Scale;
		std::string Name;
		std::string SourceName;
		std::map<std::string, FDxfAttrib> Attribs;

		virtual void WriteToDxfFile(const DL_Writer& DW) const override;
		void AddAttrib(const FDxfAttrib& InAttrib);
		void AddAttrib(const std::string& InTag, const std::string& InText);

		static FVector GetRotatePosition(const FVector& Position, double Angle, const FVector& SrcPosition)
		{
			double Radians = Angle * M_PI / 180;
			double X = (Position.X - SrcPosition.X) * std::cos(Radians) - (Position.Y - SrcPosition.Y) * std::sin(Radians) + SrcPosition.X;
			double Y = (Position.X - SrcPosition.X) * sin(Radians) + (Position.Y - SrcPosition.Y) * cos(Radians) + SrcPosition.Y;

			return FVector(X, Y,0.0f);
		}
	};


#pragma region  Dimension
	struct FDxfDimensionBase 
	{
	public:
		FDxfDimensionBase(int InType) :DefinitionPoint(FVector::ZeroVector), TextPoint(FVector::ZeroVector), Type(InType),
			AttachmentPoint(5), LineSpacingStyle(1), LineSpacingFactor(1.0),
			Text(""), Style("ISO-25"), TextAngle(0.0), LinearFactor(0), DimScale(1) {
		};


		FDxfDimensionBase(const FVector& InDefinitionPoint,const FVector& InTextPoint,
			int InType,int InAttachmentPoint,int InLineSpacingStyle,
			double InLineSpacingFactor,const std::string& InText,const std::string& InStyle,
			double InTextAngle,double InLinearFactor =-1,double InDimScale = -1) 
			:DefinitionPoint(InDefinitionPoint),TextPoint(InTextPoint),Type(InType),
			AttachmentPoint(InAttachmentPoint),LineSpacingStyle(InLineSpacingStyle),
			LineSpacingFactor(InLineSpacingFactor),Text(InText),Style(InStyle),
			TextAngle(InTextAngle),LinearFactor(InLinearFactor),DimScale(InDimScale){};

		FDxfDimensionBase(const FDxfDimensionBase& InBase)
		:DefinitionPoint(InBase.DefinitionPoint), TextPoint(InBase.TextPoint), Type(InBase.Type),
			AttachmentPoint(InBase.AttachmentPoint), LineSpacingStyle(InBase.LineSpacingStyle),
			LineSpacingFactor(InBase.LineSpacingFactor), Text(InBase.Text), Style(InBase.Style),
			TextAngle(InBase.TextAngle), LinearFactor(InBase.LinearFactor), DimScale(InBase.DimScale){};
		~FDxfDimensionBase() {};


		void WriteDimStyleOverrides(const DL_Writer& DW)const;
		void WriteDimBaseData(const DL_Writer& DW)const;
	public:

		FVector DefinitionPoint;
		FVector TextPoint;
		/*
		*Dimension type.
		*
		* 0   Rotated, horizontal, or vertical
		* 1   Aligned
		* 2   Angular
		* 3   Diametric
		* 4   Radius
		* 5   Angular 3 - point
		* 6   Ordinate
		* 64  Ordinate type.This is a bit value(bit 7)
		* used only with integer value 6. If set,
		* ordinate is X - type; if not set, ordinate is
		* Y - type
		* 128 This is a bit value(bit 8) added to the
		* other group 70 values if the dimension text
		* has been positioned at a user - defined
		* location rather than at the default location
		*/
		int Type;
		/**
		 * Attachment point.
		 *
		 * 1 = Top left, 2 = Top center, 3 = Top right,
		 * 4 = Middle left, 5 = Middle center, 6 = Middle right,
		 * 7 = Bottom left, 8 = Bottom center, 9 = Bottom right,
		 */
		int AttachmentPoint;
		/**
		 * Line spacing style.
		 *
		 * 1 = at least, 2 = exact
		 */
		int LineSpacingStyle;
		/**
		 * Line spacing factor. 0.25 .. 4.0
		 */
		double LineSpacingFactor;
		/**
		 * Text std::string.
		 *
		 * Text std::string entered explicitly by user or null
		 * or "<>" for the actual measurement or " " (one blank space).
		 * for supressing the text.
		 */
		std::string Text;
		/*! Dimension style (font name). */
		std::string Style;
		/**
		 * Rotation angle of dimension text away from
		 * default orientation.
		 */
		double TextAngle;
		/**
		 * Linear factor style override.
		 */
		double LinearFactor;
		/**
		 * Dimension scale (dimscale) style override.
		 */
		double DimScale;
	};

	struct FDxfDimAlignedData:public FDxfBaseEntityData
	{
	public:
		FDxfDimAlignedData(const FVector& InExtenPoint1,const FVector& InExtenPoint2,const FDxfDimensionBase& InDimBase,const DL_Attributes& InAttributes)
		: FDxfBaseEntityData(InAttributes), DimensionBaseData(InDimBase), ExtenPoint1(InExtenPoint1), ExtenPoint2(InExtenPoint2) {};
		virtual ~FDxfDimAlignedData() {};
		virtual void WriteToDxfFile(const DL_Writer& DW) const override;
		FDxfDimensionBase DimensionBaseData;
		FVector ExtenPoint1;
		FVector ExtenPoint2;
	};
	struct  FDxfDimLinearData:public FDxfDimAlignedData
	{

	public:
		FDxfDimLinearData(const FVector& InExtenPoint1, const FVector& InExtenPoint2,
			double InAngle,double InOblique,const FDxfDimensionBase& InDimBase, const DL_Attributes& InAttributes)
		:FDxfDimAlignedData(InExtenPoint1,InExtenPoint2, InDimBase,InAttributes),Angle(InAngle),Oblique(InOblique){};
		virtual ~FDxfDimLinearData() {};
		virtual void WriteToDxfFile(const DL_Writer& DW) const override;
		double Angle;
		double Oblique;
	};


	struct FDxfDimRadialData:public FDxfBaseEntityData
	{
	public:
		FDxfDimRadialData(const FVector& InCirclePoint, double InLeader,
			const FDxfDimensionBase& InDimBase, const DL_Attributes& InAttributes) :
		FDxfBaseEntityData(InAttributes), DimensionBaseData(InDimBase), CirclePoint(InCirclePoint), Leader(InLeader) {};
		virtual ~FDxfDimRadialData() {};
		virtual void WriteToDxfFile(const DL_Writer& DW) const override;
	public:
		FDxfDimensionBase DimensionBaseData;
		FVector CirclePoint;
		double Leader;
	};

	struct  FDxfDimDiametricData:public FDxfDimRadialData
	{
	public:
		FDxfDimDiametricData(const FVector& InCirclePoint, double InLeader,
			const FDxfDimensionBase& InDimBase, const DL_Attributes& InAttributes) :
			FDxfDimRadialData(InCirclePoint,InLeader,InDimBase,InAttributes){};
		virtual ~FDxfDimDiametricData() {};
		virtual void WriteToDxfFile(const DL_Writer& DW) const override;

	};

	struct  FDxfDimAngular2LineData:public FDxfBaseEntityData
	{
	public:
		FDxfDimAngular2LineData(const FVector& InLinePoint1, const FVector& InLinePoint2, const FVector& InLinePoint3,
			const FVector& InLinePoint4, const FDxfDimensionBase& InDimBase, const DL_Attributes& InAttributes)
			: FDxfBaseEntityData(InAttributes), DimensionBaseData(InDimBase), LinePoint1(InLinePoint1), LinePoint2(InLinePoint2), LinePoint3(InLinePoint3), LinePoint4(InLinePoint4) {};
		virtual ~FDxfDimAngular2LineData() {};
		virtual void WriteToDxfFile(const DL_Writer& DW) const override;
	public:
		FDxfDimensionBase DimensionBaseData;
		FVector LinePoint1;
		FVector LinePoint2;
		FVector LinePoint3;
		FVector LinePoint4;
	};


	struct FDxfDimAngular3PointData:public FDxfBaseEntityData
	{
	public:
		FDxfDimAngular3PointData(const FVector& InPoint1, const FVector& InPoint2, const FVector& InPoint3,
			const FDxfDimensionBase& InDimBase,const DL_Attributes& InAttributes) 
		:FDxfBaseEntityData(InAttributes),DimensionBaseData(InDimBase), Point1(InPoint1), Point2(InPoint2), Point3(InPoint3){};
		virtual ~FDxfDimAngular3PointData() {};
		virtual void WriteToDxfFile(const DL_Writer& DW) const override;
	public:
		FDxfDimensionBase DimensionBaseData;
		FVector Point1;
		FVector Point2;
		FVector Point3;
	};

	struct  FDxfDimOrdinateData:public FDxfBaseEntityData
	{
	public:
		FDxfDimOrdinateData(const FVector& InStartPoint, const FVector& InEndPoint, bool InbXtype,
			const FDxfDimensionBase& InDimBase,const DL_Attributes& InAttributes)
		:FDxfBaseEntityData(InAttributes), DimensionBaseData(InDimBase), StartPoint(InStartPoint), EndPoint(InEndPoint), bXType(InbXtype) {};
		virtual ~FDxfDimOrdinateData() {};
		virtual void WriteToDxfFile(const DL_Writer& DW) const override;
	public:
		FDxfDimensionBase DimensionBaseData;
		FVector StartPoint;
		FVector EndPoint;
		bool bXType;
	};
#pragma endregion




	struct FDxfHeader
	{
	public:
		FDxfHeader() {
		};
		~FDxfHeader() {
			for (size_t i = 0; i < HeaderItems.size(); i++)
			{
				if (HeaderItems[i])
				{
					delete HeaderItems[i];
					HeaderItems[i] = nullptr;
				}
			}
		};
		std::vector<FDxfHeaderItemBase*>   HeaderItems;

		void AddHeaderItem(FDxfHeaderItemBase* pHeaderItem);


		void WriteToDxfFile(const DL_Writer& DW)const;
	};

	
	struct  FBlockSource
	{
	public:
		FBlockSource() {};
		~FBlockSource() {
			/*
			for (auto item = Blocks.begin(); item != Blocks.end(); item++)
			{
				if (item->second)
				{
					delete item->second;
					item->second = nullptr;
				}
			}
			*/

			for (auto& Ite : Blocks)
			{
				if (Ite.Value != nullptr)
				{
					delete Ite.Value;
					Ite.Value = nullptr;
				}
			}
		};
		void AddBlock(FDxfBlockData* pBlockData);
		void WriteToDxfFile(const DL_Writer& DW) const;
		const FDxfBlockData* GetBlock(const FString& BlockName) const;
		const TMap<FString, FDxfBlockData*>& GetBlocks() const { return Blocks; }
		void Clear() { Blocks.Empty(); }
	public:
		TMap<FString, FDxfBlockData*> Blocks;
	};

	struct  FDxfTables
	{
	public:
		FDxfTables() {};
		~FDxfTables() {
			for (size_t i = 0; i < LineTypeDatas.size(); i++)
			{
				if (LineTypeDatas[i])
				{
					delete LineTypeDatas[i];
					LineTypeDatas[i] = nullptr;
				}
			}
			for (size_t i = 0; i < Layers.size(); i++)
			{
				if (Layers[i])
				{
					delete Layers[i];
					Layers[i] = nullptr;
				}
			}
			for (size_t i = 0; i < Styles.size(); i++)
			{
				if (Styles[i])
				{
					delete Styles[i];
					Styles[i] = nullptr;
				}
			}
			for (size_t i = 0; i < DimStyles.size(); i++)
			{
				if (DimStyles[i])
				{
					delete DimStyles[i];
					DimStyles[i] = nullptr;
				}
			}
			for (size_t i = 0; i < AppIds.size(); i++)
			{
				if (AppIds[i])
				{
					delete AppIds[i];
					AppIds[i] = nullptr;
				}
			}
		};


		std::vector<FDxfLineTypeData*> LineTypeDatas;
		std::vector<FDxfLayer*> Layers;
		std::vector<FDxfStyleData*> Styles;
		std::vector<FDxfDimensionStyle*> DimStyles;
		std::vector<FDxfAppIDData*> AppIds;

		void AddLineType(FDxfLineTypeData* pLineTypeData);
		void AddLayer(FDxfLayer* pLayer);

		void AddStyle(FDxfStyleData* pStyle);
		void AddAppID(FDxfAppIDData* pAppID);
		void AddDimendionStyle(FDxfDimensionStyle* pDimStyle);
		void WriteToDxfFile(const DL_Writer& DW)const;

		void WriteToDxfFile(const DL_Writer& DW, const FBlockSource* DefaultBlocks)const;
	private:
		void WriteSampleUCS(const DL_Writer& DW) const;

		void WriteSampleVport(const DL_Writer& DW)const;
		void WriteSampleView(const DL_Writer& DW)const;

		void WriteSampleBlockRecord(const DL_Writer& DW,bool bWriterEnd =true) const;
		
		void WriteAppIDs(const DL_Writer& DW)const;
		void WriteDimStyle(const DL_Writer& DW)const;
	};


	struct  FDxfBlocks
	{
	public:
		FDxfBlocks() {};
		virtual ~FDxfBlocks() {
			/*
			for (auto item = BlockMap.begin();item != BlockMap.end();item++)
			{
				if (item->second)
				{
					delete item->second;
					item->second = nullptr;
				}
			}
			*/

			for (auto& Ite : BlockMap)
			{
				if (Ite.Value != nullptr)
				{
					delete Ite.Value;
					Ite.Value = nullptr;
				}
			}
		};
		TMap<FString, FBlockSource*> BlockMap;
		void AddBlock(FDxfBlockData* pBlockData);

		virtual void WriteToDxfFile(const DL_Writer& DW) const;

		//const FDxfBlockData* GetBlock(const std::string& BlockName,const std::string& SourceName) const ;

		const FDxfBlockData* GetBlock(const FString& BlockName, const FString& SourceName) const;

		const FBlockSource* GetBlockSource(const FString& SourceName)const;
	};

	class FDxfTempData
	{
	public:
		

		static FDxfTempData* GetInstance();

		static void CreateInstance();
		static void DeleteInstance();

	private:
		FDxfTempData() {};

		~FDxfTempData();


		FDxfTempData(const FDxfTempData& DxfTempData);

		const FDxfTempData& operator=(const FDxfTempData& DxfTempData);


	private :
		
		static FDxfTempData* pDxfTempData;


	private:
		TSharedPtr<FDxfHeader> DxfHeader;

		TSharedPtr<FDxfTables> DxfTables;

		TSharedPtr<FDxfBlocks> DxfBlocks;

		TArray<TSharedPtr<FDxfBaseEntityData>> EntityDatas;

	public:

		TSharedPtr<FDxfHeader> GetDxfHeader();
		 
		TSharedPtr<FDxfTables> GetDxfTables();

		TSharedPtr<FDxfBlocks> GetDxfBlocks();

		const TArray< TSharedPtr<FDxfBaseEntityData>>& GetEntityDatas() { return EntityDatas; }

		 void Clear();

	public:
		//const FDxfBlockData* GetBlockByName(const std::string& BlockName,const std::string& SourceName)const;
		const FDxfBlockData* GetBlockByName(const FName& InBlockName, const FString& SourceName)const;
		void AddBlock(FDxfBlockData* InBlock);
		void AddEntity(TSharedPtr<FDxfBaseEntityData> InEntity);
	};


}