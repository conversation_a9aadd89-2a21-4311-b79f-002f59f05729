// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "OrientedBoxTypes.h"
#include "MeshAABBTree3.h"
#include "DynamicMesh/DynamicMeshAABBTree3.h"
#include "GridBoxMeshGenerator.h"
#include "SubSystems/AdaptiveAdsorption/Data/AdaptationDynamicData.h"

#ifndef ADAPTIVE_TOLERANCE
#define ADAPTIVE_TOLERANCE  0.1f//UE_DOUBLE_KINDA_SMALL_NUMBER
#endif // !ADAPTIVE_TOLERANCE

#ifndef ADAPTIVE_TOLERANCE_TWO_DIGITAL
#define ADAPTIVE_TOLERANCE_TWO_DIGITAL  0.01f
#endif // !ADAPTIVE_UNIT_SQUARE


#include "Parameter/ParameterDataDefine.h"


#ifndef ADAPATIVE_UNIT
#define ADAPATIVE_UNIT  0.01f
#endif // !ADAPATIVE_UNIT

DECLARE_LOG_CATEGORY_EXTERN(AdaptationExecuterLog, Log, All);

using namespace UE::Geometry;
namespace UE
{
	namespace Geometry {
		class FGridBoxMeshGenerator;
		class FDynamicMesh3;
	}
}
typedef TFunction<void(bool bExtentsModified, bool bCenterModified, const FVector& OutExtents, const FVector& OutCenter)> TAdaptiveAndAdsorptionHandle;

typedef TFunction<void(const TSharedPtr<FAdaptationData>& AdaptaionData)> TAdaptationHandle;

typedef TFunction<void(TMap<FString, FParameterData>&)>TRefreshParameterDataHandle;

class DESIGNSTATION_API FIntersectionDynamicMesh
{
public:
	FIntersectionDynamicMesh();

	FIntersectionDynamicMesh(const FVector& InExtent, const FVector& InCenter, const FQuat& InRotation,EIntersectionDataType InType = EIntersectionDataType::E_Unknown);


	virtual ~FIntersectionDynamicMesh();
	FIntersectionDynamicMesh(const FIntersectionDynamicMesh& In) {
		this->BoxGen = In.BoxGen;
		this->SourceMesh.Copy(&BoxGen);
		this->DynamicMeshTree.SetMesh(&SourceMesh);
	};

public:
	virtual void Initialized(const FVector& InExtent, const FVector& InCenter, const FQuat& InRotation);

	virtual void UpdateExtentsAndLocation(const FVector& InExtent, const FVector& InCenter);

	virtual void UpdateExtentsAndLocation(const FVector& InExtent, const FVector& InCenter, bool& OutExtentsModified, bool& OutCenterModified);
	virtual bool UpdateLocation(const FVector& InCenter);

	virtual bool UpdateRotation(const FQuat& InRotation);

	virtual bool UpdateTrans(const FVector& InCenter, const FQuat& InRotation);

	virtual bool UpdateOribox(const FOrientedBox3d& InOriBox);

	virtual bool UpdateOribox(const FVector& InCenter, const FQuaterniond& InRotation, const FVector& InExtent);

public:
	bool RayHit(const FRay& Ray, const IMeshSpatial::FQueryOptions& Options,int& OutTriID, double& OutDistance);

public:

	void SetOwnerUUID(const FString& InOwnerUUID) { LinkDSModelInfo.UUID = InOwnerUUID; };
	
	void SetDebugName(const FString& InDebugName) { LinkDSModelInfo.Name = InDebugName; };

	void SetLinkModelBaseInfo(const FAdaptationLinkModelInfo& InDSModelBaseInfo) { LinkDSModelInfo = InDSModelBaseInfo; };
	void SetLinkModelBaseInfo(const FString& InUUID, const FString& InName, int32 InType,const FString& InComponentUUID ="") { LinkDSModelInfo.Update(InUUID, InName, InType, InComponentUUID); };
	FAdaptationLinkModelInfo GetLinkModelBaseInfo() const { return LinkDSModelInfo; };

	FAdaptationLinkModelInfo& GetLinkModelBaseInfoRef() { return LinkDSModelInfo; }

public:
	const FDynamicMeshAABBTree3& GetDynamicMeshTree() const;

	FAxisAlignedBox3d  GetBounds() const;

	const FOrientedBox3d& GetOriBox()const { return BoxGen.Box; };

	const FFrame3d& GetSelfFrame()const { return BoxGen.Box.Frame; };

	EIntersectionDataType GetIntersectionType()const { return IntersectionType; };

	const FString& GetDebugName()const { return LinkDSModelInfo.Name; };
	const FString& GetOwnerUUID()const { return LinkDSModelInfo.UUID; };
	const FString& GetComponentUUID()const { return LinkDSModelInfo.ComponentUUID; };
private:
	UE::Geometry::FGridBoxMeshGenerator BoxGen;
	UE::Geometry::FDynamicMesh3 SourceMesh;
	UE::Geometry::FDynamicMeshAABBTree3 DynamicMeshTree;

	EIntersectionDataType IntersectionType;
	
	//link model base data, convinent to find model by UUID and type
	FAdaptationLinkModelInfo LinkDSModelInfo;

};



typedef  TSharedPtr<class FDynamicMeshAdaptiveAdsorption>  FAdaptiveAdsorptionPtr;


struct DESIGNSTATION_API FAdaptationExecuterInitializedData
{
public:
	FAdaptationExecuterInitializedData():Center(FVector::ZeroVector),Extents(FVector::ZeroVector),Rotation(FQuat::Identity)
		,MaxExtents(FVector::ZeroVector),MinExtents(FVector::ZeroVector),DefaultExtents(FVector::ZeroVector) {};
	~FAdaptationExecuterInitializedData() {};
	FVector Center;
	FVector Extents;
	FQuat Rotation;

	//最大包围盒
	FVector MaxExtents;
	//最小包围盒
	FVector MinExtents;

	FVector DefaultExtents;


	bool bExtentsXEnableAdaptation = true;
	bool bExtentsYEnableAdaptation = true;
	bool bExtentsZEnableAdaptation = true;
};



/**
 * 
 */
class DESIGNSTATION_API FAdapationIntersectionExecuter  :public TSharedFromThis<FAdapationIntersectionExecuter>
{
public:
	FAdapationIntersectionExecuter() {};
	virtual~FAdapationIntersectionExecuter() {};


public:

	virtual void Initialize(const TSharedPtr<FAdaptationExecuterInitializedData>& InInitializedData) {};
	virtual bool DoIntersectionTest(const FIntersectionDynamicMesh& SelfIntersectionMesh, const FIntersectionDynamicMesh& Other,
		MeshIntersection::FIntersectionsQueryResult& OutResault, bool bReportCoplanar = true,const IMeshSpatial::FQueryOptions SelfQueryOption = IMeshSpatial::FQueryOptions(), const IMeshSpatial::FQueryOptions& OtherQueryOptions = IMeshSpatial::FQueryOptions());

	virtual void OnInitializeDataUpdate() {};
	virtual bool DoIntersectionTest(const FIntersectionDynamicMesh& SelfIntersectionMesh, const FIntersectionDynamicMesh& Ohter, const IMeshSpatial::FQueryOptions SelfQueryOption = IMeshSpatial::FQueryOptions(), const IMeshSpatial::FQueryOptions& OtherQueryOptions = IMeshSpatial::FQueryOptions());

	virtual bool HandleAdaptiveAndAdsorptionWithRay(const FVector& RayStart, const FVector& RayDir, const TArray<TSharedPtr<FIntersectionDynamicMesh>>& AdaptationEnvs) { return false; };

	virtual bool FindHitNearestPoint(const FVector& RayStart, const FVector& RayDir, const TArray<TSharedPtr<FIntersectionDynamicMesh>>& Env, FVector& OutPoint, FVector& OutNormal, TSharedPtr<FIntersectionDynamicMesh>& OutHitPtr);

	virtual bool FindHitNearestPoint(const FVector& RayStart, const FVector& RayDir, const TArray<TSharedPtr<FIntersectionDynamicMesh>>& Env, FRayHitResault& HitResautl, const TSharedPtr<FIntersectionDynamicMesh>& IgnoreData = nullptr);

	virtual bool FindHitNearestPoint(const FVector& RayStart, const FVector& RayDir, double RayDistance , const TArray<TSharedPtr<FIntersectionDynamicMesh>>& Env, FRayHitResault& HitResautl, const TSharedPtr<FIntersectionDynamicMesh>& IgnoreData = nullptr);

	

	 void DrawOriBox(UObject* WorldContextObject, const UE::Geometry::FOrientedBox3d& InOriBox, FLinearColor LineColor = FLinearColor::Green);

public:
	const FIntersectionDynamicMesh& GetIntersectionData();

	FIntersectionDynamicMesh& GetIntersectionDataRef();

protected:

	//预处理最大最小
	virtual void PreproccessMaxAndMinExtents(const FVector& DefaultExtents, FVector& MaxExtents, FVector& MinExtents);

protected:
	TSharedPtr<FIntersectionDynamicMesh> SourceIntersectionData;
};
