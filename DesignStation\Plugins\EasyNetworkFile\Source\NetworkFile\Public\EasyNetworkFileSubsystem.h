#pragma once

#include "CoreMinimal.h"
#include "Core/Structures/NetworkFileInfo.h"
#include "Core/Structures/NetworkFileTaskPoolPair.h"
#include "Subsystems/WorldSubsystem.h"
#include "Misc/QueuedThreadPool.h"
#include "EasyNetworkFileSubsystem.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogEasyNetworkFileSubsystem, Log, All);

class UBaseNetworkFileTask;
class FQueuedThreadPool;
class IQueuedWork;

enum class EQueuedWorkPriority : uint8;

/*
 *  @@ File subsystem : manager all data
 *  @@ load data from file, save data to file
 */
UCLASS()
class EASYNETWORKFILE_API UEasyNetworkFileSubsystem : public UWorldSubsystem
{
	GENERATED_BODY()

public:
	static UEasyNetworkFileSubsystem* GetInstance();

	virtual void Initialize(FSubsystemCollectionBase& Collection) override;
	virtual void Deinitialize() override;

	UFUNCTION(BlueprintCallable, Category = "DSNetworkFileSubsystem")
	void SetTaskPoolSize(ENetworkFileOperation InOperation, int32 InSize);

	UFUNCTION(BlueprintCallable, Category = "DSNetworkFileSubsystem")
	UBaseNetworkFileTask* CreateTask(ENetworkFileOperation InOperation, bool bAutoRelease = true);

	UFUNCTION(BlueprintPure, Category = "DSNetworkFileSubsystem")
	UBaseNetworkFileTask* FindSameTask(UBaseNetworkFileTask* InTask);

	UFUNCTION(BlueprintPure, Category = "DSNetworkFileSubsystem")
	const TMap<ENetworkFileOperation, TSubclassOf<UBaseNetworkFileTask>>& GetTaskTypes() const;

	UFUNCTION(BlueprintPure, Category = "DSNetworkFileSubsystem")
	const TMap<FString, UBaseNetworkFileTask*>& GetTaskMap() const;

	UFUNCTION(BlueprintPure, Category = "DSNetworkFileSubsystem")
	UBaseNetworkFileTask* FindTask(const FString& TaskId) const;

	UFUNCTION(BlueprintCallable, Category = "DSNetworkFileSubsystem")
	void ReleaseTask(const FString& TaskId);

	UFUNCTION(BlueprintCallable)
	void SetUploadAccessKey(const FString& InAccessKey);
	
	UFUNCTION(BlueprintPure)
	const FString& GetUploadAccessKey() const { return UploadAccessKey; }

	UFUNCTION(BlueprintCallable)
	void SetUploadSecretKey(const FString& InSecretKey);
	
	UFUNCTION(BlueprintPure)
	const FString& GetUploadSecretKey() const { return UploadSecretKey; }

	virtual bool AddMissionToPool(const TSharedPtr<IQueuedWork>& InMission, EQueuedWorkPriority Priority = EQueuedWorkPriority::Normal);
	virtual bool RetractMissionFromPool(const TSharedPtr<IQueuedWork>& InMission);

protected:
	virtual bool ReleaseUselessTask();
	virtual void ResizeTaskPool();

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly)
	TMap<ENetworkFileOperation, int32> TaskPoolSize;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Getter, BlueprintGetter="GetTaskMap")
	TMap<FString, UBaseNetworkFileTask*> TaskMap;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly)
	TArray<FNetworkFileTaskPoolPair> TaskPool;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Getter, BlueprintGetter="GetTaskTypes")
	TMap<ENetworkFileOperation, TSubclassOf<UBaseNetworkFileTask>> TaskTypes;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly)
	FString UploadAccessKey;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly)
	FString UploadSecretKey;

private:
	static UEasyNetworkFileSubsystem* Instance;

	TSharedPtr<FQueuedThreadPool> ProcessorPool;

	FDelegateHandle PreGarbageCollectHandle;

	TArray<FString> AutoReleaseTasks;
};
