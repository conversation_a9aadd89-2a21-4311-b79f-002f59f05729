// Fill out your copyright notice in the Description page of Project Settings.
#pragma once

#include "DSCupboardLibrary.h"

#include "Decimal.h"
#include "FolderData/FolderDataDefine.h"
#include "Geometry/DataDefines/GeometryDatas.h"
#include "Parameter/ParameterProcLibrary.h"
#include "SubSystems/DSNetworkSubsystem.h"
#include "SubSystems/File/Core/ProtoDataConvert/ProtobufOperatorFunctionLibrary.h"
#include "SubSystems/MVC/CatalogSupport/Library/RefRelationFunction.h"
#include "SubSystems/MVC/Core/Macro.h"
#include "SubSystems/MVC/Model/Custom/DSCupboardModel.h"
#include "Subsystems/MVC/Model/Group/DSMultiModel.h"
#include "SubSystems/MVC/DSMVCSubsystem.h"
#include "SubSystems/MVC/StateMachine/State/Public/DSFSMCustomEdit.h"
#include "SubSystems/Library/DesignStationFunctionLibrary.h"
#include "SubSystems/ModelDependencySubsystem/DSModelDependencySubsystem.h"
#include "SubSystems/MVC/Library/DSToolLibrary.h"
#include "Subsystems/Resource/DSResourceSubsystem.h"
#include "SubSystems/UI/Widget/CustomCupboard/Property/CustomCupboard/Core/DSCustomPropertyData.h"
#include "Subsystems/MVC/Model/Custom/ParallelParseTreeNode/DSParallelParseTreeNodePool.h"
#include "SubSystems/MVC/SelectOperate/DSSelectOperateSubsystem.h"
#include "SubSystems/UI/DSUISubsystem.h"
#include "SubSystems/Undo/Library/DSRevokeLibrary.h"

DECLARE_LOG_CATEGORY_CLASS(LogDSCupboardLibrary, Log, All);

extern const FString PARAM_WJ_STR;
extern const FString PARAM_W_STR;
extern const FString PARAM_HJ_STR;
extern const FString PARAM_H_STR;
extern const FString PARAM_DJ_STR;
extern const FString PARAM_D_STR;
extern const FString DRAWER_CHD_STR;
extern const FString DRAWER_CMH_STR;
extern const FString PARAM_ZBFG;
extern const FString PARAM_YBFG;
extern const FString PARAM_SBFG;
extern const FString PARAM_XBFG;

extern const FString PARAM_ZCBJT_STR;
extern const FString PARAM_XCBJT_STR;
extern const FString PARAM_YCBJT_STR;
extern const FString PARAM_SCBJT_STR;

bool UDSCupboardLibrary::LoadFileByFolderID(const FString& InFolderID, FRefToLocalFileData& OutFileData)
{
	FString ResourcePath = FPaths::Combine(FPaths::ProjectSavedDir(), TEXT("Resources"), InFolderID + TEXT(".dat"));
	if(IFileManager::Get().FileExists(*ResourcePath))
	{
		return FProtobufOperatorFunctionLibrary::LoadRelationFromFile(ResourcePath, OutFileData);
	}
	return false;
}

TArray<FRefToLocalFileData> UDSCupboardLibrary::CollectRelativeFoldersData(const FRefToLocalFileData& InData)
{
	TArray<FString> RelativeFolders = URefRelationFunction::GetUpperFolderDirectory(InData.FolderDBData.backend_directory, false);

	TArray<FRefToLocalFileData> Result;
	for (const auto& RelativeFolder : RelativeFolders)
	{
		auto& NewRefData = Result.AddDefaulted_GetRef();
		if (!FProtobufOperatorFunctionLibrary::LoadRelationFromFile(FPaths::Combine(FPaths::ProjectSavedDir(), TEXT("Resources"), RelativeFolder + TEXT(".dat")), NewRefData))
		{
			UE_LOG(LogDSCupboardLibrary, Error, TEXT("Load Ref File [%s] Error!"), *RelativeFolder);
		}
	}

	return Result;
}

void UDSCupboardLibrary::CollectRelativeMaterialFromNode(const TSharedPtr<FMultiComponentDataItem>& InNode,
	TMap<FString, EDSResourceType>& OutMaterialIds)
{
	if (!InNode || InNode->ComponentType != ECompType::SingleCom)
	{
		return;
	}
	
	if (!UDSResourceSubsystem::IsInitialized())
	{
		return;
	}

	for (const FSingleComponentItem& ComponentItem : InNode->SingleComponentData.ComponentItems)
	{
		if (ComponentItem.ComponentMaterial.Value.IsEmpty() || ComponentItem.ComponentMaterial.Value.Equals(TEXT("0.0")))
		{
			continue;
		}

		FString LocalMaterialChecksum = UDSResourceSubsystem::GetInstance()->FindLocalZipMaterialChecksum(FCString::Atoi64(*ComponentItem.ComponentMaterial.Value));
		if (LocalMaterialChecksum.IsEmpty())
		{
			OutMaterialIds.Add(ComponentItem.ComponentMaterial.GetFormattedValue(), EDSResourceType::Material);
		}
	}
}

void UDSCupboardLibrary::CalculateTreeRootNodeParams(const FRefToLocalFileData& InData, const TSharedPtr<FMultiComponentDataItem>& InNode, const TMap<FString, FParameterData>& StyleParams, TMap<FString, FParameterData>& OutParentParams)
{
	OutParentParams.Empty();

	TMap<FString, FParameterData> GlobalParams = UDSMVCSubsystem::GetInstance()->GetGlobalParamsMap();
	TMap<FString, FParameterData> GlobalStyleParams = UDSMVCSubsystem::GetInstance()->GetGlobalStyleParamsMap();
	
	// Collect the parameter set of all parent directories.
	TArray<FRefToLocalFileData> RelativeFoldersData = CollectRelativeFoldersData(InData);
	GetHeritParameterData(RelativeFoldersData, UDSMVCSubsystem::GetInstance()->GetGlobalParamsMap(),OutParentParams);

	// Bind global style parameters to directory parameters. 
	CombineParameters(OutParentParams, GlobalStyleParams,OutParentParams);

	// Bind applied style parameters to parameter set, but not replace parameter set.
	CombineParameters(OutParentParams, StyleParams, OutParentParams);
	
	// Calculate the result of root node parameter set.
	TMap<FString, FParameterData> TreeRootParams = URefRelationFunction::ConvertParamsArrayToMap(InNode->ComponentParameters);
	CalculateParameters(GlobalParams, OutParentParams, TreeRootParams);
	TreeRootParams.GenerateValueArray(InNode->ComponentParameters);
}

bool UDSCupboardLibrary::ParseTreeFromNode(const TSharedPtr<FMultiComponentDataItem>& RootNode,
	const TSharedPtr<FMultiComponentDataItem>& InNode,
	const TSharedPtr<FMultiComponentDataItem>& InOriginalNode,
	const TMap<FString, FParameterData>& ParentParams,
	const FOnProcessParsedByOriginalNodeDelegate& ParseNodeProcessor,
	TMap<TSharedPtr<FMultiComponentDataItem>, TSharedPtr<TMap<FString, FParameterData>>>& OutComponentOverrideParams,
	TMap<FString, EDSResourceType>& OutFolderIdsForDownload,
	TArray<FDSCustomStyleReplacementNode>& OutStylizedNodes)
{
	if (!UDSResourceSubsystem::IsInitialized() || !UDSMVCSubsystem::IsInitialized())
	{
		return false;
	}
	
	TMap<FString, FParameterData> GlobalParams = UDSMVCSubsystem::GetInstance()->GetGlobalParamsMap();
	FGeometryDatas::CalculateCurMultiComponentData(GlobalParams, ParentParams, *InNode);

	// Parent params for the children of current layer.
	OutComponentOverrideParams.Add(InNode, MakeShared<TMap<FString, FParameterData>>());
	FParameterProcLibrary::CombineParameters(ParentParams, InNode->ComponentParameters, *OutComponentOverrideParams[InNode]);

	// Collect the relative materials of root.
	CollectRelativeMaterialFromNode(InNode, OutFolderIdsForDownload);

	// Collect which file need to download, even it has already downloaded.
	for (const TSharedPtr<FMultiComponentDataItem>& ChildItem : InNode->ChildComponent)
	{
		FGeometryDatas::CalculateCurMultiComponentData(GlobalParams, *OutComponentOverrideParams[InNode], *ChildItem);
		
		ChildItem->ComponentID.FormatValue();
		if (ChildItem->ComponentID.Value.IsEmpty() || ChildItem->ComponentType != ECompType::ImportModel)
		{
			continue;
		}
		
		// Child component resource file.
		OutFolderIdsForDownload.Add(ChildItem->ComponentID.Value, EDSResourceType::Model);
	}
	
	if (InNode->UUID.IsEmpty())
	{
		InNode->UUID = FGuid::NewGuid().ToString();
	}

	TArray<TPair<FString, TWeakPtr<FMultiComponentDataItem>>> NodePath;
	RecursiveGetTreeNodePath(RootNode, InNode, NodePath);
	return ParseTreeFromNode_Inner(InNode, InOriginalNode, GlobalParams, ParseNodeProcessor, NodePath, OutComponentOverrideParams, OutFolderIdsForDownload, OutStylizedNodes);
}

void UDSCupboardLibrary::GetHeritParameterData(
	const TArray<FRefToLocalFileData>& DirectoryRefDatas,
	const TMap<FString, FParameterData>& GlobalParams, 
	TMap<FString, FParameterData>& OutHeritParameters
)
{
	if (DirectoryRefDatas.Num() > 0)
	{
		for (int32 i = 0; i < DirectoryRefDatas.Num(); ++i)
		{
			TMap<FString, FParameterData> CurParameters = URefRelationFunction::ConvertParamsArrayToMap(
				DirectoryRefDatas[i].ParamDatas
			);
			FGeometryDatas::CalculateParameterValue_LevelSort(
				GlobalParams, 
				OutHeritParameters, 
				CurParameters
			);

			for (const TPair<FString, FParameterData>& Pair : CurParameters)
			{
				if (OutHeritParameters.Contains(Pair.Key))
				{
					OutHeritParameters[Pair.Key] = Pair.Value;
				}
				else
				{
					OutHeritParameters.Add(Pair);
				}
			}
		}
	}
}

void UDSCupboardLibrary::ConstructTreeRootNode(const FRefToLocalFileData& RefData, FMultiComponentDataItem& TreeNode)
{
	const auto& FileDBData = RefData.FolderDBData;
	TreeNode.UUID = FGuid::NewGuid().ToString();
	TreeNode.ComponentName = FileDBData.folder_name;
	TreeNode.ComponentVisibility.Expression = FileDBData.visibility_exp;
	TreeNode.ComponentVisibility.Value = FString::SanitizeFloat(FileDBData.visibility);
	TreeNode.bHiden = false;
	TreeNode.ComponentID.Expression = FileDBData.folder_id;
	TreeNode.ComponentID.Value = FileDBData.folder_id;
	TreeNode.Description = FileDBData.folder_name;
	TreeNode.Code = FileDBData.folder_code;
	TreeNode.CodeExp = FileDBData.folder_code_exp;
	TreeNode.ModelType = GetModelType_Parameters(RefData.ParamDatas);
	TreeNode.ComponentType = ECompType::MultiCom;
	TreeNode.SingleComponentPath = TEXT("");
	TreeNode.ComponentLocation = FLocationProperty();
	TreeNode.ComponentRotation = FRotationProperty();
	TreeNode.ComponentScale = FScaleProperty();

	TreeNode.ComponentParameters = RefData.ParamDatas;
	TreeNode.StyleParameters = TArray<FParameterData>();

	TreeNode.bComponentIDChanged = false;

	TreeNode.SingleComponentData = FSingleComponentProperty();

	TreeNode.ChildComponent.Empty();
	for(const auto& RDCD : RefData.ComponentDatas)
	{
		auto& NewComponent = TreeNode.ChildComponent.AddDefaulted_GetRef();
		NewComponent = MakeShared<FMultiComponentDataItem>(RDCD.ConvertToMultiComponentDataItem());
	}

	TreeNode.AdaptationData = UDesignStationFunctionLibrary::GetPlaceRuleData(RefData);
}

void UDSCupboardLibrary::CombineParameters(
	const TMap<FString, FParameterData>& InBaseParameters,
	const TMap<FString, FParameterData>& InCombineParameters,
	TMap<FString, FParameterData>& Result
)
{
	Result = InBaseParameters;
	for (auto& Iter : InCombineParameters)
	{
		if (Result.Contains(Iter.Key))
		{
			Result[Iter.Key] = Iter.Value;
		}
		else
		{
			Result.Add(Iter);
		}
	}
}

void UDSCupboardLibrary::CalculateParameters(const TMap<FString, FParameterData>& GlobalParameters,
	const TMap<FString, FParameterData>& OverrideParameters, TMap<FString, FParameterData>& Result)
{
	FGeometryDatas::CalculateParameterValue_LevelSort(
		GlobalParameters,
		OverrideParameters,
		Result
	);
}

bool UDSCupboardLibrary::ComponentNeedSeparate(const TArray<FParameterData>& InParams)
{
	const int32 KXZ_Index = InParams.IndexOfByPredicate(
		[](const FParameterData& InData)
		{
			return InData.Data.name.Equals(DS_CUPBOARD_COMPONENT_SEPARATE_MARK, ESearchCase::IgnoreCase);
		}
	);
	/*if(KXZ_Index != INDEX_NONE)
	{
		double Value = FCString::Atod(*InParams[KXZ_Index].Data.value);
		return !FMath::IsNearlyZero(Value, 0.01f);
	}*/
	return KXZ_Index != INDEX_NONE;
}

int32 UDSCupboardLibrary::GetModelType_Parameters(const TArray<FParameterData>& InParams)
{
	static TArray<FString> TypeParamNames = {
		TEXT("LXGT"), TEXT("LXGNJ"), TEXT("LXMB"), TEXT("LXTZB"), TEXT("LXFB"), TEXT("LXWJ"), TEXT("LXLMZ"), TEXT("LXDX"),
		TEXT("LXXTX"), TEXT("LXTJX"), TEXT("LXCPJJ"), TEXT("LXRZSP"), TEXT("LXDQ"), TEXT("LXSC"), TEXT("LXLT"), TEXT("LXTM"),
		TEXT("LXCPDD"), TEXT("LXGTB"), TEXT("LXMX"), TEXT("LXFJPJ"), TEXT("LXKS"), TEXT("LXCPDD"), TEXT("LXGT"), TEXT("LXLS"),
		TEXT("LXWJMJ"), TEXT("LXWJLG"), TEXT("LXHXMB"), TEXT("LXBLMX"), TEXT("MRQ")
	};
	
	int32 ParamPos = InParams.IndexOfByPredicate([&](const FParameterData& InParam){ return InParam.Data.is_enum && TypeParamNames.Contains(InParam.Data.name); });
	if (ParamPos == INDEX_NONE)
	{
		return -1;
	}
	
	return FCString::Atoi(*InParams[ParamPos].Data.value);
}

bool UDSCupboardLibrary::GetMultiComponentInfo(
	const TMap<FString, FParameterData>& GlobalParams, 
	TMap<FString, FParameterData> UpperLevelParams, 
	FDSCupboardModelInfo& OutTreeDatas,
	TArray<FString>& NeedDownloadIDS,
	TArray<FExpressionValuePair>& DependModelIDS,
	TArray<FExpressionValuePair>& DependMatIDS,
	FDependFileData InDependDatas,
	TArray<FString> InSkipDependFiles
)
{
	//calculate current level parameter
	TMap<FString, FParameterData> CurComponentParameters = URefRelationFunction::ConvertParamsArrayToMap(
		OutTreeDatas.ComponentTreeData->ComponentParameters
	);

	FGeometryDatas::CalculateParameterValue_LevelSort(
		GlobalParams, 
		UpperLevelParams,
		CurComponentParameters
	);

	//rewrite new value to component parameters
	auto& ParamsArray = OutTreeDatas.ComponentTreeData->ComponentParameters;
	for (const auto& iter : CurComponentParameters)
	{
		auto& EditParameter = iter.Value;
		const int32 Index = ParamsArray.IndexOfByPredicate([EditParameter](const FParameterData& InOther) { return InOther.Data.name.Equals(EditParameter.Data.name); });
		if (INDEX_NONE != Index) ParamsArray[Index] = EditParameter;
	}

	//combine current level override parameters
	FParameterProcLibrary::CombineParameters(
		UpperLevelParams,
		ParamsArray, 
		UpperLevelParams
	);

	//计算当前层数据
	for (auto& Iter : OutTreeDatas.ComponentTreeData->ChildComponent)
	{
		FGeometryDatas::CalculateCurMultiComponentData(GlobalParams, UpperLevelParams, *Iter);
	}

	//need download files
	for (auto& Iter : OutTreeDatas.ComponentTreeData->ChildComponent)
	{
		//if (Iter.ComponentType == ECompType::None) continue;

		Iter->ComponentID.FormatValue();

		if (Iter->ComponentID.Value.IsEmpty()) continue;

		if (InSkipDependFiles.Contains(Iter->ComponentID.Value))
		{//skip parse, clear old data, only save component id
			Iter->ChildComponent.Empty();
			//Iter.ComponentType = ECompType::None;
			Iter->SingleComponentPath.Empty();
			Iter->SingleComponentData.ComponentItems.Empty();
			continue;
		}

		if (Iter->ComponentType == ECompType::ImportModel)
		{
			Iter->ComponentID.FormatValue();
			if (!InDependDatas.ModelAlreadyHas(Iter->ComponentID.Value))
			{
				DependModelIDS.AddUnique(Iter->ComponentID);
			}
			/*else
			{
				auto& ImportModelComp = Iter.SingleComponentData.ComponentItems.AddDefaulted_GetRef();
				ImportModelComp.ComponentSource = ESingleComponentSource::EImportPAK;

				FCSModelMatData ModelData;
				if (InDependDatas.GetDependFile(Iter.ComponentID.Value, ModelData))
				{
					ModelData.GetPakFileMountInfo(ImportModelComp.PakRefPath, ImportModelComp.PakRelativeFilePath);
				}
			}*/
			continue;
		}

		FString FileName = URefRelationFunction::FormatFolderID(Iter->ComponentID.Value);
		FString FileRelativePath = URefToFileData::GetFileRelativeAddress(FileName);
		if (!FPaths::FileExists(FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir(), FileRelativePath))))
		{
			/*
			*  @@ step 1 : judge has compare as custom file, if not input file id to download custom file array [NeedDownloadIDS] 
			*  @@ if step 1 is true, then step 2
			*  @@ step 2 : judge has skip as import file, if not input file id to import file array [DependModelIDS]
			*  @@ if step 2 is true, then all skip , this file has no ref, only save component id
			*/
			if (InSkipDependFiles.Contains(FileRelativePath))
			{
				if (!InSkipDependFiles.Contains(Iter->ComponentID.Value))
				{
					DependModelIDS.AddUnique(Iter->ComponentID);
				}
				continue;
			}

			NeedDownloadIDS.AddUnique(FileRelativePath);
		}
	}

	//递归获取下一层数据
	//if (NeedDownloadIDS.Num() <= 0)
	{
		int32 ComponentIndex = 0;
		for (auto& Iter : OutTreeDatas.ComponentTreeData->ChildComponent)
		{
			const FString TreePath = FString::Printf(TEXT("%02d"), ComponentIndex);
			ComponentIndex++;

			//if (Iter.ComponentType == ECompType::None) continue;

			if (Iter->ComponentType == ECompType::ImportModel)
			{
				Iter->ComponentID.FormatValue();
				if (InDependDatas.ModelAlreadyHas(Iter->ComponentID.Value))
				{
					Iter->ComponentType = ECompType::ImportModel;
					auto& ImportModelComp = Iter->SingleComponentData.ComponentItems.AddDefaulted_GetRef();
					ImportModelComp.ComponentSource = ESingleComponentSource::EImportPAK;

					FCSModelMatData ModelData;
					if (InDependDatas.GetDependFile(Iter->ComponentID.Value, ModelData))
					{
						ModelData.GetPakFileMountInfo(ImportModelComp.PakRefPath, ImportModelComp.PakRelativeFilePath);
					}
				}
				continue;
			}

			FRefToLocalFileData IterFileData;
			if (URefRelationFunction::GetCurrentRefRelationFromFile(Iter->ComponentID.Value, IterFileData))
			{
				UDSCupboardLibrary::GetRefRelationRecursiveLevelOrder(
					IterFileData,
					GlobalParams,
					UpperLevelParams,
					Iter,
					OutTreeDatas.ComponentInfoArr,
					TreePath,
					NeedDownloadIDS,
					DependModelIDS,
					DependMatIDS,
					InDependDatas,
					InSkipDependFiles
				);
			}
		}
	}
	
	return NeedDownloadIDS.Num() > 0 || DependModelIDS.Num() > 0 || DependMatIDS.Num() > 0;
}

bool UDSCupboardLibrary::ParseTreeFromNode_Inner(const TSharedPtr<FMultiComponentDataItem>& InNode,
                                                 const TSharedPtr<FMultiComponentDataItem>& InOriginalNode,
                                                 const TMap<FString, FParameterData>& GlobalParams,
												 const FOnProcessParsedByOriginalNodeDelegate& ParseNodeProcessor,
												 const TArray<TPair<FString, TWeakPtr<FMultiComponentDataItem>>>& NodePath,
                                                 TMap<TSharedPtr<FMultiComponentDataItem>, TSharedPtr<TMap<FString, FParameterData>>>& OutComponentOverrideParams,
                                                 TMap<FString, EDSResourceType>& OutFolderIdsForDownload,
                                                 TArray<FDSCustomStyleReplacementNode>& OutStylizedNodes)
{
	if (!InNode)
	{
		return false;
	}

	struct FQueueItem
	{
		TSharedPtr<FMultiComponentDataItem> Node;
		TSharedPtr<FMultiComponentDataItem> OriginalNode;
		TArray<TPair<FString, TWeakPtr<FMultiComponentDataItem>>> ItemNodePath;
	};

	TArray<FQueueItem> CurrentLevel;
	{
		if (!OutComponentOverrideParams.Contains(InNode))
		{
			return false;
		}

		CurrentLevel.Add({ InNode, InOriginalNode, NodePath });
	}

	while (!CurrentLevel.IsEmpty())
	{
		FDSParallelParseTreeNodePool ParsePool;

		for (const FQueueItem& CurrentItem : CurrentLevel)
		{
			if (!OutComponentOverrideParams.Contains(CurrentItem.Node))
			{
				continue;
			}

			for (int32 ChildIndex = 0; ChildIndex < CurrentItem.Node->ChildComponent.Num(); ++ChildIndex)
			{
				TSharedPtr<FDSParallelParseTreeNodeWorker> ChildWorker = MakeShared<FDSParallelParseTreeNodeWorker>(GlobalParams);
				ChildWorker->ChildItem = CurrentItem.Node->ChildComponent[ChildIndex];
				if (CurrentItem.OriginalNode && CurrentItem.OriginalNode->ChildComponent.IsValidIndex(ChildIndex))
				{
					ChildWorker->OriginalChildItem = CurrentItem.OriginalNode->ChildComponent[ChildIndex];
				}

				if (!OutComponentOverrideParams.Contains(ChildWorker->ChildItem))
				{
					OutComponentOverrideParams.Add(ChildWorker->ChildItem, MakeShared<TMap<FString, FParameterData>>());
				}

				ChildWorker->ParentItemOverrideParams = OutComponentOverrideParams[CurrentItem.Node];
				ChildWorker->ChildItemOverrideParams = OutComponentOverrideParams[ChildWorker->ChildItem];

				ChildWorker->NodePath = CurrentItem.ItemNodePath;

				ParsePool.EnqueueTask(ChildWorker);
			}
		}

		ParsePool.StartTasks();
		bool bAllTasksCompleted = ParsePool.WaitForTasks();

		CurrentLevel.Empty();

		const TArray<TSharedPtr<FDSParallelParseTreeNodeWorker>>& Workers = ParsePool.GetWorkers();
		for (const TSharedPtr<FDSParallelParseTreeNodeWorker>& Worker : Workers)
		{
			if (bAllTasksCompleted)
			{
				FQueueItem& NewItem = CurrentLevel.AddDefaulted_GetRef();
				NewItem.Node = Worker->ChildItem;
				NewItem.OriginalNode = Worker->bChildrenFromFiles ? nullptr : Worker->OriginalChildItem;
				NewItem.ItemNodePath = Worker->NodePath;

				FString TypeCode;
				if (UDSCupboardLibrary::TryGetCodeNameFromCustomType(NewItem.Node->ModelType, TypeCode))
				{
					NewItem.ItemNodePath.Push({ TypeCode, NewItem.Node });
				}

				OutFolderIdsForDownload.Append(Worker->FolderIdsForDownload);

				OutStylizedNodes.Append(Worker->StyleReplacementNodes);
			}

			if (Worker && Worker->bSuccessfully && Worker->bNeedExecuteProcessor)
			{
				if (ParseNodeProcessor.IsBound())
				{
					ParseNodeProcessor.Execute(Worker->ChildItem, Worker->OriginalChildItem);
				}
			}
		}

		if (!bAllTasksCompleted)
		{
			return false;
		}
	}
	
	return true;
}


bool UDSCupboardLibrary::GetRefRelationRecursiveLevelOrder(
	FRefToLocalFileData& FileData,
	const TMap<FString, FParameterData>& GlobalParams,
	TMap<FString, FParameterData> UpperLevelParams,
	const TSharedPtr<FMultiComponentDataItem>& CurComponentData,
	TArray<FDSComponentInfo>& ModelComponentData,
	const FString& TreePath,
	TArray<FString>& NeedDownloadIDS,
	TArray<FExpressionValuePair>& DependModelIDS,
	TArray<FExpressionValuePair>& DependMatIDS,
	FDependFileData InDependDatas,
	TArray<FString> InSkipDependFiles
)
{
	CurComponentData->ChildComponent.Empty();
	
	/*
	 *  @@ calculate , combine current layer parameters
	 *  @@ when ComponentID is empty, it is marked as the outermost layer, and the parameters on the outer layer file need to be used at this time
	 */
	TMap<FString, FParameterData> CurComponentParameters = URefRelationFunction::ConvertParamsArrayToMap(CurComponentData->ComponentParameters);
	FGeometryDatas::CalculateParameterValue_LevelSort(
		GlobalParams, 
		UpperLevelParams, 
		CurComponentParameters);

	auto& ParamsArray = CurComponentData->ComponentParameters;
	for (const auto& iter : CurComponentParameters)
	{
		auto& EditParameter = iter.Value;
		const int32 Index = ParamsArray.IndexOfByPredicate(
			[EditParameter](const FParameterData& InOther)
			{
				return InOther.Data.name.Equals(EditParameter.Data.name, ESearchCase::IgnoreCase);
			}
		);
		if (INDEX_NONE != Index)
		{
			ParamsArray[Index] = EditParameter;
		}
	}
	FParameterProcLibrary::CombineParameters(
		UpperLevelParams,
		ParamsArray, 
		UpperLevelParams
	);
	
	if(FileData.FolderDBData.folder_type == 4)  //folder_type == EFolderType
	{
		CurComponentData->ComponentType = ECompType::MultiCom;
		/*
		*  @@ get and transform current layer datas
		*/
		for (auto& Iter : FileData.ComponentDatas)
		{
			TSharedPtr<FMultiComponentDataItem> NewData = CurComponentData->ChildComponent.AddDefaulted_GetRef();
			NewData = MakeShared<FMultiComponentDataItem>(Iter.ConvertToMultiComponentDataItem());
			NewData->ComponentID.FormatValue();
		}

		/*
		*  @@ calculate current layer data
		*/
		for (auto& Iter : CurComponentData->ChildComponent)
		{
			FGeometryDatas::CalculateCurMultiComponentData(GlobalParams, UpperLevelParams, *Iter);
		}

		//need download files
		for (auto& Iter : CurComponentData->ChildComponent)
		{
			if(!Iter->IsVisiable()) continue;

			Iter->ComponentID.FormatValue();

			if (Iter->ComponentID.Value.IsEmpty()) continue;

			if (Iter->ComponentType == ECompType::ImportModel)
			{
				if (!InDependDatas.ModelAlreadyHas(Iter->ComponentID.Value))
				{//need to download file 
					Iter->ComponentID.FormatValue();
					DependModelIDS.AddUnique(Iter->ComponentID);
				}
				continue;
			}

			FString FileName = URefRelationFunction::FormatFolderID(Iter->ComponentID.Value);
			FString FileRelativePath = URefToFileData::GetFileRelativeAddress(FileName);
			if (!FPaths::FileExists(FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectContentDir(), FileRelativePath))))
			{
				if (InSkipDependFiles.Contains(FileRelativePath))
				{
					if (!InSkipDependFiles.Contains(Iter->ComponentID.Value))
					{
						DependModelIDS.AddUnique(Iter->ComponentID);
					}
					continue;
				}

				NeedDownloadIDS.AddUnique(FileRelativePath);
			}
		}

		//depend model
		for (auto Iter : FileData.MatModelDependFiles.GetDependFolderID_Model())
		{
			DependModelIDS.AddUnique(Iter);
		}

		//depend mat
		for (auto Iter : FileData.MatModelDependFiles.GetDependFolderID_Mat())
		{
			DependMatIDS.AddUnique(Iter);
		}

		//递归获取下一层数据
		//if (NeedDownloadIDS.Num() <= 0)
		{
			int32 ComponentIndex = 0;
			for (auto& Iter : CurComponentData->ChildComponent)
			{
				const FString TreeCurPath = TreePath + FString::Printf(TEXT("%02d"), ComponentIndex);
				ComponentIndex++;

				if (Iter->ComponentType == ECompType::ImportModel)
				{
					Iter->ComponentID.FormatValue();
					if (InDependDatas.ModelAlreadyHas(Iter->ComponentID.Value))
					{
						auto& ImportModelComp = Iter->SingleComponentData.ComponentItems.AddDefaulted_GetRef();
						ImportModelComp.ComponentSource = ESingleComponentSource::EImportPAK;

						FCSModelMatData ModelData;
						if (InDependDatas.GetDependFile(Iter->ComponentID.Value, ModelData))
						{
							ModelData.GetPakFileMountInfo(ImportModelComp.PakRefPath, ImportModelComp.PakRelativeFilePath);
						}


					}
					continue;
				}
				else
				{
					FRefToLocalFileData IterFileData;
					if (URefRelationFunction::GetCurrentRefRelationFromFile(Iter->ComponentID.Value, IterFileData))
					{
						if (Iter->ComponentType == ECompType::None)
						{//rewrite type from data
							Iter->ComponentType = static_cast<ECompType>(IterFileData.FolderDBData.folder_type);
						}
						GetRefRelationRecursiveLevelOrder(
							IterFileData,
							GlobalParams,
							UpperLevelParams,
							Iter,
							ModelComponentData,
							TreeCurPath,
							NeedDownloadIDS,
							DependModelIDS,
							DependMatIDS,
							InDependDatas,
							InSkipDependFiles
						);
					}
				}
			}
		}
	}
	else if(FileData.FolderDBData.folder_type == 5)
	{
		CurComponentData->ComponentType = ECompType::SingleCom;
		CurComponentData->SingleComponentData.ComponentItems = FileData.FileData.component_datas;
		FGeometryDatas::CalculateParameterValue(GlobalParams, UpperLevelParams, CurComponentData->SingleComponentData);
		CurComponentData->SingleComponentPath = FileData.FileData.file_data_path;

		//depend model
		for (auto Iter : FileData.MatModelDependFiles.GetDependFolderID_Model())
		{
			Iter.FormatValue();
			if (InSkipDependFiles.Contains(Iter.Value))
				continue;
			DependModelIDS.AddUnique(Iter);
		}

		//depend mat
		for (auto Iter : FileData.MatModelDependFiles.GetDependFolderID_Mat())
		{
			Iter.FormatValue();
			if (InSkipDependFiles.Contains(Iter.Value))
				continue;
			DependMatIDS.AddUnique(Iter);
		}
		
	}

	//component separate ?
	// if(UDSCupboardLibrary::ComponentNeedSeparate(CurComponentData->ComponentParameters))
	// {
	// 	if(CurComponentData->UUID.IsEmpty())
	// 	{
	// 		CurComponentData->GenerateUUID();
	// 	}
	//
	// 	FDSComponentInfo NewComponentInfo;
	// 	NewComponentInfo.ComponentUUID = CurComponentData->UUID;
	// 	NewComponentInfo.ComponentTreePath = TreePath;
	// 	NewComponentInfo.ComponentOperator = EDSComponentOperator::DSO_Separate;
	// 	UDSCupboardModel* SeparateModel = UDSCupboardModel::CreateModel(
	// 		UDSCupboardLibrary::GetModelType_Parameters(FileData.ParamDatas)
	// 	);
	// 	if (DS_MODEL_VALID_FOR_USE(SeparateModel))
	// 	{
	// 		SeparateModel->InitConstruct_Separate(FileData, CurComponentData, UpperLevelParams);
	// 	}
	// 	NewComponentInfo.ComponentModel = SeparateModel;
	// }

	return NeedDownloadIDS.Num() > 0 || DependModelIDS.Num() > 0 || DependMatIDS.Num() > 0;
}


#define PARAM_X  TEXT("W")
#define PARAM_Y TEXT("D")
#define PARAM_Z TEXT("H")



bool UDSCupboardLibrary::CombineBaseDHWParameters(TMap<FString, FParameterData>& OutParameters, const TMap<FString, FParameterData>& InParameters)
{
	if (InParameters.Contains(PARAM_X) && InParameters.Contains(PARAM_Y) && InParameters.Contains(PARAM_Z))
	{
		OutParameters.Add(PARAM_X, /*InParameters.Contains(PARAM_WJ_STR) ? InParameters[PARAM_WJ_STR] : */InParameters[PARAM_X]);
		OutParameters.Add(PARAM_Y, InParameters[PARAM_Y]);
		OutParameters.Add(PARAM_Z, /*InParameters.Contains(PARAM_HJ_STR) ? InParameters[PARAM_HJ_STR] : */InParameters[PARAM_Z]);
		return true;
	}
	return false;
}

bool UDSCupboardLibrary::ConvertBaseDHWParametersToFVector(FVector& OutVector, const TMap<FString, FParameterData>& InParameters)
{
	if (InParameters.Contains(PARAM_X) && InParameters.Contains(PARAM_Y) && InParameters.Contains(PARAM_Z))
	{
		OutVector.X =FCString::Atod(*InParameters[PARAM_X].Data.value);
		OutVector.Y = FCString::Atod(*InParameters[PARAM_Y].Data.value);
		OutVector.Z = FCString::Atod(*InParameters[PARAM_Z].Data.value);
		OutVector /= 10.f;
		return true;
	}
	return false;
}

bool UDSCupboardLibrary::ConvertBaseDHWParametersToFVector(FVector& OutVector, const TArray<FParameterData>& InParameters)
{
	OutVector = FVector::One();
	bool bXExist = false;
	bool bYExist = false;
	bool bZExist = false;
	for (auto& Iter : InParameters)
	{
		if (Iter.Data.name.Equals(PARAM_X))
		{
			OutVector.X = FCString::Atod(*Iter.Data.value);
			bXExist = true;
		}
		else if (Iter.Data.name.Equals(PARAM_Y))
		{
			OutVector.Y = FCString::Atod(*Iter.Data.value);
			bYExist = true;
		}
		else if (Iter.Data.name.Equals(PARAM_Z))
		{
			OutVector.Z = FCString::Atod(*Iter.Data.value);
			bZExist = true;
		}


	}
	OutVector /= 10.f;
	return bXExist&& bYExist&& bZExist;
}

bool UDSCupboardLibrary::ConvertMaxDHWParametersToFVector(FVector& OutVector, const TMap<FString, FParameterData>& InParameters)
{
	if (InParameters.Contains(PARAM_X) && InParameters.Contains(PARAM_Y) && InParameters.Contains(PARAM_Z))
	{
		OutVector.X = FCString::Atod(*InParameters[PARAM_X].Data.max_value);
		OutVector.Y = FCString::Atod(*InParameters[PARAM_Y].Data.max_value);
		OutVector.Z = FCString::Atod(*InParameters[PARAM_Z].Data.max_value);
		OutVector /= 10.f;
		return true;
	}
	return false;
}

bool UDSCupboardLibrary::ConvertMinDHWParametersToFVector(FVector& OutVector, const TMap<FString, FParameterData>& InParameters)
{
	if (InParameters.Contains(PARAM_X) && InParameters.Contains(PARAM_Y) && InParameters.Contains(PARAM_Z))
	{
		OutVector.X = FCString::Atod(*InParameters[PARAM_X].Data.min_value);
		OutVector.Y = FCString::Atod(*InParameters[PARAM_Y].Data.min_value);
		OutVector.Z = FCString::Atod(*InParameters[PARAM_Z].Data.min_value);
		OutVector /= 10.f;
		return true;
	}
	return false;
}

bool UDSCupboardLibrary::ConvertDHWParametersToFVector(FVector& OutMinVector, FVector& OutDefaultVector, FVector& OutMaxVector, const TMap<FString, FParameterData>& InParameters)
{
	if (InParameters.Contains(PARAM_X) && InParameters.Contains(PARAM_Y) && InParameters.Contains(PARAM_Z))
	{
		OutMinVector.X = FCString::Atod(*InParameters[PARAM_X].Data.min_value);
		OutMinVector.Y = FCString::Atod(*InParameters[PARAM_Y].Data.min_value);
		OutMinVector.Z = FCString::Atod(*InParameters[PARAM_Z].Data.min_value);
		OutMinVector /= 10.f;

		OutDefaultVector.X = FCString::Atod(*InParameters[PARAM_X].Data.value);
		OutDefaultVector.Y = FCString::Atod(*InParameters[PARAM_Y].Data.value);
		OutDefaultVector.Z = FCString::Atod(*InParameters[PARAM_Z].Data.value);
		OutDefaultVector /= 10.f;

		OutMaxVector.X = FCString::Atod(*InParameters[PARAM_X].Data.max_value);
		OutMaxVector.Y = FCString::Atod(*InParameters[PARAM_Y].Data.max_value);
		OutMaxVector.Z = FCString::Atod(*InParameters[PARAM_Z].Data.max_value);
		OutMaxVector /= 10.f;
		return true;
	}

	return false;
}

bool UDSCupboardLibrary::WriteBaseDHWParamtersFromVector(const FVector& InVector, TArray<FParameterData>& OutParameters)
{
	bool bSucceded = false;
	for (size_t i = 0; i < OutParameters.Num(); i++)
	{
		FParameterData& Param = OutParameters[i];
		if (Param.Data.name.Equals(PARAM_X))
		{
			Param.Data.value = FString::SanitizeFloat(InVector.X * 10.f, 1);
			Param.Data.expression = FString::SanitizeFloat(InVector.X * 10.f, 1);
			bSucceded = true;
			continue;
		}
		else if(Param.Data.name.Equals(PARAM_Y))
		{
			Param.Data.value = FString::SanitizeFloat(InVector.Y * 10.f, 1);
			Param.Data.expression = FString::SanitizeFloat(InVector.Y * 10.f, 1);
			bSucceded = true;
			continue;
		}
		else if (Param.Data.name.Equals(PARAM_Z))
		{
			Param.Data.value = FString::SanitizeFloat(InVector.Z * 10.f, 1);
			Param.Data.expression = FString::SanitizeFloat(InVector.Z * 10.f, 1);
			bSucceded = true;
			continue;
		}
		
	}
	return bSucceded;
}

bool UDSCupboardLibrary::WriteBaseDrawerParamtersFromVector(const FVector& InVector, TArray<FParameterData>& OutParameters)
{
	bool bSucceded = false;
	for (size_t i = 0; i < OutParameters.Num(); i++)
	{
		FParameterData& Param = OutParameters[i];
		if (Param.Data.name.Equals(PARAM_W_STR))
		{
			Param.Data.value = FString::SanitizeFloat(InVector.X * 10.f, 1);
			Param.Data.expression = FString::SanitizeFloat(InVector.X * 10.f, 1);
			bSucceded = true;
			continue;
		}
		else if (Param.Data.name.Equals(PARAM_H_STR))
		{
			Param.Data.value = FString::SanitizeFloat(InVector.Z * 10.f, 1);
			Param.Data.expression = FString::SanitizeFloat(InVector.Z * 10.f, 1);
			bSucceded = true;
			continue;
		}
		else if (Param.Data.name.Equals(PARAM_D_STR))
		{
			Param.Data.value = FString::SanitizeFloat(InVector.Y * 10.f, 1);
			Param.Data.expression = FString::SanitizeFloat(InVector.Y * 10.f, 1);
			bSucceded = true;
			continue;
		}
		/*else if (Param.Data.name.Equals(DRAWER_CHD_STR))
		{
			auto DJ = InVector.Y * 10.f -15 ;
			if (250 <= DJ && DJ < 300)
			{
				Param.Data.value = FString::FromInt(250);
				Param.Data.expression = FString::FromInt(250);
			}
			else 	if (300 <= DJ && DJ < 350)
			{
				Param.Data.value = FString::FromInt(300);
				Param.Data.expression = FString::FromInt(300);
			}
			else 	if (350 <= DJ && DJ < 400)
			{
				Param.Data.value = FString::FromInt(350);
				Param.Data.expression = FString::FromInt(350);
			}
			else 	if (400 <= DJ && DJ < 450)
			{
				Param.Data.value = FString::FromInt(400);
				Param.Data.expression = FString::FromInt(400);
			}
			else 	if (450 <= DJ && DJ < 500)
			{
				Param.Data.value = FString::FromInt(450);
				Param.Data.expression = FString::FromInt(450);
			}
			else if (500 <= DJ)
			{
				Param.Data.value = FString::FromInt(500);
				Param.Data.expression = FString::FromInt(500);
			}
			else
			{
				Param.Data.value = FString::FromInt(250);
				Param.Data.expression = FString::FromInt(250);
			}
			bSucceded = true;
		}*/
	}
	return bSucceded;
}

bool UDSCupboardLibrary::WriteBaseFGParamtersFromVector(const double& UpFG, const double& DownFG,const double& LeftFG, const double& RightFG, TArray<FParameterData>& OutParameters)
{
	bool bSucceded = false;
	for (size_t i = 0; i < OutParameters.Num(); i++)
	{
		FParameterData& Param = OutParameters[i];
		if (Param.Data.name.Equals(PARAM_SCBJT_STR))
		{
			Param.Data.value = FString::SanitizeFloat(UpFG * 10.f, 1);
			Param.Data.expression = FString::SanitizeFloat(UpFG * 10.f, 1);
			bSucceded = true;
			continue;
		}
		else if (Param.Data.name.Equals(PARAM_XCBJT_STR))
		{
			Param.Data.value = FString::SanitizeFloat(DownFG * 10.f, 1);
			Param.Data.expression = FString::SanitizeFloat(DownFG * 10.f, 1);
			bSucceded = true;
			continue;
		}
		else if (Param.Data.name.Equals(PARAM_ZCBJT_STR))
		{
			Param.Data.value = FString::SanitizeFloat(LeftFG * 10.f, 1);
			Param.Data.expression = FString::SanitizeFloat(LeftFG * 10.f, 1);
			bSucceded = true;
			continue;
		}
		else if (Param.Data.name.Equals(PARAM_YCBJT_STR))
		{
			Param.Data.value = FString::SanitizeFloat(RightFG * 10.f, 1);
			Param.Data.expression = FString::SanitizeFloat(RightFG * 10.f, 1);
			bSucceded = true;
			continue;
		}

	}
	return bSucceded;
}


double UDSCupboardLibrary::GetDistanceToFloorParameter(UDSBaseModel* InModel)
{
	if (!InModel)
	{
		return 0.f;
	}
	UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(InModel);
	if (!CupboardModel)
	{
		return 0.f;
	}
	return GetDistanceToFloorParameter(CupboardModel->GetComponentTreeDataRef()->ComponentParameters);
}

double UDSCupboardLibrary::GetDistanceToFloorParameter(const TArray<FParameterData>& Parameters)
{
	for (const auto& Iter: Parameters)
	{
		if (Iter.Data.name.Equals("LDH"))
		{
			double DistanceToFloor = FCString::Atod(*Iter.Data.value) * 0.1f;
			return DistanceToFloor;
		}
	}

	return 0.0;
}

bool UDSCupboardLibrary::WriteDistanceToFloorParameter(const float& InDistance, TArray<FParameterData>& OutParameters, bool bDisableAdaptation)
{
	for (size_t i = 0; i < OutParameters.Num(); i++)
	{
		FParameterData& Param = OutParameters[i];
		if (Param.Data.name.Equals("LDH"))
		{

			double OldValue = FCString::Atoi(*Param.Data.value) * 0.1f;

			if (FMath::IsNearlyEqual(OldValue,InDistance,0.1))
			{
				return false;
			}
			Param.Data.value = FString::SanitizeFloat(InDistance * 10.f, 1);
			Param.Data.expression = FString::SanitizeFloat(InDistance * 10.f, 1);

			if (bDisableAdaptation)
			{
				Param.bEnableAdaptation = false;
			}
			return true;
		}
	}
	return false;
}

#undef PARAM_X 
#undef PARAM_Y 
#undef PARAM_Z 





bool UDSCupboardLibrary::IsFunctionalCupboardModel(UDSBaseModel* Model)
{
	if (!Model || !Model->IsA<UDSCupboardModel>())
	{
		return false;
	}

	UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(Model);
	if (!CupboardModel)
	{
		return false;
	}
	auto& Params = CupboardModel->GetParamsRef();
	return IsFunctionalCupboardModel(Params);
}

bool UDSCupboardLibrary::IsFunctionalCupboardModel(const TArray<FParameterData> & Parameters)
{
	/*临时代码判断挂衣杆为功能件——————LZJ*/
	//
	int32 Code =  GetModelType_Parameters(Parameters);
	EDSModelType ModelType = UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(Code);
	
	if (ModelType == EDSModelType::E_Custom_HangingRod 
		|| ModelType== EDSModelType::E_Custom_FunctionalDrawer || ModelType == EDSModelType::E_Custom_FunctionalDrawer
		|| ModelType == EDSModelType::E_Custom_SideClosurePanel)
	{
		return true;
	}
	//
	int32 ParamPos = Parameters.IndexOfByPredicate([&](const FParameterData& InParam) {
		return InParam.Data.is_enum && InParam.Data.name.Equals(TEXT("LXGNJ"));
		});
	if (ParamPos == INDEX_NONE)
	{
		return false;
	}
	return true;
}

UDSBaseModel* UDSCupboardLibrary::GetModelByUUID(const FString& InUUID, const FDSCupboardModelInfo& InModelInfo)
{
	const TArray<FDSComponentInfo> ComponentInfoArr = InModelInfo.ComponentInfoArr;
	for (auto& Iter : ComponentInfoArr)
	{
		if (Iter.ComponentUUID.Equals(InUUID))
		{
			return Iter.ComponentModel;
		}
		if (!Iter.ComponentModel)
		{
			continue;
		}
		UDSCupboardModel* Model = Cast<UDSCupboardModel>(Iter.ComponentModel);
		if (!Model)
		{
			continue;
		}
		UDSBaseModel* OutModel =  GetModelByUUID(InUUID, Model->GetModelInfo());
		if (!OutModel)
		{
			continue;
		}
		return OutModel;
	}
	return nullptr;
}

UDSBaseModel* UDSCupboardLibrary::GetModelByUUID(const FString& InUUID)
{
	auto Models = UDSMVCSubsystem::GetInstance()->GetAllModels();
	for (auto & M : Models)
	{
		if (M->IsA<UDSCupboardModel>())
		{
			auto CupboardModel = Cast<UDSCupboardModel>(M);
			if (CupboardModel)
			{
				auto ModelTree = CupboardModel->GetComponentTreeDataRef();
				if (InUUID.Equals(ModelTree->UUID))
				{
					return M;
				}
			}
		}
	}
	return nullptr;
}

UDSBaseModel* UDSCupboardLibrary::GetFunctionalRootModel()
{
	auto StateType = UDSMVCSubsystem::GetInstance()->GetStateType();
	if (StateType != EDSFSMState::FSM_CustomCupboard)
	{
		return nullptr;
	}
	auto CustomFiniteState =Cast<UDSFSMCustomEdit>(UDSMVCSubsystem::GetInstance()->GetState());
	if (!CustomFiniteState)
	{
		return nullptr;
	}

	return CustomFiniteState->GetFunctionalRootModel();
}

bool UDSCupboardLibrary::SetFunctionalRootModel(UDSBaseModel* InRootModel)
{
	auto StateType = UDSMVCSubsystem::GetInstance()->GetStateType();
	if (StateType != EDSFSMState::FSM_CustomCupboard)
	{
		return false;
	}
	auto CustomFiniteState = Cast<UDSFSMCustomEdit>(UDSMVCSubsystem::GetInstance()->GetState());
	if (!CustomFiniteState)
	{
		return false;
	}
	CustomFiniteState->SetFunctionalRootModel(InRootModel);
	return true;
}


#define PARAM_CQS "CQS"
#define PARAM_CHS "CHS"
#define PARAM_SQS "SQS"
#define PARAM_SHS "SHS"


bool UDSCupboardLibrary::GetRetractValue(UDSBaseModel* InModel, float& ForwardRetract, float& BackwardRetract)
{
	if (!InModel|| !InModel->IsA<UDSCupboardModel>())
	{
		return false;
	}
	UDSCupboardModel* InCupboardModel = Cast<UDSCupboardModel>(InModel);
	const  auto& ComponentParameters =  InCupboardModel->GetComponentTreeDataRef()->ComponentParameters;
	return  GetRetractValue(ComponentParameters, ForwardRetract, BackwardRetract);
}

bool UDSCupboardLibrary::GetRetractValue(const TArray<FParameterData>& Parameters, float& ForwardRetract, float& BackwardRetract)
{
	ForwardRetract = 0.f;
	BackwardRetract = 0.f;
	bool bExist = false;
	for (auto& Iter : Parameters)
	{
		if (Iter.Data.name.Equals(PARAM_CQS))
		{
			ForwardRetract = FCString::Atod(*Iter.Data.value)*0.1f;
			bExist = true;
		}
		else if(Iter.Data.name.Equals(PARAM_CHS))
		{
			BackwardRetract = FCString::Atod(*Iter.Data.value)*0.1f;
			bExist = true;
		}
		else if(Iter.Data.name.Equals(PARAM_SQS))
		{
			ForwardRetract = FCString::Atod(*Iter.Data.value) * 0.1f;
			bExist = true;
		}
		else if(Iter.Data.name.Equals(PARAM_SHS))
		{
			BackwardRetract = FCString::Atod(*Iter.Data.value) * 0.1f;
			bExist = true;
		}
	}
	return bExist;
}

#undef PARAM_CQS 
#undef PARAM_CHS 
#undef PARAM_SQS 
#undef PARAM_SHS 
bool UDSCupboardLibrary::GetAdaptationIsOverallCollision(UDSBaseModel* InModel, bool DefaultOverall)
{
	if (!InModel || !InModel->IsA<UDSCupboardModel>())
	{
		return false;
	}
	UDSCupboardModel* InCupboardModel = Cast<UDSCupboardModel>(InModel);
	const  auto& ComponentParameters = InCupboardModel->GetComponentTreeDataRef()->ComponentParameters;
	return GetAdaptationIsOverallCollision(ComponentParameters, DefaultOverall);
}
bool UDSCupboardLibrary::GetAdaptationIsOverallCollision(const TArray<FParameterData>& Parameters, bool DefaultOverall)
{
	for (auto Iter : Parameters)
	{
		if (Iter.Data.name.Equals("GNJBWHPD"))
		{
			return  (FCString::Atoi(*Iter.Data.value)==0);
		}
	}
	return DefaultOverall;
}
bool UDSCupboardLibrary::GetIsIngoreCollsion(UDSBaseModel* InModel)
{
	return false;
}
bool UDSCupboardLibrary::GetIsIngoreCollsion(const TArray<FParameterData>& Parameters)
{
	return false;
}

bool UDSCupboardLibrary::CanCupboardApplyStyle(UDSBaseModel* InModel)
{
	UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(InModel);
	if (CupboardModel == nullptr || !CupboardModel->GetModelInfo().ComponentTreeData)
	{
		return false;
	}

	return CupboardModel->GetModelInfo().ComponentTreeData->ComponentParameters.ContainsByPredicate([](const FParameterData& InParam) 
	{ 
		return InParam.Data.name == TEXT("SFFGQD") && FCString::Atoi(*InParam.Data.value) == 1;
	});
}

int UDSCupboardLibrary::GetCornerCutType(const TArray<FParameterData>& Parameters)
{
	for (const auto& Iter : Parameters)
	{
		if (Iter.Data.name.Equals("QJLX"))
		{
			return FCString::Atoi(*Iter.Data.value);
		}
	}
	return 0;
}

TArray<int32> UDSCupboardLibrary::SplitParameterPath(const FString& InPath)
{
	TArray<int32> Result;

	int32 Length = InPath.Len();
	for (int32 i = 0; i < Length; i += 2)
	{
		int32 SubLength = FMath::Min(2, Length - i);
		Result.Add(FCString::Atoi(*InPath.Mid(i, SubLength)));
	}

	Algo::Reverse(Result);
	return Result;
}

UDSBaseModel* UDSCupboardLibrary::FindChildModelByPath(UDSBaseModel* InModel, const FString& InPath)
{
	UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(InModel);
	if (CupboardModel == nullptr)
	{
		return nullptr;
	}

	TArray<int32> ParamIndexes = SplitParameterPath(InPath);
	while (!ParamIndexes.IsEmpty())
	{
		int32 CurrentIndex = ParamIndexes.Pop();
		if (!CupboardModel->GetModelInfoRef().ComponentInfoArr.IsValidIndex(CurrentIndex))
		{
			CupboardModel = nullptr;
			break;
		}

		CupboardModel = Cast<UDSCupboardModel>(CupboardModel->GetModelInfoRef().ComponentInfoArr[CurrentIndex].ComponentModel);
		if (CupboardModel == nullptr)
		{
			break;
		}
	}

	return CupboardModel;
}

bool UDSCupboardLibrary::FindParameterByPath(UDSBaseModel* InModel, const FDSCustomGroupParam& InParamInfo, FParameterData& OutParameterData)
{
	UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(InModel);
	if (CupboardModel == nullptr)
	{
		return false;
	}
	
	TArray<int32> ParamIndexes = SplitParameterPath(InParamInfo.ParamPathInTree);
	while (!ParamIndexes.IsEmpty())
	{
		int32 CurrentIndex = ParamIndexes.Pop();
		if (!CupboardModel->GetModelInfoRef().ComponentInfoArr.IsValidIndex(CurrentIndex))
		{
			return false;
		}

		CupboardModel = Cast<UDSCupboardModel>(CupboardModel->GetModelInfoRef().ComponentInfoArr[CurrentIndex].ComponentModel);
		if (CupboardModel == nullptr)
		{
			return false;
		}
	}

	if (CupboardModel == nullptr)
	{
		return false;
	}
	
	FParameterData* FoundParam = CupboardModel->GetModelInfoRef().ComponentTreeData->ComponentParameters.FindByPredicate([&](const FParameterData& InParam)
	{
		return InParam.Data.name == InParamInfo.ParamName;
	});

	if (FoundParam == nullptr)
	{
		return false;
	}

	OutParameterData = *FoundParam;

	return true;
}

bool UDSCupboardLibrary::FindParameterFromModel(UDSBaseModel* InModel, const FString& InParamName, FParameterData& OutParameterData)
{
	UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(InModel);
	if (CupboardModel == nullptr)
	{
		return false;
	}

	FParameterData* FoundParam = CupboardModel->GetModelInfoRef().ComponentTreeData->ComponentParameters.FindByPredicate([&](const FParameterData& InParam)
	{
		return InParam.Data.name == InParamName;
	});

	if (FoundParam == nullptr)
	{
		return false;
	}

	OutParameterData = *FoundParam;
	return true;
}

FParameterData* UDSCupboardLibrary::FindParameterPointerFromModel(UDSBaseModel* InModel, const FString& InParamName)
{
	UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(InModel);
	if (CupboardModel == nullptr)
	{
		return nullptr;
	}

	return CupboardModel->GetModelInfoRef().ComponentTreeData->ComponentParameters.FindByPredicate([&](const FParameterData& InParam)
	{
		return InParam.Data.name == InParamName;
	});
}

TArray<FParameterEnumTableData> UDSCupboardLibrary::CollectValidSortedEnumTables(const FParameterData& InParam)
{
	TArray<FParameterEnumTableData> Result;
	if (InParam.Data.is_enum == 0)
	{
		return Result;
	}

	TOptional<FDecimal> MinDecimal;
	if (!InParam.Data.min_value.IsEmpty() && InParam.Data.min_value.IsNumeric())
	{
		MinDecimal = FDecimal(InParam.Data.min_value);	
	}

	TOptional<FDecimal> MaxDecimal;
	if (!InParam.Data.max_value.IsEmpty() && InParam.Data.max_value.IsNumeric())
	{
		MaxDecimal = FDecimal(InParam.Data.max_value);
	}

	Result = InParam.EnumData.FilterByPredicate([&](const FParameterEnumTableData& InEnum)
	{
		if (MinDecimal.IsSet() || MaxDecimal.IsSet())
		{
			FDecimal CurrentDecimal(InEnum.value);
			if (MinDecimal.IsSet() && CurrentDecimal < MinDecimal.GetValue())
			{
				return false;
			}

			if (MaxDecimal.IsSet() && CurrentDecimal > MaxDecimal.GetValue())
			{
				return false;
			}
		}

		return true;
	});

	Result.Sort([](const FParameterEnumTableData& A, const FParameterEnumTableData& B)
	{
		return FCString::Atoi(*A.priority) < FCString::Atoi(*B.priority);
	});

	return Result;
}

FString UDSCupboardLibrary::ForceNumericFractionalDigits(const FString& Source, int32 FractionalDigits)
{
	if (FractionalDigits < 0)
	{
		return Source;
	}

	int32 DotPos;
	if (!Source.FindChar('.', DotPos))
	{
		if (FractionalDigits == 0)
		{
			return Source;
		}

		return Source + TEXT(".") + FString::ChrN(FractionalDigits, '0');
	}

	FString IntPart = Source.Left(DotPos);
	FString FracPart = Source.Mid(DotPos + 1);

	if (FracPart.Len() > FractionalDigits)
	{
		FracPart = FracPart.Left(FractionalDigits);
	}
	else if (FracPart.Len() < FractionalDigits)
	{
		FracPart += FString::ChrN(FractionalDigits - FracPart.Len(), '0');
	}

	if (FractionalDigits == 0)
	{
		return IntPart;
	}
	
	return IntPart + TEXT(".") + FracPart;
}

FString UDSCupboardLibrary::GetCustomTypeDisplayName(EDSModelType InType)
{
	switch (InType)
	{
	case EDSModelType::E_Custom_UpperCabinet:			return TEXT("上柜");
	case EDSModelType::E_Custom_WallCabinet:			return TEXT("吊柜");
	case EDSModelType::E_Custom_BaseCabinet:			return TEXT("地柜");
	case EDSModelType::E_Custom_TallCabinet:			return TEXT("高柜");
	case EDSModelType::E_Custom_CornerCabinet:			return TEXT("转角柜");
	case EDSModelType::E_Custom_Tatami:					return TEXT("榻榻米");
	case EDSModelType::E_Custom_DrawerBox:				return TEXT("抽屉盒");
	case EDSModelType::E_Custom_Board:					return TEXT("板件");
	case EDSModelType::E_Custom_FunctionalDrawer:		return TEXT("功能件抽屉");
	case EDSModelType::E_Custom_Functional_Hardware:	return TEXT("功能件五金");
	case EDSModelType::E_Custom_AdjustablePanel:		return TEXT("调整板");
	case EDSModelType::E_Custom_TopClosurePanel:		return TEXT("顶封板");
	case EDSModelType::E_Custom_SideClosurePanel:		return TEXT("侧封板");
	case EDSModelType::E_Custom_Handle:					return TEXT("拉手(五金)");
	case EDSModelType::E_Custom_Hinge:					return TEXT("铰链");
	case EDSModelType::E_Custom_Track:					return TEXT("轨道");
	case EDSModelType::E_Custom_HangingRod:				return TEXT("挂衣杆");
	case EDSModelType::E_Custom_Leg:					return TEXT("地脚");
	case EDSModelType::E_Custom_Hardware:				return TEXT("五金-五金");
	case EDSModelType::E_Custom_RomanColumn:			return TEXT("罗马柱");
	case EDSModelType::E_Custom_TopMolding:				return TEXT("顶线");
	case EDSModelType::E_Custom_BottomTrim:				return TEXT("下拖线");
	case EDSModelType::E_Custom_KickBoard:				return TEXT("踢脚线");
	case EDSModelType::E_Custom_SoftFurniture:			return TEXT("成品家具");
	case EDSModelType::E_Custom_SoftDecoration:			return TEXT("软装饰品");
	case EDSModelType::E_Custom_Faucet:					return TEXT("龙头");
	case EDSModelType::E_Custom_CounterTop:				return TEXT("台面");
	case EDSModelType::E_Custom_CabinetBoard:			return TEXT("柜体板");
	case EDSModelType::E_Custom_DoorCore:				return TEXT("门芯");
	case EDSModelType::E_Custom_Accessory:				return TEXT("附属配件（扔篮子）");
	case EDSModelType::E_Custom_Buckle:					return TEXT("扣手");
	case EDSModelType::E_Custom_LayoutDoor:				return TEXT("户型门");
	case EDSModelType::E_Custom_LayoutDoor_Board:		return TEXT("户型门板");
	case EDSModelType::E_Custom_CornerCutCabinet:		return TEXT("切角柜");
	case EDSModelType::E_Custom_WallBoardCabinet:		return TEXT("护墙板");
	case EDSModelType::E_Custom_DoorPanel:				return TEXT("门板");
	case EDSModelType::E_Custom_DoorPanel_Flat:			return TEXT("平板门板");
	case EDSModelType::E_Custom_DoorPanel_SolidWood:	return TEXT("实木门板");
	case EDSModelType::E_Custom_DoorPanel_AluminumFrame:return TEXT("铝框门板");
	case EDSModelType::E_Custom_DoorPanel_Glass:		return TEXT("玻璃门板");
	case EDSModelType::E_Custom_DoorPanel_Fake:			return TEXT("假门");
	case EDSModelType::E_Custom_Knob:					return TEXT("拉手（把手）");
	default:
		return TEXT("未知");
	}
}

FString UDSCupboardLibrary::GetCustomNodeTypeDisplayName(int32 InType)
{
	EDSModelType Type = UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(InType);

	return GetCustomTypeDisplayName(Type);
}

FString UDSCupboardLibrary::GetRelativeCodeFromModelFileType(int32 InType)
{
	return TEXT("");
}

bool UDSCupboardLibrary::IsSubOfSpecifiedCustomType(const FString& SuperiorType, int32 InSubType)
{
	const TMap<FString, FParameterData>& GlobalParams = UDSMVCSubsystem::GetInstance()->GetGlobalParamsMap();
	if (!GlobalParams.Contains(SuperiorType) || GlobalParams[SuperiorType].Data.is_enum == 0)
	{
		return false;
	}

	return GlobalParams[SuperiorType].EnumData.ContainsByPredicate([&](const FParameterEnumTableData& InEnumData){ return FCString::Atoi(*InEnumData.value) == InSubType; });
}

bool UDSCupboardLibrary::TryGetCodeNameFromCustomType(int32 InCustomType, FString& OutCodeName)
{
	const TMap<FString, FParameterData>& GlobalParams = UDSMVCSubsystem::GetInstance()->GetGlobalParamsMap();
	
	TArray<FString> AllCodeNames = UDSToolLibrary::GetAllCustomCodeNames();
	for (const FString& CodeName : AllCodeNames)
	{
		if (!GlobalParams.Contains(CodeName) || GlobalParams[CodeName].Data.is_enum == 0)
		{
			continue;
		}

		const FParameterEnumTableData* FoundData = GlobalParams[CodeName].EnumData.FindByPredicate([&](const FParameterEnumTableData& InEnumData){ return FCString::Atoi(*InEnumData.value) == InCustomType; });
		if (FoundData != nullptr)
		{
			OutCodeName = CodeName;
			return true;
		}
	}

	return false;
}

void UDSCupboardLibrary::TryGenerateReplacementNodeFromNode(const TSharedPtr<FMultiComponentDataItem>& InNode, const TArray<TPair<FString, TWeakPtr<FMultiComponentDataItem>>>& InNodePath,
	TArray<FDSCustomStyleReplacementNode>& OutStylizedNodes)
{
	FDSCustomStyleReplacementNode NewInfo;
	NewInfo.CustomType = InNode->ModelType;
	NewInfo.FolderId = InNode->ComponentID.GetFormattedValue();
	NewInfo.NodeId = InNode->UUID;

	// 收集样式材质应用风格，检查ModelType是否有效
	if (InNode->ModelType != -1 && UDSCupboardLibrary::TryGetCodeNameFromCustomType(InNode->ModelType, NewInfo.CustomTypeCode))
	{
		// IMPORTANT:只记录有材质参数的节点，这里收集的是材质应用风格
		if (InNode->ComponentParameters.ContainsByPredicate([](const FParameterData& InParam){ return InParam.Data.name == TEXT("DZCZ"); }))
		{
			NewInfo.AssociationType = EDSCustomAssociationType::Material;
			OutStylizedNodes.Push(NewInfo);
		}
				
		NewInfo.AssociationType = EDSCustomAssociationType::Pattern;
				
		// 检查当前类型（Code）是否需要依赖某个上级类型（Code）的关联
		// 如果能找到上级类型（Code），表示需要找到此上级指定类型的节点的FolderId作为查询关联的FolderId
		FString SuperiorTypeCode = UDSNetworkSubsystem::GetInstance()->FindCustomSuperiorTypeCode(InNode->ModelType, EDSCustomAssociationType::Pattern);
		if (!SuperiorTypeCode.IsEmpty())
		{
			int32 FoundPos = InNodePath.FindLastByPredicate([&](const TPair<FString, TWeakPtr<FMultiComponentDataItem>>& InPair)
			{
				return InPair.Key == SuperiorTypeCode;
			});

			// 如果找到了需要的上级节点，将查询用的FolderId修改为上级节点的FolderId
			// 如果没找到有效上级节点，则保持查询用的FolderId为当前节点FolderId，最终会导致这个节点使用风格默认项
			if (FoundPos != INDEX_NONE && InNodePath[FoundPos].Value.IsValid())
			{
				NewInfo.FolderId = InNodePath[FoundPos].Value.Pin()->ComponentID.GetFormattedValue();
			}
		}

		OutStylizedNodes.Push(NewInfo);
	}
}

bool UDSCupboardLibrary::ReplaceCustomNodeWithFolderId(const TSharedPtr<FMultiComponentDataItem>& InNode, const FString& FolderId)
{
	if (!InNode || FolderId.IsEmpty())
	{
		return false;
	}

	FString NewFilePath = FPaths::Combine(FPaths::ProjectSavedDir(), TEXT("Resources"), FString::Printf(TEXT("%s.dat"), *FolderId));

	FRefToLocalFileData NewFileData;
	if (!IFileManager::Get().FileExists(*NewFilePath) || !FProtobufOperatorFunctionLibrary::LoadRelationFromFile(NewFilePath, NewFileData))
	{
		UE_LOG(LogTemp, Error, TEXT("Custom file [%s] does not exists!"), *FolderId);
		return false;
	}

	// 将节点信息替换为新文件中的信息
	InNode->ComponentID.Value = NewFileData.FolderDBData.folder_id;
	InNode->ComponentID.Expression = NewFileData.FolderDBData.folder_id;
	InNode->ComponentName = NewFileData.FolderDBData.folder_name;
	InNode->Code = NewFileData.FolderDBData.folder_code;
	InNode->CodeExp = NewFileData.FolderDBData.folder_code_exp;

	// 覆盖新旧节点同时存在的参数，不覆盖类型参数
	TArray<FString> TypeCodeNames = UDSToolLibrary::GetAllCustomCodeNames();
	TArray<FParameterData> OldDoorParams = InNode->ComponentParameters;
	InNode->ComponentParameters = NewFileData.ParamDatas;
	for (FParameterData& NewParam : InNode->ComponentParameters)
	{
		FParameterData* ExistsParam = OldDoorParams.FindByPredicate([NewParam, TypeCodeNames](const FParameterData& InParam)
		{
			return InParam.Data.name == NewParam.Data.name && !TypeCodeNames.Contains(InParam.Data.name);
		});

		if (ExistsParam != nullptr)
		{
			NewParam.Data.value = ExistsParam->Data.value;
			NewParam.Data.expression = ExistsParam->Data.expression;
			NewParam.Data.DefaultExpress = ExistsParam->Data.DefaultExpress;

			NewParam.Data.editable = ExistsParam->Data.editable;
			NewParam.Data.editable_exp = ExistsParam->Data.editable_exp;

			NewParam.Data.visibility = ExistsParam->Data.visibility;
			NewParam.Data.visibility_exp = ExistsParam->Data.visibility_exp;
		}
	}

	// 从文件获取新Type
	InNode->ModelType = UDSCupboardLibrary::GetModelType_Parameters(InNode->ComponentParameters);

	// 替换节点下挂载的所有子节点
	InNode->ChildComponent.Empty();
	for (const FRefToFileComponentData& ChildComponentData : NewFileData.ComponentDatas)
	{
		InNode->ChildComponent.Add(MakeShared<FMultiComponentDataItem>(ChildComponentData.ConvertToMultiComponentDataItem()));
	}

	return true;
}

bool UDSCupboardLibrary::IsDoorPanelDrawer(UDSCupboardModel* InModel)
{
	if (InModel == nullptr)
	{
		return false;
	}
	
	if (IsSubOfSpecifiedCustomType(TEXT("LXMB"), InModel->GetModelInfoRef().ComponentTreeData->ModelType))
	{
		if (FParameterData* FoundParam = InModel->GetModelInfoRef().ComponentTreeData->ComponentParameters.FindByPredicate([&](const FParameterData& InParam){ return InParam.Data.name == TEXT("MBLX"); }))
		{
			return FCString::Atoi(*FoundParam->Data.GetFormattedValue()) == 3;
		}
	}

	return false;
}

UDSBaseModel* UDSCupboardLibrary::FindModelByMultiComponentDataUUID(const FString& InUUID)
{
	auto AllModels = UDSMVCSubsystem::GetInstance()->GetAllModels();
	for (auto & Iter : AllModels)
	{
		if (UDSToolLibrary::IsCustomCupboardType(Iter->GetModelType()))
		{
			auto CupboardModel = Cast<UDSCupboardModel>(Iter);
			if (CupboardModel && CupboardModel->GetComponentTreeDataRef()->UUID.Equals(InUUID))
			{
				return CupboardModel;
			}
		}
	}
	return nullptr;
}

void UDSCupboardLibrary::CollectAllStylizedNodeInfos(const TSharedPtr<FMultiComponentDataItem>& InNode,
	TArray<FDSCustomStyleReplacementNode>& OutStylizedNodes,
	TArray<TPair<FString, TWeakPtr<FMultiComponentDataItem>>>& NodePath)
{
	if (!InNode)
	{
		return;
	}

	struct FScopedNodePath
	{
		bool bValidNodeTypeCode;
		FString NodeTypeCode;

		TArray<TPair<FString, TWeakPtr<FMultiComponentDataItem>>>& NodePathRef;

		FScopedNodePath(const TSharedPtr<FMultiComponentDataItem>& InNode, TArray<TPair<FString, TWeakPtr<FMultiComponentDataItem>>>& InNodePath)
			: bValidNodeTypeCode(false)
			, NodePathRef(InNodePath)
		{
			bValidNodeTypeCode = UDSCupboardLibrary::TryGetCodeNameFromCustomType(InNode->ModelType, NodeTypeCode);
			if (bValidNodeTypeCode)
			{
				NodePathRef.Push({NodeTypeCode, InNode});
			}
		}

		~FScopedNodePath()
		{
			if (bValidNodeTypeCode)
			{
				NodePathRef.Pop();
			}
		}
		
	};

	FScopedNodePath ScopedNodePath(InNode, NodePath);

	TryGenerateReplacementNodeFromNode(InNode, NodePath, OutStylizedNodes);

	for (const TSharedPtr<FMultiComponentDataItem>& Child : InNode->ChildComponent)
	{
		CollectAllStylizedNodeInfos(Child, OutStylizedNodes, NodePath);
	}
}

bool UDSCupboardLibrary::RecursiveFindDoorCoreNode(const TSharedPtr<FMultiComponentDataItem>& InNode, TSharedPtr<FMultiComponentDataItem>& OutNode)
{
	if (!InNode)
	{
		return false;
	}

	// 门芯和玻璃门芯
	if (InNode->ComponentVisibility.GetFormattedValue().Equals(TEXT("1")))
	{
		if (IsSubOfSpecifiedCustomType(TEXT("LXMX"), InNode->ModelType) || IsSubOfSpecifiedCustomType(TEXT("LXBLMX"), InNode->ModelType))
		{
			OutNode = InNode;
			return true;
		}
	}
	
	for (const TSharedPtr<FMultiComponentDataItem>& Child : InNode->ChildComponent)
	{
		if (InNode->ComponentVisibility.GetFormattedValue().Equals(TEXT("1")))
		{
			if (RecursiveFindDoorCoreNode(Child, OutNode))
			{
				return true;
			}
		}
	}

	return false;
}

void UDSCupboardLibrary::CollectTreeRelationship(UDSCupboardModel* RootModel,
	const TSharedPtr<FMultiComponentDataItem>& RootNode,
	const TSharedPtr<FMultiComponentDataItem>& BelongsNode,
	TMap<TSharedPtr<FMultiComponentDataItem>, UDSCupboardModel*>& OutBelongsModel,
	TMap<TSharedPtr<FMultiComponentDataItem>, TSharedPtr<FMultiComponentDataItem>>& OutBelongsNode)
{
	if (RootModel == nullptr || !RootNode)
	{
		return;
	}

	OutBelongsModel.Add(RootNode, RootModel);
	OutBelongsNode.Add(RootNode, BelongsNode);

	for (const TSharedPtr<FMultiComponentDataItem>& ChildNode : RootNode->ChildComponent)
	{
		const FDSComponentInfo* FoundChildModel = RootModel->GetModelInfo().ComponentInfoArr.FindByPredicate([&ChildNode](const FDSComponentInfo& Info)
		{
			if (UDSCupboardModel* CastedModel = Cast<UDSCupboardModel>(Info.ComponentModel))
			{
				return CastedModel->GetModelInfoRef().ComponentTreeData == ChildNode;
			}

			return false;
		});

		CollectTreeRelationship(FoundChildModel == nullptr ? RootModel : Cast<UDSCupboardModel>(FoundChildModel->ComponentModel), ChildNode, RootNode, OutBelongsModel, OutBelongsNode);
	}
}

bool UDSCupboardLibrary::CheckSizeForNewNodeIsValid(const TSharedPtr<FMultiComponentDataItem>& InNode, const FString& FolderId, const TMap<FString, FParameterData>& ParentParams)
{
	if (!InNode || FolderId.IsEmpty() || ParentParams.IsEmpty())
	{
		return false;
	}
	
	FString NewFilePath = FPaths::Combine(FPaths::ProjectSavedDir(), TEXT("Resources"), FString::Printf(TEXT("%s.dat"), *FolderId));

	FRefToLocalFileData NewFileData;
	if (!IFileManager::Get().FileExists(*NewFilePath) || !FProtobufOperatorFunctionLibrary::LoadRelationFromFile(NewFilePath, NewFileData))
	{
		UE_LOG(LogDSCupboardLibrary, Error, TEXT("Custom file [%s] does not exists!"), *FolderId);
		return false;
	}

	FMultiComponentDataItem NewData;
	ConstructTreeRootNode(NewFileData, NewData);

	for (FParameterData& Parameter : NewData.ComponentParameters)
	{
		FParameterData* OldParam = InNode->ComponentParameters.FindByPredicate([&](const FParameterData& InParam){ return Parameter.Data.name == InParam.Data.name; });
		if (OldParam != nullptr)
		{
			Parameter.Data.expression = OldParam->Data.expression;
			Parameter.Data.value = OldParam->Data.value;
		}
	}

	if (!FGeometryDatas::CalculateCurMultiComponentData(UDSMVCSubsystem::GetInstance()->GetGlobalParamsMap(), ParentParams, NewData))
	{
		UE_LOG(LogDSCupboardLibrary, Error, TEXT("Calculate custom component[%s] parameters failed"), *FolderId);
		return false;
	}

	FParameterData* OldWidth = InNode->ComponentParameters.FindByPredicate([&](const FParameterData& InParam){ return InParam.Data.name == TEXT("W"); });
	FParameterData* OldDepth = InNode->ComponentParameters.FindByPredicate([&](const FParameterData& InParam){ return InParam.Data.name == TEXT("D"); });
	FParameterData* OldHeight = InNode->ComponentParameters.FindByPredicate([&](const FParameterData& InParam){ return InParam.Data.name == TEXT("H"); });

	FParameterData* NewWidth = NewData.ComponentParameters.FindByPredicate([&](const FParameterData& InParam){ return InParam.Data.name == TEXT("W"); });
	FParameterData* NewDepth = NewData.ComponentParameters.FindByPredicate([&](const FParameterData& InParam){ return InParam.Data.name == TEXT("D"); });
	FParameterData* NewHeight = NewData.ComponentParameters.FindByPredicate([&](const FParameterData& InParam){ return InParam.Data.name == TEXT("H"); });

	if (OldWidth != nullptr && NewWidth != nullptr)
	{
		FDecimal OldWidthValue(OldWidth->Data.value);
		if (!NewWidth->Data.min_value.IsEmpty() && OldWidthValue < FDecimal(NewWidth->Data.min_value))
		{
			return false;
		}

		if (!NewWidth->Data.max_value.IsEmpty() && OldWidthValue > FDecimal(NewWidth->Data.max_value))
		{
			return false;
		}
	}

	if (OldDepth != nullptr && NewDepth != nullptr)
	{
		FDecimal OldDepthValue(OldDepth->Data.value);
		if (!NewDepth->Data.min_value.IsEmpty() && OldDepthValue < FDecimal(NewDepth->Data.min_value))
		{
			return false;
		}

		if (!NewDepth->Data.max_value.IsEmpty() && OldDepthValue > FDecimal(NewDepth->Data.max_value))
		{
			return false;
		}
	}

	if (OldHeight != nullptr && NewHeight != nullptr)
	{
		FDecimal OldHeightValue(OldHeight->Data.value);
		if (!NewHeight->Data.min_value.IsEmpty() && OldHeightValue < FDecimal(NewHeight->Data.min_value))
		{
			return false;
		}

		if (!NewHeight->Data.max_value.IsEmpty() && OldHeightValue > FDecimal(NewHeight->Data.max_value))
		{
			return false;
		}
	}

	return true;
}


UDSCupboardModel* UDSCupboardLibrary::GetModelByNodeUUID(const FString& InUUID, UDSCupboardModel* InModel)
{
	if (InModel && InModel->GetModelInfoRef().ComponentTreeData->UUID == InUUID)
		return InModel;

	return Cast<UDSCupboardModel>(GetModelByUUID(InUUID, InModel->GetModelInfoRef()));
}

UDSCupboardModel* UDSCupboardLibrary::GetModelByNodeUUID(const FString& InUUID)
{
	auto AllCupboardModels = UDSMVCSubsystem::GetInstance()->GetAllModels();
	for (auto & Model : AllCupboardModels)
	{
		if (Model->IsA<UDSCupboardModel>())
		{
			auto CupboardModel = Cast<UDSCupboardModel>(Model);
			if (CupboardModel)
			{
				auto ModelTree = CupboardModel->GetModelInfoRef().ComponentTreeData;
				if (ModelTree && ModelTree->UUID.Equals(InUUID))
				{
					return CupboardModel;
				}
			}
		}
	}
	return nullptr;
}

void UDSCupboardLibrary::GetAllChildrenModels(const TSharedPtr<FMultiComponentDataItem>& InModelTree, TArray<UDSBaseModel*>& OutModels)
{

	if (InModelTree == nullptr)
	{
		return;
	}
	if (InModelTree->ChildComponent.IsEmpty())
	{
		return;
	}
	for (auto & Iter : InModelTree->ChildComponent)
	{
		auto UUID = Iter->UUID;
		auto ChildModel = GetModelByUUID(UUID);
		if (ChildModel && ChildModel->IsA<UDSCupboardModel>())
		{
			OutModels.Add(ChildModel);
		}
		GetAllChildrenModels(Iter, OutModels);
	}
}

TSharedPtr<FMultiComponentDataItem> UDSCupboardLibrary::GetNodeByNodeUUID(const TSharedPtr<FMultiComponentDataItem>& InRootNode, const FString& InNode)
{
	if (InRootNode->UUID == InNode)
		return InRootNode;

	for (auto Ite : InRootNode->ChildComponent)
	{
		TSharedPtr<FMultiComponentDataItem> ResultNode = GetNodeByNodeUUID(Ite, InNode);
		if (ResultNode.IsValid())
			return ResultNode;
	}

	return nullptr;
}

void UDSCupboardLibrary::CollectNodesToDisableCollision(const TSharedPtr<FMultiComponentDataItem>& InNode, bool bParentDisabled, TArray<FString>& OutNodes)
{
	if (!InNode)
	{
		return;
	}

	bool bShouldCloseCollision = false;
	if (FParameterData* FoundParam = InNode->ComponentParameters.FindByPredicate([](const FParameterData& InParam) { return InParam.Data.name == TEXT("QXPZ"); }))
	{
		bShouldCloseCollision = FoundParam->Data.GetFormattedValue() == TEXT("1");
	}

	bool bDisabled = bParentDisabled || bShouldCloseCollision;
	if (bDisabled)
	{
		OutNodes.Add(InNode->UUID);
	}

	for (const TSharedPtr<FMultiComponentDataItem>& ChildNode : InNode->ChildComponent)
	{
		CollectNodesToDisableCollision(ChildNode, bDisabled, OutNodes);
	}
}

UDSBaseModel* UDSCupboardLibrary::FindNearestFunctionalCupboardModel(UDSBaseModel* InModel)
{
	UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(InModel);
	if (CupboardModel == nullptr)
	{
		return nullptr;
	}

	UDSCupboardModel* CurrentModel = Cast<UDSCupboardModel>(CupboardModel->GetOwnerModel());
	while (CurrentModel != nullptr)
	{
		if (CurrentModel->IsFunctionalCupboardModel())
		{
			break;
		}

		CurrentModel = Cast<UDSCupboardModel>(CurrentModel->GetOwnerModel());
	}

	return CurrentModel;
}
void UDSCupboardLibrary::GetAllNodes(const TSharedPtr<FMultiComponentDataItem>& InRootNode, TArray<TSharedPtr<FMultiComponentDataItem>>& OutNodes)
{
	OutNodes.Add(InRootNode);
	for (auto ChildIte : InRootNode->ChildComponent)
	{
		GetAllNodes(ChildIte, OutNodes);
	}
}

FVector UDSCupboardLibrary::GetFixedSizeParameter(const UDSCupboardModel* InModel)
{
	if (InModel == nullptr)
	{
		return FVector::ZeroVector;
	}

	const FDSCupboardModelInfo& ModelInfo = InModel->GetModelInfo();

	return GetFixedSizeParameter(ModelInfo.ComponentTreeData);
}

FVector UDSCupboardLibrary::GetFixedSizeParameter(const TSharedPtr<FMultiComponentDataItem> InNode)
{
	auto FindAxisLength = [](const TArray<FParameterData>& Parameters, const FString& ParamName) ->FDecimal
		{
			if (const FParameterData* FoundParam = Parameters.FindByPredicate([&](const FParameterData& InParam) { return InParam.Data.name == ParamName; }))
			{
				return FDecimal(FoundParam->Data.value) * 0.1;
			}

			return FDecimal(0);
		};

	//const FDSCupboardModelInfo& ModelInfo = InModel->GetModelInfo();

	FVector Size;
	Size.X = FindAxisLength(InNode->ComponentParameters, TEXT("WIDTH")).ToDouble();
	Size.Y = FindAxisLength(InNode->ComponentParameters, TEXT("DEPTH")).ToDouble();
	Size.Z = FindAxisLength(InNode->ComponentParameters, TEXT("HEIGHT")).ToDouble();

	return Size;
}

FVector UDSCupboardLibrary::GetFixedOffsetParameter(const UDSCupboardModel* InModel)
{
	if (InModel == nullptr)
	{
		return FVector::ZeroVector;
	}

	const FDSCupboardModelInfo& ModelInfo = InModel->GetModelInfo();

	return GetFixedOffsetParameter(ModelInfo.ComponentTreeData);
}

FVector UDSCupboardLibrary::GetFixedOffsetParameter(const TSharedPtr<FMultiComponentDataItem> InNode)
{

	auto FindAxisOffset = [](const TArray<FParameterData>& Parameters, const FString& ParamName) -> FDecimal
		{
			if (const FParameterData* FoundParam = Parameters.FindByPredicate([&](const FParameterData& InParam) { return InParam.Data.name == ParamName; }))
			{
				return FDecimal(FoundParam->Data.value) * 0.1;
			}

			return FDecimal(0);
		};

	//const FDSCupboardModelInfo& ModelInfo = InModel->GetModelInfo();

	FVector Offset;
	Offset.X = FindAxisOffset(InNode->ComponentParameters, TEXT("GDOW")).ToDouble();
	Offset.Y = FindAxisOffset(InNode->ComponentParameters, TEXT("GDOD")).ToDouble();
	Offset.Z = FindAxisOffset(InNode->ComponentParameters, TEXT("GDOH")).ToDouble();

	return Offset;
}

bool UDSCupboardLibrary::IsDrawerOpening(UDSCupboardModel* InModel)
{
	if (InModel == nullptr)
	{
		return false;
	}

	FParameterData* FoundParam = InModel->GetModelInfoRef().ComponentTreeData->ComponentParameters.FindByPredicate([](const FParameterData& InParam){ return InParam.Data.name == TEXT("DKJD"); });
	if (FoundParam == nullptr)
	{
		return false;
	}

	return FCString::Atoi(*FoundParam->Data.value) != 0;
}

bool UDSCupboardLibrary::RecursiveGetTreeNodePath(const TSharedPtr<FMultiComponentDataItem>& RootNode,
	const TSharedPtr<FMultiComponentDataItem>& TargetNode,
	TArray<TPair<FString, TWeakPtr<FMultiComponentDataItem>>>& OutPath)
{
	if (RootNode == nullptr)
	{
		return false;
	}

	FString TypeCode;
	TryGetCodeNameFromCustomType(RootNode->ModelType, TypeCode);
	OutPath.Push({ TypeCode, RootNode });

	if (RootNode == TargetNode)
	{
		return true;
	}

	for (const TSharedPtr<FMultiComponentDataItem>& Child : RootNode->ChildComponent)
	{
		if (RecursiveGetTreeNodePath(Child, TargetNode, OutPath))
		{
			return true;
		}
	}
	
	OutPath.Pop();
	return false;
}

bool UDSCupboardLibrary::IsDoorBoardType(int32 ModelType)
{
	return UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), ModelType);
}

TOptional<bool> UDSCupboardLibrary::ToggleDrawerState(UDSBaseModel* InModel)
{
	bool bInMultiModel = false;
	bool bNeedCreateNewMultiModel = false;

	FString SelectedModelId;
	
	TArray<UDSBaseModel*> DrawerModels;
	if (UDSMultiModel* MultiModel = Cast<UDSMultiModel>(InModel))
	{
		bNeedCreateNewMultiModel = true;
		
		TArray<UDSBaseModel*> IncludeModels = MultiModel->GetIncludeModel();
		for (int32 Index = 0; Index < IncludeModels.Num(); Index++)
		{
			UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(IncludeModels[Index]);
			if (CupboardModel == nullptr)
			{
				bNeedCreateNewMultiModel = false;
				continue;
			}

			if (CupboardModel->GetModelType() == EDSModelType::E_Custom_FunctionalDrawer)
			{
				DrawerModels.AddUnique(CupboardModel);
			}
			else if (CupboardModel == CupboardModel->GetRootCupboardModel())
			{
				bNeedCreateNewMultiModel = false;

				TArray<UDSCupboardModel*> Queue = { CupboardModel };
				while (!Queue.IsEmpty())
				{
					UDSCupboardModel* CurrentModel = Queue.Pop();
					if (CurrentModel->GetModelType() == EDSModelType::E_Custom_FunctionalDrawer)
					{
						DrawerModels.AddUnique(CurrentModel);
					}
				
					for (const FDSComponentInfo& Info : CurrentModel->GetModelInfoRef().ComponentInfoArr)
					{
						UDSCupboardModel* ChildModel = Cast<UDSCupboardModel>(Info.ComponentModel);
						if (ChildModel != nullptr && ChildModel->GetModelType() == EDSModelType::E_Custom_FunctionalDrawer)
						{
							DrawerModels.AddUnique(ChildModel);
						}
					}
				}
			}
			else
			{
				bNeedCreateNewMultiModel = false;
			}
		}

		bInMultiModel = true;
	}
	else if (UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(InModel))
	{
		SelectedModelId = CupboardModel->GetComponentTreeDataRef()->UUID;
		if (CupboardModel == CupboardModel->GetRootCupboardModel())
		{
			TArray<UDSCupboardModel*> Queue = { CupboardModel };
			while (!Queue.IsEmpty())
			{
				UDSCupboardModel* CurrentModel = Queue.Pop();
				if (CurrentModel->GetModelType() == EDSModelType::E_Custom_FunctionalDrawer)
				{
					DrawerModels.AddUnique(CurrentModel);
				}
				
				for (const FDSComponentInfo& Info : CurrentModel->GetModelInfoRef().ComponentInfoArr)
				{
					UDSCupboardModel* ChildModel = Cast<UDSCupboardModel>(Info.ComponentModel);
					if (ChildModel != nullptr && ChildModel->GetModelType() == EDSModelType::E_Custom_FunctionalDrawer)
					{
						DrawerModels.AddUnique(ChildModel);
					}
				}
			}
		}
		else if (CupboardModel->GetModelType() == EDSModelType::E_Custom_FunctionalDrawer)
		{
			DrawerModels.AddUnique(InModel);
		}
	}
	else if (InModel == nullptr || (InModel->GetModelType() == EDSModelType::E_House_Wall || InModel->GetModelType() == EDSModelType::E_House_Area))
	{
		DrawerModels = UDSMVCSubsystem::GetInstance()->GetModels(EDSModelType::E_Custom_FunctionalDrawer);	
	}

	bool bShouldOpenDrawer = DrawerModels.ContainsByPredicate([](UDSBaseModel* DrawerModel){ return !IsDrawerOpening(Cast<UDSCupboardModel>(DrawerModel)); });

	FString RevokeUUID = FGuid::NewGuid().ToString();

	TSet<UDSCupboardModel*> ProcessedRootModels;
	TArray<FString> DrawerIds;
	for (UDSBaseModel* Model : DrawerModels)
	{
		UDSCupboardModel* DrawerModel = Cast<UDSCupboardModel>(Model);

		FParameterData* FoundParam = DrawerModel->GetModelInfoRef().ComponentTreeData->ComponentParameters.FindByPredicate([](const FParameterData& InParam){ return InParam.Data.name == TEXT("DKJD"); });
		if (FoundParam == nullptr)
		{
			continue;
		}

		bool bIsDrawerOpening = FCString::Atoi(*FoundParam->Data.value) != 0;
		if (bIsDrawerOpening == bShouldOpenDrawer)
		{
			continue;
		}

		UDSCupboardModel* RootModel = DrawerModel->GetRootCupboardModel();
		if (!ProcessedRootModels.Contains(RootModel))
		{
			ProcessedRootModels.Add(RootModel);

			FDSRevokePushData PushData(EDSPushDataType::E_Custom, FDSModelExecuteType::ExecuteUpdateSelf, false);
			UDSRevokeLibrary::UpdatePushDataPropertyUnion(RootModel, PushData.ExecuteType, {}, PushData);
			UDSMVCSubsystem::GetInstance()->OnExecuteFromFunctionBarOrPropertyWidgetUnion(RootModel, FDSModelExecuteType::ExecuteUpdateSelf, PushData, RevokeUUID, false);
		}
		
		FoundParam->Data.value = bShouldOpenDrawer ? TEXT("1") : TEXT("0");
		FoundParam->Data.expression = FoundParam->Data.value;

		DrawerIds.Add(DrawerModel->GetModelInfoRef().ComponentTreeData->UUID);
	}

	for (UDSCupboardModel* RootModel : ProcessedRootModels)
	{
		RootModel->OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf, FDSBroadcastMarkData::OnlyOutlineBroadcastMark);
	}

	TArray<UDSBaseModel*> DrawerModelsForSelect;
	TArray<UDSBaseModel*> AllDrawerModels = UDSMVCSubsystem::GetInstance()->GetModels(EDSModelType::E_Custom_FunctionalDrawer);
	for (UDSBaseModel* Model : AllDrawerModels)
	{
		UDSCupboardModel* DrawerModel = Cast<UDSCupboardModel>(Model);
		if (DrawerModel == nullptr)
		{
			continue;
		}

		if (DrawerIds.Contains(DrawerModel->GetModelInfoRef().ComponentTreeData->UUID))
		{
			DrawerModelsForSelect.Add(DrawerModel);
		}
	}

	if (InModel != nullptr)
	{
		if (DrawerModelsForSelect.Num() == 1 && !bInMultiModel)
		{
			if (Cast<UDSCupboardModel>(DrawerModelsForSelect[0])->GetComponentTreeDataRef()->UUID == SelectedModelId)
			{
				UDSMVCSubsystem::GetInstance()->SetCurrentModel(DrawerModelsForSelect[0]);
			}
		}
		else if (bNeedCreateNewMultiModel)
		{
			UDSMultiModel* NewMultiModel = UDSMultiModel::CreateMultiModel();
			NewMultiModel->AddSelect(DrawerModelsForSelect);
			UDSMVCSubsystem::GetInstance()->SetCurrentModel(NewMultiModel);
		}
		else if (UDSMultiModel* MultiModel = Cast<UDSMultiModel>(InModel))
		{
			TArray<UDSBaseModel*> IncludeModels = MultiModel->GetIncludeModel();
			for (UDSBaseModel* ModelForSelect : DrawerModelsForSelect)
			{
				bool bDrawerRootInMulti = IncludeModels.ContainsByPredicate([&](UDSBaseModel* IncludeModel)
				{
					UDSCupboardModel* DrawerModel = Cast<UDSCupboardModel>(ModelForSelect);
					UDSCupboardModel* ModelInMulti = Cast<UDSCupboardModel>(IncludeModel);
					return DrawerModel != nullptr && ModelInMulti != nullptr && DrawerModel->GetRootCupboardModel() == ModelInMulti;
				});

				if (!bDrawerRootInMulti)
				{
					MultiModel->AddSelect(ModelForSelect);
				}
			}
		}

		UDSMVCSubsystem::GetInstance()->GetCurrentModel()->OnExecuteAction(FDSModelExecuteType::ExecuteSelect, FDSBroadcastMarkData::SelectBroadcastMark);
		UDSUISubsystem::GetInstance()->ProcessStateEvent(UDSMVCSubsystem::GetInstance()->GetCurrentModel(), EUIOperationType::Selected, UDSMVCSubsystem::GetInstance()->GetStateType());
	}
	
	if (DrawerModelsForSelect.IsEmpty())
	{
		return {};
	}
	else
	{
		return bShouldOpenDrawer;
	}
}

bool UDSCupboardLibrary::ShouldNodeHideInGame(const TSharedPtr<FMultiComponentDataItem>& RootNode, const TSharedPtr<FMultiComponentDataItem>& TargetNode)
{
	TArray<TPair<FString, TWeakPtr<FMultiComponentDataItem>>> NodePath;
	if (!RecursiveGetTreeNodePath(RootNode, TargetNode, NodePath))
	{
		return false;
	}

	for (int32 Index = NodePath.Num() - 1; Index >= 0; --Index)
	{
		if (NodePath[Index].Value.Pin()->bHiden)
		{
			return true;
		}
	}

	return false;
}

FTransform UDSCupboardLibrary::GetNodeTransform(UDSCupboardModel* InRootModel, const TSharedPtr<FMultiComponentDataItem>& InTargetNode)
{
	FTransform RootTrans = FTransform(InRootModel->GetProperty()->GetTransformProperty().Rotation, InRootModel->GetProperty()->GetTransformProperty().Location);
	TArray<TSharedPtr<FMultiComponentDataItem>> ComponentPath;
	InRootModel->CollectComponentPath_Public(InRootModel->GetModelInfo().ComponentTreeData, InTargetNode, ComponentPath);

	FTransform ComponentRelativeTransform;
	if (ComponentPath.IsValidIndex(0))
	{
		ComponentRelativeTransform.SetLocation(ComponentPath[0]->ComponentLocation.GetLocation());
		ComponentRelativeTransform.SetRotation(ComponentPath[0]->ComponentRotation.GetRotation().Quaternion());
		ComponentRelativeTransform.SetScale3D(ComponentPath[0]->ComponentScale.GetScale());
		//ComponentRelativeTransform = RelativeTransform * ComponentRelativeTransform;
		for (int32 Index = 1; Index < ComponentPath.Num(); ++Index)
		{
			FTransform CurrentTransform;
			CurrentTransform.SetLocation(ComponentPath[Index]->ComponentLocation.GetLocation());
			CurrentTransform.SetRotation(ComponentPath[Index]->ComponentRotation.GetRotation().Quaternion());
			CurrentTransform.SetScale3D(ComponentPath[Index]->ComponentScale.GetScale());

			ComponentRelativeTransform = CurrentTransform * ComponentRelativeTransform;
		}
	}
	ComponentRelativeTransform = ComponentRelativeTransform * RootTrans;
	return ComponentRelativeTransform;
}

bool UDSCupboardLibrary::CupboardIntersect(const TArray<FVector>& CubeVerticesA, const TArray<FVector>& CubeVerticesB)
{
	if (CubeVerticesA.Num()!= 8 || CubeVerticesB.Num() != 8)
	{
		return false;
	}

	struct FOBB
	{
		FVector Center;    // 中心点
		FVector AxisX;     // X轴（单位向量）
		FVector AxisY;     // Y轴（单位向量）
		FVector AxisZ;     // Z轴（单位向量）
		FVector HalfExtent; // 半长（各轴方向）
	};

	auto BuildOBB = [&](const TArray<FVector>& Vertices)
		{
			FOBB Obb;

			// 计算中心点
			Obb.Center = FVector::ZeroVector;
			for (const FVector& Vert : Vertices)
			{
				Obb.Center += Vert;
			}
			Obb.Center /= Vertices.Num();

			// 根据新的顶点顺序计算轴向
			// 顶面：0-3顺时针 (0,1,2,3)
			// 底面：4-7顺时针 (4,5,6,7)
			FVector EdgeX = Vertices[3] - Vertices[0]; // 点0到点3（X方向）
			FVector EdgeY = Vertices[1] - Vertices[0]; // 点0到点1（Y方向）
			FVector EdgeZ = Vertices[4] - Vertices[0]; // 点0到点4（Z方向）

			// 归一化轴向
			Obb.AxisX = EdgeX.GetSafeNormal();
			Obb.AxisY = EdgeY.GetSafeNormal();
			Obb.AxisZ = EdgeZ.GetSafeNormal();

			// 确保三个轴向互相垂直（修正Z轴）
			Obb.AxisZ = FVector::CrossProduct(Obb.AxisX, Obb.AxisY).GetSafeNormal();
			Obb.AxisY = FVector::CrossProduct(Obb.AxisZ, Obb.AxisX).GetSafeNormal();
			Obb.AxisX = FVector::CrossProduct(Obb.AxisY, Obb.AxisZ).GetSafeNormal();

			// 计算半长（所有顶点在轴上的投影范围）
			FVector MinProjection(FLT_MAX, FLT_MAX, FLT_MAX);
			FVector MaxProjection(-FLT_MAX, -FLT_MAX, -FLT_MAX);

			for (const FVector& Vert : Vertices)
			{
				FVector RelVert = Vert - Obb.Center;
				float ProjX = FVector::DotProduct(RelVert, Obb.AxisX);
				float ProjY = FVector::DotProduct(RelVert, Obb.AxisY);
				float ProjZ = FVector::DotProduct(RelVert, Obb.AxisZ);

				MinProjection.X = FMath::Min(MinProjection.X, ProjX);
				MaxProjection.X = FMath::Max(MaxProjection.X, ProjX);

				MinProjection.Y = FMath::Min(MinProjection.Y, ProjY);
				MaxProjection.Y = FMath::Max(MaxProjection.Y, ProjY);

				MinProjection.Z = FMath::Min(MinProjection.Z, ProjZ);
				MaxProjection.Z = FMath::Max(MaxProjection.Z, ProjZ);
			}

			Obb.HalfExtent.X = 0.5f * (MaxProjection.X - MinProjection.X);
			Obb.HalfExtent.Y = 0.5f * (MaxProjection.Y - MinProjection.Y);
			Obb.HalfExtent.Z = 0.5f * (MaxProjection.Z - MinProjection.Z);

			return Obb;
		};

	auto GetProjectionRadius = [&](const FOBB& Obb, const FVector& Axis)
		{
			return
				Obb.HalfExtent.X * FMath::Abs(FVector::DotProduct(Obb.AxisX, Axis)) +
				Obb.HalfExtent.Y * FMath::Abs(FVector::DotProduct(Obb.AxisY, Axis)) +
				Obb.HalfExtent.Z * FMath::Abs(FVector::DotProduct(Obb.AxisZ, Axis));
		};

	// 构建OBB
	const FOBB ObbA = BuildOBB(CubeVerticesA);
	const FOBB ObbB = BuildOBB(CubeVerticesB);

	// 中心连线向量
	const FVector CenterDiff = ObbB.Center - ObbA.Center;

	// 检查各立方体的3个轴向（共6轴）
	const FVector AxesA[3] = { ObbA.AxisX, ObbA.AxisY, ObbA.AxisZ };
	const FVector AxesB[3] = { ObbB.AxisX, ObbB.AxisY, ObbB.AxisZ };

	for (const FVector& Axis : AxesA)
	{
		const float ProjRadiusA = GetProjectionRadius(ObbA, Axis);
		const float ProjRadiusB = GetProjectionRadius(ObbB, Axis);
		const float ProjDiff = FMath::Abs(FVector::DotProduct(CenterDiff, Axis));

		if (ProjDiff > ProjRadiusA + ProjRadiusB + KINDA_SMALL_NUMBER)
			return false;
	}

	for (const FVector& Axis : AxesB)
	{
		const float ProjRadiusA = GetProjectionRadius(ObbA, Axis);
		const float ProjRadiusB = GetProjectionRadius(ObbB, Axis);
		const float ProjDiff = FMath::Abs(FVector::DotProduct(CenterDiff, Axis));

		if (ProjDiff > ProjRadiusA + ProjRadiusB + KINDA_SMALL_NUMBER)
			return false;
	}

	// 检查轴向叉积（9轴）
	for (int32 i = 0; i < 3; ++i)
	{
		for (int32 j = 0; j < 3; ++j)
		{
			FVector TestAxis = FVector::CrossProduct(AxesA[i], AxesB[j]);
			if (TestAxis.IsNearlyZero(KINDA_SMALL_NUMBER)) // 避免平行轴向的零向量
				continue;

			TestAxis.Normalize();

			const float ProjRadiusA = GetProjectionRadius(ObbA, TestAxis);
			const float ProjRadiusB = GetProjectionRadius(ObbB, TestAxis);
			const float ProjDiff = FMath::Abs(FVector::DotProduct(CenterDiff, TestAxis));

			if (ProjDiff > ProjRadiusA + ProjRadiusB + KINDA_SMALL_NUMBER)
				return false;
		}
	}

	// 所有轴未分离，则相交
	return true;
}

FTransform UDSCupboardLibrary::GetNodeTransform(UDSCupboardModel* InRootModel, const TSharedPtr<FMultiComponentDataItem>& InFromNode, const TSharedPtr<FMultiComponentDataItem>& InToNode)
{
	TArray<TSharedPtr<FMultiComponentDataItem>> ComponentPath;
	InRootModel->CollectComponentPath_Public(InFromNode, InToNode, ComponentPath);

	FTransform ComponentRelativeTransform;
	if (ComponentPath.IsValidIndex(0))
	{
		ComponentRelativeTransform.SetLocation(ComponentPath[0]->ComponentLocation.GetLocation());
		ComponentRelativeTransform.SetRotation(ComponentPath[0]->ComponentRotation.GetRotation().Quaternion());
		ComponentRelativeTransform.SetScale3D(ComponentPath[0]->ComponentScale.GetScale());
		//ComponentRelativeTransform = RelativeTransform * ComponentRelativeTransform;
		for (int32 Index = 1; Index < ComponentPath.Num(); ++Index)
		{
			FTransform CurrentTransform;
			CurrentTransform.SetLocation(ComponentPath[Index]->ComponentLocation.GetLocation());
			CurrentTransform.SetRotation(ComponentPath[Index]->ComponentRotation.GetRotation().Quaternion());
			CurrentTransform.SetScale3D(ComponentPath[Index]->ComponentScale.GetScale());

			ComponentRelativeTransform = CurrentTransform * ComponentRelativeTransform;
		}
	}
	ComponentRelativeTransform = ComponentRelativeTransform;
	return ComponentRelativeTransform;
}

#undef PARAM_X 
#undef PARAM_Y 
#undef PARAM_Z 