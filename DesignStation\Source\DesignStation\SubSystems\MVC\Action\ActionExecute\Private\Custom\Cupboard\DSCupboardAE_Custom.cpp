﻿// Fill out your copyright notice in the Description page of Project Settings.

#include "SubSystems/MVC/Action/ActionExecute/Public/Custom/Cupboard/DSCupboardAE_Custom.h"
#include "BasicClasses/DesignStationController.h"
#include "SubSystems/AdaptiveAdsorption/Core/Operator/CupboardModelAdaptationOperator.h"
#include "SubSystems/AdaptiveAdsorption/Core/Operator/DrawerAdaptationOperator.h"
#include "SubSystems/AdaptiveAdsorption/Core/Operator/FunctionalAdaptationOperator.h"
#include "Subsystems/Library/DesignStationFunctionLibrary.h"
#include "SubSystems/ModelDependencySubsystem/DSModelDependencySubsystem.h"
#include "SubSystems/MVC/DSMVCSubsystem.h"
#include "SubSystems/MVC/Core/Macro.h"
#include "SubSystems/MVC/Core/Property/FurnitureProperty.h"
#include "SubSystems/MVC/Core/Property/HousePathProperty.h"
#include "SubSystems/MVC/Library/CounterTopLibrary.h"
#include "SubSystems/MVC/Library/CupBoardDoorLibrary.h"
#include "SubSystems/MVC/Library/DSCustomLibrary.h"
#include "SubSystems/MVC/Library/DSToolLibrary.h"
#include "SubSystems/MVC/Model/Custom/DSCupboardModel.h"
#include "SubSystems/MVC/Model/Custom/DSCupboardModel.h"
#include "SubSystems/MVC/Model/Custom/Library/DSCupboardLibrary.h"
#include "SubSystems/MVC/Model/Custom/Library/DSHandleFreeLibrary.h"
#include "SubSystems/MVC/StateMachine/DragDropOps/DSModelDragDropOperation.h"
#include "Subsystems/UI/DSUISubsystem.h"
#include "SubSystems/Undo/DSRevokeSubsystem.h"

extern const FString PARAM_SBFG;
extern const FString PARAM_XBFG;
extern const FString PARAM_ZBFG;
extern const FString PARAM_YBFG;

UDSCupboardAE_Custom::UDSCupboardAE_Custom()
{
	bUseSnapSubsystem = false;
}

void UDSCupboardAE_Custom::EnterAction(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& InMarkData)
{
	if (NeedPushRevoke())
	{
		HasPushCommand = true;

		auto OwnerModel = ActionModel->GetOwnerModel();
		if (UDSCupboardLibrary::IsFunctionalCupboardModel(ActionModel) && OwnerModel)
		{
			FDSCustomPushData CustomPushData(FDSModelExecuteType::ExecuteUpdateSelf, OwnerModel, nullptr);
			FDSRevokePushData PushData(EDSPushDataType::E_Custom, FDSModelExecuteType::ExecuteUpdateSelf, false);
			PushData.SetData(CustomPushData);
			UDSRevokeSubsystem::GetInstance()->InsertCommandDataUnion(
				OwnerModel,
				FDSModelExecuteType::ExecuteUpdateSelf,
				PushData,
				GetBelongStateRevokePoolMark(),
				TEXT("")
			);
		}
		else
		{
			FString GUIDStr;
			//处理免拉五金合并
			if (UDSToolLibrary::IsCustomCabinetType(ActionModel->GetModelType())
				&& UDSModelDependencySubsystem::GetInstance()->IsHaveModelHandleFree(ActionModel->GetUUID()))
			{
				UDSUISubsystem::GetInstance()->SetToastState(EToastState::EWarning, TEXT("选中的合并拉手已进行拆分"));

				GUIDStr = FGuid::NewGuid().ToString();
				UDSHandleFreeLibrary::SplitHandleFree(ActionModel, true, &GUIDStr); //拆分
			}

			FDSCustomPushData CustomPushData(FDSModelExecuteType::ExecuteUpdateSelf, ActionModel, nullptr);
			FDSRevokePushData PushData(EDSPushDataType::E_Custom, FDSModelExecuteType::ExecuteUpdateSelf, false);
			PushData.SetData(CustomPushData);
			UDSRevokeSubsystem::GetInstance()->InsertCommandDataUnion(
				ActionModel,
				FDSModelExecuteType::ExecuteUpdateSelf,
				PushData,
				GetBelongStateRevokePoolMark(),
				GUIDStr
			);
		}
	}

	UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(ActionModel);
	if (CupboardModel->HasParsed())
	{
		PrepareCupboardModelAdaptationInfo();
	}
	else
	{
		CupboardModel->ParseCompleteHandle().RemoveAll(this);
		CupboardModel->ParseCompleteHandle().AddUObject(this, &ThisClass::PrepareCupboardModelAdaptationInfo);
	}
}

void UDSCupboardAE_Custom::InitAction(UDSBaseModel* InActionModel, const FPointerEvent& InMouseEvent)
{
	Super::InitAction(InActionModel, InMouseEvent);
}

FReply UDSCupboardAE_Custom::OnDragDetected(const FGeometry& MyGeometry, const FPointerEvent& MouseEvent)
{
	Super::OnDragDetected(MyGeometry, MouseEvent);

	if (ActionModel)
	{
		UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(ActionModel);
		if (!CupboardModel)
		{
			return FReply::Unhandled();
		}
		const auto& Operater = CupboardModel->GetAdaptationOperator();
		if (Operater.IsValid())
		{
			Operater->OnAdaptationStarting();
		}
	}

	ActionModel->AddModelStateFlag(EModelState::E_Transform); //标尺只读
	ActionModel->AddModelStateFlag(EModelState::E_Dragging); //gizmo 隐藏

	//移动的时候要删除当前model产生的孔，不然射线会穿过孔，检测不到台面
	if (ActionModel->GetModelType() == EDSModelType::E_Custom_Sink)
	{
		UDSCounterTopLibrary::RemoveHoleIfTargetLeave(ActionModel);
	}
	UDSUISubsystem::GetInstance()->HiddenUI(false, true);
	//开始拖拽
	return FReply::Handled().BeginDragDrop(FDSModelDragDropOperation::New(MouseEvent, ActionModel));
}

FReply UDSCupboardAE_Custom::OnDragOver(const FGeometry& MyGeometry, const FDragDropEvent& DragDropEvent)
{
	Super::OnDragOver(MyGeometry, DragDropEvent);

	if (DragDropEvent.GetLastScreenSpacePosition().Equals(DragDropEvent.GetScreenSpacePosition()))
	{
		return FReply::Handled();
	}

	ADesignStationController* Controller = ADesignStationController::Get();
	if (Controller == nullptr)
	{
		return FReply::Unhandled();
	}

	FVector2D MouseScreenPos = MyGeometry.AbsoluteToLocal(DragDropEvent.GetScreenSpacePosition());
	FVector MousePos = FVector::ZeroVector;
	FVector MouseDir = -FVector::UpVector;
	ADesignStationController::Get()->DeprojectScreenPositionToWorld(MouseScreenPos.X, MouseScreenPos.Y, MousePos, MouseDir);

	FDSFurnitureBaseProperty* Property = static_cast<FDSFurnitureBaseProperty*>(ActionModel->GetProperty());
	if (Property == nullptr)
	{
		return FReply::Unhandled();
	}

	TArray<AActor*> IgnoreActors;
	ActionModel->GetOwnedView()->GetAttachedActors(IgnoreActors);
	IgnoreActors.Add(ActionModel->GetOwnedView());

	FVector Location = Property->GetTransformProperty().Location;

	bool bIsFunctionalModel = UDSCupboardLibrary::IsFunctionalCupboardModel(ActionModel);
	if (!DragDropEvent.IsLeftControlDown())
	{
		// pressing ctrl to ignore snap
		if (UDSToolLibrary::IsCustomCabinetType(ActionModel->GetModelType()) || bIsFunctionalModel)
		{
			bool bSuccess = HandleAdaptiveAndAdssorptionByHitPoint(MousePos, MouseDir);
			if (!bSuccess)
			{
				if (!FMath::IsNearlyZero(MouseDir.Z))
				{
					FVector Offset = FVector::Zero();
					Offset.X = Property->SizeProperty.Width * 0.05f;
					Offset.Y = Property->SizeProperty.Depth * 0.05f;
					Offset = Property->TransformProperty.Rotation.RotateVector(Offset);
					Offset.Z = 0;
					double Delta = ((Location.Z - MousePos.Z) / MouseDir.Z);
					Property->GetTransformPropertyRef().SetLocation(MousePos + MouseDir * Delta - Offset);
					ActionModel->OnExecuteAction(FDSModelExecuteType::ExecuteTransform);
				}
			}
			return FReply::Handled();
		}
	}

	if (!FMath::IsNearlyZero(MouseDir.Z))
	{
		FVector Offset = FVector::Zero();
		Offset.X = Property->SizeProperty.Width * 0.05f;
		Offset.Y = Property->SizeProperty.Depth * 0.05f;
		Offset = Property->TransformProperty.Rotation.RotateVector(Offset);
		Offset.Z = 0;
		double Delta = ((Location.Z - MousePos.Z) / MouseDir.Z);
		Property->GetTransformPropertyRef().SetLocation(MousePos + MouseDir * Delta - Offset);
		ActionModel->OnExecuteAction(FDSModelExecuteType::ExecuteTransform);
	}
	return FReply::Handled();
}

FReply UDSCupboardAE_Custom::OnDrop(const FGeometry& MyGeometry, const FDragDropEvent& DragDropEvent)
{
	Super::OnDrop(MyGeometry, DragDropEvent);

	bool bIsFunctionalModel = UDSCupboardLibrary::IsFunctionalCupboardModel(ActionModel);
	FAdaptiveAdsorptionResault AdaptationResault;
	if (bIsFunctionalModel)
	{
		UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(ActionModel);
		auto& Operater = CupboardModel->GetAdaptationOperator();
		TSharedPtr<FFunctionalAdaptationOperator> FunctionalOperator = StaticCastSharedPtr<FFunctionalAdaptationOperator>(Operater);
		if (FunctionalOperator.IsValid())
		{
			FunctionalOperator->CompleteAdaptation(AdaptationResault);
		}
	}

	if (UDSToolLibrary::IsCustomCabinetType(ActionModel->GetModelType()))
	{
		UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(ActionModel);

		auto& Operater = CupboardModel->GetAdaptationOperator();
		TSharedPtr<FCupboardModelAdaptationOperator> CupboardOperator = StaticCastSharedPtr<FCupboardModelAdaptationOperator>(Operater);
		if (CupboardOperator.IsValid() && !DragDropEvent.IsLeftControlDown())
		{
			CupboardOperator->CompleteAdaptation(AdaptationResault);
		}
	}

	bool bIsNewGenerate = ActionModel->IsNewGenerate();
	if (ActionModel->IsNewGenerate())
	{
		if (DragDropEvent.GetEffectingButton() == EKeys::RightMouseButton)
		{
			if (UDSCupboardLibrary::IsFunctionalCupboardModel(ActionModel))
			{
				UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(ActionModel);

				UDSCupboardModel* RootCupbroadModel =  CupboardModel->GetRootCupboardModel();
				RootCupbroadModel->RemoveFunctionalCupboardModel(ActionModel);
				UDSRevokeSubsystem::GetInstance()->PopUndoCommandWithNoExecute(
					GetBelongStateRevokePoolMark()
				);
			}
			//右键取消
			ActionModel->OnExecuteAction(FDSModelExecuteType::ExecuteDelete);
			UDSMVCSubsystem::GetInstance()->SetCurrentModel(nullptr);
			UDSUISubsystem::GetInstance()->ProcessStateEvent(nullptr, EUIOperationType::Selected, UDSMVCSubsystem::GetInstance()->GetStateType());
			return FReply::Handled();
		}

		ActionModel->SetNoNewGenerate();

		if (UDSCupboardLibrary::IsFunctionalCupboardModel(ActionModel))
		{
			if (AdaptationResault.ResaultType == EAdaptiveResaultType::E_WithoutAdsorption)
			{
				UDSRevokeSubsystem::GetInstance()->PopUndoCommandWithNoExecute(
					GetBelongStateRevokePoolMark()
				);
			}
			else
			{
				// 功能件生成时正常放置后根据Root刷新自己的样式和材质
				UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(ActionModel);
				CupboardModel->ApplyRootStyle_Functional();
			}
		}
		else
		{
			FDSCustomPushData CustomPushData(FDSModelExecuteType::ExecuteSpawn, ActionModel, nullptr);
			FDSRevokePushData PushData(EDSPushDataType::E_Custom, FDSModelExecuteType::ExecuteSpawn, false);
			PushData.SetData(CustomPushData);
			UDSRevokeSubsystem::GetInstance()->InsertCommandDataUnion(
				ActionModel,
				FDSModelExecuteType::ExecuteSpawn,
				PushData,
				GetBelongStateRevokePoolMark(),
				UDSRevokeSubsystem::GetInstance()->GetGlobalCommandUUID()
			);
		}
	}

	bool bShouldRefreshWallHoles = false;
	//检测是否要挖洞,TODO:还有其他类型的Model，需要挖洞
	if (ActionModel->GetModelType() == EDSModelType::E_Custom_Sink)
	{
		UDSCounterTopLibrary::TraceIsOnCounterTop(ActionModel, MyGeometry, DragDropEvent);
	}
	else if (ActionModel->GetModelType() == EDSModelType::E_Custom_LayoutDoor)
	{
		bShouldRefreshWallHoles = true;
		if (!AdaptationResault.Dependents.IsEmpty())
		{
			for (auto& Mod : AdaptationResault.Dependents)
			{
				auto Ptr = Mod.LinkModelInfo.OwnerModelPtr.Get();
				if (Ptr && Ptr->GetModelType() == EDSModelType::E_House_Wall
					&& (Mod.TargetDirection == EAdaptationDirection::E_Left || Mod.TargetDirection == EAdaptationDirection::E_Right))
				{
					//户型门需要内嵌入墙体，且挖洞

					auto ActionProp = ActionModel->GetPropertySharedPtr();
					auto Loc = ActionProp->GetActualTransform().GetLocation();

					auto WallProp = StaticCastSharedPtr<FDSHousePathProperty>(Ptr->GetPropertySharedPtr());
					auto WallStart = WallProp->SegmentStart;
					auto WallEnd = WallProp->SegmentEnd;
					WallStart.Z = Loc.Z;
					WallEnd.Z = Loc.Z;

					ActionProp->GetSizePropertyRef().Depth = WallProp->GetThickness() * 10.0;

					auto Width = ActionProp->SizeProperty.Width * 0.1;
					auto Depth = ActionProp->SizeProperty.Depth * 0.1;
					auto Rot = ActionProp->GetActualTransform().Rotator();
					auto Center = FGeometryLibrary::ConvertLeftBackToCenterRotated(Loc, Width, Depth, Rot);

					auto ClosetLoc = FMath::ClosestPointOnSegment(Center, WallStart, WallEnd);
					auto Move = ClosetLoc - Center;
					ActionModel->GetPropertySharedPtr()->GetTransformPropertyRef().Location += Move;

					auto ModelInfo = Cast<UDSCupboardModel>(ActionModel)->GetModelInfoRef();
					ModelInfo.ComponentTreeData->SetParameter(TEXT("MDD"), FString::FromInt(ActionProp->GetSizePropertyRef().Depth));
					break;
				}
			}
		}
	}

	ActionModel->RemoveModelStateFlag(EModelState::E_Transform); //标尺可写
	ActionModel->RemoveModelStateFlag(EModelState::E_Dragging); //gizmo 显示

	if (bIsFunctionalModel && (AdaptationResault.ResaultType == EAdaptiveResaultType::E_UnkownFailed || AdaptationResault.ResaultType == EAdaptiveResaultType::E_WithoutAdsorption))
	{
		UDSMVCSubsystem::GetInstance()->SetCurrentModel(nullptr);
		UDSUISubsystem::GetInstance()->ProcessStateEvent(nullptr, EUIOperationType::Selected, UDSMVCSubsystem::GetInstance()->GetStateType());
	}
	else
	{
		ActionModel->OnExecuteAction(FDSModelExecuteType::ExecuteTransform);
		ActionModel->SyncChildOutline();

		if (bShouldRefreshWallHoles)
		{
			ActionModel->OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf, FDSBroadcastMarkData::NotBroadcastToMVCMark);
			UDSCustomLibrary::RefreshLayoutDoorOnWalls(ActionModel);
		}

		ActionModel->OnExecuteAction(FDSModelExecuteType::ExecuteSelect);

		UDSMVCSubsystem::GetInstance()->SetCurrentModel(ActionModel);
		UDSUISubsystem::GetInstance()->ProcessStateEvent(ActionModel, EUIOperationType::Selected, UDSMVCSubsystem::GetInstance()->GetStateType());
		UDSCounterTopLibrary::RefreshAllCounterTop();
	}
	
	if (!bIsNewGenerate)
	{
		if (UDSCupboardLibrary::IsFunctionalCupboardModel(ActionModel))
		{
			FString CurrentID = Cast<UDSCupboardModel>(ActionModel)->GetModelInfoRef().ComponentTreeData->UUID;
			TArray<FString> DoorIds = UDSModelDependencySubsystem::GetInstance()->FindDoorByDependentBoard(CurrentID);

			if (!DoorIds.IsEmpty())
			{
				UDSCupboardModel* RootModel = Cast<UDSCupboardModel>(ActionModel)->GetRootCupboardModel();
				UDSCupBoardDoorLibrary::UpdateDoorOnCupboard(RootModel);
				
				TArray<UDSBaseModel*> Result;
				UDSCounterTopLibrary::GetSingleComponentModels(RootModel, Result);
				for (auto& It : Result)
				{
					UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(It);
					if (CupboardModel->GetModelInfoRef().ComponentTreeData->UUID == CurrentID)
					{
						UDSMVCSubsystem::GetInstance()->SetCurrentModel(CupboardModel);
						break;
					}
				}
			}
		}
	}
	else
	{
		//新生成的柜子，如果带门，要找关联板件
		UDSCupBoardDoorLibrary::FindDoorOfCabinetOfInit(ActionModel);
	}

	return FReply::Handled();
}

bool UDSCupboardAE_Custom::HandleAdaptiveAndAdssorptionByHitPoint(const FVector& RayStartPoint, const FVector& RayDir)
{
	if (!ActionModel || !ActionModel->IsA<UDSCupboardModel>())
	{
		return false;
	}
	UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(ActionModel);
	const auto& Operater = CupboardModel->GetAdaptationOperator();

	if (CupboardModel->GetModelType() == EDSModelType::E_Custom_FunctionalDrawer && !CupboardModel->IsNewGenerate())
	{//抽屉移动自适应时，重置盖值
		if (CupboardModel->GetModelInfoRef().ComponentTreeData.IsValid())
		{
			CupboardModel->GetModelInfoRef().ComponentTreeData->SetParameter(PARAM_SBFG, FString::FromInt(0));
			CupboardModel->GetModelInfoRef().ComponentTreeData->SetParameter(PARAM_XBFG, FString::FromInt(0));
			CupboardModel->GetModelInfoRef().ComponentTreeData->SetParameter(PARAM_ZBFG, FString::FromInt(0));
			CupboardModel->GetModelInfoRef().ComponentTreeData->SetParameter(PARAM_YBFG, FString::FromInt(0));
		}
	}

	if (Operater.IsValid())
	{
		return Operater->HandleAdaptiveAndAdssorptionByHitPoint(RayStartPoint, RayDir);
	}
	else
	{
		PrepareCupboardModelAdaptationInfo();
		const auto& Operater = CupboardModel->GetAdaptationOperator();
		if (Operater.IsValid())
		{
			return Operater->HandleAdaptiveAndAdssorptionByHitPoint(RayStartPoint, RayDir);
		}
	}
	return false;
}

void UDSCupboardAE_Custom::PrepareCupboardModelAdaptationInfo()
{
	if (!ActionModel || !ActionModel->IsA<UDSCupboardModel>())
	{
		return;
	}
	UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(ActionModel);
	CupboardModel->ParseCompleteHandle().RemoveAll(this);
	bool bIsFunctionalModel = UDSCupboardLibrary::IsFunctionalCupboardModel(ActionModel);

	if (bIsFunctionalModel)
	{
		auto& Operator = CupboardModel->GetAdaptationOperator();
		if (!Operator.IsValid())
		{
			if (CupboardModel->GetModelType() == EDSModelType::E_Custom_FunctionalDrawer)
			{
				Operator = MakeShared<FDrawerAdaptationOperator>(CupboardModel);
			}
			else
			{
				Operator = MakeShared<FFunctionalAdaptationOperator>(CupboardModel);
			}
		}
		Operator->PrepareAdaptation(ActionModel);
	}
	else
	{
		auto& Operator = CupboardModel->GetAdaptationOperator();
		if (!Operator.IsValid())
		{
			Operator = MakeShared<FCupboardModelAdaptationOperator>(CupboardModel);
		}
		Operator->PrepareAdaptation(ActionModel);
	}
}
