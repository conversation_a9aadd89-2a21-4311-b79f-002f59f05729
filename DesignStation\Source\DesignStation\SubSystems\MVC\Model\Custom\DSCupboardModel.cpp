#pragma once

#include "DSCupboardModel.h"

#include "DecimalMath.h"
#include "Http.h"
#include "Library/DSCupboardLibrary.h"
#include "Parameter/ParameterProcLibrary.h"
#include "SubSystems/MVC/DSMVCSubsystem.h"
#include "Subsystems/DSNetworkSubsystem.h"
#include "Subsystems/Resource/DSResourceSubsystem.h"
#include "SubSystems/MVC/Core/Property/CupboardProperty.h"
#include "EasyNetworkFileSubsystem.h"
#include "Core/Tasks/BaseNetworkFileTask.h"
#include "DownloadTaskPayload/CustomRelativeResourcePayload.h"
#include "Geometry/DataDefines/GeometryDatas.h"
#include "SubSystems/Library/DesignStationFunctionLibrary.h"
#include "SubSystems/MVC/Library/DSToolLibrary.h"
#include "SubSystems/AdaptiveAdsorption/Core/Executer/DynamicMeshAdaptiveAddorption.h"
#include "SubSystems/ModelDependencySubsystem/DSModelDependencySubsystem.h"
#include "Subsystems/UI/DSUISubsystem.h"
#include "SubSystems/AdaptiveAdsorption/Core/Operator/DrawerAdaptationOperator.h"
#include "SubSystems/MVC/Core/Macro.h"
#include "SubSystems/Undo/DSRevokeSubsystem.h"
#include "SubSystems/AdaptiveAdsorption/Library/AdaptationFunctionLibrary.h"
#include "SubSystems/MVC/Library/CounterTopLibrary.h"
#include "SubSystems/MVC/Model/DoorAndWindow/DSDoorAndWindowBaseModel.h"
#include "SubSystems/Drawing/DSDrawingSubsystem.h"
#include "Parameter/ParameterProcLibrary.h"
#include "SubSystems/File/Core/ProtoDataConvert/ProtobufOperatorFunctionLibrary.h"
#include "SubSystems/MVC/Library/DSCustomLibrary.h"
#include "Subsystems/MVC/Model/Custom/Library/DSWallBoardLibrary.h"

#include "SubSystems/AdaptiveAdsorption/Core/Operator/FunctionalAdaptationOperator.h"
#include "SubSystems/AdaptiveAdsorption/Core/Operator/CupboardModelAdaptationOperator.h"
#include "SubSystems/AdaptiveAdsorption/Data/FunctionalDependencyInfo.h"
#include "SubSystems/MVC/Library/CupBoardDoorLibrary.h"
#include "SubSystems/UI/Widget/CustomCupboard/Property/CustomCupboard/DSCustomCupboardPropertyWidget.h"

#include "Library/DSHandleFreeLibrary.h"

DECLARE_LOG_CATEGORY_CLASS(LogDSCupboardModel, Log, All);

extern const FString PARAM_ZCBJT_STR;
extern const FString PARAM_XCBJT_STR;
extern const FString PARAM_YCBJT_STR;
extern const FString PARAM_SCBJT_STR;

extern const FString PARAM_W_STR;
extern const FString PARAM_H_STR;
extern const FString PARAM_D_STR;

extern const FString PARAM_SBFG;
extern const FString PARAM_XBFG;
extern const FString PARAM_ZBFG;
extern const FString PARAM_YBFG;

UDSCupboardModel::UDSCupboardModel()
	: ModelInfo(FDSCupboardModelInfo())
{
	bParsed = false;
	bResourceAllLoaded = false;
	SetModelType(EDSModelType::E_Custom_UpperCabinet);
}

void UDSCupboardModel::InitConstruct(const FRefToLocalFileData& InData)
{
	bParsed = false;
	bResourceAllLoaded = false;

	OriginalComponentData = nullptr;

	ModelInfo.RefFileData = InData;

	// Parse ref file to tree data.
	UDSCupboardLibrary::ConstructTreeRootNode(InData, *ModelInfo.ComponentTreeData);
	UDSCupboardLibrary::CalculateTreeRootNodeParams(InData, ModelInfo.ComponentTreeData, {}, ParentComponentParams);
	SetModelType(UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(ModelInfo.ComponentTreeData->ModelType));

	ModelInfo.ComponentTreeData->bNeedLoadChildrenFromFile = false;

	OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf);
}

void UDSCupboardModel::InitConstruct_Separate(const TSharedPtr<FMultiComponentDataItem>& InComponentData, const TMap<FString, FParameterData>& ParentParams,
                                              TMap<TSharedPtr<FMultiComponentDataItem>, TSharedPtr<TMap<FString, FParameterData>>>& ComponentOverrideParamsPool,
                                              const FTransform& InRelativeTransform)
{
	ModelInfo.ComponentTreeData = InComponentData;
	ParentComponentParams = ParentParams;
	RelativeTransform = InRelativeTransform;
	OriginalComponentData = nullptr;
	SetModelType(UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(InComponentData->ModelType));

	SplitOverrideParamsPoolBySubtree(ModelInfo.ComponentTreeData, ComponentOverrideParamsPool, ComponentOverrideParams);

	RefreshSizePropertyAfterUpdate();

	for (const TSharedPtr<FMultiComponentDataItem>& ChildNode : ModelInfo.ComponentTreeData->ChildComponent)
	{
		ProcessSeparateChildren(ModelInfo.ComponentTreeData, ModelInfo.ComponentTreeData, ChildNode);
	}

	VerifyNeedsLazyLoadResources();

	bParsed = true;
	bResourceAllLoaded = LazyLoadResources.IsEmpty();

	if (OnCupboardModelParseCompleteHandle.IsBound())
	{
		OnCupboardModelParseCompleteHandle.Broadcast();
	}

	if (ComponentOverrideParams.Contains(ModelInfo.ComponentTreeData) && Property.IsValid())
	{//write transform / size info
		TSharedPtr<TMap<FString, FParameterData>> Params = ComponentOverrideParams[ModelInfo.ComponentTreeData];
		if (Params.IsValid())
		{
			if (GetModelType() == EDSModelType::E_Custom_LayoutDoor)
			{
				//户型门的size取门洞数据
				Property->SizeProperty.Width = FCString::Atod(*(*Params)[TEXT("MDW")].Data.value);
				Property->SizeProperty.Depth = FCString::Atod(*(*Params)[TEXT("MDD")].Data.value);
				Property->SizeProperty.Height = FCString::Atod(*(*Params)[TEXT("MDH")].Data.value);
			}
			else
			{
				//参数中一定有WHD
				Property->SizeProperty.Width = FCString::Atod(*(*Params)[TEXT("W")].Data.value);
				Property->SizeProperty.Depth = FCString::Atod(*(*Params)[TEXT("D")].Data.value);
				Property->SizeProperty.Height = FCString::Atod(*(*Params)[TEXT("H")].Data.value);
			}
		}

		FTransform ActualTransform = RelativeTransform * GetParentTransform();
		Property->TransformProperty.SetTransform(ActualTransform);

		if (ModelInfo.ComponentTreeData->bHiden)
		{
			ModelStateFlag.AddState(EModelState::E_Hidden);
			Property->SetHidden(true);
		}
	}

	CalculateOutline();
	OnExecuteAction(FDSModelExecuteType::ExecuteAll, FDSBroadcastMarkData::OnlyOutlineBroadcastMark);
}

void UDSCupboardModel::InitConstruct_Functional(const FRefToLocalFileData& InData, UDSCupboardModel* OwnerModel, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr/* = FDSBroadcastMarkData::BroadcastToMVCMark*/)
{
	if (!OwnerModel)
	{
		return;
	}
	
	ModelInfo.RefFileData = InData;
	UDSCupboardLibrary::ConstructTreeRootNode(ModelInfo.RefFileData, *ModelInfo.ComponentTreeData);
	OwnerModel->GetSelfComponentOverriderParametersRef(ParentComponentParams);
	OriginalComponentData = nullptr;
	SetModelType(UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(ModelInfo.ComponentTreeData->ModelType));

	ModelInfo.ComponentTreeData->bNeedLoadChildrenFromFile = false;

	CalculateOutline();
	OwnerModel->AddFunctionalCupboardModel(this);

	OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf, BroadcastMarkPtr);
}

void UDSCupboardModel::SetProperty(FDSBaseProperty* InProperty)
{
	checkf(InProperty != nullptr, TEXT("%s - Property is nullptre."), __FUNCTIONW__);
	Property->CopyData(InProperty);

	UpdatePivotOffset();
}

void UDSCupboardModel::InitProperty()
{
	if (!Property.IsValid())
	{
		Property = MakeShareable(new FCupboardProperty());
	}
}

void UDSCupboardModel::InitAdditionData()
{
	ModelState = FDSModelState(
		FDSModelCreateMark(true, false),
		FDSModelTransformMark(true, true, true),
		FDSModelTransformMark(true, true, true)
	);
	PendantInfo = FPendantInfo(
		FDSAxisMark(true, true, true, true),
		FDSScaleMark(true, true),
		FDSRulerMark(
			FDSRuler_Inner(true, true, true, true),
			FDSRuler_RectSelf(false, false),
			FDSRuler_Inner(true, true, true, true),
			FDSRuler_Path(false, false),
			FDSRuler_Segment(false),
			FDSRuler_OnWall(false, false)
		)
	);
}

UDSBaseModel* UDSCupboardModel::OnCopy()
{
	UDSCupboardModel* NewModel = NewObject<UDSCupboardModel>(this);
	NewModel->SetModelInfo(ModelInfo);
	NewModel->bParsed = bParsed;
	NewModel->LoadState = LoadState;
	NewModel->ComponentOverrideParams = ComponentOverrideParams;
	NewModel->RelativeTransform = RelativeTransform;
	NewModel->Property->CopyData(Property.Get());
	
	NewModel->GetModelInfoRef().ComponentInfoArr.Empty();
	NewModel->SetModelType(GetModelType());
	NewModel->SubFunctionalNodeDependencyMap = NewModel->GetSubFunctionalNodeDependencyMap();
	NewModel->SubFunctionalNodeDependencyMap->OnCopy(SubFunctionalNodeDependencyMap);
	auto ReplaceUUIDs = NewModel->RegenerateUUIDs();
	if (IsFunctionalCupboardModel())
	{
		NewModel->ModelInfo.ComponentTreeData->RefreshChildUUIDWithStyle();
		NewModel->ParentComponentParams = ParentComponentParams;
		NewModel->LazyLoadResources = LazyLoadResources;
	}

	//处理门复制后的依赖关系
	{
		for (auto & IterUUID : ReplaceUUIDs)
		{
			FDSDoorDependencyInfo Info = UDSModelDependencySubsystem::GetInstance()->FindDoorDependencyInfo(IterUUID.Key);
			if (Info.DependentBoards.Num() == 4)
			{
				for (auto & B : Info.DependentBoards)
				{
					if (ReplaceUUIDs.Contains(B.Key))
					{
						B.Key = ReplaceUUIDs[B.Key];
					}
					if (ReplaceUUIDs.Contains(B.Value))
					{
						B.Value = ReplaceUUIDs[B.Value];
					}
				}

				for (auto & G : Info.DependentGroupBoards)
				{
					for (auto & B : G)
					{
						if (ReplaceUUIDs.Contains(B.Key))
						{
							B.Key = ReplaceUUIDs[B.Key];
						}
						if (ReplaceUUIDs.Contains(B.Value))
						{
							B.Value = ReplaceUUIDs[B.Value];
						}
					}
				}

				UDSModelDependencySubsystem::GetInstance()->AddDoorDependencyInfo(IterUUID.Value, Info);
			}
		}
	}

	//处理免拉五金合并
	if (UDSModelDependencySubsystem::GetInstance()->IsHaveModelHandleFree(GetUUID()))
	{
		if (IsInGroup() || IsInMultiSelect())
		{
			const TArray<FString>* Result = UDSModelDependencySubsystem::GetInstance()->GetHandleFreeByModelID(GetUUID());
			for (auto NodeIte : *Result)
			{
				auto FindNode = UDSCupboardLibrary::GetNodeByNodeUUID(GetComponentTreeDataRef(), NodeIte);
				int8 Result = UDSHandleFreeLibrary::HandleFreeModelInMulti(this, FindNode);
				if (1 == Result)
				{
					const FString* NewID = ReplaceUUIDs.Find(NodeIte);
					if (NewID != nullptr)
					{
						auto FindNode = UDSCupboardLibrary::GetNodeByNodeUUID(NewModel->GetComponentTreeDataRef(), *NewID);
						UDSHandleFreeLibrary::ResetHandleFreeInModel(FindNode);
					}
				}
				else if (2 == Result)
				{
					const FString* NewID = ReplaceUUIDs.Find(NodeIte);
					if (NewID != nullptr)
					{
						FDSHandleFreeCopyTempData NewData;
						NewData.NewNodeID = *NewID;
						NewData.OldNodeID = NodeIte;
						NewData.NewRootModelID = NewModel->GetUUID();
						NewData.OldRootModelID = GetUUID();
						UDSModelDependencySubsystem::GetInstance()->GetHandleFreeCopyTempDatas().Add(NewData);
					}

				}
			}
		}
		else
		{
			UDSHandleFreeLibrary::ResetHandleFreeInModel(NewModel->GetComponentTreeDataRef());
		}
	}

	UDSMVCSubsystem::GetInstance()->SpawnViewUnion(NewModel);
	NewModel->OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf, FDSBroadcastMarkData::OnlyOutlineBroadcastMark);

	auto OwnerModel = GetTopLevelOwnerModel();
	if (Cast<UDSCupboardModel>(OwnerModel) && IsFunctionalCupboardModel())
	{
		Cast<UDSCupboardModel>(OwnerModel)->AddFunctionalCupboardModel(NewModel);
		auto ModelTreeUUID = NewModel->GetModelInfo().ComponentTreeData->UUID;
		Cast<UDSCupboardModel>(OwnerModel)->GetSubFunctionalNodeDependencyMap()->AddDependencyInfo(ModelTreeUUID);
	}



	return NewModel;
}

UDSBaseModel* UDSCupboardModel::CopyModel()
{
	UDSCupboardModel* NewModel = NewObject<UDSCupboardModel>(this);
	NewModel->GetModelInfoRef() = ModelInfo;
	NewModel->GetModelInfoRef().ComponentInfoArr.Empty();
	if (IsFunctionalCupboardModel())
	{
		NewModel->ModelInfo.ComponentTreeData->RefreshChildUUIDWithStyle();
		NewModel->ParentComponentParams = ParentComponentParams;
		NewModel->LazyLoadResources = LazyLoadResources;
		NewModel->SetModelType(GetModelType());
	}
	//CalculateModelAfterModelInfoValid();

	return NewModel;
}

bool UDSCupboardModel::IsCanGroup() const
{
	return UDSToolLibrary::GetOwnerModelRecursion(const_cast<UDSCupboardModel*>(this)) == this;
}

bool UDSCupboardModel::CanMove(bool Is2D) const
{
	UDSBaseModel* OwnerModel = UDSToolLibrary::GetOwnerModelRecursion(const_cast<UDSCupboardModel*>(this));
	if (OwnerModel != nullptr)
	{
		// has owner

		return (OwnerModel == this) || (this->IsFunctionalCupboardModel());
	}

	return true;
}

void UDSCupboardModel::UpdatePivotOffset()
{
	TSharedPtr<FCupboardProperty> CupboardProperty = StaticCastSharedPtr<FCupboardProperty>(Property);

	Property->GizmoStatusProperty.PivotOffset.X = Property->SizeProperty.Width * 0.1f * 0.5f;
	Property->GizmoStatusProperty.PivotOffset.Y = Property->SizeProperty.Depth * 0.1f * 0.5f;

	FVector BoxOffset;
	if (!BoxOffset.InitFromString(CupboardProperty->BusinessInfo.BoxOffset))
	{
		BoxOffset = FVector::ZeroVector;
	}

	Property->GizmoStatusProperty.PivotOffset += BoxOffset;
}

void UDSCupboardModel::OnExecute_Model_GenerateByProperty(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{

}

void UDSCupboardModel::OnExecute_Model_UpdateSelf(const FDSModelExecuteType& InExecuteType,
	const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	UE_LOG(LogDSCupboardModel, Log, TEXT("%s - %s - %s"), __FUNCTIONW__, *ModelInfo.RefFileData.FolderDBData.folder_name, *GetUUID());

	InterruptParse();

	if (bParsed && !OriginalComponentData)
	{
		OriginalComponentData = MakeShared<FMultiComponentDataItem>();
		OriginalComponentData->DeepCopy(*ModelInfo.ComponentTreeData);
	}

	UDSBaseModel* CurSelectedModel = UDSMVCSubsystem::GetInstance()->GetCurrentModel();
	FString CurSelectedComponentUUID("");
	if (CurSelectedModel && CurSelectedModel->IsA<UDSCupboardModel>() && this !=CurSelectedModel)
	{
		UDSCupboardModel* CurSelectedCupboardModel = Cast<UDSCupboardModel>(CurSelectedModel);
		if (CurSelectedCupboardModel->GetRootCupboardModel() == this)
		{
			CurSelectedComponentUUID = CurSelectedCupboardModel->GetModelInfo().GetModelUUID();
		}
	}

	bParsed = false;

	Super::OnExecute_Model_UpdateSelf(InExecuteType, BroadcastMarkPtr);

	ComponentOverrideParams.Empty();

	for (const FDSComponentInfo& ComponentInfo : ModelInfo.ComponentInfoArr)
	{
		if (UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(ComponentInfo.ComponentModel))
		{
			CupboardModel->OnExecuteAction(FDSModelExecuteType::ExecuteDelete, FDSBroadcastMarkData::OnlyOutlineBroadcastMark);
		}
	}

	ModelInfo.ComponentInfoArr.Empty();

	TArray<FDSCustomStyleReplacementNode> StylizedNodes;

	UDSCupboardModel* RootModel = GetRootCupboardModel();
	
	TMap<FString, EDSResourceType> FolderIdsForDownload;
	if (!UDSCupboardLibrary::ParseTreeFromNode(GetRootCupboardModel()->GetComponentTreeDataRef(),
		ModelInfo.ComponentTreeData,
		OriginalComponentData, ParentComponentParams,
		FOnProcessParsedByOriginalNodeDelegate::CreateUObject(this, &UDSCupboardModel::ProcessNodeAfterParsed),
		ComponentOverrideParams,
		FolderIdsForDownload, StylizedNodes))
	{
		UE_LOG(LogDSCupboardModel, Error, TEXT("Failed to parse custom tree from node '%s'."), *ModelInfo.ComponentTreeData->ComponentID.GetFormattedValue());
		return;
	}

	// Collect which node need to disable collision.
	GetRootCupboardModel()->UpdateNodesToDisableCollision();

	if (!StylizedNodes.IsEmpty() && GetRootCupboardModel()->ModelInfo.ApplyStyle_Whole.IsValid())
	{
		bool bHasPatternNode = StylizedNodes.ContainsByPredicate([](const FDSCustomStyleReplacementNode& InNode){ return InNode.AssociationType == EDSCustomAssociationType::Pattern; });

		// 顶级柜子节点拥有风格时，如果树下某个节点是新加载的，且能够替换样式，对节点应用风格
		if (bHasPatternNode)
		{
			QueryStyleReplacements(StylizedNodes, GetRootCupboardModel()->ModelInfo.ApplyStyle_Whole, EDSCustomAssociationType::Pattern);
		}
	}

	OriginalComponentData.Reset();

	RefreshSizePropertyAfterUpdate();

	for (const TSharedPtr<FMultiComponentDataItem>& ChildNode : ModelInfo.ComponentTreeData->ChildComponent)
	{
		ProcessSeparateChildren(ModelInfo.ComponentTreeData, ModelInfo.ComponentTreeData, ChildNode);
	}

	VerifyNeedsLazyLoadResources();

	bParsed = true;
	bResourceAllLoaded = LazyLoadResources.IsEmpty();
	
	CalculateOutline();

	OnExecuteAction(FDSModelExecuteType::ExecuteAll, BroadcastMarkPtr);

	if (!bResourceAllLoaded)
	{
		StopObserveResourceStatusEvent();
		ObserveResourceStatusEvent();
	}

	if (OnCupboardModelParseCompleteHandle.IsBound())
	{
		OnCupboardModelParseCompleteHandle.Broadcast();
	}

	if (!FolderIdsForDownload.IsEmpty())
	{
		QueryRelatedResourceList(FolderIdsForDownload);
	}

	if (!CurSelectedComponentUUID.IsEmpty())
	{
		UDSBaseModel* NewSelectedModel =  UDSCupboardLibrary::GetModelByUUID(CurSelectedComponentUUID, ModelInfo);
		if (NewSelectedModel&& NewSelectedModel->IsValid())
		{
			NewSelectedModel->OnExecuteAction(FDSModelExecuteType::ExecuteSelect);
		}
	}
}

void UDSCupboardModel::OnExecute_Model_Transform(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	Super::OnExecute_Model_Transform(InExecuteType, BroadcastMarkPtr);

	if (OwnerModel != nullptr && OwnerModel->IsA<UDSDoorAndWindowBaseModel>())
	{
		return;
	}

	if (OwnerModel != nullptr && OwnerModel->IsA<UDSCupboardModel>())
	{
		RelativeTransform = OwnedView->GetRootComponent()->GetRelativeTransform();

		// Get transform from ComponentTreeData->ComponentXXX will divide 10 to convert from mm to cm, so we multiply 10 for all values.
		ModelInfo.ComponentTreeData->ComponentLocation = RelativeTransform.GetLocation() * 10.0f;
		ModelInfo.ComponentTreeData->ComponentRotation = RelativeTransform.GetRotation().Rotator();
		ModelInfo.ComponentTreeData->ComponentScale = RelativeTransform.GetScale3D();
	}

	float FloorHeight = UDSConfigSubsystem::GetInstance()->GetValue_FlootHeight();
	float DistanceToFloor = Property->GetTransformPropertyRef().Location.Z - FloorHeight;

	bool bNeedUpdate = UDSCupboardLibrary::WriteDistanceToFloorParameter(DistanceToFloor, ModelInfo.ComponentTreeData->ComponentParameters);
	if (bNeedUpdate && UDSUISubsystem::GetInstance()->GetCurrentWidgetType() == static_cast<int32>(GetModelType()))
	{
		if (OnCupboardModelDistanceToFloorChangeHandle.IsBound())
		{
			OnCupboardModelDistanceToFloorChangeHandle.Broadcast(DistanceToFloor);
		}
	}

	RefreshChildModelActualTransform();

	//CalculateOutline();
	SyncChildOutline();
	BroadcastLinkModelsTransform();
}

void UDSCupboardModel::OnExecute_Model_Hidden(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	for (const FDSComponentInfo& Component : ModelInfo.ComponentInfoArr)
	{
		if (Component.ComponentModel == nullptr)
		{
			continue;
		}
		Component.ComponentModel->OnExecuteAction(FDSModelExecuteType::ExecuteHidden, FDSBroadcastMarkData::OnlyOutlineBroadcastMark);
	}

	for (auto & Iter :  LinkModels)
	{
		Iter->OnExecuteAction(FDSModelExecuteType::ExecuteHidden,FDSBroadcastMarkData::OnlyOutlineBroadcastMark);
	}

	Super::OnExecute_Model_Hidden(InExecuteType, BroadcastMarkPtr);
}

void UDSCupboardModel::OnExecute_Model_UnHidden(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	if (ModelInfo.ComponentTreeData->bHiden)
	{
		return;
	}
	Super::OnExecute_Model_UnHidden(InExecuteType, BroadcastMarkPtr);

	for (const FDSComponentInfo& Component : ModelInfo.ComponentInfoArr)
	{
		if (Component.ComponentModel == nullptr)
		{
			continue;
		}

		UDSCupboardModel* ComponentCupModel = Cast<UDSCupboardModel>(Component.ComponentModel);

		Component.ComponentModel->OnExecuteAction(FDSModelExecuteType::ExecuteUnHidden, FDSBroadcastMarkData::OnlyOutlineBroadcastMark);
	}
	 
	for (auto& Iter : LinkModels)
	{
		Iter->OnExecuteAction(FDSModelExecuteType::ExecuteUnHidden, FDSBroadcastMarkData::OnlyOutlineBroadcastMark);
	}
}

void UDSCupboardModel::OnExecute_Model_ViewTypeHidden(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	Super::OnExecute_Model_ViewTypeHidden(InExecuteType, BroadcastMarkPtr);

	for (const FDSComponentInfo& Component : ModelInfo.ComponentInfoArr)
	{
		if (Component.ComponentModel == nullptr)
		{
			continue;
		}

		Component.ComponentModel->OnExecuteAction(FDSModelExecuteType::ExecuteHidden);
	}

	for (auto& Iter : LinkModels)
	{
		Iter->OnExecuteAction(FDSModelExecuteType::ExecuteHidden);
	}
}

void UDSCupboardModel::OnExecute_Model_UnViewTypeHidden(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	Super::OnExecute_Model_UnViewTypeHidden(InExecuteType, BroadcastMarkPtr);

	for (const FDSComponentInfo& Component : ModelInfo.ComponentInfoArr)
	{
		if (Component.ComponentModel == nullptr)
		{
			continue;
		}

		Component.ComponentModel->OnExecuteAction(FDSModelExecuteType::ExecuteUnHidden);
	}

	for (auto& Iter : LinkModels)
	{
		Iter->OnExecuteAction(FDSModelExecuteType::ExecuteUnHidden);
	}
}

void UDSCupboardModel::OnExecute_Model_Delete(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	//删除五金免拉的合并的关联数据
	if (UDSModelDependencySubsystem::GetInstance()->IsHaveModelHandleFree(GetUUID()))
	{
		UDSModelDependencySubsystem::GetInstance()->RemoveAllHandleFreeContainTheModelID(GetUUID());
	}

	if (GetModelType() == EDSModelType::E_Custom_LayoutDoor)
	{
		UDSCustomLibrary::RemoveLayoutDoorFromWalls(this);
	}

	InterruptParse();

	for (const FDSComponentInfo& ChildComponentInfo : ModelInfo.ComponentInfoArr)
	{
		if (UDSCupboardModel* ChildModel = Cast<UDSCupboardModel>(ChildComponentInfo.ComponentModel))
		{
			ChildModel->OnExecuteAction(FDSModelExecuteType::ExecuteDelete);
		}
	}
	auto TreeUUID = ModelInfo.ComponentTreeData->UUID;
	ModelInfo.ComponentInfoArr.Empty();

	if (UDSToolLibrary::IsCustomCabinetType(ModelType))
	{
		TArray<FString> CounterTops = UDSModelDependencySubsystem::GetInstance()->FindCounterTopsByRelatedModel(GetUUID());
		for (const FString& CounterTop : CounterTops)
		{
			UDSModelDependencySubsystem::GetInstance()->RemoveCounterTopRelation(CounterTop, GetUUID());
		}

		//如果有挖洞，还要删除挖洞
		UDSCounterTopLibrary::RemoveHoleIfTargetLeave(this);

		//删除关联
		FString SinkUUID = UDSModelDependencySubsystem::GetInstance()->FindSinkByCupboard(GetUUID());
		UDSModelDependencySubsystem::GetInstance()->RemoveSink(SinkUUID);

		FString StoveUUID = UDSModelDependencySubsystem::GetInstance()->FindStoveByCupboard(GetUUID());
		UDSModelDependencySubsystem::GetInstance()->RemoveStove(StoveUUID);

		for (auto& Iter : LinkModels)
		{
			if (DS_MODEL_VALID_FOR_USE(Iter) &&  (Iter->GetModelType() == EDSModelType::E_Custom_Sink || Iter->GetModelType() == EDSModelType::E_Custom_Stove))
			{
				Iter->OnExecuteAction(FDSModelExecuteType::ExecuteDeleteSelf);
			}
		}
	}

	if (!LinkModels.IsEmpty())
	{
		for (auto& Iter : LinkModels)
		{
			if (Iter.IsValid())
			{
				Iter->DeleteLinkModel(this);
			}
		}
	}

	//if (IsFunctionalCupboardModel())
	//{
	//	auto TopLevelOwner = GetTopLevelOwnerModel();
	//	if (TopLevelOwner && TopLevelOwner->IsA<UDSCupboardModel>())
	//	{
	//		UDSCupboardModel* OwnerCupboardModel = Cast<UDSCupboardModel>(TopLevelOwner);
	//		OwnerCupboardModel->GetSubFunctionalNodeDependencyMap()->RemoveDependencyInfo(TreeUUID);
	//	}
	//}
	Super::OnExecute_Model_Delete(InExecuteType, BroadcastMarkPtr);
}

void UDSCupboardModel::OnExecute_Model_Select(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	if (!IsHasModelFlag(EModelState::E_Selected))
	{
		if (AdaptationOperator.IsValid())
		{
			AdaptationOperator.Reset();
		}
	}

	Super::OnExecute_Model_Select(InExecuteType, BroadcastMarkPtr);
}

void UDSCupboardModel::OnExecute_Model_UnSelect(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	if (IsHasModelFlag(EModelState::E_Selected))
	{
		if (AdaptationOperator.IsValid())
		{
			AdaptationOperator.Reset();
		}
	}
	Super::OnExecute_Model_UnSelect(InExecuteType, BroadcastMarkPtr);
}

void UDSCupboardModel::OnExecute_Model_Overlap(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	Super::OnExecute_Model_Overlap(InExecuteType, BroadcastMarkPtr);

	for (auto& Iter : ModelInfo.ComponentInfoArr)
	{
		if (Iter.ComponentModel == nullptr)
		{
			continue;
		}
		Iter.ComponentModel->AddModelStateFlag(EModelState::E_Overlap);
	}
}

void UDSCupboardModel::OnExecute_Model_UnOverlap(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	Super::OnExecute_Model_UnOverlap(InExecuteType, BroadcastMarkPtr);

	for (auto& Iter : ModelInfo.ComponentInfoArr)
	{
		if (Iter.ComponentModel == nullptr)
		{
			continue;
		}
		Iter.ComponentModel->RemoveModelStateFlag(EModelState::E_Overlap);
	}
}

EDSGizmoModeType UDSCupboardModel::GetDefaultGizmoModeType(bool bIs2DScene) const
{
	if (!UDSToolLibrary::IsCustomCabinetType(ModelType) || OwnerModel != nullptr)
	{
		return EDSGizmoModeType::GMT_None;
	}

	return bIs2DScene ? EDSGizmoModeType::GMT_Rotate_Scale_2D : EDSGizmoModeType::GMT_Translate_Rotate;
}

bool UDSCupboardModel::IsValidSizeToScale(EDSGizmoOperatorType OperatorType, const FVector& SourceSize, const FVector& NewSize) const
{
	if (NewSize.X < 1.0f || NewSize.Y < 1.0f || NewSize.Z < 1.0f)
	{
		return false;
	}

	if (!ModelInfo.ComponentTreeData)
	{
		return false;
	}

	switch (OperatorType)
	{
	case EDSGizmoOperatorType::Front_Width:
	case EDSGizmoOperatorType::Back_Width:
		{
			int32 ParamPos = ModelInfo.ComponentTreeData->ComponentParameters.IndexOfByPredicate(
				[](const FParameterData& InParam)
				{
					return InParam.Data.name.Equals(TEXT("W"));
				});

			if (ParamPos == INDEX_NONE)
			{
				return false;
			}

			return ModelInfo.ComponentTreeData->ComponentParameters[ParamPos].IsValueValid(FString::Printf(TEXT("%f"), NewSize.X));
		}
		break;
	case EDSGizmoOperatorType::Left_Depth:
	case EDSGizmoOperatorType::Right_Depth:
		{
			int32 ParamPos = ModelInfo.ComponentTreeData->ComponentParameters.IndexOfByPredicate(
				[](const FParameterData& InParam)
				{
					return InParam.Data.name.Equals(TEXT("D"));
				});

			if (ParamPos == INDEX_NONE)
			{
				return false;
			}

			return ModelInfo.ComponentTreeData->ComponentParameters[ParamPos].IsValueValid(FString::Printf(TEXT("%f"), NewSize.Y));
		}
		break;
	case EDSGizmoOperatorType::Up_Height:
	case EDSGizmoOperatorType::Down_Height:
		{
			int32 ParamPos = ModelInfo.ComponentTreeData->ComponentParameters.IndexOfByPredicate(
				[](const FParameterData& InParam)
				{
					return InParam.Data.name.Equals(TEXT("H"));
				});

			if (ParamPos == INDEX_NONE)
			{
				return false;
			}

			return ModelInfo.ComponentTreeData->ComponentParameters[ParamPos].IsValueValid(FString::Printf(TEXT("%f"), NewSize.Z));
		}
		break;
	case EDSGizmoOperatorType::TopLeft_Width_Depth:
	case EDSGizmoOperatorType::TopRight_Width_Depth:
	case EDSGizmoOperatorType::BottomLeft_Width_Depth:
	case EDSGizmoOperatorType::BottomRight_Width_Depth:
		{
			int32 WidthPos = ModelInfo.ComponentTreeData->ComponentParameters.IndexOfByPredicate(
				[](const FParameterData& InParam)
				{
					return InParam.Data.name.Equals(TEXT("W"));
				});

			int32 DepthPos = ModelInfo.ComponentTreeData->ComponentParameters.IndexOfByPredicate(
				[](const FParameterData& InParam)
				{
					return InParam.Data.name.Equals(TEXT("D"));
				});

			if (WidthPos == INDEX_NONE || DepthPos == INDEX_NONE)
			{
				return false;
			}

			return ModelInfo.ComponentTreeData->ComponentParameters[WidthPos].IsValueValid(FString::Printf(TEXT("%f"), NewSize.X))
				&& ModelInfo.ComponentTreeData->ComponentParameters[DepthPos].IsValueValid(FString::Printf(TEXT("%f"), NewSize.Y));
		}
		break;
	default:
		break;
	}
	return false;
}

bool UDSCupboardModel::ClampScaleSizeToLimitation(EDSGizmoOperatorType OperatorType, const FVector& SourceSize, FVector& NewSize)
{
	if (!ModelInfo.ComponentTreeData)
	{
		return false;
	}

	auto TryClampAxisValue = [](const TArray<FParameterData>& Params, const FString& ParamName, double& Value) -> bool
	{
		const FParameterData* FoundParam = Params.FindByPredicate([&](const FParameterData& InParam)
		{
			return InParam.Data.name.Equals(ParamName);
		});

		if (FoundParam == nullptr)
		{
			return false;
		}

		FDecimal DecimalValue(Value);
		if (FoundParam->Data.is_enum)
		{
			for (int32 Index = 0; Index < FoundParam->EnumData.Num(); ++Index)
			{
				if (FoundParam->EnumData[Index].value.IsNumeric())
				{
					FDecimal EnumValue(FoundParam->EnumData[Index].value);
					if (FDecimalMath::IsNearlyEqual(DecimalValue, EnumValue, FDecimal(5)))
					{
						Value = EnumValue.ToDouble();
						return true;
					}
				}
			}
		}
		else
		{
			if (!FoundParam->Data.min_value.IsEmpty())
			{
				FDecimal MinValue(FoundParam->Data.min_value);
				if (DecimalValue < MinValue)
				{
					Value = MinValue.ToDouble();
					return true;
				}
			}

			if (!FoundParam->Data.max_value.IsEmpty())
			{
				FDecimal MaxValue(FoundParam->Data.max_value);
				if (DecimalValue > MaxValue)
				{
					Value = MaxValue.ToDouble();
					return true;
				}
			}
		}

		return false;
	};

	switch (OperatorType)
	{
	case EDSGizmoOperatorType::Front_Width:
	case EDSGizmoOperatorType::Back_Width:
		{
			if (TryClampAxisValue(ModelInfo.ComponentTreeData->ComponentParameters, TEXT("W"), NewSize.X))
			{
				return true;
			}
		}
		break;
	case EDSGizmoOperatorType::Left_Depth:
	case EDSGizmoOperatorType::Right_Depth:
		{
			if (TryClampAxisValue(ModelInfo.ComponentTreeData->ComponentParameters, TEXT("D"), NewSize.Y))
			{
				return true;
			}
		}
		break;
	case EDSGizmoOperatorType::Up_Height:
	case EDSGizmoOperatorType::Down_Height:
		{
			if (TryClampAxisValue(ModelInfo.ComponentTreeData->ComponentParameters, TEXT("H"), NewSize.Z))
			{
				return true;
			}
		}
		break;
	case EDSGizmoOperatorType::TopLeft_Width_Depth:
	case EDSGizmoOperatorType::TopRight_Width_Depth:
	case EDSGizmoOperatorType::BottomLeft_Width_Depth:
	case EDSGizmoOperatorType::BottomRight_Width_Depth:
		{
			bool bClampedWidth = TryClampAxisValue(ModelInfo.ComponentTreeData->ComponentParameters, TEXT("W"), NewSize.X);
			bool bClampedDepth = TryClampAxisValue(ModelInfo.ComponentTreeData->ComponentParameters, TEXT("D"), NewSize.Y);
			
			if (bClampedWidth || bClampedDepth)
			{
				return true;
			}
		}
		break;
	default:
		break;
	}

	return false;
}

bool UDSCupboardModel::CupboardSizeValidToScale(EDSGizmoOperatorType OperatorType, const FVector& SourceSize, const FVector& NewSize) const
{
	auto ValidFunction = [this](const FString& ParamName, const double& NewValue)->bool
		{
			bool bValid = false;
			FParameterData* FindParam = ModelInfo.ComponentTreeData->ComponentParameters.FindByPredicate(
				[&ParamName](const FParameterData& Param)->bool
				{
					return Param.Data.name.Equals(ParamName);
				}
			);
			if (FindParam != nullptr)
			{
				bValid = FindParam->IsValueValid(FString::SanitizeFloat(NewValue));
			}

			return bValid;
		};


	if (Property.IsValid())
	{
		if (OperatorType == EDSGizmoOperatorType::Front_Width || OperatorType == EDSGizmoOperatorType::Back_Width)
		{
			if (Property->SizeProperty.bWidthCanEdit)
			{
				return ValidFunction(TEXT("W"), NewSize.X);
			}
			else
			{
				return false;
			}
		}
		else if (OperatorType == EDSGizmoOperatorType::Left_Depth || OperatorType == EDSGizmoOperatorType::Right_Depth)
		{
			if (Property->SizeProperty.bDepthCanEdit)
			{
				return ValidFunction(TEXT("D"), NewSize.Y);
			}
			else
			{
				return false;
			}
		}
		else if (OperatorType == EDSGizmoOperatorType::Up_Height || OperatorType == EDSGizmoOperatorType::Down_Height)
		{
			if (Property->SizeProperty.bHeightCanEdit)
			{
				return ValidFunction(TEXT("H"), NewSize.Z);
			}
			else
			{
				return false;
			}
		}
	}

	return false;
}

void UDSCupboardModel::ScaleSizeByGizmo(EDSGizmoOperatorType OperatorType, const FVector& SourceSize,
                                        const FVector& NewSize)
{
	Super::ScaleSizeByGizmo(OperatorType, SourceSize, NewSize);

	if (!ModelInfo.ComponentTreeData)
	{
		return;
	}

	switch (OperatorType)
	{
	case EDSGizmoOperatorType::Front_Width:
	case EDSGizmoOperatorType::Back_Width:
		{
			int32 ParamPos = ModelInfo.ComponentTreeData->ComponentParameters.IndexOfByPredicate(
				[](const FParameterData& InParam)
				{
					return InParam.Data.name.Equals(TEXT("W"));
				});

			if (ParamPos == INDEX_NONE)
			{
				return;
			}

			FString NewVal = FString::Printf(TEXT("%f"), NewSize.X);
			ModelInfo.ComponentTreeData->ComponentParameters[ParamPos].Data.value = NewVal;
			ModelInfo.ComponentTreeData->ComponentParameters[ParamPos].Data.expression = NewVal;
			ModelInfo.ComponentTreeData->ComponentParameters[ParamPos].bEnableAdaptation = false;
		}
		break;
	case EDSGizmoOperatorType::Left_Depth:
	case EDSGizmoOperatorType::Right_Depth:
		{
			int32 ParamPos = ModelInfo.ComponentTreeData->ComponentParameters.IndexOfByPredicate(
				[](const FParameterData& InParam)
				{
					return InParam.Data.name.Equals(TEXT("D"));
				});

			if (ParamPos == INDEX_NONE)
			{
				return;
			}

			FString NewVal = FString::Printf(TEXT("%f"), NewSize.Y);
			ModelInfo.ComponentTreeData->ComponentParameters[ParamPos].Data.value = NewVal;
			ModelInfo.ComponentTreeData->ComponentParameters[ParamPos].Data.expression = NewVal;
			ModelInfo.ComponentTreeData->ComponentParameters[ParamPos].bEnableAdaptation = false;
		}
		break;
	case EDSGizmoOperatorType::Up_Height:
	case EDSGizmoOperatorType::Down_Height:
		{
			int32 ParamPos = ModelInfo.ComponentTreeData->ComponentParameters.IndexOfByPredicate(
				[](const FParameterData& InParam)
				{
					return InParam.Data.name.Equals(TEXT("H"));
				});

			if (ParamPos == INDEX_NONE)
			{
				return;
			}

			FString NewVal = FString::Printf(TEXT("%f"), NewSize.Z);
			ModelInfo.ComponentTreeData->ComponentParameters[ParamPos].Data.value = NewVal;
			ModelInfo.ComponentTreeData->ComponentParameters[ParamPos].Data.expression = NewVal;
			ModelInfo.ComponentTreeData->ComponentParameters[ParamPos].bEnableAdaptation = false;
		}
		break;
	case EDSGizmoOperatorType::TopLeft_Width_Depth:
	case EDSGizmoOperatorType::TopRight_Width_Depth:
	case EDSGizmoOperatorType::BottomLeft_Width_Depth:
	case EDSGizmoOperatorType::BottomRight_Width_Depth:
		{
			int32 WidthPos = ModelInfo.ComponentTreeData->ComponentParameters.IndexOfByPredicate(
				[](const FParameterData& InParam)
				{
					return InParam.Data.name.Equals(TEXT("W"));
				});

			int32 DepthPos = ModelInfo.ComponentTreeData->ComponentParameters.IndexOfByPredicate(
				[](const FParameterData& InParam)
				{
					return InParam.Data.name.Equals(TEXT("D"));
				});

			if (WidthPos == INDEX_NONE || DepthPos == INDEX_NONE)
			{
				return;
			}

			FString NewWidthVal = FString::Printf(TEXT("%f"), NewSize.X);
			ModelInfo.ComponentTreeData->ComponentParameters[WidthPos].Data.value = NewWidthVal;
			ModelInfo.ComponentTreeData->ComponentParameters[WidthPos].Data.expression = NewWidthVal;
			ModelInfo.ComponentTreeData->ComponentParameters[WidthPos].bEnableAdaptation = false;

			FString NewDepthVal = FString::Printf(TEXT("%f"), NewSize.Y);
			ModelInfo.ComponentTreeData->ComponentParameters[DepthPos].Data.value = NewDepthVal;
			ModelInfo.ComponentTreeData->ComponentParameters[DepthPos].Data.expression = NewDepthVal;
			ModelInfo.ComponentTreeData->ComponentParameters[DepthPos].bEnableAdaptation = false;
		}
		break;
	default:
		break;
	}
	
	TMap<FString, FParameterData> PeerParameters = FGeometryDatas::ConvertParamsArrayToMap(ModelInfo.ComponentTreeData->ComponentParameters);
	FGeometryDatas::CalculateParameterValue_LevelSort(UDSMVCSubsystem::GetInstance()->GetGlobalParamsMap(), GetParentComponentParams(), PeerParameters);
	for (const auto& iter : PeerParameters)
	{
		auto& EditParameter = iter.Value;
		const int32 Index = ModelInfo.ComponentTreeData->ComponentParameters.IndexOfByPredicate([EditParameter](const FParameterData& InOther)
		{
			return InOther.Data.name.Equals(EditParameter.Data.name);
		});
		if (INDEX_NONE != Index)
		{
			ModelInfo.ComponentTreeData->ComponentParameters[Index] = EditParameter;
		}
	}

	OnExecuteAction(FDSModelExecuteType::ExecuteAll);

	if (GetPropertySharedPtr().IsValid())
	{
		if (UDSCustomCupboardPropertyWidget* PropertyWidget = Cast<UDSCustomCupboardPropertyWidget>(UDSUISubsystem::GetInstance()->GetCurPropertyWidget()))
		{
			PropertyWidget->UpdateStableBasicSizeParams(
				this, UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), ModelInfo.ComponentTreeData->ModelType));
		}
	}


}

void UDSCupboardModel::PreScaleSizeByGizmo(EDSGizmoOperatorType OperatorType, const FVector& SourceSize,
	const FVector& NewSize)
{
	Super::PreScaleSizeByGizmo(OperatorType, SourceSize, NewSize);

	for (const FDSComponentInfo& Component : ModelInfo.ComponentInfoArr)
	{
		if (Component.ComponentModel == nullptr)
		{
			continue;
		}

		//护墙板贴图的板子不改变大小
		if (UDSWallBoardLibrary::IsTextureWallBoard(Component.ComponentModel))
		{
			continue;
		}

		Component.ComponentModel->PreScaleSizeByGizmo(OperatorType, SourceSize, NewSize);
	}

	InterruptParse();
	bPreParsed = bParsed;
	bParsed = false;

	//删除子部件原有轮廓线
	for (const FDSComponentInfo& ComponentInfo : ModelInfo.ComponentInfoArr)
	{
		if (UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(ComponentInfo.ComponentModel))
		{
			UDSDrawingSubsystem::GetInstance()->RefreshScreenLine(CupboardModel, FDSModelExecuteType::ExecuteDelete, FDSBroadcastMarkData::OnlyOutlineBroadcastMark);
		}
	}
	
	OnExecuteAction(FDSModelExecuteType::ExecuteAll, FDSBroadcastMarkData::NotBroadcastToMVCMark);
}

void UDSCupboardModel::PostScaleSizeByGizmo(EDSGizmoOperatorType OperatorType, const FVector& SourceSize,
	const FVector& NewSize)
{
	Super::PostScaleSizeByGizmo(OperatorType, SourceSize, NewSize);

	for (const FDSComponentInfo& Component : ModelInfo.ComponentInfoArr)
	{
		if (Component.ComponentModel == nullptr)
		{
			continue;
		}

		//护墙板贴图的板子不改变大小
		if (UDSWallBoardLibrary::IsTextureWallBoard(Component.ComponentModel))
		{
			continue;
		}

		Component.ComponentModel->PostScaleSizeByGizmo(OperatorType, SourceSize, NewSize);
	}

	bParsed = bPreParsed;
	if (GetRootCupboardModel() != this)
	{
		OnExecuteAction(FDSModelExecuteType::ExecuteAll, FDSBroadcastMarkData::OnlyOutlineBroadcastMark);
	}
	else
	{
		TSharedPtr<FDSBroadcastMarkData> MarkData = MakeShared<FDSBroadcastMarkData>();
		MarkData->DisablePendantRefresh(ERefreshPendantFlags::RPF_RefreshGizmo);
		OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf, MarkData);
	}

	UpdateOutlineInfo();

	if (GetModelType() == EDSModelType::E_Custom_LayoutDoor)
	{
		UDSCustomLibrary::RefreshLayoutDoorOnWalls(this);
	}


	//柜体修改后刷新自适应逻辑
	if (UDSToolLibrary::IsCustomCabinetType(ModelType))
	{
		if (!AdaptationOperator.IsValid())
		{
			AdaptationOperator = MakeShared<FCupboardModelAdaptationOperator>(this);
			AdaptationOperator->PrepareAdaptation(this);
		}

		AdaptationOperator->ExecuteStepAdaptation();
		HandleChildrenFunctionalModelAdaptation();
	}
}

void UDSCupboardModel::PreTranslateByGizmo(const EAxis::Type& InRotAxis, const FTransform& SourceTrans, const FTransform& NewTrans)
{
	Super::PreTranslateByGizmo(InRotAxis, SourceTrans, NewTrans);
}

void UDSCupboardModel::PostTranslateByGizmo(const EAxis::Type& InRotAxis, const FTransform& SourceTrans, const FTransform& NewTrans)
{
	Super::PostTranslateByGizmo(InRotAxis, SourceTrans, NewTrans);
	if (GetModelType() == EDSModelType::E_Custom_LayoutDoor)
	{
		UDSCustomLibrary::RefreshLayoutDoorOnWalls(this);
	}
	if (!FMath::IsNearlyEqual(SourceTrans.GetLocation().Z, NewTrans.GetLocation().Z, 0.01f))
	{
		ModelInfo.ComponentTreeData->SetParameterAdaptive(TEXT("LDH"), false);
	}
}
void UDSCupboardModel::PostRotatorByGizmo(const EAxis::Type& InRotAxis, const FTransform& SourceTrans, const FTransform& NewTrans)
{
	Super::PostRotatorByGizmo(InRotAxis, SourceTrans, NewTrans);
	OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf, FDSBroadcastMarkData::NotBroadcastToMVCMark);
	if (GetModelType() == EDSModelType::E_Custom_LayoutDoor)
	{
		UDSCustomLibrary::RefreshLayoutDoorOnWalls(this);
	}
}

void UDSCupboardModel::RotatorByGizmo(const EAxis::Type& InRotAxis, const FTransform& SourceTrans, const FTransform& NewTrans)
{
	Super::RotatorByGizmo(InRotAxis, SourceTrans, NewTrans);
}

FVector UDSCupboardModel::GetCurrentSize()
{
	if (ModelInfo.ComponentTreeData)
	{
		int32 WidthPos = ModelInfo.ComponentTreeData->ComponentParameters.IndexOfByPredicate(
			[](const FParameterData& InParam)
			{
				return InParam.Data.name.Equals(TEXT("W"));
			});

		int32 DepthPos = ModelInfo.ComponentTreeData->ComponentParameters.IndexOfByPredicate(
			[](const FParameterData& InParam)
			{
				return InParam.Data.name.Equals(TEXT("D"));
			});

		int32 HeightPos = ModelInfo.ComponentTreeData->ComponentParameters.IndexOfByPredicate(
			[](const FParameterData& InParam)
			{
				return InParam.Data.name.Equals(TEXT("H"));
			});

		if (WidthPos != INDEX_NONE && DepthPos != INDEX_NONE && HeightPos != INDEX_NONE)
		{
			return FVector(
				FCString::Atof(*ModelInfo.ComponentTreeData->ComponentParameters[WidthPos].Data.value),
				FCString::Atof(*ModelInfo.ComponentTreeData->ComponentParameters[DepthPos].Data.value),
				FCString::Atof(*ModelInfo.ComponentTreeData->ComponentParameters[HeightPos].Data.value));
		}
	}

	return FVector::ZeroVector;
}

FVector UDSCupboardModel::GetCurrentRealSize()
{
	if (ModelInfo.ComponentTreeData)
	{
		int32 WidthPos = ModelInfo.ComponentTreeData->ComponentParameters.IndexOfByPredicate(
			[](const FParameterData& InParam)
			{
				return InParam.Data.name.Equals(TEXT("W"));
			});

		int32 DepthPos = ModelInfo.ComponentTreeData->ComponentParameters.IndexOfByPredicate(
			[](const FParameterData& InParam)
			{
				return InParam.Data.name.Equals(TEXT("D"));
			});

		int32 HeightPos = ModelInfo.ComponentTreeData->ComponentParameters.IndexOfByPredicate(
			[](const FParameterData& InParam)
			{
				return InParam.Data.name.Equals(TEXT("H"));
			});


		if (WidthPos != INDEX_NONE && DepthPos != INDEX_NONE && HeightPos != INDEX_NONE)
		{
			FString Width = ModelInfo.ComponentTreeData->ComponentParameters[WidthPos].Data.GetRealValue();
            FString Depth = ModelInfo.ComponentTreeData->ComponentParameters[DepthPos].Data.GetRealValue();
            FString Height = ModelInfo.ComponentTreeData->ComponentParameters[HeightPos].Data.GetRealValue();

            return FVector(FCString::Atod(*Width), FCString::Atod(*Depth), FCString::Atod(*Height));
		}
	}

	return FVector::ZeroVector;
}

void UDSCupboardModel::SyncScale(const FVector& InScale)
{
	FVector RealSize = InScale;
	//w
	int32 WIndex = ModelInfo.ComponentTreeData->ComponentParameters.IndexOfByPredicate(
		[&](const FParameterData& InParam)
		{
			return InParam.Data.name.Equals(GetModelType() == EDSModelType::E_Custom_LayoutDoor ?  TEXT("MDW") : TEXT("W"));
		});
	if (WIndex != INDEX_NONE)
	{
        ModelInfo.ComponentTreeData->ComponentParameters[WIndex].Data.value = FString::Printf(TEXT("%f"), InScale.X);
        ModelInfo.ComponentTreeData->ComponentParameters[WIndex].Data.expression = FString::Printf(TEXT("%f"), InScale.X);

		RealSize.X = FCString::Atod(*ModelInfo.ComponentTreeData->ComponentParameters[WIndex].Data.FormatValueRet());
	}
	//d
	int32 DIndex = ModelInfo.ComponentTreeData->ComponentParameters.IndexOfByPredicate(
		[&](const FParameterData& InParam)
		{
			return InParam.Data.name.Equals(GetModelType() == EDSModelType::E_Custom_LayoutDoor ? TEXT("MDD") : TEXT("D"));
		});
    if (DIndex != INDEX_NONE)
    {
        ModelInfo.ComponentTreeData->ComponentParameters[DIndex].Data.value = FString::Printf(TEXT("%f"), InScale.Y);
        ModelInfo.ComponentTreeData->ComponentParameters[DIndex].Data.expression = FString::Printf(TEXT("%f"), InScale.Y);

		RealSize.Y = FCString::Atod(*ModelInfo.ComponentTreeData->ComponentParameters[DIndex].Data.FormatValueRet());
    }
	//h
    int32 HIndex = ModelInfo.ComponentTreeData->ComponentParameters.IndexOfByPredicate(
		[&](const FParameterData& InParam)
		{
			return InParam.Data.name.Equals(GetModelType() == EDSModelType::E_Custom_LayoutDoor ? TEXT("MDH") : TEXT("H"));
		});
	if (HIndex != INDEX_NONE)
	{
        ModelInfo.ComponentTreeData->ComponentParameters[HIndex].Data.value = FString::Printf(TEXT("%f"), InScale.Z);
        ModelInfo.ComponentTreeData->ComponentParameters[HIndex].Data.expression = FString::Printf(TEXT("%f"), InScale.Z);

		RealSize.Z = FCString::Atod(*ModelInfo.ComponentTreeData->ComponentParameters[HIndex].Data.FormatValueRet());
	}
	
	Property->SizeProperty.Width = RealSize.X;
	Property->SizeProperty.Depth = RealSize.Y;
	Property->SizeProperty.Height = RealSize.Z;

	OnExecuteAction(FDSModelExecuteType::ExecuteAll, FDSBroadcastMarkData::OnlyOutlineBroadcastMark);
}

void UDSCupboardModel::ReInitData()
{
	FVector CurrentSize = GetCurrentRealSize();
	Property->SizeProperty.SetSize(CurrentSize);
	UpdateOutlineInfo();

	UDSMVCSubsystem::GetInstance()->OnExecuteRefreshDrawing(this, FDSModelExecuteType::ExecuteTransform);
}

bool UDSCupboardModel::IsValueInRange(const FString& InName, const float& InValue)
{
	FParameterData* WPtr = ModelInfo.ComponentTreeData->ComponentParameters.FindByPredicate(
		[&InName](const FParameterData& InParam)->bool
		{
			return InParam.Data.name.Equals(InName);
		}
	);
	if (WPtr != nullptr)
	{
		bool IsValueValid = WPtr->Data.IsValueInMinMax(InValue);
		if (!IsValueValid)
			return false;
	}

	return true;
}

bool UDSCupboardModel::CurrentSizeValidToScale(const FVector& InScale)
{
	//w
	if (!IsValueInRange(TEXT("W"), InScale.X))
	{
		return false;
	}

	//d
	if (!IsValueInRange(TEXT("D"), InScale.Y))
	{
		return false;
	}

	//h
	if (!IsValueInRange(TEXT("H"), InScale.Z))
	{
		return false;
	}

	return true;
}

bool UDSCupboardModel::SupportsLazyLoad() const
{
	return true;
}

bool UDSCupboardModel::ShouldUseParentModel() const
{
	if ((ModelType == EDSModelType::E_House_Door || ModelType == EDSModelType::E_House_Window) && OwnerModel != nullptr)
	{
		return true;
	}
	if (!UDSMVCSubsystem::IsInitialized())
	{
		return false;
	}

	UDSFiniteState* FiniteState = UDSMVCSubsystem::GetInstance()->GetState();
	if (FiniteState == nullptr)
	{
		return false;
	}

	return FiniteState->GetMouseSelectType() == EDSSelectType::E_Whole_Select && OwnerModel != nullptr
		&& (!IsFunctionalCupboardModel() || FiniteState->GetState() == EDSFSMState::FSM_CounterTop);
}

void UDSCupboardModel::OnResourceStatusInPoolChanged(const FString& Identify, EDSResourceType ResourceType,
                                                     EResourceStatusEventType EventType)
{
	FChangeObserveResourceHelper ObserveHelper(this);

	TSharedPtr<FDSResourceInfo> FoundResourceInfo;
	for (auto It = LazyLoadResources.CreateIterator(); It; ++It)
	{
		if (It->Value != ResourceType)
		{
			continue;
		}

		TSharedPtr<FDSResourceInfo> ResourceInfo = UDSResourceSubsystem::GetInstance()->FindResourceFromCache(It->Key);
		if (!ResourceInfo)
		{
			continue;
		}

		FDSResourceFile FileInfo = ResourceInfo->GetResourceFile(EDSResourceQuality::Low);
		FString ResourceIdentify = ResourceType == EDSResourceType::Material ? FileInfo.MD5 : FPaths::Combine(FPaths::ProjectSavedDir(), TEXT("Resources"), FileInfo.ComputeLocalFileName());
		if (ResourceIdentify == Identify)
		{
			FoundResourceInfo = ResourceInfo;
			It.RemoveCurrent();
			break;
		}
	}

	if (!FoundResourceInfo)
	{
		return;
	}

	if (ResourceType == EDSResourceType::Model)
	{
		ApplyResourceForImportModelNode(FoundResourceInfo, ModelInfo.ComponentTreeData);
	}

	if (LazyLoadResources.IsEmpty())
	{
		bResourceAllLoaded = true;

		OnExecuteAction(FDSModelExecuteType::ExecuteAll, FDSBroadcastMarkData::NotBroadcastToMVCMark);
	}
}

bool UDSCupboardModel::CheckIfNeedsStopObserveResourceStatus() const
{
	if (!HasParsed())
	{
		return false;
	}

	return bResourceAllLoaded;
}

void UDSCupboardModel::Serialization(TSharedRef<TJsonWriter<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>> JsonWriter)
{
	Super::Serialization(JsonWriter);
	JsonWriter->WriteObjectStart(TEXT("ModelInfo"));
	{
		ModelInfo.Serialization(JsonWriter);
	}
	JsonWriter->WriteObjectEnd();

	if (SubFunctionalNodeDependencyMap.IsValid())
	{
		JsonWriter->WriteObjectStart(TEXT("SubFunctionalNodeDependencyMap"));
		SubFunctionalNodeDependencyMap->Serialization(JsonWriter);
		JsonWriter->WriteObjectEnd();
	}

}

void UDSCupboardModel::Deserialization(const TSharedPtr<FJsonObject>& InJsonData, bool bDeserializeID)
{
	Super::Deserialization(InJsonData,bDeserializeID);

	InterruptParse();

	ModelInfo.Deserialization(InJsonData->GetObjectField(TEXT("ModelInfo")));


	if (InJsonData->HasField(TEXT("SubFunctionalNodeDependencyMap")))
	{
		SubFunctionalNodeDependencyMap = MakeShared<FFunctionalDependencyMap>();
		SubFunctionalNodeDependencyMap->Deserialization(InJsonData->GetObjectField(TEXT("SubFunctionalNodeDependencyMap")));
	}
	if (!OriginalComponentData)
	{
		OriginalComponentData = MakeShared<FMultiComponentDataItem>();
	}
	OriginalComponentData->DeepCopy(*ModelInfo.ComponentTreeData);

	OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf, FDSBroadcastMarkData::SpawnBroadcastMark);
}

TArray<FVector> UDSCupboardModel::GetModelOrientedBoundingBox(bool bUseFixedSize) const
{
	TSharedPtr<FDSFurnitureBaseProperty> CastedProperty = StaticCastSharedPtr<FDSFurnitureBaseProperty>(Property);
	if (!CastedProperty)
	{
		return Super::GetModelOrientedBoundingBox(bUseFixedSize);
	}

	FVector BoxSize;
	if (bUseFixedSize)
	{
		BoxSize = UDSCupboardLibrary::GetFixedSizeParameter(this);
	}
	else
	{
		BoxSize = CastedProperty->SizeProperty.ToVector() * 0.1f;
	}
	
	if (GetModelType() == EDSModelType::E_Custom_LayoutDoor)
	{
		GetCustomLayoutDoorSize(BoxSize);
	}
	
	FVector BoxOffset;
	if (bUseFixedSize)
	{
		BoxOffset = UDSCupboardLibrary::GetFixedOffsetParameter(this);
	}
	else
	{
		BoxOffset.InitFromString(CastedProperty->BusinessInfo.BoxOffset);
	}

	TArray<FVector> ResultPoints;
	ResultPoints.Add(BoxOffset + FVector(0, 0, BoxSize.Z));
	ResultPoints.Add(BoxOffset + FVector(BoxSize.X, 0, BoxSize.Z));
	ResultPoints.Add(BoxOffset + FVector(BoxSize.X, BoxSize.Y, BoxSize.Z));
	ResultPoints.Add(BoxOffset + FVector(0, BoxSize.Y, BoxSize.Z));

	ResultPoints.Add(BoxOffset);
	ResultPoints.Add(BoxOffset + FVector(BoxSize.X, 0, 0));
	ResultPoints.Add(BoxOffset + FVector(BoxSize.X, BoxSize.Y, 0));
	ResultPoints.Add(BoxOffset + FVector(0, BoxSize.Y, 0));

	FTransform Transform = CastedProperty->GetActualTransform();
	
	if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), ModelInfo.ComponentTreeData->ModelType))
	{
		Transform = UDSCupBoardDoorLibrary::CalDoorWorldTransform(this);
	}

	for (FVector& Point : ResultPoints)
	{
		Point = Transform.TransformPosition(Point);
	}

	return ResultPoints;
}

TArray<FVector> UDSCupboardModel::GetModelOrientedBoundingBoxWithoutRotator(bool bUseFixedSize) const
{
	TSharedPtr<FDSFurnitureBaseProperty> CastedProperty = StaticCastSharedPtr<FDSFurnitureBaseProperty>(Property);
	if (!CastedProperty)
	{
		return Super::GetModelOrientedBoundingBox(bUseFixedSize);
	}

	FVector BoxSize;
	if (bUseFixedSize)
	{
		BoxSize = UDSCupboardLibrary::GetFixedSizeParameter(this);
	}
	else
	{
		BoxSize = CastedProperty->SizeProperty.ToVector() * 0.1f;
	}

	if (GetModelType() == EDSModelType::E_Custom_LayoutDoor)
	{
		GetCustomLayoutDoorSize(BoxSize);
	}

	FVector BoxOffset;
	if (bUseFixedSize)
	{
		BoxOffset = UDSCupboardLibrary::GetFixedOffsetParameter(this);
	}
	else
	{
		BoxOffset.InitFromString(CastedProperty->BusinessInfo.BoxOffset);
	}

	TArray<FVector> ResultPoints;
	ResultPoints.Add(BoxOffset + FVector(0, 0, BoxSize.Z));
	ResultPoints.Add(BoxOffset + FVector(BoxSize.X, 0, BoxSize.Z));
	ResultPoints.Add(BoxOffset + FVector(BoxSize.X, BoxSize.Y, BoxSize.Z));
	ResultPoints.Add(BoxOffset + FVector(0, BoxSize.Y, BoxSize.Z));

	ResultPoints.Add(BoxOffset);
	ResultPoints.Add(BoxOffset + FVector(BoxSize.X, 0, 0));
	ResultPoints.Add(BoxOffset + FVector(BoxSize.X, BoxSize.Y, 0));
	ResultPoints.Add(BoxOffset + FVector(0, BoxSize.Y, 0));

	FTransform Transform = CastedProperty->GetActualTransform();

	if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), ModelInfo.ComponentTreeData->ModelType))
	{
		Transform = UDSCupBoardDoorLibrary::CalDoorWorldTransform(this,false);
	}

	for (FVector& Point : ResultPoints)
	{
		Point = Transform.TransformPosition(Point);
	}

	return ResultPoints;
}

TArray<FVector> UDSCupboardModel::GetModelFixedOrientedBoundingBox() const
{
	TSharedPtr<FDSFurnitureBaseProperty> CastedProperty = StaticCastSharedPtr<FDSFurnitureBaseProperty>(Property);
	if (!CastedProperty)
	{
		return Super::GetModelOrientedBoundingBox();
	}

	FVector FixedExtents = UDSCupboardLibrary::GetFixedSizeParameter(this);

	if (GetModelType() == EDSModelType::E_Custom_LayoutDoor)
	{
		return GetModelOrientedBoundingBox();
	}
	
	FVector BoxOffset = UDSCupboardLibrary::GetFixedOffsetParameter(this);
	TArray<FVector> ResultPoints;
	ResultPoints.Add(BoxOffset + FVector(0, 0, FixedExtents.Z));
	ResultPoints.Add(BoxOffset + FVector(FixedExtents.X, 0, FixedExtents.Z));
	ResultPoints.Add(BoxOffset + FVector(FixedExtents.X, FixedExtents.Y, FixedExtents.Z));
	ResultPoints.Add(BoxOffset + FVector(0, FixedExtents.Y, FixedExtents.Z));

	ResultPoints.Add(BoxOffset);
	ResultPoints.Add(BoxOffset + FVector(FixedExtents.X, 0, 0));
	ResultPoints.Add(BoxOffset + FVector(FixedExtents.X, FixedExtents.Y, 0));
	ResultPoints.Add(BoxOffset + FVector(0, FixedExtents.Y, 0));

	FTransform Transform = CastedProperty->GetActualTransform();

	for (FVector& Point : ResultPoints)
	{
		Point = Transform.TransformPosition(Point);
	}

	return ResultPoints;
}

bool UDSCupboardModel::GetCustomLayoutDoorSize(FVector& OutSize) const
{
	if (GetModelType() == EDSModelType::E_Custom_LayoutDoor)
	{
		auto MDH = ModelInfo.ComponentTreeData->ComponentParameters.FindByPredicate(
			[](const FParameterData& InParam)
			{
				return InParam.Data.name.Equals(TEXT("MDH"));
			});
		auto MDD = ModelInfo.ComponentTreeData->ComponentParameters.FindByPredicate(
			[](const FParameterData& InParam)
			{
				return InParam.Data.name.Equals(TEXT("MDD"));
			});
		auto MDW = ModelInfo.ComponentTreeData->ComponentParameters.FindByPredicate(
			[](const FParameterData& InParam)
			{
				return InParam.Data.name.Equals(TEXT("MDW"));
			});
		if (MDH && MDD && MDW)
		{
			OutSize.X = FCString::Atof(*MDW->Data.value) * 0.1;
			OutSize.Y = FCString::Atof(*MDD->Data.value) * 0.1;
			OutSize.Z = FCString::Atof(*MDH->Data.value) * 0.1;
		}
		return true;
	}
	return false;
}

FTransform UDSCupboardModel::GetParentTransform()
{
	FTransform ParentTransform = FTransform::Identity;
	UDSBaseModel* OwnerModel = GetOwnerModel();
	while (OwnerModel != nullptr && Cast<UDSCupboardModel>(OwnerModel))
	{
		if (OwnerModel->GetOwnerModel() == nullptr)
		{
            ParentTransform = ParentTransform * OwnerModel->GetPropertySharedPtr()->GetActualTransform();
		}
		else
		{
			ParentTransform = ParentTransform * Cast<UDSCupboardModel>(OwnerModel)->GetRelativeTransform();
		}

		OwnerModel = OwnerModel->GetOwnerModel();
	}

	return ParentTransform;
}

void UDSCupboardModel::GetModelOrientedBoundingBox(FVector& Center, FVector& Extents, FQuat& Rotation) const
{
	TSharedPtr<FDSFurnitureBaseProperty> CastedProperty = StaticCastSharedPtr<FDSFurnitureBaseProperty>(Property);

	if (!CastedProperty)
	{
		return Super::GetModelOrientedBoundingBox(Center, Extents, Rotation);
	}
	//FVector BoxSize;
	Extents.X = Property->SizeProperty.Width * 0.1f;
	Extents.Y = Property->SizeProperty.Depth * 0.1f;
	Extents.Z = Property->SizeProperty.Height * 0.1f;
	Extents *= 0.5f;
	FVector BoxOffset;
	if (!BoxOffset.InitFromString(CastedProperty->BusinessInfo.BoxOffset))
	{
		BoxOffset = FVector::ZeroVector;
	}

	FTransform Transform = CastedProperty->GetActualTransform();
	Center = Transform.TransformPosition(BoxOffset + Extents);
	Rotation = Transform.Rotator().Quaternion();
}

FBox UDSCupboardModel::GetBoundBoxByPropertyCalculate()
{
	FBox Res(ForceInit);

	TSharedPtr<FDSFurnitureBaseProperty> CurProperty = StaticCastSharedPtr<FDSFurnitureBaseProperty>(Property);
	if (CurProperty.IsValid())
	{
		FVector Size = UDSCupboardLibrary::GetFixedSizeParameter(this);
		if (GetModelType() == EDSModelType::E_Custom_LayoutDoor)
		{
			GetCustomLayoutDoorSize(Size);
		}

		//relative base on self
		FVector Loc = FVector::ZeroVector;
		FVector FarPoint = Loc + FVector::XAxisVector * Size.X + FVector::YAxisVector * Size.Y + FVector::ZAxisVector * Size.Z;

		Res += Loc;
		Res += FarPoint;

		Res = Res.ShiftBy(UDSCupboardLibrary::GetFixedOffsetParameter(this));
	}

	return Res;
}

void UDSCupboardModel::CalculateOutline()
{
	FBox OBB = GetBoundBoxByPropertyCalculate();
	FTransform RealTrans = GetPropertySharedPtr()->GetActualTransform();
	//门板额外处理
	if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), GetModelInfoRef().ComponentTreeData->ModelType))
	{
		RealTrans = UDSCupBoardDoorLibrary::CalDoorWorldTransform(this);
	}
	OutlineInfo = UDSToolLibrary::CalculateOutlineByOBB(OBB, RealTrans);
}

void UDSCupboardModel::UpdateOutlineInfo()
{
	CalculateOutline();
	SyncChildOutline();
}

void UDSCupboardModel::SyncChildOutline()
{
	for (auto Iter : ModelInfo.ComponentInfoArr)
	{
		if (!Iter.ComponentModel)continue;
		Iter.ComponentModel->CalculateOutline();
		UDSDrawingSubsystem::GetInstance()->RefreshScreenLine(Iter.ComponentModel, FDSModelExecuteType::ExecuteTransform, FDSBroadcastMarkData::OnlyOutlineBroadcastMark);
		Iter.ComponentModel->SyncChildOutline();
	}
}

bool UDSCupboardModel::ExpandOutlineForComponentSelect()
{
	FBox OBB = GetBoundBoxByPropertyCalculate();
	OBB = OBB.ExpandBy(FVector(5.0));
	FTransform RealTrans = GetPropertySharedPtr()->GetActualTransform();
	OutlineInfo = UDSToolLibrary::CalculateOutlineByOBB(OBB, RealTrans);

	return true;
}

bool UDSCupboardModel::RecoverOutline()
{
	CalculateOutline();

	return true;
}

TArray<TArray<FVector>> UDSCupboardModel::GetWorldFrameOutline()
{
	return OutlineInfo.FrameOutline;
}

bool UDSCupboardModel::HasParsed() const
{
	return bParsed;
}

bool UDSCupboardModel::AreRelatedResourcesLoaded() const
{
	return bResourceAllLoaded;
}

UDSCupboardModel* UDSCupboardModel::GetRootCupboardModel()
{
	UDSCupboardModel* Result = this;
	while (Result->GetOwnerModel() != nullptr && Result->GetOwnerModel()->IsA<UDSCupboardModel>())
	{
		Result = Cast<UDSCupboardModel>(Result->GetOwnerModel());
	}

	return Result;
}

const FTransform& UDSCupboardModel::GetRelativeTransform() const
{
	return RelativeTransform;
}


TArray<FParameterData> UDSCupboardModel::GetParams() const
{
	return ModelInfo.ComponentTreeData->ComponentParameters;
}

TArray<FParameterData>& UDSCupboardModel::GetParamsRef() const
{
	return ModelInfo.ComponentTreeData->ComponentParameters;
}

void UDSCupboardModel::GetSelfComponentOverriderParametersRef(TMap<FString, FParameterData>& OutParentOverriderParmeters) const
{
	FParameterProcLibrary::CombineParameters(ParentComponentParams, ModelInfo.ComponentTreeData->ComponentParameters, OutParentOverriderParmeters);
}

FTransform UDSCupboardModel::GetWorldTransform()
{
	UDSCupboardModel* RootModel = GetRootCupboardModel();


	if (RootModel == this)
	{
		return GetProperty()->GetActualTransform();
	}

	return UDSCupboardLibrary::GetNodeTransform(RootModel, ModelInfo.ComponentTreeData);
}

void UDSCupboardModel::ReplaceComponent(const FRefToLocalFileData& InFileData)
{
	InterruptParse();

	//revoke
	TSharedPtr<FDSCustomComponentRevokeData> RevokeData = MakeShared<FDSCustomComponentRevokeData>();
	RevokeData->SetComponentData(ModelInfo.ComponentTreeData, true);
	RevokeData->SetComponentCommandType(EDSRevokeComponentType::E_Component_CustomReplace);
	RevokeData->SetNodePath(TEXT(""));
	FDSRevokePushData PushData(EDSPushDataType::E_Custom, FDSModelExecuteType::ExecuteUpdateSelf, false);
    PushData.SetData(FDSCustomPushData(FDSModelExecuteType::ExecuteUpdateSelf, this, RevokeData));
	UDSRevokeSubsystem::GetInstance()->InsertCommandDataUnion(
		this,
		FDSModelExecuteType::ExecuteUpdateSelf,
		PushData,
		UDSMVCSubsystem::GetInstance()->GetRevokeMark(), TEXT("")
	);

	// Convert file data to component data, preserve UUID.
	ModelInfo.RefFileData = InFileData;
	ModelInfo.ComponentTreeData->ComponentID.Value = InFileData.FolderDBData.folder_id;
	ModelInfo.ComponentTreeData->ComponentID.Expression = InFileData.FolderDBData.folder_id;

	FGeometryDatas::CalculateCurMultiComponentData(UDSMVCSubsystem::GetInstance()->GetGlobalParamsMap(), ParentComponentParams, *ModelInfo.ComponentTreeData);

	ModelInfo.ComponentTreeData->ChildComponent.Empty();
	for (const FRefToFileComponentData& ChildComponentData : InFileData.ComponentDatas)
	{
		ModelInfo.ComponentTreeData->ChildComponent.Add(MakeShared<FMultiComponentDataItem>(ChildComponentData.ConvertToMultiComponentDataItem()));
	}

	bParsed = false;

	OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf);
}

void UDSCupboardModel::UpdateParseStatus()
{
	bool bAllChildrenParsed = true;
	if (ModelInfo.ComponentInfoArr.IsValidIndex(0))
	{
		for (const FDSComponentInfo& ChildInfo : ModelInfo.ComponentInfoArr)
		{
			UDSCupboardModel* ChildModel = Cast<UDSCupboardModel>(ChildInfo.ComponentModel);
			if (ChildModel == nullptr)
			{
				continue;
			}

			if (!ChildModel->HasParsed())
			{
				bAllChildrenParsed = false;
				break;
			}
		}
	}

	if (bAllChildrenParsed)
	{
		OriginalComponentData.Reset();

		RefreshSizePropertyAfterUpdate();

		bParsed = true;
		OnExecuteAction(FDSModelExecuteType::ExecuteAll);

		if (OnCupboardModelParseCompleteHandle.IsBound())
		{
			OnCupboardModelParseCompleteHandle.Broadcast();
		}
		UDSCupboardModel* ParentModel = Cast<UDSCupboardModel>(OwnerModel);
		if (ParentModel != nullptr)
		{
			ParentModel->UpdateParseStatus();
		}

		/*if (GetOwnedView() != nullptr && Cast<ADSCupboardBaseView>(GetOwnedView()) != nullptr)
		{
			Cast<ADSCupboardBaseView>(GetOwnedView())->SetupCollisionIgnoreActor();
		}*/
	}
}

void UDSCupboardModel::UpdateDirect()
{
	OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf, FDSBroadcastMarkData::OnlyOutlineBroadcastMark);
}

void UDSCupboardModel::ShallowCopy(UDSBaseModel* OtherModel)
{
	ModelInfo = Cast<UDSCupboardModel>(OtherModel)->GetModelInfo();
}

EDSCustomCupboardLoadState UDSCupboardModel::GetLoadState() const
{
	return LoadState;
}

int32 UDSCupboardModel::GetFGType() const
{
	if (ModelInfo.ComponentTreeData.IsValid())
	{
		return ModelInfo.ComponentTreeData->GetFGType();
	}
	return INDEX_NONE;
}

bool UDSCupboardModel::IsEmbedded() const
{
	return GetFGType() == 1;
}

bool UDSCupboardModel::IsOuterCover() const
{
	return GetFGType() == 0;
}

void UDSCupboardModel::CalculateModelAfterModelInfoValid()
{
	if (!OriginalComponentData)
	{
		OriginalComponentData = MakeShared<FMultiComponentDataItem>();
	}
	OriginalComponentData->DeepCopy(*ModelInfo.ComponentTreeData);


	if (!IsFunctionalCupboardModel())
	{
		OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf);
	}
}

TArray<UDSBaseModel*> UDSCupboardModel::RemoveSelfFromDataTree()
{
	TArray<UDSBaseModel*> ResultModels;

	UDSCupboardModel* ParentModel = Cast<UDSCupboardModel>(GetOwnerModel());
	if (ParentModel == nullptr)
	{
		return ResultModels;
	}

	ResultModels.AddUnique(ParentModel);

	TArray<TSharedPtr<FMultiComponentDataItem>> ComponentPath;
	CollectComponentPath(ParentModel->GetModelInfoRef().ComponentTreeData, ModelInfo.ComponentTreeData, ComponentPath);

	ComponentPath.Pop();
	if (ComponentPath.IsEmpty())
	{
		return ResultModels;
	}

	ComponentPath.Last()->ChildComponent.Remove(ModelInfo.ComponentTreeData);

	if (!UDSModelDependencySubsystem::IsInitialized() || !UDSMVCSubsystem::IsInitialized())
	{
		return ResultModels;
	}

	if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), ModelInfo.ComponentTreeData->ModelType))
	{
		if (!UDSModelDependencySubsystem::GetInstance()->IsGeneratedDoor(ModelInfo.ComponentTreeData->UUID))
		{
			return ResultModels;
		}

		UDSModelDependencySubsystem::GetInstance()->RemoveDoorDependencyInfo(ModelInfo.ComponentTreeData->UUID);
		while (ComponentPath.Last()->ChildComponent.IsEmpty() && ComponentPath.Num() > 1)
		{
			TSharedPtr<FMultiComponentDataItem> ContainerNode = ComponentPath.Pop();
			ComponentPath.Last()->ChildComponent.Remove(ContainerNode);
		}
	}
	else if (UDSToolLibrary::IsCustomBoardType(ModelType))
	{
		TArray<FString> DependentDoors = UDSModelDependencySubsystem::GetInstance()->FindDoorByDependentBoard(ModelInfo.ComponentTreeData->UUID);
		if (DependentDoors.IsEmpty())
		{
			return ResultModels;
		}

		TArray<UDSBaseModel*> AllCustomModels = UDSMVCSubsystem::GetInstance()->GetAllCustomModels();

		for (UDSBaseModel* CustomModel : AllCustomModels)
		{
			UDSCupboardModel* CastedModel = Cast<UDSCupboardModel>(CustomModel);
			if (CastedModel == nullptr)
			{
				continue;
			}

			if (DependentDoors.Contains(CastedModel->GetModelInfoRef().ComponentTreeData->UUID))
			{
				CastedModel->RemoveSelfFromDataTree();
				ResultModels.AddUnique(CastedModel->GetRootCupboardModel());
			}
		}
	}

	return ResultModels;
}

void UDSCupboardModel::ApplyNewStyle(const FApplyStyleData& InNewStyle)
{
	InterruptQueryMaterialByStyle();
	
	ModelInfo.ApplyStyle_Whole = InNewStyle;
	
	// 根据风格刷新定制树样式
	TArray<FDSCustomStyleReplacementNode> StylizedNodes;
	TArray<TPair<FString, TWeakPtr<FMultiComponentDataItem>>> NodePath;
	UDSCupboardLibrary::CollectAllStylizedNodeInfos(ModelInfo.ComponentTreeData, StylizedNodes, NodePath);
	QueryStyleReplacements(StylizedNodes, InNewStyle, EDSCustomAssociationType::Pattern);
}

void UDSCupboardModel::ApplyRootStyle_Functional()
{
	InterruptQueryMaterialByStyle();

	UDSCupboardModel* RootModel = GetRootCupboardModel();

	if (RootModel->GetModelInfoRef().ApplyStyle_Whole.IsValid())
	{
		// 根据风格刷新定制树样式
		TArray<FDSCustomStyleReplacementNode> StylizedNodes;
		TArray<TPair<FString, TWeakPtr<FMultiComponentDataItem>>> NodePath;
		UDSCupboardLibrary::CollectAllStylizedNodeInfos(RootModel->GetComponentTreeDataRef(), StylizedNodes, NodePath);
		QueryStyleReplacements(StylizedNodes, RootModel->GetModelInfoRef().ApplyStyle_Whole, EDSCustomAssociationType::Pattern);	
	}
}

FApplyStyleData UDSCupboardModel::GetApplyStyleData() const
{
	return ModelInfo.ApplyStyle_Whole;
}

void UDSCupboardModel::InterruptParse()
{
	LazyLoadResources.Empty();

	if (RequestHandle.IsValid() && RequestHandle->GetStatus() == EHttpRequestStatus::Processing)
	{
		RequestHandle->OnProcessRequestComplete().Unbind();
		RequestHandle->CancelRequest();
		RequestHandle.Reset();
	}

	UWorld* World = GetWorld();
	if (World == nullptr)
	{
		return;
	}

	UEasyNetworkFileSubsystem* NetworkFileSubsystem = World->GetSubsystem<UEasyNetworkFileSubsystem>();
	if (NetworkFileSubsystem == nullptr)
	{
		return;
	}

	const TMap<FString, UBaseNetworkFileTask*>& TaskMap = NetworkFileSubsystem->GetTaskMap();
	for (const FString& TaskId : ProcessingTaskIds)
	{
		if (TaskMap.Contains(TaskId))
		{
			TaskMap[TaskId]->Cancel();
		}
	}

	ProcessingTaskIds.Empty();

	InterruptQueryMaterialByStyle();
}

FBox UDSCupboardModel::GetBoundBox()
{
	FBox BoundBox = FBox(ForceInit);
	//view bound box
	FBox ViewBound = UDSToolLibrary::GetViewBoundBoxByModel(this);
	BoundBox += ViewBound;
	//separate 
	for (auto& MI : ModelInfo.ComponentInfoArr)
	{
		if (DS_MODEL_VALID_FOR_USE(MI.ComponentModel))
		{
			FBox CompBox = UDSToolLibrary::GetViewBoundBoxByModel(MI.ComponentModel);
			BoundBox += CompBox;
		}
	}
	return BoundBox;
}

void UDSCupboardModel::PreChangeEnumParameterValueFromWidget(const FString& InParamName, const FString& InOldValue,
	const FString& InNewValue)
{
	if (GetModelType() == EDSModelType::E_Custom_WallBoardCabinet)
	{
		if (InParamName == TEXT("YXFF"))
		{
			if (InOldValue != InNewValue)
			{
				for (auto& NodeIte : GetModelInfoRef().ComponentTreeData->ChildComponent)
				{
					FParameterData* FindParam = NodeIte->ComponentParameters.FindByPredicate
					(
						[](const FParameterData& Param)->bool
						{
							return Param.Data.name.Equals(TEXT("HQBPIAN"));
						}
					);

					if (FindParam != nullptr)
					{
						FindParam = NodeIte->ComponentParameters.FindByPredicate
						(
							[](const FParameterData& Param)->bool
							{
								return Param.Data.name.Equals(TEXT("HQV"));
							}
						);

						if (FindParam != nullptr)
						{
							FindParam->Data.value = FindParam->Data.expression = TEXT("0.0");
						}

						//移除保存到根节点的材质参数
						UDSWallBoardLibrary::RemoveWallBoardMaterialInfo(this, (EWallBoardType)FCString::Atoi(*FindParam->Data.value));
					}
				}
			}
		}
	}

}

void UDSCupboardModel::PreChangeDecimalParameterValueFromWidget(const FString& InParamName, const FString& InOldValue,
	const FString& InNewValue)
{
	if (IsFunctionalCupboardModel())
	{
		//改变宽高深
		if (InParamName.Equals(PARAM_W_STR)|| InParamName.Equals(PARAM_H_STR)|| InParamName.Equals(PARAM_D_STR))
		{
			UDSCupboardModel* RootModel = GetRootCupboardModel();
			if (RootModel->IsValid())
			{
				TArray<TSharedPtr<FMultiComponentDataItem>> OutPath;
				CollectComponentPath(RootModel->GetModelInfo().ComponentTreeData, ModelInfo.ComponentTreeData,OutPath);
				RelativeTransform = RootModel->Property->GetActualTransform().Inverse()* Property->GetActualTransform();
				for (size_t i = 0; i < OutPath.Num()-1; i++)
				{
					if (i== 0)
					{
						continue;
					}
					const auto& PathNode = OutPath[i];
					FTransform NodeRelativeTransform(PathNode->ComponentRotation.GetRotation(), PathNode->ComponentLocation.GetLocation(),
						PathNode->ComponentScale.GetScale());
					RelativeTransform = NodeRelativeTransform.Inverse() * RelativeTransform;
				}
				ModelInfo.ComponentTreeData->ComponentLocation = RelativeTransform.GetLocation() * 10.0f;
				ModelInfo.ComponentTreeData->ComponentRotation = RelativeTransform.GetRotation().Rotator();
				ModelInfo.ComponentTreeData->ComponentScale = RelativeTransform.GetScale3D();
			}
		}
	}
}

void UDSCupboardModel::PostChangeEnumParameterValueFromWidget(const FString& InParamName, const FString& InOldValue, const FString& InNewValue)
{
	ModelInfo.ComponentTreeData->SetParameterAdaptive(InParamName, false);
	
	//柜体修改后刷新自适应逻辑
	if (UDSToolLibrary::IsCustomCabinetType(ModelType))
	{
		if (!AdaptationOperator.IsValid())
		{
			AdaptationOperator = MakeShared<FCupboardModelAdaptationOperator>(this);
			AdaptationOperator->PrepareAdaptation(this);
		}

		AdaptationOperator->ExecuteStepAdaptation();
		HandleChildrenFunctionalModelAdaptation();
	}
	else if (IsFunctionalCupboardModel())
	{
		if (UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(ModelInfo.ComponentTreeData->ModelType) == EDSModelType::E_Custom_FunctionalDrawer
			&& InParamName.Equals(TEXT("MYGLX")))
		{
			if (FMath::IsNearlyZero(FCString::Atod(*InNewValue), 0.01f))
			{
				//抽屉内缩改为外盖时，重置FG类型
				ModelInfo.ComponentTreeData->SetParameter(PARAM_SBFG, FString::FromInt(0));
				ModelInfo.ComponentTreeData->SetParameter(PARAM_XBFG, FString::FromInt(0));
				ModelInfo.ComponentTreeData->SetParameter(PARAM_ZBFG, FString::FromInt(0));
				ModelInfo.ComponentTreeData->SetParameter(PARAM_YBFG, FString::FromInt(0));
			}
			else if(FMath::IsNearlyZero(FCString::Atod(*InNewValue) - 1.0, 0.01f))
			{//外盖改内嵌, 重置高度自适应
				if (FParameterData* Param_H = ModelInfo.ComponentTreeData->ComponentParameters.FindByPredicate(
					[](const FParameterData& InParam) 
					{ 
						return InParam.Data.name == TEXT("H"); 
					}))
				{
					Param_H->bEnableAdaptation = true;
				}
			}
		}

		if (!AdaptationOperator.IsValid())
		{
			AdaptationOperator = CreateAdaptationOperator();
		}
		if (AdaptationOperator.IsValid())
		{
			AdaptationOperator->ExecuteStepAdaptation();
		}
		
		TArray<FString> DoorIds = UDSModelDependencySubsystem::GetInstance()->FindDoorByDependentBoard(GetModelInfoRef().ComponentTreeData->UUID);

		if (!DoorIds.IsEmpty())
		{
			UDSCupBoardDoorLibrary::UpdateDoorOnCupboard(this->GetRootCupboardModel());
		}
	}
	else if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), GetModelInfoRef().ComponentTreeData->ModelType))
	{
		if (InParamName == TEXT("MBLX"))
		{
			if (InNewValue != TEXT("0.0") && InNewValue != TEXT("1.0"))
			{
				FParameterData* FindParam = ModelInfo.ComponentTreeData->ComponentParameters.FindByPredicate([](const FParameterData& InData)->bool { return  InData.Data.name.Equals(TEXT("DKJD")); });
				if (FindParam)
				{
					FindParam->Data.value = TEXT("0");
					FindParam->Data.expression = TEXT("0");
					UDSUISubsystem::GetInstance()->GetFunctionBarBoxWidget()->SwitchButtons(this, false);
				}
			}

			UDSCupBoardDoorLibrary::UpdateDoorOnCupboard(this->GetRootCupboardModel(), this);
		}
		else if (InParamName == TEXT("MYGLX"))
		{
			UDSCupBoardDoorLibrary::OnDoorConveringTypeChange(this, InNewValue);
			UDSCupBoardDoorLibrary::UpdateDoorOnCupboard(this->GetRootCupboardModel(), this);
		}
		else if (InParamName == TEXT("SBFG") || InParamName == TEXT("XBFG") || InParamName == TEXT("ZBFG") || InParamName == TEXT("YBFG"))
		{
			UDSCupBoardDoorLibrary::ChangeCoverByProperty(this, InParamName, InOldValue, InNewValue);
		}
	}
}

void UDSCupboardModel::PostChangeDecimalParameterValueFromWidget(const FString& InParamName, const FString& InOldValue, const FString& InNewValue)
{
	ModelInfo.ComponentTreeData->SetParameterAdaptive(InParamName, false);
	if (UDSToolLibrary::IsCustomCabinetType(ModelType))
	{
		if (!AdaptationOperator.IsValid())
		{
			AdaptationOperator = MakeShared<FCupboardModelAdaptationOperator>(this);
			AdaptationOperator->PrepareAdaptation(this);
		}
		AdaptationOperator->ExecuteStepAdaptation();
		HandleChildrenFunctionalModelAdaptation();
	}
	else if (IsFunctionalCupboardModel())
	{
		if (!AdaptationOperator.IsValid())
		{
			AdaptationOperator = CreateAdaptationOperator();
		}
		if (AdaptationOperator.IsValid())
		{
			AdaptationOperator->ExecuteStepAdaptation();
		}
	}

}

FOnCupboardModelApplyStyleComplete& UDSCupboardModel::OnCupboardModelApplyStyleCompleteEvent()
{
	return OnCupboardModelApplyStyleComplete;
}

void UDSCupboardModel::PreParseTreeFromNode()
{
	TArray<FDSCustomStyleReplacementNode> StylizedNodes;
	TMap<FString, EDSResourceType> FolderIdsForDownload;
	if (!UDSCupboardLibrary::ParseTreeFromNode(GetRootCupboardModel()->GetComponentTreeDataRef(), ModelInfo.ComponentTreeData,
		OriginalComponentData, ParentComponentParams,
		FOnProcessParsedByOriginalNodeDelegate::CreateUObject(this, &UDSCupboardModel::ProcessNodeAfterParsed),
		ComponentOverrideParams,
		FolderIdsForDownload, StylizedNodes))
	{
		UE_LOG(LogDSCupboardModel, Error, TEXT("Failed to parse custom tree from node '%s'."), *ModelInfo.ComponentTreeData->ComponentID.GetFormattedValue());
		return;
	}

	if (!FolderIdsForDownload.IsEmpty())
	{
		QueryRelatedResourceList(FolderIdsForDownload);
	}
}

void UDSCupboardModel::UpdateNodesToDisableCollision()
{
	NodesToDisableCollision.Empty();
	UDSCupboardLibrary::CollectNodesToDisableCollision(ModelInfo.ComponentTreeData, false, NodesToDisableCollision);
}

void UDSCupboardModel::VerifyNeedsLazyLoadResources()
{
	if (LazyLoadResources.IsEmpty())
	{
		return;
	}

	for (auto It = LazyLoadResources.CreateIterator(); It; ++It)
	{
		TSharedPtr<FDSResourceInfo> ResourceInfo = UDSResourceSubsystem::GetInstance()->FindResourceFromCache(It->Key);
		if (!ResourceInfo)
		{
			continue;
		}

		FDSResourceFile FileInfo = ResourceInfo->GetResourceFile(EDSResourceQuality::Low);
		switch (It->Value)
		{
		case EDSResourceType::Material:
			{
				if (UDSResourceSubsystem::GetInstance()->FindMaterialFromCache(FileInfo.MD5) != nullptr)
				{
					It.RemoveCurrent();
				}
			}
			break;
		case EDSResourceType::Model:
			{
				if (UDSResourceSubsystem::GetInstance()->IsPakFileMounted(FPaths::Combine(FPaths::ProjectSavedDir(), TEXT("Resources"), FileInfo.ComputeLocalFileName())))
				{
					It.RemoveCurrent();
				}
			}
			break;
		default:
			break;
		}
	}
}

void UDSCupboardModel::CollectRelatedLazyLoadResource(const TSharedPtr<FMultiComponentDataItem>& InNode)
{
	if (!InNode)
	{
		return;
	}

	if (InNode->ComponentType == ECompType::ImportModel)
	{
		LazyLoadResources.Add(InNode->ComponentID.GetFormattedValue(), EDSResourceType::Model);
	}
	else if (InNode->ComponentType == ECompType::SingleCom)
	{
		for (const FSingleComponentItem& ComponentItem : InNode->SingleComponentData.ComponentItems)
		{
			if (ComponentItem.ComponentMaterial.Value.IsEmpty() || ComponentItem.ComponentMaterial.Value.Equals(TEXT("0.0")))
			{
				continue;
			}

			FString LocalMaterialChecksum = UDSResourceSubsystem::GetInstance()->FindLocalZipMaterialChecksum(FCString::Atoi64(*ComponentItem.ComponentMaterial.Value));
			if (!LocalMaterialChecksum.IsEmpty())
			{
				continue;
			}

			LazyLoadResources.Add(ComponentItem.ComponentMaterial.GetFormattedValue(), EDSResourceType::Material);
		}
	}
}

void UDSCupboardModel::SplitOverrideParamsPoolBySubtree(const TSharedPtr<FMultiComponentDataItem>& InNode,
                                                        TMap<TSharedPtr<FMultiComponentDataItem>, TSharedPtr<TMap<FString, FParameterData>>>& ComponentOverrideParamsPool,
                                                        TMap<TSharedPtr<FMultiComponentDataItem>, TSharedPtr<TMap<FString, FParameterData>>>& OutComponentOverrideParams)
{
	if (ComponentOverrideParamsPool.Contains(InNode))
	{
		OutComponentOverrideParams.Add(InNode, ComponentOverrideParamsPool[InNode]);
	}

	for (const TSharedPtr<FMultiComponentDataItem>& ChildNode : InNode->ChildComponent)
	{
		SplitOverrideParamsPoolBySubtree(ChildNode, ComponentOverrideParamsPool, OutComponentOverrideParams);
	}
}

void UDSCupboardModel::ProcessNodeAfterParsed(const TSharedPtr<FMultiComponentDataItem>& InOriginalNode,
                                              const TSharedPtr<FMultiComponentDataItem>& InParsedNode)
{
	// Check if door related board has changed.
	if (UDSToolLibrary::IsCustomBoardType(UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(InParsedNode->ModelType)))
	{
		if (!IsParameterEquals(TEXT("W"), InOriginalNode, InParsedNode) ||
			!IsParameterEquals(TEXT("H"), InOriginalNode, InParsedNode) ||
			!IsParameterEquals(TEXT("D"), InOriginalNode, InParsedNode))
		{
			TArray<FString> RelativeDoors = UDSModelDependencySubsystem::GetInstance()->FindDoorByDependentBoard(InParsedNode->UUID);
			for (const FString& RelativeDoor : RelativeDoors)
			{
				UDSModelDependencySubsystem::GetInstance()->MarkDependentBoardChanged(RelativeDoor);
			}
		}
	}
}

void UDSCupboardModel::ProcessSeparateChildren(const TSharedPtr<FMultiComponentDataItem>& InRoot, const TSharedPtr<FMultiComponentDataItem>& InParent,
                                               const TSharedPtr<FMultiComponentDataItem>& InNode)
{
	if (UDSCupboardLibrary::ComponentNeedSeparate(InNode->ComponentParameters))
	{
		if (InNode->IsVisiable())
		{
			CreateSeparateChild(InRoot, InParent, InNode);
		}
		else
		{
			TMap<TSharedPtr<FMultiComponentDataItem>, TSharedPtr<TMap<FString, FParameterData>>> DirtyMap;
			SplitOverrideParamsPoolBySubtree(InNode, ComponentOverrideParams, DirtyMap);
		}

		return;
	}
	CollectRelatedLazyLoadResource(InNode);

	for (const TSharedPtr<FMultiComponentDataItem>& Child : InNode->ChildComponent)
	{
		ProcessSeparateChildren(InRoot, InNode, Child);
	}
}

void UDSCupboardModel::CreateSeparateChild(const TSharedPtr<FMultiComponentDataItem>& InRoot, const TSharedPtr<FMultiComponentDataItem>& InParent,
                                           const TSharedPtr<FMultiComponentDataItem>& InChild)
{
	if (!UDSMVCSubsystem::IsInitialized())
	{
		return;
	}

	if (InChild->UUID.IsEmpty())
	{
		InChild->GenerateUUID();
	}

	FDSComponentInfo ChildComponentInfo;

	ChildComponentInfo.ComponentUUID = InChild->UUID;
	ChildComponentInfo.ComponentOperator = EDSComponentOperator::DSO_Separate;

	ComputeTreePath(InRoot, InChild, ChildComponentInfo.ComponentTreePath);

	FTransform ComponentRelativeTransform;
	TArray<TSharedPtr<FMultiComponentDataItem>> ComponentPath;
	CollectComponentPath(ModelInfo.ComponentTreeData, InChild, ComponentPath);

	if (ComponentPath.IsValidIndex(0))
	{
		//ComponentRelativeTransform.SetLocation(ComponentPath[0]->ComponentLocation.GetLocation());
		//ComponentRelativeTransform.SetRotation(ComponentPath[0]->ComponentRotation.GetRotation().Quaternion());
		//ComponentRelativeTransform.SetScale3D(ComponentPath[0]->ComponentScale.GetScale());

		for (int32 Index = 1; Index < ComponentPath.Num(); ++Index)
		{
			FTransform CurrentTransform;
			CurrentTransform.SetLocation(ComponentPath[Index]->ComponentLocation.GetLocation());
			CurrentTransform.SetRotation(ComponentPath[Index]->ComponentRotation.GetRotation().Quaternion());
			CurrentTransform.SetScale3D(ComponentPath[Index]->ComponentScale.GetScale());

			ComponentRelativeTransform = CurrentTransform * ComponentRelativeTransform;
		}
	}

	UDSCupboardModel* SeparateModel = NewObject<UDSCupboardModel>(this);
	ChildComponentInfo.ComponentModel = SeparateModel;
	if (SeparateModel != nullptr)
	{
		SeparateModel->SetOwnerModel(this);
		SeparateModel->SetNoNewGenerate();
		UDSMVCSubsystem::GetInstance()->SpawnViewUnion(SeparateModel, FDSBroadcastMarkData::SpawnBroadcastMark, false);
		SeparateModel->InitConstruct_Separate(InChild, *ComponentOverrideParams[InParent], ComponentOverrideParams, ComponentRelativeTransform);
	}

	ModelInfo.ComponentInfoArr.Add(ChildComponentInfo);
}

void UDSCupboardModel::UpdateParameterOfComponent_Inner(const TSharedPtr<FMultiComponentDataItem>& InComponent,
                                                        const FParameterData& InNewParameter)
{
	FParameterData* TargetParameter = InComponent->ComponentParameters.FindByPredicate(
		[&](const FParameterData& InParam)
		{
			return InParam.Data.name.Equals(InNewParameter.Data.name);
		});

	if (TargetParameter == nullptr)
	{
		return;
	}

	if (InNewParameter.Data.name.Equals("LDH"))
	{
		double NewDistanceToFloor = FCString::Atoi(*InNewParameter.Data.value) * 0.1f;

		const FVector& CurentLocation = Property->GetTransformProperty().GetLocation();
		if (!FMath::IsNearlyEqual(NewDistanceToFloor, CurentLocation.Z - UDSConfigSubsystem::GetInstance()->GetValue_FlootHeight(), 0.1f))
		{
			Property->GetTransformPropertyRef().Location.Z = NewDistanceToFloor;
			OnExecuteAction(FDSModelExecuteType::ExecuteTransform);
		}
		TargetParameter->bEnableAdaptation = InNewParameter.bEnableAdaptation;
	}
	else
	{
		InterruptParse();
		*TargetParameter = InNewParameter;
		OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf);
	}
	//临时代码需要等加载非异步是才正确
	if (UDSToolLibrary::IsCustomCabinetType(ModelType))
	{
		if (AdaptationOperator.IsValid())
		{
			AdaptationOperator->ExecuteStepAdaptation();
		}
		HandleChildrenFunctionalModelAdaptation();
	}
	else if (IsFunctionalCupboardModel())
	{
		if (AdaptationOperator.IsValid())
		{
			AdaptationOperator->ExecuteStepAdaptation();
		}
	}
	
}

void UDSCupboardModel::RefreshSizePropertyAfterUpdate()
{
	TArray<FParameterData> Params;
	ComponentOverrideParams[ModelInfo.ComponentTreeData]->GenerateValueArray(Params);
	FParameterData* Param = Params.FindByPredicate([&](const FParameterData& InParam) 
		{ return GetModelType() ==EDSModelType::E_Custom_LayoutDoor ? InParam.Data.name.Equals(TEXT("MDW")) : InParam.Data.name.Equals(TEXT("W")); });
	if (Param != nullptr)
	{
		Property->SizeProperty.Width = FCString::Atod(*Param->Data.value);
	}

	Param = Params.FindByPredicate([&](const FParameterData& InParam) 
		{ return GetModelType() == EDSModelType::E_Custom_LayoutDoor ?  InParam.Data.name.Equals(TEXT("MDD")) : InParam.Data.name.Equals(TEXT("D")); });
	if (Param != nullptr)
	{
		Property->SizeProperty.Depth = FCString::Atod(*Param->Data.value);
	}

	Param = Params.FindByPredicate([&](const FParameterData& InParam) 
		{ return  GetModelType() == EDSModelType::E_Custom_LayoutDoor ? InParam.Data.name.Equals(TEXT("MDH")) : InParam.Data.name.Equals(TEXT("H")); });
	if (Param != nullptr)
	{
		Property->SizeProperty.Height = FCString::Atod(*Param->Data.value);
	}

	UpdatePivotOffset();
}

bool UDSCupboardModel::ComputeTreePath(const TSharedPtr<FMultiComponentDataItem>& StartNode, const TSharedPtr<FMultiComponentDataItem>& InTarget, FString& OutPath)
{
	if (StartNode == InTarget)
	{
		return true;
	}

	for (int32 Index = 0; Index < StartNode->ChildComponent.Num(); ++Index)
	{
		if (ComputeTreePath(StartNode->ChildComponent[Index], InTarget, OutPath))
		{
			OutPath = FString::Printf(TEXT("%02d%s"), Index, *OutPath);
			return true;
		}
	}

	return false;
}

bool UDSCupboardModel::CollectComponentPath(const TSharedPtr<FMultiComponentDataItem>& StartNode, const TSharedPtr<FMultiComponentDataItem>& InTarget,
                                            TArray<TSharedPtr<FMultiComponentDataItem>>& OutPath)
{
	OutPath.Push(StartNode);
	if (StartNode == InTarget)
	{
		return true;
	}

	for (const TSharedPtr<FMultiComponentDataItem>& ChildNode : StartNode->ChildComponent)
	{
		if (CollectComponentPath(ChildNode, InTarget, OutPath))
		{
			return true;
		}
	}

	OutPath.Pop();
	return false;
}

bool UDSCupboardModel::IsParameterEquals(const FString& ParamName, const TSharedPtr<FMultiComponentDataItem>& A,
                                         const TSharedPtr<FMultiComponentDataItem>& B)
{
	auto CompareFunc = [&](const FParameterData& InParam) { return InParam.Data.name.Equals(ParamName); };
	int32 IndexA = A->ComponentParameters.IndexOfByPredicate(CompareFunc);
	int32 IndexB = B->ComponentParameters.IndexOfByPredicate(CompareFunc);

	if (IndexA == INDEX_NONE && IndexB == INDEX_NONE)
	{
		return true;
	}
	if (IndexA == INDEX_NONE || IndexB == INDEX_NONE)
	{
		return false;
	}
	return A->ComponentParameters[IndexA].Data.GetFormattedValue() == B->ComponentParameters[IndexB].Data.GetFormattedValue();
}

void UDSCupboardModel::FindAllGeneratedDoors(const TSharedPtr<FMultiComponentDataItem>& StartNode,
                                             TArray<TSharedPtr<FMultiComponentDataItem>>& OutDoors)
{
	if (!StartNode.IsValid())
	{
		return;
	}

	if (UDSModelDependencySubsystem::GetInstance()->IsGeneratedDoor(StartNode->UUID))
	{
		OutDoors.AddUnique(StartNode);
	}

	for (const TSharedPtr<FMultiComponentDataItem>& Child : StartNode->ChildComponent)
	{
		FindAllGeneratedDoors(Child, OutDoors);
	}
}

void UDSCupboardModel::QueryRelatedResourceList(const TMap<FString, EDSResourceType>& FolderIdsForDownload)
{
	if (RequestHandle.IsValid() && RequestHandle->GetStatus() == EHttpRequestStatus::Processing)
	{
		RequestHandle->OnProcessRequestComplete().Unbind();
		RequestHandle->CancelRequest();
		RequestHandle.Reset();
	}

	TMap<EDSResourceType, TArray<TSharedPtr<FJsonValue>>> IdsMap;

	for (const TPair<FString, EDSResourceType>& Pair : FolderIdsForDownload)
	{
		if (!IdsMap.Contains(Pair.Value))
		{
			IdsMap.Add(Pair.Value, {});
		}

		IdsMap[Pair.Value].Add(MakeShared<FJsonValueString>(Pair.Key));
	}

	TMap<EDSResourceType, TSharedPtr<FJsonValue>> ParamObjMap;
	for (const TPair<EDSResourceType, TArray<TSharedPtr<FJsonValue>>>& Pair : IdsMap)
	{
		if (!ParamObjMap.Contains(Pair.Key))
		{
			ParamObjMap.Add(Pair.Key, MakeShared<FJsonValueObject>(MakeShared<FJsonObject>()));
		}

		const TSharedPtr<FJsonObject>& CurrentObj = ParamObjMap[Pair.Key]->AsObject();

		CurrentObj->SetNumberField(TEXT("type"), static_cast<int32>(Pair.Key));
		CurrentObj->SetArrayField(TEXT("folderIds"), Pair.Value);
	}

	TArray<TSharedPtr<FJsonValue>> ParamObjArray;
	ParamObjMap.GenerateValueArray(ParamObjArray);

	FString QueryParamStr;
	TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&QueryParamStr);
	if (!FJsonSerializer::Serialize(ParamObjArray, Writer))
	{
		UE_LOG(LogDSCupboardModel, Error, TEXT("%s - Convert params to json failure."), __FUNCTIONW__);
		return;
	}

	RequestHandle = FHttpModule::Get().CreateRequest();

	RequestHandle->SetVerb(TEXT("POST"));
	RequestHandle->SetURL(FString::Printf(TEXT("%s/bk-design/api/store/getListByFolderId"), *UDSNetworkSubsystem::GetInstance()->GetRequestDomain()));
	RequestHandle->SetHeader(TEXT("Authorization"), UDSNetworkSubsystem::GetInstance()->GetToken());
	RequestHandle->SetHeader(TEXT("Content-Type"), TEXT("application/json"));
	RequestHandle->SetContentAsString(QueryParamStr);

	RequestHandle->OnProcessRequestComplete().BindUObject(this, &ThisClass::QueryRelatedResourceList_Callback);
	RequestHandle->ProcessRequest();
}

void UDSCupboardModel::QueryRelatedResourceList_Callback(FHttpRequestPtr Request, FHttpResponsePtr Response,
                                                         bool bConnectedSuccessfully)
{
	if (!bConnectedSuccessfully || !EHttpResponseCodes::IsOk(Response->GetResponseCode()))
	{
		UE_LOG(LogDSCupboardModel, Error, TEXT("Query folder resource informations failure."));
		return;
	}

	TSharedPtr<FJsonObject> ResponseObj;
	TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(Response->GetContentAsString());

	if (!FJsonSerializer::Deserialize(Reader, ResponseObj))
	{
		UE_LOG(LogDSCupboardModel, Error, TEXT("Parse response json failure."));
		return;
	}

	if (!ResponseObj->GetBoolField(TEXT("success")))
	{
		UE_LOG(LogDSCupboardModel, Error, TEXT("%s : The success field is false, msg is '%s'."), __FUNCTIONW__, *ResponseObj->GetStringField(TEXT("msg")));
		return;
	}

	if (!ResponseObj->HasTypedField<EJson::Array>(TEXT("resp")))
	{
		UE_LOG(LogDSCupboardModel, Error, TEXT("%s : Not found 'resp' field."), __FUNCTIONW__);
		return;
	}

	TArray<FDSResourceInfo> ItemList;
	UDesignStationFunctionLibrary::ParseJsonToResourceList(ResponseObj->GetArrayField(TEXT("resp")), ItemList);

	UWorld* World = GetWorld();
	if (World == nullptr)
	{
		return;
	}

	UEasyNetworkFileSubsystem* EasyNetworkFileSubsystem = World->GetSubsystem<UEasyNetworkFileSubsystem>();
	if (EasyNetworkFileSubsystem == nullptr)
	{
		return;
	}

	NeedsDownloadResourceCount = ItemList.Num();
	DownloadedResourceCount = 0;

	for (const FDSResourceInfo& ItemInfo : ItemList)
	{
		UDSResourceSubsystem::GetInstance()->AddResourceCache(ItemInfo);

		FDSResourceFile FileInfo = ItemInfo.GetResourceFile(EDSResourceQuality::Low);
		if (FileInfo.FilePath.IsEmpty())
		{
			continue;
		}

		UBaseNetworkFileTask* NewTask = EasyNetworkFileSubsystem->CreateTask(ENetworkFileOperation::ENFO_Download);
		NewTask->SetUrl( FileInfo.FilePath);
		NewTask->SetFilePath(FPaths::Combine(FPaths::ProjectSavedDir(), TEXT("Resources"), FileInfo.ComputeLocalFileName()));
		NewTask->SetChecksum(FileInfo.MD5);

		UCustomRelativeResourcePayload* Payload = NewObject<UCustomRelativeResourcePayload>(NewTask);
		Payload->ResourceInfo = ItemInfo;
		NewTask->SetPayload(Payload);

		NewTask->OnTaskCompleteEvent().AddDynamic(this, &ThisClass::OnDownloadResourceTaskComplete);
		NewTask->StartProcess();

		ProcessingTaskIds.Add(NewTask->GetTaskId());
	}
}

void UDSCupboardModel::OnDownloadResourceTaskComplete(const FString& TaskId, bool bSucceed)
{
	ProcessingTaskIds.Remove(TaskId);

	UWorld* World = GetWorld();
	if (World == nullptr)
	{
		return;
	}

	UEasyNetworkFileSubsystem* EasyNetworkFileSubsystem = World->GetSubsystem<UEasyNetworkFileSubsystem>();
	if (!UDSResourceSubsystem::IsInitialized() || EasyNetworkFileSubsystem == nullptr)
	{
		return;
	}

	UBaseNetworkFileTask* Task = EasyNetworkFileSubsystem->FindTask(TaskId);
	UCustomRelativeResourcePayload* Payload = Cast<UCustomRelativeResourcePayload>(Task->GetPayload());
	if (Payload == nullptr)
	{
		return;
	}

	if (!bSucceed)
	{
		UE_LOG(LogDSCupboardModel, Warning, TEXT("Download relative resource '%s' failure."), *Payload->ResourceInfo.Id);
		return;
	}

	++DownloadedResourceCount;

	switch (Payload->ResourceInfo.Type)
	{
	case EDSResourceType::Material:
		{
			UDSResourceSubsystem::GetInstance()->RegisterMaterialResource(Task->GetFilePath());
		}
		break;
	case EDSResourceType::Model:
		{
			UDSResourceSubsystem::GetInstance()->MountPakFile(Task->GetFilePath(), false);
		}
		break;
	default:
		break;
	}
}

void UDSCupboardModel::ApplyResourceForImportModelNode(const TSharedPtr<FDSResourceInfo>& InInfo,
                                                       const TSharedPtr<FMultiComponentDataItem>& InNode)
{
	if (!InInfo || !InNode)
	{
		return;
	}

	if (InNode->ComponentType == ECompType::ImportModel && InNode->SingleComponentData.ComponentItems.IsValidIndex(0))
	{
		for (FSingleComponentItem& ComponentItem : InNode->SingleComponentData.ComponentItems)
		{
			if (ComponentItem.PakRelativeFilePath != InInfo->FolderId)
			{
				continue;
			}

			FString ResourceFilePath = FPaths::Combine(FPaths::ProjectSavedDir(), TEXT("Resources"), InInfo->GetResourceFile(EDSResourceQuality::Low).ComputeLocalFileName());
			ComponentItem.PakRefPath = UDSResourceSubsystem::GetInstance()->ComputeModelSoftPathByPakPath(ResourceFilePath, UDSResourceSubsystem::GetDefaultModelAssetFileName());
			ComponentItem.PakRelativeFilePath = ResourceFilePath;
			FPaths::MakePathRelativeTo(ComponentItem.PakRelativeFilePath, *FPaths::ProjectSavedDir());
		}
	}

	for (const TSharedPtr<FMultiComponentDataItem>& ChildNode : InNode->ChildComponent)
	{
		ApplyResourceForImportModelNode(InInfo, ChildNode);
	}
}


void UDSCupboardModel::CollectAllDoors(const TSharedPtr<FMultiComponentDataItem>& InNode, TMap<FString, TSharedPtr<FMultiComponentDataItem>>& OutDoorNodes)
{
	if (!InNode)
	{
		return;
	}

	if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), InNode->ModelType))
	{
		OutDoorNodes.Add(InNode->UUID, InNode);
	}

	for (const TSharedPtr<FMultiComponentDataItem>& Child : InNode->ChildComponent)
	{
		CollectAllDoors(Child, OutDoorNodes);
	}
}

void UDSCupboardModel::InterruptQueryMaterialByStyle()
{
	if (StyleReplacementHandle)
	{
		StyleReplacementHandle->OnProcessRequestComplete().Unbind();
		StyleReplacementHandle->CancelRequest();
		StyleReplacementHandle.Reset();
	}
}

void UDSCupboardModel::QueryStyleReplacements(const TArray<FDSCustomStyleReplacementNode>& InStylizedNodes, const FApplyStyleData& InStyle, EDSCustomAssociationType QueryType)
{
	TArray<FDSStyleDefaultOption> StyleDefaultOptions = UDSToolLibrary::CollectAppliedStyleDefaultOptions(InStyle);

	FString RequestContent;
	TSharedRef<TJsonWriter<>> JsonWriter = TJsonWriterFactory<>::Create(&RequestContent);
	JsonWriter->WriteArrayStart();
	
	TArray<TSharedPtr<FJsonValue>> ComponentValues;
	for (const FDSCustomStyleReplacementNode& Node : InStylizedNodes)
	{
		// 只查询指定风格应用类型
		if (Node.AssociationType != QueryType)
		{
			continue;
		}
		
		// 查找类型对应的风格目录及默认项
		FDSStyleDefaultOption* FoundOption = StyleDefaultOptions.FindByPredicate([&](const FDSStyleDefaultOption& InOption)
		{
			return InOption.AssociationType == Node.AssociationType && InOption.CustomTypeCode == Node.CustomTypeCode;
		});
		
		if (FoundOption == nullptr)
		{
			continue;
		}

		// 查看当前节点类型是否存在需要查询的关联，不存在为无效数据，需要人为干预
		FString NodeAssociation = UDSNetworkSubsystem::GetInstance()->FindCustomAssociation(Node.CustomType, Node.AssociationType);
		if (NodeAssociation.IsEmpty())
		{
			UE_LOG(LogDSCupboardModel, Error, TEXT("Can not find custom association for %d - %d"), Node.CustomType, Node.AssociationType);
			continue;
		}

		JsonWriter->WriteObjectStart();

		// 样式需要的FolderId已经在收集时确定，无须特别处理
		JsonWriter->WriteValue(TEXT("folderId"), Node.FolderId);
		JsonWriter->WriteValue(TEXT("association"), NodeAssociation);
		JsonWriter->WriteValue(TEXT("customType"), Node.CustomType);
		JsonWriter->WriteValue(TEXT("isMate"), static_cast<int32>(Node.AssociationType));
		JsonWriter->WriteValue(TEXT("nodeId"), Node.NodeId);
		JsonWriter->WriteValue(TEXT("styleDefault"), FoundOption->Parameter.Data.GetFormattedValue());

		JsonWriter->WriteObjectEnd();
	}

	JsonWriter->WriteArrayEnd();
	JsonWriter->Close();
	
	StyleReplacementHandle = UDSNetworkSubsystem::GetInstance()->CreatePostRequest(TEXT("bk-design/api/styleReplace/styleReplace"), RequestContent);
	StyleReplacementHandle->SetHeader(TEXT("DS-QueryType"), FString::FromInt(static_cast<int32>(QueryType)));
	StyleReplacementHandle->OnProcessRequestComplete().BindUObject(this, &ThisClass::OnQueryStyleReplacementsComplete);
	StyleReplacementHandle->ProcessRequest();
}

void UDSCupboardModel::OnQueryStyleReplacementsComplete(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bConnectedSuccessfully)
{
	struct FScopedApplyCompleteCallback
	{
		UDSCupboardModel* Employer;
		FOnCupboardModelApplyStyleComplete& Callback;

		FScopedApplyCompleteCallback(UDSCupboardModel* InEmployer, FOnCupboardModelApplyStyleComplete& InCallback)
			: Employer(InEmployer)
			, Callback(InCallback)
		{

		}

		~FScopedApplyCompleteCallback()
		{
			if (Callback.IsBound())
			{
				Callback.Broadcast(Employer);
				Callback.Clear();
			}
		}
	};

	EDSCustomAssociationType QueryType = static_cast<EDSCustomAssociationType>(FCString::Atoi(*Request->GetHeader(TEXT("DS-QueryType"))));
	
	TUniquePtr<FScopedApplyCompleteCallback> ScopedApplyCompleteCallback;
	if (QueryType == EDSCustomAssociationType::Material)
	{
		ScopedApplyCompleteCallback = MakeUnique<FScopedApplyCompleteCallback>(this, OnCupboardModelApplyStyleComplete);
	}

	if (!bConnectedSuccessfully || !EHttpResponseCodes::IsOk(Response->GetResponseCode()))
	{
		UE_LOG(LogDSCupboardModel, Warning, TEXT("QueryStyleReplacements failed, connection: %s, response code: %d"), bConnectedSuccessfully ? TEXT("true") : TEXT("false"), Response->GetResponseCode());
		return;
	}

	TSharedPtr<FJsonObject> ResponseObj;
	TSharedRef<TJsonReader<>> JsonReader = TJsonReaderFactory<>::Create(Response->GetContentAsString());
	if (!FJsonSerializer::Deserialize(JsonReader, ResponseObj))
	{
		UE_LOG(LogDSCupboardModel, Warning, TEXT("OnQueryStyleReplacementsComplete can not parse response content as json."));
		return;
	}

	if (!ResponseObj->GetBoolField(TEXT("success")))
	{
		UE_LOG(LogDSCupboardModel, Warning, TEXT("OnQueryStyleReplacementsComplete some errors occurred in server: %s"), *ResponseObj->GetStringField(TEXT("msg")));
		return;
	}

	TMap<FString, FDSCustomReplaceStyleResult> ReplaceMaterialResults;
	TArray<FDSCustomReplaceStyleResult> ReplacePatternResults;
	
	const TArray<TSharedPtr<FJsonValue>>& ReplaceValues = ResponseObj->GetArrayField(TEXT("resp"));
	for (const TSharedPtr<FJsonValue>& Value : ReplaceValues)
	{
		const TSharedPtr<FJsonObject>& ValueObj = Value->AsObject();

		FDSCustomReplaceStyleResult Result;
		Result.ResultType = static_cast<EDSCustomReplaceStyleResultType>(ValueObj->GetIntegerField(TEXT("type")));
		Result.AssociationType = static_cast<EDSCustomAssociationType>(ValueObj->GetIntegerField(TEXT("isMate")));
		Result.CustomType = ValueObj->GetIntegerField(TEXT("customType"));
		Result.NodeId = ValueObj->GetStringField(TEXT("nodeId"));
		Result.FolderId = ValueObj->GetStringField(TEXT("folderId"));
		Result.Association = ValueObj->GetStringField(TEXT("association"));
		Result.StyleDefault = ValueObj->GetStringField(TEXT("styleDefault"));

		if (Result.AssociationType == EDSCustomAssociationType::Material)
		{
			ReplaceMaterialResults.Add(Result.NodeId, Result);
		}
		else if (Result.AssociationType == EDSCustomAssociationType::Pattern)
		{
			ReplacePatternResults.Add(Result);
		}
	}

	if (!ReplacePatternResults.IsEmpty())
	{
		ReplaceNormalNode(ReplacePatternResults);
		ReplaceDoorAndHandleNode(ReplacePatternResults);
	}

	if (!ReplaceMaterialResults.IsEmpty())
	{
		RecursiveReplaceNodeMaterial(ModelInfo.ComponentTreeData, ReplaceMaterialResults);
	}

	StyleReplacementHandle.Reset();

	if (!ReplacePatternResults.IsEmpty() || !ReplaceMaterialResults.IsEmpty())
	{
		if (UDSMVCSubsystem::GetInstance()->GetCurrentModel() == this)
		{
			OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf, FDSBroadcastMarkData::SelectBroadcastMark);
			UDSUISubsystem::GetInstance()->ProcessStateEvent(this, EUIOperationType::Selected, UDSMVCSubsystem::GetInstance()->GetStateType());
		}
		else
		{
			OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf, FDSBroadcastMarkData::OnlyOutlineBroadcastMark);
		}	
	}

	// 应用风格后替换完样式后替换材质，如果UpdateSelf解析新树发现有新的要替换样式的节点，则略过
	if (QueryType == EDSCustomAssociationType::Pattern && !StyleReplacementHandle.IsValid())
	{
		// 根据风格刷新定制树样式
		TArray<FDSCustomStyleReplacementNode> StylizedNodes;
		TArray<TPair<FString, TWeakPtr<FMultiComponentDataItem>>> NodePath;
		UDSCupboardLibrary::CollectAllStylizedNodeInfos(ModelInfo.ComponentTreeData, StylizedNodes, NodePath);
		QueryStyleReplacements(StylizedNodes, GetRootCupboardModel()->GetModelInfoRef().ApplyStyle_Whole, EDSCustomAssociationType::Material);
	}
}

void UDSCupboardModel::RecursiveReplaceNodeMaterial(const TSharedPtr<FMultiComponentDataItem>& InNode, const TMap<FString, FDSCustomReplaceStyleResult>& InResult)
{
	if (!InNode)
	{
		return;
	}

	if (InResult.Contains(InNode->UUID))
	{
		const FDSCustomReplaceStyleResult& Result = InResult[InNode->UUID];
		if (FParameterData* FoundParam = InNode->ComponentParameters.FindByPredicate([](const FParameterData& InParam){ return InParam.Data.name == TEXT("DZCZ"); }))
		{
			switch (Result.ResultType)
			{
			case EDSCustomReplaceStyleResultType::NonAssociation:
			case EDSCustomReplaceStyleResultType::Matched:
				{
					FoundParam->Data.expression = Result.StyleDefault;
					FoundParam->Data.value = Result.StyleDefault;
				}
				break;
			case EDSCustomReplaceStyleResultType::NotMatched:
				{
					// TODO: Set parameter expression to default expression.
					FoundParam->Data.expression = FoundParam->Data.DefaultExpress;
				}
				break;
			}
		}
	}

	for (const TSharedPtr<FMultiComponentDataItem>& Child : InNode->ChildComponent)
	{
		RecursiveReplaceNodeMaterial(Child, InResult);
	}
}

void UDSCupboardModel::ReplaceDoorAndHandleNode(const TArray<FDSCustomReplaceStyleResult>& InResults)
{
	TMap<FString, TSharedPtr<FMultiComponentDataItem>> DoorNodes;
	CollectAllDoors(ModelInfo.ComponentTreeData, DoorNodes);

	TMap<TSharedPtr<FMultiComponentDataItem>, UDSCupboardModel*> NodeBelongsModel;
	TMap<TSharedPtr<FMultiComponentDataItem>, TSharedPtr<FMultiComponentDataItem>> NodeBelongsNode;
	UDSCupboardLibrary::CollectTreeRelationship(this, ModelInfo.ComponentTreeData, nullptr, NodeBelongsModel, NodeBelongsNode);

	for (const FDSCustomReplaceStyleResult& Result : InResults)
	{
		if (Result.ResultType == EDSCustomReplaceStyleResultType::NotMatched || !UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), Result.CustomType))
		{
			continue;
		}

		if (!DoorNodes.Contains(Result.NodeId))
		{
#if WITH_EDITOR
			UE_LOG(LogDSCupboardModel, Error, TEXT("Can not find the door node [%s - %d], but it exists when collect replacement nodes, check it."), *Result.NodeId, Result.CustomType);
#endif
			continue;
		}

		const TSharedPtr<FMultiComponentDataItem>& DoorNode = DoorNodes[Result.NodeId];

		// 当前门与风格默认门型相同，则不需要替换
		if (DoorNode->ComponentID.GetFormattedValue() == Result.StyleDefault)
		{
			continue;
		}

		// 缓存旧门上的拉手，默认每个门上只会有一个拉手
		TSharedPtr<FMultiComponentDataItem> OldDoorHandleNode;
		for (const TSharedPtr<FMultiComponentDataItem>& DoorChild : DoorNode->ChildComponent)
		{
			if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXLS"), DoorChild->ModelType))
			{
				OldDoorHandleNode = DoorChild;
				break;
			}
		}

		if (!NodeBelongsNode.Contains(DoorNode))
		{
			UE_LOG(LogDSCupboardModel, Error, TEXT("Door node relationship not found for node [%s]."), *DoorNode->UUID);
			continue;
		}

		const TSharedPtr<FMultiComponentDataItem>& ParentNode = NodeBelongsNode[DoorNode];
		if (!NodeBelongsModel.Contains(DoorNode))
		{
			UE_LOG(LogDSCupboardModel, Error, TEXT("Door node relationship not found for node [%s]."), *DoorNode->UUID);
			continue;
		}

		const TMap<TSharedPtr<FMultiComponentDataItem>, TSharedPtr<TMap<FString, FParameterData>>>& ComponentParamsRef = NodeBelongsModel[ParentNode]->GetComponentOverrideParamsRef();
		if (!ComponentParamsRef.Contains(ParentNode) || !ComponentParamsRef[ParentNode])
		{
            UE_LOG(LogDSCupboardModel, Error, TEXT("ComponentParamsRef does not contain ParentNode for door node [%s]."), *DoorNode->UUID);
			continue;
		}

        if (!UDSCupboardLibrary::CheckSizeForNewNodeIsValid(DoorNode, Result.StyleDefault, *ComponentParamsRef[ParentNode]))
        {
			UE_LOG(LogDSCupboardModel, Error, TEXT("CheckSizeForNewNodeIsValid failed for door node [%s] with new style [%s]."), *DoorNode->UUID, *Result.StyleDefault);
			continue; // 如果新风格的门型尺寸不合法，则跳过替换
        }

		// 将门替换为新风格中的门型
		if (!UDSCupboardLibrary::ReplaceCustomNodeWithFolderId(DoorNode, Result.StyleDefault))
		{
			UE_LOG(LogDSCupboardModel, Error, TEXT("Replace door node [%s] from [%s] to [%s] failed."), *DoorNode->ComponentName, *DoorNode->ComponentID.Value, *Result.StyleDefault);
			continue;
		}

		// 如果替换的是当前Model根节点，要修改ModelType
		if (DoorNode == ModelInfo.ComponentTreeData)
		{
			SetModelType(UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(ModelInfo.ComponentTreeData->ModelType));
		}

		// 检测新风格门型是否允许安装拉手
		FParameterData* HandParam = DoorNode->ComponentParameters.FindByPredicate([](const FParameterData& InParam)
		{
			return InParam.Data.name == TEXT("ZLLSAZ");
		});

		if (HandParam == nullptr || FCString::Atoi(*HandParam->Data.value) == 0)
		{
			// 新风格中的门型允许安装拉手，将拉手添加到门下
			if (OldDoorHandleNode)
			{
				DoorNode->ChildComponent.Add(OldDoorHandleNode);
			}
		}
	}
}

void UDSCupboardModel::ReplaceNormalNode(const TArray<FDSCustomReplaceStyleResult>& InResults)
{
	TMap<FString, FDSCustomReplaceStyleResult> ReplaceMap;
	for (const FDSCustomReplaceStyleResult& Result : InResults)
	{
		// 门型需要额外处理
		if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), Result.CustomType) || Result.ResultType == EDSCustomReplaceStyleResultType::NotMatched)
		{
			continue;
		}

		ReplaceMap.Add(Result.NodeId, Result);
	}

	for (const TSharedPtr<FMultiComponentDataItem>& Child : ModelInfo.ComponentTreeData->ChildComponent)
	{
		RecursiveReplaceNormalNodePattern(Child, ReplaceMap);	
	}
}

void UDSCupboardModel::RecursiveReplaceNormalNodePattern(const TSharedPtr<FMultiComponentDataItem>& InNode, const TMap<FString, FDSCustomReplaceStyleResult>& InReplaceMap)
{
	if (!InNode)
	{
		return;
	}

	if (InReplaceMap.Contains(InNode->UUID))
	{
		const FDSCustomReplaceStyleResult& ReplaceResult = InReplaceMap[InNode->UUID];
		switch (ReplaceResult.ResultType)
		{
		case EDSCustomReplaceStyleResultType::NonAssociation:
		case EDSCustomReplaceStyleResultType::Matched:
			{
				// 当前节点ID已经是风格默认ID，则不需要替换
				if (InNode->ComponentID.GetFormattedValue() == ReplaceResult.StyleDefault)
				{
					break;
				}

				if (!UDSCupboardLibrary::ReplaceCustomNodeWithFolderId(InNode, InReplaceMap[InNode->UUID].StyleDefault))
				{
					UE_LOG(LogDSCupboardModel, Error, TEXT("Replace normal node [%s - %d] from [%s] to [%s] failed."), *InNode->ComponentName, InNode->ModelType, *InNode->ComponentID.Value, *InReplaceMap[InNode->UUID].StyleDefault);
				}
				else if (InNode == ModelInfo.ComponentTreeData)
				{
					// 如果替换的是当前Model根节点，要修改ModelType
					SetModelType(UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(ModelInfo.ComponentTreeData->ModelType));
				}
			}
			break;
		case EDSCustomReplaceStyleResultType::NotMatched: // 保持当前节点不变
			break;
		}
	}
	else
	{
		for (const TSharedPtr<FMultiComponentDataItem>& Child : InNode->ChildComponent)
		{
			RecursiveReplaceNormalNodePattern(Child, InReplaceMap);
		}	
	}
}

void UDSCupboardModel::RefreshChildModelActualTransform()
{
	for (const auto& ChildComponentModel:ModelInfo.ComponentInfoArr)
	{
		if (ChildComponentModel.ComponentModel&& ChildComponentModel.ComponentModel->IsA<UDSCupboardModel>())
		{
			ADSBaseView* OwnerView = ChildComponentModel.ComponentModel->GetOwnedView();
			if (!OwnerView) continue;
			const FTransform& ViewTrans = OwnerView->GetActorTransform();
			ChildComponentModel.ComponentModel->GetProperty()->GetTransformPropertyRef().SetTransform(ViewTrans);
			Cast<UDSCupboardModel>(ChildComponentModel.ComponentModel)->RefreshChildModelActualTransform();
		}
	}
}

bool UDSCupboardModel::CollectComponentPath_Public(const TSharedPtr<FMultiComponentDataItem>& StartNode, const TSharedPtr<FMultiComponentDataItem>& InTarget,
                                                   TArray<TSharedPtr<FMultiComponentDataItem>>& OutPath)
{
	OutPath.Push(StartNode);
	if (StartNode == InTarget)
	{
		return true;
	}

	for (const TSharedPtr<FMultiComponentDataItem>& ChildNode : StartNode->ChildComponent)
	{
		if (CollectComponentPath(ChildNode, InTarget, OutPath))
		{
			return true;
		}
	}

	OutPath.Pop();
	return false;
}

FString UDSCupboardModel::GetComponentTreePath(const TSharedPtr<FMultiComponentDataItem>& InTarget)
{
	auto RootNode = ModelInfo.ComponentTreeData;
	if (!RootNode.IsValid())
	{
		return TEXT("");
	}
	FString OutRes = TEXT("");
	for (int32 i = 0; i < RootNode->ChildComponent.Num(); ++i)
	{
		if (RootNode->ChildComponent[i].IsValid())
		{
			FString ThisNodePath = FString::Printf(TEXT("%02d"), i);
			OutRes.Append(ThisNodePath);
			bool Res = GetComponentTreePath_Recurse(RootNode->ChildComponent[i], InTarget, OutRes);
			if (Res)
			{
				break;
			}
			OutRes.RemoveFromEnd(ThisNodePath);
		}
	}
	return OutRes;
}

bool UDSCupboardModel::GetComponentTreePath_Recurse(const TSharedPtr<FMultiComponentDataItem>& TreeNode, const TSharedPtr<FMultiComponentDataItem>& InTarget, FString& OutPath)
{
	if (TreeNode == InTarget)
	{
		return true;
	}

	bool Ret = false;
	for (int32 i = 0; i < TreeNode->ChildComponent.Num(); ++i)
	{
		if (TreeNode->ChildComponent[i].IsValid())
		{
			FString ThisNodePath = FString::Printf(TEXT("%02d"), i);
			OutPath.Append(ThisNodePath);
			bool Res_Temp = GetComponentTreePath_Recurse(TreeNode->ChildComponent[i], InTarget, OutPath);
			if (Res_Temp)
			{
				Ret = true;
				break;
			}
			OutPath.RemoveFromEnd(ThisNodePath);
		}
	}
	return Ret;
}

void UDSCupboardModel::ParseDataByNodePath(TSharedPtr<FMultiComponentDataItem> InData, FString NodePath)
{
	auto RootNode = ModelInfo.ComponentTreeData;
	if (!RootNode.IsValid())
	{
		return;
	}
	if (NodePath.Len() < 2)
	{
		return;
	}

	FString SubStr = NodePath.Left(2);
	int32 Index = FCString::Atoi(*SubStr);
	if (RootNode->ChildComponent.IsValidIndex(Index))
	{
		NodePath.RemoveFromStart(SubStr);
		ParseDataByNodePath_Recurse(RootNode->ChildComponent[Index], InData, NodePath);
	}
}

void UDSCupboardModel::ParseDataByNodePath_Recurse(TSharedPtr<FMultiComponentDataItem> TreeNode, const TSharedPtr<FMultiComponentDataItem>& InTarget, FString NodePath)
{
	if (NodePath.IsEmpty())
	{
		TreeNode->DeepCopy(*InTarget);
	}
	else
	{
		FString SubStr = NodePath.Left(2);
		int32 Index = FCString::Atoi(*SubStr);
		if (TreeNode->ChildComponent.IsValidIndex(Index))
		{
			NodePath.RemoveFromStart(SubStr);
			ParseDataByNodePath_Recurse(TreeNode->ChildComponent[Index], InTarget, NodePath);
		}
	}
}

void UDSCupboardModel::GetDataByNodePathRet(TSharedPtr<FMultiComponentDataItem> InData, FString NodePath,
                                            TSharedPtr<FMultiComponentDataItem>& OutTargetNode)
{
	auto RootNode = ModelInfo.ComponentTreeData;
	if (!RootNode.IsValid())
	{
		return;
	}
	if (NodePath.Len() < 2)
	{
		return;
	}

	FString SubStr = NodePath.Left(2);
	int32 Index = FCString::Atoi(*SubStr);
	if (RootNode->ChildComponent.IsValidIndex(Index))
	{
		NodePath.RemoveFromStart(SubStr);
		GetDataByNodePath_RecurseRet(RootNode->ChildComponent[Index], NodePath, OutTargetNode);
	}
}

void UDSCupboardModel::GetDataByNodePath_RecurseRet(TSharedPtr<FMultiComponentDataItem> TreeNode, FString NodePath, TSharedPtr<FMultiComponentDataItem>& OutTargetNode)
{
	if (NodePath.IsEmpty())
	{
		OutTargetNode = TreeNode;
	}
	else
	{
		FString SubStr = NodePath.Left(2);
		int32 Index = FCString::Atoi(*SubStr);
		if (TreeNode->ChildComponent.IsValidIndex(Index))
		{
			NodePath.RemoveFromStart(SubStr);
			GetDataByNodePath_RecurseRet(TreeNode->ChildComponent[Index], NodePath, OutTargetNode);
		}
	}
}

void UDSCupboardModel::BroadcastLinkModelsTransform()
{
	if (UDSToolLibrary::IsCustomCabinetType(GetModelType()) && OnLinkModelTransformDelegate.IsBound())
	{
		OnLinkModelTransformDelegate.Broadcast(this);
	}
}

void UDSCupboardModel::BroadcastLinkModelsUpdateSelf()
{
	if (UDSToolLibrary::IsCustomCabinetType(GetModelType()) && OnLinkModelUpdatedDelegate.IsBound())
	{
		OnLinkModelUpdatedDelegate.Broadcast(this);
	}
}

void UDSCupboardModel::BroadcastLinkModelsZHeight()
{
	if (UDSToolLibrary::IsCustomCabinetType(GetModelType()) && OnLinkModelZHeightDelegate.IsBound())
	{
		OnLinkModelZHeightDelegate.Broadcast(this);
	}
}

TSet<FString> UDSCupboardModel::GetAllComponentModelsUUID(const FDSCupboardModelInfo& InInfo)
{
	if (InInfo.ComponentInfoArr.IsEmpty())
	{
		return TSet<FString>();
	}

	TSet<FString> OutSet;
	for (auto& Iter : InInfo.ComponentInfoArr)
	{
		auto CmpModel = Iter.ComponentModel;
		OutSet.Add(CmpModel->GetUUID());
		auto CupCmpModel = Cast<UDSCupboardModel>(CmpModel);
		if (CupCmpModel)
		{
			OutSet.Append(GetAllComponentModelsUUID(CupCmpModel->GetModelInfo()));
		}
	}

	return OutSet;
}

TSet<FString> UDSCupboardModel::GetAllComponentModelsUUID(bool bIncludeSelf)
{
	TSet<FString> OutSet;
	if (bIncludeSelf)
	{
		OutSet.Add(GetUUID());
	}

	OutSet.Append(GetAllComponentModelsUUID(ModelInfo));
	return OutSet;
}

TMap<FString, FString> UDSCupboardModel::RegenerateUUIDs()
{
	TMap<FString, FString> UUIDMap;
	auto TreeData = ModelInfo.ComponentTreeData;
	if (!TreeData.IsValid())
	{
		return UUIDMap;
	}
	std::function<void(TSharedPtr<FMultiComponentDataItem>&)> RegenerateUUIDs_Inner = [&](TSharedPtr<FMultiComponentDataItem>& InTree)->void
		{
			auto OldUUID = InTree->UUID;
			InTree->UUID = FGuid::NewGuid().ToString();
			UUIDMap.Add(OldUUID, InTree->UUID);
			for (TSharedPtr<FMultiComponentDataItem>& Child : InTree->ChildComponent)
			{
				RegenerateUUIDs_Inner(Child);
			}
		};

	RegenerateUUIDs_Inner(TreeData);
	if (SubFunctionalNodeDependencyMap)
	{
		for (auto& Iter : UUIDMap)
		{
			if (SubFunctionalNodeDependencyMap->GetDependencyInfo(Iter.Key))
			{
				SubFunctionalNodeDependencyMap->ReplaceDependencyInfo(Iter.Key, Iter.Value);
			}
		}
	}
	return UUIDMap;
}

void UDSCupboardModel::SetParsed(bool bInParsed)
{
	bParsed = bInParsed;
}

void UDSCupboardModel::RecoverModelFromData(const FDSCupboardModelInfo& Info, bool bInParsed)
{
	SetModelInfo(Info);

	if (bInParsed)
	{
		bParsed = true;
		bResourceAllLoaded = true;
		GetRootCupboardModel()->UpdateNodesToDisableCollision();
	}
}

void UDSCupboardModel::AddFunctionalCupboardModel(UDSBaseModel* InModel)
{
	if (!InModel)
	{
		return;
	}
	if (!InModel->IsA<UDSCupboardModel>())
	{
		InModel->OnExecuteAction(FDSModelExecuteType::ExecuteDelete);
	}

	InModel->SetOwnerModel(this);
	UDSCupboardModel* InCupboardModel = Cast<UDSCupboardModel>(InModel);
	auto& InCupboardModelInfo = InCupboardModel->GetModelInfoRef();

	bool bContains =  ModelInfo.ComponentTreeData->ChildComponent.ContainsByPredicate(
		[&](const TSharedPtr<FMultiComponentDataItem>& Child)
		{
			return Child->UUID.Equals(InCupboardModel->GetComponentTreeDataRef()->UUID);
		});
	if (bContains)
	{
		return;
	}
	ModelInfo.ComponentTreeData->ChildComponent.Add(InCupboardModel->ModelInfo.ComponentTreeData);
	
	//设置相对坐标
	ADSBaseView* InModelView = InCupboardModel->GetOwnedView();
	ADSBaseView* SelfView = GetOwnedView();
	InModelView->AttachToActor(SelfView, FAttachmentTransformRules::SnapToTargetIncludingScale);
	FTransform WorldTransfrom = InModel->GetProperty()->GetTransformProperty().ToUETransform();
	InModelView->SetActorTransform(WorldTransfrom);

	FDSComponentInfo ChildComponentInfo;
	ChildComponentInfo.ComponentUUID = InCupboardModel->GetModelInfo().ComponentTreeData->UUID;
	ChildComponentInfo.ComponentOperator = EDSComponentOperator::DSO_Attach;
	ComputeTreePath(ModelInfo.ComponentTreeData, InCupboardModelInfo.ComponentTreeData, ChildComponentInfo.ComponentTreePath);
	ChildComponentInfo.ComponentModel = InCupboardModel;


	ModelInfo.ComponentInfoArr.Add(ChildComponentInfo);

	InCupboardModel->RelativeTransform = InModelView->GetRootComponent()->GetRelativeTransform();
	// Get transform from ComponentTreeData->ComponentXXX will divide 10 to convert from mm to cm, so we multiply 10 for all values.
	InCupboardModelInfo.ComponentTreeData->ComponentLocation = InCupboardModel->RelativeTransform.GetLocation() * 10.0f;
	InCupboardModelInfo.ComponentTreeData->ComponentRotation = InCupboardModel->RelativeTransform.GetRotation().Rotator();
	InCupboardModelInfo.ComponentTreeData->ComponentScale = InCupboardModel->RelativeTransform.GetScale3D();
}

void UDSCupboardModel::RemoveFunctionalCupboardModel(UDSBaseModel* InModel)
{
	if (!InModel || !InModel->IsA<UDSCupboardModel>())
	{
		return;
	}
	UDSCupboardModel* InCupboardModel = Cast<UDSCupboardModel>(InModel);
	int32 ChildComponentIndex = ModelInfo.ComponentTreeData->ChildComponent.IndexOfByPredicate(
		[&](const TSharedPtr<FMultiComponentDataItem>& Child)
		{
			return Child->UUID.Equals(InCupboardModel->GetComponentTreeDataRef()->UUID);
		}
	);
	if (ChildComponentIndex != INDEX_NONE)
	{
		ModelInfo.ComponentTreeData->ChildComponent.RemoveAt(ChildComponentIndex);
	}


	int32 ComponentInfoArrIndex = ModelInfo.ComponentInfoArr.IndexOfByPredicate(
		[&](const FDSComponentInfo& InComponentInfo)
		{
			return InComponentInfo.ComponentUUID.Equals(InCupboardModel->GetComponentTreeDataRef()->UUID);
		}
	);
	if (ComponentInfoArrIndex != INDEX_NONE)
	{
		ModelInfo.ComponentInfoArr.RemoveAt(ComponentInfoArrIndex);
	}

	SubFunctionalNodeDependencyMap->RemoveDependencyInfo(InCupboardModel->GetComponentTreeDataRef()->UUID);
}

bool UDSCupboardModel::IsFunctionalCupboardModel() const
{
	return UDSCupboardLibrary::IsFunctionalCupboardModel(ModelInfo.ComponentTreeData->ComponentParameters);
}

bool UDSCupboardModel::IsValidFunctionCupboardModel()
{
	if (!IsFunctionalCupboardModel())
	{
		return false;
	}
	//功能件不能单独存在
	if (OwnerModel == nullptr)
	{
		return false;
	}


	return true;
}

void UDSCupboardModel::OnAdaptiveAndAdSorptionCallback(bool bExtentsModified, bool bCenterModified, const FVector& OutExtents, const FVector& OutCenter)
{
	if (bExtentsModified /*|| bSelfExtentsChange*/)
	{
		UDSCupboardLibrary::WriteBaseDHWParamtersFromVector(OutExtents * 2.f, ModelInfo.ComponentTreeData->ComponentParameters);
		OriginalComponentData.Reset();
		OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf);
	}
	if (bExtentsModified || bCenterModified)
	{
		Property->GetTransformPropertyRef().Location = OutCenter;
		OnExecuteAction(FDSModelExecuteType::ExecuteTransform);
	}
}

void UDSCupboardModel::UpdateDWHParamters(const FVector& Size, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	if (GetModelType() == EDSModelType::E_Custom_FunctionalDrawer)
	{
		auto Operator = StaticCastSharedPtr<FDrawerAdaptationOperator>(GetAdaptationOperator());
		double UpExtent = 0;
		double DownExtent = 0;
		double LeftExtent = 0;
		double RightExtent = 0;

		if (Operator.IsValid())
		{
			auto EvenInfo = Operator->GetAdaptationEvenInfo();
			auto SourceExtents = EvenInfo.SourceExtents;

			auto& TD = GetModelInfo().ComponentTreeData;

			auto UpObj = EvenInfo.GetAroundEnv(EAdaptationDirection::E_Up);
			auto DownObj = EvenInfo.GetAroundEnv(EAdaptationDirection::E_Down);
			if (UpObj.IsValid() && UpObj.Pin()->GetIntersectionType() != EIntersectionDataType::E_Drawer)
			{
				UpExtent = UpObj.Pin()->GetOriBox().Extents.Z;
				TD->SetParameter(PARAM_SCBJT_STR, FString::SanitizeFloat(UpObj.Pin()->GetOriBox().Extents.Z * 20));
			}
			if (DownObj.IsValid() && DownObj.Pin()->GetIntersectionType() != EIntersectionDataType::E_Drawer)
			{
				DownExtent = DownObj.Pin()->GetOriBox().Extents.Z;
				TD->SetParameter(PARAM_XCBJT_STR, FString::SanitizeFloat(DownObj.Pin()->GetOriBox().Extents.Z * 20));
			}

			auto LeftObj = EvenInfo.GetAroundEnv(EAdaptationDirection::E_Forward);
			auto RightObj = EvenInfo.GetAroundEnv(EAdaptationDirection::E_Backward);
			if (LeftObj.IsValid() && LeftObj.Pin()->GetIntersectionType() != EIntersectionDataType::E_Drawer)
			{
				LeftExtent = LeftObj.Pin()->GetOriBox().Extents.X;
				TD->SetParameter(PARAM_ZCBJT_STR, FString::SanitizeFloat(LeftObj.Pin()->GetOriBox().Extents.X * 20));
			}
			if (RightObj.IsValid() && RightObj.Pin()->GetIntersectionType() != EIntersectionDataType::E_Drawer)
			{
				RightExtent = RightObj.Pin()->GetOriBox().Extents.X;
				TD->SetParameter(PARAM_YCBJT_STR, FString::SanitizeFloat(RightObj.Pin()->GetOriBox().Extents.X * 20));
			}
		}

		UDSCupboardLibrary::WriteBaseDrawerParamtersFromVector(Size, ModelInfo.ComponentTreeData->ComponentParameters);
		UDSCupboardLibrary::WriteBaseFGParamtersFromVector(UpExtent * 2, DownExtent * 2, LeftExtent * 2.f, RightExtent * 2.f, ModelInfo.ComponentTreeData->ComponentParameters);
	}
	else
	{
		UDSCupboardLibrary::WriteBaseDHWParamtersFromVector(Size, ModelInfo.ComponentTreeData->ComponentParameters);
	}

	OriginalComponentData.Reset();
	OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf, BroadcastMarkPtr);
}

void UDSCupboardModel::UpdateLocationAndRotation(const FVector& Location, const FQuat& Rotation, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	Property->GetTransformPropertyRef().Location = Location;
	Property->GetTransformPropertyRef().Rotation = FRotator(Rotation);
	OnExecuteAction(FDSModelExecuteType::ExecuteTransform, BroadcastMarkPtr);
}

void UDSCupboardModel::UpdateLocation(const FVector& Location)
{
	Property->GetTransformPropertyRef().Location = Location;
	OnExecuteAction(FDSModelExecuteType::ExecuteTransform);
}

FVector UDSCupboardModel::GetLocation()
{
	return Property->GetTransformPropertyRef().Location;
}

void UDSCupboardModel::ResetFGInfo()
{
	if (ModelInfo.ComponentTreeData != nullptr)
	{
		ModelInfo.ComponentTreeData->SetParameter(PARAM_SBFG, TEXT("0"));
		ModelInfo.ComponentTreeData->SetParameter(PARAM_XBFG, TEXT("0"));
		ModelInfo.ComponentTreeData->SetParameter(PARAM_ZBFG, TEXT("0"));
		ModelInfo.ComponentTreeData->SetParameter(PARAM_YBFG, TEXT("0"));
	}
}


const TSharedPtr<FFunctionalDependencyMap>& UDSCupboardModel::GetSubFunctionalNodeDependencyMap()
{
	if (!SubFunctionalNodeDependencyMap.IsValid())
	{
		SubFunctionalNodeDependencyMap = MakeShared<FFunctionalDependencyMap>();
	}
	return SubFunctionalNodeDependencyMap;
	// TODO: 在此处插入 return 语句
}

void UDSCupboardModel::HandleChildrenFunctionalModelAdaptation()
{
	//// TODO
	//找到Map中只依赖柜子的对象，从他们开始逐层遍历功能件依赖
	auto& SelfDependentMap = GetSubFunctionalNodeDependencyMap()->GetDependencyInfoMapRef();

	TMap<TWeakPtr<FFunctionalDependencyInfo>, int32> NodeLevels;
	TArray<TWeakPtr<FFunctionalDependencyInfo>> SortedNodes;

	// 递归计算依赖层级
	std::function<void(const TWeakPtr<FFunctionalDependencyInfo>&, TMap<TWeakPtr<FFunctionalDependencyInfo>, int32>&, TSet<FString>&, int32)> GetNodeLevel;
	GetNodeLevel = [&](const TWeakPtr<FFunctionalDependencyInfo>& Node, TMap<TWeakPtr<FFunctionalDependencyInfo>, int32>& Visited, TSet<FString>& Path, int32 Levels) -> void
		{
			if (!Node.IsValid()) return;
			FString UUID = Node.Pin()->GetUUID();

			if (Path.Contains(UUID))
			{
				return;
			}
			Path.Add(UUID);

			if (Visited.Contains(Node) && Visited[Node] > Levels) return;
			Visited.Add(Node, Levels);

			const auto& BeDependentNodes = Node.Pin()->GetBeDependentNodes();
			for (const auto& Pair : BeDependentNodes)
			{
				if (Pair.Key.IsValid())
				{
					GetNodeLevel(Pair.Key, Visited, Path, Levels+1);
				}
			}
			return;
		};
	// 计算每个节点的层级

	for (const auto& Node : SelfDependentMap)
	{
		if (Node.Value.IsValid() && Node.Value->bEnablePassiveAdaptation)
		{
			TSet<FString> Path;
			GetNodeLevel(Node.Value, NodeLevels, Path,1);
		}
	}
	NodeLevels.GenerateKeyArray(SortedNodes);
	// 按层级升序排序
	SortedNodes.Sort([&](const TWeakPtr<FFunctionalDependencyInfo>& A, const TWeakPtr<FFunctionalDependencyInfo>& B)
		{
			FString UUIDA = A.IsValid() ? A.Pin()->GetUUID() : TEXT("");
			FString UUIDB = B.IsValid() ? B.Pin()->GetUUID() : TEXT("");
			return NodeLevels.FindRef(A) < NodeLevels.FindRef(B);
		});

	TMap<FString, bool> ExecuteAdaptationMap;
	//临时测试功能件自适应
	TArray<FString> DependentInfoArray;
	SelfDependentMap.GenerateKeyArray(DependentInfoArray);
	for (auto& DependentInfo : SelfDependentMap)
	{
		ExecuteAdaptationMap.Add(DependentInfo.Key, false);
	}

	for (auto& Iter : SortedNodes)
	{
		if (!Iter.IsValid())
		{
			continue;
		}

		UDSBaseModel* Model = UDSCupboardLibrary::GetModelByUUID(Iter.Pin()->GetUUID(), GetModelInfo());

		if (UDSCupboardLibrary::IsFunctionalCupboardModel(Model))
		{
			if (Model->GetModelType() == EDSModelType::E_Custom_FunctionalDrawer && Cast<UDSCupboardModel>(Model) != nullptr)
			{
				Cast<UDSCupboardModel>(Model)->ResetFGInfo();
			}
			TSharedPtr<FModelAdaptationOperatorBase> Operator = Cast<UDSCupboardModel>(Model)->CreateAdaptationOperator();
			Operator->ExecuteStepAdaptation(false,FDSBroadcastMarkData::OnlyOutlineBroadcastMark);
			//找依赖此对象的节点，判断依赖的节点是否都自适应完成
		}
	}

	//跨柜体检测门的依赖关系
	TArray<UDSBaseModel*> Result;
	TArray<FString> DoorIds;
	UDSCounterTopLibrary::GetSingleComponentModels(this, Result);
	for (auto& It : Result)
	{
		if (UDSCupboardModel* BoardModel = Cast<UDSCupboardModel>(It))
		{
			for (auto& Id : UDSModelDependencySubsystem::GetInstance()->FindDoorByDependentBoard(BoardModel->GetModelInfoRef().ComponentTreeData->UUID))
			{
				DoorIds.AddUnique(Id);
			}
		}
	}

	if (DoorIds.IsEmpty())
	{
		return;
	}

	TArray<UDSBaseModel*> AllCustoms = UDSMVCSubsystem::GetInstance()->GetAllCustomModels();
	TArray<UDSCupboardModel*> DoorModels;
	TArray<UDSCupboardModel*> CabinetModels;
	for (auto& Custom  : AllCustoms)
	{
		if (UDSCupboardModel* CustomModel = Cast<UDSCupboardModel>(Custom))
		{
			if (DoorIds.IsEmpty())
			{
				break;
			}
		
			if (DoorIds.Contains(CustomModel->GetModelInfoRef().ComponentTreeData->UUID))
			{
				DoorIds.Remove(CustomModel->GetModelInfoRef().ComponentTreeData->UUID);
				DoorModels.Add(CustomModel);
			}
		}
	}
	
	for (auto& DoorModel : DoorModels)
	{
		CabinetModels.AddUnique(DoorModel->GetRootCupboardModel());
	}

	for (auto& CabinetModel : CabinetModels)
	{
		UDSCupBoardDoorLibrary::UpdateDoorOnCupboard(CabinetModel);
	}
}

void UDSCupboardModel::UpdateAdaptationIntersectionTransform()
{
	if (IsFunctionalCupboardModel())
	{
		if (!AdaptationOperator.IsValid())
		{
			CreateAdaptationOperator();
		}
		RemoveFunctionalDependency();
		AdaptationOperator->ExecuteStepAdaptation();
	}
}

void UDSCupboardModel::RemoveFunctionalDependency()
{
	if (IsFunctionalCupboardModel())
	{
		UDSCupboardModel* RootModel = GetRootCupboardModel();
		auto& DependencyMap = RootModel->GetSubFunctionalNodeDependencyMap();
		auto DependencyInfo = DependencyMap->GetDependencyInfo(ModelInfo.GetModelUUID());
		if (DependencyInfo.IsValid())
		{
			DependencyInfo->bEnablePassiveAdaptation = false;
			for (auto& Iter : DependencyInfo->DependentNodes)
			{
				if (Iter.Value.IsValid())
				{
					Iter.Value.Pin()->RemoveBeDependentNode(DependencyInfo.ToSharedRef());
				}
			}
		}
	}
}

TSharedPtr<FModelAdaptationOperatorBase>& UDSCupboardModel::GetAdaptationOperator()
{
	return AdaptationOperator;
}

TSharedPtr<FModelAdaptationOperatorBase>& UDSCupboardModel::CreateAdaptationOperator()
{
	// TODO: 在此处插入 return 语句

	if (IsFunctionalCupboardModel())
	{
		if (GetModelType() == EDSModelType::E_Custom_FunctionalDrawer)
		{
			AdaptationOperator = MakeShared<FDrawerAdaptationOperator>(this);
		}
		else
		{
			AdaptationOperator = MakeShared<FFunctionalAdaptationOperator>(this);
		}
	}
	else
	{
		AdaptationOperator = MakeShared<FCupboardModelAdaptationOperator>(this);
	}
	return AdaptationOperator;
}
