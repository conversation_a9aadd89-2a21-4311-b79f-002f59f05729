// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "ModelAdaptationOperatorBase.h"
#include "SubSystems/AdaptiveAdsorption/Data/AdaptationDynamicData.h"
//#include "FunctionalAdaptationOperator.generated.h"


typedef TFunction<void()> TFunctionalAdaptationCompleteHandle;

class UDSBaseModel;
class FIntersectionDynamicMesh;
class FDynamicMeshAdaptiveAdsorption;
struct FMultiComponentDataItem;
struct FDSCupboardModelDependentNodeInfo;
struct FDsCupboardModelDependModelList;
struct FAdaptationExecuterInitializedData;
struct FAdaptiveAdsorptionRule3D;
/**
 * 
 */
class DESIGNSTATION_API FFunctionalAdaptationOperator :public FModelAdaptationOperatorBase
{
public:
	FFunctionalAdaptationOperator(UDSBaseModel* InModel);

	virtual ~FFunctionalAdaptationOperator() {};
public:
	/**
	* 准备适配操作
	* @param InSourceModel 源模型
	* @return 是否成功准备适配
	*/
	virtual bool PrepareAdaptation(UDSBaseModel* InSourceModel) override;

	/**
	* 当适配开始时调用
	*/
	virtual void OnAdaptationStarting() override;

	/**
	* 准备适配操作，指定源模型和目标模型
	* @param InSourceMolde 源模型
	* @param InTargetModel 目标模型
	*/
	virtual void PrepareAdaptation(UDSBaseModel* InSourceMolde, UDSBaseModel* InTargetModel);


	virtual void GenerateInitializedData_Functional(const TSharedPtr<FAdaptationExecuterInitializedData>& InInitialziedData);


	virtual void GenerateInitializedData_CornerCut(const TSharedPtr<FAdaptationExecuterInitializedData>& InInitialziedData);

	virtual void GenerateInitializedData_Corner(const TSharedPtr<FAdaptationExecuterInitializedData>& InInitialziedData);

	/**
	* 生成目标模型的交集环境
	* @param InTargetMode 目标模型
	* @param RootTrans 根变换
	* @param OutEnv 输出的交集动态网格数组
	* @param InComponentTreeData 组件树数据
	* @param IgnoreModelTreeData 忽略的模型树数据
	* @param InDependencyInfo 依赖信息列表
	*/

	virtual void GeneratorTargetModelIntersectionEnv(UDSBaseModel* InTargetMode, const FTransform& RootTrans, TArray<TSharedPtr<FIntersectionDynamicMesh>>& OutEnv,
		const TSharedPtr<FMultiComponentDataItem>& InComponentTreeData, const TSharedPtr<FMultiComponentDataItem>& IgnoreModelTreeData,
		const TSharedPtr<struct  FFunctionalDependencyInfo>& InDependencyInfo);


	/**
	* 生成适配环境数据
	*/
	virtual void GenerateAdaptationEnvData();

	/**
	* 在没有依赖的情况下生成适配环境数据
	*/
	virtual void GenerateAdapationEnvDataWithoutDenpendented();

	/**
	* 生成适配源信息
	*/
	virtual void GenerateAdaptationSourceInfo();

	/**
	* 根据射线起点和方向处理适配和吸附
	* @param RayStartPoint 射线起点
	* @param RayDir 射线方向
	* @return 是否成功处理
	*/
	virtual bool HandleAdaptiveAndAdssorptionByHitPoint(const FVector& RayStartPoint, const FVector& RayDir) override;


	/**
	* 完成适配操作
	* @param OutResault 输出的适配结果
	*/
	virtual void CompleteAdaptation(FAdaptiveAdsorptionResault& OutResault,bool bRecalculateDependentedNode = true);

	/**
	* 处理适配结果
	* @param OutResault 适配结果
	*/
	virtual void HandelAdaptationResault(FAdaptiveAdsorptionResault& OutResault);

	/**
	* 重新计算依赖目标的适配信息
	* @param OutDependenceTargetResaults 输出的依赖目标结果数组
	* @param InUUID 输入的UUID
	* @param bIncludeNext 是否包含下一级
	* @param Level 当前层级
	*/
	virtual void ReCalculateDependenceTargetAdapationInfo(TArray<FAdaptiveAdsorptionResault>& OutDependenceTargetResaults, const TSharedPtr<FFunctionalDependencyInfo>& InDependentInfo, bool bIncludeNext = false, int32 Level = 0);

	/**
	* 执行一步适配操作
	* @param bNeedWaitParse 是否需要等待解析
	*/
	virtual void ExecuteStepAdaptation(const TSharedPtr<FDSBroadcastMarkData>& InBroadcastMarkPtr = FDSBroadcastMarkData::BroadcastToMVCMark) override;

	virtual void ExecuteStepAdaptation(bool bReCalculateBeDependency, const TSharedPtr<FDSBroadcastMarkData>& InBroadcastMarkPtr = FDSBroadcastMarkData::BroadcastToMVCMark) override;


	virtual void ExecuteEvenStepAdaptation(bool bHorizontalEven, const TSharedPtr<FDSBroadcastMarkData>& InBroadcastMarkPtr = FDSBroadcastMarkData::BroadcastToMVCMark);
	/**
	* 执行事件
	* @param Count 事件计数
	*/
	virtual void ExecuteEven(int Count);

	/**
	* 获取适配事件信息
	* @return 适配事件信息
	*/
	virtual FAdaptationEvenInfo GetAdaptationEvenInfo();

	virtual FAdaptiveAdsorptionRule3D GetAdaptationRule();

	/**
	* 获取交集的命中点
	* @param Start 起点
	* @param End 终点
	* @param Dir 方向
	* @return 是否成功获取
	*/
	virtual bool GetIntersectionHitPoint(FVector& Start, FVector& End, const FVector& Dir) const;

	/**
	* 获取交集线段
	* @param IntersectionFun 交集处理函数
	* @param Dir 方向
	*/
	template<typename IntersectionFunType>
	void GetIntersectionSeg(IntersectionFunType IntersectionFun, const FVector& Dir) const
	{
		FVector Start = FVector::ZeroVector;
		FVector End = FVector::ZeroVector;
		bool bSuccess = GetIntersectionHitPoint(Start, End, Dir);
		IntersectionFun(Start, End, bSuccess);
	}

	/**
	* 获取源适配信息
	* @return 源适配信息
	*/
	const TSharedPtr<FDynamicMeshAdaptiveAdsorption>& GetSourceAdapationInfo();

	/**
	* 显示调试信息
	* @param WorldContextObject 世界上下文对象
	*/
	virtual void ShowDebug(UObject* WorldContextObject) override;

	/**
	* 适配和吸附的回调函数
	* @param AdaptationData 适配数据
	*/
	virtual void OnAdaptiveAndAdSorptionCallback(const TSharedPtr<FAdaptationData>& AdaptationData) override;

	/**
	* 更新初始化数据
	*/
	virtual void UpdateInitializedData();

	/**
	* 刷新节点的父节点信息
	* @param InAdaptationResault 输入的适配结果
	*/
	void RefreshNodeParent(const FAdaptiveAdsorptionResault& InAdaptationResault);

	/**
	* 根据适配UUID获取相等的模型
	* @param NegativeOwnerUUID 负所有者UUID
	* @param PositiveOwnerUUID 正所有者UUID
	* @return 相等的模型
	*/
	UDSBaseModel* GetEqualModelByAdaptationUUID(const FString& NegativeOwnerUUID, const FString& PositiveOwnerUUID);


	virtual void UpdateSourceIntersectionTransform() override;

	virtual TArray<TSharedPtr<FIntersectionDynamicMesh>> GetRealEnvironments() { return AdaptationEnvs; }

protected:

	TSharedPtr<FDynamicMeshAdaptiveAdsorption>  SourceAdaptationInfo;

	TArray<TSharedPtr<FIntersectionDynamicMesh>> AdaptationEnvs;

	UDSBaseModel* TargetModel;

	bool bPrepared;
	//吸附规则
};
