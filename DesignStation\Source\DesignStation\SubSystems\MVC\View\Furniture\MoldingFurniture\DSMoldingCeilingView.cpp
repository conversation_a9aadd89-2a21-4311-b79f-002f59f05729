#pragma once

#include "DSMoldingCeilingView.h"

#include "SubSystems/MVC/Core/Macro.h"
#include "SubSystems/MVC/Library/DSToolLibrary.h"
#include "SubSystems/MVC/Model/Furniture/MoldingFurniture/DSMoldingCeilingModel.h"
#include "SubSystems/Library/DesignStationFunctionLibrary.h"
#include "SubSystems/MVC/Core/Property/FurnitureProperty.h"

void ADSMoldingCeilingView::Init()
{
	Super::Init();
}

void ADSMoldingCeilingView::BeginPlay()
{
	Super::BeginPlay();
}

void ADSMoldingCeilingView::RealTransformViewLogic(UDSBaseModel* InModel)
{
	TSharedPtr<FDSMoldingCeilingProperty> Property = StaticCastSharedPtr<FDSMoldingCeilingProperty>(InModel->GetPropertySharedPtr());
	if (Property.IsValid())
	{
		//FTransform ParentTransform = Property->GetTransformProperty().ToUETransform();
	   // FTransform RelativeTransform = Property->RelativeTransform;
		FTransform Transform = Property->GetActualTransform();

		auto IsDownRot = Property->GetTransformPropertyRef().IsDownRot;
		auto IsLeftRot = Property->GetTransformPropertyRef().IsLeftRot;

		FVector Scale(1, 1, 1);
		Property->TransformProperty.IsDownRot ? Scale.X = -1 : Scale.X = 1;
		Property->TransformProperty.IsLeftRot ? Scale.Y = -1 : Scale.Y = 1;

		Transform.SetScale3D(Scale);
		SetActorTransform(Transform, true);
	}
}

void ADSMoldingCeilingView::OnGenerateMeshWorkFinished()
{
	if (Model != nullptr)
	{
		if (Model->GetViewHidden())
		{
			RealHiddenViewLogic(Model);
		}
	}
}

void ADSMoldingCeilingView::RealSpawnViewLogic(UDSBaseModel* InModel)
{
	Super::RealSpawnViewLogic(InModel);
}

void ADSMoldingCeilingView::RealUpdateViewLogic(UDSBaseModel* InModel)
{
	UDSMoldingCeilingModel* NewModel = Cast<UDSMoldingCeilingModel>(InModel);
	if (NewModel == nullptr || Model == nullptr)
	{
		return;
	}

	checkf(Model->GetUUID().Equals(NewModel->GetUUID()), TEXT("ADSMoldingCeilingView::UpdateView --- No Equal Model"));

	//Model->ShallowCopy(NewModel);

	Super::RealUpdateViewLogic(Model);

	FDSMoldingCeilingProperty* Property = static_cast<FDSMoldingCeilingProperty*>(Model->GetProperty());
	if (Property == nullptr)
	{
		return;
	}

	//MeshComponent->ClearAllMeshSections();

	//if (Property->FileSourceProperty.SourcePath.IsEmpty())
	//{
	//	UDesignStationFunctionLibrary::CreateLazyLoadBoxMesh(MeshComponent, StaticCastSharedPtr<FDSFurnitureBaseProperty>(Model->GetPropertySharedPtr()));
	//}

#if 0
	UDSMoldingCeilingModel* NewModel = Cast<UDSMoldingCeilingModel>(InModel);
	if (OBJECT_VALID_FOR_USE(NewModel) && OBJECT_VALID_FOR_USE(Model))
	{
		checkf(Model->GetUUID().Equals(NewModel->GetUUID()), TEXT("ADSMoldingCeilingView::UpdateView --- No Equal Model"));

		Model->ShallowCopy(NewModel);

		FDSMoldingCeilingProperty* Property = static_cast<FDSMoldingCeilingProperty*>(Model->GetProperty());
		UDSToolLibrary::GenerateMesh(MeshComponent, Property->GetFileSourceProperty());

		this->SetActorTransform(Property->GetTransformProperty().ToUETransform());
	}

#endif

	RealTransformViewLogic(InModel);

	if (IsValid(Model))
	{
		ClearComponentOverlaps();
		UpdateOverlaps(true);
	}
}

void ADSMoldingCeilingView::RealHiddenViewLogic(UDSBaseModel* InModel)
{
    Super::RealHiddenViewLogic(InModel);
}

void ADSMoldingCeilingView::RealUnHiddenViewLogic(UDSBaseModel* InModel)
{
    Super::RealUnHiddenViewLogic(InModel);
}

void ADSMoldingCeilingView::RealHoverViewLogic(UDSBaseModel* InModel)
{
    Super::RealHoverViewLogic(InModel);
}

void ADSMoldingCeilingView::RealUnHoverViewLogic(UDSBaseModel* InModel)
{
    Super::RealUnHoverViewLogic(InModel);
}

void ADSMoldingCeilingView::RealSelectViewLogic(UDSBaseModel* InModel)
{
    Super::RealSelectViewLogic(InModel);
}

void ADSMoldingCeilingView::RealUnSelectViewLogic(UDSBaseModel* InModel)
{
    Super::RealUnSelectViewLogic(InModel);
}
