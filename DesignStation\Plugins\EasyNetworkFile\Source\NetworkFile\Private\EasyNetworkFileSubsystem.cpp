#include "EasyNetworkFileSubsystem.h"
#include "Core/Tasks/DownloadTask/NetworkFileDownloadTask.h"
#include "Core/Tasks/DownloadTask/NetworkFileUploadTask.h"

DEFINE_LOG_CATEGORY(LogEasyNetworkFileSubsystem);

UEasyNetworkFileSubsystem* UEasyNetworkFileSubsystem::Instance = nullptr;

UEasyNetworkFileSubsystem* UEasyNetworkFileSubsystem::GetInstance()
{
	return Instance;
}

void UEasyNetworkFileSubsystem::Initialize(FSubsystemCollectionBase& Collection)
{
	Super::Initialize(Collection);

#if WITH_EDITOR
	if (!FParse::Value(FCommandLine::Get(), TEXT("UploadAccessKey="), UploadAccessKey))
	{
		UploadAccessKey = TEXT("488bf9e443af478cbf0d5f43ccaff277");
	}

	if (!FParse::Value(FCommandLine::Get(), TEXT("UploadSecretKey="), UploadSecretKey))
	{
		UploadSecretKey = TEXT("d837d0b132ba411f8f30721bc7d6c8f3");
	}
#endif

	PreGarbageCollectHandle = FCoreUObjectDelegates::GetPreGarbageCollectDelegate().AddLambda([this]() { ReleaseUselessTask(); });

	Instance = this;

	ProcessorPool = MakeShareable(FQueuedThreadPool::Allocate());
	if (ProcessorPool)
	{
		ProcessorPool->Create(FPlatformMisc::NumberOfCoresIncludingHyperthreads(), 1024 * 1024, TPri_Normal, TEXT("EasyNetworkFileSubsystem Processor Pool"));
	}
	else
	{
		UE_LOG(LogEasyNetworkFileSubsystem, Error, TEXT("Allocate thread pool for network file processor failure, operations for network file is invalid now."));
		return;
	}

	TaskTypes.Add(ENetworkFileOperation::ENFO_Download, UNetworkFileDownloadTask::StaticClass());
	TaskTypes.Add(ENetworkFileOperation::ENFO_Upload, UNetworkFileUploadTask::StaticClass());

	TaskPoolSize.Add(ENetworkFileOperation::ENFO_Download, 20);
}

void UEasyNetworkFileSubsystem::Deinitialize()
{
	Instance = nullptr;

	FCoreUObjectDelegates::GetPreGarbageCollectDelegate().Remove(PreGarbageCollectHandle);

	TaskMap.Empty();
	TaskPool.Empty();

	if (ProcessorPool)
	{
		ProcessorPool->Destroy();
		ProcessorPool.Reset();
	}

	Super::Deinitialize();
}

void UEasyNetworkFileSubsystem::SetTaskPoolSize(ENetworkFileOperation InOperation, int32 InSize)
{
	TaskPoolSize.Add(InOperation, InSize);
}

UBaseNetworkFileTask* UEasyNetworkFileSubsystem::CreateTask(ENetworkFileOperation InOperation, bool bAutoRelease)
{
	if (!TaskTypes.Contains(InOperation))
	{
		return nullptr;
	}

	UBaseNetworkFileTask* Task = nullptr;
	FNetworkFileTaskPoolPair* PoolPair = TaskPool.FindByPredicate([&](const FNetworkFileTaskPoolPair& InPair) { return InPair.Type == InOperation; });
	if (PoolPair == nullptr || PoolPair->PoolData.IsEmpty())
	{
		Task = NewObject<UBaseNetworkFileTask>(this, TaskTypes[InOperation]);
	}
	else
	{
		Task = PoolPair->PoolData.Pop();
		Task->CleanTask();
	}

	if (bAutoRelease)
	{
		AutoReleaseTasks.Add(Task->GetTaskId());
	}

	TaskMap.Add(Task->GetTaskId(), Task);
	return Task;
}

UBaseNetworkFileTask* UEasyNetworkFileSubsystem::FindSameTask(UBaseNetworkFileTask* InTask)
{
	for (const TPair<FString, UBaseNetworkFileTask*>& Pair : TaskMap)
	{
		if (Pair.Value != InTask && InTask->IsSameTask(Pair.Value))
		{
			return Pair.Value;
		}
	}

	return nullptr;
}

const TMap<ENetworkFileOperation, TSubclassOf<UBaseNetworkFileTask>>& UEasyNetworkFileSubsystem::GetTaskTypes() const
{
	return TaskTypes;
}

const TMap<FString, UBaseNetworkFileTask*>& UEasyNetworkFileSubsystem::GetTaskMap() const
{
	return TaskMap;
}

UBaseNetworkFileTask* UEasyNetworkFileSubsystem::FindTask(const FString& TaskId) const
{
	return TaskMap.Contains(TaskId) ? TaskMap[TaskId] : nullptr;
}

void UEasyNetworkFileSubsystem::ReleaseTask(const FString& TaskId)
{
	if (!TaskMap.Contains(TaskId))
	{
		return;
	}

	FNetworkFileTaskPoolPair* PoolPair = TaskPool.FindByPredicate(
		[&](const FNetworkFileTaskPoolPair& InPair)
		{
			return InPair.Type == TaskMap[TaskId]->GetType();
		});

	if (PoolPair == nullptr)
	{
		PoolPair = &TaskPool.AddDefaulted_GetRef();
		PoolPair->Type = TaskMap[TaskId]->GetType();
	}

	PoolPair->PoolData.Add(TaskMap[TaskId]);
	TaskMap.Remove(TaskId);
}

void UEasyNetworkFileSubsystem::SetUploadAccessKey(const FString& InAccessKey)
{
	UploadAccessKey = InAccessKey;
}

void UEasyNetworkFileSubsystem::SetUploadSecretKey(const FString& InSecretKey)
{
	UploadSecretKey = InSecretKey;
}

bool UEasyNetworkFileSubsystem::AddMissionToPool(const TSharedPtr<IQueuedWork>& InMission, EQueuedWorkPriority Priority)
{
	if (!InMission || !ProcessorPool)
	{
		return false;
	}

	ProcessorPool->AddQueuedWork(InMission.Get(), Priority);
	return true;
}

bool UEasyNetworkFileSubsystem::RetractMissionFromPool(const TSharedPtr<IQueuedWork>& InMission)
{
	if (!InMission || !ProcessorPool)
	{
		return false;
	}

	InMission->Abandon();
	ProcessorPool->RetractQueuedWork(InMission.Get());
	return true;
}

bool UEasyNetworkFileSubsystem::ReleaseUselessTask()
{
	UE_LOG(LogEasyNetworkFileSubsystem, Log, TEXT("Collect and release all useless network file tasks."));

	for (TArray<FString>::TIterator It = AutoReleaseTasks.CreateIterator(); It; ++It)
	{
		UBaseNetworkFileTask* Task = TaskMap.Contains(*It) ? TaskMap[*It] : nullptr;
		if (Task == nullptr)
		{
			It.RemoveCurrent();
			continue;
		}

		if (!Task->IsProcessing())
		{
			ReleaseTask(*It);
			It.RemoveCurrent();
		}
	}

	ResizeTaskPool();

	return true;
}

void UEasyNetworkFileSubsystem::ResizeTaskPool()
{
	TMap<ENetworkFileOperation, int32> OperationTaskCount;
	for (const TPair<FString, UBaseNetworkFileTask*>& Pair : TaskMap)
	{
		if (Pair.Value == nullptr)
		{
			continue;
		}

		ENetworkFileOperation TaskType = Pair.Value->GetType();
		if (!OperationTaskCount.Contains(TaskType))
		{
			OperationTaskCount.Add(TaskType, 0);
		}

		OperationTaskCount[TaskType] += 1;
	}

	for (FNetworkFileTaskPoolPair& PoolPair : TaskPool)
	{
		if (!TaskPoolSize.Contains(PoolPair.Type))
		{
			PoolPair.PoolData.Empty();
			continue;
		}

		int32 InProcessTaskCount = OperationTaskCount.Contains(PoolPair.Type) ? OperationTaskCount[PoolPair.Type] : 0;
		int32 LimitCountInPool = FMath::Clamp(TaskPoolSize[PoolPair.Type] - InProcessTaskCount, 0, TaskPoolSize[PoolPair.Type]);

		while (PoolPair.PoolData.Num() > LimitCountInPool)
		{
			PoolPair.PoolData.Pop();
		}
	}
}
