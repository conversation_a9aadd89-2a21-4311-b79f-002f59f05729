// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "ProceduralMeshComponent.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "SubSystems/DSCore.h"
#include "SubSystems/MVC/Core/Property/CommonProperty.h"
#include "SubSystems/MVC/Core/DSMouseData.h"
#include "SubSystems/MVC/Core/DSPolygonData.h"
#include "SubSystems/MVC/Core/Function/DSFunction.h"
#include "Subsystems/MVC/Core/Property/CounterTopLineProperty.h"
#include "Subsystems/MVC/Core/Property/CounterTopPointProperty.h"
#include "SubSystems/MVC/Core/Property/CounterTopProperty.h"
#include "SubSystems/MVC/Core/Property/SideCounterTopProperty.h"
#include "SubSystems/MVC/Model/Core/DSMarkData.h"
#include "SubSystems/MVC/Core/Property/FurnitureProperty.h"
#include "SubSystems/MVC/Core/Property/PillarProperty.h"
#include "SubSystems/MVC/StateMachine/Core/DSSMCore.h"
#include "Subsystems/MVC/Model/Custom/ModelInfo/DSCustomStyleInfo.h"
#include "DSToolLibrary.generated.h"

class FDSDoorAndWindowProperty;
class FDSPillarProperty;
class FDSBaseProperty;
class FDSHousePathProperty;
class ADSBaseView;
class UDSBaseModel;
class FDSSinkProperty;

enum class EDSModelType : uint16;

/**
 * 
 */
UCLASS()
class DESIGNSTATION_API UDSToolLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()

public:
	static void GetBoundingBox()
	{
	}

	//Model
	static void GenerateMesh(UProceduralMeshComponent*& MeshComponent, const FDSFileSourceProperty& FileSource);

	static UDSBaseModel* CopyDataExecute(UDSBaseModel* EditModel);
	static UDSBaseModel* CopyMultiSelectData(UDSBaseModel* EditModel);
	static UDSBaseModel* CopyGroupData(UDSBaseModel* EditModel);
	static UDSBaseModel* CopyGenerateData(UDSBaseModel* EditModel);

	//获取鼠标下的Actor
	static ADSBaseView* GetActorUnderMouse(const FDSMouseData& InMouseData);

	static UDSBaseModel* GetModelUnderMouse(const FDSMouseData& InMouseData);

	/*
	 *  @@ Get Model Under Mouse and hit point
	 *  @@ trace owner model [ or parent model for cupboard model]
	 */
	static UDSBaseModel* GetModelUnderMouseWithHitPoint(const FDSMouseData& InMouseData, FVector& HitPoint);
	/*
	 *  @@ Get Model Under Mouse and hit point
	 *  @@ get hit model self, no trace owner model
	 */
	static UDSBaseModel* GetModelUnderMouseWithHitPoint_NoGetOwner(const FDSMouseData& InMouseData, FVector& HitPoint);

	static UDSBaseModel* GetModelUnderMouse(UObject* WorldContextObject, const FVector2D& MouseScreenPos, FHitResult& HitResult, const TArray<EDSModelType>& InTypeFilters);

	static ADSBaseView* GetViewUnderMouseByName(const FString& InName, const FVector& WorldLoc, const FVector& WorldDir);

	static ADSBaseView* GetViewUnderMouseByName(const TArray<FString>& InNames, const FVector& WorldLoc, const FVector& WorldDir);

	static ADSBaseView* GetComponentActorUnderMouse(const FDSMouseData& InMouseData);
	static UDSBaseModel* GetComponentModelUnderMouse(const FDSMouseData& InMouseData);
	static UDSBaseModel* GetComponentModelUnderMouseWithHitPoint(const FDSMouseData& InMouseData, FVector& HitPoint);

	static UDSBaseModel* GetRootOwner(UDSBaseModel* InModel);

	static bool ModelVaild(UDSBaseModel* InModel);

	static bool ModelOverlay(UDSBaseModel* InModel);

	//判断是否有按键保持按下状态
	static bool IsSomeKeyHold(const FKey& Key);

	static bool IsKeyDownForMultiOperator();

	//获取鼠标位置和方向
	static void GetMousePositionAndDir(FVector& OutPosition, FVector& OutDir);

	static FDSMaterialProperty GetSelectMaterialProperty();
	static FDSMaterialProperty GetWallMaterialProperty();
	static FDSMaterialProperty GetScalerMaterialProperty();
	static FDSMaterialProperty GetScaler2MaterialProperty();
	static FDSMaterialProperty GetBeamAndPlatformMaterialProperty();
	static FDSMaterialProperty GetPanelMaterialProperty();

	//get and set property data
	static void ParsePropertyCopy(UDSBaseModel* EditModel, const TSharedPtr<FDSBaseProperty>& InProperty);
	static bool IsConstructChanged(UDSBaseModel* EditModel, const TSharedPtr<FDSBaseProperty>& InProperty);
	static TSharedPtr<FDSBaseProperty> GetBasePropertyCopy(UDSBaseModel* EditModel);
	static TSharedPtr<FDSBaseProperty> GetBasePropertyCopy(const EDSModelType& InModelType, const TSharedPtr<FDSBaseProperty>& InProperty);
	static TSharedPtr<FDSHousePathProperty> GetPropertyCopy_Wall(const TSharedPtr<FDSBaseProperty>& InProperty);
	static TSharedPtr<FDSPillarProperty> GetPropertyCopy_Pillar(const TSharedPtr<FDSBaseProperty>& InProperty);
	static TSharedPtr<FDSBaseProperty> GetPropertyCopy_Base(const TSharedPtr<FDSBaseProperty>& InProperty);
	static TSharedPtr<FDSHousePathProperty> GetPropertyCopy_BeamAndPlatform(const TSharedPtr<FDSBaseProperty>& InProperty);
	static TSharedPtr<FDSDoorAndWindowProperty> GetPropertyCopy_WindowAndDoor(const TSharedPtr<FDSBaseProperty>& InProperty);
	static TSharedPtr<FDSCounterTopProperty> GetPropertyCopy_CounterTop(const TSharedPtr<FDSBaseProperty>& InProperty);
	static TSharedPtr<FDSSideCounterTopProperty> GetPropertyCopy_SideCounterTop(const TSharedPtr<FDSBaseProperty>& InProperty);
	static TSharedPtr<FDSCounterTopLineProperty> GetPropertyCopy_CounterTopLine(const TSharedPtr<FDSBaseProperty>& InProperty);
	static TSharedPtr<FDSCounterTopPointProperty> GetPropertyCopy_CounterTopPoint(const TSharedPtr<FDSBaseProperty>& InProperty);

	template<class T>
	static TSharedPtr<T> GetPropertyCopy(const TSharedPtr<FDSBaseProperty>& InProperty)
	{
		if (InProperty.IsValid() && StaticCastSharedPtr<T>(InProperty))
		{
			TSharedPtr<T> NewProperty = MakeShared<T>();
			NewProperty->CopyData(InProperty.Get());
			return NewProperty;
		}
		return nullptr;
	}

	static void ModelDialog(
		UDSBaseModel* InModel, 
		const FDSModelExecuteType& InExecute, 
		const FString& Msg1, 
		const FString& Msg2, 
		const DSDialogFunction& SureFunction,
		const DSDialogFunction& CancelFunction);
	static void ModelDialog_Inner(
		const TArray<UDSBaseModel*>& InModels,
		const FDSModelExecuteType& InExecute,
		const FString& Msg1,
		const FString& Msg2,
		const DSDialogFunction& SureFunction,
		const DSDialogFunction& CancelFunction);

	//is top level ?
	static bool ModelIsSelf(UDSBaseModel* EditModel);

	//is component type
	static bool ModelIsComponent(UDSBaseModel* EditModel);

	//is custom type
	static bool IsCustomType(UDSBaseModel* EditModel);

	static bool IsCustomBoardType(EDSModelType InType);

	static bool IsCanFunctionalEvenType(EDSModelType InType);

	static bool IsGeneratedLineType(EDSModelType InType);

	static bool IsCustomCupboardType(EDSModelType InType);

	static bool IsCustomCabinetType(EDSModelType InType);

	static bool IsFunctionalTopRootType(EDSModelType InType);

	static bool IsCustomDoorType(EDSModelType InType);

	static bool IsInSameGroup(UDSBaseModel* Model1, UDSBaseModel* Model2);

	//get owner model 
	static UDSBaseModel* GetOwnerModelRecursion(UDSBaseModel* EditModel);

	static FInputChord LoadCommandFromIni(const FString& IniFilename, const FString& SectionName, const FString& CommandId);
	static void SaveCommandToIni(const FString& IniFilename, const FString& SectionName, const FString& CommandId, const FInputChord& InputChord);

#pragma region COLLISION

	/**
	 *  @@ to mark actor is top level actor
	 *  @@ use for custom cupboard actor
	 */
	static bool IsActorTopLevel(ADSBaseView* InActor, ADSBaseView*& TopLevelActor);
	static bool IsActorTopLevel_Only(ADSBaseView* InActor);

	static void SetIgnoreActorForCollision(ADSBaseView* ModifyActor, const TArray<AActor*>& ToIgnoreActors);
	static void SetIgnoreActorForComponentCollision(UPrimitiveComponent* ModifyComponent, const TArray<AActor*>& ToIgnoreActors);
	static void SetIgnoreActorForComponentCollision_Inner(UPrimitiveComponent* ModifyComponent, const TArray<AActor*>& ToIgnoreActors);

	static void SetIgnoreSelfForComponentCollision(UPrimitiveComponent* ModifyComponent);
	static void SetIgnoreComponentForComponentCollision(UPrimitiveComponent* ModifyComponent, const TArray<USceneComponent*>& ToIgnoreComponent);
	static void SetIgnoreComponentForComponentCollision_Inner(UPrimitiveComponent* ModifyComponent, const TArray<USceneComponent*>& ToIgnoreComponent);

	static void ModifyOverlapBySnap(ADSBaseView* ModifyActor, const TArray<ADSBaseView*>& SnapActors, TArray<AActor*>& TopOverlappingActor);

#pragma endregion

#pragma region POLYGON

	static TArray<FVector> CalculateCollisionConvexByNormal(const TArray<FVector>& InVertices, const TArray<FVector>& InNormals, const double& Offset = -0.02f);
	static TArray<FVector> CalculateCollisionConvexByNormal(const TArray<FVector>& InVertices, const TArray<FVector>& InNormals, const FVector InScale3D, const double& Offset = -0.02f);
	static TArray<FVector> CalculateCollisionConvex(const TArray<FVector>& InVertics);
	static void IndentPanelVertices(FDSIndexVerticePanelArr& PanelIndexVertices);
	static TArray<FDSIndexVertice> IndentIndexVertices(const TArray<FDSIndexVertice>& InVertices);
	static void IndentVertices_Concave(TArray<FDSVerticeIndexs>& PanelIndexVertices);
	static void SplitConcaveToConvex(const TArray<FDSIndexVertice>& InPolygonVertices, TArray<TArray<FDSIndexVertice>>& SplitPolygons);
	static TArray<TArray<FDSIndexVertice>> SplitConcaveToConvex_Inner(const TArray<FDSIndexVertice>& InPolygonVertices, const TArray<int32>& ConcaveIndex);
	static TArray<FDSIndexVertice> GetSubVerticeArr(const TArray<FDSIndexVertice>& InPolygonVertices, const int32& Start, const int32& End);
	static TArray<FDSIndexVertice> ConstructIndexVertice(const TArray<FDSVerticeIndexs>& PanelIndexVertices);
	static TArray<int32> GetConcavePointIndex(const TArray<FDSVerticeIndexs>& Vertices);
	static TArray<int32> GetConcavePointIndex(const TArray<FDSIndexVertice>& Vertices);
	static TArray<FDSVerticeIndexs> ConvertToVerticeIndexs(const TArray<FDSIndexVertice>& InVertices);
	static bool IsPolygonConvex(const TArray<FDSVerticeIndexs>& PolygonIndexsVertice);
	static bool IsPolygonConvex(const TArray<FDSIndexVertice>& PolygonVertices);

	/*
	 *  @@ 对于定制的顶点数据的排序 --- 逆时针排序
	 *  @@ 由于离散点寻找路径的算法结果不唯一，本方法结合Melkman凸包算法进行特化
	 *  @@ 由于定制顶点位置相对有规律，故对于基准点，轴和四象限判断（-X轴，左下，Y轴，右下，X轴，右上，-Y轴，左上）
	 *  @@ TODO : 特殊情况的处理
	 */
	static void SortVertices(const TArray<FDSVerticeIndexs>& InVerticesArr, TArray<FDSVerticeIndexs>& OutVerticesArr);
	static void RecurseSort(TArray<FDSVerticeIndexs>& AlreadySortVertice, TArray<FDSVerticeIndexs> ConsiderVerticesArr);
	static void SplitVerticesToFourArea(const FDSVerticeIndexs& BaseVertice, const TArray<FDSVerticeIndexs>& ConsiderVerticesArr,
		TArray<FDSVerticeIndexs>& NegXAxisAlign, TArray<FDSVerticeIndexs>& NegXPosYArea, TArray<FDSVerticeIndexs>& PosYAxisAlign, TArray<FDSVerticeIndexs>& PosXPosYArea,
		TArray<FDSVerticeIndexs>& PosXAxisAlign, TArray<FDSVerticeIndexs>& PosXNegYArea, TArray<FDSVerticeIndexs>& NegYAxisAlign, TArray<FDSVerticeIndexs>& NegXNegYArea
	);
	static void AddNextVertice(TArray<FDSVerticeIndexs>& AlreadySortVertice, const FDSVerticeIndexs& BasePoint, TArray<FDSVerticeIndexs> SplitAreaData);

	static void MergeMeshVertices(const TArray<FVector>& InVertices, const TArray<int32>& InTriangles, const TArray<FVector>& InNormals, TArray<FVector>& OutVertices, TArray<int32>& OutTriangles, TArray<FVector>& OutNormals);
	
#pragma endregion

#pragma region MULTI_GROUP

	static bool VerifyMultiPretentClear(UDSBaseModel* NewModel);
	static bool VerifyMultiSelectToClear(UDSBaseModel* MultiMouse, UDSBaseModel* UnderMouse);

	static bool IsGizmoType(UDSBaseModel* EditModel);

	static bool IsMultiSelectType(UDSBaseModel* EditModel);
	static bool IsMultiOperator(UDSBaseModel* EditModel);
	static bool IsInMultiSelect(UDSBaseModel* EditModel);
	static bool IsGroupType(UDSBaseModel* EditModel);
	static bool IsGroupOperator(UDSBaseModel* EditModel);
	static bool IsInGroup(UDSBaseModel* EditModel);
	static bool IsGroupComponent(UDSBaseModel* EditModel);
	static bool IsMultiGroupOperatorForRevoke(UDSBaseModel* EditModel);
	static bool IsCounterTopType(UDSBaseModel* EditModel);

	static bool IsMultiIncludeInGroup(UDSBaseModel* EditModel, FString& InlcudeGroupUUID);
	static bool IsMultiIncludeInGroupNoRet(UDSBaseModel* EditModel);

	//只用于部件编辑模式下，获取同一的根
	static UDSBaseModel* GetMultiIncludeOwner(UDSBaseModel* EditModel);

	static void SyncView(UDSBaseModel* EditModel);
	static void ReleaseView(UDSBaseModel* EditModel);

#pragma endregion

#pragma region OUTLINE

	static FBox CalculateBox(const TArray<UDSBaseModel*>& Models);
	static FBox CalculateLocalBox(const FBox& WorldBox, const FTransform& ToTrans);

	static FBox CalculateLocalBox(UDSBaseModel* InModel, const FTransform& Trans, const FBox& NoRotLocalBox);

	static void CalculateLocalBoxRetCenter(
		const TArray<UDSBaseModel*>& InModels,
		const FTransform& Trans,
		FBox& OutLocalBound,
		FVector& OutCenter
	);
	static void CalculateBoxOutlineInfo(const FBox& BoundBox, FOutlineInfo& OutlineInfo);
	static void CalculateBoxOutlineInfo_Local(const FBox& LocalBoundBox, const FTransform& Trans, FOutlineInfo& OutlineInfo);
	static FOutlineInfo TransformOutLine(const FOutlineInfo& InOutLine, const FTransform& Trans);
	static void TransformOutLine_Inner(const TArray<FVector>& InOutLine, const FTransform& Trans, TArray<FVector>& OutLineRes);

	//local to world
	static void TransformOutLinePoint_Inner(const FVector& InOutLinePoint, const FTransform& Trans, FVector& OutLineResPoint);

	static TMap<UDSBaseModel*, FBox> CalculateIncludeModelLocalBoundBox(const FTransform& Trans, const TArray<UDSBaseModel*>& IncludeModel);
	static FBox CalculateIncludeModelLocalBoundBox_Inner(const FTransform& Trans, UDSBaseModel* IncludeModel);

	//
	static FBox CalculateIncludeModelLocalBoundBox_NoRot(const FTransform& Trans, UDSBaseModel* IncludeModel);

	/*
	*  @@ world bound box to local bound box
	*/
	static FBox CalculateLocalBoundBox_Inner(const FTransform& Trans, const FBox& InBoundBox);
	/*
	 * @@ local bound box to world box 
	 */
	static FBox CalculateWorldBoundBox_Inner(const FTransform& Trans, const FBox& InBoundBox);


	static FBox CalculateLocalBoundBox(const TMap<UDSBaseModel*, FBox>& IncludeLocalBoundBox);

	static FBox GetViewBoundBoxByModel(UDSBaseModel* EditModel);

	static void SyncPropertyByBoundBox(const FBox& InBoundBox, const TSharedPtr<FDSBaseProperty>& ModifyProperty);
	static void SyncPropertyByView(const ADSBaseView* EditView, const TSharedPtr<FDSBaseProperty>& ModifyProperty);
	static void SyncPropertyByModel(UDSBaseModel* EditModel);

#pragma endregion

	//Camera
	static void SetCameraLocation(const FVector& Location);

	static ECameraType GetCameraType();

	static double FormatAngleFrom0To360(const double& InAngle);
	static void FormatRotFrom0To360(FRotator& InRot);

	/*
	*  @@ 校验：2D不能选中的模型，返回校验后的模型
	*  @@ 类型：水槽、龙头、灶台、烟机。。。
	*/
	static UDSBaseModel* VerfiyModel(UDSBaseModel* EditModel, bool Is2D);

	static UDSBaseModel* VerifyGroupModel(UDSBaseModel* EditModel);

	//sink
	static void ParseDataByDatFile(const FString& InFileFolderID, TSharedPtr<FDSSinkProperty> ToParseProperty);

	//axis get relative rot
	static FRotator GetAxisRelativeRot(const FQuat& WorldQuat, const FString& InLinkUUID, const FQuat& InBaseQuat);
	//axis get new location for model[axis no center]
	static FVector GetAxisNewLocation(const FVector& InCTLoc, const double& ModelWidth, const double& ModelDepth, const FRotator& BaseRot, const FRotator& RelRot);

	//check and sync origin size
    static void CheckAndSyncOriginSize(TSharedPtr<FDSFurnitureBaseProperty> CheckProperty);
	
	static void GetCrossSectionParamsFromFile(const FString& Id, TMap<FString, FParameterData>& OutParams);
		
	/**
	 * Calculate the cross-section points from a single component file.
	 * @param Id Folder id of cross-section.
	 * @param Params Set the 'value' to the parameter named 'key'.
	 * @return the cross-section calculated according the given parameters.
	 */
	static TArray<FVector> CalculateCrossSectionPointsFromFile(const FString& Id, const TMap<FString, FString>& Params);

	//set axis component status
    static void SetAxisComponentStatus(UStaticMeshComponent*& InAxis, bool bIsVisi);

	//get OBB world 8 Point
    static TArray<FVector> GetOBBWorld8Point(const double& InW, const double& InD, const double& InH, const FTransform& InWorldTrans, bool NeedFlipH = false);
	
	//calcculate outline by OBB
    static FOutlineInfo CalculateOutlineByOBB(const FBox& RelativeBox, const FTransform& RealTrans);

	//is model Z Axis Flip
    static bool IsModelZAxisFlip(UDSBaseModel* InModel);
	
	/*
	*  @@ 获取真实状态响应的模型, 根据状态中选中类型（InSelectType）和当前选中的模型（OldModel）判断
	*/
	static void GetRealModelForHoverState(const EDSSelectType& InSelectType, UDSBaseModel* OldModel, UDSBaseModel* NewModel, UDSBaseModel*& NewParentModel);

	//求交集
	template<class  T>
	static TArray<T> GetIntersection(const TArray<TArray<T>>& InArrays)
	{
		TArray<T> Result;	
		
		//空的数组直接返回
		if(InArrays.IsEmpty()) return Result;
		for (const auto& Array : InArrays)
		{
			if(Array.IsEmpty()) return Result;
		}

		//创建Set数组
		TArray<TSet<T>> SetArray;
		for (const auto& Array : InArrays)
		{
			TSet<T> Set(Array);
			if(Set.IsEmpty()) return Result;
            SetArray.Add(MoveTemp(Set));
		}

		TSet<T> IntersectionSet = SetArray[0];
		for (int32 i = 1; i < SetArray.Num(); ++i)
		{
			IntersectionSet = IntersectionSet.Intersect(SetArray[i]);
			if (IntersectionSet.IsEmpty())
			{
				break;
			}
		}

		Result = IntersectionSet.Array();

		return Result;
	}

	//组里面是否有吸顶模型
	static bool GroupHasPlaceCeilingModel(class UDSMultiModel* InGroup);

	//组里面全是吸顶模型
	static bool GroupOnlyCeilingModel(class UDSMultiModel* InGroup);

	//单指顶面区域和吊顶
	static bool IsCeilingModel(UDSBaseModel* InModel);

	//户型门窗
	static bool IsRoomWindDoor(UDSBaseModel* InModel);

	//吸顶模型
	static bool IsPlaceCeilingModel(UDSBaseModel* InModel);

	static void GenerateModelDragDropEvent(UDSBaseModel* InModel);

	static FPointerEvent MapPointerToDispatcher(const FGeometry& InGeometry, const FPointerEvent& InPointerEvent);
	static FDragDropEvent MapDragDropToDispatcher(const FGeometry& InGeometry, const FDragDropEvent& InDragDropEvent);

	static FVector2D GetMousePositionInViewport();

	static bool IsValidDecimalValue(const FString& InValue, int32 FractionalDigits);

	static TArray<FDSStyleDefaultOption> CollectAppliedStyleDefaultOptions(const FApplyStyleData& InApplyStyleData);

	static TArray<FString> GetAllCustomCodeNames();

	//overlap
	static TArray<UDSBaseModel*> GetOverlapModels(UDSBaseModel* InModel);
	static void UpdateOverlap(UDSBaseModel* InModel, const TSet<UDSBaseModel*>& IgnoreModels = {}, bool bEffectOthers = true);
	static void UpdateOverlapInner(UDSBaseModel* InModel);
	static void UpdateUnOverlap(UDSBaseModel* InModel);
	static void UpdateUnOverlap(const TArray<UDSBaseModel*>& InModels);
	static bool IgnoreActorOverlap(UDSBaseModel* InModel);
	static bool IgnoreActorOverlap(UDSBaseModel* InModel, UDSBaseModel* OtherModel);

	static bool AnyEditableWidgetFocused();

	static FString RSA_OAEP_SHA256_Decrypt_Base64(const FString& Base64Cipher, const FString& PrivatePem);
};
