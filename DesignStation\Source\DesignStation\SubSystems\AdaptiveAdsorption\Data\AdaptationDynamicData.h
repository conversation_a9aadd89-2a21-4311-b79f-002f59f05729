// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "OrientedBoxTypes.h"
#include "AdaptationDynamicData.generated.h"


UENUM()
enum class EAdaptiveResaultType :uint8
{
	E_Succeded, //成功
	E_Intersection, //有相交
	E_LessThanMin, // 小于最小值
	E_MoreThanMax, //大于最大值
	E_WithoutAdsorption, //没有吸附对象
	E_UnkownFailed, //未知失败
};

UENUM()
enum class EIntersectionDataType :uint8
{
	E_Unknown,
	E_CupboardModel,
	E_Wall,
	E_Pillar,
	E_Drawer,
	E_Functional,
	E_CornerCutCabinet,
	E_CornerCabinet,
	E_Function_Combine
};


UENUM()
enum class EAdaptationDirection:uint8
{
	E_None = 0,
	E_Forward,
	E_Backward,
	E_Left,
	E_Right,
	E_Up,
	E_Down,
};

UENUM()
enum class ECornerType : uint8
{
	E_Left_Backward = 0, //左后切
	E_Right_Backward,	//	右后切
	E_Left_Forward,		//左前切
	E_Right_Forward,	//右前切
	E_Center_Bacckward,	//后凹切
};


class FIntersectionDynamicMesh;

USTRUCT()
struct DESIGNSTATION_API FRayHitResault
{
	GENERATED_USTRUCT_BODY()

public:
	FRayHitResault():HitPoint(FVector::ZeroVector),HitNormal(FVector::ZeroVector),Distance(0.f),HitTarget(nullptr){}

	FVector HitPoint;

	FVector HitNormal;

	double Distance;

	TSharedPtr<FIntersectionDynamicMesh> HitTarget;
};


/*
 * @@ the adaptation link model information.
 * @@ UUID -- the unique id of the model.
 * @@ Name -- the name of the model, for debug.
 * @@ Type -- model type.
 * @@ use uuid and type to find the model by MVC Subsystem.
*/

USTRUCT()
struct DESIGNSTATION_API FAdaptationLinkModelInfo
{
	GENERATED_USTRUCT_BODY()

public:
	UPROPERTY()
	FString UUID;

	UPROPERTY()
	FString Name;

	UPROPERTY()
	int32 Type;

	TWeakObjectPtr<class UDSBaseModel> OwnerModelPtr;

	FString ComponentUUID;

public:
	FAdaptationLinkModelInfo()
		: Type(0)
	{
	} ;

	FAdaptationLinkModelInfo(const FString& InUUID, const FString& InName, int32 InType, const FString& InComponentUUID = "")
		: UUID(InUUID), Name(InName), Type(InType), ComponentUUID(InComponentUUID)
	{
	};

	void Update(const FString& InUUID, const FString& InName, int32 InType, const FString& InComponentUUID = "")
	{
		UUID = InUUID;
		Name = InName;
		Type = InType;
		ComponentUUID = InComponentUUID;
	};

	bool IsValid() const { return !UUID.IsEmpty() && Type > 0; };
};




struct DESIGNSTATION_API FAdaptiveAdsorptionDenpendedTarget
{

public:
	FAdaptiveAdsorptionDenpendedTarget()
		: Direction(EAdaptationDirection::E_None)
	{
	};

	FAdaptiveAdsorptionDenpendedTarget(const FAdaptationLinkModelInfo& InLinModelInfo, EAdaptationDirection InDirection)
		: Direction(InDirection), LinkModelInfo(InLinModelInfo)
	{
	};

	FAdaptiveAdsorptionDenpendedTarget(const FAdaptationLinkModelInfo& InLinModelInfo, const EAdaptationDirection& InSourceDirection, const EAdaptationDirection& InTargetDirection)
		: Direction(InSourceDirection), TargetDirection(InTargetDirection), LinkModelInfo(InLinModelInfo)
	{
	};


	FAdaptiveAdsorptionDenpendedTarget(const FAdaptationLinkModelInfo& InLinModelInfo, const EAdaptationDirection& InSourceDirection, const FString& InNodeUUID)
		: Direction(InSourceDirection), LinkModelInfo(InLinModelInfo)
	{
	};

public:
	EAdaptationDirection Direction; //自身吸附方向
	EAdaptationDirection TargetDirection; //对象吸附方向
	FAdaptationLinkModelInfo LinkModelInfo;
};


USTRUCT()
struct DESIGNSTATION_API FAdaptiveAdsorptionResault
{
	GENERATED_USTRUCT_BODY()

public:
	FAdaptiveAdsorptionResault()
		: Owner(nullptr), SourceUUID(""), ResaultType(EAdaptiveResaultType::E_UnkownFailed), Description("")
	{
	};
	UObject* Owner;

	FString SourceUUID;

	EAdaptiveResaultType ResaultType;

	FString Description;

	TArray<FAdaptiveAdsorptionDenpendedTarget> Dependents;

	TArray<FAdaptationLinkModelInfo> Intersections;

	bool bParamterModified;
	
};

USTRUCT()
struct DESIGNSTATION_API FAdaptationEvenInfo
{
	GENERATED_USTRUCT_BODY()

public:
	UE::Geometry::FOrientedBox3d SpaceBox;

	FVector SourceExtents;

	UE::Geometry::FOrientedBox3d SourceBox;
	void AddAroundEnv(EAdaptationDirection InDirection, const TSharedPtr<FIntersectionDynamicMesh>& InEnv);

	TWeakPtr<FIntersectionDynamicMesh> GetAroundEnv(EAdaptationDirection InDirection);

	int	 MaxEvenCount = 0;

private:
	TMap<EAdaptationDirection, TWeakPtr<FIntersectionDynamicMesh>> ArounEnvs;
};


static FVector GetAdaptationVectorByDirection(const EAdaptationDirection& InDirection)
{
	switch (InDirection)
	{
	case EAdaptationDirection::E_Forward: return FVector::ForwardVector;
	case EAdaptationDirection::E_Backward: return FVector::BackwardVector;
	case EAdaptationDirection::E_Up: return FVector::UpVector;
	case EAdaptationDirection::E_Down: return FVector::DownVector;
	case EAdaptationDirection::E_Right: return FVector::RightVector;
	case EAdaptationDirection::E_Left: return FVector::LeftVector;
	case EAdaptationDirection::E_None: return FVector::LeftVector;
	default:
		return FVector::LeftVector;
	}
};





struct DESIGNSTATION_API FAdaptationData
{
	FAdaptationData() :bTransformModified(false), bExtentsModified(false)
		, bExtentsAxisXLocked(false), bExtentsAxisYLocked(false), bExtentsAxisZLocked(false) {
	};
	virtual ~FAdaptationData(){};

public:
	virtual void DeepCopy(TSharedPtr<FAdaptationData> NewAdaptationData);

	virtual bool OnlyTransformModified();
	virtual bool HasModified();
	
	virtual bool HasLocked();



public:
	bool bTransformModified;
	bool bExtentsModified;


	bool bExtentsAxisXLocked;

	bool bExtentsAxisYLocked;

	bool bExtentsAxisZLocked;


	UE::Geometry::FOrientedBox3d OriBox;

	UPROPERTY()
	FAdaptationLinkModelInfo HitLinkModelInfo;

	TArray<FAdaptiveAdsorptionDenpendedTarget> AdaptationDependentTargets;
};


struct DESIGNSTATION_API FCupboardAdaptationData :public FAdaptationData
{
	FCupboardAdaptationData() :FAdaptationData(), AdaptationDir(EAdaptationDirection::E_None){};
	virtual ~FCupboardAdaptationData() {};
public:
	virtual void DeepCopy(TSharedPtr<FAdaptationData> NewAdaptationData) override;
	virtual bool OnlyTransformModified() override;
	virtual bool HasModified() override;

	virtual bool CheckLockedState(EAdaptationDirection InNewAdaptationDir);

	virtual bool CheckLockedState(const FAdaptationLinkModelInfo& InHitLinkModelInfo);


public:
	double DistanceToFloor;
	EAdaptationDirection AdaptationDir;

};


struct DESIGNSTATION_API FCornerCutCupboardAdaptationData :public FCupboardAdaptationData
{
	FCornerCutCupboardAdaptationData() :FCupboardAdaptationData(),bParamtersModified(false)
	,bCornerSizeAxisXLocked(false),bCornerSizeAxisYLocked(false),bCornerSizeAxisZLocked(false)
	,bCornerLocationAxisXLocked(false),bCornerLocationAxisYLocked(false){};
	virtual ~FCornerCutCupboardAdaptationData() {};
public:
	virtual void DeepCopy(TSharedPtr<FAdaptationData> NewAdaptationData)override;

	virtual bool OnlyTransformModified() override;
	virtual bool HasModified() override;

	virtual bool HasLocked() override;

	virtual bool CheckLockedState(EAdaptationDirection InNewAdaptationDir)override;


	virtual bool CheckLockedState(const FAdaptationLinkModelInfo& InHitLinkModelInfo);
public:
	bool bParamtersModified;

	bool bCornerSizeAxisXLocked;

	bool bCornerSizeAxisYLocked;
	
	bool bCornerSizeAxisZLocked;

	bool bCornerLocationAxisXLocked;

	bool bCornerLocationAxisYLocked;

	FVector CornerSize; //切角大小
	FVector2D CornerLocation;

	//目标切角Size
	FVector TargetCornerSize;

	FVector TargetPillarOriBox;
};


struct DESIGNSTATION_API FFunctionalAdaptationData :public FAdaptationData
{
	FFunctionalAdaptationData() :FAdaptationData() ,bDependent(false){
	};
	virtual ~FFunctionalAdaptationData() {};
public:
	bool bDependent;
};