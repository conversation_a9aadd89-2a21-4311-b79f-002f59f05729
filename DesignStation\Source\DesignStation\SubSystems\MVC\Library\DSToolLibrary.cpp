﻿// Fill out your copyright notice in the Description page of Project Settings.

#include "DSToolLibrary.h"

#include "SMultiLineEditableText.h"
#include "Blueprint/SlateBlueprintLibrary.h"
#include "BasicClasses/DesignStationController.h"
#include "BasicClasses/CameraPawn.h"
#include "ImageProcess/Public/ImageProcess.h"
#include "Kismet/KismetSystemLibrary.h"
#include "SubSystems/MVC/Core/Macro.h"
#include "SubSystems/MVC/View/DSBaseView.h"
#include "SubSystems/MVC/Model/DSBaseModel.h"
#include "Subsystems/MVC/Core/Property/HouseAreaProperty.h"
#include "Subsystems/MVC/Core/Property/DoorAndWindowProperty.h"
#include "Runtime/Core/Public/UObject/NameTypes.h"
#include "SubSystems/MVC/Core/Property/PillarProperty.h"
#include "Clipper2/Library/Clipper2Library.h"
#include "Geometry/DataDefines/GeometryDatas.h"
#include "Subsystems/ModelDependencySubsystem/DSModelDependencySubsystem.h"
#include "SubSystems/ModelDependencySubsystem/Structures/SideCounterTopDependencyInfo.h"
#include "Subsystems/MVC/Core/Property/CounterTopLineProperty.h"
#include "Subsystems/MVC/Core/Property/CounterTopPointProperty.h"
#include "SubSystems/MVC/Core/Property/FurnitureProperty.h"
#include "SubSystems/MVC/Core/Property/SideCounterTopProperty.h"
#include "SubSystems/MVC/Model/DoorAndWindow/DSDoorAndWindowBaseModel.h"
#include "SubSystems/MVC/Model/Group/DSGroupModel.h"
#include "SubSystems/MVC/Model/Group/DSMultiModel.h"
#include "SubSystems/MVC/Model/House/Area/DSHouseAreaModel.h"
#include "SubSystems/MVC/View/Custom/DSCupboardBaseView.h"
#include "Subsystems/MVC/View/Gizmo/DSGizmoView.h"
#include "SubSystems/UI/DSUISubsystem.h"
#include "SubSystems/File/Core/ProtoDataConvert/ProtobufOperatorFunctionLibrary.h"
#include "Subsystems/MVC/DSMVCSubsystem.h"
#include "Subsystems/MVC/CatalogSupport/Library/RefRelationFunction.h"
#include "Subsystems/MVC/Core/Property/SinkProperty.h"
#include "Subsystems/MVC/Core/Property/StoveProperty.h"
#include "Subsystems/MVC/Core/Property/RangeHoodProperty.h"
#include "SubSystems/MVC/StateMachine/DragDropOps/DSModelDragDropOperation.h"
#include "SubSystems/MVC/View/House/Roof/DSCeilingAreaView.h"
#include "SubSystems/MVC/View/Furniture/MoldingFurniture/DSMoldingCeilingView.h"
#include "SubSystems/MVC/View/Furniture/MoldingFurniture/DSSoftFurnitureView.h"
#include "Runtime/Engine/Classes/Engine/GameViewportClient.h"
#include "Runtime/Slate/Public/Widgets/SViewport.h"
#include "Misc/Base64.h"

#define UI UI_ST
THIRD_PARTY_INCLUDES_START
#include "openssl/evp.h"
#include "openssl/pem.h"
#include "openssl/bio.h"
THIRD_PARTY_INCLUDES_END
#undef UI

class FDSSoftFurnitureProperty;
extern const FString MT_OpacityRef;
extern const FString MT_ColorRef;
extern const FString MT_WhiteRef;

extern const TArray<EDSModelType> NoBroadcastNoModifyType;

#define INDENT_CIRCLE 0.02
#define POLYGON_LEAST_CONVEX_NUM 4

void UDSToolLibrary::GenerateMesh(UProceduralMeshComponent*& MeshComponent, const FDSFileSourceProperty& FileSource)
{
	if (FileSource.SourceType == EDSSourceType::E_FBX)
	{
		//ULoadMeshBPLibrary::LoadMeshToProceduralMesh(MeshComponent, FileSource.SourcePath, FTransform(), EPathType::E_Relative);
	}
}

UDSBaseModel* UDSToolLibrary::CopyDataExecute(UDSBaseModel* EditModel)
{
	UDSBaseModel* Res = nullptr;
	if (DS_MODEL_VALID_FOR_USE(EditModel))
	{
		UDSBaseModel* ActualCopyModel = EditModel;
		if (IsMultiSelectType(EditModel) || IsInMultiSelect(EditModel))
		{
			if (IsInMultiSelect(EditModel))
			{
				ActualCopyModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(EDSModelType::E_MultiSelect, EditModel->GetMultiUUID());
			}
			Res = CopyMultiSelectData(ActualCopyModel);
		}
		else if (IsGroupType(EditModel) || IsInGroup(EditModel))
		{
			if (IsInGroup(EditModel))
			{
				ActualCopyModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(EDSModelType::E_Group, EditModel->GetGroupUUID());
			}
			Res = CopyGroupData(ActualCopyModel);
		}
		else
		{
			Res = CopyGenerateData(ActualCopyModel);
		}
	}
	return Res;
}

UDSBaseModel* UDSToolLibrary::CopyMultiSelectData(UDSBaseModel* EditModel)
{
	UDSMultiModel* Res = nullptr;
	if (DS_MODEL_VALID_FOR_USE(EditModel) && EditModel->GetModelType() == EDSModelType::E_MultiSelect)
	{
		//copy include model
		TArray<UDSBaseModel*> CopyIncludeModel;
		for (auto& IM : Cast<UDSMultiModel>(EditModel)->GetIncludeModel())
		{
			if (!DS_MODEL_VALID_FOR_USE(IM))
			{
				continue;
			}

			UDSBaseModel* CopyIM = CopyGenerateData(IM);
			if (CopyIM != nullptr)
			{
				CopyIncludeModel.AddUnique(CopyIM);
			}
		}
		//clear old multi select model
		Cast<UDSMultiModel>(EditModel)->ClearMultiSelect();

		//copy multi select model
		Res = UDSMultiModel::CreateMultiModel();
		Res->AddSelect(CopyIncludeModel);
	}
	return Res;
}

UDSBaseModel* UDSToolLibrary::CopyGroupData(UDSBaseModel* EditModel)
{
	UDSGroupModel* Res = nullptr;
	if (DS_MODEL_VALID_FOR_USE(EditModel) && EditModel->GetModelType() == EDSModelType::E_Group)
	{
		//copy include model
		TArray<UDSBaseModel*> CopyIncludeModel;
		for (auto& IM : Cast<UDSGroupModel>(EditModel)->GetIncludeModel())
		{
			if (!DS_MODEL_VALID_FOR_USE(IM))
			{
				continue;
			}

			UDSBaseModel* CopyIM = CopyGenerateData(IM);
			if (CopyIM != nullptr)
			{
				CopyIncludeModel.AddUnique(CopyIM);
			}
		}

		//copy group model
		Res = UDSGroupModel::CreateGroupModel();
		Res->AddItem(CopyIncludeModel, false);
	}

	return Res;
}

UDSBaseModel* UDSToolLibrary::CopyGenerateData(UDSBaseModel* EditModel)
{
	UDSBaseModel* Res = nullptr;
	if (DS_MODEL_VALID_FOR_USE(EditModel))
	{
		Res = EditModel->OnCopy();
		//Res->OnExecuteAction(FDSModelExecuteType::ExecuteSelect);    //卢春锋注释：为什么这里要执行选择操作？会导致Ctrl-V复制新的模型没有Gizmo轴。
		//FBox BoundBox = EditModel->GetBoundBox();
		//FVector RightShift = FVector::RightVector * (BoundBox.Max.X - BoundBox.Min.X);
		//Res->GetPropertySharedPtr()->AddDeltaTransform(FTransform(RightShift));

		//UDSMVCSubsystem::GetInstance()->OnGenerateView_NeedConfirm(Res, ADesignStationController::Get()->GetCurrentMouseData(), false, false);
	}

	return Res;
}

ADSBaseView* UDSToolLibrary::GetActorUnderMouse(const FDSMouseData& InMouseData)
{
	TArray<AActor*> IgnoreActor;
	TArray<FHitResult> OutHits;
	bool IsHit = UKismetSystemLibrary::LineTraceMulti(
		GWorld, InMouseData.MouseWorldPos, InMouseData.MouseWorldPos + InMouseData.MouseDir * 1000000.0f, ETraceTypeQuery::TraceTypeQuery3 /*UEngineTypes::ConvertToTraceType(ECollisionChannel::ECC_Visibility)*/
		, true, IgnoreActor, EDrawDebugTrace::Type::None, OutHits, true, FLinearColor::Red, FLinearColor::Green, 1.0f
	);


	for (auto& HitIter : OutHits)
	{
		ADSBaseView* DSActor = Cast<ADSBaseView>(HitIter.GetActor());
		auto OwnerActor = HitIter.GetActor();
		while (OwnerActor && OwnerActor->GetOwner()
			&& (OwnerActor->ActorHasTag(FName(TEXT("Cmp"))) || OwnerActor->ActorHasTag(FName(TEXT("BP"))))
		)
		{
			OwnerActor = OwnerActor->GetOwner();
		}
		if (OwnerActor)
		{
			ADSBaseView* ScaleActor = Cast<ADSBaseView>(OwnerActor);
			if (OBJECT_VALID_FOR_USE(DSActor) && (DSActor->GetModelType() == EDSModelType::E_Scale2D || DSActor->GetModelType() == EDSModelType::E_Scale2DForPath))
			{
				return ScaleActor;
			}
		}
	}

	OutHits.Sort([](const FHitResult& A, const FHitResult& B) { return A.Distance < B.Distance; });

	for (auto& HitIter : OutHits)
	{
		ADSBaseView* DSActor = Cast<ADSBaseView>(HitIter.GetActor());
		auto OwnerActor = HitIter.GetActor();

		while (OwnerActor && OwnerActor->GetOwner() && OwnerActor->ActorHasTag(FName(TEXT("Cmp"))))
		{
			OwnerActor = OwnerActor->GetOwner();
		}

		if (OwnerActor)
		{
			ADSBaseView* DSActor = Cast<ADSBaseView>(OwnerActor);
			if (OBJECT_VALID_FOR_USE(DSActor))
			{
				return DSActor;
			}
		}
	}

	return nullptr;
}

UDSBaseModel* UDSToolLibrary::GetModelUnderMouse(const FDSMouseData& InMouseData)
{
	TArray<AActor*> IgnoreActor;
	TArray<FHitResult> OutHits;
	bool IsHit = UKismetSystemLibrary::LineTraceMulti(
		GWorld, InMouseData.MouseWorldPos, InMouseData.MouseWorldPos + InMouseData.MouseDir * 1000000.0f,
		/*ETraceTypeQuery::TraceTypeQuery3*/ UEngineTypes::ConvertToTraceType(ECollisionChannel::ECC_Visibility)
		, true, IgnoreActor, EDrawDebugTrace::Type::None, OutHits, true, FLinearColor::Red, FLinearColor::Green, 1.0f
	);


	OutHits.Sort([](const FHitResult& A, const FHitResult& B) { return A.Distance < B.Distance; });

	TArray<UDSBaseModel*> IntersectedModels;

	for (auto& HitIter : OutHits)
	{
		auto OwnerActor = HitIter.GetActor();
		if (OwnerActor->ActorHasTag(FName(TEXT("BP"))) || OwnerActor->GetFName().ToString().Contains((TEXT("BP"))))
		{
			while (OwnerActor->GetOwner())
			{
				OwnerActor = OwnerActor->GetOwner();
			}
		}


		ADSBaseView* DSActor = Cast<ADSBaseView>(OwnerActor);
		if (DSActor == nullptr)
		{
			continue;
		}

		UDSBaseModel* CurrentModel = DSActor->GetModel();
		if (CurrentModel == nullptr)
		{
			continue;
		}

		while (CurrentModel->ShouldUseParentModel())
		{
			UDSBaseModel* ParentModel = CurrentModel->GetOwnerModel();
			if (ParentModel == nullptr)
			{
				break;
			}
			CurrentModel = ParentModel;
		}

		IntersectedModels.Add(CurrentModel);
	}

	//顶面模式做特殊处理,如果有多个hit,且第一个是顶面区域,第二个是吸顶模型或者吊顶,则返回第二个
	ECameraType CameraType = GetCameraType();
	if (ECameraType::EXYPlan2D_Ceil == CameraType && IntersectedModels.Num() > 1 && IntersectedModels[0]->GetModelType() == EDSModelType::E_RoofArea)
	{
		if (IsPlaceCeilingModel(IntersectedModels[1]))
		{
			return IntersectedModels[1];
		}
	}

	// Find scale.
	int32 PriorModelPos = IntersectedModels.IndexOfByPredicate([&](UDSBaseModel* InModel)
	{
		return InModel->GetModelType() == EDSModelType::E_Scale2D || InModel->GetModelType() == EDSModelType::E_Scale2DForPath;
	});

	if (PriorModelPos != INDEX_NONE)
	{
		return IntersectedModels[PriorModelPos];
	}

	// Find door and window.
	PriorModelPos = IntersectedModels.IndexOfByPredicate([&](UDSBaseModel* InModel)
	{
		return InModel->GetModelType() == EDSModelType::E_House_Window || InModel->GetModelType() == EDSModelType::E_House_Door;
	});

	if (PriorModelPos != INDEX_NONE)
	{
		return IntersectedModels[PriorModelPos];
	}

	//Find Area Label
	PriorModelPos = IntersectedModels.IndexOfByPredicate([&](UDSBaseModel* InModel)
	{
		return InModel->GetModelType() == EDSModelType::E_House_Area_Label;
	});

	if (PriorModelPos != INDEX_NONE)
	{
		return IntersectedModels[PriorModelPos];
	}

	return IntersectedModels.IsValidIndex(0) ? IntersectedModels[0] : nullptr;
}

UDSBaseModel* UDSToolLibrary::GetModelUnderMouseWithHitPoint(const FDSMouseData& InMouseData, FVector& HitPoint)
{
	TArray<AActor*> IgnoreActor;
	TArray<FHitResult> OutHits;
	bool IsHit = UKismetSystemLibrary::LineTraceMulti(
		GWorld, InMouseData.MouseWorldPos, InMouseData.MouseWorldPos + InMouseData.MouseDir * 1000000.0f, /*ETraceTypeQuery::TraceTypeQuery3*/ UEngineTypes::ConvertToTraceType(ECollisionChannel::ECC_Visibility)
		, true, IgnoreActor, EDrawDebugTrace::Type::None, OutHits, true, FLinearColor::Red, FLinearColor::Green, 1.0f
	);

	for (auto& HitIter : OutHits)
	{
		if (HitIter.GetActor()->IsA<ADSGizmoView>())
		{
			ADSBaseView* DSActor = Cast<ADSBaseView>(HitIter.GetActor());
			if (OBJECT_VALID_FOR_USE(DSActor))
			{
				HitPoint = HitIter.ImpactPoint;
				return DSActor->GetModel();
			}
		}
	}

	FHitResult GizmoHitResult;
	UKismetSystemLibrary::LineTraceSingle(
			GWorld, InMouseData.MouseWorldPos, InMouseData.MouseWorldPos + InMouseData.MouseDir * 1000000.0f, UEngineTypes::ConvertToTraceType(ECC_GameTraceChannel2)
			, true, IgnoreActor, EDrawDebugTrace::Type::None, GizmoHitResult, true, FLinearColor::Red, FLinearColor::Green, 1.0f
		);

	if (GizmoHitResult.bBlockingHit)
	{
		ADSGizmoView* DSActor = Cast<ADSGizmoView>(GizmoHitResult.GetActor());
		if (OBJECT_VALID_FOR_USE(DSActor) && DSActor->IsProcessingModel())
		{
			HitPoint = GizmoHitResult.ImpactPoint;
			return DSActor->GetModel();
		}
	}

	OutHits.Sort([](const FHitResult& A, const FHitResult& B) { return A.Distance < B.Distance; });

	//顶面模式做特殊处理,如果有多个hit,且第一个是顶面区域,第二个是吸顶模型或者吊顶,则返回第二个
	ECameraType CameraType = GetCameraType();
	if (ECameraType::EXYPlan2D_Ceil == CameraType && OutHits.Num() > 1 && OutHits[0].GetActor()->IsA<ADSCeilingAreaView>())
	{
		if (OutHits[1].GetActor()->IsA<ADSMoldingCeilingView>() ||
			OutHits[1].GetActor()->IsA<ADSSoftFurnitureView>())
		{
			ADSBaseView* DSActor = Cast<ADSBaseView>(OutHits[1].GetActor());
			if (OBJECT_VALID_FOR_USE(DSActor) && IsPlaceCeilingModel(DSActor->GetModel()))
			{
				HitPoint = OutHits[1].ImpactPoint;
				return DSActor->GetModel();
			}
		}
	}

	TArray<UDSBaseModel*> HitModels;
	//get scale point
	for (auto& HitIter : OutHits)
	{
		auto OwnerActor = HitIter.GetActor();
		if (OwnerActor->ActorHasTag(FName(TEXT("Cmp"))) || OwnerActor->ActorHasTag(FName(TEXT("BP"))))
		{
			while (OwnerActor->GetOwner())
			{
				OwnerActor = OwnerActor->GetOwner();
			}
		}

		if (OwnerActor == nullptr)
		{
			continue;
		}

		ADSBaseView* DSActor = Cast<ADSBaseView>(OwnerActor);
		if (DSActor == nullptr)
		{
			continue;
		}

		UDSBaseModel* CurrentModel = DSActor->GetModel();
		if (CurrentModel == nullptr)
		{
			continue;
		}

		if (CurrentModel->GetModelType() == EDSModelType::E_Scale2D || CurrentModel->GetModelType() == EDSModelType::E_Scale2DForPath)
		{
			HitPoint = HitIter.ImpactPoint;
			HitModels.Add(CurrentModel);
		}
		else
		{
			while (CurrentModel->ShouldUseParentModel())
			{
				UDSBaseModel* ParentModel = CurrentModel->GetOwnerModel();
				if (ParentModel == nullptr)
				{
					break;
				}
				CurrentModel = ParentModel;
			}

			HitPoint = HitIter.ImpactPoint;
			HitModels.Add(CurrentModel);
		}
	}
	if (HitModels.Num() > 0)
	{
		for (auto& Iter : HitModels)
		{
			if (Iter->GetModelType() == EDSModelType::E_Scale2D || Iter->GetModelType() == EDSModelType::E_Scale2DForPath)
			{
				return Iter;
			}
		}
		return HitModels[0];
	}
	return nullptr;
}

UDSBaseModel* UDSToolLibrary::GetModelUnderMouseWithHitPoint_NoGetOwner(const FDSMouseData& InMouseData, FVector& HitPoint)
{
	TArray<AActor*> IgnoreActor;
	TArray<FHitResult> OutHits;
	bool IsHit = UKismetSystemLibrary::LineTraceMulti(
		GWorld, InMouseData.MouseWorldPos, InMouseData.MouseWorldPos + InMouseData.MouseDir * 1000000.0f, /*ETraceTypeQuery::TraceTypeQuery3*/ UEngineTypes::ConvertToTraceType(ECollisionChannel::ECC_Visibility)
		, true, IgnoreActor, EDrawDebugTrace::Type::None, OutHits, true, FLinearColor::Red, FLinearColor::Green, 1.0f
	);

	for (auto& HitIter : OutHits)
	{
		if (HitIter.GetActor()->IsA(ADSGizmoView::StaticClass()))
		{
			ADSBaseView* DSActor = Cast<ADSBaseView>(HitIter.GetActor());
			if (OBJECT_VALID_FOR_USE(DSActor))
			{
				HitPoint = HitIter.ImpactPoint;
				return DSActor->GetModel();
			}
		}
	}

	FHitResult GizmoHitResult;
	UKismetSystemLibrary::LineTraceSingle(
			GWorld, InMouseData.MouseWorldPos, InMouseData.MouseWorldPos + InMouseData.MouseDir * 1000000.0f, UEngineTypes::ConvertToTraceType(ECC_GameTraceChannel2)
			, true, IgnoreActor, EDrawDebugTrace::Type::None, GizmoHitResult, true, FLinearColor::Red, FLinearColor::Green, 1.0f
		);

	if (GizmoHitResult.bBlockingHit)
	{
		ADSGizmoView* DSActor = Cast<ADSGizmoView>(GizmoHitResult.GetActor());
		if (OBJECT_VALID_FOR_USE(DSActor) && DSActor->IsProcessingModel())
		{
			HitPoint = GizmoHitResult.ImpactPoint;
			return DSActor->GetModel();
		}
	}

	OutHits.Sort([](const FHitResult& A, const FHitResult& B) { return A.Distance < B.Distance; });

	TArray<UDSBaseModel*> HitModels;
	//get scale point
	for (auto& HitIter : OutHits)
	{
		auto OwnerActor = HitIter.GetActor();
		if (OwnerActor->ActorHasTag(FName(TEXT("Cmp"))) || OwnerActor->ActorHasTag(FName(TEXT("BP"))))
		{
			while (OwnerActor->GetOwner())
			{
				OwnerActor = OwnerActor->GetOwner();
			}
		}

		if (OwnerActor == nullptr)
		{
			continue;
		}

		ADSBaseView* DSActor = Cast<ADSBaseView>(OwnerActor);
		if (DSActor == nullptr)
		{
			continue;
		}

		UDSBaseModel* CurrentModel = DSActor->GetModel();
		if (CurrentModel == nullptr)
		{
			continue;
		}

		HitPoint = HitIter.ImpactPoint;
		HitModels.Add(CurrentModel);
	}
	if (HitModels.Num() > 0)
	{
		for (auto& Iter : HitModels)
		{
			if (Iter->GetModelType() == EDSModelType::E_Scale2D || Iter->GetModelType() == EDSModelType::E_Scale2DForPath)
			{
				return Iter;
			}
		}
		return HitModels[0];
	}
	return nullptr;
}

UDSBaseModel* UDSToolLibrary::GetModelUnderMouse(UObject* WorldContextObject, const FVector2D& MouseScreenPos, FHitResult& HitResult, const TArray<EDSModelType>& InTypeFilters)
{
	UWorld* World = WorldContextObject->GetWorld();
	if (World == nullptr)
	{
		return nullptr;
	}

	FVector2D MousePixelPos, MouseViewportPos;
	USlateBlueprintLibrary::AbsoluteToViewport(World, MouseScreenPos, MousePixelPos, MouseViewportPos);

	FVector MousePos, MouseDir;
	if (APlayerController* Controller = World->GetFirstPlayerController())
	{
		Controller->DeprojectScreenPositionToWorld(MousePixelPos.X, MousePixelPos.Y, MousePos, MouseDir);
	}

	TArray<AActor*> IgnoreActor;
	TArray<FHitResult> OutHits;

	UKismetSystemLibrary::SphereTraceMulti(
		GWorld, MousePos, MousePos + MouseDir * 1000000.0f, 5.f, UEngineTypes::ConvertToTraceType(ECollisionChannel::ECC_Visibility)
		, true, IgnoreActor, EDrawDebugTrace::Type::None, OutHits, true, FLinearColor::Red, FLinearColor::Green, 1.0f
	);

	OutHits.Sort([](const FHitResult& A, const FHitResult& B) { return A.Distance < B.Distance; });

	for (const FHitResult& HitIt : OutHits)
	{
		auto OwnerActor = HitIt.GetActor();
		if (OwnerActor->ActorHasTag(FName(TEXT("Cmp"))))
		{
			while (OwnerActor->GetOwner() != nullptr)
			{
				OwnerActor = OwnerActor->GetOwner();
			}
		}

		ADSBaseView* DSActor = Cast<ADSBaseView>(OwnerActor);
		if (OBJECT_VALID_FOR_USE(DSActor))
		{
			UDSBaseModel* CurrentModel = DSActor->GetModel();
			if (CurrentModel != nullptr && InTypeFilters.Contains(CurrentModel->GetModelType()))
			{
				while (CurrentModel->ShouldUseParentModel())
				{
					UDSBaseModel* ParentModel = CurrentModel->GetOwnerModel();
					if (ParentModel == nullptr)
					{
						break;
					}
					CurrentModel = ParentModel;
				}

				if (InTypeFilters.Contains(CurrentModel->GetModelType()))
				{
					HitResult = HitIt;
					return CurrentModel;
				}
			}
		}
	}

	return nullptr;
}

ADSBaseView* UDSToolLibrary::GetViewUnderMouseByName(const FString& InName, const FVector& WorldLoc, const FVector& WorldDir)
{
	TArray<AActor*> IgnoreActor;
	TArray<FHitResult> OutHits;
	//bool IsHit = UKismetSystemLibrary::LineTraceMulti(
	//	GWorld, MousePos - MouseDir * 1000000.0f, MousePos + MouseDir * 1000000.0f, ETraceTypeQuery::TraceTypeQuery3/*UEngineTypes::ConvertToTraceType(ECollisionChannel::ECC_Visibility)*/
	//	, true, IgnoreActor, EDrawDebugTrace::Type::None, OutHits, true, FLinearColor::Red, FLinearColor::Green, 1.0f
	//);

	bool IsHit = UKismetSystemLibrary::SphereTraceMulti(
		GWorld, WorldLoc, WorldLoc + WorldDir * 1000000.0f, 5.f, ETraceTypeQuery::TraceTypeQuery3 /*UEngineTypes::ConvertToTraceType(ECollisionChannel::ECC_Visibility)*/
		, true, IgnoreActor, EDrawDebugTrace::Type::None, OutHits, true, FLinearColor::Red, FLinearColor::Green, 1.0f
	);

	OutHits.Sort([](const FHitResult& A, const FHitResult& B) { return A.Distance < B.Distance; });

	for (const FHitResult& HitIt : OutHits)
	{
		auto OwnerActor = HitIt.GetActor();

		while (OwnerActor)
		{
			auto DSActor = Cast<ADSBaseView>(OwnerActor);
			if (Cast<ADSBaseView>(OwnerActor))
			{
				auto CurrentModel = DSActor->GetModel();
				if (CurrentModel->GetProperty()->ProductProperty.Name.Contains(InName))
				{
					return DSActor;
				}
			}
			OwnerActor = OwnerActor->GetOwner();
		}
	}
	return nullptr;
}

ADSBaseView* UDSToolLibrary::GetViewUnderMouseByName(const TArray<FString>& InNames, const FVector& WorldLoc, const FVector& WorldDir)
{
	TArray<AActor*> IgnoreActor;
	TArray<FHitResult> OutHits;
	//bool IsHit = UKismetSystemLibrary::LineTraceMulti(
	//	GWorld, MousePos - MouseDir * 1000000.0f, MousePos + MouseDir * 1000000.0f, ETraceTypeQuery::TraceTypeQuery3/*UEngineTypes::ConvertToTraceType(ECollisionChannel::ECC_Visibility)*/
	//	, true, IgnoreActor, EDrawDebugTrace::Type::None, OutHits, true, FLinearColor::Red, FLinearColor::Green, 1.0f
	//);

	bool IsHit = UKismetSystemLibrary::SphereTraceMulti(
		GWorld, WorldLoc, WorldLoc + WorldDir * 1000000.0f, 5.f, ETraceTypeQuery::TraceTypeQuery3 /*UEngineTypes::ConvertToTraceType(ECollisionChannel::ECC_Visibility)*/
		, true, IgnoreActor, EDrawDebugTrace::Type::None, OutHits, true, FLinearColor::Red, FLinearColor::Green, 1.0f
	);

	OutHits.Sort([](const FHitResult& A, const FHitResult& B) { return A.Distance < B.Distance; });

	for (const FHitResult& HitIt : OutHits)
	{
		auto OwnerActor = HitIt.GetActor();

		while (OwnerActor)
		{
			auto DSActor = Cast<ADSBaseView>(OwnerActor);
			if (Cast<ADSBaseView>(OwnerActor))
			{
				auto CurrentModel = DSActor->GetModel();
				for (auto& Name : InNames)
				{
					if (CurrentModel->GetProperty()->ProductProperty.Name.Contains(Name))
					{
						return DSActor;
					}
				}
			}
			OwnerActor = OwnerActor->GetOwner();
		}
	}
	return nullptr;
}

ADSBaseView* UDSToolLibrary::GetComponentActorUnderMouse(const FDSMouseData& InMouseData)
{
	TArray<AActor*> IgnoreActor;
	TArray<FHitResult> OutHits;
	//bool IsHit = UKismetSystemLibrary::LineTraceMulti(
	//	GWorld, MousePos - MouseDir * 1000000.0f, MousePos + MouseDir * 1000000.0f, ETraceTypeQuery::TraceTypeQuery3/*UEngineTypes::ConvertToTraceType(ECollisionChannel::ECC_Visibility)*/
	//	, true, IgnoreActor, EDrawDebugTrace::Type::None, OutHits, true, FLinearColor::Red, FLinearColor::Green, 1.0f
	//);

	bool IsHit = UKismetSystemLibrary::SphereTraceMulti(
		GWorld, InMouseData.MouseWorldPos, InMouseData.MouseWorldPos + InMouseData.MouseDir * 1000000.0f, 5.f, /*ETraceTypeQuery::TraceTypeQuery3*/ UEngineTypes::ConvertToTraceType(ECollisionChannel::ECC_Visibility)
		, true, IgnoreActor, EDrawDebugTrace::Type::None, OutHits, true, FLinearColor::Red, FLinearColor::Green, 1.0f
	);

	OutHits.Sort([](const FHitResult& A, const FHitResult& B) { return A.Distance < B.Distance; });

	for (auto& HitIter : OutHits)
	{
		ADSBaseView* DSActor = Cast<ADSBaseView>(HitIter.GetActor());
		auto OwnerActor = HitIter.GetActor();

		while (OwnerActor && OwnerActor->GetOwner() && OwnerActor->GetOwner()->ActorHasTag(FName(TEXT("Cmp"))))
		{
			OwnerActor = OwnerActor->GetOwner();
		}

		if (OwnerActor)
		{
			ADSBaseView* DSActor = Cast<ADSBaseView>(OwnerActor);
			if (OBJECT_VALID_FOR_USE(DSActor))
			{
				return DSActor;
			}
		}
	}
	return nullptr;
}

UDSBaseModel* UDSToolLibrary::GetComponentModelUnderMouse(const FDSMouseData& InMouseData)
{
	TArray<AActor*> IgnoreActor;
	TArray<FHitResult> OutHits;

	bool IsHit = UKismetSystemLibrary::SphereTraceMulti(
		GWorld, InMouseData.MouseWorldPos, InMouseData.MouseWorldPos + InMouseData.MouseDir * 1000000.0f, 5.f, ETraceTypeQuery::TraceTypeQuery3 /*UEngineTypes::ConvertToTraceType(ECollisionChannel::ECC_Visibility)*/
		, true, IgnoreActor, EDrawDebugTrace::Type::None, OutHits, true, FLinearColor::Red, FLinearColor::Green, 1.0f
	);

	OutHits.Sort([](const FHitResult& A, const FHitResult& B) { return A.Distance < B.Distance; });

	for (auto& HitIter : OutHits)
	{
		ADSBaseView* DSActor = Cast<ADSBaseView>(HitIter.GetActor());
		auto OwnerActor = HitIter.GetActor();

		while (OwnerActor && OwnerActor->GetOwner() && OwnerActor->GetOwner()->ActorHasTag(FName(TEXT("Cmp"))))
		{
			OwnerActor = OwnerActor->GetOwner();
		}

		if (OwnerActor)
		{
			ADSBaseView* DSOwnerActor = Cast<ADSBaseView>(OwnerActor);
			if (OBJECT_VALID_FOR_USE(DSOwnerActor) && DS_MODEL_VALID_FOR_USE(DSOwnerActor->GetModel()))
			{
				return DSOwnerActor->GetModel();
			}
		}
	}
	return nullptr;
}

UDSBaseModel* UDSToolLibrary::GetComponentModelUnderMouseWithHitPoint(const FDSMouseData& InMouseData, FVector& HitPoint)
{
	TArray<AActor*> IgnoreActor;
	TArray<FHitResult> OutHits;
	//bool IsHit = UKismetSystemLibrary::LineTraceMulti(
	//	GWorld, MousePos - MouseDir * 1000000.0f, MousePos + MouseDir * 1000000.0f, ETraceTypeQuery::TraceTypeQuery3/*UEngineTypes::ConvertToTraceType(ECollisionChannel::ECC_Visibility)*/
	//	, true, IgnoreActor, EDrawDebugTrace::Type::None, OutHits, true, FLinearColor::Red, FLinearColor::Green, 1.0f
	//);

	bool IsHit = UKismetSystemLibrary::SphereTraceMulti(
		GWorld, InMouseData.MouseWorldPos, InMouseData.MouseWorldPos + InMouseData.MouseDir * 1000000.0f, 5.f, ETraceTypeQuery::TraceTypeQuery3 /*UEngineTypes::ConvertToTraceType(ECollisionChannel::ECC_Visibility)*/
		, true, IgnoreActor, EDrawDebugTrace::Type::None, OutHits, true, FLinearColor::Red, FLinearColor::Green, 1.0f
	);

	OutHits.Sort([](const FHitResult& A, const FHitResult& B) { return A.Distance < B.Distance; });

	for (auto& HitIter : OutHits)
	{
		ADSBaseView* DSActor = Cast<ADSBaseView>(HitIter.GetActor());
		auto OwnerActor = HitIter.GetActor();

		while (OwnerActor && OwnerActor->GetOwner() && OwnerActor->GetOwner()->ActorHasTag(FName(TEXT("Cmp"))))
		{
			OwnerActor = OwnerActor->GetOwner();
		}

		if (OwnerActor)
		{
			DSActor = Cast<ADSBaseView>(OwnerActor);
			if (OBJECT_VALID_FOR_USE(DSActor))
			{
				HitPoint = HitIter.ImpactPoint;
				return DSActor->GetModel();
			}
		}
	}
	return nullptr;
}

UDSBaseModel* UDSToolLibrary::GetRootOwner(UDSBaseModel* InModel)
{
	UDSBaseModel* RootOwner = InModel;
	while (RootOwner->GetOwnerModel())
	{
		RootOwner = RootOwner->GetOwnerModel();
	}
	return RootOwner;
}

bool UDSToolLibrary::ModelVaild(UDSBaseModel* InModel)
{
	if (!InModel)
	{
		return false;
	}
	if (InModel->GetModelType() == EDSModelType::E_House_Area)
	{
		auto InArea = Cast<UDSHouseAreaModel>(InModel);
		auto Parent = InArea->GetParentPlane();
		if (!DS_MODEL_VALID_FOR_USE(Parent))
		{
			return true;
		}
		auto InProp = static_cast<FDSHouseAreaProperty*>(InModel->GetProperty());
		auto InPoints = InProp->Points;

		auto ParentProp = static_cast<FDSHouseAreaProperty*>(Parent->GetProperty());
		auto ParentPoints = ParentProp->Points;

		for (auto& P : InPoints)
		{
			auto bIn = FImageProcessModule::Get()->PointInPolygon(P, ParentPoints, true, 1.f);
			if (!bIn)
			{
				return false;
			}
		}
	}
	else if (InModel->GetModelType() == EDSModelType::E_House_Area_Split_Line)
	{
	}
	else if (InModel->GetModelType() == EDSModelType::E_House_Door || InModel->GetModelType() == EDSModelType::E_House_Window)
	{
		auto Walls = UDSMVCSubsystem::GetInstance()->GetModels(EDSModelType::E_House_Wall);
		auto Prop = static_cast<FDSDoorAndWindowProperty*>(InModel->GetProperty());
		auto Start = Prop->SegmentStart;
		auto End = Prop->SegmentEnd;
		auto HT = Prop->GetThickness() * 0.5;
		auto Nor = Prop->GetNormal();
		auto L = Prop->GetLength();

		TArray<FVector> Points =
		{
			Start + Nor * HT,
			Start,
			Start - Nor * HT,
			End + Nor * HT,
			End,
			End - Nor * HT
		};

		bool bOnWall = false;
		for (auto& Iter : Walls)
		{
			auto Prop = static_cast<FDSPathProperty*>(Iter->GetProperty());
			auto Outline = Prop->GetBottomOutline();
			int32 Count = 0;
			for (auto& P : Points)
			{
				if (FImageProcessModule::Get()->PointInPolygon(P, Outline, true, 1.5f))
				{
					++Count;
				}
			}
			if (Count >= Points.Num())
			{
				bOnWall = true;
				break;
			}
		}
		if (!bOnWall)
		{
			return false;
		}
		auto Doors = UDSMVCSubsystem::GetInstance()->GetModels(EDSModelType::E_House_Door);
		auto Windows = UDSMVCSubsystem::GetInstance()->GetModels(EDSModelType::E_House_Window);
		for (auto& Iter : Doors)
		{
			if (InModel == Iter)
			{
				continue;
			}
			auto Outline = Iter->GetBottomOutline();
			if (FClipper2Library::PolygonIntersection(Points, Outline))
			{
				return false;
			}
			/*for (auto& P : Points)
			{
				if (FImageProcessModule::Get()->PointInPolygon(P, Outline,true,-0.5))
				{
					return false;
				}
				
			}*/
		}

		for (auto& Iter : Windows)
		{
			if (InModel == Iter)
			{
				continue;
			}
			auto Outline = Iter->GetBottomOutline();
			if (FClipper2Library::PolygonIntersection(Points, Outline))
			{
				return false;
			}
			//for (auto& P : Points)
			//{
			//	if (FImageProcessModule::Get()->PointInPolygon(P, Outline, true, -0.5))
			//	{
			//		return false;
			//	} 
			//}
		}
	}
	return true;
}

bool UDSToolLibrary::ModelOverlay(UDSBaseModel* InModel)
{
	if (InModel->GetModelType() == EDSModelType::E_House_Pillar)
	{
		auto AllPaths = UDSMVCSubsystem::GetInstance()->GetModels(EDSModelType::E_House_Wall);
		AllPaths.Append(UDSMVCSubsystem::GetInstance()->GetModels(EDSModelType::E_House_Beam));
		AllPaths.Append(UDSMVCSubsystem::GetInstance()->GetModels(EDSModelType::E_House_Platform));

		auto Points = InModel->GetWorldBottomOutline();
		auto Loc = InModel->GetProperty()->GetTransformProperty().Location;
		for (auto& Iter : AllPaths)
		{
			auto OL = Iter->GetBottomOutline();
			for (auto& P : Points)
			{
				if (FImageProcessModule::Get()->PointInPolygon(P, OL))
				{
					return true;
				}
			}
		}

		for (auto& Iter : UDSMVCSubsystem::GetInstance()->GetModels(EDSModelType::E_House_Pillar))
		{
			auto OL = Iter->GetWorldBottomOutline();
			for (auto& P : Points)
			{
				if (InModel == Iter)
				{
					continue;
				}
				if (FImageProcessModule::Get()->PointInPolygon(P, OL, true, -1))
				{
					return true;
				}
			}
		}
	}
	return false;
}

bool UDSToolLibrary::IsSomeKeyHold(const FKey& Key)
{
	return ADesignStationController::Get()->IsInputKeyDown(Key);
}

bool UDSToolLibrary::IsKeyDownForMultiOperator()
{
	return UDSToolLibrary::IsSomeKeyHold(EKeys::LeftShift) || UDSToolLibrary::IsSomeKeyHold(EKeys::RightShift);
}

void UDSToolLibrary::GetMousePositionAndDir(FVector& OutPosition, FVector& OutDir)
{
	ADesignStationController::Get()->DeprojectMousePositionToWorld(OutPosition, OutDir);
}

FDSMaterialProperty UDSToolLibrary::GetSelectMaterialProperty()
{
	FDSMaterialInfo MaterialInfo2D;
	MaterialInfo2D.Index = 0;
	MaterialInfo2D.MaterialPath = TEXT("Local_Wall_Edit");


	FDSMaterialInfo MaterialInfo3D;
	MaterialInfo3D.Index = 0;
	MaterialInfo3D.MaterialPath = TEXT("Local_Wall_Edit");

	FDSMaterialProperty DefaultMaterialProperty;
	DefaultMaterialProperty.MaterialInfo2D.Add(MaterialInfo2D);
	DefaultMaterialProperty.MaterialInfo3D.Add(MaterialInfo3D);
	return DefaultMaterialProperty;
}

FDSMaterialProperty UDSToolLibrary::GetWallMaterialProperty()
{
	FDSMaterialInfo MaterialInfo2D;
	MaterialInfo2D.Index = 0;
	MaterialInfo2D.MaterialPath = TEXT("Local_Wall");

	FDSMaterialInfo MaterialInfo3D;
	MaterialInfo3D.Index = 0;
	MaterialInfo3D.MaterialPath = TEXT("Local_Wall_3D");

	FDSMaterialProperty DefaultMaterialProperty;
	DefaultMaterialProperty.MaterialInfo2D.Add(MaterialInfo2D);
	DefaultMaterialProperty.MaterialInfo3D.Add(MaterialInfo3D);
	return DefaultMaterialProperty;
}

FDSMaterialProperty UDSToolLibrary::GetScalerMaterialProperty()
{
	FDSMaterialInfo MaterialInfo2D;
	MaterialInfo2D.Index = 0;
	MaterialInfo2D.MaterialPath = TEXT("Scaler");

	FDSMaterialInfo MaterialInfo3D;
	MaterialInfo3D.Index = 0;
	MaterialInfo3D.MaterialPath =TEXT("Scaler");

	FDSMaterialProperty DefaultMaterialProperty;
	DefaultMaterialProperty.MaterialInfo2D.Add(MaterialInfo2D);
	DefaultMaterialProperty.MaterialInfo3D.Add(MaterialInfo3D);
	return DefaultMaterialProperty;
}

FDSMaterialProperty UDSToolLibrary::GetScaler2MaterialProperty()
{
	FDSMaterialInfo MaterialInfo2D;
	MaterialInfo2D.Index = 0;
	MaterialInfo2D.MaterialPath = TEXT("Scaler2");

	FDSMaterialInfo MaterialInfo3D;
	MaterialInfo3D.Index = 0;
	MaterialInfo3D.MaterialPath = TEXT("Scaler2");

	FDSMaterialProperty DefaultMaterialProperty;
	DefaultMaterialProperty.MaterialInfo2D.Add(MaterialInfo2D);
	DefaultMaterialProperty.MaterialInfo3D.Add(MaterialInfo3D);
	return DefaultMaterialProperty;
}

FDSMaterialProperty UDSToolLibrary::GetBeamAndPlatformMaterialProperty()
{
	FDSMaterialInfo MaterialInfo2D;
	MaterialInfo2D.Index = 0;
	MaterialInfo2D.MaterialPath = TEXT("Local_BeamAndPlatform");

	FDSMaterialInfo MaterialInfo3D;
	MaterialInfo3D.Index = 0;
	MaterialInfo3D.MaterialPath = TEXT("Local_BeamAndPlatform_3D");

	FDSMaterialProperty DefaultMaterialProperty;
	DefaultMaterialProperty.MaterialInfo2D.Add(MaterialInfo2D);
	DefaultMaterialProperty.MaterialInfo3D.Add(MaterialInfo3D);
	return DefaultMaterialProperty;
}

FDSMaterialProperty UDSToolLibrary::GetPanelMaterialProperty()
{
	FDSMaterialInfo MaterialInfo2D;
	MaterialInfo2D.Index = 0;
	MaterialInfo2D.MaterialPath = TEXT("Local_Pillar_Plane2D");

	FDSMaterialInfo MaterialInfo3D;
	MaterialInfo3D.Index = 0;
	MaterialInfo3D.MaterialPath = TEXT("Local_Pillar_3D");

	FDSMaterialProperty DefaultMaterialProperty;
	DefaultMaterialProperty.MaterialInfo2D.Add(MaterialInfo2D);
	DefaultMaterialProperty.MaterialInfo3D.Add(MaterialInfo3D);
	return DefaultMaterialProperty;
}

void UDSToolLibrary::ParsePropertyCopy(UDSBaseModel* EditModel, const TSharedPtr<FDSBaseProperty>& InProperty)
{
	if (DS_MODEL_VALID_FOR_USE(EditModel) && EditModel->GetPropertySharedPtr().IsValid() && InProperty.IsValid())
	{
		EDSModelType ModelType = EditModel->GetModelType();
		if (ModelType == EDSModelType::E_House_Wall ||
			ModelType == EDSModelType::E_House_Beam ||
			ModelType == EDSModelType::E_House_Platform)
		{
			StaticCastSharedPtr<FDSHousePathProperty>(EditModel->GetPropertySharedPtr())->CopyData(InProperty.Get());
			StaticCastSharedPtr<FDSHousePathProperty>(EditModel->GetPropertySharedPtr())->InitPathOutline();
		}
		else if (ModelType == EDSModelType::E_House_Pillar)
		{
			StaticCastSharedPtr<FDSPillarProperty>(EditModel->GetPropertySharedPtr())->CopyData(InProperty.Get());
		}
		else if (ModelType == EDSModelType::E_House_Door || ModelType == EDSModelType::E_House_Window)
		{
			bool IsWinDoorTypeChange = IsConstructChanged(EditModel, InProperty);
			StaticCastSharedPtr<FDSDoorAndWindowProperty>(EditModel->GetPropertySharedPtr())->CopyData(InProperty.Get());
			StaticCastSharedPtr<FDSDoorAndWindowProperty>(EditModel->GetPropertySharedPtr())->RefreshDoorAndWindow();
			if (IsWinDoorTypeChange)
			{
				Cast<UDSDoorAndWindowBaseModel>(EditModel)->ClearCupModel();
				auto InnerView = Cast<UDSDoorAndWindowBaseModel>(EditModel)->GenerateCupboardModel();
				auto OuterView = UDSMVCSubsystem::GetInstance()->GetView(EditModel);
				if (InnerView && OuterView)
				{
					InnerView->AttachToActor(OuterView, FAttachmentTransformRules::KeepRelativeTransform);
				}
			}
		}
		else if (ModelType == EDSModelType::E_Furniture_HouseFurniture)
		{
			StaticCastSharedPtr<FDSSoftFurnitureProperty>(EditModel->GetPropertySharedPtr())->CopyData(InProperty.Get());
		}
		else if(ModelType == EDSModelType::E_Generated_CounterTop)
		{
			StaticCastSharedPtr<FDSCounterTopProperty>(EditModel->GetPropertySharedPtr())->CopyData(InProperty.Get());
		}
		else if(ModelType == EDSModelType::E_Generated_SideCounterTop)
		{
			StaticCastSharedPtr<FDSSideCounterTopProperty>(EditModel->GetPropertySharedPtr())->CopyData(InProperty.Get());
		}
		else if(ModelType == EDSModelType::E_Generated_CounterTop_Line)
		{
			StaticCastSharedPtr<FDSCounterTopLineProperty>(EditModel->GetPropertySharedPtr())->CopyData(InProperty.Get());
		}
		else if(ModelType == EDSModelType::E_Generated_CounterTop_Point)
		{
			StaticCastSharedPtr<FDSCounterTopPointProperty>(EditModel->GetPropertySharedPtr())->CopyData(InProperty.Get());
		}
		else if (ModelType == EDSModelType::E_Custom_Sink)
		{
			StaticCastSharedPtr<FDSSinkProperty>(EditModel->GetPropertySharedPtr())->CopyData(InProperty.Get());
		}
		else if (EditModel->GetModelType() >= EDSModelType::E_Custom_UpperCabinet && EditModel->GetModelType() < EDSModelType::E_Custom_Furniture_Range_End)
		{
			StaticCastSharedPtr<FDSFurnitureBaseProperty>(EditModel->GetPropertySharedPtr())->CopyData(InProperty.Get());
		}
		else if (ModelType == EDSModelType::E_RoofArea)
		{
			StaticCastSharedPtr<FDSMoldingCeilingProperty>(EditModel->GetPropertySharedPtr())->CopyData(InProperty.Get());
		}
		else //剩余法，上面的处理完以后，剩余的全部走基类处理
		{
			EditModel->GetPropertySharedPtr()->CopyData(InProperty.Get());
		}
	}
}

bool UDSToolLibrary::IsConstructChanged(UDSBaseModel* EditModel, const TSharedPtr<FDSBaseProperty>& InProperty)
{
	if (DS_MODEL_VALID_FOR_USE(EditModel))
	{
		if (EditModel->GetModelType() == EDSModelType::E_House_Window
			|| EditModel->GetModelType() == EDSModelType::E_House_Door)
		{
			if (StaticCastSharedPtr<FDSDoorAndWindowProperty>(EditModel->GetPropertySharedPtr())
				&& StaticCastSharedPtr<FDSDoorAndWindowProperty>(InProperty))
			{
				EDoorAndWindowType CurType = StaticCastSharedPtr<FDSDoorAndWindowProperty>(EditModel->GetPropertySharedPtr())->GetWindowDoorType();
				EDoorAndWindowType RevokeType = StaticCastSharedPtr<FDSDoorAndWindowProperty>(InProperty)->GetWindowDoorType();
				return CurType != RevokeType;
			}
		}
	}
	return false;
}

TSharedPtr<FDSBaseProperty> UDSToolLibrary::GetBasePropertyCopy(UDSBaseModel* EditModel)
{
	if (DS_MODEL_VALID_FOR_USE(EditModel) && EditModel->GetPropertySharedPtr().IsValid() && EditModel->GetPropertySharedPtr().IsValid())
	{
		TSharedPtr<FDSBaseProperty> InProperty = EditModel->GetPropertySharedPtr();
		if (EditModel->GetModelType() == EDSModelType::E_House_Wall ||
			EditModel->GetModelType() == EDSModelType::E_House_Beam ||
			EditModel->GetModelType() == EDSModelType::E_House_Platform)
		{
			if (InProperty.IsValid() && StaticCastSharedPtr<FDSHousePathProperty>(InProperty))
			{
				TSharedPtr<FDSHousePathProperty> NewProperty = MakeShared<FDSHousePathProperty>();
				NewProperty->CopyData(InProperty.Get());
				return NewProperty;
			}
		}
		else if (EditModel->GetModelType() == EDSModelType::E_House_Pillar)
		{
			if (InProperty.IsValid() && StaticCastSharedPtr<FDSHousePathProperty>(InProperty))
			{
				TSharedPtr<FDSPillarProperty> NewProperty = MakeShared<FDSPillarProperty>();
				NewProperty->CopyData(InProperty.Get());
				return NewProperty;
			}
		}
		else if (EditModel->GetModelType() == EDSModelType::E_House_Door || EditModel->GetModelType() == EDSModelType::E_House_Window)
		{
			if (InProperty.IsValid() && StaticCastSharedPtr<FDSDoorAndWindowProperty>(InProperty))
			{
				TSharedPtr<FDSDoorAndWindowProperty> NewProperty = MakeShared<FDSDoorAndWindowProperty>();
				NewProperty->CopyData(InProperty.Get());
				return NewProperty;
			}
		}
		else if (EditModel->GetModelType() == EDSModelType::E_Furniture_HouseFurniture)
		{
			if (InProperty.IsValid() && StaticCastSharedPtr<FDSSoftFurnitureProperty>(InProperty))
			{
				TSharedPtr<FDSSoftFurnitureProperty> NewProperty = MakeShared<FDSSoftFurnitureProperty>();
				NewProperty->CopyData(InProperty.Get());
				return NewProperty;
			}
		}
		else if (EditModel->GetModelType() >= EDSModelType::E_Custom_UpperCabinet && EditModel->GetModelType() < EDSModelType::E_Custom_Furniture_Range_End)
		{
			if (InProperty.IsValid() && StaticCastSharedPtr<FDSFurnitureBaseProperty>(InProperty))
			{
				TSharedPtr<FDSFurnitureBaseProperty> NewProperty = MakeShared<FDSFurnitureBaseProperty>();
				NewProperty->CopyData(InProperty.Get());
				return NewProperty;
			}
		}
		else if (EditModel->GetModelType() == EDSModelType::E_Custom_Stove)
		{
			if (InProperty.IsValid())
			{
				TSharedPtr<FDSStoveProperty> NewProperty = MakeShared<FDSStoveProperty>();
				NewProperty->CopyData(InProperty.Get());
				return NewProperty;
			}
		}
		else if (EditModel->GetModelType() == EDSModelType::E_Custom_RangeHood)
		{
			if (InProperty.IsValid())
			{
				TSharedPtr<FDSRangeHoodProperty> NewProperty = MakeShared<FDSRangeHoodProperty>();
				NewProperty->CopyData(InProperty.Get());
				return NewProperty;
			}
		}
		else if (EditModel->GetModelType() == EDSModelType::E_Custom_Sink)
		{
			if (InProperty.IsValid())
			{
				TSharedPtr<FDSSinkProperty> NewProperty = MakeShared<FDSSinkProperty>();
				NewProperty->CopyData(InProperty.Get());
				return NewProperty;
			}
		}
		else if (EditModel->GetModelType() == EDSModelType::E_Furniture_MoldingCeiling)
		{
			if (InProperty.IsValid())
			{
				TSharedPtr<FDSMoldingCeilingProperty> NewProperty = MakeShared<FDSMoldingCeilingProperty>();
				NewProperty->CopyData(InProperty.Get());
				return NewProperty;
			}
		}
		else //剩余法，上面的处理完以后，剩余的全部走基类处理
		{
			if (InProperty.IsValid())
			{
				TSharedPtr<FDSBaseProperty> NewProperty = MakeShared<FDSBaseProperty>();
				NewProperty->CopyData(InProperty.Get());
				return NewProperty;
			}
		}
	}
	return nullptr;
}

TSharedPtr<FDSBaseProperty> UDSToolLibrary::GetBasePropertyCopy(const EDSModelType& InModelType, const TSharedPtr<FDSBaseProperty>& InProperty)
{
	if (InProperty.IsValid())
	{
		if (InModelType == EDSModelType::E_House_Wall ||
			InModelType == EDSModelType::E_House_Beam ||
			InModelType == EDSModelType::E_House_Platform)
		{
			if (InProperty.IsValid() && StaticCastSharedPtr<FDSHousePathProperty>(InProperty))
			{
				TSharedPtr<FDSHousePathProperty> NewProperty = MakeShared<FDSHousePathProperty>();
				NewProperty->CopyData(InProperty.Get());
				return NewProperty;
			}
		}
		else if (InModelType == EDSModelType::E_House_Pillar)
		{
			if (InProperty.IsValid() && StaticCastSharedPtr<FDSHousePathProperty>(InProperty))
			{
				TSharedPtr<FDSPillarProperty> NewProperty = MakeShared<FDSPillarProperty>();
				NewProperty->CopyData(InProperty.Get());
				return NewProperty;
			}
		}
		else if (InModelType == EDSModelType::E_House_Door || InModelType == EDSModelType::E_House_Window)
		{
			if (InProperty.IsValid() && StaticCastSharedPtr<FDSDoorAndWindowProperty>(InProperty))
			{
				TSharedPtr<FDSDoorAndWindowProperty> NewProperty = MakeShared<FDSDoorAndWindowProperty>();
				NewProperty->CopyData(InProperty.Get());
				return NewProperty;
			}
		}
		else if (InModelType == EDSModelType::E_Furniture_HouseFurniture)
		{
			if (InProperty.IsValid() && StaticCastSharedPtr<FDSSoftFurnitureProperty>(InProperty))
			{
				TSharedPtr<FDSSoftFurnitureProperty> NewProperty = MakeShared<FDSSoftFurnitureProperty>();
				NewProperty->CopyData(InProperty.Get());
				return NewProperty;
			}
		}
		else if (InModelType >= EDSModelType::E_Custom_UpperCabinet && InModelType < EDSModelType::E_Custom_Furniture_Range_End)
		{
			if (InProperty.IsValid() && StaticCastSharedPtr<FDSFurnitureBaseProperty>(InProperty))
			{
				TSharedPtr<FDSFurnitureBaseProperty> NewProperty = MakeShared<FDSFurnitureBaseProperty>();
				NewProperty->CopyData(InProperty.Get());
				return NewProperty;
			}
		}
		else
		{
			if (InProperty.IsValid())
			{
				TSharedPtr<FDSBaseProperty> NewProperty = MakeShared<FDSBaseProperty>();
				NewProperty->CopyData(InProperty.Get());
				return NewProperty;
			}
		}
	}
	return nullptr;
}

TSharedPtr<FDSHousePathProperty> UDSToolLibrary::GetPropertyCopy_Wall(const TSharedPtr<FDSBaseProperty>& InProperty)
{
	if (InProperty.IsValid() && StaticCastSharedPtr<FDSHousePathProperty>(InProperty))
	{
		TSharedPtr<FDSHousePathProperty> NewProperty = MakeShared<FDSHousePathProperty>();
		NewProperty->CopyData(InProperty.Get());
		return NewProperty;
	}
	return nullptr;
}

TSharedPtr<FDSPillarProperty> UDSToolLibrary::GetPropertyCopy_Pillar(const TSharedPtr<FDSBaseProperty>& InProperty)
{
	if (InProperty.IsValid() && StaticCastSharedPtr<FDSHousePathProperty>(InProperty))
	{
		TSharedPtr<FDSPillarProperty> NewProperty = MakeShared<FDSPillarProperty>();
		NewProperty->CopyData(InProperty.Get());
		return NewProperty;
	}
	return nullptr;
}

TSharedPtr<FDSBaseProperty> UDSToolLibrary::GetPropertyCopy_Base(const TSharedPtr<FDSBaseProperty>& InProperty)
{
	if (InProperty.IsValid())
	{
		TSharedPtr<FDSBaseProperty> NewProperty = MakeShared<FDSBaseProperty>();
		NewProperty->CopyData(InProperty.Get());
		return NewProperty;
	}
	return nullptr;
}

TSharedPtr<FDSHousePathProperty> UDSToolLibrary::GetPropertyCopy_BeamAndPlatform(const TSharedPtr<FDSBaseProperty>& InProperty)
{
	if (InProperty.IsValid() && StaticCastSharedPtr<FDSHousePathProperty>(InProperty))
	{
		TSharedPtr<FDSHousePathProperty> NewProperty = MakeShared<FDSHousePathProperty>();
		NewProperty->CopyData(InProperty.Get());
		return NewProperty;
	}
	return nullptr;
}

TSharedPtr<FDSDoorAndWindowProperty> UDSToolLibrary::GetPropertyCopy_WindowAndDoor(const TSharedPtr<FDSBaseProperty>& InProperty)
{
	if (InProperty.IsValid() && StaticCastSharedPtr<FDSDoorAndWindowProperty>(InProperty))
	{
		TSharedPtr<FDSDoorAndWindowProperty> NewProperty = MakeShared<FDSDoorAndWindowProperty>();
		NewProperty->CopyData(InProperty.Get());
		return NewProperty;
	}
	return nullptr;
}

TSharedPtr<FDSCounterTopProperty> UDSToolLibrary::GetPropertyCopy_CounterTop(const TSharedPtr<FDSBaseProperty>& InProperty)
{
	if (InProperty.IsValid() && StaticCastSharedPtr<FDSCounterTopProperty>(InProperty))
	{
		TSharedPtr<FDSCounterTopProperty> NewProperty = MakeShared<FDSCounterTopProperty>();
		NewProperty->CopyData(InProperty.Get());
		return NewProperty;
	}
	return nullptr;
}

TSharedPtr<FDSSideCounterTopProperty> UDSToolLibrary::GetPropertyCopy_SideCounterTop(const TSharedPtr<FDSBaseProperty>& InProperty)
{
	if (InProperty.IsValid() && StaticCastSharedPtr<FDSSideCounterTopProperty>(InProperty))
	{
		TSharedPtr<FDSSideCounterTopProperty> NewProperty = MakeShared<FDSSideCounterTopProperty>();
		NewProperty->CopyData(InProperty.Get());
		return NewProperty;
	}
	return nullptr;
}

TSharedPtr<FDSCounterTopLineProperty> UDSToolLibrary::GetPropertyCopy_CounterTopLine(const TSharedPtr<FDSBaseProperty>& InProperty)
{
	if (InProperty.IsValid() && StaticCastSharedPtr<FDSCounterTopLineProperty>(InProperty))
	{
		TSharedPtr<FDSCounterTopLineProperty> NewProperty = MakeShared<FDSCounterTopLineProperty>();
		NewProperty->CopyData(InProperty.Get());
		return NewProperty;
	}
	return nullptr;
}

TSharedPtr<FDSCounterTopPointProperty> UDSToolLibrary::GetPropertyCopy_CounterTopPoint(const TSharedPtr<FDSBaseProperty>& InProperty)
{
	if (InProperty.IsValid() && StaticCastSharedPtr<FDSCounterTopPointProperty>(InProperty))
	{
		TSharedPtr<FDSCounterTopPointProperty> NewProperty = MakeShared<FDSCounterTopPointProperty>();
		NewProperty->CopyData(InProperty.Get());
		return NewProperty;
	}
	return nullptr;
}

void UDSToolLibrary::ModelDialog(
	UDSBaseModel* InModel, const FDSModelExecuteType& InExecute, const FString& Msg1, const FString& Msg2, 
	const DSDialogFunction& SureFunction, const DSDialogFunction& CancelFunction)
{
	if (IsValid(InModel))
	{
		EDSModelType EditType = InModel->GetModelType();
		if (EditType == EDSModelType::E_MultiSelect || EditType == EDSModelType::E_Group)
		{ 
			TArray<UDSBaseModel*> IncludeModels = Cast<UDSMultiModel>(InModel)->GetIncludeModel();
			UDSToolLibrary::ModelDialog_Inner(IncludeModels, InExecute, Msg1, Msg2, SureFunction, CancelFunction);
		}
		else
		{
			UDSToolLibrary::ModelDialog_Inner({ InModel }, InExecute, Msg1, Msg2, SureFunction, CancelFunction);
		}
	}
}

void UDSToolLibrary::ModelDialog_Inner(
	const TArray<UDSBaseModel*>& InModels, const FDSModelExecuteType& InExecute, const FString& Msg1, const FString& Msg2,
	const DSDialogFunction& SureFunction, const DSDialogFunction& CancelFunction)
{
	if (!InModels.IsEmpty())
	{
		TMap<EDSModelType, UDSBaseModel*> AllTypeModel;
		for (const auto& Iter : InModels)
		{
			AllTypeModel.Add(Iter->GetModelType(), Iter);
		}
		if (InExecute.IsDeleteExecute())
		{
			TMap<EDSModelType, UDSBaseModel*> CTModel = AllTypeModel.FilterByPredicate(
				[](const TPair<EDSModelType, UDSBaseModel*>& InElement)->bool 
				{
					return InElement.Key == EDSModelType::E_Generated_CounterTop;
				}
			);
			bool NeedDialog = false;
			if (!CTModel.IsEmpty())
			{
				for (const auto& CTM : CTModel)
				{
					if(!IsValid(CTM.Value))
						continue;
					
					TMap<FString, ESideCounterTopDependencyCheckType> RelatedModelMap = UDSModelDependencySubsystem::GetInstance()->FindSideCounterTopByDependency(CTM.Value->GetUUID(), ESideCounterTopDependencyCheckType::UpperOrLower);
					TMap<FString, ESideCounterTopDependencyCheckType> UpperRelate = RelatedModelMap.FilterByPredicate(
						[](const TPair<FString, ESideCounterTopDependencyCheckType>& InElement)->bool
						{
								return InElement.Value == ESideCounterTopDependencyCheckType::UpperModel;
						});
					NeedDialog = UpperRelate.Num() > 0;
					if (NeedDialog)
					{
						break;
					}
				}
			}
			if (NeedDialog)
			{
				UDSUISubsystem::GetInstance()->PresentModalDialog(
					Msg1, Msg2,
					FSimpleDelegate::CreateLambda(SureFunction, InModels, InExecute, TEXT("")),
					FSimpleDelegate::CreateLambda(CancelFunction, InModels, InExecute, TEXT("")),
					false,
					TEXT(""),
					ETextJustify::Type::Left
				);
			}
			else
			{
				SureFunction(InModels, InExecute, TEXT(""));
			}
		}
	}
}

bool UDSToolLibrary::ModelIsSelf(UDSBaseModel* EditModel)
{
	if (DS_MODEL_VALID_FOR_USE(EditModel))
	{
		UDSBaseModel* TopModel = EditModel->GetTopLevelOwnerModel();
		return TopModel == EditModel;
	}
	return true;
}

bool UDSToolLibrary::ModelIsComponent(UDSBaseModel* EditModel)
{
	if (DS_MODEL_VALID_FOR_USE(EditModel))
	{
		return NoBroadcastNoModifyType.Contains(EditModel->GetModelType());
	}
	return false;
}

bool UDSToolLibrary::IsCustomType(UDSBaseModel* EditModel)
{
	if (DS_MODEL_VALID_FOR_USE(EditModel))
	{
		return EditModel->GetModelType() >= EDSModelType::E_Custom_UpperCabinet && EditModel->GetModelType() < EDSModelType::E_Custom_Furniture_Range_End;
	}
	return false;
}

bool UDSToolLibrary::IsCustomBoardType(EDSModelType InType)
{
	return InType == EDSModelType::E_Custom_Board || InType == EDSModelType::E_Custom_AdjustablePanel
		|| InType == EDSModelType::E_Custom_SideClosurePanel || InType == EDSModelType::E_Custom_TopClosurePanel;
}

bool UDSToolLibrary::IsCanFunctionalEvenType(EDSModelType InType)
{
	return InType == EDSModelType::E_Custom_Board ||InType == EDSModelType::E_Custom_Functional_HangingRod
		|| InType == EDSModelType::E_Custom_Functional_Combine;
}

bool UDSToolLibrary::IsGeneratedLineType(EDSModelType InType)
{
	return InType == EDSModelType::E_Generated_CrownMoulding || InType == EDSModelType::E_Generated_LightCord || InType == EDSModelType::E_Generated_SkirtingBoard;
}

bool UDSToolLibrary::IsCustomCupboardType(EDSModelType InType)
{
	return InType >= EDSModelType::E_Custom_UpperCabinet && InType < EDSModelType::E_Custom_Furniture_Range_End;
}

bool UDSToolLibrary::IsCustomCabinetType(EDSModelType InType)
{
	switch (InType)
	{
	case EDSModelType::E_Custom_UpperCabinet:
	case EDSModelType::E_Custom_WallCabinet:
	case EDSModelType::E_Custom_TallCabinet:
	case EDSModelType::E_Custom_BaseCabinet:
	case EDSModelType::E_Custom_CornerCabinet:
	case EDSModelType::E_Custom_WallBoardCabinet:
	case EDSModelType::E_Custom_LayoutDoor:
	case EDSModelType::E_Custom_CornerCutCabinet:
		return true;
	default:
		return false;
	}
}

bool UDSToolLibrary::IsFunctionalTopRootType(EDSModelType InType)
{
	switch (InType)
	{
	case EDSModelType::E_Custom_UpperCabinet:
	case EDSModelType::E_Custom_WallCabinet:
	case EDSModelType::E_Custom_TallCabinet:
	case EDSModelType::E_Custom_BaseCabinet:
	case EDSModelType::E_Custom_CornerCabinet:
	case EDSModelType::E_Custom_CornerCutCabinet:
		return true;
	default:
		return false;
	}
}

bool UDSToolLibrary::IsCustomDoorType(EDSModelType InType)
{
	switch (InType)
	{
	case EDSModelType::E_Custom_DoorPanel:
	case EDSModelType::E_Custom_DoorPanel_Flat:	
	case EDSModelType::E_Custom_DoorPanel_SolidWood:	
	case EDSModelType::E_Custom_DoorPanel_AluminumFrame:
	case EDSModelType::E_Custom_DoorPanel_Glass:		
	case EDSModelType::E_Custom_DoorPanel_Fake:	
		return true;
	default:
		return false;
	}
}

bool UDSToolLibrary::IsInSameGroup(UDSBaseModel* Model1, UDSBaseModel* Model2)
{
	if (UDSToolLibrary::IsInGroup(Model1) && UDSToolLibrary::IsInGroup(Model2))
	{
		return Model1->GetGroupUUID().Equals(Model2->GetGroupUUID());
	}

	return false;
}

UDSBaseModel* UDSToolLibrary::GetOwnerModelRecursion(UDSBaseModel* EditModel)
{
	if (DS_MODEL_VALID_FOR_USE(EditModel))
	{
		return EditModel->ShouldUseParentModel() ? EditModel->GetOwnerModel() : EditModel->GetTopLevelOwnerModel();
	}
	return EditModel;
}

FInputChord UDSToolLibrary::LoadCommandFromIni(const FString& IniFilename, const FString& SectionName, const FString& CommandId)
{
	FString CommandArg;
	GConfig->GetValue(*SectionName, *CommandId, CommandArg, IniFilename);

	TArray<FString> ParsedArgs;
	CommandArg.ParseIntoArray(ParsedArgs, TEXT(","));

	FString Key = ParsedArgs[0];

	ParsedArgs.RemoveAt(0);

	bool bCtrl = ParsedArgs.Contains(TEXT("Ctrl"));
	bool bAlt = ParsedArgs.Contains(TEXT("Alt"));
	bool bShift = ParsedArgs.Contains(TEXT("Shift"));
	bool bCommand = ParsedArgs.Contains(TEXT("Command"));

	return FInputChord(EModifierKey::FromBools(bCtrl, bAlt, bShift, bCommand), FKey(*Key));
}

void UDSToolLibrary::SaveCommandToIni(const FString& IniFilename, const FString& SectionName, const FString& CommandId,
                                      const FInputChord& InputChord)
{
	FString CommandArg;

	CommandArg.Append(InputChord.Key.ToString());

	if (InputChord.NeedsControl())
	{
		CommandArg.Append(TEXT(",Ctrl"));
	}

	if (InputChord.NeedsAlt())
	{
		CommandArg.Append(TEXT(",Alt"));
	}

	if (InputChord.NeedsShift())
	{
		CommandArg.Append(TEXT(",Shift"));
	}

	if (InputChord.NeedsCommand())
	{
		CommandArg.Append(TEXT(",Command"));
	}

	GConfig->SetString(*SectionName, *CommandId, *CommandArg, IniFilename);

	GConfig->Flush(false, IniFilename);
}

bool UDSToolLibrary::IsActorTopLevel(ADSBaseView* InActor, ADSBaseView*& TopLevelActor)
{
	if (InActor == nullptr)
	{
		TopLevelActor = nullptr;
		return false;
	}

	AActor* OwnerTopLevel = InActor->GetAttachParentActor();
	TopLevelActor = Cast<ADSBaseView>(OwnerTopLevel);
	return TopLevelActor == nullptr;
}

bool UDSToolLibrary::IsActorTopLevel_Only(ADSBaseView* InActor)
{
	ADSBaseView* TopLevelActor = nullptr;
	return IsActorTopLevel(InActor, TopLevelActor);
}

void UDSToolLibrary::SetIgnoreActorForCollision(ADSBaseView* ModifyActor, const TArray<AActor*>& ToIgnoreActors)
{
	if (ModifyActor != nullptr && Cast<ADSCupboardBaseView>(ModifyActor))
	{
		Cast<ADSCupboardBaseView>(ModifyActor)->AddDSIgnoreActors(ToIgnoreActors);
	}
}

void UDSToolLibrary::SetIgnoreActorForComponentCollision(UPrimitiveComponent* ModifyComponent, const TArray<AActor*>& ToIgnoreActors)
{
	TArray<TObjectPtr<USceneComponent>> AttachChild = ModifyComponent->GetAttachChildren();
	for (auto& AC : AttachChild)
	{
		if (!AC)
		{
			continue;
		}
		UPrimitiveComponent* ChildComponent = Cast<UPrimitiveComponent>(AC);
		if (ChildComponent != nullptr)
		{
			SetIgnoreActorForComponentCollision_Inner(ChildComponent, ToIgnoreActors);
		}
	}

	SetIgnoreActorForComponentCollision_Inner(ModifyComponent, ToIgnoreActors);
	SetIgnoreSelfForComponentCollision(ModifyComponent);
}

void UDSToolLibrary::SetIgnoreActorForComponentCollision_Inner(UPrimitiveComponent* ModifyComponent, const TArray<AActor*>& ToIgnoreActors)
{
	if (ModifyComponent != nullptr)
	{
		for (auto& TIA : ToIgnoreActors)
		{
			if (TIA != nullptr)
			{
				ModifyComponent->IgnoreActorWhenMoving(TIA, true);
			}
		}
	}
}

void UDSToolLibrary::SetIgnoreSelfForComponentCollision(UPrimitiveComponent* ModifyComponent)
{
	if (ModifyComponent != nullptr)
	{
		TArray<TObjectPtr<USceneComponent>> AttachChild = ModifyComponent->GetAttachChildren();
		TArray<USceneComponent*> AllComponent = {ModifyComponent};
		for (auto& AC : AttachChild)
		{
			if (AC)
			{
				AllComponent.AddUnique(AC);
			}
		}

		SetIgnoreComponentForComponentCollision(ModifyComponent, AllComponent);
	}
}

void UDSToolLibrary::SetIgnoreComponentForComponentCollision(UPrimitiveComponent* ModifyComponent, const TArray<USceneComponent*>& ToIgnoreComponent)
{
	if (ModifyComponent != nullptr)
	{
		TArray<TObjectPtr<USceneComponent>> AttachChild = ModifyComponent->GetAttachChildren();
		for (auto& AC : AttachChild)
		{
			if (AC && Cast<UPrimitiveComponent>(AC))
			{
				SetIgnoreComponentForComponentCollision_Inner(Cast<UPrimitiveComponent>(AC), ToIgnoreComponent);
			}
		}

		SetIgnoreComponentForComponentCollision_Inner(ModifyComponent, ToIgnoreComponent);
	}
}

void UDSToolLibrary::SetIgnoreComponentForComponentCollision_Inner(UPrimitiveComponent* ModifyComponent, const TArray<USceneComponent*>& ToIgnoreComponent)
{
	if (ModifyComponent != nullptr)
	{
		for (auto& TIC : ToIgnoreComponent)
		{
			if (TIC && Cast<UPrimitiveComponent>(TIC))
			{
				ModifyComponent->IgnoreComponentWhenMoving(Cast<UPrimitiveComponent>(TIC), true);
			}
		}
	}
}

TArray<FVector> UDSToolLibrary::CalculateCollisionConvex(const TArray<FVector>& InVertics)
{
	TArray<FDSIndexVertice> FormatVertices;
	for (int32 i = 0; i < InVertics.Num(); ++i)
	{
		FormatVertices.Add(FDSIndexVertice(i, InVertics[i]));
	}
	//split top down by z 
	TArray<FDSIndexVerticePanelArr> PanelArr;
	for (const auto& FV : FormatVertices)
	{
		int32 ArrIndex = PanelArr.IndexOfByPredicate(
			[FV](const FDSIndexVerticePanelArr& InPanel)-> bool
			{
				return InPanel.IsInThisPanel(FV.Point.Z);
			}
		);
		if (ArrIndex == INDEX_NONE)
		{
			auto& New = PanelArr.AddDefaulted_GetRef();
			New.InitVertice(FV.Point.Z, FV);
		}
		else
		{
			PanelArr[ArrIndex].AddVertices(FV);
		}
	}
	PanelArr.Sort(
		[](const FDSIndexVerticePanelArr& P1, const FDSIndexVerticePanelArr& P2)-> bool
		{
			return P1.PanelValue <= P2.PanelValue;
		}
	);

	for (auto& PA : PanelArr)
	{
		//UDSToolLibrary::IndentPanelVertices(PA);
		PA.IndentToCircle(INDENT_CIRCLE);
	}
	if (PanelArr.Num() >= 2)
	{
		PanelArr[0].Shift(FVector::ZAxisVector, INDENT_CIRCLE);
		PanelArr.Last().Shift(-FVector::ZAxisVector, INDENT_CIRCLE);
	}

	TArray<FVector> Res = InVertics;
	for (const auto& PA : PanelArr)
	{
		for (const auto& VA : PA.VerticesArr)
		{
			if (Res.IsValidIndex(VA.Index))
			{
				Res[VA.Index] = VA.Point;
			}
		}
	}
	return Res;
}

void UDSToolLibrary::IndentPanelVertices(FDSIndexVerticePanelArr& PanelIndexVertices)
{
	TArray<FDSVerticeIndexs> PureVerticeIndexs = ConvertToVerticeIndexs(PanelIndexVertices.VerticesArr);
	//if (PureVerticeIndexs.Num() <= POLYGON_LEAST_CONVEX_NUM) return;
	//polygon convex or not
	bool IsPolygonConvex = UDSToolLibrary::IsPolygonConvex(PureVerticeIndexs);
	if (IsPolygonConvex || PureVerticeIndexs.Num() <= POLYGON_LEAST_CONVEX_NUM)
	{
		PanelIndexVertices.IndentToCircle(INDENT_CIRCLE);
	}
	else
	{
		IndentVertices_Concave(PureVerticeIndexs);
	}
	//indent vertices
	//rewrite vertices in panel
}

TArray<FDSIndexVertice> UDSToolLibrary::IndentIndexVertices(const TArray<FDSIndexVertice>& InVertices)
{
	FVector Sum = FVector::ZeroVector;
	int32 Num = InVertices.Num();
	if (Num <= 2)
	{
		return InVertices;
	}


	for (const auto& IV : InVertices)
	{
		Sum += IV.Point;
	}
	FVector Center = Sum / Num;
	TArray<FDSIndexVertice> Res;
	for (const auto& IV : InVertices)
	{
		FVector VerticeToCenter = Center - IV.Point;
		VerticeToCenter.Normalize();
		FVector IndentPoint = IV.Point + VerticeToCenter * INDENT_CIRCLE;
		auto& NewTemp = Res.AddDefaulted_GetRef();
		NewTemp.Init(IV.Index, IndentPoint);
	}
	return Res;
}

void UDSToolLibrary::IndentVertices_Concave(TArray<FDSVerticeIndexs>& PanelIndexVertices)
{
	TArray<FDSVerticeIndexs> OriginVerticesArr = PanelIndexVertices;
	SortVertices(OriginVerticesArr, PanelIndexVertices);
	TArray<int32> ConcaveIndex = GetConcavePointIndex(PanelIndexVertices);
	TArray<FDSIndexVertice> TempVertices = ConstructIndexVertice(PanelIndexVertices);
	if (ConcaveIndex.Num() > 0)
	{
		TArray<TArray<FDSIndexVertice>> SplitConvexPolygon;
		SplitConcaveToConvex(TempVertices, SplitConvexPolygon);
		TArray<TArray<FDSIndexVertice>> IndentPolygon;
		for (const auto& SCP : SplitConvexPolygon)
		{
			TArray<FDSIndexVertice> IndentTemp = IndentIndexVertices(SCP);
			IndentPolygon.Add(IndentTemp);
		}
		//average index point
		TArray<FDSIndexVertice> IndentIV;
		for (const auto& IP : IndentPolygon)
		{
			for (const auto& IP_IV : IP)
			{
				int32 Index = IndentIV.IndexOfByPredicate(
					[IP_IV](const FDSIndexVertice& ArrIV)-> bool
					{
						return IP_IV.Index == ArrIV.Index;
					}
				);
				if (Index == INDEX_NONE)
				{
					// no to add
					IndentIV.Add(IP_IV);
				}
				else
				{
					// has to average
					IndentIV[Index].AveragePoint(IP_IV.Point);
				}
			}
		}
		//rewrite indent point 
		//checkf(IndentIV.Num() == PanelIndexVertices.Num(), TEXT("indent point num error!"));
		for (const auto& IIV : IndentIV)
		{
			/*const int32 IndexPIV = PanelIndexVertices.IndexOfByPredicate(
				[IIV](const FDSVerticeIndexs& PIV)->bool
				{
					return PIV.HasIndex(IIV.Index);
				}
			);*/
			if (PanelIndexVertices.IsValidIndex(IIV.Index))
			{
				PanelIndexVertices[IIV.Index].InitPoint(IIV.Point);
			}
		}
	}
}

void UDSToolLibrary::SplitConcaveToConvex(const TArray<FDSIndexVertice>& InPolygonVertices, TArray<TArray<FDSIndexVertice>>& SplitPolygons)
{
	if (InPolygonVertices.Num() <= POLYGON_LEAST_CONVEX_NUM || IsPolygonConvex(InPolygonVertices))
	{
		SplitPolygons.Append({InPolygonVertices});
	}
	else
	{
		TArray<int32> ConcaveIndex = GetConcavePointIndex(InPolygonVertices);
		TArray<TArray<FDSIndexVertice>> RecuriseSplitPolygon = SplitConcaveToConvex_Inner(InPolygonVertices, ConcaveIndex);
		for (const auto& RSP : RecuriseSplitPolygon)
		{
			SplitConcaveToConvex(RSP, SplitPolygons);
		}
	}
}

TArray<TArray<FDSIndexVertice>> UDSToolLibrary::SplitConcaveToConvex_Inner(
	const TArray<FDSIndexVertice>& InPolygonVertices, const TArray<int32>& ConcaveIndex)
{
	if (ConcaveIndex.Num() <= 0)
	{
		return {InPolygonVertices};
	}
	if (InPolygonVertices.Num() <= POLYGON_LEAST_CONVEX_NUM)
	{
		return {InPolygonVertices};
	}

	TArray<TArray<FDSIndexVertice>> Res;
	const int32 VerticesNum = InPolygonVertices.Num();
	int32 StopIndex = VerticesNum - 1;

	//concave point index is 0
	if (ConcaveIndex[0] == 0)
	{
		//special 1
		if (ConcaveIndex.Num() != 1)
		{
			TArray<FDSIndexVertice> TempStart = GetSubVerticeArr(InPolygonVertices, 0, ConcaveIndex[1]);
			Res.Add(TempStart);

			int32 CI_Index = 1;
			while (ConcaveIndex.IsValidIndex(CI_Index))
			{
				int32 NextIndex = CI_Index + 1;
				if (ConcaveIndex.IsValidIndex(NextIndex))
				{
					TArray<FDSIndexVertice> InnerSub = GetSubVerticeArr(InPolygonVertices, ConcaveIndex[CI_Index], NextIndex);
					Res.Add(InnerSub);
				}
				else
				{
					TArray<FDSIndexVertice> InnerSub = GetSubVerticeArr(InPolygonVertices, ConcaveIndex[CI_Index], StopIndex);
					//need add index 0
					InnerSub.Add(InPolygonVertices[0]);
					Res.Add(InnerSub);
				}

				CI_Index = NextIndex;
			}
		}
		else
		{
			// only one polygon
			TArray<FDSIndexVertice> Sub1 = GetSubVerticeArr(InPolygonVertices, 0, 2);
			Res.Add(Sub1);
			TArray<FDSIndexVertice> Sub2 = GetSubVerticeArr(InPolygonVertices, 2, VerticesNum);
			Res.Add(Sub2);
		}
	}
	else if (ConcaveIndex[0] == 1)
	{
		//special 2
		int32 SIndex = VerticesNum - 1;
		int32 EIndex = 1;
		TArray<FDSIndexVertice> TempStart = GetSubVerticeArr(InPolygonVertices, SIndex, EIndex);
		Res.Add(TempStart);

		if (ConcaveIndex.Num() > 1)
		{
			int32 CI_Index = 1;
			while (ConcaveIndex.IsValidIndex(CI_Index))
			{
				int32 NextIndex = CI_Index + 1;
				if (ConcaveIndex.IsValidIndex(NextIndex))
				{
					TArray<FDSIndexVertice> InnerSub = GetSubVerticeArr(InPolygonVertices, ConcaveIndex[CI_Index], NextIndex);
					Res.Add(InnerSub);
				}
				else
				{
					TArray<FDSIndexVertice> InnerSub = GetSubVerticeArr(InPolygonVertices, ConcaveIndex[CI_Index], StopIndex);
					Res.Add(InnerSub);
				}

				CI_Index = NextIndex;
			}
		}
		else
		{
			TArray<FDSIndexVertice> LeftVertices = GetSubVerticeArr(InPolygonVertices, EIndex, SIndex);
			Res.Add(LeftVertices);
		}
	}
	else
	{
		int32 CI_Index = 0;
		while (ConcaveIndex.IsValidIndex(CI_Index))
		{
			if (CI_Index == 0)
			{
				TArray<FDSIndexVertice> InnerSub = GetSubVerticeArr(InPolygonVertices, 0, ConcaveIndex[CI_Index]);
				Res.Add(InnerSub);
				++CI_Index;
				continue;
			}
			int32 NextIndex = CI_Index + 1;
			if (ConcaveIndex.IsValidIndex(NextIndex))
			{
				TArray<FDSIndexVertice> InnerSub = GetSubVerticeArr(InPolygonVertices, ConcaveIndex[CI_Index], NextIndex);
				Res.Add(InnerSub);
			}
			else
			{
				TArray<FDSIndexVertice> InnerSub = GetSubVerticeArr(InPolygonVertices, ConcaveIndex[CI_Index], StopIndex);
				Res.Add(InnerSub);
			}

			CI_Index = NextIndex;
		}
	}
	return Res;
}

TArray<FDSIndexVertice> UDSToolLibrary::GetSubVerticeArr(const TArray<FDSIndexVertice>& InPolygonVertices,
                                                         const int32& Start, const int32& End)
{
	TArray<FDSIndexVertice> Res;
	if (Start > End)
	{
		int32 TempS = Start;
		int32 TempE = End;
		int32 ArrNum = InPolygonVertices.Num();
		do
		{
			if (InPolygonVertices.IsValidIndex(TempS))
			{
				Res.Add(InPolygonVertices[TempS]);
			}
			else
			{
				break;
			}

			TempS = (TempS + 1) % ArrNum;
		}
		while (TempS <= TempE);
	}
	else
	{
		for (int32 i = Start; i <= End; ++i)
		{
			if (InPolygonVertices.IsValidIndex(i))
			{
				Res.Add(InPolygonVertices[i]);
			}
			else
			{
				break;
			}
		}
	}
	return Res;
}

TArray<FDSIndexVertice> UDSToolLibrary::ConstructIndexVertice(const TArray<FDSVerticeIndexs>& PanelIndexVertices)
{
	TArray<FDSIndexVertice> Res;
	for (int32 i = 0; i < PanelIndexVertices.Num(); ++i)
	{
		FDSIndexVertice& NewTemp = Res.AddDefaulted_GetRef();
		NewTemp.Init(i, PanelIndexVertices[i].Point);
	}
	return Res;
}

TArray<int32> UDSToolLibrary::GetConcavePointIndex(const TArray<FDSVerticeIndexs>& Vertices)
{
	TArray<int32> Res;
	if (Vertices.Num() < 3)
	{
		return Res;
	}
	int32 Num = Vertices.Num();
	for (int32 i = 0; i < Vertices.Num(); ++i)
	{
		int32 PreIndex = (i - 1 + Num) % Num;
		int32 NextIndex = (i + 1) % Num;
		FVector2D ToPre = FVector2D(Vertices[PreIndex].Point - Vertices[i].Point);
		FVector2D ToNext = FVector2D(Vertices[NextIndex].Point - Vertices[i].Point);
		double CrossProduct = FVector2D::CrossProduct(ToPre, ToNext);
		if (CrossProduct < 0.0 && !FMath::IsNearlyZero(CrossProduct, 0.01))
		{
			Res.Add(i);
		}
	}
	return Res;
}

TArray<int32> UDSToolLibrary::GetConcavePointIndex(const TArray<FDSIndexVertice>& Vertices)
{
	TArray<int32> Res;
	if (Vertices.Num() < 3)
	{
		return Res;
	}
	int32 Num = Vertices.Num();
	for (int32 i = 0; i < Vertices.Num(); ++i)
	{
		int32 PreIndex = (i - 1 + Num) % Num;
		int32 NextIndex = (i + 1) % Num;
		FVector2D ToPre = FVector2D(Vertices[PreIndex].Point - Vertices[i].Point);
		FVector2D ToNext = FVector2D(Vertices[NextIndex].Point - Vertices[i].Point);
		double CrossProduct = FVector2D::CrossProduct(ToPre, ToNext);
		//avoid parallel
		if (CrossProduct < 0.0 && !FMath::IsNearlyZero(CrossProduct, 0.01))
		{
			Res.Add(i);
		}
	}
	return Res;
}

TArray<FDSVerticeIndexs> UDSToolLibrary::ConvertToVerticeIndexs(const TArray<FDSIndexVertice>& InVertices)
{
	TArray<FDSVerticeIndexs> Res;
	for (int32 i = 0; i < InVertices.Num(); ++i)
	{
		FDSIndexVertice JudgeIV = InVertices[i];
		int32 CurIndex = Res.IndexOfByPredicate(
			[JudgeIV](const FDSVerticeIndexs& VI)-> bool
			{
				return VI.IsThisVertice(JudgeIV.Point);
			}
		);
		if (CurIndex == INDEX_NONE)
		{
			FDSVerticeIndexs& NewTemp = Res.AddDefaulted_GetRef();
			NewTemp.InitPoint(JudgeIV.Point, JudgeIV.Index);
		}
		else
		{
			Res[CurIndex].AddIndex(JudgeIV.Index);
		}
	}
	return Res;
}

bool UDSToolLibrary::IsPolygonConvex(const TArray<FDSVerticeIndexs>& PolygonIndexsVertice)
{
	if (PolygonIndexsVertice.Num() <= 3)
	{
		return true;
	}
	int32 VerticeNum = PolygonIndexsVertice.Num();
	FVector PreCrossProduct = FVector::ZeroVector;
	for (int32 i = 0; i < PolygonIndexsVertice.Num(); ++i)
	{
		FVector V1 = PolygonIndexsVertice[i].Point;
		FVector V2 = PolygonIndexsVertice[(i + 1) % VerticeNum].Point;
		FVector V3 = PolygonIndexsVertice[(i + 2) % VerticeNum].Point;
		FVector CurCrossProduct = FVector::CrossProduct(V2 - V1, V3 - V2);
		if (PreCrossProduct.IsNearlyZero(0.01))
		{
			PreCrossProduct = CurCrossProduct;
		}
		else
		{
			if (FVector::DotProduct(PreCrossProduct, CurCrossProduct) < 0)
			{
				return false;
			}
		}
	}
	return true;
}

bool UDSToolLibrary::IsPolygonConvex(const TArray<FDSIndexVertice>& PolygonVertices)
{
	if (PolygonVertices.Num() <= 3)
	{
		return true;
	}
	int32 VerticeNum = PolygonVertices.Num();
	FVector PreCrossProduct = FVector::ZeroVector;
	for (int32 i = 0; i < PolygonVertices.Num(); ++i)
	{
		FVector V1 = PolygonVertices[i].Point;
		FVector V2 = PolygonVertices[(i + 1) % VerticeNum].Point;
		FVector V3 = PolygonVertices[(i + 2) % VerticeNum].Point;
		FVector CurCrossProduct = FVector::CrossProduct(V2 - V1, V3 - V2);
		if (PreCrossProduct.IsNearlyZero(0.01))
		{
			PreCrossProduct = CurCrossProduct;
		}
		else
		{
			if (FVector::DotProduct(PreCrossProduct, CurCrossProduct) < 0)
			{
				return false;
			}
		}
	}
	return true;
}

void UDSToolLibrary::SortVertices(const TArray<FDSVerticeIndexs>& InVerticesArr, TArray<FDSVerticeIndexs>& OutVerticesArr)
{
	if (InVerticesArr.Num() <= POLYGON_LEAST_CONVEX_NUM)
	{
		OutVerticesArr = InVerticesArr;
		return;
	}

	//index 0 , 1 is default, no need consider
	OutVerticesArr.Empty();
	OutVerticesArr.Add(InVerticesArr[0]);
	OutVerticesArr.Add(InVerticesArr[1]);

	TArray<FDSVerticeIndexs> ConsiderArr = InVerticesArr;
	//remove old index 0, 1
	ConsiderArr.RemoveAt(0);
	ConsiderArr.RemoveAt(0);
	/*
	*  @@ sort anti-clockwise, consider left \ down point priority
	*  @@ Align Point Is Max Priority
	*  @@ 0_______ X
	*  @@ |
	*  @@ |
	*  @@ | Y
	*/
	RecurseSort(OutVerticesArr, ConsiderArr);
}

void UDSToolLibrary::RecurseSort(TArray<FDSVerticeIndexs>& AlreadySortVertice, TArray<FDSVerticeIndexs> ConsiderVerticesArr)
{
	if (ConsiderVerticesArr.IsEmpty() || AlreadySortVertice.IsEmpty())
	{
		return;
	}

	FDSVerticeIndexs LastPoint = AlreadySortVertice.Last();
	TArray<FDSVerticeIndexs> NegXAxisAlign;
	TArray<FDSVerticeIndexs> NegXPosYArea;
	TArray<FDSVerticeIndexs> PosYAxisAlign;
	TArray<FDSVerticeIndexs> PosXPosYArea;
	TArray<FDSVerticeIndexs> PosXAxisAlign;
	TArray<FDSVerticeIndexs> PosXNegYArea;
	TArray<FDSVerticeIndexs> NegYAxisAlign;
	TArray<FDSVerticeIndexs> NegXNegYArea;
	SplitVerticesToFourArea(LastPoint, ConsiderVerticesArr,
	                        NegXAxisAlign, NegXPosYArea, PosYAxisAlign, PosXPosYArea,
	                        PosXAxisAlign, PosXNegYArea, NegYAxisAlign, NegXNegYArea
	);
	if (NegXAxisAlign.Num() > 0)
	{
		AddNextVertice(AlreadySortVertice, LastPoint, NegXAxisAlign);
	}
	else if (NegXPosYArea.Num() > 0)
	{
		AddNextVertice(AlreadySortVertice, LastPoint, NegXPosYArea);
	}
	else if (PosYAxisAlign.Num() > 0)
	{
		AddNextVertice(AlreadySortVertice, LastPoint, PosYAxisAlign);
	}
	else if (PosXPosYArea.Num() > 0)
	{
		AddNextVertice(AlreadySortVertice, LastPoint, PosXPosYArea);
	}
	else if (PosXAxisAlign.Num() > 0)
	{
		AddNextVertice(AlreadySortVertice, LastPoint, PosXAxisAlign);
	}
	else if (PosXNegYArea.Num() > 0)
	{
		AddNextVertice(AlreadySortVertice, LastPoint, PosXNegYArea);
	}
	else if (NegYAxisAlign.Num() > 0)
	{
		AddNextVertice(AlreadySortVertice, LastPoint, NegYAxisAlign);
	}
	else if (NegXNegYArea.Num() > 0)
	{
		AddNextVertice(AlreadySortVertice, LastPoint, NegXNegYArea);
	}

	FDSVerticeIndexs NewLast = AlreadySortVertice.Last();
	ConsiderVerticesArr.Remove(NewLast);
	if (ConsiderVerticesArr.IsEmpty())
	{
		return;
	}

	RecurseSort(AlreadySortVertice, ConsiderVerticesArr);
}

void UDSToolLibrary::SplitVerticesToFourArea(const FDSVerticeIndexs& BaseVertice, const TArray<FDSVerticeIndexs>& ConsiderVerticesArr,
                                             TArray<FDSVerticeIndexs>& NegXAxisAlign,
                                             TArray<FDSVerticeIndexs>& NegXPosYArea,
                                             TArray<FDSVerticeIndexs>& PosYAxisAlign,
                                             TArray<FDSVerticeIndexs>& PosXPosYArea,
                                             TArray<FDSVerticeIndexs>& PosXAxisAlign,
                                             TArray<FDSVerticeIndexs>& PosXNegYArea,
                                             TArray<FDSVerticeIndexs>& NegYAxisAlign,
                                             TArray<FDSVerticeIndexs>& NegXNegYArea
)
{
	FVector BasePoint = BaseVertice.Point;
	for (const auto& CVA : ConsiderVerticesArr)
	{
		if (FMath::IsNearlyEqual(CVA.Point.X, BasePoint.X, 0.01))
		{
			if (CVA.Point.Y > BasePoint.Y)
			{
				PosYAxisAlign.Add(CVA);
			}
			else
			{
				NegYAxisAlign.Add(CVA);
			}
		}
		else if (CVA.Point.X < BasePoint.X)
		{
			if (FMath::IsNearlyEqual(CVA.Point.Y, BasePoint.Y, 0.01))
			{
				NegXAxisAlign.Add(CVA);
			}
			else if (CVA.Point.Y > BasePoint.Y)
			{
				NegXPosYArea.Add(CVA);
			}
			else
			{
				NegXNegYArea.Add(CVA);
			}
		}
		else
		{
			if (FMath::IsNearlyEqual(CVA.Point.Y, BasePoint.Y, 0.01))
			{
				PosXAxisAlign.Add(CVA);
			}
			else if (CVA.Point.Y > BasePoint.Y)
			{
				PosXPosYArea.Add(CVA);
			}
			else
			{
				PosXNegYArea.Add(CVA);
			}
		}
	}
}

void UDSToolLibrary::AddNextVertice(TArray<FDSVerticeIndexs>& AlreadySortVertice, const FDSVerticeIndexs& BasePoint, TArray<FDSVerticeIndexs> SplitAreaData)
{
	//add closest point
	if (SplitAreaData.Num() <= 0)
	{
		return;
	}
	SplitAreaData.Sort(
		[&BasePoint](const FDSVerticeIndexs& Iter1, const FDSVerticeIndexs& Iter2)-> bool
		{
			double Dis1 = FVector::DistSquared(Iter1.Point, BasePoint.Point);
			double Dis2 = FVector::DistSquared(Iter2.Point, BasePoint.Point);
			return Dis1 < Dis2;
		}
	);
	AlreadySortVertice.Add(SplitAreaData[0]);
}

void UDSToolLibrary::MergeMeshVertices(const TArray<FVector>& InVertices, const TArray<int32>& InTriangles, const TArray<FVector>& InNormals, TArray<FVector>& OutVertices, TArray<int32>& OutTriangles, TArray<FVector>& OutNormals)
{
	TArray<int32> OldToNewMapping;
	OldToNewMapping.Init(INDEX_NONE, InVertices.Num());

	TArray<FVector> UniqueVertices;
	TArray<FVector> AccumulatedNormals;
	TMap<FVector, int32> PositionToIndex;

	for (int32 Index = 0; Index < InVertices.Num(); Index++)
	{
		const FVector& Vertex = InVertices[Index];

		const TPair<FVector, int32>* FoundPair = Algo::FindByPredicate(PositionToIndex, [&Vertex](const TPair<FVector, int32>& InPair) { return InPair.Key.Equals(Vertex, 0.01f); });

        if (FoundPair != nullptr)
		{
			OldToNewMapping[Index] = FoundPair->Value;
			AccumulatedNormals[FoundPair->Value] += InNormals[Index];
		}
		else
		{
			int32 NewIndex = UniqueVertices.Num();
			UniqueVertices.Add(Vertex);
			AccumulatedNormals.Add(InNormals[Index]);
			PositionToIndex.Add(Vertex, NewIndex);
			OldToNewMapping[Index] = NewIndex;
		}
	}

	for (FVector& Normal : AccumulatedNormals)
	{
		Normal.Normalize();
	}

	TArray<int32> RemappedTriangles;
	RemappedTriangles.SetNum(InTriangles.Num());
	for (int32 Index = 0; Index < InTriangles.Num(); Index++)
	{
		RemappedTriangles[Index] = OldToNewMapping[InTriangles[Index]];
	}

	OutVertices = MoveTemp(UniqueVertices);
	OutNormals = MoveTemp(AccumulatedNormals);
	OutTriangles = MoveTemp(RemappedTriangles);
}

void UDSToolLibrary::ModifyOverlapBySnap(ADSBaseView* ModifyActor, const TArray<ADSBaseView*>& SnapActors, TArray<AActor*>& TopOverlappingActor)
{
	if (ModifyActor == nullptr || ModifyActor->GetModel() == nullptr)
	{
		return;
	}
	TArray<AActor*> OverlappingActors;
	ModifyActor->GetOverlappingActors(OverlappingActors, ADSBaseView::StaticClass());
	if (OverlappingActors.Num() <= 0)
	{
		return;
	}
	for (auto& SA : SnapActors)
	{
		if (SA == nullptr)
		{
			continue;
		}

		OverlappingActors.RemoveAll(
			[SA](AActor* InActor)
			{
				if (InActor == nullptr)
				{
					return false;
				}
				AActor* Owner = InActor->GetAttachParentActor();
				return (Owner == SA || InActor == SA);
			}
		);
	}
	for (auto& OA : OverlappingActors)
	{
		if (OA == nullptr)
		{
			continue;
		}
		AActor* Owner = OA->GetAttachParentActor();
		if (Owner == nullptr)
		{
			TopOverlappingActor.AddUnique(OA);
		}
		else
		{
			TopOverlappingActor.AddUnique(Owner);
		}
	}
	/*if (OverlappingActors.Num() <= 0 && ModifyActor->GetModel()->IsHasModelFlag(EModelState::E_Overlap))
	{
		ModifyActor->GetModel()->OnExecuteAction(EActionCommandType::E_UnOverlap);
	}*/
}

TArray<FVector> UDSToolLibrary::CalculateCollisionConvexByNormal(const TArray<FVector>& InVertices, const TArray<FVector>& InNormals, const double& Offset)
{
	TArray<FVector> ResultVertices = InVertices;
	TMap<FVector, FVector> CombinedMap;

	for (int32 Index = 0; Index < ResultVertices.Num(); Index++)
	{
		if (!CombinedMap.Contains(ResultVertices[Index]))
		{
			CombinedMap.Add(ResultVertices[Index], InNormals[Index] * Offset);
		}
		else
		{
			CombinedMap[ResultVertices[Index]] += InNormals[Index] * Offset;
		}
	}

	for (FVector& Vertex : ResultVertices)
	{
		if (CombinedMap.Contains(Vertex))
		{
			Vertex += CombinedMap[Vertex];
		}
	}

	return ResultVertices;
}

TArray<FVector> UDSToolLibrary::CalculateCollisionConvexByNormal(const TArray<FVector>& InVertices, const TArray<FVector>& InNormals, const FVector InScale3D, const double& Offset)
{
	TArray<FVector> ResultVertices = InVertices;
	TMap<FVector, FVector> CombinedMap;

	FVector InvScale3D = FVector(1.0f / InScale3D.X, 1.0f / InScale3D.Y, 1.0f / InScale3D.Z);

	for (int32 Index = 0; Index < ResultVertices.Num(); Index++)
	{
		if (!CombinedMap.Contains(ResultVertices[Index]))
		{
			CombinedMap.Add(ResultVertices[Index], InNormals[Index] * Offset * InvScale3D);
		}
		else
		{
			CombinedMap[ResultVertices[Index]] += InNormals[Index] * Offset * InvScale3D;
		}
	}

	for (FVector& Vertex : ResultVertices)
	{
		if (CombinedMap.Contains(Vertex))
		{
			Vertex += CombinedMap[Vertex];
		}
	}

	return ResultVertices;
}

bool UDSToolLibrary::VerifyMultiPretentClear(UDSBaseModel* NewModel)
{
	if (NewModel != nullptr)
	{
		EDSModelType VerifyType = NewModel->GetModelType();
		return VerifyType == EDSModelType::E_Gizmo;
	}
	return false;
}

bool UDSToolLibrary::VerifyMultiSelectToClear(UDSBaseModel* MultiMouse, UDSBaseModel* UnderMouse)
{	
	if (IsValid(MultiMouse) && UDSToolLibrary::IsMultiOperator(MultiMouse) && !UDSToolLibrary::IsGizmoType(UnderMouse) && !UDSToolLibrary::IsInMultiSelect(UnderMouse))
	{
		return true;
	}
	return false;
}

bool UDSToolLibrary::IsGizmoType(UDSBaseModel* EditModel)
{
	if (EditModel != nullptr)
	{
		return EditModel->GetModelType() == EDSModelType::E_Gizmo;
	}
	return false;
}

bool UDSToolLibrary::IsMultiSelectType(UDSBaseModel* EditModel)
{
	if (DS_MODEL_VALID_FOR_USE(EditModel))
	{
		return EditModel->GetModelType() == EDSModelType::E_MultiSelect;
	}
	return false;
}

bool UDSToolLibrary::IsMultiOperator(UDSBaseModel* EditModel)
{
	return IsMultiSelectType(EditModel);
}

bool UDSToolLibrary::IsInMultiSelect(UDSBaseModel* EditModel)
{
	if (DS_MODEL_VALID_FOR_USE(EditModel))
	{
		return EditModel->IsInMultiSelect();
	}
	return false;
}

bool UDSToolLibrary::IsGroupType(UDSBaseModel* EditModel)
{
	if (DS_MODEL_VALID_FOR_USE(EditModel))
	{
		return EditModel->GetModelType() == EDSModelType::E_Group;
	}
	return false;
}

bool UDSToolLibrary::IsGroupOperator(UDSBaseModel* EditModel)
{
	return IsGroupType(EditModel);
}

bool UDSToolLibrary::IsInGroup(UDSBaseModel* EditModel)
{
	if (DS_MODEL_VALID_FOR_USE(EditModel))
	{
		return EditModel->IsInGroup();
	}
	return false;
}

bool UDSToolLibrary::IsGroupComponent(UDSBaseModel* EditModel)
{
	if (DS_MODEL_VALID_FOR_USE(EditModel))
	{
		return EditModel->IsInGroup();
	}
	return false;
}

bool UDSToolLibrary::IsMultiGroupOperatorForRevoke(UDSBaseModel* EditModel)
{
	return IsMultiSelectType(EditModel) || IsGroupType(EditModel);
}

bool UDSToolLibrary::IsCounterTopType(UDSBaseModel* EditModel)
{
	if (IsValid(EditModel))
	{
		EDSModelType ModelType = EditModel->GetModelType();
		return ModelType == EDSModelType::E_Generated_CounterTop || ModelType == EDSModelType::E_Generated_SideCounterTop;
	}
	return false;
}

bool UDSToolLibrary::IsMultiIncludeInGroup(UDSBaseModel* EditModel, FString& InlcudeGroupUUID)
{
	if (EditModel != nullptr && Cast<UDSMultiModel>(EditModel) != nullptr)
	{
		for (auto& IM : Cast<UDSMultiModel>(EditModel)->GetIncludeModel())
		{
			if (IM != nullptr && UDSToolLibrary::IsInGroup(IM))
			{
				InlcudeGroupUUID = IM->GetGroupUUID();
				return true;
			}
		}
	}
	return false;
}

bool UDSToolLibrary::IsMultiIncludeInGroupNoRet(UDSBaseModel* EditModel)
{
	FString MultiGroupUUID;
	return UDSToolLibrary::IsMultiIncludeInGroup(EditModel, MultiGroupUUID);
}

UDSBaseModel* UDSToolLibrary::GetMultiIncludeOwner(UDSBaseModel* EditModel)
{
	if (EditModel != nullptr && Cast<UDSMultiModel>(EditModel) != nullptr)
	{
		for (auto& IM : Cast<UDSMultiModel>(EditModel)->GetIncludeModel())
		{
			if (IM != nullptr)
			{
				return UDSToolLibrary::GetOwnerModelRecursion(IM);
			}
		}
	}
	return nullptr;
}

void UDSToolLibrary::SyncView(UDSBaseModel* EditModel)
{
	if (DS_MODEL_VALID_FOR_USE(EditModel))
	{
		if (EditModel->GetModelType() == EDSModelType::E_MultiSelect || EditModel->GetModelType() == EDSModelType::E_Group)
		{
			if (!Cast<UDSMultiModel>(EditModel) || EditModel->GetOwnedView() == nullptr)
			{
				return;
			}
			EditModel->GetOwnedView()->SetActorTransform(EditModel->GetProperty()->GetActualTransform());
			for (auto& IM : Cast<UDSMultiModel>(EditModel)->GetIncludeModel())
			{
				if (!DS_MODEL_VALID_FOR_USE(IM))
				{
					continue;
				}
				if (IM->GetOwnedView() == nullptr)
				{
					continue;
				}

				IM->GetOwnedView()->AttachToActor(EditModel->GetOwnedView(), FAttachmentTransformRules::KeepWorldTransform);
			}
		}
	}
}

void UDSToolLibrary::ReleaseView(UDSBaseModel* EditModel)
{
	if (DS_MODEL_VALID_FOR_USE(EditModel))
	{
		if (EditModel->GetModelType() == EDSModelType::E_MultiSelect || EditModel->GetModelType() == EDSModelType::E_Group)
		{
			if (Cast<UDSMultiModel>(EditModel) && Cast<UDSMultiModel>(EditModel)->IsOnlyData())
			{
				//only multi select, no attach view --- normal for component select
				return;
			}
			for (auto& IM : Cast<UDSMultiModel>(EditModel)->GetIncludeModel())
			{
				if (!DS_MODEL_VALID_FOR_USE(IM))
				{
					continue;
				}
				if (IM->GetOwnedView() == nullptr)
				{
					continue;
				}

				if (IM->GetOwnedView()->GetAttachParentActor() == EditModel->GetOwnedView())
				{
					IM->GetOwnedView()->DetachFromActor(FDetachmentTransformRules::KeepWorldTransform);
				}

				SyncPropertyByView(IM->GetOwnedView(), IM->GetPropertySharedPtr());
			}
		}
	}
}

FBox UDSToolLibrary::CalculateBox(const TArray<UDSBaseModel*>& Models)
{
	FBox Bound = FBox(ForceInit);
	for (const auto& MS : Models)
	{
		if (DS_MODEL_VALID_FOR_USE(MS))
		{
			const FBox ItemBox = MS->GetBoundBox();
			Bound += ItemBox;
		}
	}
	return Bound;
}

FBox UDSToolLibrary::CalculateLocalBox(const FBox& WorldBox, const FTransform& ToTrans)
{
	FBox Res(ForceInit);
	if (WorldBox.IsValid)
	{
		FVector BoxCenter, BoxExtend;
		WorldBox.GetCenterAndExtents(BoxCenter, BoxExtend);
		Res = WorldBox.MoveTo(FVector::ZeroVector);
	}
	return Res;
}

FBox UDSToolLibrary::CalculateLocalBox(UDSBaseModel* InModel, const FTransform& Trans, const FBox& NoRotLocalBoxs)
{
	FBox Res(ForceInit);
	if (DS_MODEL_VALID_FOR_USE(InModel) && NoRotLocalBoxs.IsValid)
	{
		const FTransform& WorldToActor = Trans.Inverse();
		const FTransform& ModelToWorld = InModel->GetOwnedView()->GetTransform();
		const FTransform& Component = InModel->GetOwnedView()->ActorToWorld();
		const FTransform& ModelToActor = ModelToWorld * WorldToActor;
		Res = NoRotLocalBoxs.TransformBy(ModelToActor);
	}
	return Res;
}

void UDSToolLibrary::CalculateLocalBoxRetCenter(const TArray<UDSBaseModel*>& InModels, const FTransform& Trans, FBox& OutLocalBound, FVector& OutCenter)
{
	FBox WorldBound = FBox(ForceInit);
	FBox LocalBound = FBox(ForceInit);
	TArray<FBox> AllWorldBound;
	for (auto& IM : InModels)
	{
		if (!DS_MODEL_VALID_FOR_USE(IM))
		{
			continue;
		}

		FBox ItemWorldBox = IM->GetBoundBox();
		FBox ItemLocalBox = CalculateLocalBoundBox_Inner(Trans, ItemWorldBox);

		WorldBound += ItemWorldBox;
		LocalBound += ItemLocalBox;

		AllWorldBound.Add(ItemWorldBox);
	}

	FVector LocalCenter = LocalBound.GetCenter();
	OutCenter = Trans.TransformPosition(LocalCenter);
	FTransform RealTrans = FTransform(Trans.GetRotation(), OutCenter);
	LocalBound = CalculateLocalBoundBox_Inner(RealTrans, LocalBound);
	OutLocalBound = LocalBound.MoveTo(FVector::ZeroVector);


#if 0

	//rotator at point FVector::ZeroVector
	FTransform Trans = FTransform(Trans.GetRotation());
	FBox ThisOriginAxisLocalBound(ForceInit);
	for (auto& IM : InModels)
	{
		if (!DS_MODEL_VALID_FOR_USE(IM)) continue;

		FBox ItemWorldBox = IM->GetBoundBox();
		FBox ItemLocalBox = UDSToolLibrary::CalculateLocalBoundBox_Inner(Trans, ItemWorldBox);

		ThisOriginAxisLocalBound += ItemLocalBox;
	}

	FBox OriginWorldBound = UDSToolLibrary::CalculateWorldBoundBox_Inner(Trans, ThisOriginAxisLocalBound);
	OutCenter = OriginWorldBound.GetCenter();

	//calculate local bound
	FTransform RealTrans = FTransform(Trans.GetRotation(), OutCenter);
	OutLocalBound = UDSToolLibrary::CalculateLocalBoundBox_Inner(RealTrans, OriginWorldBound);

#endif
}

void UDSToolLibrary::CalculateBoxOutlineInfo(const FBox& BoundBox, FOutlineInfo& OutlineInfo)
{
	if (BoundBox.IsValid)
	{
		FVector Max = BoundBox.Max;
		FVector Min = BoundBox.Min;
		const double XExtend = (Max - Min).X;
		const double YExtend = (Max - Min).Y;
		const double ZExtend = (Max - Min).Z;

		TArray<FVector> BoxEightPoint = {
			Min + FVector(0.0, 0.0, ZExtend),
			Min + FVector(XExtend, 0.0, ZExtend),
			Max,
			Min + FVector(0.0, YExtend, ZExtend),
			Min,
			Min + FVector(XExtend, 0.0, 0.0),
			Max - FVector(0.0, 0.0, ZExtend),
			Min + FVector(0.0, YExtend, 0.0),
		};

		//left hand , point out box panel
		OutlineInfo.TopOutline = {
			BoxEightPoint[0],
			BoxEightPoint[1],
			BoxEightPoint[2],
			BoxEightPoint[3]
		};
		OutlineInfo.BottomOutline = {
			BoxEightPoint[4],
			BoxEightPoint[5],
			BoxEightPoint[6],
			BoxEightPoint[7]
		};

		OutlineInfo.FrameOutline.Empty();
		//panel upper
		OutlineInfo.FrameOutline.Add(OutlineInfo.TopOutline);
		//panel down
		OutlineInfo.FrameOutline.Add(OutlineInfo.BottomOutline);
		//panel left
		OutlineInfo.FrameOutline.Add({
			BoxEightPoint[4],
			BoxEightPoint[5],
			BoxEightPoint[1],
			BoxEightPoint[0]
		});
		//panel right
		OutlineInfo.FrameOutline.Add({
			BoxEightPoint[7],
			BoxEightPoint[3],
			BoxEightPoint[2],
			BoxEightPoint[6]
		});
		//panel front
		OutlineInfo.FrameOutline.Add({
			BoxEightPoint[4],
			BoxEightPoint[0],
			BoxEightPoint[3],
			BoxEightPoint[7]
		});
		//panel back
		OutlineInfo.FrameOutline.Add({
			BoxEightPoint[5],
			BoxEightPoint[6],
			BoxEightPoint[2],
			BoxEightPoint[1]
		});
	}
}

void UDSToolLibrary::CalculateBoxOutlineInfo_Local(const FBox& LocalBoundBox, const FTransform& Trans,
                                                   FOutlineInfo& OutlineInfo)
{
	FOutlineInfo LocalOutLine;
	CalculateBoxOutlineInfo(LocalBoundBox, LocalOutLine);
	OutlineInfo = TransformOutLine(LocalOutLine, Trans);
}

FOutlineInfo UDSToolLibrary::TransformOutLine(const FOutlineInfo& InOutLine, const FTransform& Trans)
{
	FOutlineInfo Res = InOutLine;
	//top
	TransformOutLine_Inner(InOutLine.TopOutline, Trans, Res.TopOutline);

	//button
	TransformOutLine_Inner(InOutLine.BottomOutline, Trans, Res.BottomOutline);

	//frame
	for (int32 i = 0; i < InOutLine.FrameOutline.Num(); ++i)
	{
		TransformOutLine_Inner(InOutLine.FrameOutline[i], Trans, Res.FrameOutline[i]);
	}

	//complex
	for (int32 i = 0; i < InOutLine.ComplexOutline.Num(); ++i)
	{
		if (InOutLine.ComplexOutline.IsValidIndex(i) && Res.ComplexOutline.IsValidIndex(i))
		{
			auto& InComplex = InOutLine.ComplexOutline[i];
			auto& ResComplex = Res.ComplexOutline[i];
			TransformOutLinePoint_Inner(InComplex.Key, Trans, ResComplex.Key);
			TransformOutLinePoint_Inner(InComplex.Value, Trans, ResComplex.Value);
		}
	}

	return Res;
}

void UDSToolLibrary::TransformOutLine_Inner(const TArray<FVector>& InOutLine, const FTransform& Trans,
                                            TArray<FVector>& OutLineRes)
{
	for (int32 i = 0; i < InOutLine.Num(); ++i)
	{
		if (OutLineRes.IsValidIndex(i) && InOutLine.IsValidIndex(i))
		{
			//local to world
			TransformOutLinePoint_Inner(InOutLine[i], Trans, OutLineRes[i]);
			//OutLineRes[i] = Trans.TransformPosition(InOutLine[i]);
		}
	}
}

void UDSToolLibrary::TransformOutLinePoint_Inner(const FVector& InOutLinePoint, const FTransform& Trans, FVector& OutLineResPoint)
{
	OutLineResPoint = Trans.TransformPosition(InOutLinePoint);
}

TMap<UDSBaseModel*, FBox> UDSToolLibrary::CalculateIncludeModelLocalBoundBox(const FTransform& Trans,
                                                                             const TArray<UDSBaseModel*>& IncludeModel)
{
	TMap<UDSBaseModel*, FBox> Res;
	for (auto& IM : IncludeModel)
	{
		FBox LocalBoundBox = CalculateIncludeModelLocalBoundBox_Inner(Trans, IM);
		if (LocalBoundBox.IsValid)
		{
			Res.Add(IM, LocalBoundBox);
		}
	}

	return Res;
}

FBox UDSToolLibrary::CalculateIncludeModelLocalBoundBox_Inner(const FTransform& Trans, UDSBaseModel* IncludeModel)
{
	FBox Res(ForceInit);
	if (DS_MODEL_VALID_FOR_USE(IncludeModel))
	{
		FBox BoundBox = IncludeModel->GetBoundBox();
		if (BoundBox.IsValid)
		{
			//world to local
			Res = CalculateLocalBoundBox_Inner(Trans, BoundBox);
			/*Res = BoundBox;
			Res.Min = Trans.InverseTransformPosition(BoundBox.Min);
			Res.Max = Trans.InverseTransformPosition(BoundBox.Max);*/
		}
	}
	return Res;
}

FBox UDSToolLibrary::CalculateIncludeModelLocalBoundBox_NoRot(const FTransform& Trans, UDSBaseModel* IncludeModel)
{
	FBox Res(ForceInit);
	if (DS_MODEL_VALID_FOR_USE(IncludeModel))
	{
		//local bound
		FBox TransLocalBound = CalculateLocalBoundBox_Inner(Trans, IncludeModel->GetBoundBox());
		//Res = TransLocalBound;

		//reverse rot
		FTransform ReversRotTrans = FTransform(Trans.GetRotation() * (-1.0));
		FBox ReverseBound = TransLocalBound.TransformBy(ReversRotTrans);
		Res = ReverseBound;
	}

	return Res;
}

FBox UDSToolLibrary::CalculateLocalBoundBox_Inner(const FTransform& Trans, const FBox& InBoundBox)
{
	FBox Res = InBoundBox;
	if (InBoundBox.IsValid)
	{
		Res.Min = Trans.InverseTransformPosition(InBoundBox.Min);
		Res.Max = Trans.InverseTransformPosition(InBoundBox.Max);
	}
	return Res;
}

FBox UDSToolLibrary::CalculateWorldBoundBox_Inner(const FTransform& Trans, const FBox& InBoundBox)
{
	FBox Res = InBoundBox;
	if (InBoundBox.IsValid)
	{
		Res.Min = Trans.TransformPosition(InBoundBox.Min);
		Res.Max = Trans.TransformPosition(InBoundBox.Max);
	}
	return Res;
}

FBox UDSToolLibrary::CalculateLocalBoundBox(const TMap<UDSBaseModel*, FBox>& IncludeLocalBoundBox)
{
	FBox Res(ForceInit);
	for (auto& ILBB : IncludeLocalBoundBox)
	{
		if (!DS_MODEL_VALID_FOR_USE(ILBB.Key))
		{
			continue;
		}

		Res += ILBB.Value;
	}
	return Res;
}

FBox UDSToolLibrary::GetViewBoundBoxByModel(UDSBaseModel* EditModel)
{
	if (DS_MODEL_VALID_FOR_USE(EditModel) && EditModel->GetOwnedView() != nullptr)
	{
		return EditModel->GetOwnedView()->GetBoundBox();
	}
	return FBox();
}

void UDSToolLibrary::SyncPropertyByBoundBox(const FBox& InBoundBox, const TSharedPtr<FDSBaseProperty>& ModifyProperty)
{
	if (ModifyProperty.IsValid())
	{
		FVector Center, Extend;
		InBoundBox.GetCenterAndExtents(Center, Extend);
		//取BOX的下表面为Z轴值
		Center.Z = InBoundBox.Min.Z;
		ModifyProperty->GetTransformPropertyRef().SetLocation(Center);


		//回填尺寸
		FDSSizeProperty SizeProperty;
		SizeProperty.AnchorType = EDSAnchorType::E_Center;
		//SizeProperty.Width = Extend.Y * 20.0;
		SizeProperty.Width = Extend.X * 20.0;
		//SizeProperty.Depth = Extend.X * 20.0;
		SizeProperty.Depth = Extend.Y * 20.0;
		SizeProperty.Height = Extend.Z * 20.0;
		SizeProperty.DistanceToFloor = Center.Z;
		ModifyProperty->SetSizeProperty(SizeProperty);
	}
}

void UDSToolLibrary::SyncPropertyByView(const ADSBaseView* EditView, const TSharedPtr<FDSBaseProperty>& ModifyProperty)
{
	if (EditView != nullptr && ModifyProperty.IsValid())
	{
		FVector RealLocation = EditView->GetActorLocation();
		FRotator RealRotation = EditView->GetActorRotation();
		FTransform RealTrans;
		RealTrans.SetLocation(RealLocation);
		RealTrans.SetRotation(RealRotation.Quaternion());
		ModifyProperty->SetTransformProperty(RealTrans);
	}
}

void UDSToolLibrary::SyncPropertyByModel(UDSBaseModel* EditModel)
{
	if (DS_MODEL_VALID_FOR_USE(EditModel) && EditModel->GetPropertySharedPtr().IsValid() && EditModel->GetOwnedView() != nullptr)
	{
		FVector RealLocation = EditModel->GetOwnedView()->GetActorLocation();
		FRotator RealRotation = EditModel->GetOwnedView()->GetActorRotation();
		FTransform RealTrans;
		RealTrans.SetLocation(RealLocation);
		RealTrans.SetRotation(RealRotation.Quaternion());
		EditModel->GetPropertySharedPtr()->SetTransformProperty(RealTrans);

		EditModel->UpdateOutlineInfo();
	}
}

void UDSToolLibrary::SetCameraLocation(const FVector& Location)
{
}

ECameraType UDSToolLibrary::GetCameraType()
{
	return Cast<ACameraPawn>(ADesignStationController::Get()->GetPawn())->GetCameraType();
}

double UDSToolLibrary::FormatAngleFrom0To360(const double& InAngle)
{
	double Res = InAngle;
	while (Res > 360.0)
	{
		Res -= 360.0;
	}
	while (Res < 0)
	{
		Res += 360.0;
	}
	return Res;
}

void UDSToolLibrary::FormatRotFrom0To360(FRotator& Rot)
{
	Rot.Roll = UDSToolLibrary::FormatAngleFrom0To360(Rot.Roll);
    Rot.Pitch = UDSToolLibrary::FormatAngleFrom0To360(Rot.Pitch);
    Rot.Yaw = UDSToolLibrary::FormatAngleFrom0To360(Rot.Yaw);
}

UDSBaseModel* UDSToolLibrary::VerfiyModel(UDSBaseModel* EditModel, bool Is2D)
{
	UDSBaseModel* Res = EditModel;
	if (IsValid(EditModel))
	{
		EDSModelType CurType = EditModel->GetModelType();
		bool IsNoActionModelFor2D = CurType == EDSModelType::E_Custom_Sink || CurType == EDSModelType::E_WaterTap
			|| CurType == EDSModelType::E_Custom_Stove || CurType == EDSModelType::E_Custom_RangeHood;
		
		if (IsNoActionModelFor2D && Is2D)
		{
			Res = nullptr;
		}
	}
	return Res;
}

UDSBaseModel* UDSToolLibrary::VerifyGroupModel(UDSBaseModel* EditModel)
{
	UDSBaseModel* Res = EditModel;
	if (IsValid(EditModel))
	{
		if (UDSToolLibrary::IsGroupComponent(EditModel))
		{
			UDSBaseModel* GroupModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(EDSModelType::E_Group, EditModel->GetGroupUUID());
			Res = GroupModel;
		}
	}
	return Res;
}

FRotator UDSToolLibrary::GetAxisRelativeRot(const FQuat& WorldQuat, const FString& InLinkUUID, const FQuat& InBaseQuat)
{
	FRotator RelativeRotator = WorldQuat.Rotator();
	FQuat QuatBase = InBaseQuat;
	if (!InLinkUUID.IsEmpty())
	{
		auto UUID = UDSModelDependencySubsystem::GetInstance()->FindCupboardByStove(InLinkUUID);
		if (!UUID.IsEmpty())
		{
			auto CupboardModel = UDSMVCSubsystem::GetInstance()->GetModelByID(UUID);
			if (CupboardModel)
			{
				FRotator Base = CupboardModel->GetPropertySharedPtr()->GetTransformProperty().GetRotation();
				QuatBase = Base.Quaternion();
				
			}
		}
	}
	RelativeRotator = (WorldQuat * QuatBase.Inverse()).Rotator();
	return RelativeRotator;
}

FVector UDSToolLibrary::GetAxisNewLocation(const FVector& InCTLoc, const double& ModelWidth, const double& ModelDepth, const FRotator& BaseRot, const FRotator& RelRot)
{
	FVector OrgPos = FGeometryLibrary::ConvertCenterToLeftBackRotated(InCTLoc, ModelWidth, ModelDepth);

	FQuat Quat1 = FQuat(BaseRot);
	FQuat Quat2 = FQuat(RelRot);
	FQuat CombinedQuat = Quat1 * Quat2;
	FRotator CombinedRotator = CombinedQuat.Rotator();
	FVector FinalCenter = FGeometryLibrary::RotatePointsAroundCenter(InCTLoc, CombinedRotator, OrgPos);
	return FinalCenter;
}

void UDSToolLibrary::CheckAndSyncOriginSize(TSharedPtr<FDSFurnitureBaseProperty> CheckProperty)
{
	if (CheckProperty.IsValid() && !CheckProperty->ScaleProperty.GetIsSync())
	{
		CheckProperty->ScaleProperty.SetOriginalSize(CheckProperty->SizeProperty);
	}
}

void UDSToolLibrary::GetCrossSectionParamsFromFile(const FString& Id, TMap<FString, FParameterData>& OutParams)
{
	FString CrossSectionFilePath = FPaths::Combine(FPaths::ProjectSavedDir(), TEXT("Resources"), Id + TEXT(".dat"));
	FRefToLocalFileData FileData;
	if (!FPaths::FileExists(CrossSectionFilePath) || !FProtobufOperatorFunctionLibrary::LoadRelationFromFile(CrossSectionFilePath, FileData))
	{
		return ;
	}

	if (!FileData.ComponentDatas.IsEmpty() || FileData.FileData.component_datas.IsEmpty())
	{
		return ;
	}

	FSingleComponentProperty ComponentProperty;
	ComponentProperty.ComponentItems = FileData.FileData.component_datas;

	TArray<FParameterData> ComponentParams = FileData.ParamDatas;

	// Collect params from component.
	for (TPair<FString, FParameterData>& Pair : OutParams)
	{
		int32 ParamPos = ComponentParams.IndexOfByPredicate([&](const FParameterData& InParam){ return InParam.Data.name.Equals(Pair.Key); });
		if (ParamPos != INDEX_NONE)
		{
			Pair.Value = ComponentParams[ParamPos];
		}
	}
}

TArray<FVector> UDSToolLibrary::CalculateCrossSectionPointsFromFile(const FString& Id, const TMap<FString, FString>& Params)
{
	TArray<FVector> Result;
	
	FString CrossSectionFilePath = FPaths::Combine(FPaths::ProjectSavedDir(), TEXT("Resources"), Id + TEXT(".dat"));
	FRefToLocalFileData FileData;
	if (!FPaths::FileExists(CrossSectionFilePath) || !FProtobufOperatorFunctionLibrary::LoadRelationFromFile(CrossSectionFilePath, FileData))
	{
		return Result;
	}

	if (!FileData.ComponentDatas.IsEmpty() || FileData.FileData.component_datas.IsEmpty())
	{
		return Result;
	}

	FSingleComponentProperty ComponentProperty;
	ComponentProperty.ComponentItems = FileData.FileData.component_datas;

	TArray<FParameterData> ComponentParams = FileData.ParamDatas;

	// Set params to component.
	for (const TPair<FString, FString>& Pair : Params)
	{
		int32 ParamPos = ComponentParams.IndexOfByPredicate([&](const FParameterData& InParam){ return InParam.Data.name.Equals(Pair.Key); });
		if (ParamPos != INDEX_NONE)
		{
			ComponentParams[ParamPos].Data.value = Pair.Value;
			ComponentParams[ParamPos].Data.expression = Pair.Value;
		}
	}
	
	FGeometryDatas::CalculateParameterValue(UDSMVCSubsystem::GetInstance()->GetGlobalParamsMap(), URefRelationFunction::ConvertParamsArrayToMap(ComponentParams), ComponentProperty);

	TMap<int32, TArray<FVector>> CrossSections;
	FCrossSectionGenerator::SingleCrossSectionVertexPath(ComponentProperty.ComponentItems[0].OperatorSection, CrossSections);
	for (const TPair<int32, TArray<FVector>>& Pair : CrossSections)
	{
		for (const FVector& Point : Pair.Value)
		{
			if (Result.Num() > 0)
			{
				if (FVector::Distance(Point, Result.Last()) < 0.1)
				{
					continue;
				}
			}
			Result.Add(Point);
		}
	}

	return Result;
}
void UDSToolLibrary::SetAxisComponentStatus(UStaticMeshComponent*& InAxis, bool bIsVisi)
{
	if (InAxis != nullptr)
	{
		InAxis->SetVisibility(bIsVisi, true);
		InAxis->SetCollisionEnabled(bIsVisi ? ECollisionEnabled::QueryOnly : ECollisionEnabled::NoCollision);
	}
}

bool UDSToolLibrary::IsModelZAxisFlip(UDSBaseModel* InModel)
{
	if (InModel != nullptr)
	{
		EDSModelType CurType = InModel->GetModelType();
		return CurType == EDSModelType::E_Custom_Stove || CurType == EDSModelType::E_Custom_RangeHood;
	}
	return false;
}

void UDSToolLibrary::GetRealModelForHoverState(const EDSSelectType& InSelectType, UDSBaseModel* OldModel, UDSBaseModel* NewModel, UDSBaseModel*& NewParentModel)
{
	if (InSelectType == EDSSelectType::E_Whole_Part_Select)
	{
		UDSBaseModel* OldParentModel = UDSToolLibrary::GetOwnerModelRecursion(OldModel);
		NewParentModel = UDSToolLibrary::GetOwnerModelRecursion(NewModel);
		if (OldParentModel == NewParentModel)
		{
			bool bNewModelIsFunctional = UDSCupboardLibrary::IsFunctionalCupboardModel(NewModel);

			UDSBaseModel* OldFunctionalOwner = UDSCupboardLibrary::IsFunctionalCupboardModel(OldModel) ? OldModel : UDSCupboardLibrary::FindNearestFunctionalCupboardModel(OldModel);
			UDSBaseModel* NewFunctionalOwner = bNewModelIsFunctional ? NewModel : UDSCupboardLibrary::FindNearestFunctionalCupboardModel(NewModel);
			if (OldFunctionalOwner != nullptr)
			{
				if (NewFunctionalOwner != nullptr)
				{
					if (bNewModelIsFunctional || OldFunctionalOwner != NewFunctionalOwner)
					{
						NewParentModel = NewFunctionalOwner;
					}
					else
					{
						NewParentModel = nullptr;
					}
				}
			}
			else
			{
				if (NewFunctionalOwner != nullptr)
				{
					NewParentModel = NewFunctionalOwner;
				}
				else
				{
					NewParentModel = nullptr;
				}
			}
		}
		else if (UDSToolLibrary::IsInGroup(NewParentModel))
		{
			NewParentModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(EDSModelType::E_Group, NewParentModel->GetGroupUUID());
		}
	}
}

bool UDSToolLibrary::GroupHasPlaceCeilingModel(UDSMultiModel* InGroup)
{
	if (!DS_MODEL_VALID_FOR_USE(InGroup))
		return false;

	UDSMultiModel* GroupModel = Cast<UDSMultiModel>(InGroup);
	TArray<UDSBaseModel*> AllChildModels = GroupModel->GetIncludeModel();

	for (auto ChildIte : AllChildModels)
	{
		if (DS_MODEL_VALID_FOR_USE(ChildIte))
		{
			if (IsPlaceCeilingModel(ChildIte))
			{
				return true;
			}
		}
	}

	return false;
}

bool UDSToolLibrary::GroupOnlyCeilingModel(UDSMultiModel* InGroup)
{
	if (!DS_MODEL_VALID_FOR_USE(InGroup))
		return false;

	UDSMultiModel* GroupModel = Cast<UDSMultiModel>(InGroup);
	TArray<UDSBaseModel*> AllChildModels = GroupModel->GetIncludeModel();

	if (AllChildModels.Num() > 0)
	{
		for (auto ChildIte : AllChildModels)
		{
			if (DS_MODEL_VALID_FOR_USE(ChildIte))
			{
				if (!IsPlaceCeilingModel(ChildIte))
				{
					return false;
				}
			}
		}
	}
	else
	{
		return false;
	}

	return true;
}

bool UDSToolLibrary::IsCeilingModel(UDSBaseModel* InModel)
{
	if (!DS_MODEL_VALID_FOR_USE(InModel))
		return false;

	if (InModel->GetModelType() == EDSModelType::E_RoofArea || InModel->GetModelType() == EDSModelType::E_Furniture_MoldingCeiling)
	{
		return true;
	}

	return false;
}

bool UDSToolLibrary::IsRoomWindDoor(UDSBaseModel* InModel)
{
	if (InModel != nullptr)
	{
		EDSModelType ModelType = InModel->GetModelType();
		return ModelType == EDSModelType::E_House_Window || ModelType == EDSModelType::E_House_Door;
	}
	return false;
}

bool UDSToolLibrary::IsPlaceCeilingModel(UDSBaseModel* InModel)
{
	if (!DS_MODEL_VALID_FOR_USE(InModel))
		return false;

	if (IsCeilingModel(InModel))
		return true;

	if (InModel->GetModelType() == EDSModelType::E_Furniture_HouseFurniture)
	{
		TSharedPtr<FDSSoftFurnitureProperty> SoftProp = InModel->GetTypedProperty<FDSSoftFurnitureProperty>();
		if (SoftProp.IsValid() && !SoftProp->BusinessInfo.Id.IsEmpty() && SoftProp->BusinessInfo.PlacementRules.Equals(TEXT("XD")))
		{
			return true;
		}
	}

	return false;
}

TArray<FVector> UDSToolLibrary::GetOBBWorld8Point(const double& InW, const double& InD, const double& InH, const FTransform& InWorldTrans, bool NeedFlipH /*= false*/)
{
	FVector BoxSize;
	BoxSize.X = InW * 0.1f;
	BoxSize.Y = InD * 0.1f;
	BoxSize.Z = InH * 0.1f;
	if (NeedFlipH)
	{
		BoxSize.Z *= (-1.0);
	}

	/**
	 * GetVertices Return 8 points.
	 * Vertices order :

	 *		  Z	
			  |
     *        |          
	 *       4+++++++++++7
	 *      + +         ++
	 *     +  +        + +
	 *    +   +       +  +
	 *   5+++++++++++6   +
	 *   +    +      +   +
	 *   +    0++++++++++3 ------X
	 *   +   +       +  +
	 *   + +         + +
	 *   1+++++++++++2
     *  /
	 * Y
     *
     *  
	 */ 

	TArray<FVector> ResultPoints;
	ResultPoints.Add(FVector::ZeroVector);
	ResultPoints.Add(FVector(0, BoxSize.Y, 0));
	ResultPoints.Add(FVector(BoxSize.X, BoxSize.Y, 0));
	ResultPoints.Add(FVector(BoxSize.X, 0, 0));

	ResultPoints.Add(FVector(0, 0, BoxSize.Z));
	ResultPoints.Add(FVector(0, BoxSize.Y, BoxSize.Z));
	ResultPoints.Add(FVector(BoxSize.X, BoxSize.Y, BoxSize.Z));
	ResultPoints.Add(FVector(BoxSize.X, 0, BoxSize.Z));

	for (FVector& Point : ResultPoints)
	{
		Point = InWorldTrans.TransformPosition(Point);
	}

	return ResultPoints;
}

FOutlineInfo UDSToolLibrary::CalculateOutlineByOBB(const FBox& RelativeBox, const FTransform& RealTrans)
{
	FOutlineInfo OutlineInfo;

	FVector EightVertices[8];
	RelativeBox.GetVertices(EightVertices);

	//trans to world
	//FTransform RealTrans = GetPropertySharedPtr()->GetActualTransform();
	for (int32 i = 0; i < 8; ++i)
	{
		EightVertices[i] = RealTrans.TransformPosition(EightVertices[i]);
	}


	/**
	 * GetVertices Return 8 points.
	 * Vertices order :
	 *
	 *       1+++++++++++5
	 *      + +         ++
	 *     +  +        + +
	 *    +   +       +  +
	 *
	 *   6+++++++++++7   +
	 *   +    +      +   +
	 *   +    0++++++++++3
	 *   +   +       +  +
	 *   + +         + +
	 *   2+++++++++++4
	 */

	TArray<FVector> TopLines = { EightVertices[1], EightVertices[5], EightVertices[7], EightVertices[6] };
	TArray<FVector> BottomLines = { EightVertices[0], EightVertices[3], EightVertices[4], EightVertices[2] };
	OutlineInfo.TopOutline = TopLines;
	OutlineInfo.BottomOutline = BottomLines;

	OutlineInfo.FrameOutline.Empty();
	OutlineInfo.FrameOutline.Add(OutlineInfo.TopOutline);
	OutlineInfo.FrameOutline.Add(OutlineInfo.BottomOutline);
	OutlineInfo.FrameOutline.Add({ EightVertices[1], EightVertices[0], EightVertices[2], EightVertices[6] });
	OutlineInfo.FrameOutline.Add({ EightVertices[6], EightVertices[7], EightVertices[4], EightVertices[2] });
	OutlineInfo.FrameOutline.Add({ EightVertices[7], EightVertices[5], EightVertices[3], EightVertices[4] });
	OutlineInfo.FrameOutline.Add({ EightVertices[5], EightVertices[1], EightVertices[0], EightVertices[3] });

	return OutlineInfo;
}

void UDSToolLibrary::GenerateModelDragDropEvent(UDSBaseModel* InModel)
{
	TSharedPtr<FSlateUser> SlateUser = FSlateApplication::Get().GetUser(0);
	static const TSet<FKey> PressedButtons = { EKeys::LeftMouseButton };
	FPointerEvent MouseEvent = FPointerEvent(
		SlateUser->GetUserIndex(),
		10,
		SlateUser->GetCursorPosition(),
		SlateUser->GetPreviousCursorPosition(),
		PressedButtons,
		EKeys::Invalid,
		0,
		FModifierKeysState()
	);
	
	UDSMVCSubsystem::GetInstance()->GetStateMachine()->PushPressedKey(EKeys::LeftMouseButton, InModel);
	UDSMVCSubsystem::GetInstance()->GetStateMachine()->OnDragDetected(UDSUISubsystem::GetInstance()->GetUserInputDispatcher()->GetTickSpaceGeometry(), MouseEvent);
	FWidgetPath WidgetPath = SlateUser->GetLastWidgetsUnderCursor().ToWidgetPath();
	FSlateApplication::Get().ProcessExternalReply(WidgetPath, FReply::Handled().BeginDragDrop(FDSModelDragDropOperation::New(MouseEvent, InModel)), SlateUser->GetUserIndex());
}

FPointerEvent UDSToolLibrary::MapPointerToDispatcher(const FGeometry& InGeometry, const FPointerEvent& InPointerEvent)
{
	FVector2D OldLocalPos = InGeometry.AbsoluteToLocal(InPointerEvent.GetScreenSpacePosition());
	FVector2D OldLastLocalPos = InGeometry.AbsoluteToLocal(InPointerEvent.GetLastScreenSpacePosition());

	FVector2D ViewportSize = InGeometry.GetLocalSize();

	FGeometry DispatcherGeometry = UDSUISubsystem::GetInstance()->GetUserInputDispatcher()->GetCachedGeometry();
	FVector2D NewLocalPos = DispatcherGeometry.GetLocalSize() * (OldLocalPos / ViewportSize);
	FVector2D NewLastLocalPos = DispatcherGeometry.GetLocalSize() * (OldLastLocalPos / ViewportSize);

	FVector2D NewScreenPos = DispatcherGeometry.LocalToAbsolute(NewLocalPos);
	FVector2D NewLastScreenPos = DispatcherGeometry.LocalToAbsolute(NewLastLocalPos);
	
	return FPointerEvent::MakeTranslatedEvent(InPointerEvent, FVirtualPointerPosition(NewScreenPos, NewLastScreenPos));
}

FDragDropEvent UDSToolLibrary::MapDragDropToDispatcher(const FGeometry& InGeometry,
	const FDragDropEvent& InDragDropEvent)
{
	return FDragDropEvent(MapPointerToDispatcher(InGeometry, InDragDropEvent), InDragDropEvent.GetOperation());
}

FVector2D UDSToolLibrary::GetMousePositionInViewport()
{
	if (ADesignStationController* Controller = ADesignStationController::Get())
	{
		FVector2D ScreenPos = FSlateApplication::Get().GetCursorPos();

		UGameViewportClient* ViewportClient = Controller->GetWorld()->GetGameViewport();
		FVector2D LocalScreenPos = ViewportClient->GetGameViewportWidget()->GetTickSpaceGeometry().AbsoluteToLocal(ScreenPos);
		return LocalScreenPos;
	}
	return FVector2D();
}

bool UDSToolLibrary::IsValidDecimalValue(const FString& InValue, int32 FractionalDigits)
{
	FRegexPattern RegexPattern(TEXT(""));
	if (FractionalDigits == 0)
	{
		RegexPattern = FRegexPattern("^[+-]?\\d+$");
	}
	else
	{
		RegexPattern = FRegexPattern(FString::Printf(TEXT("^[+-]?\\d+(\\.\\d{1,%d})?$"), FractionalDigits));
	}

	return FRegexMatcher(RegexPattern, InValue).FindNext();
}

TArray<FDSStyleDefaultOption> UDSToolLibrary::CollectAppliedStyleDefaultOptions(const FApplyStyleData& InApplyStyleData)
{
	TArray<FDSStyleDefaultOption> DefaultOptions;
	const FRefToStyleFile& OriginalStyle = UDSMVCSubsystem::GetInstance()->GetOriginalStyleData();
	for (const FRefToContentData& Content : OriginalStyle.content_datas)
	{
		if (!Content.style_option_checks.Contains(InApplyStyleData.ApplyStyleData.style_id))
		{
			continue;
		}

		const FRefToOptionData* FoundOption = InApplyStyleData.ApplySelectOptionData.FindByPredicate([&Content](const FRefToOptionData& InData){ return InData.option_content_id == Content.content_id; });
		if (FoundOption == nullptr)
		{
			continue;
		}

		FDSStyleDefaultOption& NewOption = DefaultOptions.AddDefaulted_GetRef();
		NewOption.ContentId = Content.content_id;
		NewOption.OptionId = FoundOption->option_id;
		NewOption.Parameter = FoundOption->option_params[0];
		NewOption.AssociationType = static_cast<EDSCustomAssociationType>(Content.content_base_type);
		NewOption.CustomTypeCode = Content.content_relation_code;
	}

	return DefaultOptions;
}

TArray<FString> UDSToolLibrary::GetAllCustomCodeNames()
{
	return {
		TEXT("LXGT"), TEXT("LXGNJ"), TEXT("LXMB"), TEXT("LXTZB"), TEXT("LXFB"), TEXT("LXWJ"), TEXT("LXLMZ"), TEXT("LXDX"), TEXT("LXXTX"),
		TEXT("LXTJX"), TEXT("LXCPJJ"), TEXT("LXRZSP"), TEXT("LXDQ"), TEXT("LXSC"), TEXT("LXLT"), TEXT("LXTM"), TEXT("LXGTB"), TEXT("LXMX"),
		TEXT("LXFJPJ"), TEXT("LXKS"), TEXT("LXCPDD"), TEXT("LXLS"), TEXT("LXWJMJ"), TEXT("LXWJLG"), TEXT("LXHXMB"), TEXT("LXBLMX"),TEXT("MRQ")
	};
}

TArray<UDSBaseModel*> UDSToolLibrary::GetOverlapModels(UDSBaseModel* InModel)
{
	TSet<UDSBaseModel*> OverlapModels;
	if (InModel == nullptr)
	{
		return OverlapModels.Array();
	}
	ADSBaseView* ThisView = InModel->GetOwnedView();

	if (ThisView != nullptr)
	{
		//获取自身碰撞
		TArray<AActor*> OverlapActors;
		ThisView->GetOverlappingActors(OverlapActors);

		//获取子组件碰撞
		TArray<AActor*> ChildrenActors;
		TArray<USceneComponent*> AllChildrenComponents;
		ThisView->GetRootComponent()->GetChildrenComponents(true, AllChildrenComponents);
		for (USceneComponent* It : AllChildrenComponents)
		{
			if (It)
			{
				auto OwnerActor = It->GetOwner();
				if (ThisView != OwnerActor)
				{
					ChildrenActors.AddUnique(OwnerActor);
				}
			}
		}
		for (auto& Actor : ChildrenActors)
		{
			if (Actor)
			{
				TArray<AActor*> ChildOverlapActors;
				Actor->GetOverlappingActors(ChildOverlapActors);
				OverlapActors.Append(ChildOverlapActors);
			}
		}

		//转成model
		for (auto& Iter : OverlapActors)
		{
			if (Iter && Iter->IsA<ADSBaseView>() && ThisView != Iter && !ChildrenActors.Contains(Iter))
			{
				OverlapModels.Add(Cast<ADSBaseView>(Iter)->GetModel());
				auto OwnerModel = Cast<ADSBaseView>(Iter)->GetModel()->GetOwnerModel();
				if (OwnerModel && OwnerModel!= ThisView->GetModel())
				{
					OverlapModels.Add(OwnerModel);
				}
			}
		}
	}
	return OverlapModels.Array();
}

void UDSToolLibrary::UpdateOverlap(UDSBaseModel* InModel, const TSet<UDSBaseModel*>& IgnoreModels, bool bEffectOthers)
{
	if (InModel == nullptr)
	{
		return;
	}

	bool bOverlapThis = false;
	bool bOverlapThisParent = false;

	ADSBaseView* ThisView = InModel->GetOwnedView();
	TArray<UDSBaseModel*> OverlapModels = GetOverlapModels(InModel);

	for (auto & Ig : IgnoreModels)
	{
		OverlapModels.Remove(Ig);

		auto OwnedView = Ig->GetOwnedView();
		if (OwnedView == nullptr)
		{
			continue;
		}
		TArray<USceneComponent*> AllChildrenComponents;
		OwnedView->GetRootComponent()->GetChildrenComponents(true, AllChildrenComponents);
		for (USceneComponent* It : AllChildrenComponents)
		{
			auto ItView = It->GetOwner();
			if (ItView && Cast<ADSBaseView>(ItView))
			{
				OverlapModels.Remove(Cast<ADSBaseView>(ItView)->GetModel());
			}
		}
	}

	if (ThisView != nullptr && !OverlapModels.IsEmpty())
	{
		ADSBaseView* ThisParentView = Cast<ADSBaseView>(ThisView->GetAttachParentActor());

		for (auto& M : OverlapModels)
		{
			bool bOverlapOther = false;
			bool bOverlapOtherParent = false;

			ADSBaseView* OtherView = UDSMVCSubsystem::GetInstance()->GetView(M);
			if (ThisView == OtherView)
			{
				continue;
			}
			ADSBaseView* OtherParentView = OtherView ? Cast<ADSBaseView>(OtherView->GetAttachParentActor()) : nullptr;

			if (ThisParentView == OtherParentView)
			{
				bOverlapThis = true;
				bOverlapOther = true;
			}
			else
			{
				if (ThisParentView == nullptr && OtherParentView != nullptr && ThisView!= OtherParentView)
				{
					bOverlapThis = true;
					bOverlapOtherParent = true;
				}
				else if (ThisParentView == nullptr && OtherParentView != nullptr && OtherView != ThisParentView)
				{
					bOverlapOther = true;
					bOverlapThisParent = true;
				}
				else if (ThisParentView != nullptr && OtherParentView != nullptr)
				{
					bOverlapThisParent = true;
					bOverlapOtherParent = true;
				}
			}

			if (bEffectOthers)
			{
				if (bOverlapOtherParent && OtherParentView != nullptr && OtherParentView->GetModel())
				{
					OtherParentView->GetModel()->OnExecuteAction(FDSModelExecuteType::ExecuteOverlap, FDSBroadcastMarkData::NotBroadcastToMVCMark);
				}
				else if (bOverlapOther)
				{
					M->OnExecuteAction(FDSModelExecuteType::ExecuteOverlap, FDSBroadcastMarkData::NotBroadcastToMVCMark);
				}
			}
		}

		if (bOverlapThisParent && ThisParentView && ThisParentView->GetModel())
		{
			ThisParentView->GetModel()->OnExecuteAction(FDSModelExecuteType::ExecuteOverlap, FDSBroadcastMarkData::NotBroadcastToMVCMark);
		}
		else if (bOverlapThis)
		{
			InModel->OnExecuteAction(FDSModelExecuteType::ExecuteOverlap, FDSBroadcastMarkData::NotBroadcastToMVCMark);
		}
	}
	else if (ThisView != nullptr)
	{
		UpdateOverlapInner(InModel);
	}
}

void UDSToolLibrary::UpdateOverlapInner(UDSBaseModel* InModel)
{
	if (InModel == nullptr || !InModel->IsA<UDSCupboardModel>())
	{
		return;
	}
	auto ThisView = InModel->GetOwnedView();
	if (ThisView)
	{
		TArray<UDSBaseModel*> ChildrenModels;
		UDSCupboardLibrary::GetAllChildrenModels(Cast<UDSCupboardModel>(InModel)->GetComponentTreeDataRef(), ChildrenModels);
		TArray<AActor*> ChildrenActors;

		for (auto& ChildModel : ChildrenModels)
		{
			auto ChildView = ChildModel->GetOwnedView();
			if (ChildView)
			{
				ChildrenActors.AddUnique(ChildView);
			}
		}

		//TArray<USceneComponent*> AllChildrenComponents;
		//ThisView->GetRootComponent()->GetChildrenComponents(true, AllChildrenComponents);
		//for (USceneComponent* It : AllChildrenComponents)
		//{
		//	if (It)
		//	{
		//		auto OwnerActor = It->GetOwner();
		//		if (ThisView != OwnerActor)
		//		{
		//			ChildrenActors.AddUnique(OwnerActor);
		//		}
		//	}
		//}
		TSet<AActor*>ChildrenActorSet;
		ChildrenActorSet.Append(ChildrenActors);

		for (auto& Actor : ChildrenActors)
		{
			if (Actor)
			{
				TArray<AActor*> ChildOverlapActors;
				Actor->GetOverlappingActors(ChildOverlapActors);
				TSet<AActor*>ChildOverlapActorSet;
				ChildOverlapActorSet.Append(ChildOverlapActors);

				auto Intersection = ChildrenActorSet.Intersect(ChildOverlapActorSet);

				if (!Intersection.IsEmpty() && Cast<ADSBaseView>(Actor))
				{
					auto M = Cast<ADSBaseView>(Actor)->GetModel();
					if (M)
					{
						M->OnExecuteAction(FDSModelExecuteType::ExecuteOverlap,FDSBroadcastMarkData::NotBroadcastToMVCMark);
					}
				}
			}
		}
	}
}

void UDSToolLibrary::UpdateUnOverlap(UDSBaseModel* InModel)
{ 
	if (InModel == nullptr)
	{
		return;
	}
	ADSBaseView* ThisView = UDSMVCSubsystem::GetInstance()->GetView(InModel);
	TArray<UDSBaseModel*> OverlapModels = GetOverlapModels(InModel);
	TSet<UDSBaseModel*> Visited;

	if (ThisView != nullptr)
	{
		UpdateUnOverlap(OverlapModels);

		for (auto& M : OverlapModels)
		{
			UpdateOverlap(M, { InModel }, false);
			Visited.Add(M);
			auto OwnerModel = M->GetOwnerModel();
			if (!Visited.Contains(OwnerModel))
			{
				UpdateOverlap(OwnerModel, { InModel }, false);
				Visited.Add(OwnerModel);
			}
		}

		InModel->OnExecuteAction(FDSModelExecuteType::ExecuteUnOverlap, FDSBroadcastMarkData::NotBroadcastToMVCMark);
	}
}

void UDSToolLibrary::UpdateUnOverlap(const TArray<UDSBaseModel*>& InModels)
{
	for (auto& M : InModels)
	{
		if (M != nullptr)
		{
			M->OnExecuteAction(FDSModelExecuteType::ExecuteUnOverlap);
		}
	}
}

bool UDSToolLibrary::IgnoreActorOverlap(UDSBaseModel* InModel)
{
	//如果包含过滤参数，则不处理

	//if (IsCustomCupboardType(InModel->GetModelType()))
	//{
	//	auto CupboardModel = Cast<UDSCupboardModel>(InModel);
	//	if (CupboardModel == nullptr)
	//	{
	//		return false;
	//	}

	//	auto ThisTree = CupboardModel->GetModelInfo().ComponentTreeData;
	//	if (ThisTree)
	//	{
	//		auto Param = ThisTree->ComponentParameters.FindByPredicate([](const FParameterData& InParam) {return InParam.Data.name.Equals(TEXT("BT1")); });
	//		return Param && FCString::Atoi(*Param->Data.value) == 9;
	//	}
	//}
	return false;
}

bool UDSToolLibrary::IgnoreActorOverlap(UDSBaseModel* InModel, UDSBaseModel* OtherModel)
{
	//如果包含过滤参数，则不处理
	return IgnoreActorOverlap(InModel) || IgnoreActorOverlap(OtherModel);
}

bool UDSToolLibrary::AnyEditableWidgetFocused()
{
	TSharedPtr<SWidget> FocusedWidget = FSlateApplication::Get().GetKeyboardFocusedWidget();
	if (!FocusedWidget)
	{
		return false;
	}

	TArray<FString> FilterNames = { TEXT("SEditableText"), TEXT("SEditableTextBox"), TEXT("SMultiLineEditableText"), TEXT("SMultiLineEditableTextBox") };
	
	FString TypeName = FocusedWidget->GetTypeAsString();
	
	return FilterNames.Contains(TypeName);
}

FString UDSToolLibrary::RSA_OAEP_SHA256_Decrypt_Base64(const FString& Base64Cipher, const FString& PrivatePem)
{
	TArray<uint8> EncryptedData;
	FBase64::Decode(Base64Cipher, EncryptedData);
	if (EncryptedData.Num() == 0)
	{
		throw std::runtime_error("Base64 decoding failed!");
	}
	
	std::string StdPrivatePem = TCHAR_TO_UTF8(*PrivatePem);
	BIO* Bio = BIO_new_mem_buf(StdPrivatePem.data(), (int)StdPrivatePem.size());
	if (Bio == nullptr)
	{
		throw std::runtime_error("Load private key failed!");
	}

	EVP_PKEY* PrivateKey = PEM_read_bio_PrivateKey(Bio, nullptr, nullptr, nullptr);
	BIO_free(Bio);

	EVP_PKEY_CTX* Context = EVP_PKEY_CTX_new(PrivateKey, nullptr);
	if (Context == nullptr)
	{
		EVP_PKEY_free(PrivateKey);
		throw std::runtime_error("Create EVP_PKEY_CTX failed!");
	}

	if (EVP_PKEY_decrypt_init(Context) <= 0)
	{
		EVP_PKEY_CTX_free(Context);
		EVP_PKEY_free(PrivateKey);
		throw std::runtime_error("Decrypt init failed!");
	}

	if (EVP_PKEY_CTX_set_rsa_padding(Context, RSA_PKCS1_OAEP_PADDING) <= 0)
	{
		EVP_PKEY_CTX_free(Context);
		EVP_PKEY_free(PrivateKey);
		throw std::runtime_error("EVP_PKEY_CTX_set_rsa_padding failed!");
	}

	if (EVP_PKEY_CTX_set_rsa_oaep_md(Context, EVP_sha256()) <= 0)
	{
		EVP_PKEY_CTX_free(Context);
		EVP_PKEY_free(PrivateKey);
		throw std::runtime_error("EVP_PKEY_CTX_set_rsa_oaep_md failed!");
	}

	if (EVP_PKEY_CTX_set_rsa_mgf1_md(Context, EVP_sha256()) <= 0)
	{
		EVP_PKEY_CTX_free(Context);
		EVP_PKEY_free(PrivateKey);
		throw std::runtime_error("EVP_PKEY_CTX_set_rsa_mgf1_md failed!");
	}
	
	size_t OutLen = 0;
	if (EVP_PKEY_decrypt(Context, nullptr, &OutLen, EncryptedData.GetData(), EncryptedData.Num()) <= 0)
	{
		EVP_PKEY_CTX_free(Context);
		EVP_PKEY_free(PrivateKey);
		throw std::runtime_error("EVP_PKEY_decrypt failed to get output length!");
	}

	TArray<uint8> OutData;
	OutData.SetNumUninitialized((int32)OutLen);

	if (EVP_PKEY_decrypt(Context, OutData.GetData(), &OutLen, EncryptedData.GetData(), EncryptedData.Num()) <= 0)
	{
		EVP_PKEY_CTX_free(Context);
		EVP_PKEY_free(PrivateKey);
		throw std::runtime_error("EVP_PKEY_decrypt failed to decrypt data!");
	}

	OutData.SetNum((int32)OutLen);

	EVP_PKEY_CTX_free(Context);
	EVP_PKEY_free(PrivateKey);

	FUTF8ToTCHAR Utf8Converter(reinterpret_cast<const ANSICHAR*>(OutData.GetData()), OutLen);
	
	return FString(Utf8Converter.Length(), Utf8Converter.Get());
}

#undef INDENT_CIRCLE
#undef POLYGON_DEFAULT_CONVEX_NUM
