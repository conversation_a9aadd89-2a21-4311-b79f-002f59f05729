// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "DSConstructionCore.h"
#include "SubSystems/MVC/Model/House/Area/DSHouseAreaModel.h"
#include "DSConstructionFrame.h"
#include "DSConstructionPaper.generated.h"

class UDSBaseModel;
class UDSCupboardModel;
class DSConstructionDimensionGenerator;

/**
 * 图纸类
 */
UCLASS()
class DESIGNSTATION_API UDSConstructionPaper : public UObject
{
	GENERATED_BODY()

public:
	struct FDSPaperInitData
	{
		E_ConstructionPaperType   PaperType;
		FString PaperName;   //图纸名称
		FName   FrameName;   //图框名称
		//FVector BasePos = FVector::ZeroVector;
		FTransform WorldToProjective;
		TArray<TSharedPtr<FDSConstructionData>> CupboardDatas;
		TArray<TSharedPtr<FDSConstructionData>>	AreaDatas;
		TArray<UDSHouseAreaModel*> AreaModels;
		//FVector FrameSize = FVector::ZeroVector; //图框尺寸
	};

public:
	static const FString Paper_PlaneBaseCabinet;   //平面地柜图
	static const FString Paper_PlaneWallCabinet;   //平面吊柜图
	static const FString Paper_FrontConstructionCabinet;   //立面柜体结构图
	static const FString Paper_FrontDoorCabinet;   //立面门板图

	//static const TSet<FName> AllPaperNames;

public:
	UDSConstructionPaper();

	void InitPaper(const FDSPaperInitData &InData);

	void DrawPaper();

	void CalcDrawingOffset(FVector InFrameOffset);

	const UDSConstructionFrame* GetFrame() const
	{
		return Frame;
	}

	//填充图框属性定义
	void FillFrameAttDef();
private:

	void CalcRootPrimitive(TSharedPtr<FDSConstructionData> InOutRoot, TArray<TSharedPtr<FDSConstructionData>>& InOutChildren);
	bool IsFrontType() const;   //是否是立面图纸
	bool IsPlaneType() const;   //是否是平面图纸
	void CalcFrontPrimitive(TArray<TSharedPtr<FDSConstructionData>>& InOutDatas);

	void RemovePrimitiveMPolygon3DByNormal(TArray<TSharedPtr<FDSPrimitiveBase>>& InPrimitives, FVector InDir);
	void GetChildCupboardData(const FString &InParentUUID, TArray<TSharedPtr<FDSConstructionData>>& OutDatas);
	UDSHouseAreaModel* GetAreaModelByUUID(const FString& InUUID) const;

	void UpdateFrameScale();
	FBox GetDrawingBox() const;
	FString GetCombineName(const TArray<FString>& InNames, int32 Index);
protected:
	E_ConstructionPaperType   PaperType;
	FString PaperName;   //图纸名称
	FName   FrameName;   //图框名称
	//FVector BasePos = FVector::ZeroVector; 
	FTransform WorldToProjective; //世界坐标系到图纸坐标系的转换矩阵 , Z轴为纸面法线方向
	TMap<FString, TSharedPtr<FDSConstructionData>> CupboardDataMap;   //柜子图纸数据列表
	TMap<FString, TSharedPtr<FDSConstructionData>> AreaDataMap;       //户型图纸数据列表

	TArray<TWeakObjectPtr<UDSHouseAreaModel>> AreaModels; //房间模型指针

	TSharedPtr<class FDSConstructionDimensionGeneratorBase> DimensionGenerator; //标注生成器

	UPROPERTY()
	UDSConstructionFrame* Frame = nullptr;   //图框对象

	FVector FrameOffsetInCAD   = FVector::ZeroVector;     //图框偏移量
	FVector DrawingOffsetInCAD = FVector::ZeroVector;   //绘制偏移量
};
