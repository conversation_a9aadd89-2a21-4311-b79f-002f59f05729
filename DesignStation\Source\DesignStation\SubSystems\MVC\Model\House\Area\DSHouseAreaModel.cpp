﻿#pragma once

#include "DSHouseAreaModel.h"

#include "Clipper2/Library/Clipper2Library.h"
#include "Subsystems/Camera/DSCameraSubsystem.h"
#include "Subsystems/Drawing/DSDrawingSubsystem.h"
#include "Subsystems/MVC/DSMVCSubsystem.h"
#include "SubSystems/MVC/Core/Macro.h"
#include "Subsystems/MVC/Core/Property/DoorAndWindowProperty.h"
#include "SubSystems/MVC/Core/Property/HouseAreaProperty.h"
#include "SubSystems/MVC/Model/Custom/ModelInfo/DSDataDefine.h"
#include "Subsystems/MVC/Model/DoorAndWindow/DSWindowModel.h"
#include "SubSystems/MVC/Model/House/HouseFurniture/DSHouseWinDoorModel.h"
#include "SubSystems/MVC/Model/House/Wall/DSHouseWallModel.h"
#include "GeometricCalculate/Library/MaterialLibrary.h"
#include "SubSystems/Resource/DSResourceSubsystem.h"
#include "DesignStation/BasicClasses/DesignStationController.h"

extern const FString MT_DefaultFloor;

extern const FString MT_RegionHover;

extern const FString MT_RegionSelect;

UDSHouseAreaModel::UDSHouseAreaModel()
	: LabelModel(nullptr), LinkRoofModel(nullptr), LinkCeilingAreaModel(nullptr)
{
	ModelType = EDSModelType::E_House_Area;
}

void UDSHouseAreaModel::InitProperty()
{
	Property = MakeShared<FDSHouseAreaProperty>();
	Property->SetProductProperty(TEXT("Area"), TEXT(""));

	FDSMaterialProperty DefaultMaterialProperty;

	FDSMaterialInfo MaterialInfo2D;
	MaterialInfo2D.Index = 0;
	MaterialInfo2D.Id.Empty();
	MaterialInfo2D.MaterialPath = TEXT("Basic_Floor");
	MaterialInfo2D.USize = 500;
	MaterialInfo2D.VSize = 500;
	
	FDSMaterialInfo MaterialInfo3D;
	MaterialInfo3D.Index = 0;
	MaterialInfo3D.Id.Empty();
	MaterialInfo3D.MaterialPath = TEXT("Basic_Floor_3D");
	MaterialInfo3D.USize = 500;
	MaterialInfo3D.VSize = 500;
	DefaultMaterialProperty.MaterialInfo2D.Add(MaterialInfo2D);
	DefaultMaterialProperty.MaterialInfo3D.Add(MaterialInfo3D);

	Property->SetMaterialProperty(DefaultMaterialProperty);
}

void UDSHouseAreaModel::InitAdditionData()
{
	ModelState = FDSModelState(
		FDSModelCreateMark(false, false),
		FDSModelTransformMark(false, false, false),
		FDSModelTransformMark(false, false, false));

	TMap<FString, bool> RulerMark = GetModelRulerMark(ModelType);
	PendantInfo = FPendantInfo(
		FDSAxisMark(false, false, false, false),
		FDSScaleMark(false, false),
		FDSRulerMark(false, false, RulerMark
		             // FDSRuler_Inner(false, false, false, false),
		             // FDSRuler_RectSelf(false, false),
		             // FDSRuler_Inner(false, false, false, false),
		             // FDSRuler_Path(false, false),
		             // FDSRuler_Segment(false),
		             // FDSRuler_OnWall(true, true)
		)
	);
}

void UDSHouseAreaModel::InitMeshInfo()
{
	UpdateMeshInfo();
}

void UDSHouseAreaModel::SetLabelModel(UDSBaseModel* InModel)
{
	DeleteLinkModel(LabelModel);
	LabelModel = InModel;
	AddLinkModel(InModel);
}

UDSBaseModel* UDSHouseAreaModel::GetLabelModel()
{
	return LabelModel;
}

void UDSHouseAreaModel::AddWallModel(UDSBaseModel* InModel, bool bRight)
{
	if (InModel->GetModelType() == EDSModelType::E_House_Wall)
	{
		if (LinkWalls.Contains(InModel))
		{
			LinkWalls[InModel] = bRight;
		}
		else
		{
			LinkWalls.Add(InModel, bRight);
		}
		AddLinkModel(InModel);
	}
}

void UDSHouseAreaModel::DeleteWallModel(UDSBaseModel* InModel)
{
	if (InModel->GetModelType() == EDSModelType::E_House_Wall)
	{
		LinkWalls.Remove(InModel);
		DeleteLinkModel(InModel);
	}
}

void UDSHouseAreaModel::ClearWallModel()
{
	auto LinkWalls = GetWallModels();
	for (auto& Iter : LinkWalls)
	{
		Cast<UDSHouseWallModel>(Iter.Key)->DeleteLinkArea(this);
		DeleteLinkModel(Iter.Key);
	}
	LinkWalls.Empty();
}

TMap<UDSBaseModel*, bool> UDSHouseAreaModel::GetWallModels()
{
	return LinkWalls;
}

void UDSHouseAreaModel::AddSplitLineModel(UDSBaseModel* InModel)
{
	if (InModel->GetModelType() == EDSModelType::E_House_Area_Split_Line)
	{
		LinkSplitLines.Add(InModel);
		AddLinkModel(InModel);
	}
}

void UDSHouseAreaModel::DeleteSplitLineModel(UDSBaseModel* InModel)
{
	if (InModel->GetModelType() == EDSModelType::E_House_Area_Split_Line)
	{
		LinkSplitLines.Remove(InModel);
		DeleteLinkModel(InModel);
	}
}

void UDSHouseAreaModel::ClearSplitLineModel()
{
	for (auto& Iter : LinkSplitLines)
	{
		DeleteLinkModel(Iter);
		Iter->OnExecuteAction(FDSModelExecuteType::ExecuteDeleteSelf);
	}
	LinkSplitLines.Empty();
}

TSet<UDSBaseModel*> UDSHouseAreaModel::GetSplitLineModels()
{
	return LinkSplitLines;
}

void UDSHouseAreaModel::SetRoofModel(UDSBaseModel* InModel)
{
	if (InModel->GetModelType() == EDSModelType::E_House_Roof)
	{
		RemoveRoofModel();
		LinkRoofModel = InModel;
		LinkModels.Add(InModel);
	}
}

UDSBaseModel* UDSHouseAreaModel::GetRoofModel()
{
	return LinkRoofModel;
}

void UDSHouseAreaModel::RemoveRoofModel()
{
	LinkModels.Remove(LinkRoofModel);
	LinkRoofModel = nullptr;
}

void UDSHouseAreaModel::GetCeilingAreaModels(TArray<UDSBaseModel*>& OutCeilingAreaModels)
{
	for (auto ModelIte : LinkModels)
	{
		if (ModelIte.IsValid() && ModelIte->GetModelType() == EDSModelType::E_RoofArea)
		{
			OutCeilingAreaModels.Add(ModelIte.Get());
		}
	}
}

void UDSHouseAreaModel::ClearCeilingAreaModel()
{
	TArray<UDSBaseModel*> CeilingAreaModels;
	GetCeilingAreaModels(CeilingAreaModels);
	for (auto& Iter : CeilingAreaModels)
	{
		LinkModels.Remove(Iter);
	}
}

bool UDSHouseAreaModel::ShouldUseParentModel() const
{
	return false;
}

void UDSHouseAreaModel::SetLinkTopLines(const TArray<UDSBaseModel*>& InModels)
{
	LinkTopLines.Empty();
	LinkTopLines.Append(InModels);
}

TArray<UDSBaseModel*> UDSHouseAreaModel::GetLinkTopLines()
{
	return LinkTopLines.Array();
}

void UDSHouseAreaModel::SetLinkBottomLines(const TArray<UDSBaseModel*>& InModels)
{
	LinkBottomLines.Empty();
	LinkBottomLines.Append(InModels);
}

TArray<UDSBaseModel*> UDSHouseAreaModel::GetLinkBottomLines()
{
	return LinkBottomLines.Array();
}

void UDSHouseAreaModel::ApplyNewStyle(const FApplyStyleData& InNewStyle)
{
	TSharedPtr<FDSHouseAreaProperty> AreaProperty = StaticCastSharedPtr<FDSHouseAreaProperty>(Property);
	AreaProperty->AreaStyle = InNewStyle;

	if (OwnerModel == nullptr)
	{
		for (UDSBaseModel* ChildComponent : ComponentModelSet)
		{
			if (UDSHouseAreaModel* ChildArea = Cast<UDSHouseAreaModel>(ChildComponent))
			{
				ChildArea->ApplyNewStyle(InNewStyle);
			}
		}
	}
}

FApplyStyleData UDSHouseAreaModel::GetApplyStyleData() const
{
	return StaticCastSharedPtr<FDSHouseAreaProperty>(Property)->AreaStyle;
}

void UDSHouseAreaModel::UpdateMeshInfo()
{
	FGeoMesh Mesh;
	auto ChildHoles = GetChildPlanes();
	//if (GetParentPlane() || ChildHoles.IsEmpty())
	{
		auto Prop = static_cast<FDSHouseAreaProperty*>(GetProperty());

		auto Outline = Prop->Points;
		if (ChildHoles.IsEmpty())
		{
			FGeometryLibrary::Delaunay2XY(Outline, TArray<TArray<FVector>>{}, FVector::ZAxisVector, Mesh);
			Prop->AreaSize = Mesh.area * 0.0001f;
		}
		else
		{
			TArray<TArray<FVector>> Holes;
			for (auto& Iter : ChildHoles)
			{
				if (Iter->GetProperty()->ProductProperty.Name.Contains(TEXT("PILLAR")))
				{
					continue;
				}
				auto HP = static_cast<FDSHouseAreaProperty*>(Iter->GetProperty());
				Holes.Add(HP->Points);
			}

			auto NewHoles = FClipper2Library::ComputeOuterContour(Holes, 0);

			for (auto& Hole : NewHoles)
			{
				FGeometryLibrary::OffsetPointsOnContour(Hole, Outline, 0.1);
			}

			FGeometryLibrary::Delaunay2XY(Outline, NewHoles, FVector::ZAxisVector, Mesh);
			Prop->AreaSize = Mesh.area * 0.0001f;
		}

		FDSMeshData MeshData;

		MeshData.Uvs.SetNumZeroed(Mesh.vertexs.Num());
		MeshData.Normals.SetNumZeroed(Mesh.vertexs.Num());

		MeshData.Vertex.SetNumZeroed(Mesh.vertexs.Num());
		for (int32 i = 0; i < Mesh.vertexs.Num(); ++i)
		{
			MeshData.Vertex[i] = Mesh.vertexs[i];
			auto U = FVector::DotProduct(Mesh.vertexs[i], FVector::XAxisVector) * 0.01f;
			auto V = FVector::DotProduct(Mesh.vertexs[i], FVector::YAxisVector) * 0.01f;
			MeshData.Uvs[i] = FVector2D(U, V);
			MeshData.Normals[i] = FVector::ZAxisVector;
		}

		MeshData.Indices.Empty();
		MeshData.Indices = Mesh.indices;
		MeshInfo.MeshDatas.Add(TEXT("0"), MeshData);
	}
}

void UDSHouseAreaModel::UpdateMeshInfoUV()
{
	auto MaterialProperty = Property->MaterialProperty;
	if (MaterialProperty.MaterialInfo3D.IsEmpty())
	{
		return;
	}

	auto MaterialResource = MaterialProperty.MaterialInfo3D[0];
	//uv
	auto Width = MaterialResource.USize * 0.1;
	auto Height = MaterialResource.VSize * 0.1;
	auto USize = Width > 1.0 ? Width : 1000;
	auto VSize = Height > 1.0 ? Height : 1000;

	for (auto& Iter : MeshInfo.MeshDatas)
	{
		Iter.Value.Uvs = FMaterialLibrary::CalculateUVWorld2D(Iter.Value.Vertex, USize, VSize);
	}

	//refresh mesh
	OnExecuteAction(FDSModelExecuteType::ExecuteUVSection);
}

void UDSHouseAreaModel::OnExecute_All(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	Super::OnExecute_All(InExecuteType, BroadcastMarkPtr);
}

void UDSHouseAreaModel::OnExecute_Model_UpdateSelf(const FDSModelExecuteType& InExecuteType,
                                                   const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	UpdateMeshInfo();
	UpdateMeshInfoUV();
	Super::OnExecute_Model_UpdateSelf(InExecuteType, BroadcastMarkPtr);

	auto L = GetLabelModel();
	if (L)
	{
		L->OnExecuteAction(FDSModelExecuteType::ExecuteAll);
	}
}

void UDSHouseAreaModel::OnExecute_Model_Spawn(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	//InitMeshInfo();
	Super::OnExecute_Model_Spawn(InExecuteType, BroadcastMarkPtr);
	UpdateMeshInfoUV();
	OnExecute_Model_RefreshMaterialForce(FDSModelExecuteType::ExecuteRefreshMaterialForce, FDSBroadcastMarkData::NotBroadcastToMVCMark);
	BindCameraTypeChangeHandle();
}

void UDSHouseAreaModel::OnExecute_Model_Delete(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	auto Walls = GetWallModels();

	for (auto& Iter : Walls)
	{
		auto Areas = Cast<UDSHouseWallModel>(Iter.Key)->GetLinkAreas();
		if (Areas.Num() < 2)
		{
			Iter.Key->OnExecuteAction(FDSModelExecuteType::ExecuteDeleteSelf);
		}
	}

	if (LinkRoofModel)
	{
		LinkRoofModel->OnExecuteAction(FDSModelExecuteType::ExecuteDeleteSelf);
		RemoveRoofModel();
	}

	TArray<UDSBaseModel*> CeilingAreaModels;
	GetCeilingAreaModels(CeilingAreaModels);
	for (auto CeilingIte : CeilingAreaModels)
	{
		CeilingIte->OnExecuteAction(FDSModelExecuteType::ExecuteDeleteSelf);
	}
	ClearCeilingAreaModel();

	UDSMVCSubsystem::GetInstance()->OnLayoutRefresh(EDSModelType::E_House_Wall);
}

void UDSHouseAreaModel::OnExecute_Model_DeleteSelf(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	auto Childern = GetChildPlanes();

	for (auto& Iter : Childern)
	{
		Iter->OnExecuteAction(FDSModelExecuteType::ExecuteDeleteSelf);
	}

	auto ParentPlane = GetParentPlane();
	if (DS_MODEL_VALID_FOR_USE(ParentPlane))
	{
		Cast<UDSHouseAreaModel>(ParentPlane)->DeleteChildPlane(this);
		//ParentPlane->OnExecuteAction(FDSModelExecuteType::ExecuteAll);
	}
	if (LabelModel)
	{
		LabelModel->OnExecuteAction(FDSModelExecuteType::ExecuteDeleteSelf);
		LabelModel = nullptr;
	}

	if (LinkRoofModel)
	{
		LinkRoofModel->OnExecuteAction(FDSModelExecuteType::ExecuteDeleteSelf);
		RemoveRoofModel();
	}

	TArray<UDSBaseModel*> CeilingAreaModels;
	GetCeilingAreaModels(CeilingAreaModels);
	for (auto CeilingIte : CeilingAreaModels)
	{
		CeilingIte->OnExecuteAction(FDSModelExecuteType::ExecuteDeleteSelf);
	}
	ClearCeilingAreaModel();

	ClearWallModel();
	ClearSplitLineModel();
	LabelModel = nullptr;
	Super::OnExecute_Model_DeleteSelf(InExecuteType, BroadcastMarkPtr);
}

void UDSHouseAreaModel::OnExecute_Model_Hidden(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	Super::OnExecute_Model_Hidden(InExecuteType, BroadcastMarkPtr);

	if (LabelModel)
	{
		LabelModel->OnExecuteAction(FDSModelExecuteType::ExecuteHidden, BroadcastMarkPtr);
	}
}

void UDSHouseAreaModel::OnExecute_Model_UnHidden(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	Super::OnExecute_Model_UnHidden(InExecuteType, BroadcastMarkPtr);

	if (LabelModel)
	{
		LabelModel->OnExecuteAction(FDSModelExecuteType::ExecuteUnHidden, BroadcastMarkPtr);
	}
}

void UDSHouseAreaModel::OnExecute_Model_AreaHidden(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	bAreaHidden = true;
	//AddModelStateFlag(EModelState::E_Hidden);
	GetModelViewExecuteDelegate().ExecuteIfBound(this, InExecuteType);

	if (LabelModel)
	{
		LabelModel->OnExecuteAction(FDSModelExecuteType::ExecuteHidden, BroadcastMarkPtr);
	}
}

void UDSHouseAreaModel::OnExecute_Model_UnAreaHidden(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	Super::OnExecute_Model_UnAreaHidden(InExecuteType, BroadcastMarkPtr);
	if (LabelModel)
	{
		LabelModel->OnExecuteAction(FDSModelExecuteType::ExecuteUnAreaHidden, BroadcastMarkPtr);
	}
}

void UDSHouseAreaModel::OnExecute_Model_ViewTypeHidden(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	Super::OnExecute_Model_ViewTypeHidden(InExecuteType, BroadcastMarkPtr);

	if (LabelModel)
	{
		LabelModel->OnExecuteAction(FDSModelExecuteType::ExecuteViewTypeHidden, BroadcastMarkPtr);
	}
}

void UDSHouseAreaModel::OnExecute_Model_UnViewTypeHidden(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	Super::OnExecute_Model_UnViewTypeHidden(InExecuteType, BroadcastMarkPtr);

	if (LabelModel && ADesignStationController::Get()->Is2DScene())
	{
		LabelModel->OnExecuteAction(FDSModelExecuteType::ExecuteUnViewTypeHidden, BroadcastMarkPtr);
	}
}

void UDSHouseAreaModel::DeleteArea(bool bDeleteSplitLine)
{
	ClearWallModel();
	if (bDeleteSplitLine)
	{
		ClearSplitLineModel();
	}
	ClearChildPlane();

	if (LabelModel)
	{
		LabelModel->OnExecuteAction(FDSModelExecuteType::ExecuteDeleteSelf);
		LabelModel = nullptr;
	}

	if (LinkRoofModel)
	{
		LinkRoofModel->OnExecuteAction(FDSModelExecuteType::ExecuteDeleteSelf);
		RemoveRoofModel();
	}

	for (auto& BTLine : LinkTopLines)
	{
		BTLine->OnExecuteAction(FDSModelExecuteType::ExecuteDeleteSelf);
	}
	for (auto& BTLine : LinkBottomLines)
	{
		BTLine->OnExecuteAction(FDSModelExecuteType::ExecuteDeleteSelf);
	}

	TArray<UDSBaseModel*> CeilingAreaModels;
	GetCeilingAreaModels(CeilingAreaModels);
	for (auto CeilingIte : CeilingAreaModels)
	{
		CeilingIte->OnExecuteAction(FDSModelExecuteType::ExecuteDeleteSelf);
	}
	ClearCeilingAreaModel();

	if (OnModelPoolDeleteDelegate.IsBound())
	{
		OnModelPoolDeleteDelegate.ExecuteIfBound(this);
	}

	UDSDrawingSubsystem::GetInstance()->DeleteModel(this);

	bIsDeleted = true;
}

UDSBaseModel* UDSHouseAreaModel::GetRootArea()
{
	auto RootArea = this;
	while (RootArea->GetParentPlane() && RootArea->GetParentPlane()->GetModelType() == EDSModelType::E_House_Area)
	{
		RootArea = Cast<UDSHouseAreaModel>(RootArea->GetParentPlane());
	}
	return RootArea;
}

void UDSHouseAreaModel::BindCameraTypeChangeHandle()
{
	if (UDSCameraSubsystem::GetInstance())
	{
		UDSCameraSubsystem::GetInstance()->CameraTypeDelegate.AddUObject(this, &ThisClass::OnCameraTypeChange);
	}
}

void UDSHouseAreaModel::OnCameraTypeChange(int32 InType)
{
	OnExecuteAction(FDSModelExecuteType::ExecuteRefreshMaterialForce);
}

bool UDSHouseAreaModel::CheckIfNeedsStopObserveResourceStatus() const
{
	TArray<FDSMaterialInfo> AllMaterials = Property->MaterialProperty.MaterialInfo2D;
	AllMaterials.Append(Property->MaterialProperty.MaterialInfo3D);
	for (const FDSMaterialInfo& Info : AllMaterials)
	{
		if (Info.Id.IsEmpty())
		{
			continue;
		}

		TSharedPtr<FDSResourceInfo> FoundInfo = UDSResourceSubsystem::GetInstance()->FindResourceFromCache(Info.Id);
		if (!FoundInfo || UDSResourceSubsystem::GetInstance()->FindMaterialFromCache(FoundInfo->GetResourceFile(EDSResourceQuality::Low).MD5) == nullptr)
		{
			return false;
		}
	}
	
	return true;
}

void UDSHouseAreaModel::OnResourceStatusInPoolChanged(const FString& Identify, EDSResourceType ResourceType, EResourceStatusEventType EventType)
{
	FChangeObserveResourceHelper Helper(this);

	if (ResourceType == EDSResourceType::Material)
	{
		bool bNeedRefreshMaterial = false;
		TArray<FDSMaterialInfo> AllMaterials = Property->MaterialProperty.MaterialInfo2D;
		AllMaterials.Append(Property->MaterialProperty.MaterialInfo3D);
		for (const FDSMaterialInfo& Info : AllMaterials)
		{
			if (Info.Id.IsEmpty())
			{
				continue;
			}

			TSharedPtr<FDSResourceInfo> FoundInfo = UDSResourceSubsystem::GetInstance()->FindResourceFromCache(Info.Id);
			if (!FoundInfo)
			{
				continue;
			}

			if (FoundInfo->GetResourceFile(EDSResourceQuality::Low).MD5 == Identify)
			{
				bNeedRefreshMaterial = true;
				break;
			}
		}

		if (bNeedRefreshMaterial)
		{
			OnExecuteAction(FDSModelExecuteType::ExecuteRefreshMaterialForce, FDSBroadcastMarkData::NotBroadcastToMVCMark);	
		}
	}
}

void UDSHouseAreaModel::OnExecute_Model_RefreshMaterialForce(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr)
{
	Super::OnExecute_Model_RefreshMaterialForce(InExecuteType, BroadcastMarkPtr);
}

void UDSHouseAreaModel::OnLinkModelUpdatedHandle(UDSBaseModel* InModel) {}

void UDSHouseAreaModel::OnLinkModelDeletedHandle(UDSBaseModel* InModel)
{
	DeleteWallModel(InModel);
}

void UDSHouseAreaModel::Serialization(TSharedRef<TJsonWriter<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>> JsonWriter)
{
	Super::Serialization(JsonWriter);
}

void UDSHouseAreaModel::Deserialization(const TSharedPtr<FJsonObject>& InJsonData, bool bDeserializeID)
{
	Super::Deserialization(InJsonData, bDeserializeID);
}

const FString UDSHouseAreaModel::GetAreaName() const
{
	if (Property.IsValid())
	{
		FDSHouseAreaProperty* AreaProperty = StaticCast<FDSHouseAreaProperty*>(Property.Get());
		if (AreaProperty)
		{
			return AreaProperty->GetAreaName();
		}
	}
	return FString();
}

void UDSHouseAreaModel::GetWindowsOfArea(TMap<TPair<FVector, FVector>, UDSBaseModel*>& WindowsOfAreas)
{
	if (Property.IsValid())
	{
		FDSHouseAreaProperty* AreaProperty = StaticCast<FDSHouseAreaProperty*>(Property.Get());

		TArray<TArray<FVector>> AllAreaLines;
		for (int i = 0; i < AreaProperty->Points.Num(); ++i)
		{
			TArray<FVector> AreaLine;
			if (i == AreaProperty->Points.Num() - 1)
			{
				AreaLine.Add(AreaProperty->Points[i]);
				AreaLine.Add(AreaProperty->Points[0]);
			}
			else
			{
				AreaLine.Add(AreaProperty->Points[i]);
				AreaLine.Add(AreaProperty->Points[i + 1]);
			}
			AllAreaLines.Add(AreaLine);
		}

		for (auto& Element : UDSMVCSubsystem::GetInstance()->GetModels(EDSModelType::E_House_Window))
		{
			UDSWindowModel* Window = Cast<UDSWindowModel>(Element);
			TSharedPtr<FDSDoorAndWindowProperty> WindowProperty = StaticCastSharedPtr<FDSDoorAndWindowProperty>(Window->GetPropertySharedPtr());

			FVector LeftStart = WindowProperty->GetLeftSegment()->SegmentStart;
			FVector LeftEnd = WindowProperty->GetLeftSegment()->SegmentEnd;

			FVector RightStart = WindowProperty->GetRightSegment()->SegmentStart;
			FVector RightEnd = WindowProperty->GetRightSegment()->SegmentEnd;

			for (auto AllAreaLine : AllAreaLines)
			{
				if (FGeometryLibrary::PointOnSegment(LeftStart, AllAreaLine[0], AllAreaLine[1])
					&& FGeometryLibrary::PointOnSegment(LeftEnd, AllAreaLine[0], AllAreaLine[1]))
				{
					WindowsOfAreas.Add(TPair<FVector, FVector>(WindowProperty->SegmentStart, WindowProperty->SegmentEnd), Window);
					break;
				}
				if (FGeometryLibrary::PointOnSegment(RightStart, AllAreaLine[0], AllAreaLine[1])
					&& FGeometryLibrary::PointOnSegment(RightEnd, AllAreaLine[0], AllAreaLine[1]))
				{
					WindowsOfAreas.Add(TPair<FVector, FVector>(WindowProperty->SegmentStart, WindowProperty->SegmentEnd), Window);
					break;
				}
			}
		}
	}
}
