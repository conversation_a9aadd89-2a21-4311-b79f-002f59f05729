// Fill out your copyright notice in the Description page of Project Settings.


#include "SubSystems/Construction/DSConstructionPaper.h"
#include "Subsystems/MVC/Model/Custom/DSCupboardModel.h"
#include "SubSystems/MVC/Library/DSToolLibrary.h"
#include "DSConstructionLibrary.h"
#include "SubSystems/Library/DesignStationFunctionLibrary.h"
#include "DSConstructionSubsystem.h"
#include "Clipper2/Library/Clipper2Library.h"
#include "SubSystems/Construction/Dimension/DSConstructionDimensionGenerator.h" 
#include <SubSystems/MVC/Core/Property/HouseAreaProperty.h>
#include "SubSystems/Resource/DSResourceSubsystem.h"
#include "Subsystems/DSNetworkSubsystem.h"
#include "Subsystems/MVC/DSMVCSubsystem.h"
#include "SubSystems/File/DSFileSubsystem.h"

const FString UDSConstructionPaper::Paper_PlaneBaseCabinet = FString(TEXT("地柜平面图"));       //平面地柜图纸
const FString UDSConstructionPaper::Paper_PlaneWallCabinet = FString(TEXT("顶柜平面图"));       //平面吊柜图纸
const FString UDSConstructionPaper::Paper_FrontConstructionCabinet = FString(TEXT("结构图"));   //立面柜体结构图纸
const FString UDSConstructionPaper::Paper_FrontDoorCabinet = FString(TEXT("立面图"));       //立面门板图纸


UDSConstructionPaper::UDSConstructionPaper()
{
}

void UDSConstructionPaper::InitPaper(const FDSPaperInitData& InData)
{
	PaperName = InData.PaperName;
	FrameName = InData.FrameName;
	PaperType = InData.PaperType;
	WorldToProjective = InData.WorldToProjective;
	
	for (auto Ite : InData.AreaModels)
	{
		AreaModels.Add(Ite);
	}

	//处理柜子图纸数据
	for (auto& DataIte : InData.CupboardDatas)
	{
		TSharedPtr<FDSConstructionData> NewData = MakeShareable(new FDSConstructionData());
		NewData->DeepCopy(*DataIte);

		for (auto PriIte : NewData->Primitives)
		{
			PriIte->Transform(WorldToProjective);
		}

		RemovePrimitiveMPolygon3DByNormal(NewData->Primitives, FVector::ZAxisVector);

		CupboardDataMap.Add(NewData->SelfUUID, NewData);

		if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), NewData->Node->ModelType))
		{
			 FVector DepthDirInProj = (NewData->LocalToWorld * WorldToProjective).TransformVector(FVector::YAxisVector).GetSafeNormal();
			//生成门的朝向线
			if (IsFrontType() && FVector::DotProduct(DepthDirInProj, FVector::ZAxisVector) > 0)
			{
				FDSPrimitive_Polygon* NewPrimitive = new FDSPrimitive_Polygon();
				NewPrimitive->bIsClose = false;
				NewPrimitive->Linetype = TEXT("DASHEDX2");
				NewPrimitive->ColorID = 30;
				NewData->Primitives.Add(TSharedPtr<FDSPrimitiveBase>(NewPrimitive));
				UDSConstructionLibrary::GetDoorOpenDir2DOutline(Cast<UDSCupboardModel>(NewData->Model.Get()),
					WorldToProjective, NewPrimitive->Points);
			}
		}
	}

	//处理户型数据
	for (auto& DataIte : InData.AreaDatas)
	{
		TSharedPtr<FDSConstructionData> NewData = MakeShareable(new FDSConstructionData());
		NewData->DeepCopy(*DataIte);

		for (auto PriIte : NewData->Primitives)
		{
			PriIte->Transform(WorldToProjective);
		}

		//NewData->DepthDirInProj = (NewData->LocalToWorld * WorldToProjective).TransformVector(FVector::YAxisVector).GetSafeNormal();
		AreaDataMap.Add(NewData->SelfUUID, NewData);
	}

	for (auto& DataIte : InData.CupboardDatas)
	{
		if (DataIte->ParentUUID.IsEmpty())
		{
			TArray<TSharedPtr<FDSConstructionData>> ChildDatas;
			GetChildCupboardData(DataIte->SelfUUID, ChildDatas);

			if (IsPlaneType())  //平面
			{
				//根据这个柜子的子部件，计算柜子的图元
				CalcRootPrimitive(CupboardDataMap[DataIte->SelfUUID], ChildDatas);
				for (auto& ChildData : ChildDatas)
				{
					//剔除子节点图纸数据
					CupboardDataMap.Remove(ChildData->SelfUUID);
				}
			}
			else if (IsFrontType())  //立面
			{
				CalcFrontPrimitive(ChildDatas);

				for (auto& ChildData : ChildDatas)
				{
					//剔除完全被遮挡的子节点图纸数据
					if (!ChildData->IsHasVaildPrimitive())   
					{
						CupboardDataMap.Remove(ChildData->SelfUUID);
					}
				}

			}
		}
	}


	if (IsPlaneType())
	{
		DimensionGenerator = MakeShared<DSConstructionDimensionGenerator>();
		//把墙体和需要标注的柜子数据传入标注生成器
		TArray<TSharedPtr<FDSConstructionData>> ConstructionDatas;
		for (auto& WallIte : AreaDataMap)
		{
			if (WallIte.Value->IsHasVaildPrimitive())
			{
				ConstructionDatas.Add(WallIte.Value);
			}
		}

		for (auto& CupboardIte : CupboardDataMap)
		{
			if (CupboardIte.Value->IsHasVaildPrimitive())
			{
				ConstructionDatas.Add(CupboardIte.Value);
			}
		}

		DimensionGenerator->GenerateDimension(ConstructionDatas);
	}

	//创建图框
	Frame = NewObject<UDSConstructionFrame>(this, UDSConstructionFrame::StaticClass());
	Frame->FrameType = FrameName;
	Frame->SetFrameOuterSizeInCAD(UDSConstructionSubsystem::GetInstance()->GetDefaultFrameOuterSizeInCAD());
	UpdateFrameScale();
	//FillFrameAttDef();
}

void UDSConstructionPaper::DrawPaper()
{
	for (auto& DataIte : CupboardDataMap)
	{
		if (!DataIte.Value->IsHasVaildPrimitive())
			continue;

		FString BlockName = UDSConstructionLibrary::GetBlockName(DataIte.Value->Model.Get(), DataIte.Value->Node,
			GetAreaModelByUUID(DataIte.Value->AreaUUID));

		UDSConstructionSubsystem::GetInstance()->DrawPrimitive(DataIte.Value->Primitives,
			UDSConstructionLibrary::GetLayerName(DataIte.Value->Node, DataIte.Value->ModelType),
			BlockName, DrawingOffsetInCAD);
	}

	for (auto& DataIte : AreaDataMap)
	{
		if (!DataIte.Value->IsHasVaildPrimitive())
			continue;

		FString BlockName = UDSConstructionLibrary::GetBlockName(DataIte.Value->Model.Get(), DataIte.Value->Node, nullptr);

		UDSConstructionSubsystem::GetInstance()->DrawPrimitive(DataIte.Value->Primitives,
			UDSConstructionLibrary::GetLayerName(DataIte.Value->Node, DataIte.Value->ModelType),
			BlockName, DrawingOffsetInCAD);
	}

	if (DimensionGenerator)
	{
		DimensionGenerator->DrawDimension(DrawingOffsetInCAD);
		
	}

	if (Frame != nullptr)
	{
		UDSConstructionSubsystem::GetInstance()->DrawFrame(Frame, FrameOffsetInCAD);
	}

}

void UDSConstructionPaper::CalcDrawingOffset(FVector InFrameOffset)
{
	FrameOffsetInCAD = InFrameOffset - (Frame->GetFrameMinOffsetInCAD() * Frame->ScaleInCAD);

	FBox DrawingBox = GetDrawingBox();
	//FVector DrawingExtentInCAD = UDSConstructionLibrary::UE4ToCAD(DrawingBox.GetExtent());
	FVector OffsetInCAD = UDSConstructionLibrary::UE4ToCAD(DrawingBox.GetCenter());

	DrawingOffsetInCAD = (FrameOffsetInCAD + Frame->GetFrameActiveContentSizeInCAD() * 0.5f) - OffsetInCAD;
}

namespace __Internal__
{
	struct FTempData
	{
		FString UUID;
		FString Name;
		TArray<TArray<FVector>> Points;
		bool bIsDeleted = false;
		float MaxZ = 0.0f;
		int32 PrimitiveIndex = 0;
		FBox GetBox() const
		{
			FBox TotalBox;
			for (auto& Ite : Points)
			{
				TotalBox += FBox(Ite);
			}
			return TotalBox;
		}

		float GetArea() const
		{
			return FClipper2Library::GetAreaSizeCM(Points);
		}
	};
};

void UDSConstructionPaper::CalcRootPrimitive(TSharedPtr<FDSConstructionData> InOutRoot, TArray<TSharedPtr<FDSConstructionData>>& InOutChildren)
{
	TArray< TSharedPtr<FDSConstructionData>> TempConstructionDatas;
	TempConstructionDatas.Add(InOutRoot);
	for (auto Ite : InOutChildren)
	{
		TempConstructionDatas.Add(Ite);
	}

	TArray<__Internal__::FTempData> TempDatas;
	for (auto Ite : TempConstructionDatas)
	{
		if (Ite->IsHasVaildPrimitive())
		{
			for(int32 i = 0 ; i < Ite->Primitives.Num(); ++i)
			{
				auto& PIte = Ite->Primitives[i];
				if (PIte->PrimitiveType == EDSPrimitiveType::PrimitiveType_MPolygon3D)
				{
					TSharedPtr<FDSPrimitive_MPolygon3D> M3D = StaticCastSharedPtr<FDSPrimitive_MPolygon3D>(PIte);
					__Internal__::FTempData NewData;
					NewData.UUID = Ite->SelfUUID;
					NewData.Name = Ite->Node.IsValid() ?  Ite->Node->ComponentName : TEXT("");
					M3D->GetPoints(NewData.Points);
					NewData.MaxZ = NewData.GetBox().Max.Z;
					NewData.PrimitiveIndex = i;

					TempDatas.Add(MoveTemp(NewData));
				}
			}
		}
	}

	if (TempDatas.Num() > 2)
	{
		TempDatas.Sort([](const __Internal__::FTempData& A, const __Internal__::FTempData& B) {
			return A.MaxZ > B.MaxZ;
			});

		//float MaxZ = TempDatas[0].GetBox().Max.Z;
		/*
		TempDatas.Sort([](const FTempData& A, const FTempData& B) {
			FBox ABox = A.GetBox();
			FBox BBox = B.GetBox();

			if (ABox.Max.Z == BBox.Max.Z)
			{
				return A.GetArea() > B.GetArea();
			}
			else
				return false;
			
			});
		*/

		for (int32 i = 0; i < TempDatas.Num(); ++i)
		{
			auto& TopData = TempDatas[i];
			if (TopData.bIsDeleted)
				continue;

			for (int32 j = i + 1; j < TempDatas.Num(); ++j)
			{
				auto& CurData = TempDatas[j];

				if (CurData.bIsDeleted)
					continue;

				TArray<TArray<FVector>> ResultPaths;
				float Area = 0.0f;
				CurData.bIsDeleted = !(FClipper2Library::PolygonDifference02(CurData.Points, TopData.Points, ResultPaths, &Area) && Area > 0.1f);
				if (!CurData.bIsDeleted)
				{
					CurData.Points = MoveTemp(ResultPaths);
				}
			}
		}
	}

	//InOutRoot->Primitives.Empty();

	for (auto& Ite : TempDatas)
	{
		if (Ite.bIsDeleted)
			continue;

		FString UUID = Ite.UUID;
		auto FindData = TempConstructionDatas.FindByPredicate([UUID](const TSharedPtr<FDSConstructionData> A) {
			return A->SelfUUID == UUID;
			});

		if (FindData != nullptr)
		{
			FDSPrimitive_MPolygon* NewPrimitive = new FDSPrimitive_MPolygon();
			NewPrimitive->Points = Ite.Points;
			NewPrimitive->CopyCADAttribute((*FindData)->Primitives[Ite.PrimitiveIndex].Get());

			InOutRoot->Primitives.Add(TSharedPtr<FDSPrimitive_MPolygon>(NewPrimitive));
		}
	}
}

bool UDSConstructionPaper::IsFrontType() const
{
	return PaperType == E_ConstructionPaperType::PaperType_Front_Door || PaperType == E_ConstructionPaperType::PaperType_Front_Construction;
}

bool UDSConstructionPaper::IsPlaneType() const
{
	return PaperType == E_ConstructionPaperType::PaperType_SingleRoomPlane || 
		PaperType == E_ConstructionPaperType::PaperType_MutilRoomPlane;
}

void UDSConstructionPaper::CalcFrontPrimitive(TArray<TSharedPtr<FDSConstructionData>>& InOutDatas)
{
	TArray<__Internal__::FTempData> TempDatas;
	for (auto Ite : InOutDatas)
	{
		if (Ite->IsHasVaildPrimitive())
		{
			for (int32 i = 0; i < Ite->Primitives.Num(); ++i)
			{
				auto& PIte = Ite->Primitives[i];

				if (PIte->PrimitiveType == EDSPrimitiveType::PrimitiveType_MPolygon3D)
				{
					TSharedPtr<FDSPrimitive_MPolygon3D> M3D = StaticCastSharedPtr<FDSPrimitive_MPolygon3D>(PIte);
					__Internal__::FTempData NewData;
					NewData.UUID = Ite->SelfUUID;
					NewData.Name = Ite->Node.IsValid() ? Ite->Node->ComponentName : TEXT("");
					M3D->GetPoints(NewData.Points);
					NewData.MaxZ = NewData.GetBox().Max.Z;
					NewData.PrimitiveIndex = i;

					TempDatas.Add(MoveTemp(NewData));
				}
			}
		}
	}

	if (TempDatas.Num() > 2)
	{
		TempDatas.Sort([](const __Internal__::FTempData& A, const __Internal__::FTempData& B) {
			return A.MaxZ > B.MaxZ;
			});

		for (int32 i = 0; i < TempDatas.Num(); ++i)
		{
			auto& TopData = TempDatas[i];
			if (TopData.bIsDeleted)
				continue;

			for (int32 j = i + 1; j < TempDatas.Num(); ++j)
			{
				auto& CurData = TempDatas[j];

				if (CurData.bIsDeleted)
					continue;

				TArray<TArray<FVector>> ResultPaths;
				float Area = 0.0f;
				CurData.bIsDeleted = !(FClipper2Library::PolygonDifference02(CurData.Points, TopData.Points, ResultPaths, &Area) && Area > 0.1f);
				if (!CurData.bIsDeleted)
				{
					CurData.Points = MoveTemp(ResultPaths);
				}
			}
		}
	}

	for (auto& Ite : TempDatas)
	{
		if (Ite.bIsDeleted)
			continue;

		FString UUID = Ite.UUID;
		auto FindData = InOutDatas.FindByPredicate([UUID](const TSharedPtr < FDSConstructionData> A) {
			return A->SelfUUID == UUID;
			});

		if (FindData != nullptr)
		{
			FDSPrimitive_MPolygon* NewPrimitive = new FDSPrimitive_MPolygon();
			NewPrimitive->Points = Ite.Points;

			for (auto& PointsIte : NewPrimitive->Points)
			{
				for (auto& PIte : PointsIte)
				{
					PIte.Z = Ite.MaxZ;
				}
			}

			NewPrimitive->CopyCADAttribute((*FindData)->Primitives[Ite.PrimitiveIndex].Get());

			//(*FindData)->Primitives.Add(TSharedPtr<FDSPrimitive_MPolygon>(NewPrimitive));
			(*FindData)->Primitives.RemoveAt(Ite.PrimitiveIndex);  //删除原来的图元
			(*FindData)->Primitives.Insert(TSharedPtr<FDSPrimitive_MPolygon>(NewPrimitive), Ite.PrimitiveIndex);  //添加新的图元
		}
	}

	for (auto& Ite : TempDatas)
	{
		if (Ite.bIsDeleted)
		{
			FString UUID = Ite.UUID;
			auto FindData = InOutDatas.FindByPredicate([UUID](const TSharedPtr < FDSConstructionData> A) {
				return A->SelfUUID == UUID;
				});

			if (FindData != nullptr)
			{
				(*FindData)->Primitives.RemoveAt(Ite.PrimitiveIndex);  //删除原来的图元
			}
		}
	}
}

void UDSConstructionPaper::RemovePrimitiveMPolygon3DByNormal(TArray<TSharedPtr<FDSPrimitiveBase>>& InPrimitives, FVector InDir)
{
	InPrimitives.RemoveAll([InDir](const TSharedPtr<FDSPrimitiveBase>& Primitive) {
			if (Primitive->PrimitiveType != EDSPrimitiveType::PrimitiveType_MPolygon3D)
				return false;
			TSharedPtr<FDSPrimitive_MPolygon3D> M3D = StaticCastSharedPtr<FDSPrimitive_MPolygon3D>(Primitive);
			FVector Normal = M3D->FaceNormal;
			float TempValue = FMath::Cos(FMath::DegreesToRadians(89.5));
			return !(FVector::DotProduct(Normal, InDir) > TempValue);
		});
}

void UDSConstructionPaper::GetChildCupboardData(const FString& InParentUUID, TArray<TSharedPtr<FDSConstructionData>>& OutDatas)
{
	OutDatas.Empty();
	for (auto& Ite : CupboardDataMap)
	{
		if (Ite.Value->ParentUUID == InParentUUID)
		{
			OutDatas.Add(Ite.Value);
		}
	}
}

UDSHouseAreaModel* UDSConstructionPaper::GetAreaModelByUUID(const FString& InUUID) const
{
	for (auto& Ite : AreaModels)
	{
		if (Ite.IsValid() && Ite->GetUUID() == InUUID)
		{
			return Ite.Get();
		}
	}

	return nullptr;
}

void UDSConstructionPaper::FillFrameAttDef()
{
	if (Frame == nullptr)
		return;

	static const TArray<FString> DefaultFrameAttrKeys = {
	DSFrameAttDefName::Name_DRAWING_NAME  //图纸名称
	,DSFrameAttDefName::Name_room_name    //区域名称
	,DSFrameAttDefName::Name_style_name   //风格名称
	,DSFrameAttDefName::Name_USERNAME
	,DSFrameAttDefName::Name_USERPHONE
	,DSFrameAttDefName::Name_DESIGNERNAME  //设计师名称
	,DSFrameAttDefName::Name_DESIGNERPHONE  //设计师联系方式
	,DSFrameAttDefName::Name_CITYNAMEBUILDNAMEANDMODELNAME
	,DSFrameAttDefName::Name_most_door_board_material_name      //门板颜色
	,DSFrameAttDefName::Name_most_door_board_material_name2
	,DSFrameAttDefName::Name_most_door_board_material_name3
	,DSFrameAttDefName::Name_most_door_board_name               //门型
	,DSFrameAttDefName::Name_most_glass_core_name               //玻璃花色
	,DSFrameAttDefName::Name_most_knob_name                     //拉手型号
	,DSFrameAttDefName::Name_most_knob_name2
	,DSFrameAttDefName::Name_most_cabinet_board_material_name   //柜体颜色
	,DSFrameAttDefName::Name_most_cabinet_board_material_name2
	,DSFrameAttDefName::Name_most_cabinet_board_material_name3
	,DSFrameAttDefName::Name_most_hinge_name                   //铰链品牌
	,DSFrameAttDefName::Name_most_down_rail_name               //轨道品牌
	,DSFrameAttDefName::Name_table_surface_mateiralvo          //台面花色
	,DSFrameAttDefName::Name_front_section_name1               //前挡水
	,DSFrameAttDefName::Name_back_section_name1                //后挡水
	};

	TMap<FString, FString> AttDefMap;
	for (auto& Ite : DefaultFrameAttrKeys)
	{
		AttDefMap.Add(Ite, FString());
	}
	AttDefMap[DSFrameAttDefName::Name_DRAWING_NAME] = PaperName;
	AttDefMap[DSFrameAttDefName::Name_room_name] = AreaModels.Num() == 1 && AreaModels[0].IsValid() ? AreaModels[0]->GetTypedProperty<FDSHouseAreaProperty>()->AreaName : TEXT("全屋");
	
	TArray<TSharedPtr<FMultiComponentDataItem>> AllNodes;
	for (auto& CupboardIte : CupboardDataMap)
	{
		if (CupboardIte.Value->ParentUUID.IsEmpty())
		{
			TArray<TSharedPtr<FMultiComponentDataItem>> CurNodes;
			UDSCupboardLibrary::GetAllNodes(CupboardIte.Value->Node, CurNodes);
			AllNodes.Append(CurNodes);
		}
	}
	TArray<FString> DoorColors;
	TArray<FString> DoorTypes;
	TArray<FString> GTColors;
	TArray<FString> LSTypes;
	for (auto &NodeIte : AllNodes)
	{
		auto Node = NodeIte;
		if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXMB"), Node->ModelType))
		{
			//门板颜色
			FString ParameterValue = Node->GetParameterValue(TEXT("DZCZ"),TEXT(""));
			if (!ParameterValue.IsEmpty())
			{
				TSharedPtr<FDSResourceInfo> ResInfo = UDSResourceSubsystem::GetInstance()->FindResourceFromCache(ParameterValue);
				if (ResInfo.IsValid())
				{
					DoorColors.AddUnique(ResInfo->Name);
				}
			}

			//门型
			TSharedPtr<FDSResourceInfo> ResInfo = UDSResourceSubsystem::GetInstance()->FindResourceFromCache(Node->ComponentID.GetFormattedValue());
			if (ResInfo.IsValid())
			{
				DoorTypes.AddUnique(ResInfo->Name);
			}
		}
		else if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXGTB"), Node->ModelType))
		{
			//柜体板颜色
			FString ParameterValue = Node->GetParameterValue(TEXT("DZCZ"), TEXT(""));
			if (!ParameterValue.IsEmpty())
			{
				TSharedPtr<FDSResourceInfo> ResInfo = UDSResourceSubsystem::GetInstance()->FindResourceFromCache(ParameterValue);
				if (ResInfo.IsValid())
				{
					GTColors.AddUnique(ResInfo->Name);
				}
			}
		}
		else if (UDSCupboardLibrary::IsSubOfSpecifiedCustomType(TEXT("LXLS"), Node->ModelType))
		{
			//拉手型号
			TSharedPtr<FDSResourceInfo> ResInfo = UDSResourceSubsystem::GetInstance()->FindResourceFromCache(Node->ComponentID.GetFormattedValue());
			if (ResInfo.IsValid())
			{
				LSTypes.AddUnique(ResInfo->Name);
			}
		}
	}
	
	AttDefMap[DSFrameAttDefName::Name_most_door_board_material_name] = DoorColors.IsValidIndex(0) ? DoorColors[0] : TEXT("");
	AttDefMap[DSFrameAttDefName::Name_most_door_board_material_name2] = DoorColors.IsValidIndex(1) ? DoorColors[1] : TEXT("");
	AttDefMap[DSFrameAttDefName::Name_most_door_board_material_name3] = GetCombineName(DoorColors,2);
	AttDefMap[DSFrameAttDefName::Name_most_door_board_name]  = GetCombineName(DoorTypes, 0);
	AttDefMap[DSFrameAttDefName::Name_most_knob_name] = DoorTypes.IsValidIndex(0) ? DoorTypes[0] : TEXT("");
	AttDefMap[DSFrameAttDefName::Name_most_knob_name2] = GetCombineName(LSTypes,1);
	AttDefMap[DSFrameAttDefName::Name_most_cabinet_board_material_name] = GTColors.IsValidIndex(0) ? GTColors[0] : TEXT("");
	AttDefMap[DSFrameAttDefName::Name_most_cabinet_board_material_name2] = GTColors.IsValidIndex(1) ? GTColors[1] : TEXT("");
	AttDefMap[DSFrameAttDefName::Name_most_cabinet_board_material_name3] = GetCombineName(GTColors, 2);

	//风格
	FApplyStyleData FindStyle;
	if (AreaModels.Num() ==1)
	{
		TSharedPtr<FDSHouseAreaProperty> AreaProperty = StaticCastSharedPtr<FDSHouseAreaProperty>(AreaModels[0]->GetPropertySharedPtr());
		if (AreaProperty)
		{
			FindStyle = AreaProperty->AreaStyle;
		}
	}
	else
	{
		FindStyle = UDSMVCSubsystem::GetInstance()->GetGlobalStyleData();
	}
	if (FindStyle.IsValid())
	{
		AttDefMap[DSFrameAttDefName::Name_style_name] = FindStyle.ApplyStyleData.style_description;
	}

	//用户信息（还没有）
	//const FUserInfoData& UserInfo = UDSNetworkSubsystem::GetInstance()->GetLoginUserInfo();
	//AttDefMap[DSFrameAttDefName::Name_DESIGNERNAME] = UserInfo.username;
	
	//方案信息
	AttDefMap[DSFrameAttDefName::Name_USERNAME] = UDSFileSubsystem::GetInstance()->CurProjectInfo.CustomerName;
	AttDefMap[DSFrameAttDefName::Name_USERPHONE] = UDSFileSubsystem::GetInstance()->CurProjectInfo.CustomerTel;
	AttDefMap[DSFrameAttDefName::Name_CITYNAMEBUILDNAMEANDMODELNAME] = UDSFileSubsystem::GetInstance()->CurProjectInfo.CityName;

	Frame->SetAttDefs(AttDefMap);
}

FBox UDSConstructionPaper::GetDrawingBox() const
{
	FBox TotalBox;

	//加上图元包围盒
	for (auto& AreaDataMapIte : AreaDataMap)
	{
		TotalBox += AreaDataMapIte.Value->GetAABB();
	}
	for (auto& AreaDataMapIte : CupboardDataMap)
	{
		TotalBox += AreaDataMapIte.Value->GetAABB();
	}

	//加上标尺包围盒（还没处理）


	return TotalBox.ExpandBy(100.0f);
}

FString UDSConstructionPaper::GetCombineName(const TArray<FString>& InNames, int32 Index)
{
	FString NameStr;
	if (InNames.IsValidIndex(Index))
	{
		for (int32 i = 2; i < InNames.Num(); ++i)
		{
			NameStr += TEXT("  ") + InNames[i];
		}
	}

	return FString();
}

void UDSConstructionPaper::UpdateFrameScale()
{
	if (Frame == nullptr)
		return;


	FBox TotalBox = GetDrawingBox();
    //得到绘制区域包围盒大小
	FVector BoxSizeInCAD = UDSConstructionLibrary::UE4ToCAD(TotalBox.GetSize());
	FVector FrameContentSizeInCAD = Frame->GetFrameContentSizeInCAD();

	float XY = FrameContentSizeInCAD.X / FrameContentSizeInCAD.Y;

	//计算图框缩放比例
	FVector Scale(1.0f, 1.0f, 1.0f);

	if (BoxSizeInCAD.X > FrameContentSizeInCAD.X)
	{
		Scale.X = BoxSizeInCAD.X / FrameContentSizeInCAD.X;
	}

	if (BoxSizeInCAD.Y > FrameContentSizeInCAD.Y)
	{
		Scale.Y = BoxSizeInCAD.Y / FrameContentSizeInCAD.Y;
	}

	Scale.X = Scale.Y = FMath::Max(Scale.X, Scale.Y);

	Frame->SetFrameScaleInCAD(Scale);
}
