// Fill out your copyright notice in the Description page of Project Settings.


#include "DynamicMeshAdaptiveAddorption.h"
#include "DynamicMesh/DynamicMesh3.h"
#include "SubSystems/AdaptiveAdsorption/Data/AdaptationDynamicData.h"
#include "SubSystems/AdaptiveAdsorption/Data/FunctionalDependencyInfo.h"

using namespace MeshIntersection;

#ifndef ADAPTIVE_TOLERANCE
#define ADAPTIVE_TOLERANCE  0.01f//UE_DOUBLE_KINDA_SMALL_NUMBER
#endif // !ADAPTIVE_TOLERANCE


#ifndef ADAPATIVE_UNIT
#define ADAPATIVE_UNIT  0.01f
#endif // !ADAPATIVE_UNIT


FFunctionalIntersectionMesh::~FFunctionalIntersectionMesh()
{
}

void FFunctionalIntersectionMesh::SetRetract(float InForwardRetract, float InBackwardRetract)
{
    ForwardRetract = InForwardRetract;
    BackwardRetract = InBackwardRetract;
}

FOrientedBox3d FFunctionalIntersectionMesh::GetOriBoxWithSpecialExtents()
{
    FOrientedBox3d OutOriBox = GetOriBox();
    if (!bUseRelativeOBB&&bHasSpecialExtents)
    {
        OutOriBox.Frame.Origin = GetOriBox().Frame.FromFramePoint(SpecialExtent+ SepcialOffset -OutOriBox.Extents );
        OutOriBox.Extents = SpecialExtent;
	}
    return OutOriBox;
}


FDynamicMeshAdaptiveAdsorption::FDynamicMeshAdaptiveAdsorption()
	: InitializedData(), OutAdaptationData(), UseExtents()
{
	XNegativeAdsorption = nullptr;
	XPositiveAdsorption = nullptr;
	ZNegativeAdsorption = nullptr;
	ZPositiveAdsorption = nullptr;
}

FDynamicMeshAdaptiveAdsorption::~FDynamicMeshAdaptiveAdsorption()
{
}


void FDynamicMeshAdaptiveAdsorption::Initialized(const TSharedPtr<FAdaptationExecuterInitializedData>& InInitializedData)
{
	InitializedData = InInitializedData;

    OnInitializeDataUpdate();
    if (!SourceIntersectionData.IsValid())
    {
        SourceIntersectionData = MakeShared<FIntersectionDynamicMesh>(UseExtents, InitializedData->Center, InitializedData->Rotation);
    }
    //SourceIntersectionData->SetLinkModelBaseInfo(InInitializedData.LinkModelInfo);
    UE_LOG(LogTemp, Warning, TEXT("FunctionalCupboardModel UseExtents:%s"), *UseExtents.ToString());
}

void FDynamicMeshAdaptiveAdsorption::OnInitializeDataUpdate()
{
   if (!OutAdaptationData.IsValid())
    {
        OutAdaptationData = MakeShared<FFunctionalAdaptationData>();
    }
    OutAdaptationData->OriBox.Extents = InitializedData->Extents;
    OutAdaptationData->OriBox.Frame.Origin = InitializedData->Center;
    OutAdaptationData->OriBox.Frame.Rotation = FQuaterniond(InitializedData->Rotation);


    PreproccessMaxAndMinExtents(InitializedData->Extents, InitializedData->MaxExtents, InitializedData->MinExtents);


    AdaptationOriBox = OutAdaptationData->OriBox;

	//将固定宽高深的Extents应用到自适应包围盒
    ApplyFixedExtentsOnInitialize();


    //记录了返回的自适应包围盒范围
    UseExtents = AdaptationOriBox.Extents;

	//将自适应规则中的偏移应用到自适应包围盒
    ApplyAdaptationOffsetToOrientedBox(AdaptiveRule, AdaptationOriBox);


	//如果某个方向能自适应,则取该方向最大范围
    if (AdaptiveRule.XAxisRule.bAdaptived && InitializedData->bExtentsXEnableAdaptation)
    {
        UseExtents.X = InitializedData->MaxExtents.X;
    }
    if (AdaptiveRule.YAxisRule.bAdaptived && InitializedData->bExtentsYEnableAdaptation)
    {
        UseExtents.Y = InitializedData->MaxExtents.Y;
    }
    if (AdaptiveRule.ZAxisRule.bAdaptived && InitializedData->bExtentsZEnableAdaptation)
    {
        UseExtents.Z = InitializedData->MaxExtents.Z;
    }




    FVector MinRelativePoint = -UseExtents;
    FVector MaxRelativePoint = UseExtents;

    if (!FMath::IsNearlyZero(AdaptiveRule.XAxisRule.NegativeOffset, ADAPTIVE_TOLERANCE))
    {
        MinRelativePoint.X -= AdaptiveRule.XAxisRule.NegativeOffset;
    }
    if (!FMath::IsNearlyZero(AdaptiveRule.XAxisRule.PositiveOffset, ADAPTIVE_TOLERANCE))
    {
        MaxRelativePoint.X += AdaptiveRule.XAxisRule.PositiveOffset;
    }

    if (!FMath::IsNearlyZero(AdaptiveRule.YAxisRule.NegativeOffset, ADAPTIVE_TOLERANCE))
    {
        MinRelativePoint.Y -= AdaptiveRule.YAxisRule.NegativeOffset;
    }
    if (!FMath::IsNearlyZero(AdaptiveRule.YAxisRule.PositiveOffset, ADAPTIVE_TOLERANCE))
    {
        MaxRelativePoint.Y += AdaptiveRule.YAxisRule.PositiveOffset;
    }
    if (!FMath::IsNearlyZero(AdaptiveRule.ZAxisRule.NegativeOffset, ADAPTIVE_TOLERANCE))
    {
        MinRelativePoint.Z -= AdaptiveRule.ZAxisRule.NegativeOffset;
    }
    if (!FMath::IsNearlyZero(AdaptiveRule.ZAxisRule.PositiveOffset, ADAPTIVE_TOLERANCE))
    {
        MaxRelativePoint.Z += AdaptiveRule.ZAxisRule.PositiveOffset;
    }
    UseExtents = (MaxRelativePoint - MinRelativePoint) * 0.5f;

    if (SourceIntersectionData.IsValid())
    {
        SourceIntersectionData->UpdateOribox(InitializedData->Center, FQuaterniond(InitializedData->Rotation),InitializedData->Extents);
    }
}

void FDynamicMeshAdaptiveAdsorption::ApplyFixedExtentsOnInitialize()
{
	TSharedPtr<FFunctionalExecuterInitializedData> FunctionalInitializedData = StaticCastSharedPtr<FFunctionalExecuterInitializedData>(InitializedData);
    FunctionalInitializedData->FixedMaxExtentOffset = FVector::ZeroVector;
    FunctionalInitializedData->FixedMinExtentOffset = FVector::ZeroVector;
    if (!FunctionalInitializedData->bUsedRelativedOBB && FunctionalInitializedData->bHasFixedExtents)
	{
		FVector ApplyFixedOffset = FVector::ZeroVector;
		FVector ApplyFixedExtent = AdaptationOriBox.Extents;
		if (/*!InitializedData->bExtentsXEnableAdaptation ||*/ !FunctionalInitializedData->AdaptativeRule.XAxisRule.bAdaptived)
		{
			ApplyFixedOffset.X = FunctionalInitializedData->FixedOffset.X;
			ApplyFixedExtent.X = FunctionalInitializedData->FixedExtent.X;
		}
		if (/*!InitializedData->bExtentsYEnableAdaptation ||*/ !FunctionalInitializedData->AdaptativeRule.YAxisRule.bAdaptived)
		{
			ApplyFixedOffset.Y = FunctionalInitializedData->FixedOffset.Y;
			ApplyFixedExtent.Y = FunctionalInitializedData->FixedExtent.Y;

		}if (/*!InitializedData->bExtentsZEnableAdaptation ||*/ !FunctionalInitializedData->AdaptativeRule.ZAxisRule.bAdaptived)
		{
			ApplyFixedOffset.Z = FunctionalInitializedData->FixedOffset.Z;
			ApplyFixedExtent.Z = FunctionalInitializedData->FixedExtent.Z;
		}
		AdaptationOriBox.Frame.Origin = AdaptationOriBox.Frame.FromFramePoint(-AdaptationOriBox.Extents + ApplyFixedOffset + ApplyFixedExtent);

		FVector FixedMinOffset = -ApplyFixedExtent - AdaptationOriBox.Frame.ToFramePoint(OutAdaptationData->OriBox.Frame.FromFramePoint(-OutAdaptationData->OriBox.Extents));
		FVector FixedMaxOffset = ApplyFixedExtent - AdaptationOriBox.Frame.ToFramePoint(OutAdaptationData->OriBox.Frame.FromFramePoint(OutAdaptationData->OriBox.Extents));
		if (/*!InitializedData->bExtentsXEnableAdaptation || */!FunctionalInitializedData->AdaptativeRule.XAxisRule.bAdaptived)
		{
			FunctionalInitializedData->FixedMinExtentOffset.X = FixedMinOffset.X;
			FunctionalInitializedData->FixedMaxExtentOffset.X = FixedMaxOffset.X;
			AdaptationOriBox.Extents.X = FunctionalInitializedData->FixedExtent.X;
		}
		if (/*!InitializedData->bExtentsYEnableAdaptation ||*/ !FunctionalInitializedData->AdaptativeRule.YAxisRule.bAdaptived)
		{
			FunctionalInitializedData->FixedMinExtentOffset.Y = FixedMinOffset.Y;
			FunctionalInitializedData->FixedMaxExtentOffset.Y = FixedMaxOffset.Y;
			AdaptationOriBox.Extents.Y = FunctionalInitializedData->FixedExtent.Y;

		}if (/*!InitializedData->bExtentsZEnableAdaptation ||*/ !FunctionalInitializedData->AdaptativeRule.ZAxisRule.bAdaptived)
		{
			FunctionalInitializedData->FixedMinExtentOffset.Z = FixedMinOffset.Z;
			FunctionalInitializedData->FixedMaxExtentOffset.Z = FixedMaxOffset.Z;
			AdaptationOriBox.Extents.Z = FunctionalInitializedData->FixedExtent.Z;
		}
	}
}

void FDynamicMeshAdaptiveAdsorption::AdaptiveAndAdsorptionComplete(const TArray<TSharedPtr<FIntersectionDynamicMesh>>& AdaptationEnvs,FAdaptiveAdsorptionResault& OutAdaptiveAdResualt)
{
	//判断本次自适应和移动是否有相交
	// 判断本次自适应和移动是否移出柜体

	TSharedPtr<FFunctionalExecuterInitializedData> FunctionalInitializedData = StaticCastSharedPtr<FFunctionalExecuterInitializedData>(InitializedData);

    
    OutAdaptiveAdResualt.ResaultType = EAdaptiveResaultType::E_Succeded;
   // OutAdaptiveAdResualt.Owner = OwnerObject;
    OutAdaptiveAdResualt.SourceUUID = GetOwnerUUID();
    //判断结束是自适应是否合法



    bool bWithoutAdsorption = 
        (AdaptiveRule.XAxisRule.AdsorptionRule == EAdsorptionRule::E_Positive && !XPositiveAdsorption.IsValid())
        || (AdaptiveRule.XAxisRule.AdsorptionRule == EAdsorptionRule::E_Negative && !XNegativeAdsorption.IsValid())
        || (AdaptiveRule.ZAxisRule.AdsorptionRule == EAdsorptionRule::E_Positive && !ZPositiveAdsorption.IsValid())
        || (AdaptiveRule.ZAxisRule.AdsorptionRule == EAdsorptionRule::E_Negative && !ZNegativeAdsorption.IsValid());


    bool  bOutCabientOriBox = false;
    if (!FunctionalInitializedData->CabinetOriBox.Contains(OutAdaptationData->OriBox.Center()))
    {
        bOutCabientOriBox = OutAdaptationData->OriBox.TestCorners([&](const FVector& Corner)
            {
                if (!FunctionalInitializedData->CabinetOriBox.Contains(Corner))
                {
                    return true;
                }
                return  false;
            });
    }
    if (bWithoutAdsorption && bOutCabientOriBox )
    {
        OutAdaptiveAdResualt.ResaultType = EAdaptiveResaultType::E_WithoutAdsorption;
        return;
    }

    FOrientedBox3d EndBox = OutAdaptationData->OriBox;
    

    //判断是否超极致范围
    auto CheckAndSetDescription = [&](const FString& AxisName, float MinExtent, float MaxExtent, float CurrentExtent)
        {
            EAdaptiveResaultType ResaultType = EAdaptiveResaultType::E_Succeded;
            if (!FMath::IsNearlyEqual(CurrentExtent, MinExtent, ADAPATIVE_UNIT) && CurrentExtent < MinExtent)
            {
                ResaultType = EAdaptiveResaultType::E_LessThanMin;
            }
            else if (!FMath::IsNearlyEqual(CurrentExtent, MaxExtent, ADAPATIVE_UNIT) && CurrentExtent > MaxExtent)
            {
                ResaultType = EAdaptiveResaultType::E_MoreThanMax;
            }
            if (ResaultType == EAdaptiveResaultType::E_MoreThanMax || ResaultType == EAdaptiveResaultType::E_LessThanMin)
            {
                OutAdaptiveAdResualt.ResaultType = ResaultType;
                OutAdaptiveAdResualt.Description = FString::Printf(TEXT("<Normal_Char>工艺错误！%s %s 尺寸超过尺寸范围</><Normal_Num> %d-%d</>"),
                    *SourceIntersectionData->GetDebugName(), *AxisName, FMath::RoundToInt(MinExtent*20.f), FMath::RoundToInt(MaxExtent*20.f));
            }
        };

    if (AdaptiveRule.XAxisRule.bAdaptived)
    {
        CheckAndSetDescription(TEXT("宽度"), InitializedData->MinExtents.X, InitializedData->MaxExtents.X, AdaptationOriBox.Extents.X);
    }

    if (AdaptiveRule.YAxisRule.bAdaptived)
    {
        CheckAndSetDescription(TEXT("深度"), InitializedData->MinExtents.Y, InitializedData->MaxExtents.Y, AdaptationOriBox.Extents.Y);
    }

    if (AdaptiveRule.ZAxisRule.bAdaptived)
    {
        CheckAndSetDescription(TEXT("高度"), InitializedData->MinExtents.Z, InitializedData->MaxExtents.Z, AdaptationOriBox.Extents.Z);
    }
    //EndBox.Extents = EndBox.Extents - 2*ADAPTIVE_TOLERANCE;
    //SourceIntersectionData->UpdateOribox(EndBox);
  //  for (auto& Iter : AdaptationEnvs)
  //  {
  //      FIntersectionsQueryResult OutResault;
  //      bool bHit = DoIntersectionTest(*SourceIntersectionData, *Iter, OutResault, false);

  //      if (bHit)
  //      {
  //          OutAdaptiveAdResualt.Intersections.Add(Iter->GetLinkModelBaseInfo());
  //      }
  //  }
  //  
  //  if (!OutAdaptiveAdResualt.Intersections.IsEmpty()&& OutAdaptiveAdResualt.ResaultType == EAdaptiveResaultType::E_Succeded)
  //  {
  //      FString IntersectionTargetNameStr;
		//for (auto& Iter : OutAdaptiveAdResualt.Intersections)
		//{
		//	IntersectionTargetNameStr += FString::Printf(TEXT("%s、"), *Iter.Name);
		//}
		//IntersectionTargetNameStr.RemoveAt(IntersectionTargetNameStr.Len() - 1);
  //      OutAdaptiveAdResualt.ResaultType = EAdaptiveResaultType::E_Intersection;
  //      OutAdaptiveAdResualt.Description = FString::Printf(TEXT("<Normal_Char>工艺错误！%s与%s有交叉</>"), *SourceIntersectionData->GetDebugName(),*IntersectionTargetNameStr);
  //  }

    if (XNegativeAdsorption.IsValid())
    {
       auto Ptr = StaticCastWeakPtr<FFunctionalIntersectionMesh>(XNegativeAdsorption).Pin();
       
        OutAdaptiveAdResualt.Dependents.Add(FAdaptiveAdsorptionDenpendedTarget(Ptr->GetLinkModelBaseInfo(), EAdaptationDirection::E_Backward));
    }
    if (XPositiveAdsorption.IsValid())
    {
        auto Ptr = StaticCastWeakPtr<FFunctionalIntersectionMesh>(XPositiveAdsorption).Pin();
        OutAdaptiveAdResualt.Dependents.Add(FAdaptiveAdsorptionDenpendedTarget(Ptr->GetLinkModelBaseInfo(), EAdaptationDirection::E_Forward));
    }
    if (ZNegativeAdsorption.IsValid())
    {
        auto Ptr = StaticCastWeakPtr<FFunctionalIntersectionMesh>(ZNegativeAdsorption).Pin();
        OutAdaptiveAdResualt.Dependents.Add(FAdaptiveAdsorptionDenpendedTarget(Ptr->GetLinkModelBaseInfo(), EAdaptationDirection::E_Down));
    }
    if (ZPositiveAdsorption.IsValid())
    {
        auto Ptr = StaticCastWeakPtr<FFunctionalIntersectionMesh>(ZPositiveAdsorption).Pin();
        OutAdaptiveAdResualt.Dependents.Add(FAdaptiveAdsorptionDenpendedTarget(Ptr->GetLinkModelBaseInfo(), EAdaptationDirection::E_Up));
    }

}

bool FDynamicMeshAdaptiveAdsorption::HandleAdaptiveAndAdsorptionWithDependent(const TArray<TSharedPtr<FIntersectionDynamicMesh>>& AdaptationEnvs, const FString& DependentUUID , EAdaptationDirection DependencyDir)
{

    //如果传入方向，按吸附方向判断位置

    //AdaptationOriBox = OutAdaptationData.OriBox;
    const auto& OriBox = AdaptationOriBox;
   
    //UE_LOG(AdaptationExecuterLog, Log, TEXT("1.%s:%s"), *SourceIntersectionData->GetDebugName(), *OriBox.Frame.FromFramePoint(-OriBox.Extents).ToString());
    FRayHitResault HitResault;
    //依赖对象是当前吸附对象的话要做反向检查
    switch (DependencyDir)
    {
    case EAdaptationDirection::E_Forward:
    {
        if (AdaptiveRule.XAxisRule.AdsorptionRule == EAdsorptionRule::E_Positive)
        {
            FVector StartPoint = OriBox.Frame.FromFramePoint(FVector::ForwardVector*OriBox.Extents);
            bool bHit = FindHitNearestPoint(StartPoint, -OriBox.AxisX(), AdaptationEnvs, HitResault);
            if (bHit)
            {
                if (HitResault.HitTarget->GetComponentUUID().Equals(DependentUUID))
                {
                    SetXPositiveDependence(HitResault.HitTarget);
                    FVector XOffset = GetIntersectionFrame().FromFrameVector(FVector::BackwardVector * (HitResault.HitTarget->GetOriBox().Extents * 2.f + HitResault.Distance));
                    AdaptationOriBox.Frame.Origin += XOffset;
                }
            }
        }
        }
        break;
    case EAdaptationDirection::E_Backward:
    {
        if (AdaptiveRule.XAxisRule.AdsorptionRule == EAdsorptionRule::E_Negative)
        {
            FVector StartPoint = OriBox.Frame.FromFramePoint(FVector::BackwardVector * OriBox.Extents);
            bool bHit = FindHitNearestPoint(StartPoint, OriBox.AxisX(), AdaptationEnvs, HitResault);
            if (bHit)
            {
                if (HitResault.HitTarget->GetComponentUUID().Equals(DependentUUID))
                {
                    SetXNegativeDependence(HitResault.HitTarget);
                    FVector XOffset = GetIntersectionFrame().FromFrameVector(FVector::ForwardVector * (HitResault.HitTarget->GetOriBox().Extents * 2.f + HitResault.Distance));
                    AdaptationOriBox.Frame.Origin += XOffset;
                }
            }
        }
        }
        break;
    case EAdaptationDirection::E_Up:
    {
        if (AdaptiveRule.ZAxisRule.AdsorptionRule == EAdsorptionRule::E_Positive)
        {
            FVector StartPoint = OriBox.Frame.FromFramePoint(FVector::UpVector * OriBox.Extents);
            bool bHit = FindHitNearestPoint(StartPoint, -OriBox.AxisZ(), AdaptationEnvs, HitResault);
            if (bHit)
            {
                if (HitResault.HitTarget->GetComponentUUID().Equals(DependentUUID))
                {
                    SetZNegativeDependence(HitResault.HitTarget);
                    FVector ZOffset = GetIntersectionFrame().FromFrameVector(FVector::DownVector * (HitResault.HitTarget->GetOriBox().Extents * 2.f + HitResault.Distance));
                    AdaptationOriBox.Frame.Origin += ZOffset;
                }
            }
        }
    }
    break;
    case EAdaptationDirection::E_Down:
    {
        if (AdaptiveRule.ZAxisRule.AdsorptionRule == EAdsorptionRule::E_Negative)
        {
            FVector StartPoint = OriBox.Frame.FromFramePoint(FVector::DownVector * OriBox.Extents);
            bool bHit = FindHitNearestPoint(StartPoint, OriBox.AxisZ(), AdaptationEnvs, HitResault);
            if (bHit)
            {
                if (HitResault.HitTarget->GetComponentUUID().Equals(DependentUUID))
                {
                    SetZNegativeDependence(HitResault.HitTarget);
                    FVector ZOffset = GetIntersectionFrame().FromFrameVector(FVector::UpVector * (HitResault.HitTarget->GetOriBox().Extents * 2.f + HitResault.Distance));
                    AdaptationOriBox.Frame.Origin += ZOffset;
                }
            }
        }
    }
    break;
    case EAdaptationDirection::E_None:
    case EAdaptationDirection::E_Left:
    case EAdaptationDirection::E_Right:
    default:
      
        break;
    }
    //UE_LOG(AdaptationExecuterLog, Log, TEXT("2.%s:%s"), *SourceIntersectionData->GetDebugName(), *OriBox.Frame.FromFramePoint(-OriBox.Extents).ToString());
    return HandleAdaptiveAndAdsorption(AdaptationEnvs);
}

bool FDynamicMeshAdaptiveAdsorption::HandleAdaptiveAndAdsorptionWithDependent(const TArray<TSharedPtr<FIntersectionDynamicMesh>>& AdaptationEnvs, const TSharedPtr<FFunctionalDependencyInfo>& SelfDependencyInfo)
{
    //AdaptationOriBox = OutAdaptationData.OriBox;
    const auto& OriBox = AdaptationOriBox;
    auto FunctionalInitializedData = StaticCastSharedPtr<FFunctionalExecuterInitializedData>(InitializedData);
    bool  bOutCabientOriBox = false;

    if (!SelfDependencyInfo.IsValid())
    {
        return HandleAdaptiveAndAdsorption(AdaptationEnvs);
    }

    if (SelfDependencyInfo.IsValid()&& !SelfDependencyInfo->bEnablePassiveAdaptation)
    {
        return false;
    }

    if (AdaptiveRule.XAxisRule.AdsorptionRule == EAdsorptionRule::E_Positive)
    {
        FRayHitResault HitResault;
        FVector StartPoint = OriBox.Frame.FromFramePoint(FVector::ForwardVector * (OriBox.Extents - ADAPATIVE_UNIT));
        bool bHit = FindHitNearestPoint(StartPoint, -OriBox.AxisX(), AdaptationEnvs, HitResault);
        if (bHit)
        {
            for (auto Iter : SelfDependencyInfo->GetDependentNodes())
            {
                if (!Iter.Key.IsValid())
                {
                    continue;
                }

                if (Iter.Key.SubNodeUUID.Equals(HitResault.HitTarget->GetComponentUUID())&& Iter.Key.Direction == EAdaptationDirection::E_Forward)
                {
                    SetXPositiveDependence(HitResault.HitTarget);
                    FVector XOffset = GetIntersectionFrame().FromFrameVector(FVector::BackwardVector * (HitResault.HitTarget->GetOriBox().Extents * 2.f + HitResault.Distance+ ADAPATIVE_UNIT));
                    AdaptationOriBox.Frame.Origin += XOffset;
                }

            }

            if (HitResault.HitNormal.X < 0)
            {
                FVector XOffset = GetIntersectionFrame().FromFrameVector(FVector::BackwardVector * (HitResault.Distance + ADAPATIVE_UNIT));
                AdaptationOriBox.Frame.Origin += XOffset;
            }
            else if (XNegativeAdsorption != nullptr)
            {
                FVector XOffset = GetIntersectionFrame().FromFrameVector(FVector::BackwardVector * (HitResault.HitTarget->GetOriBox().Extents * 2.f + HitResault.Distance + ADAPATIVE_UNIT));
                AdaptationOriBox.Frame.Origin += XOffset;
            }

        }
    }
    else if (AdaptiveRule.XAxisRule.AdsorptionRule == EAdsorptionRule::E_Negative)
    {
        FRayHitResault HitResault;
        FVector StartPoint = OriBox.Frame.FromFramePoint(FVector::BackwardVector * (OriBox.Extents-ADAPATIVE_UNIT));
        bool bHit = FindHitNearestPoint(StartPoint, OriBox.AxisX(), AdaptationEnvs, HitResault);
        if (bHit)
        {
            for (auto Iter : SelfDependencyInfo->GetDependentNodes())
            {
                if (!Iter.Key.IsValid())
                {
                    continue;
                }
                if (Iter.Key.SubNodeUUID.Equals(HitResault.HitTarget->GetComponentUUID()) && Iter.Key.Direction == EAdaptationDirection::E_Backward)
                {
                    SetXNegativeDependence(HitResault.HitTarget);
                }
            }
            if (HitResault.HitNormal.X >0)
            {
                FVector XOffset = GetIntersectionFrame().FromFrameVector(FVector::ForwardVector * (HitResault.Distance + ADAPATIVE_UNIT));
                AdaptationOriBox.Frame.Origin += XOffset;
            }
            else if (XNegativeAdsorption != nullptr)
            {
                FVector XOffset = GetIntersectionFrame().FromFrameVector(FVector::ForwardVector * (HitResault.HitTarget->GetOriBox().Extents * 2.f + HitResault.Distance + ADAPATIVE_UNIT));
                AdaptationOriBox.Frame.Origin += XOffset;
            }

        }
    }

    if (AdaptiveRule.ZAxisRule.AdsorptionRule == EAdsorptionRule::E_Positive)
    {
        FRayHitResault HitResault;
        FVector StartPoint = OriBox.Frame.FromFramePoint(FVector::UpVector * (OriBox.Extents - ADAPATIVE_UNIT));
        bool bHit = FindHitNearestPoint(StartPoint, -OriBox.AxisZ(), AdaptationEnvs, HitResault);
        if (bHit)
        {
            for (auto Iter : SelfDependencyInfo->GetDependentNodes())
            {
                if (!Iter.Key.IsValid())
                {
                    continue;
                }

                if (Iter.Key.SubNodeUUID.Equals(HitResault.HitTarget->GetComponentUUID()) && Iter.Key.Direction == EAdaptationDirection::E_Up)
                {
                    SetZNegativeDependence(HitResault.HitTarget);
                    FVector ZOffset = GetIntersectionFrame().FromFrameVector(FVector::DownVector * (HitResault.HitTarget->GetOriBox().Extents * 2.f + HitResault.Distance+ ADAPATIVE_UNIT));
                    AdaptationOriBox.Frame.Origin += ZOffset;
                }
            }

            if (HitResault.HitNormal.Z < 0)
            {
                FVector XOffset = GetIntersectionFrame().FromFrameVector(FVector::DownVector * (HitResault.Distance + ADAPATIVE_UNIT));
                AdaptationOriBox.Frame.Origin += XOffset;
            }
            else if (XNegativeAdsorption != nullptr)
            {
                FVector XOffset = GetIntersectionFrame().FromFrameVector(FVector::DownVector * (HitResault.HitTarget->GetOriBox().Extents * 2.f + HitResault.Distance + ADAPATIVE_UNIT));
                AdaptationOriBox.Frame.Origin += XOffset;
            }
        }
    }
    else if (AdaptiveRule.ZAxisRule.AdsorptionRule == EAdsorptionRule::E_Negative)
    {
        FRayHitResault HitResault;
        FVector StartPoint = OriBox.Frame.FromFramePoint(FVector::DownVector * (OriBox.Extents - ADAPATIVE_UNIT));
        bool bHit = FindHitNearestPoint(StartPoint, OriBox.AxisZ(), AdaptationEnvs, HitResault);
        if (bHit)
        {
            for (auto Iter : SelfDependencyInfo->GetDependentNodes())
            {
                if (!Iter.Key.IsValid())
                {
                    continue;
                }

                if (Iter.Key.SubNodeUUID.Equals(HitResault.HitTarget->GetComponentUUID()) && Iter.Key.Direction == EAdaptationDirection::E_Down)
                {
                    SetZNegativeDependence(HitResault.HitTarget);
                    FVector ZOffset = GetIntersectionFrame().FromFrameVector(FVector::UpVector * (HitResault.HitTarget->GetOriBox().Extents * 2.f + HitResault.Distance+ ADAPATIVE_UNIT));
                    AdaptationOriBox.Frame.Origin += ZOffset;
                }
            }

            if (HitResault.HitNormal.Z > 0)
            {
                FVector XOffset = GetIntersectionFrame().FromFrameVector(FVector::UpVector * (HitResault.Distance + ADAPATIVE_UNIT));
                AdaptationOriBox.Frame.Origin += XOffset;
            }
            else if (XNegativeAdsorption != nullptr)
            {
                FVector XOffset = GetIntersectionFrame().FromFrameVector(FVector::UpVector * (HitResault.HitTarget->GetOriBox().Extents * 2.f + HitResault.Distance + ADAPATIVE_UNIT));
                AdaptationOriBox.Frame.Origin += XOffset;
            }
        }
    }
    return HandleAdaptiveAndAdsorption(AdaptationEnvs);
}

void FDynamicMeshAdaptiveAdsorption::GenerateRay(FVector& RayStart, FVector& RayDir)
{
    //模拟鼠标射线检测吸附,计算射点
    const auto& OriBox = AdaptationOriBox;
    RayDir = -OriBox.AxisY();
    RayStart = OriBox.Center();

    if (AdaptiveRule.XAxisRule.AdsorptionRule == EAdsorptionRule::E_Positive)
    {
        RayStart += OriBox.Frame.FromFrameVector(FVector::ForwardVector * (OriBox.Extents - ADAPTIVE_TOLERANCE));
        RayDir = OriBox.AxisX();

    }
    else if (AdaptiveRule.XAxisRule.AdsorptionRule == EAdsorptionRule::E_Negative)
    {
        RayStart += OriBox.Frame.FromFrameVector(FVector::BackwardVector * (OriBox.Extents - ADAPTIVE_TOLERANCE));
        RayDir = -OriBox.AxisX();
    }

    if (AdaptiveRule.ZAxisRule.AdsorptionRule == EAdsorptionRule::E_Positive)
    {
        RayStart += OriBox.Frame.FromFrameVector(FVector::UpVector * (OriBox.Extents - ADAPTIVE_TOLERANCE));
        RayDir = OriBox.AxisZ();
    }
    else if (AdaptiveRule.ZAxisRule.AdsorptionRule == EAdsorptionRule::E_Negative)
    {
        RayStart += OriBox.Frame.FromFrameVector(FVector::DownVector * (OriBox.Extents - ADAPTIVE_TOLERANCE));
        RayDir = -OriBox.AxisZ();
    }

    if (AdaptiveRule.YAxisRule.AdsorptionRule == EAdsorptionRule::E_Positive)
    {
        RayStart += OriBox.Frame.FromFrameVector(FVector::RightVector * (OriBox.Extents - ADAPTIVE_TOLERANCE));
    }
    else if (AdaptiveRule.YAxisRule.AdsorptionRule == EAdsorptionRule::E_Negative)
    {
        RayStart += OriBox.Frame.FromFrameVector(FVector::LeftVector * (OriBox.Extents - ADAPTIVE_TOLERANCE));
    }
}

bool FDynamicMeshAdaptiveAdsorption::HandleAdaptiveAndAdsorption(const TArray<TSharedPtr<FIntersectionDynamicMesh>>& AdaptationEnvs)
{
    FVector RayStart, RayDir;
    GenerateRay(RayStart, RayDir);
    UE_LOG(AdaptationExecuterLog, Warning, TEXT("1.RayStart%s:%s"), *RayStart.ToString(), *RayDir.ToString());
    FRayHitResault HitResault;
    bool bHit = FindHitNearestPoint(RayStart, RayDir, AdaptationEnvs, HitResault);
    if (!bHit)
    {
        bool  bOutCabientOriBox = false;
        TSharedPtr<FFunctionalExecuterInitializedData> FunctionalInitializedData = StaticCastSharedPtr<FFunctionalExecuterInitializedData>(InitializedData);
        if (!FunctionalInitializedData->CabinetOriBox.Contains(OutAdaptationData->OriBox.Center()))
        {
            bOutCabientOriBox = OutAdaptationData->OriBox.TestCorners([&](const FVector& Corner)
                {
                    if (!FunctionalInitializedData->CabinetOriBox.Contains(Corner))
                    {
                        return true;
                    }
                    return  false;
                });
        }
        bHit = !bOutCabientOriBox;
        if (bHit)
        {   
            FVector Dir = FVector::ForwardVector;
            if (AdaptiveRule.XAxisRule.bAdaptived)
            {
                Dir = AdaptiveRule.XAxisRule.AdsorptionRule == EAdsorptionRule::E_Negative ? FVector::BackwardVector : FVector::ForwardVector;
            }
            else if(AdaptiveRule.ZAxisRule.bAdaptived)
            {
                Dir = AdaptiveRule.ZAxisRule.AdsorptionRule == EAdsorptionRule::E_Negative ? FVector::DownVector : FVector::UpVector;
            }
            
            HitResault.Distance = 0.f;
            HitResault.HitPoint = OutAdaptationData->OriBox.Frame.FromFramePoint(Dir * OutAdaptationData->OriBox.Extents);
            HitResault.HitNormal = -Dir;
        }
    }
    if (!bHit)
    {
        if (!FMath::IsNearlyZero(RayDir.Z))
        {
            double Delta = ((AdaptationOriBox.Frame.Origin.Z - RayStart.Z) / RayDir.Z);
            AdaptationOriBox.Frame.Origin = RayStart + RayDir * Delta;
        }
    }
    HandleAdaptiveAndAdsorptionWithHitResault(bHit, HitResault, AdaptationEnvs);
    return true;
}

bool FDynamicMeshAdaptiveAdsorption::HandleAdaptiveAndAdsorptionWithRay(const FVector& RayStart, const FVector& RayDir, const TArray<TSharedPtr<FIntersectionDynamicMesh>>& AdaptationEnvs)
{
    ClearDependentTarget();
    FRayHitResault HitResault;
    bool bHit =FindHitNearestPoint(RayStart, RayDir, AdaptationEnvs, HitResault);
    TSharedPtr<FFunctionalExecuterInitializedData> FunctionalInitializedData = StaticCastSharedPtr<FFunctionalExecuterInitializedData>(InitializedData);
	//处理射线检测位置不在柜体包围盒内的情况
    if (bHit)
    {
        const auto& CabinetOriBox = FunctionalInitializedData->CabinetOriBox;
        FVector RelativeHitPoint = CabinetOriBox.Frame.ToFramePoint(HitResault.HitPoint);
        if (FMath::IsNearlyEqual(FMath::Abs(RelativeHitPoint.X),CabinetOriBox.Extents.X,ADAPTIVE_TOLERANCE)
            ||FMath::IsNearlyEqual(FMath::Abs(RelativeHitPoint.Y),CabinetOriBox.Extents.Y,ADAPTIVE_TOLERANCE)
            ||FMath::IsNearlyEqual(FMath::Abs(RelativeHitPoint.Z),CabinetOriBox.Extents.Z,ADAPTIVE_TOLERANCE))
        {
            FVector HitPointToCenter = (CabinetOriBox.Center() - HitResault.HitPoint);
            HitPointToCenter.Normalize();
            double Value = HitResault.HitNormal.Dot(HitPointToCenter);
            if (Value < 0)
            {

                IMeshSpatial::FQueryOptions Options;
                Options.bAllowUnsafeModifiedMeshQueries = true;
                FRay Ray(CabinetOriBox.Center(), -HitPointToCenter);

                int UseTriID = 0;
                double CurDistance;
                bool bHit = HitResault.HitTarget->RayHit(Ray, Options, UseTriID, CurDistance);
                if (bHit)
                {
                    HitResault.Distance = CurDistance;
                    HitResault.HitPoint = Ray.PointAt(CurDistance);
                    HitResault.HitNormal = HitResault.HitTarget->GetDynamicMeshTree().GetMesh()->GetTriNormal(UseTriID);
                }
            }
        }
    }
    if (!bHit)
    {
        if (!FMath::IsNearlyZero(RayDir.Z))
        {
            double Delta = ((AdaptationOriBox.Frame.Origin.Z - RayStart.Z) / RayDir.Z);
            AdaptationOriBox.Frame.Origin = RayStart + RayDir * Delta;
        }
    }
    HandleAdaptiveAndAdsorptionWithHitResault(bHit, HitResault, AdaptationEnvs);
    return true;
}

void FDynamicMeshAdaptiveAdsorption::HandleAddsorption(FOrientedBox3d& OutOriBox,const FRayHitResault& HitResault, const TArray<TSharedPtr<FIntersectionDynamicMesh>>& Env)
{
	//先将位置等于当前鼠标射线检测位置
    OutOriBox.Frame.Origin = HitResault.HitPoint;

    CaculateAdsorptionPoint(OutOriBox.Frame.Origin, HitResault, UseExtents, Env);

    SourceIntersectionData->UpdateExtentsAndLocation(UseExtents, OutOriBox.Frame.Origin);
}

void FDynamicMeshAdaptiveAdsorption::HandleAdaptive(const TArray<TSharedPtr<FIntersectionDynamicMesh>>& Env, const TSharedPtr<FIntersectionDynamicMesh>& HitEnv, FOrientedBox3d& OutExecutedBox)
{
    FVector&  OutCenter = OutExecutedBox.Frame.Origin;
    FVector& OutExtents = OutExecutedBox.Extents;
    const FOrientedBox3d& IntersectionOriBox = SourceIntersectionData->GetOriBox();
    const auto& SelfFrame = IntersectionOriBox.Frame;

    //相交检测
    TMap<TSharedPtr<FIntersectionDynamicMesh>, FAdaptiveResault> ResaultMap;


	for (auto& Iter : Env)
	{
		FIntersectionsQueryResult OutResault;
		bool bHit = DoIntersectionTest(*SourceIntersectionData, *Iter, OutResault, true);

		if (bHit)
		{
			FAdaptiveResault AdaptiveResault;
			AdaptiveResault.Resault = OutResault;
			ResaultMap.Add(Iter, AdaptiveResault);
		}
	}

	if (ResaultMap.IsEmpty())
	{
		return;
	}
	bool bSucce;
	if (ResaultMap.Num() > 1)
	{
		bSucce = true;
	}
	FVector SizeMaxPoint = IntersectionOriBox.Extents;
	FVector SizeMinPoint = -IntersectionOriBox.Extents;

    if (AdaptiveRule.XAxisRule.bAdaptived && InitializedData->bExtentsXEnableAdaptation)
    {

        for (auto& Pair : ResaultMap)
        {

            auto& TargetIntersetionMesh = Pair.Key;

			FAdaptiveResault& Value = Pair.Value;
			if (Value.Resault.Segments.Num() <= 0)
			{
				continue;
			}

			const FDynamicMesh3* TargetDynamicMesh = TargetIntersetionMesh->GetDynamicMeshTree().GetMesh();
			for (auto& Segment : Value.Resault.Segments)
			{
				for (size_t i = 0; i < 2; i++)
				{
					int TriID = Segment.TriangleID[1];
					FVector Normal = TargetDynamicMesh->GetTriNormal(TriID);


					Normal = GetIntersectionFrame().ToFrameVector(Normal);

					if (FMath::IsNearlyZero(Normal.X, ADAPTIVE_TOLERANCE))
					{
						//当线段一个顶点法线X方向为0 ，该面与交互对象共面 跳出线段
						break;
					}


					FVector Vertices0, Vertices1, Vertices2;
					TargetDynamicMesh->GetTriVertices(TriID, Vertices0, Vertices1, Vertices2);
					Vertices0 = GetIntersectionFrame().ToFramePoint(Vertices0);
					Vertices1 = GetIntersectionFrame().ToFramePoint(Vertices1);
					Vertices2 = GetIntersectionFrame().ToFramePoint(Vertices2);

					if (((FMath::IsNearlyEqual(Vertices0.Z, SizeMinPoint.Z, ADAPTIVE_TOLERANCE) || Vertices0.Z < SizeMinPoint.Z)
							&& (FMath::IsNearlyEqual(Vertices1.Z, SizeMinPoint.Z, ADAPTIVE_TOLERANCE) || Vertices1.Z < SizeMinPoint.Z)
							&& (FMath::IsNearlyEqual(Vertices2.Z, SizeMinPoint.Z, ADAPTIVE_TOLERANCE) || Vertices2.Z < SizeMinPoint.Z))
						|| ((FMath::IsNearlyEqual(Vertices0.Z, SizeMaxPoint.Z, ADAPTIVE_TOLERANCE) || Vertices0.Z > SizeMaxPoint.Z)
							&& (FMath::IsNearlyEqual(Vertices1.Z, SizeMaxPoint.Z, ADAPTIVE_TOLERANCE) || Vertices1.Z > SizeMaxPoint.Z)
							&& (FMath::IsNearlyEqual(Vertices2.Z, SizeMaxPoint.Z, ADAPTIVE_TOLERANCE) || Vertices2.Z > SizeMaxPoint.Z))
						|| ((FMath::IsNearlyEqual(Vertices0.Y, SizeMinPoint.Y, ADAPTIVE_TOLERANCE) || Vertices0.Y < SizeMinPoint.Y)
							&& (FMath::IsNearlyEqual(Vertices1.Y, SizeMinPoint.Y, ADAPTIVE_TOLERANCE) || Vertices1.Y < SizeMinPoint.Y)
							&& (FMath::IsNearlyEqual(Vertices2.Y, SizeMinPoint.Y, ADAPTIVE_TOLERANCE) || Vertices2.Y < SizeMinPoint.Y))
						|| ((FMath::IsNearlyEqual(Vertices0.Y, SizeMaxPoint.Y, ADAPTIVE_TOLERANCE) || Vertices0.Y > SizeMaxPoint.Y)
							&& (FMath::IsNearlyEqual(Vertices1.Y, SizeMaxPoint.Y, ADAPTIVE_TOLERANCE) || Vertices1.Y > SizeMaxPoint.Y)
							&& (FMath::IsNearlyEqual(Vertices2.Y, SizeMaxPoint.Y, ADAPTIVE_TOLERANCE) || Vertices2.Y > SizeMaxPoint.Y))
					)
					{
						break;
					}
					FVector RelativeIntersectionPoint = GetIntersectionFrame().ToFramePoint(Segment.Point[i]);


					if (FMath::IsNearlyEqual(FMath::Abs(RelativeIntersectionPoint.Z), SizeMaxPoint.Z, ADAPTIVE_TOLERANCE)
						&& FMath::IsNearlyEqual(FMath::Abs(RelativeIntersectionPoint.Y), SizeMaxPoint.Y, ADAPTIVE_TOLERANCE))
					{
						continue;
					}


					if (AdaptiveRule.XAxisRule.AdsorptionRule == EAdsorptionRule::E_Negative)
					{
						FVector Delta = RelativeIntersectionPoint - SizeMaxPoint;

                        if (FMath::IsNearlyEqual(RelativeIntersectionPoint.X,SizeMinPoint.X,ADAPTIVE_TOLERANCE))
                        {
                            //最大最小值相等，忽略
                            break;
                        }

                        if ((!FMath::IsNearlyZero(Delta.X,ADAPATIVE_UNIT)&& Delta.X < 0) && (!FMath::IsNearlyZero(Normal.X, ADAPATIVE_UNIT) && Normal.X < 0))
                        {
                            SizeMaxPoint.X = RelativeIntersectionPoint.X;
                            SetXPositiveDependence(Pair.Key);
                        }

                    }
                    else if (AdaptiveRule.XAxisRule.AdsorptionRule == EAdsorptionRule::E_Positive)
                    {


                        if (FMath::IsNearlyEqual(RelativeIntersectionPoint.X, SizeMaxPoint.X, ADAPTIVE_TOLERANCE))
                        {
                            //最大最小值相等，忽略
                            break;
                        }

                        FVector Delta = RelativeIntersectionPoint - SizeMinPoint;
                        if ((!FMath::IsNearlyZero(Delta.X, ADAPATIVE_UNIT) && Delta.X > 0) && (!FMath::IsNearlyZero(Normal.X, ADAPATIVE_UNIT)&&Normal.X > 0))
                        {
                            SizeMinPoint.X = RelativeIntersectionPoint.X;
                            SetXNegativeDependence(Pair.Key);
                        }
                    }
                    else
                    {

                        if (RelativeIntersectionPoint.X > 0)
                        {
                            FVector Delta = RelativeIntersectionPoint - SizeMaxPoint;

                            if ((!FMath::IsNearlyZero(Delta.X, ADAPATIVE_UNIT) && Delta.X < 0) && (!FMath::IsNearlyZero(Normal.X, ADAPATIVE_UNIT)&&Normal.X < 0))
                            {
                                SizeMaxPoint.X = RelativeIntersectionPoint.X;
                                SetXPositiveDependence(Pair.Key);
                            }
                        }
                        else
                        {
                            FVector Delta = RelativeIntersectionPoint - SizeMinPoint;
                            if ((!FMath::IsNearlyZero(Delta.X, ADAPATIVE_UNIT) && Delta.X > 0) && (!FMath::IsNearlyZero(Normal.X, ADAPATIVE_UNIT)&&Normal.X > 0))
                            {
                                SizeMinPoint.X = RelativeIntersectionPoint.X;
                                SetXNegativeDependence(Pair.Key);
                            }
                        }

                    }

                }
            }
        }
    }
    else
    {
        SizeMaxPoint.X = OutExtents.X;
        SizeMinPoint.X = -OutExtents.X;
    }
    if (AdaptiveRule.ZAxisRule.bAdaptived && InitializedData->bExtentsZEnableAdaptation)
    {
        for (auto& Pair : ResaultMap)
        {
            auto& TargetIntersetionMesh = Pair.Key;
            FAdaptiveResault& Value = Pair.Value;
            if (Value.Resault.Segments.Num() <= 0)
            {
                continue;
            }
            const FDynamicMesh3* TargetDynamicMesh = TargetIntersetionMesh->GetDynamicMeshTree().GetMesh();
            for (auto& Segment : Value.Resault.Segments)
            {
                for (size_t i = 0; i < 2; i++)
                {
                    int  TriID = Segment.TriangleID[1];
                    FVector Normal = TargetDynamicMesh->GetTriNormal(TriID);
                    Normal = GetIntersectionFrame().ToFrameVector(Normal);

					if (FMath::IsNearlyZero(Normal.Z, ADAPTIVE_TOLERANCE))
					{
						break;
					}

					FVector RelativeIntersectionPoint = GetIntersectionFrame().ToFramePoint(Segment.Point[i]);


                    //限制自适应Z时在X自适应后的范围内
                    if (FMath::IsNearlyEqual(RelativeIntersectionPoint.X,SizeMinPoint.X,ADAPTIVE_TOLERANCE) && RelativeIntersectionPoint.X < SizeMinPoint.X
                        || (!FMath::IsNearlyEqual(RelativeIntersectionPoint.X, SizeMaxPoint.X, ADAPTIVE_TOLERANCE))&&RelativeIntersectionPoint.X > SizeMaxPoint.X)
                    {
                        continue;
                    }

					FVector Vertices0, Vertices1, Vertices2;
					TargetDynamicMesh->GetTriVertices(TriID, Vertices0, Vertices1, Vertices2);
					Vertices0 = GetIntersectionFrame().ToFramePoint(Vertices0);
					Vertices1 = GetIntersectionFrame().ToFramePoint(Vertices1);
					Vertices2 = GetIntersectionFrame().ToFramePoint(Vertices2);


                    if (((FMath::IsNearlyEqual(Vertices0.X, SizeMinPoint.X, ADAPTIVE_TOLERANCE) || Vertices0.X < SizeMinPoint.X)
                        && (FMath::IsNearlyEqual(Vertices1.X, SizeMinPoint.X, ADAPTIVE_TOLERANCE) || Vertices1.X < SizeMinPoint.X)
                        && (FMath::IsNearlyEqual(Vertices2.X, SizeMinPoint.X, ADAPTIVE_TOLERANCE) || Vertices2.X < SizeMinPoint.X))
                        || ((FMath::IsNearlyEqual(Vertices0.X, SizeMaxPoint.X, ADAPTIVE_TOLERANCE) || Vertices0.X > SizeMaxPoint.X)
                            && (FMath::IsNearlyEqual(Vertices1.X, SizeMaxPoint.X, ADAPTIVE_TOLERANCE) || Vertices1.X > SizeMaxPoint.X)
                            && (FMath::IsNearlyEqual(Vertices2.X, SizeMaxPoint.X, ADAPTIVE_TOLERANCE) || Vertices2.X > SizeMaxPoint.X))
                        || ((FMath::IsNearlyEqual(Vertices0.Y, SizeMinPoint.Y, ADAPTIVE_TOLERANCE) || Vertices0.Y < SizeMinPoint.Y)
                            && (FMath::IsNearlyEqual(Vertices1.Y, SizeMinPoint.Y, ADAPTIVE_TOLERANCE) || Vertices1.Y < SizeMinPoint.Y)
                            && (FMath::IsNearlyEqual(Vertices2.Y, SizeMinPoint.Y, ADAPTIVE_TOLERANCE) || Vertices2.Y < SizeMinPoint.Y))
                        || ((FMath::IsNearlyEqual(Vertices0.Y, SizeMaxPoint.Y, ADAPTIVE_TOLERANCE) || Vertices0.Y > SizeMaxPoint.Y)
                            && (FMath::IsNearlyEqual(Vertices1.Y, SizeMaxPoint.Y, ADAPTIVE_TOLERANCE) || Vertices1.Y > SizeMaxPoint.Y)
                            && (FMath::IsNearlyEqual(Vertices2.Y, SizeMaxPoint.Y, ADAPTIVE_TOLERANCE) || Vertices2.Y > SizeMaxPoint.Y))
                        )
                    {
                        break;
                    }
                    if (AdaptiveRule.ZAxisRule.AdsorptionRule == EAdsorptionRule::E_Negative)
                    {
                        if (FMath::IsNearlyEqual(RelativeIntersectionPoint.Z, SizeMinPoint.Z, ADAPTIVE_TOLERANCE))
                        {
                            //最大最小值相等，忽略
                            break;
                        }
                        FVector Delta = RelativeIntersectionPoint - SizeMaxPoint;
                        if ((!FMath::IsNearlyZero(Delta.Z,ADAPATIVE_UNIT)&&Delta.Z < 0) && (!FMath::IsNearlyZero(Normal.Z, ADAPATIVE_UNIT)&&Normal.Z < 0))
                        {
                            SizeMaxPoint.Z = RelativeIntersectionPoint.Z;
                            SetZPositiveDependence(Pair.Key);
                        }

                    }
                    else if (AdaptiveRule.ZAxisRule.AdsorptionRule == EAdsorptionRule::E_Positive)
                    {
                        if (FMath::IsNearlyEqual(RelativeIntersectionPoint.Z, SizeMaxPoint.Z, ADAPTIVE_TOLERANCE))
                        {
                            //最大最小值相等，忽略
                            break;
                        }
                        FVector Delta = RelativeIntersectionPoint - SizeMinPoint;
                        if ((!FMath::IsNearlyZero(Delta.Z, ADAPATIVE_UNIT) && Delta.Z > 0) && (!FMath::IsNearlyZero(Normal.Z, ADAPATIVE_UNIT) && Normal.Z > 0))
                        {
                            SizeMinPoint.Z = RelativeIntersectionPoint.Z;
                            SetZNegativeDependence(Pair.Key);
                        }
                    }
                    else
                    {
                        if (RelativeIntersectionPoint.Z > 0)
                        {
                            FVector Delta = RelativeIntersectionPoint - SizeMaxPoint;

                            if ((!FMath::IsNearlyZero(Delta.Z, ADAPATIVE_UNIT) && Delta.Z < 0 )&& (!FMath::IsNearlyZero(Normal.Z, ADAPATIVE_UNIT) && Normal.Z < 0))
                            {
                                SizeMaxPoint.Z = RelativeIntersectionPoint.Z;
                                SetZPositiveDependence(Pair.Key);
                            }
                        }
                        else
                        {
                            FVector Delta = RelativeIntersectionPoint - SizeMinPoint;
                            if ((!FMath::IsNearlyZero(Delta.Z, ADAPATIVE_UNIT) && Delta.Z > 0) && (!FMath::IsNearlyZero(Normal.Z, ADAPATIVE_UNIT) && Normal.Z > 0))
                            {
                                SizeMinPoint.Z = RelativeIntersectionPoint.Z;
                                SetZNegativeDependence(Pair.Key);
                            }
                        }

                    }
                }
            }
        }
    }
    else
    {
        SizeMaxPoint.Z = OutExtents.Z;
        SizeMinPoint.Z = -OutExtents.Z;
    }
    CalculateYAxisAdsorption(SizeMinPoint, SizeMaxPoint, OutExtents, OutCenter,Env);
    //应用当前吸附对象XZ方向特殊宽特殊高
    CalculateXZAxisAdsorption(SizeMinPoint, SizeMaxPoint, OutExtents, OutCenter);

}


FAdaptationEvenInfo FDynamicMeshAdaptiveAdsorption::ExecuteEven(const TArray<TSharedPtr<FIntersectionDynamicMesh>>& Env)
{
   const auto& OutAdaptationOriBox = OutAdaptationData->OriBox;
    FAdaptationEvenInfo EvenInfo;
    EvenInfo.SourceExtents = OutAdaptationOriBox.Extents;
    EvenInfo.SourceBox = OutAdaptationOriBox;
    EvenInfo.SpaceBox.Frame.Rotation = OutAdaptationOriBox.Frame.Rotation;
    EvenInfo.SpaceBox.Extents = OutAdaptationOriBox.Extents;
    FVector RelativeSpaceCenter = FVector::ZeroVector;
    const FVector StartPoint = OutAdaptationOriBox.Center();

    //DrawDebugPoint(GWorld, StartPoint, 2.f, FColor::Green, true);

    //if (!AdaptiveRule.XAxisRule.bAdaptived)
    {
        //当前鼠标向左向右检测
        //向左
        FRayHitResault XNegativeResault;
        bool bXNegativeHit = FindHitNearestPoint(StartPoint, -GetIntersectionFrame().X(), Env, XNegativeResault);
        //向右
        FRayHitResault XPositiveResault;
        bool bXPositiveHit = FindHitNearestPoint(StartPoint, GetIntersectionFrame().X(), Env, XPositiveResault);

        double Distance = 0.f;

        if (bXNegativeHit)
        {
            Distance += XNegativeResault.Distance;
            EvenInfo.AddAroundEnv(EAdaptationDirection::E_Backward, XNegativeResault.HitTarget);
        }
        if (bXPositiveHit)
        {
            Distance += XPositiveResault.Distance;
            EvenInfo.AddAroundEnv(EAdaptationDirection::E_Forward, XPositiveResault.HitTarget);
        }

        if (!FMath::IsNearlyZero(Distance, ADAPTIVE_TOLERANCE))
        {
            EvenInfo.SpaceBox.Extents.X = Distance/2;
        }

        RelativeSpaceCenter.X = (XPositiveResault.Distance - XNegativeResault.Distance)/2.f;
    }
   //else
    {
        if (XNegativeAdsorption.IsValid())
        {
            EvenInfo.AddAroundEnv(EAdaptationDirection::E_Backward, XNegativeAdsorption.Pin());
        }
        if (XPositiveAdsorption.IsValid())
        {
            EvenInfo.AddAroundEnv(EAdaptationDirection::E_Forward, XPositiveAdsorption.Pin());
        }
    }
    //if (!AdaptiveRule.ZAxisRule.bAdaptived)
    {
        FRayHitResault ZNegativeResault;
        bool bZNegativeHit = FindHitNearestPoint(StartPoint, -GetIntersectionFrame().Z(), Env, ZNegativeResault);
        //向上
        FRayHitResault ZPositiveResault;
        bool bZPositiveHit = FindHitNearestPoint(StartPoint, GetIntersectionFrame().Z(), Env, ZPositiveResault);

        double ZDistance = 0.f;
        if (bZNegativeHit) //负向相交向正向偏移
        {
            ZDistance += ZNegativeResault.Distance;
            EvenInfo.AddAroundEnv(EAdaptationDirection::E_Down, ZNegativeResault.HitTarget);
        }
        if (bZPositiveHit)//正向相交向负向偏移
        {
            ZDistance += ZPositiveResault.Distance;
            EvenInfo.AddAroundEnv(EAdaptationDirection::E_Up, ZPositiveResault.HitTarget);
        }

        if (!FMath::IsNearlyZero(ZDistance, ADAPTIVE_TOLERANCE))
        {
            EvenInfo.SpaceBox.Extents.Z = ZDistance/2;
        }
        RelativeSpaceCenter.Z = (ZPositiveResault.Distance - ZNegativeResault.Distance)/2.f;

    }
    //else
    {
        if (ZNegativeAdsorption.IsValid())
        {
            EvenInfo.AddAroundEnv(EAdaptationDirection::E_Down, ZNegativeAdsorption.Pin());
        }
        if (ZPositiveAdsorption.IsValid())
        {
            EvenInfo.AddAroundEnv(EAdaptationDirection::E_Up ,ZPositiveAdsorption.Pin());
        }
    }
    EvenInfo.SpaceBox.Frame.Origin = OutAdaptationOriBox.Frame.FromFramePoint(RelativeSpaceCenter);

    if (!AdaptiveRule.XAxisRule.bAdaptived)
    {
        EvenInfo.MaxEvenCount = EvenInfo.SpaceBox.Extents.X / EvenInfo.SourceBox.Extents.X;
    }
    else if(!AdaptiveRule.ZAxisRule.bAdaptived)
    {
        EvenInfo.MaxEvenCount = EvenInfo.SpaceBox.Extents.Z / EvenInfo.SourceBox.Extents.Z;
    }
    return EvenInfo;
}

FAdaptationEvenInfo FDynamicMeshAdaptiveAdsorption::ExecuteEvenReal(const TArray<TSharedPtr<FIntersectionDynamicMesh>>& Env)
{
	const auto& OutAdaptationOriBox = OutAdaptationData->OriBox;
	FAdaptationEvenInfo EvenInfo;
	EvenInfo.SourceExtents = OutAdaptationOriBox.Extents;
	EvenInfo.SourceBox = OutAdaptationOriBox;
	EvenInfo.SpaceBox.Frame.Rotation = OutAdaptationOriBox.Frame.Rotation;
	EvenInfo.SpaceBox.Extents = OutAdaptationOriBox.Extents;
	FVector RelativeSpaceCenter = FVector::ZeroVector;
	const FVector StartPoint = OutAdaptationOriBox.Center();

	//if (!AdaptiveRule.XAxisRule.bAdaptived)
	{
		//当前鼠标向左向右检测
		//向左
		FRayHitResault XNegativeResault;
		bool bXNegativeHit = FindHitNearestPoint(StartPoint, -GetIntersectionFrame().X(), Env, XNegativeResault);
		//向右
		FRayHitResault XPositiveResault;
		bool bXPositiveHit = FindHitNearestPoint(StartPoint, GetIntersectionFrame().X(), Env, XPositiveResault);

		double Distance = 0.f;

		if (bXNegativeHit)
		{
			Distance += XNegativeResault.Distance;
			EvenInfo.AddAroundEnv(EAdaptationDirection::E_Backward, XNegativeResault.HitTarget);
		}
		if (bXPositiveHit)
		{
			Distance += XPositiveResault.Distance;
			EvenInfo.AddAroundEnv(EAdaptationDirection::E_Forward, XPositiveResault.HitTarget);
		}

		if (!FMath::IsNearlyZero(Distance, ADAPTIVE_TOLERANCE))
		{
			EvenInfo.SpaceBox.Extents.X = Distance / 2;
		}

		RelativeSpaceCenter.X = (XPositiveResault.Distance - XNegativeResault.Distance) / 2.f;
	}
	
	//if (!AdaptiveRule.ZAxisRule.bAdaptived)
	{
		FRayHitResault ZNegativeResault;
		bool bZNegativeHit = FindHitNearestPoint(StartPoint, -GetIntersectionFrame().Z(), Env, ZNegativeResault);
		//向上
		FRayHitResault ZPositiveResault;
		bool bZPositiveHit = FindHitNearestPoint(StartPoint, GetIntersectionFrame().Z(), Env, ZPositiveResault);

		double ZDistance = 0.f;
		if (bZNegativeHit) //负向相交向正向偏移
		{
			ZDistance += ZNegativeResault.Distance;
			EvenInfo.AddAroundEnv(EAdaptationDirection::E_Down, ZNegativeResault.HitTarget);
		}
		if (bZPositiveHit)//正向相交向负向偏移
		{
			ZDistance += ZPositiveResault.Distance;
			EvenInfo.AddAroundEnv(EAdaptationDirection::E_Up, ZPositiveResault.HitTarget);
		}

		if (!FMath::IsNearlyZero(ZDistance, ADAPTIVE_TOLERANCE))
		{
			EvenInfo.SpaceBox.Extents.Z = ZDistance / 2;
		}
		RelativeSpaceCenter.Z = (ZPositiveResault.Distance - ZNegativeResault.Distance) / 2.f;

	}

	EvenInfo.SpaceBox.Frame.Origin = OutAdaptationOriBox.Frame.FromFramePoint(RelativeSpaceCenter);

	if (!AdaptiveRule.XAxisRule.bAdaptived)
	{
		EvenInfo.MaxEvenCount = EvenInfo.SpaceBox.Extents.X / EvenInfo.SourceBox.Extents.X;
	}
	else if (!AdaptiveRule.ZAxisRule.bAdaptived)
	{
		EvenInfo.MaxEvenCount = EvenInfo.SpaceBox.Extents.Z / EvenInfo.SourceBox.Extents.Z;
	}
	return EvenInfo;
}


void FDynamicMeshAdaptiveAdsorption::SetAdaptiveRule(const FAdaptiveAdsorptionRule3D& NewRule)
{
    AdaptiveRule = NewRule;
    //AdaptiveRule.XAxisRule.NegativeOffset = 10.f;
    //AdaptiveRule.YAxisRule.NegativeOffset = 10.f;
    //AdaptiveRule.ZAxisRule.NegativeOffset = 10.f;
    //AdaptiveRule.XAxisRule.PositiveOffset = 10.f;
    //AdaptiveRule.YAxisRule.PositiveOffset = 10.f;
    //AdaptiveRule.ZAxisRule.PositiveOffset = 10.f;
}


const FFrame3d& FDynamicMeshAdaptiveAdsorption::GetIntersectionFrame()
{
	return SourceIntersectionData->GetSelfFrame();
	// TODO: 在此处插入 return 语句
}

const FIntersectionDynamicMesh& FDynamicMeshAdaptiveAdsorption::GetIntersectionData() const
{
	return *SourceIntersectionData;
}

FOrientedBox3d FDynamicMeshAdaptiveAdsorption::GetAdaptationOriBoxWithoutAdaptiveRulerOffset()
{
    FOrientedBox3d OutOriBox = GetAdaptationOriBox();
    ApplyAdsorptionOffsetAfter(OutOriBox);
    return OutOriBox;
}

void FDynamicMeshAdaptiveAdsorption::HandleAdaptiveAndAdsorptionWithHitResault(bool bHasHit,const FRayHitResault& HitResault,const TArray<TSharedPtr<FIntersectionDynamicMesh>>& AdaptationEnvs)
{
    TSharedPtr<FFunctionalAdaptationData> FunctionalAdaptationData = StaticCastSharedPtr<FFunctionalAdaptationData>(OutAdaptationData);
    TSharedPtr<FFunctionalExecuterInitializedData>FunctionalInitializedData = StaticCastSharedPtr<FFunctionalExecuterInitializedData>(InitializedData);
    if (bHasHit)
    {
        FunctionalAdaptationData->bDependent = true;
        //确定吸附锚点,确定自适应包围盒位置
        HandleAddsorption(AdaptationOriBox, HitResault, AdaptationEnvs);
        //UE_LOG(AdaptationExecuterLog, Log, TEXT("1.AfterAddsorption:%s"), *AdaptationOriBox.Center().ToString());
        //预处理相交包围盒
        PreHandleAdaptiveOriBox(AdaptationEnvs, HitResault, AdaptationOriBox);

        //UE_LOG(AdaptationExecuterLog, Log, TEXT("1.PreHandleAdaptiveOriBox%s:%s"), *SourceIntersectionData->GetDebugName(), *AdaptationOriBox.Frame.FromFramePoint(-AdaptationOriBox.Extents).ToString());

        //自适应包围盒
        HandleAdaptive(AdaptationEnvs, HitResault.HitTarget, AdaptationOriBox);

        //UE_LOG(AdaptationExecuterLog, Log, TEXT("1.HandleAdaptive%s:%s"), *SourceIntersectionData->GetDebugName(), *AdaptationOriBox.Frame.FromFramePoint(-AdaptationOriBox.Extents).ToString());

        // LogDenpendence();
        FOrientedBox3d ExecutedBox = AdaptationOriBox;

        ApplyAdsorptionOffsetAfter(ExecutedBox);
        ApplyFixedExtentsAfter(ExecutedBox);

        //// 将 Origin 精确到小数点后一位
        //ExecutedBox.Frame.Origin.X = FMath::RoundToFloat(ExecutedBox.Frame.Origin.X * 10.0f) / 10.0f;
        //ExecutedBox.Frame.Origin.Y = FMath::RoundToFloat(ExecutedBox.Frame.Origin.Y * 10.0f) / 10.0f;
        //ExecutedBox.Frame.Origin.Z = FMath::RoundToFloat(ExecutedBox.Frame.Origin.Z * 10.0f) / 10.0f;
        //UE_LOG(AdaptationExecuterLog, Log, TEXT("1.UpdateAdaptationOriBox%s:%s"), *SourceIntersectionData->GetDebugName(), *ExecutedBox.Frame.FromFramePoint(-ExecutedBox.Extents).ToString());

        //UE_LOG(AdaptationExecuterLog, Log, TEXT("UpdateAdaptationOriBoxCenter %s"), *ExecutedBox.Center().ToString());

        UpdateAdaptationOriBox(ExecutedBox);
    }
    else
    {


        if (FunctionalAdaptationData->bDependent)
        {
            bool  bOutCabientOriBox = false;
            if (!FunctionalInitializedData->CabinetOriBox.Contains(AdaptationOriBox.Center()))
            {
                bOutCabientOriBox = AdaptationOriBox.TestCorners([&](const FVector& Corner)
                    {
                        if (!FunctionalInitializedData->CabinetOriBox.Contains(Corner))
                        {
                            return true;
                        }
                        return  false;
                    });
            }

            if (bOutCabientOriBox)
            {
                UpdateAdaptationOriBox(AdaptationOriBox);
                FunctionalAdaptationData->bDependent = false;
            }
        }
        else
        {
            UpdateAdaptationOriBox(AdaptationOriBox);
        }
        SourceIntersectionData->UpdateLocation(AdaptationOriBox.Frame.Origin);
    }
}

void FDynamicMeshAdaptiveAdsorption::ApplyAdaptationOffsetToOrientedBox(const FAdaptiveAdsorptionRule3D& AppliedRule, FOrientedBox3d& OutOriBox)
{
	FVector MinRelativePoint = -OutOriBox.Extents;
	FVector MaxRelativePoint = OutOriBox.Extents;

    if (!FMath::IsNearlyZero(AdaptiveRule.XAxisRule.NegativeOffset, ADAPTIVE_TOLERANCE))
    {
        MinRelativePoint.X -= AdaptiveRule.XAxisRule.NegativeOffset;
    }
    if (!FMath::IsNearlyZero(AdaptiveRule.XAxisRule.PositiveOffset, ADAPTIVE_TOLERANCE))
    {
        MaxRelativePoint.X += AdaptiveRule.XAxisRule.PositiveOffset;
    }

    if (!FMath::IsNearlyZero(AdaptiveRule.YAxisRule.NegativeOffset, ADAPTIVE_TOLERANCE))
    {
        MinRelativePoint.Y -= AdaptiveRule.YAxisRule.NegativeOffset;
    }
    if (!FMath::IsNearlyZero(AdaptiveRule.YAxisRule.PositiveOffset, ADAPTIVE_TOLERANCE))
    {
        MaxRelativePoint.Y += AdaptiveRule.YAxisRule.PositiveOffset;
    }


    if (!FMath::IsNearlyZero(AdaptiveRule.ZAxisRule.NegativeOffset, ADAPTIVE_TOLERANCE))
    {
        MinRelativePoint.Z -= AdaptiveRule.ZAxisRule.NegativeOffset;
    }
    if (!FMath::IsNearlyZero(AdaptiveRule.ZAxisRule.PositiveOffset, ADAPTIVE_TOLERANCE))
    {
        MaxRelativePoint.Z += AdaptiveRule.ZAxisRule.PositiveOffset;
    }

	FVector RelativeCenter = (MaxRelativePoint + MinRelativePoint) * 0.5f;
	OutOriBox.Frame.Origin = OutOriBox.Frame.FromFramePoint(RelativeCenter);
	OutOriBox.Extents = (MaxRelativePoint - MinRelativePoint) * 0.5f;
}

void FDynamicMeshAdaptiveAdsorption::PreHandleAdaptiveOriBox(const TArray<TSharedPtr<FIntersectionDynamicMesh>>& AdaptationEnvs, const FRayHitResault& RayHitResault, FOrientedBox3d& OutOriBox)
{

    TSharedPtr<FFunctionalExecuterInitializedData> FunctionalInitializedData = StaticCastSharedPtr<FFunctionalExecuterInitializedData>(InitializedData);
    const FOrientedBox3d IntersectionOriBox = SourceIntersectionData->GetOriBox();
    const auto& SelfFrame = IntersectionOriBox.Frame;

    FVector OutCenter = IntersectionOriBox.Center();
    FVector OutExtents = IntersectionOriBox.Extents;

    FVector MinYPoint = -UseExtents;
    FVector MaxYPoint = UseExtents;

 

    if (AdaptiveRule.YAxisRule.bAdaptived && InitializedData->bExtentsYEnableAdaptation)
    {
        if (AdaptiveRule.YAxisRule.AdsorptionRule == EAdsorptionRule::E_Positive)
        {
            FVector StartPoint = FVector::RightVector * UseExtents;

            if (AdaptiveRule.XAxisRule.AdsorptionRule == EAdsorptionRule::E_Positive)
            {
                StartPoint += FVector::ForwardVector * (UseExtents - ADAPATIVE_UNIT);
            }
            else if (AdaptiveRule.XAxisRule.AdsorptionRule == EAdsorptionRule::E_Negative)
            {
                StartPoint += FVector::BackwardVector * (UseExtents - ADAPATIVE_UNIT);
            }
            else if (AdaptiveRule.ZAxisRule.AdsorptionRule == EAdsorptionRule::E_Positive)
            {
                StartPoint += FVector::UpVector * (UseExtents - ADAPATIVE_UNIT);
            }
            else if (AdaptiveRule.ZAxisRule.AdsorptionRule == EAdsorptionRule::E_Negative)
            {
                StartPoint += FVector::DownVector * (UseExtents - ADAPATIVE_UNIT);
            }
            StartPoint = SelfFrame.FromFramePoint(StartPoint);
            FRayHitResault HitResault;
            bool bHit = FindHitNearestPoint(StartPoint, -SelfFrame.Y(),UseExtents.Y*2.f,AdaptationEnvs, HitResault);

            if (bHit)
            {
                MinYPoint.Y = SelfFrame.ToFramePoint(HitResault.HitPoint).Y;
            }
            else
            {
                FVector CabinetWorldMinPoint = FunctionalInitializedData->CabinetOriBox.Frame.FromFramePoint(FVector::LeftVector * FunctionalInitializedData->CabinetOriBox.Extents);
                MinYPoint.Y = SelfFrame.ToFramePoint(CabinetWorldMinPoint).Y;
            }
            OutExtents.Y = ((MaxYPoint - MinYPoint) / 2).Y;
        }
    }
    //预处理设置包围盒大小

    if (AdaptiveRule.XAxisRule.bAdaptived && InitializedData->bExtentsXEnableAdaptation)
    {
        if (AdaptiveRule.XAxisRule.AdsorptionRule == EAdsorptionRule::E_Positive && XPositiveAdsorption.IsValid())
        {
            const auto& AdsorptionTargetOriBox = XPositiveAdsorption.Pin()->GetOriBox();
            if (AdaptiveRule.YAxisRule.bAdaptived && InitializedData->bExtentsYEnableAdaptation)
            {
                FVector AdsorptionTargetMinPoint = AdsorptionTargetOriBox.Frame.FromFramePoint(-AdsorptionTargetOriBox.Extents);
                FVector AdsorptionTargetMaxPoint = AdsorptionTargetOriBox.Frame.FromFramePoint(AdsorptionTargetOriBox.Extents);
                AdsorptionTargetMinPoint = SelfFrame.ToFramePoint(AdsorptionTargetMinPoint);
                AdsorptionTargetMaxPoint = SelfFrame.ToFramePoint(AdsorptionTargetMaxPoint);
                MinYPoint.Y = FMath::Max(MinYPoint.Y, AdsorptionTargetMinPoint.Y);
                MaxYPoint.Y = FMath::Min(MaxYPoint.Y, AdsorptionTargetMaxPoint.Y);
            }

            FRayHitResault HitResault;
            FVector StartPoint = RayHitResault.HitPoint + (RayHitResault.HitNormal * ADAPATIVE_UNIT);
            StartPoint.X = SelfFrame.FromFramePoint(FVector::ForwardVector * OutExtents).X;
            bool bHit = FindHitNearestPoint(StartPoint, -IntersectionOriBox.AxisX(), UseExtents.X * 2.f
                , AdaptationEnvs, HitResault, XPositiveAdsorption.Pin());
            if (bHit)
            {
                SetXNegativeDependence(HitResault.HitTarget);
                MinYPoint.X = SelfFrame.ToFramePoint(HitResault.HitPoint).X;
            }
        }
        else if (AdaptiveRule.XAxisRule.AdsorptionRule == EAdsorptionRule::E_Negative && XNegativeAdsorption.IsValid())
        {

            const auto& AdsorptionTargetOriBox = XNegativeAdsorption.Pin()->GetOriBox();
            if (AdaptiveRule.YAxisRule.bAdaptived && InitializedData->bExtentsYEnableAdaptation)
            {
                FVector AdsorptionTargetMinPoint = AdsorptionTargetOriBox.Frame.FromFramePoint(-AdsorptionTargetOriBox.Extents);
                FVector AdsorptionTargetMaxPoint = AdsorptionTargetOriBox.Frame.FromFramePoint(AdsorptionTargetOriBox.Extents);
                AdsorptionTargetMinPoint = SelfFrame.ToFramePoint(AdsorptionTargetMinPoint);
                AdsorptionTargetMaxPoint = SelfFrame.ToFramePoint(AdsorptionTargetMaxPoint);
                MinYPoint.Y = FMath::Max(MinYPoint.Y, AdsorptionTargetMinPoint.Y);
                MaxYPoint.Y = FMath::Min(MaxYPoint.Y, AdsorptionTargetMaxPoint.Y);
            }
            FRayHitResault HitResault;
            FVector StartPoint = RayHitResault.HitPoint+(RayHitResault.HitNormal * ADAPATIVE_UNIT);
            StartPoint.X = SelfFrame.FromFramePoint(FVector::BackwardVector * OutExtents).X;
            bool bHit = FindHitNearestPoint(StartPoint, IntersectionOriBox.AxisX(),UseExtents.X*2.f, 
                AdaptationEnvs, HitResault,XNegativeAdsorption.Pin());
            if (bHit)
            {
                SetXPositiveDependence(HitResault.HitTarget);
                MaxYPoint.X = SelfFrame.ToFramePoint(HitResault.HitPoint).X;
            }
        }
        OutExtents.X = ((MaxYPoint - MinYPoint) / 2).X;
    }

    if (AdaptiveRule.ZAxisRule.bAdaptived && InitializedData->bExtentsZEnableAdaptation)
    {
        if (AdaptiveRule.ZAxisRule.AdsorptionRule == EAdsorptionRule::E_Positive&& ZPositiveAdsorption.IsValid())
        {
            const auto& AdsorptionTargetOriBox = ZPositiveAdsorption.Pin()->GetOriBox();

            if (AdaptiveRule.YAxisRule.bAdaptived && InitializedData->bExtentsYEnableAdaptation)
            {
                FVector AdsorptionTargetMinPoint = AdsorptionTargetOriBox.Frame.FromFramePoint(-AdsorptionTargetOriBox.Extents);
                FVector AdsorptionTargetMaxPoint = AdsorptionTargetOriBox.Frame.FromFramePoint(AdsorptionTargetOriBox.Extents);
                AdsorptionTargetMinPoint = SelfFrame.ToFramePoint(AdsorptionTargetMinPoint);
                AdsorptionTargetMaxPoint = SelfFrame.ToFramePoint(AdsorptionTargetMaxPoint);
                MinYPoint.Y = FMath::Max(MinYPoint.Y, AdsorptionTargetMinPoint.Y);
                MaxYPoint.Y = FMath::Min(MaxYPoint.Y, AdsorptionTargetMaxPoint.Y);
            }
            FRayHitResault HitResault;
            FVector StartPoint = RayHitResault.HitPoint + (RayHitResault.HitNormal * ADAPATIVE_UNIT);
            StartPoint.Z = SelfFrame.FromFramePoint(FVector::UpVector * OutExtents).Z;
            bool bHit = FindHitNearestPoint(StartPoint, -IntersectionOriBox.AxisZ(), UseExtents.Z*2.f,
                AdaptationEnvs, HitResault,ZPositiveAdsorption.Pin());
            if (bHit)
            {
                SetZNegativeDependence(HitResault.HitTarget);
                MinYPoint.Z = SelfFrame.ToFramePoint(HitResault.HitPoint).Z;
            }
        }
        else if (AdaptiveRule.ZAxisRule.AdsorptionRule == EAdsorptionRule::E_Negative&& ZNegativeAdsorption.IsValid())
        {
            const auto& AdsorptionTargetOriBox = ZNegativeAdsorption.Pin()->GetOriBox();


            if (AdaptiveRule.YAxisRule.bAdaptived && InitializedData->bExtentsYEnableAdaptation)
            {
                FVector AdsorptionTargetMinPoint = AdsorptionTargetOriBox.Frame.FromFramePoint(-AdsorptionTargetOriBox.Extents);
                FVector AdsorptionTargetMaxPoint = AdsorptionTargetOriBox.Frame.FromFramePoint(AdsorptionTargetOriBox.Extents);
                AdsorptionTargetMinPoint = SelfFrame.ToFramePoint(AdsorptionTargetMinPoint);
                AdsorptionTargetMaxPoint = SelfFrame.ToFramePoint(AdsorptionTargetMaxPoint);
                MinYPoint.Y = FMath::Max(MinYPoint.Y, AdsorptionTargetMinPoint.Y);
                MaxYPoint.Y = FMath::Min(MaxYPoint.Y, AdsorptionTargetMaxPoint.Y);
            }
            FRayHitResault HitResault;
            FVector StartPoint = RayHitResault.HitPoint + (RayHitResault.HitNormal * ADAPATIVE_UNIT);
            StartPoint.Z = SelfFrame.FromFramePoint(FVector::DownVector * OutExtents).Z;
            bool bHit = FindHitNearestPoint(StartPoint, IntersectionOriBox.AxisZ(), UseExtents.Z * 2.f,
                AdaptationEnvs, HitResault,ZNegativeAdsorption.Pin());
            if (bHit)
            {
                SetZPositiveDependence(HitResault.HitTarget);
                MaxYPoint.Z = SelfFrame.ToFramePoint(HitResault.HitPoint).Z;
            }
        }

        OutExtents.Z = ((MaxYPoint - MinYPoint) / 2).Z;
    }


    //柜体极值限制包围盒
    FVector CarbinetRelativeMaxPoint = SelfFrame.ToFramePoint(FunctionalInitializedData->CabinetOriBox.Frame.FromFramePoint(FunctionalInitializedData->CabinetOriBox.Extents));


    if (AdaptiveRule.XAxisRule.bAdaptived && InitializedData->bExtentsXEnableAdaptation)
        MaxYPoint.X = FMath::Min(MaxYPoint.X, CarbinetRelativeMaxPoint.X);
    if (AdaptiveRule.YAxisRule.bAdaptived && InitializedData->bExtentsYEnableAdaptation)
        MaxYPoint.Y = FMath::Min(MaxYPoint.Y, CarbinetRelativeMaxPoint.Y);
    if (AdaptiveRule.ZAxisRule.bAdaptived && InitializedData->bExtentsZEnableAdaptation)
        MaxYPoint.Z = FMath::Min(MaxYPoint.Z, CarbinetRelativeMaxPoint.Z);

    OutExtents = ((MaxYPoint - MinYPoint) / 2);

    OutCenter = SelfFrame.FromFramePoint(((MaxYPoint + MinYPoint) / 2));
    OutOriBox.Extents = OutExtents;
    OutOriBox.Frame.Origin = OutCenter;
    SourceIntersectionData->UpdateExtentsAndLocation(OutExtents, OutCenter);
}
void FDynamicMeshAdaptiveAdsorption::CaculateAdsorptionPoint(FVector& OutAdsorptionPoint, const FRayHitResault& HitResault, const FVector& InUseExtent, const TArray<TSharedPtr<FIntersectionDynamicMesh>>& Env)
{
	//先判断深度
	CalculateAdsorptionPoint_Y(OutAdsorptionPoint, HitResault, InUseExtent, Env);
	CalculateAdsorptionPoint_X(OutAdsorptionPoint, HitResault, InUseExtent, Env);
	CalculateAdsorptionPoint_Z(OutAdsorptionPoint, HitResault, InUseExtent, Env);
}

void FDynamicMeshAdaptiveAdsorption::CalculateAdsorptionPoint_X(FVector& OutAdsorptionPoint, const FRayHitResault& HitResault, const FVector& InUseExtent,
                                                                const TArray<TSharedPtr<FIntersectionDynamicMesh>>& Env)
{
    TSharedPtr<FFunctionalExecuterInitializedData> FunctionalInitializedData = StaticCastSharedPtr<FFunctionalExecuterInitializedData>(InitializedData);

    double XMinDistance = MAX_dbl;
    FVector StartNormal = GetIntersectionData().GetSelfFrame().ToFrameVector(HitResault.HitNormal);
    //将射线向射线方向偏移防止检测不到对象
    FVector StartPoint = HitResault.HitPoint + HitResault.HitNormal * ADAPTIVE_TOLERANCE;
    if (AdaptiveRule.XAxisRule.AdsorptionRule == EAdsorptionRule::E_Positive)
    {
        if (FMath::IsNearlyZero(StartNormal.X, ADAPTIVE_TOLERANCE) || (StartNormal.X >= 0))
        {
            FRayHitResault Resault;
            bool bHit = FindHitNearestPoint(StartPoint, GetIntersectionFrame().X(), Env, Resault, HitResault.HitTarget);
            if (bHit)
            {
                if (!FMath::IsNearlyZero(StartNormal.X, ADAPTIVE_TOLERANCE) && StartNormal.X >= 0)
                {
                    Resault.Distance += ADAPTIVE_TOLERANCE;
                }
                SetXPositiveDependence(Resault.HitTarget);
                FVector XOffset = GetIntersectionFrame().FromFrameVector(FVector(Resault.Distance, 0, 0) - FVector(InUseExtent.X, 0, 0));
                OutAdsorptionPoint += XOffset;
            }
            else
            {
                //没有检测到对象以柜体包围盒为边缘
                SetXPositiveDependence(nullptr);
                FVector  CabientLeftRelativePoint = GetIntersectionFrame().ToFrameVector(FunctionalInitializedData->CabinetOriBox.Frame.FromFramePoint(FunctionalInitializedData->CabinetOriBox.Extents) - OutAdsorptionPoint);
                FVector XOffset = GetIntersectionFrame().FromFrameVector(FVector(CabientLeftRelativePoint.X, 0, 0)- FVector(InUseExtent.X, 0, 0));
                OutAdsorptionPoint += XOffset;
            }
        }
        else
        {
            SetXPositiveDependence(HitResault.HitTarget);
            FVector XOffset = GetIntersectionFrame().FromFrameVector(-FVector(InUseExtent.X, 0, 0));
            OutAdsorptionPoint += XOffset;
        }
    }
    else if (AdaptiveRule.XAxisRule.AdsorptionRule == EAdsorptionRule::E_Negative)
    {

        if (FMath::IsNearlyZero(StartNormal.X, ADAPTIVE_TOLERANCE) || (StartNormal.X <= 0))
        {
            FRayHitResault Resault;
            bool bHit = FindHitNearestPoint(StartPoint, -GetIntersectionFrame().X(), Env, Resault, HitResault.HitTarget);
            if (bHit)
            {
                if (!FMath::IsNearlyZero(StartNormal.X, ADAPTIVE_TOLERANCE)&&StartNormal.X <= 0)
                {
                    Resault.Distance += ADAPTIVE_TOLERANCE;
                }
                SetXNegativeDependence(Resault.HitTarget);
                FVector XOffset = GetIntersectionFrame().FromFrameVector(FVector(-Resault.Distance, 0, 0) + FVector(InUseExtent.X, 0, 0));
                OutAdsorptionPoint += XOffset;
            }
            else
            {
                SetXNegativeDependence(nullptr);
                FVector  CabientLeftRelativePoint = GetIntersectionFrame().ToFrameVector(FunctionalInitializedData->CabinetOriBox.Frame.FromFramePoint(-FunctionalInitializedData->CabinetOriBox.Extents)- OutAdsorptionPoint);
                
                UE_LOG(AdaptationExecuterLog, Log, TEXT("CalculateAdsorptionPoint_X:%s:%s"), *OutAdsorptionPoint.ToString(),*CabientLeftRelativePoint.ToString());
                FVector XOffset = GetIntersectionFrame().FromFrameVector(FVector(CabientLeftRelativePoint.X, 0, 0) + FVector(InUseExtent.X, 0, 0));
                OutAdsorptionPoint += XOffset;
            }
        }
        else
        {
            if (HitResault.HitTarget == nullptr)
            {
                SetXNegativeDependence(nullptr);
                FVector  CabientLeftRelativePoint = GetIntersectionFrame().ToFrameVector(FunctionalInitializedData->CabinetOriBox.Frame.FromFramePoint(-FunctionalInitializedData->CabinetOriBox.Extents) - OutAdsorptionPoint);

                UE_LOG(AdaptationExecuterLog, Log, TEXT("CalculateAdsorptionPoint_X:%s:%s"), *OutAdsorptionPoint.ToString(), *CabientLeftRelativePoint.ToString());
                FVector XOffset = GetIntersectionFrame().FromFrameVector(FVector(CabientLeftRelativePoint.X, 0, 0) + FVector(InUseExtent.X, 0, 0));
                OutAdsorptionPoint += XOffset;
            }
            else
            {
                SetXNegativeDependence(HitResault.HitTarget);
                FVector XOffset = GetIntersectionFrame().FromFrameVector(FVector(InUseExtent.X, 0, 0));
                OutAdsorptionPoint += XOffset;
            }
        }
    }
    else //避让逻辑 和距离吸附逻辑
    {
        if (FMath::IsNearlyZero(StartNormal.X, ADAPTIVE_TOLERANCE))
        {
            //当前鼠标向左向右检测
            //向左
            FRayHitResault XNegativeResault;
            double   XNegativeDistance = InUseExtent.X + FunctionalInitializedData->DistanceAdsorptionThreshold;
            bool bXNegativeHit = FindHitNearestPoint(StartPoint, -GetIntersectionFrame().X(), XNegativeDistance, Env, XNegativeResault, HitResault.HitTarget);
            //向右
            FRayHitResault XPositiveResault;
            double XPositiveDistance = InUseExtent.X + FunctionalInitializedData->DistanceAdsorptionThreshold;
            bool bXPositiveHit = FindHitNearestPoint(StartPoint, GetIntersectionFrame().X(), XPositiveDistance, Env, XPositiveResault, HitResault.HitTarget);

            if (bXNegativeHit && !bXPositiveHit) //负向相交向正向偏移
            {
                FVector XOffset = GetIntersectionFrame().FromFrameVector(FVector(-XNegativeResault.Distance, 0, 0) + FVector(InUseExtent.X, 0, 0));
                OutAdsorptionPoint += XOffset;
            }
            else if (!bXNegativeHit && bXPositiveHit)//正向相交向负向偏移
            {
                FVector XOffset = GetIntersectionFrame().FromFrameVector(FVector(XPositiveResault.Distance, 0, 0) - FVector(InUseExtent.X, 0, 0));
                OutAdsorptionPoint += XOffset;
            }
            else if (!bXNegativeHit && !bXPositiveHit && FunctionalInitializedData->bEnableAlignedAdsorption)
            {
                //都没有相交判断临近对齐
                //Calculate
                auto SelfOriBox = SourceIntersectionData->GetOriBox();
                SelfOriBox.Frame.Origin = OutAdsorptionPoint;
                for (auto& Iter : Env)
                {
                    if (Iter->GetIntersectionType() != EIntersectionDataType::E_Functional)
                    {
                        continue;
                    }

                    const FOrientedBox3d& TargetOriBox = Iter->GetOriBox();
                    FVector TargetBackward = TargetOriBox.Frame.FromFramePoint(FVector::BackwardVector * TargetOriBox.Extents);

                    TargetBackward = SelfOriBox.Frame.ToFramePoint(TargetBackward);

                    double DeltaBackward = TargetBackward.X + SelfOriBox.Extents.X;

                    FVector TargetForward = TargetOriBox.Frame.FromFramePoint(FVector::ForwardVector * TargetOriBox.Extents);
                    TargetForward = SelfOriBox.Frame.ToFramePoint(TargetForward);
                    double DeltaForward =  TargetForward.X - SelfOriBox.Extents.X;
                    double Delta = FMath::Abs(DeltaForward) > FMath::Abs(DeltaBackward) ? DeltaBackward : DeltaForward;

                    if (FMath::Abs(Delta) <FunctionalInitializedData->AlignedAdsorptionThreshold)
                    {

                        if (FMath::IsNearlyZero(Delta, ADAPTIVE_TOLERANCE))
                        {
                            break;
                        }
                        FVector XOffset = GetIntersectionFrame().FromFrameVector(FVector(Delta,0,0));
                        OutAdsorptionPoint += XOffset;
                        break;
                    }

                    //if (FMath::Abs(Delta) < InitializedData.AlignedAdsorptionThreshold)
                    //{
                    //    FVector XOffset = GetIntersectionFrame().FromFrameVector(FVector(Delta, 0, 0));
                    //    OutAdsorptionPoint += XOffset;
                    //    break;
                    //}
                }
            }
            //左右都有相交忽略
        }
        else if (StartNormal.X > 0)
        {
            FVector XOffset = GetIntersectionFrame().FromFrameVector(FVector(InUseExtent.X, 0, 0));
            OutAdsorptionPoint += XOffset;
        }
        else
        {
            FVector XOffset = GetIntersectionFrame().FromFrameVector(-FVector(InUseExtent.X, 0, 0));
            OutAdsorptionPoint += XOffset;
        }

    }
}

void FDynamicMeshAdaptiveAdsorption::CalculateAdsorptionPoint_Y(FVector& OutAdsorptionPoint, const FRayHitResault& HitResault, const FVector& InUseExtent,
                                                                const TArray<TSharedPtr<FIntersectionDynamicMesh>>& Env)
{
    TSharedPtr<FFunctionalExecuterInitializedData> FunctionalInitializedData = StaticCastSharedPtr<FFunctionalExecuterInitializedData>(InitializedData);
	FVector StartNormal = GetIntersectionData().GetSelfFrame().ToFrameVector(HitResault.HitNormal);

    //将射线向射线方向偏移防止检测不到对象
    FVector StartPoint = HitResault.HitPoint + HitResault.HitNormal * ADAPTIVE_TOLERANCE;

    if (AdaptiveRule.YAxisRule.AdsorptionRule == EAdsorptionRule::E_Positive)
    {
        
        FVector  CabientLeftRelativePoint = GetIntersectionFrame().ToFrameVector(FunctionalInitializedData->CabinetOriBox.Frame.FromFramePoint(FunctionalInitializedData->CabinetOriBox.Extents) - OutAdsorptionPoint);
        FVector YOffset = GetIntersectionFrame().FromFrameVector(FVector(0, CabientLeftRelativePoint.Y, 0) - FVector(0, InUseExtent.Y, 0));
        OutAdsorptionPoint += YOffset;


        //const FOrientedBox3d& CabientOriBox = InitializedData.CabinetOriBox;
        //FVector RelativeOutPoint = CabientOriBox.Frame.ToFramePoint(OutAdsorptionPoint);
        //RelativeOutPoint.Y = CabientOriBox.Extents.Y - InUseExtent.Y;
        //OutAdsorptionPoint = CabientOriBox.Frame.FromFramePoint(RelativeOutPoint);

        //当前射线点法线Y轴分量小于0以射线点Y坐标组为吸附点Y
      /*  if (FMath::IsNearlyZero(StartNormal.Y, ADAPTIVE_TOLERANCE) || (StartNormal.Y >= 0))
        {

            FRayHitResault Resault;
            bool  bHit = FindHitNearestPoint(StartPoint, GetIntersectionFrame().Y(), Env, Resault, HitResault.HitTarget);
            if (bHit)
            {
                FVector YOffset = GetIntersectionFrame().FromFrameVector(FVector(0, Resault.Distance, 0) - FVector(0, InUseExtent.Y, 0));
                OutAdsorptionPoint += YOffset;
            }
            else
            {
                const FOrientedBox3d& CabientOriBox = InitializedData.CabinetOriBox;
                FVector YOffset = GetIntersectionFrame().FromFrameVector(FVector(0, InUseExtent.Y, 0));
                OutAdsorptionPoint += YOffset;
            }

        }
        else
        {
            FVector YOffset = GetIntersectionFrame().FromFrameVector(-FVector(0, InUseExtent.Y, 0));
            OutAdsorptionPoint += YOffset;
        }*/
    }
    else if (AdaptiveRule.YAxisRule.AdsorptionRule == EAdsorptionRule::E_Negative)
    {



        //当前射线点法线Y轴分量大于0以射线点Y坐标组为吸附点Y
        if (FMath::IsNearlyZero(StartNormal.Y, ADAPTIVE_TOLERANCE) || (StartNormal.Y <= 0))
        {
            //初始
            FRayHitResault Resault;
            bool bHit = FindHitNearestPoint(StartPoint, -GetIntersectionFrame().Y(), Env, Resault, HitResault.HitTarget);
            if (bHit)
            {
                FVector YOffset = GetIntersectionFrame().FromFrameVector(FVector(0, -Resault.Distance, 0) + FVector(0, InUseExtent.Y, 0));
                OutAdsorptionPoint += YOffset;
            }
            else
            {
                FVector  CabientLeftRelativePoint = GetIntersectionFrame().ToFrameVector(FunctionalInitializedData->CabinetOriBox.Frame.FromFramePoint(-FunctionalInitializedData->CabinetOriBox.Extents) - OutAdsorptionPoint);
                FVector YOffset = GetIntersectionFrame().FromFrameVector(FVector(0,CabientLeftRelativePoint.Y,0) + FVector(0, InUseExtent.Y, 0));
                OutAdsorptionPoint += YOffset;
            }
        }
        else
        {
            FVector YOffset = GetIntersectionFrame().FromFrameVector(FVector(0, InUseExtent.Y, 0));
            OutAdsorptionPoint += YOffset;
        }
    }
}

void FDynamicMeshAdaptiveAdsorption::CalculateAdsorptionPoint_Z(FVector& OutAdsorptionPoint, const FRayHitResault& HitResault, const FVector& InUseExtent,
                                                                const TArray<TSharedPtr<FIntersectionDynamicMesh>>& Env)
{
    TSharedPtr<FFunctionalExecuterInitializedData> FunctionalInitializedData = StaticCastSharedPtr<FFunctionalExecuterInitializedData>(InitializedData);

    FVector StartNormal = GetIntersectionData().GetSelfFrame().ToFrameVector(HitResault.HitNormal);
    //将射线向射线方向偏移防止检测不到对象
    FVector StartPoint = HitResault.HitPoint + HitResault.HitNormal * ADAPTIVE_TOLERANCE;
    if (AdaptiveRule.ZAxisRule.AdsorptionRule == EAdsorptionRule::E_Positive)
    {
        if (FMath::IsNearlyZero(StartNormal.Z, ADAPTIVE_TOLERANCE) || (StartNormal.Z >= 0))
        {
            FRayHitResault Resault;
            bool bHit = FindHitNearestPoint(StartPoint, GetIntersectionFrame().Z(), Env, Resault, HitResault.HitTarget);
            if (bHit)
            {

                if (!FMath::IsNearlyZero(StartNormal.Z, ADAPTIVE_TOLERANCE)&&StartNormal.Z >= 0)
                {
                    Resault.Distance += ADAPTIVE_TOLERANCE;
                }
                SetZPositiveDependence(Resault.HitTarget);
                FVector ZOffset = GetIntersectionFrame().FromFrameVector(FVector(0, 0, Resault.Distance) - FVector(0, 0, InUseExtent.Z));
                OutAdsorptionPoint += ZOffset;
            }
            else
            {
                SetZPositiveDependence(nullptr);
                FVector  CabientLeftRelativePoint = GetIntersectionFrame().ToFrameVector(FunctionalInitializedData->CabinetOriBox.Frame.FromFramePoint(FunctionalInitializedData->CabinetOriBox.Extents) - OutAdsorptionPoint);
                FVector ZOffset = GetIntersectionFrame().FromFrameVector(FVector(0, 0, CabientLeftRelativePoint.Z  -InUseExtent.Z));
                OutAdsorptionPoint += ZOffset;
            }
        }
        else
        {
            SetZPositiveDependence(HitResault.HitTarget);
            FVector ZOffset = GetIntersectionFrame().FromFrameVector(-FVector(0, 0, InUseExtent.Z));
            OutAdsorptionPoint += ZOffset;
        }
    }
    else if (AdaptiveRule.ZAxisRule.AdsorptionRule == EAdsorptionRule::E_Negative)
    {
        if (FMath::IsNearlyZero(StartNormal.Z, ADAPTIVE_TOLERANCE) || (StartNormal.Z <= 0))
        {

            FRayHitResault Resault;
            bool bHit = FindHitNearestPoint(StartPoint, -GetIntersectionFrame().Z(), Env, Resault, HitResault.HitTarget);
            if (bHit)
            {
                if (!FMath::IsNearlyZero(StartNormal.Z, ADAPTIVE_TOLERANCE) && StartNormal.Z <= 0)
                {
                    Resault.Distance += ADAPTIVE_TOLERANCE;
                }
                SetZNegativeDependence(Resault.HitTarget);
                FVector ZOffset = GetIntersectionFrame().FromFrameVector(FVector(0, 0, -Resault.Distance) + FVector(0, 0, InUseExtent.Z));
                OutAdsorptionPoint += ZOffset;
            }
            else
            {
                SetZNegativeDependence(nullptr);
                FVector  CabientLeftRelativePoint = GetIntersectionFrame().ToFrameVector(FunctionalInitializedData->CabinetOriBox.Frame.FromFramePoint(FunctionalInitializedData->CabinetOriBox.Extents) - OutAdsorptionPoint);
                FVector ZOffset = GetIntersectionFrame().FromFrameVector(FVector(0, 0, CabientLeftRelativePoint.Z - InUseExtent.Z));
                OutAdsorptionPoint += ZOffset;
            }
        }
        else
        {
            FVector ZOffset = GetIntersectionFrame().FromFrameVector(FVector(0, 0, InUseExtent.Z));
            SetZNegativeDependence(HitResault.HitTarget);
            OutAdsorptionPoint += ZOffset;
        }
    }
    else //避让逻辑 和距离吸附逻辑
    {

        if (FMath::IsNearlyZero(StartNormal.Z, ADAPTIVE_TOLERANCE))
        {
            //当前鼠标向计算空间上下射线相交检测
            //向下
            FRayHitResault ZNegativeResault;
            double   ZNegativeDistance = InUseExtent.Z + FunctionalInitializedData->DistanceAdsorptionThreshold;
            bool bZNegativeHit = FindHitNearestPoint(StartPoint, -GetIntersectionFrame().Z(), ZNegativeDistance, Env, ZNegativeResault, HitResault.HitTarget);
            //向上
            FRayHitResault ZPositiveResault;
            double ZPositiveDistance = InUseExtent.Z + FunctionalInitializedData->DistanceAdsorptionThreshold;
            bool bZPositiveHit = FindHitNearestPoint(StartPoint, GetIntersectionFrame().Z(), ZPositiveDistance, Env, ZPositiveResault, HitResault.HitTarget);
            if (bZNegativeHit && !bZPositiveHit) //负向相交向正向偏移
            {
                //避让及距离吸附
                FVector ZOffset = GetIntersectionFrame().FromFrameVector(FVector(0, 0, -ZNegativeResault.Distance) + FVector(0, 0, InUseExtent.Z));
                OutAdsorptionPoint += ZOffset;
            }
            else if (!bZNegativeHit && bZPositiveHit)//正向相交向负向偏移
            {
                FVector ZOffset = GetIntersectionFrame().FromFrameVector(FVector(0, 0, ZPositiveResault.Distance) - FVector(0, 0, InUseExtent.Z));
                OutAdsorptionPoint += ZOffset;
            }
            else if (!bZNegativeHit && !bZPositiveHit && FunctionalInitializedData->bEnableAlignedAdsorption)
            {
                //都没有相交判断临近对齐
                //Calculate
                auto SelfOriBox = SourceIntersectionData->GetOriBox();
                SelfOriBox.Frame.Origin = OutAdsorptionPoint;
                for (auto& Iter : Env)
                {
                    if (Iter->GetIntersectionType() != EIntersectionDataType::E_Functional)
                    {
                        continue;
                    }
                    const FOrientedBox3d& TargetOriBox = Iter->GetOriBox();
                    FVector TargetDown = TargetOriBox.Frame.FromFramePoint(FVector::DownVector * TargetOriBox.Extents);
                    TargetDown = SelfOriBox.Frame.ToFramePoint(TargetDown);
                    double DeltaDown = TargetDown.Z + SelfOriBox.Extents.Z;
                    FVector TargetUp = TargetOriBox.Frame.FromFramePoint(FVector::UpVector * TargetOriBox.Extents);
                    TargetUp = SelfOriBox.Frame.ToFramePoint(TargetUp);
                    double DeltaUp = TargetUp.Z - SelfOriBox.Extents.Z;
                    double  Delta = FMath::Abs(DeltaUp) > FMath::Abs(DeltaDown) ? DeltaDown : DeltaUp;;
                    if (FMath::Abs(Delta) < FunctionalInitializedData->AlignedAdsorptionThreshold)
                    {
                        FVector ZOffset = GetIntersectionFrame().FromFrameVector(FVector( 0, 0, Delta));
                        OutAdsorptionPoint += ZOffset;
                        break;
                    }
                }
            }
            
            //左右都有相交忽略
        }
        else if (StartNormal.Z > 0)
        {
            FVector ZOffset = GetIntersectionFrame().FromFrameVector(FVector(0, 0, InUseExtent.Z));
            OutAdsorptionPoint += ZOffset;
        }
        else
        {
            FVector ZOffset = GetIntersectionFrame().FromFrameVector(-FVector(0, 0, InUseExtent.Z));
            OutAdsorptionPoint += ZOffset;
        }
    }

}


void FDynamicMeshAdaptiveAdsorption::CalculateYAxisAdsorption( FVector& InRelativeMinPoint,  FVector& InRelativeMaxPoint, FVector& OutExtents, FVector& OutCenter, const TArray<TSharedPtr<FIntersectionDynamicMesh>>& Env)
{
    TSharedPtr<FFunctionalExecuterInitializedData> FunctionalInitializedData = StaticCastSharedPtr<FFunctionalExecuterInitializedData>(InitializedData);
    bool bAdaptiveY = (AdaptiveRule.YAxisRule.bAdaptived && AdaptiveRule.YAxisRule.AdsorptionRule == EAdsorptionRule::E_Negative);
    //FVector MaxPoint = InRelativeMaxPoint;
    //FVector MinPoint = InRelativeMinPoint;
    const auto& SelfFrame = GetIntersectionFrame();
    if (bAdaptiveY && FunctionalInitializedData->bExtentsYEnableAdaptation)
    {

        bool bModified = false;
        if (AdaptiveRule.XAxisRule.bAdaptived)
        {
            if (XNegativeAdsorption.IsValid() || XPositiveAdsorption.IsValid())
            {
                bModified = true;
                double XNegativeMinRelativeY = InRelativeMaxPoint.Y;
                double XNegativeMaxRelativeY = InRelativeMinPoint.Y;
                if (XNegativeAdsorption.IsValid())
                {
                    TSharedPtr<FFunctionalIntersectionMesh> NegativeIntersectionMesh = StaticCastSharedPtr<FFunctionalIntersectionMesh>(XNegativeAdsorption.Pin());
                    FOrientedBox3d XNegativOriBox = NegativeIntersectionMesh->GetOriBoxWithSpecialExtents();
                    //应用前缩和后缩
                   //求出左右对象在本地坐标下深度方向最大最小值
                    XNegativOriBox.EnumerateCorners([&](const FVector& Corners)
                        {

                            FVector XNegativePoint = SelfFrame.ToFramePoint(Corners);
                            XNegativeMaxRelativeY = FMath::Max(XNegativeMaxRelativeY, XNegativePoint.Y);
                            XNegativeMinRelativeY = FMath::Min(XNegativeMinRelativeY, XNegativePoint.Y);
                        });
                }
                else
                {
                    XNegativeMinRelativeY = InRelativeMinPoint.Y;
                    XNegativeMaxRelativeY = InRelativeMaxPoint.Y;
                }
                double XPositiveMinRelativeY = InRelativeMaxPoint.Y;
                double XPositiveMaxRelativeY = InRelativeMinPoint.Y;
                if (XPositiveAdsorption.IsValid())
                {
                    TSharedPtr<FFunctionalIntersectionMesh> PositiveIntersectionMesh = StaticCastSharedPtr<FFunctionalIntersectionMesh>(XPositiveAdsorption.Pin());
                    FOrientedBox3d XPositiveOriBox = PositiveIntersectionMesh->GetOriBoxWithSpecialExtents();
                    XPositiveOriBox.EnumerateCorners([&](const FVector& Corners)
                        {
                            FVector XPositivePoint = SelfFrame.ToFramePoint(Corners);
                            XPositiveMaxRelativeY = FMath::Max(XPositiveMaxRelativeY, XPositivePoint.Y);
                            XPositiveMinRelativeY = FMath::Min(XPositiveMinRelativeY, XPositivePoint.Y);
                        });
                }
                else
                {
                    XPositiveMinRelativeY = InRelativeMinPoint.Y;
                    XPositiveMaxRelativeY = InRelativeMaxPoint.Y;
                }
                //如果有大值小于小值，只用那一组大小值
                if (FMath::IsNearlyEqual(XNegativeMaxRelativeY, XPositiveMinRelativeY, ADAPTIVE_TOLERANCE) || XNegativeMaxRelativeY < XPositiveMinRelativeY)
                {
                    //X负方向整体小于X正方向,使用X负方向
                    InRelativeMaxPoint.Y = FMath::Min(InRelativeMaxPoint.Y, XNegativeMaxRelativeY);
                    InRelativeMinPoint.Y = FMath::Max(InRelativeMinPoint.Y, XNegativeMinRelativeY);
                }
                else if (FMath::IsNearlyEqual(XPositiveMaxRelativeY, XNegativeMinRelativeY, ADAPTIVE_TOLERANCE) || XPositiveMaxRelativeY < XNegativeMinRelativeY)
                {
                    //X正方向整体小于X负方向侧,使用X正方向
                    InRelativeMaxPoint.Y = FMath::Min(InRelativeMaxPoint.Y, XPositiveMaxRelativeY);
                    InRelativeMinPoint.Y = FMath::Max(InRelativeMinPoint.Y, XPositiveMinRelativeY);
                }
                else
                {
                    //前口取左右最大值中的小值
                    InRelativeMaxPoint.Y = FMath::Min(InRelativeMaxPoint.Y, XNegativeMaxRelativeY);
                    InRelativeMaxPoint.Y = FMath::Min(InRelativeMaxPoint.Y, XPositiveMaxRelativeY);
                    //后口取左右最小值中的大值
                    InRelativeMinPoint.Y = FMath::Max(InRelativeMinPoint.Y, XNegativeMinRelativeY);
                    InRelativeMinPoint.Y = FMath::Max(InRelativeMinPoint.Y, XPositiveMinRelativeY);
                }
                //根据左右最大最小值判断深度
            }
        }

        if (AdaptiveRule.ZAxisRule.bAdaptived)
        {
            if (ZNegativeAdsorption.IsValid() || ZPositiveAdsorption.IsValid())
            {   
                bModified = true;

                //应用前缩和后缩

               //求出左右对象在本地坐标下深度方向最大最小值
                double ZNegativeMinRelativeY = InRelativeMaxPoint.Y;
                double ZNegativeMaxRelativeY = InRelativeMinPoint.Y;
                if (ZNegativeAdsorption.IsValid())
                {
                    TSharedPtr<FFunctionalIntersectionMesh> NegativeIntersectionMesh = StaticCastSharedPtr<FFunctionalIntersectionMesh>(ZNegativeAdsorption.Pin());
                    FOrientedBox3d ZNegativOriBox = NegativeIntersectionMesh->GetOriBoxWithSpecialExtents();
                    ZNegativOriBox.EnumerateCorners([&](const FVector& Corners)
                        {

                            FVector XNegativePoint = SelfFrame.ToFramePoint(Corners);
                            ZNegativeMaxRelativeY = FMath::Max(ZNegativeMaxRelativeY, XNegativePoint.Y);
                            ZNegativeMinRelativeY = FMath::Min(ZNegativeMinRelativeY, XNegativePoint.Y);
                        });
                }
                else
                {
                    ZNegativeMinRelativeY = InRelativeMinPoint.Y;
                    ZNegativeMaxRelativeY = InRelativeMaxPoint.Y;
                }


                double ZPositiveMinRelativeY = InRelativeMaxPoint.Y;
                double ZPositiveMaxRelativeY = InRelativeMinPoint.Y;
                if (ZPositiveAdsorption.IsValid())
                {
                    TSharedPtr<FFunctionalIntersectionMesh> PositiveIntersectionMesh = StaticCastSharedPtr<FFunctionalIntersectionMesh>(ZPositiveAdsorption.Pin());
                    FOrientedBox3d ZPositiveOriBox = PositiveIntersectionMesh->GetOriBoxWithSpecialExtents();
                    ZPositiveOriBox.EnumerateCorners([&](const FVector& Corners)
                        {
                            FVector ZPositivePoint = SelfFrame.ToFramePoint(Corners);
                            ZPositiveMaxRelativeY = FMath::Max(ZPositiveMaxRelativeY, ZPositivePoint.Y);
                            ZPositiveMinRelativeY = FMath::Min(ZPositiveMinRelativeY, ZPositivePoint.Y);
                        });

                }
                else
                {
                    ZPositiveMinRelativeY = InRelativeMinPoint.Y;
                    ZPositiveMaxRelativeY = InRelativeMaxPoint.Y;
                }
                //如果有大值小于小值，只用那一组大小值
                if (FMath::IsNearlyEqual(ZNegativeMaxRelativeY, ZPositiveMinRelativeY, ADAPTIVE_TOLERANCE) || ZNegativeMaxRelativeY < ZPositiveMinRelativeY)
                {
                    //Z负方向整体小于Z正方向,使用Z负方向
                    InRelativeMaxPoint.Y = FMath::Min(InRelativeMaxPoint.Y, ZNegativeMaxRelativeY);
                    InRelativeMinPoint.Y = FMath::Max(InRelativeMinPoint.Y, ZNegativeMinRelativeY);
                }
                else if (FMath::IsNearlyEqual(ZPositiveMaxRelativeY, ZNegativeMinRelativeY, ADAPTIVE_TOLERANCE) || ZPositiveMaxRelativeY < ZNegativeMinRelativeY)
                {
                    //Z正方向整体小于Z负方向侧,使用Z正方向
                    InRelativeMaxPoint.Y = FMath::Min(InRelativeMaxPoint.Y, ZPositiveMaxRelativeY);
                    InRelativeMinPoint.Y = FMath::Max(InRelativeMinPoint.Y, ZPositiveMinRelativeY);
                }
                else
                {
                    //前口取左右最大值中的小值
                    InRelativeMaxPoint.Y = FMath::Min(InRelativeMaxPoint.Y, ZNegativeMaxRelativeY);
                    InRelativeMaxPoint.Y = FMath::Min(InRelativeMaxPoint.Y, ZPositiveMaxRelativeY);
                    //后口取左右最小值中的大值
                    InRelativeMinPoint.Y = FMath::Max(InRelativeMinPoint.Y, ZPositiveMinRelativeY);
                    InRelativeMinPoint.Y = FMath::Max(InRelativeMinPoint.Y, ZNegativeMinRelativeY);
                }
            }
        }


        if (InRelativeMaxPoint.Y < InRelativeMinPoint.Y || !bModified)
        {
            InRelativeMaxPoint.Y = InRelativeMinPoint.Y + OutExtents.Y * 2;
        }
    }
    else
    {
        InRelativeMaxPoint.Y = InRelativeMinPoint.Y + OutExtents.Y * 2;
    }

    //TODO:还要应用工艺内缩

    OutExtents = (InRelativeMaxPoint - InRelativeMinPoint)/2;
    OutCenter = SelfFrame.FromFramePoint((InRelativeMaxPoint + InRelativeMinPoint)/2);
}

void FDynamicMeshAdaptiveAdsorption::CalculateXZAxisAdsorption( FVector& InRelativeMinPoint,  FVector& InRelativeMaxPoint, FVector& OutExtents, FVector& OutCenter)
{
    auto  SelfOriBox =  SourceIntersectionData->GetOriBox();
    bool bNeedUpdate = false;
    //FVector MaxPoint = InRelativeMinPoint;
    //FVector MinPoint = -InRelativeMaxPoint;
    const auto& SelfFrame = GetIntersectionFrame();

    //X方向可以自适应的情况下判断X轴吸附对象是否使用特殊宽高神，且没有相对旋转
    if (AdaptiveRule.XAxisRule.bAdaptived)
    {
        if (XNegativeAdsorption.IsValid())
        {
            TSharedPtr<FFunctionalIntersectionMesh> XNegativeMesh = StaticCastSharedPtr<FFunctionalIntersectionMesh>(XNegativeAdsorption.Pin());
            if (XNegativeMesh->bHasSpecialExtents&& !XNegativeMesh->bUseRelativeOBB)
            {
                double MinRelative = TNumericLimits<double>::Lowest();
                auto SpecialOriBox = XNegativeMesh->GetOriBoxWithSpecialExtents();

                //应用前缩和后缩
               //求出左右对象在本地坐标下深度方向最大最小值
                SpecialOriBox.EnumerateCorners([&](const FVector& Corners)
                    {
                        FVector XNegativePoint = SelfFrame.ToFramePoint(Corners);
                        MinRelative = FMath::Max(MinRelative, XNegativePoint.X);
                    });

                if (!FMath::IsNearlyEqual(MinRelative, InRelativeMinPoint.X, ADAPTIVE_TOLERANCE))
                {
                    bNeedUpdate = true;
                    InRelativeMinPoint.X = MinRelative;
                }
            }
        }
        if (XPositiveAdsorption.IsValid())
        {
            TSharedPtr<FFunctionalIntersectionMesh> XNegativeMesh = StaticCastSharedPtr<FFunctionalIntersectionMesh>(XPositiveAdsorption.Pin());
            if (XNegativeMesh->bHasSpecialExtents && !XNegativeMesh->bUseRelativeOBB)
            {
                double MaxRelative = TNumericLimits<double>::Max();
                auto SpecialOriBox = XNegativeMesh->GetOriBoxWithSpecialExtents();

                //应用前缩和后缩
               //求出左右对象在本地坐标下深度方向最大最小值
                SpecialOriBox.EnumerateCorners([&](const FVector& Corners)
                    {

                        FVector XPositivePoint = SelfFrame.ToFramePoint(Corners);
                        MaxRelative = FMath::Min(MaxRelative, XPositivePoint.X);
                    });

                if (!FMath::IsNearlyEqual(MaxRelative, InRelativeMaxPoint.X, ADAPTIVE_TOLERANCE))
                {
                    bNeedUpdate = true;
                    InRelativeMaxPoint.X = MaxRelative;
                }
            }
        }

    }
    if (AdaptiveRule.ZAxisRule.bAdaptived)
    {

        if (ZNegativeAdsorption.IsValid())
        {
            TSharedPtr<FFunctionalIntersectionMesh> ZNegativeMesh = StaticCastSharedPtr<FFunctionalIntersectionMesh>(ZNegativeAdsorption.Pin());
            if (ZNegativeMesh->bHasSpecialExtents && !ZNegativeMesh->bUseRelativeOBB)
            {
                double MinRelative = TNumericLimits<double>::Lowest();
                auto SpecialOriBox = ZNegativeMesh->GetOriBoxWithSpecialExtents();

                //应用前缩和后缩
               //求出左右对象在本地坐标下深度方向最大最小值
                SpecialOriBox.EnumerateCorners([&](const FVector& Corners)
                    {

                        FVector ZNegativePoint = SelfFrame.ToFramePoint(Corners);
                        MinRelative = FMath::Max(MinRelative, ZNegativePoint.Z);
                    });

                if (!FMath::IsNearlyEqual(MinRelative, InRelativeMinPoint.Z, ADAPTIVE_TOLERANCE))
                {
                    bNeedUpdate = true;
                    InRelativeMinPoint.Z = MinRelative;
                }
            }
        }
        if (ZPositiveAdsorption.IsValid())
        {
            TSharedPtr<FFunctionalIntersectionMesh> ZPositiveMesh = StaticCastSharedPtr<FFunctionalIntersectionMesh>(ZPositiveAdsorption.Pin());
            if (ZPositiveMesh->bHasSpecialExtents && !ZPositiveMesh->bUseRelativeOBB)
            {
                double MaxRelative = TNumericLimits<double>::Max();
                auto SpecialOriBox = ZPositiveMesh->GetOriBoxWithSpecialExtents();

                //应用前缩和后缩
               //求出左右对象在本地坐标下深度方向最大最小值
                SpecialOriBox.EnumerateCorners([&](const FVector& Corners)
                    {

                        FVector ZPositivePoint = SelfFrame.ToFramePoint(Corners);
                        MaxRelative = FMath::Min(MaxRelative, ZPositivePoint.Z);
                    });

                if (!FMath::IsNearlyEqual(MaxRelative, InRelativeMaxPoint.Z, ADAPTIVE_TOLERANCE))
                {
                    bNeedUpdate = true;
                    InRelativeMaxPoint.Z = MaxRelative;
                }
            }
        }


    }
    if (bNeedUpdate)
    {
        OutExtents = (InRelativeMaxPoint - InRelativeMinPoint) / 2;
        OutCenter.X = SelfFrame.FromFramePoint((InRelativeMaxPoint + InRelativeMinPoint) / 2).X;
        OutCenter.Z = SelfFrame.FromFramePoint((InRelativeMaxPoint + InRelativeMinPoint) / 2).Z;
    }
}

void FDynamicMeshAdaptiveAdsorption::ApplyAdsorptionOffsetBefore(FVector& OutCenter, FVector& OutExtents)
{
    const FFrame3d& IntersectionFrame = GetIntersectionData().GetSelfFrame();

	FVector SizeMin = -OutExtents;
	FVector SizeMax = OutExtents;

	if (!AdaptiveRule.XAxisRule.bAdaptived)
	{
		if (AdaptiveRule.XAxisRule.AdsorptionRule == EAdsorptionRule::E_Negative)
		{
			if (!FMath::IsNearlyZero(AdaptiveRule.XAxisRule.PositiveOffset, ADAPTIVE_TOLERANCE))
			{
				SizeMin.X += AdaptiveRule.XAxisRule.PositiveOffset;
			}
		}
		else if (AdaptiveRule.YAxisRule.AdsorptionRule == EAdsorptionRule::E_Positive)
		{
			if (!FMath::IsNearlyZero(AdaptiveRule.XAxisRule.PositiveOffset, ADAPTIVE_TOLERANCE))
			{
				SizeMin.X -= AdaptiveRule.XAxisRule.PositiveOffset;
			}
		}
	}
	if (!AdaptiveRule.YAxisRule.bAdaptived)
	{
		if (AdaptiveRule.YAxisRule.AdsorptionRule == EAdsorptionRule::E_Negative)
		{
			if (!FMath::IsNearlyZero(AdaptiveRule.YAxisRule.NegativeOffset, ADAPTIVE_TOLERANCE))
			{
				SizeMin.Y += AdaptiveRule.YAxisRule.NegativeOffset;
			}
		}
		else if (AdaptiveRule.YAxisRule.AdsorptionRule == EAdsorptionRule::E_Positive)
		{
			if (!FMath::IsNearlyZero(AdaptiveRule.YAxisRule.PositiveOffset, ADAPTIVE_TOLERANCE))
			{
				SizeMax.Y -= AdaptiveRule.YAxisRule.PositiveOffset;
			}
		}
	}

	if (!AdaptiveRule.ZAxisRule.bAdaptived)
	{
		if (AdaptiveRule.ZAxisRule.AdsorptionRule == EAdsorptionRule::E_Negative)
		{
			if (!FMath::IsNearlyZero(AdaptiveRule.ZAxisRule.NegativeOffset, ADAPTIVE_TOLERANCE))
			{
				SizeMin.Z += AdaptiveRule.ZAxisRule.NegativeOffset;
			}
		}
		else if (AdaptiveRule.ZAxisRule.AdsorptionRule == EAdsorptionRule::E_Positive)
		{
			if (!FMath::IsNearlyZero(AdaptiveRule.ZAxisRule.PositiveOffset, ADAPTIVE_TOLERANCE))
			{
				SizeMax.Z -= AdaptiveRule.ZAxisRule.PositiveOffset;
			}
		}
	}
	OutExtents = (SizeMax - SizeMin) * 0.5f;
	OutCenter = IntersectionFrame.FromFrameVector((SizeMax + SizeMin) * 0.5f) + OutCenter;
}

void FDynamicMeshAdaptiveAdsorption::ApplyAdsorptionOffsetAfter(FOrientedBox3d& OutOriBox)
{
    FVector MinRelativePoint = -OutOriBox.Extents;
    FVector MaxRelativePoint = OutOriBox.Extents;

    if (!FMath::IsNearlyZero(AdaptiveRule.XAxisRule.NegativeOffset, ADAPTIVE_TOLERANCE))
    {
        MinRelativePoint.X += AdaptiveRule.XAxisRule.NegativeOffset;
    }
    if (!FMath::IsNearlyZero(AdaptiveRule.XAxisRule.PositiveOffset, ADAPTIVE_TOLERANCE))
    {
        MaxRelativePoint.X -= AdaptiveRule.XAxisRule.PositiveOffset;
    }

    if (!FMath::IsNearlyZero(AdaptiveRule.YAxisRule.NegativeOffset, ADAPTIVE_TOLERANCE))
    {
        MinRelativePoint.Y += AdaptiveRule.YAxisRule.NegativeOffset;
    }
    if (!FMath::IsNearlyZero(AdaptiveRule.YAxisRule.PositiveOffset, ADAPTIVE_TOLERANCE))
    {
        MaxRelativePoint.Y -= AdaptiveRule.YAxisRule.PositiveOffset;
    }


    if (!FMath::IsNearlyZero(AdaptiveRule.ZAxisRule.NegativeOffset, ADAPTIVE_TOLERANCE))
    {
        MinRelativePoint.Z += AdaptiveRule.ZAxisRule.NegativeOffset;
    }
    if (!FMath::IsNearlyZero(AdaptiveRule.ZAxisRule.PositiveOffset, ADAPTIVE_TOLERANCE))
    {
        MaxRelativePoint.Z -= AdaptiveRule.ZAxisRule.PositiveOffset;
    }

    FVector RelativeCenter = (MaxRelativePoint + MinRelativePoint) * 0.5f;
    OutOriBox.Frame.Origin = OutOriBox.Frame.FromFramePoint(RelativeCenter);
    OutOriBox.Extents = (MaxRelativePoint - MinRelativePoint) * 0.5f;
}

void FDynamicMeshAdaptiveAdsorption::ApplyFixedExtentsAfter(FOrientedBox3d& OutOriBox)
{
    FVector MinRelativePoint = -OutOriBox.Extents;
    FVector MaxRelativePoint = OutOriBox.Extents;

    TSharedPtr<FFunctionalExecuterInitializedData> FunctionalInitializedData = StaticCastSharedPtr<FFunctionalExecuterInitializedData>(InitializedData);
    if (!FunctionalInitializedData->bUsedRelativedOBB && FunctionalInitializedData->bHasFixedExtents)
    {
        MinRelativePoint -= FunctionalInitializedData->FixedMinExtentOffset;
        MaxRelativePoint -= FunctionalInitializedData->FixedMaxExtentOffset;
    }
    FVector RelativeCenter = (MaxRelativePoint + MinRelativePoint) * 0.5f;
    OutOriBox.Frame.Origin = OutOriBox.Frame.FromFramePoint(RelativeCenter);
    OutOriBox.Extents = (MaxRelativePoint - MinRelativePoint) * 0.5f;
}

void FDynamicMeshAdaptiveAdsorption::UpdateAdaptationOriBox(const FOrientedBox3d& InOriBox)
{

    OutAdaptationData->bExtentsModified = !OutAdaptationData->OriBox.Extents.Equals(InOriBox.Extents, ADAPTIVE_TOLERANCE);
    OutAdaptationData->bTransformModified = !OutAdaptationData->OriBox.Center().Equals(InOriBox.Center(), ADAPTIVE_TOLERANCE);
    OutAdaptationData->bTransformModified |= !OutAdaptationData->OriBox.Frame.Rotation.EpsilonEqual(InOriBox.Frame.Rotation, ADAPTIVE_TOLERANCE);

    OutAdaptationData->OriBox = InOriBox;
	if (ExecuteHandle)
	{
		ExecuteHandle(OutAdaptationData);
	}
}

void FDynamicMeshAdaptiveAdsorption::SetXNegativeDependence(const TSharedPtr<FIntersectionDynamicMesh>& InTarget)
{
	if (XNegativeAdsorption != InTarget)
	{
		XNegativeAdsorption = InTarget;

       /* FString LogStr;
        if (XNegativeAdsorption.IsValid())
        {
            LogStr.Append(TEXT("XNegativeAdsorption:"));
            LogStr.Append(XNegativeAdsorption.Pin()->GetDebugName());
           
        }
        else
        {
            LogStr.Append(TEXT("XNegativeAdsorption:Null"));
        }
        UE_LOG(LogTemp, Warning, TEXT("Dependence:%s"), *LogStr);*/
    }

}

void FDynamicMeshAdaptiveAdsorption::SetXPositiveDependence(const TSharedPtr<FIntersectionDynamicMesh>& InTarget)
{
    if (XPositiveAdsorption != InTarget)
    {

        XPositiveAdsorption = InTarget;
      /*  FString LogStr;
        if (XPositiveAdsorption.IsValid())
        {
            LogStr.Append(TEXT("XPositiveAdsorption:"));
            LogStr.Append(XPositiveAdsorption.Pin()->GetDebugName());

        }
        else
        {
            LogStr.Append(TEXT("XPositiveAdsorption:Null"));
        }
        UE_LOG(LogTemp, Warning, TEXT("Dependence:%s"), *LogStr);*/
    }

}

void FDynamicMeshAdaptiveAdsorption::SetZNegativeDependence(const TSharedPtr<FIntersectionDynamicMesh>& InTarget)
{
	if (ZNegativeAdsorption != InTarget)
	{
		ZNegativeAdsorption = InTarget;

     /*   FString LogStr;
        if (ZNegativeAdsorption.IsValid())
        {
            LogStr.Append(TEXT("ZNegativeAdsorption:"));
            LogStr.Append(ZNegativeAdsorption.Pin()->GetDebugName());

        }
        else
        {
            LogStr.Append(TEXT("ZNegativeAdsorption:Null"));
        }
        UE_LOG(LogTemp, Warning, TEXT("Dependence:%s"), *LogStr);*/
    }
  
}

void FDynamicMeshAdaptiveAdsorption::SetZPositiveDependence(const TSharedPtr<FIntersectionDynamicMesh>& InTarget)
{
	if (ZPositiveAdsorption != InTarget)
	{
		ZPositiveAdsorption = InTarget;

     /*   FString LogStr;
        if (ZPositiveAdsorption.IsValid())
        {
            LogStr.Append(TEXT("ZPositiveAdsorption:"));
            LogStr.Append(ZPositiveAdsorption.Pin()->GetDebugName());

        }
        else
        {
            LogStr.Append(TEXT("ZPositiveAdsorption:Null"));
        }
        UE_LOG(LogTemp, Warning, TEXT("Dependence:%s"), *LogStr);*/
    }
  
}

void FDynamicMeshAdaptiveAdsorption::ClearDependentTarget()
{
	XNegativeAdsorption = nullptr;
	XPositiveAdsorption = nullptr;
	ZNegativeAdsorption = nullptr;
	ZPositiveAdsorption = nullptr;
}

void FDynamicMeshAdaptiveAdsorption::LogDenpendence()
{

    FString LogStr;
    if (XNegativeAdsorption.IsValid())
    {
        LogStr.Append(TEXT("XNegativeAdsorption:"));
        LogStr.Append(XNegativeAdsorption.Pin()->GetDebugName());
    }
    if (XPositiveAdsorption.IsValid())
    {
        LogStr.Append(TEXT("XPositiveAdsorption:"));
        LogStr.Append(XPositiveAdsorption.Pin()->GetDebugName());
    }
    if (ZNegativeAdsorption.IsValid())
    {
        LogStr.Append(TEXT("ZNegativeAdsorption:"));
        LogStr.Append(ZNegativeAdsorption.Pin()->GetDebugName());
    }
    if (ZPositiveAdsorption.IsValid())
    {
        LogStr.Append(TEXT("ZPositiveAdsorption:"));
        LogStr.Append(ZPositiveAdsorption.Pin()->GetDebugName());
    }
    UE_LOG(LogTemp, Warning, TEXT("Dependence:%s"), *LogStr);
}
