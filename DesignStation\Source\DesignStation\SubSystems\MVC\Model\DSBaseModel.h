﻿#pragma once

#include "CoreMinimal.h"
#include "Core/DSMarkData.h"
#include "Core/DSModelExecuteType.h"
#include "SubSystems/DSCore.h"
#include "SubSystems/MVC/Core/Property/BaseProperty.h"
#include "Subsystems/StatusFlag/StatusFlag.h"
#include "Subsystems/MVC/Core/GizmoStructures.h"
#include "Subsystems/MVC/Core/Property/GizmoProperty.h"
#include "DSBaseModel.generated.h"

class ADSBaseView;
DECLARE_LOG_CATEGORY_EXTERN(DSBaseModelLog, Log, All);

class UDSBaseModel;
struct FStatusFlag;

enum class EDSResourceType : uint8;
enum class EResourceStatusEventType : uint8;

DECLARE_MULTICAST_DELEGATE_OneParam(FLinkModelUpdated, UDSBaseModel*);

/**
 *   @@ 模型基类，所有模型都继承自该类
 *	 @@ 纯虚类，不支持实例化
 */

class UDSBaseModel;

/*
 *  @@ Model、View关联delegate
 */
DECLARE_DELEGATE_TwoParams(FDSModelViewExecuteDelegate, UDSBaseModel*, FDSModelExecuteType);

DECLARE_DELEGATE_OneParam(FDSBaseModelDelegate, UDSBaseModel*);
DECLARE_DELEGATE_OneParam(FDSRefreshPropertyWidgetDelegate, UDSBaseModel*);
DECLARE_MULTICAST_DELEGATE_OneParam(FLinkModelUpdated, UDSBaseModel*);

DECLARE_DELEGATE_OneParam(FDSRefreshModuleDrawDelegate, UDSBaseModel*);

DECLARE_MULTICAST_DELEGATE_OneParam(FDSRefreshForMultiGroupDelegate, UDSBaseModel*);
DECLARE_MULTICAST_DELEGATE_OneParam(FDSDeleteForMultiGroupDelegate, UDSBaseModel*);

/*
 *	@@ 用于数据删除时，通知数据池删除数据
 *	@@ 只在删除时调用
 */
DECLARE_DELEGATE_OneParam(FDSModelPoolDeleteDelegate, UDSBaseModel*);


UCLASS(Abstract)
class UDSBaseModel : public UObject
{
	GENERATED_BODY()

protected:
	FDSModelViewExecuteDelegate ModelViewExecuteDelegate;

protected:
	class FChangeObserveResourceHelper final
	{
	public:
		FChangeObserveResourceHelper(UDSBaseModel* InModel)
			: Operator(InModel)
		{
		}

		virtual ~FChangeObserveResourceHelper()
		{
			if (Operator->CheckIfNeedsStopObserveResourceStatus())
			{
				Operator->StopObserveResourceStatusEvent();
			}
		}

		UDSBaseModel* Operator;
	};

public:
	UDSBaseModel();
	UDSBaseModel(const EDSModelType& InType);

	virtual UWorld* GetWorld() const override;

	static TMap<FString, bool> GetModelRulerMark(const EDSModelType& InType);

	FDSModelViewExecuteDelegate& GetModelViewExecuteDelegate() { return ModelViewExecuteDelegate; }
	FDSModelPoolDeleteDelegate& GetDeleteToResourcePoolDelegate() { return OnModelPoolDeleteDelegate; }

#pragma region INIT_MODEL
	/*
	*  @@ self beginplay to init model special state or property
	*/
	virtual void PostInitProperties() override;
	virtual void DSBeginPlay();

	/*
	*  @@ Init Property
	*/
	virtual void InitProperty()
	{
	}

	/*
	*  @@ init all addition info for model
	*  @@ include : pendant, state
	*  @@ TODO : others...
	*/
	virtual void InitAdditionData();

	/*
	*  @@ Init model components
	*/
	virtual void InitComponents()
	{
	}

	/*
	*  @@ Init Outline
	*/
	virtual void InitOutlines()
	{
	}

	virtual void InitModelHiddenFlags();
	/*
*  @@ Init Mesh
*/
	virtual void InitMeshInfo() { UpdateMeshInfo(); }

	virtual void Initialized();

#pragma endregion

	FString GetUUID() const { return UUID; }

	/*
	 *  @@ No Use, Only For Undo\Redo
	 */
	void SetUUID(const FString& InUUID) { UUID = InUUID; }

	EDSModelType GetModelType() const { return ModelType; }
	virtual bool SupportsLazyLoad() const { return false; }
	virtual bool ShouldUseParentModel() const { return false; }

	FDSBaseProperty* GetProperty() { return Property.Get(); }
	TSharedPtr<FDSBaseProperty> GetPropertySharedPtr() { return Property; }
	TSharedPtr<FDSBaseProperty> GetPropertySharedPtr() const{ return Property; }

	template<typename T>
	TSharedPtr<T> GetTypedProperty()
	{
		return StaticCastSharedPtr<T>(Property);
	}

	virtual void SetProperty(FDSBaseProperty* InProperty)
	{
	}

	void SetModelType(const EDSModelType& InType);
	virtual bool IsValid();
	void Clear();

	bool operator==(UDSBaseModel* InModel) const;
	bool operator!=(UDSBaseModel* InModel) const;


	/*
	 *  @@ bind all delegates
	 */
	virtual void BindDelegates();

	/*
	 *  @@ unbind all delegates
	 */
	virtual void UnBindDelegates();

	/*
 *  @@ unbind all Multi delegates
 */
	virtual void UnBindMultiDelegates();

	/*
	 *  @@ shallow copy
	 *  @@ all data except uuid
	 */
	virtual void ShallowCopy(UDSBaseModel* InModel);
	/*
	 *  @@ deep copy
	 *  @@ all data include uuid
	 */
	virtual void DeepCopy(UDSBaseModel* InModel);

	virtual UDSBaseModel* CopyModel() { return nullptr; }
	
	//refresh
	virtual void RefreshComponents()
	{
	}
	
	/*
	*  @@ state function
	*/
	bool IsNewGenerate() const;

	/*
	 *  @@ set model to no new generate
	 *  @@ return old new generate mark
	 */
	bool SetNoNewGenerate();

	void SetIsContinueGenerate(bool bContinue);
	bool IsContinueGenerate() const;

	void SetCopyGenerate(bool bCopy);
	bool IsCopyGenerate() const;

	/*
	*  @@ can process transform
	*/
	virtual bool CanMove(bool Is2D) const;
	bool CanRot(bool Is2D) const;
	bool CanScale(bool Is2D) const;

	/*
	*  @@ Axis state
	*/
	bool HasAxis_2D() const;
	bool HasAxis_3D_Move() const;
	bool HasAxis_3D_Rot() const;
	bool HasAxis_3D_Scale() const;

	/*
	*  @@ Scale state
	*/
	bool HasScale_2D() const;
	bool HasScale_3D() const;

	/*
	*  @@ Ruler state
	*/
	bool Has2DRuler() const;
	bool Has3DRuler() const;

	bool bThisRulerShow(const FString& InRulerName);

	/*
	*  @@ pendant info
	*/
	virtual void SetTargetModel(UDSBaseModel* InModel)
	{
	}

	virtual void UpdateModelByTargetModel(UDSBaseModel* InModel, bool NeedRefreshView)
	{
	}

	virtual UDSBaseModel* GetTargetModel() { return nullptr; }

	TSet<UDSBaseModel*> GetComponentModels() { return ComponentModelSet; }

	virtual void ClearComponentModels();

	virtual void RemoveComponentModel(UDSBaseModel* InModel);

	virtual void AppendComponentModel(TArray<UDSBaseModel*> InModels);
	/*
	*  @@ add component model
	*/
	UDSBaseModel* GetTopLevelOwnerModel();
	UDSBaseModel* GetOwnerModel();
	void SetOwnerModel(UDSBaseModel* InModel);

	virtual FPendantInfo GetPendantInfo();


	virtual FSnapInfo GetSnapInfo();
	virtual const FDSMeshInfo& GetMeshInfo() const;

	void AddToIgnoreDSModel(UDSBaseModel* InModel);
	
	virtual const FOutlineInfo& GetOutlineInfo() const;
	//refresh outline and get it
	virtual TArray<FVector> GetBottomOutline();
	virtual TArray<FVector> GetWorldTopOutline();
	virtual TArray<FVector> GetWorldBottomOutline();
	virtual TArray<FVector> GetWorldOutline(); //one plane

	virtual TArray<TArray<FVector>> GetWorldFrameOutline();


	virtual bool GetModelOuterRect(TArray<FVector>& OutRect);
	virtual bool GetModelOuter(TArray<FVector>& Outer);
	virtual bool GetModelCenterCross(TArray<FVector>& OutCross);

	/**
	 * Return 8 points to represents an oriented bounding box of the current model in world space.
	 * Vertices order : 
	 * 
	 *       0+++++++++++1
	 *      + +         ++
	 *     +  +        + +
	 *    +   +       +  +
	 * 
	 *   3+++++++++++2   +
	 *   +    +      +   +
	 *   +    4++++++++++5
	 *   +   +       +  +
	 *   + +         + +
	 *   7+++++++++++6
	 */
	virtual TArray<FVector> GetModelOrientedBoundingBox(bool bUseFixedSize = true) const;
	virtual TArray<FVector> GetModelOrientedBoundingBoxWithoutRotator(bool bUseFixedSize = true) const;

	virtual void GetModelOrientedBoundingBox(FVector& Center, FVector& Extents, FQuat& Rotation) const;

	virtual	void GetModelCollectionCaptureComponentsAndActors(TArray<UPrimitiveComponent*>& ShowComponents, TArray<AActor*>& ShowActors);

	FDSStateProperty GetModelStateProperty() const;
	bool HasLock() const;
	bool HasHidden() const;
	bool HasFavourite() const;

	/*获取Model对应的View，在多状态叠加下是否Hidden*/
	UFUNCTION()
	bool GetViewHidden() const;

	bool IsViewTypeHidden() const { return bViewTypeHidden; }

	UFUNCTION()
	ADSBaseView* GetOwnedView() { return OwnedView; };

	template <typename T>
	T* GetOwnedView() { return Cast<T>(OwnedView); }


	/*-----------------------Setter--------------------------*/
	UFUNCTION()
	void SetOwnedView(ADSBaseView* InView) { OwnedView = InView; };

	UFUNCTION()
	void AddModelStateFlag(EModelState InState);

	UFUNCTION()
	void RemoveModelStateFlag(EModelState InState);

	bool IsHasModelFlag(const EModelState& InState);

	bool IsSelected();


	UFUNCTION()
	FModelStatusFlag GetModelStateFlag() { return ModelStateFlag; }

	UFUNCTION()
	FHiddenFlags& GetModelHiddenFlags() { return ModelHiddenFlags; }

	virtual bool IsCanMultiSelect() const { return false; }
	virtual bool IsCanGroup() const { return false; }

	virtual bool IsContainsModel(UDSBaseModel* InModel) { return false; }

	virtual FBox GetBoundBox();
	virtual FBox GetBoundBoxByPropertyCalculate() { return FBox(ForceInit); }

	bool IsInMultiSelect() const { return MultiGroupData.IsMultiItem(); }
	FString GetMultiUUID() const { return MultiGroupData.GetMultiUUID(); }
	void AddToMultiSelect(const FString& InMultiUUID);
	void ReleaseFromMultiSelect() { MultiGroupData.ReleaseMultiSelect(); }
	FDSGroupMarkData GetMultiGroupData() const { return MultiGroupData; }

	virtual bool IsInGroup() const { return MultiGroupData.IsGroupItem(); }
	virtual FString GetGroupUUID() const { return MultiGroupData.GetGroupUUID(); }
	void AddToGroupSelect(const FString& InGroupUUID);
	void ReleaseFromGroup();

	void BroadcastToRefreshForMultiGroup();
	void BroadcastToDeleteForMultiGroup();

	virtual EDSGizmoModeType GetDefaultGizmoModeType(bool bIs2DScene) const;

	virtual void UpdatePivotOffset();

	//------------ Gizmo Scale Component Operations ------------
	virtual FVector CalculateModelSize() const;
	virtual FVector CalculateScaledTranslationOffset(EDSGizmoOperatorType OperatorType, const FVector& SourceSize, const FVector& NewSize, const TArray<FVector>& OriginOBB,
	                                                 const TArray<FVector>& ScaledOBB);

	virtual TArray<FDSGizmoScalePoint> GetAllScaleOperatorPoints() const;
	virtual bool IsValidToTranslate(EDSGizmoOperatorType OperatorType, const FVector& SourceSize, const FVector& NewSize) const { return true; }
	virtual bool IsValidToRotate(EDSGizmoOperatorType OperatorType, const FVector& SourceSize, const FVector& NewSize) const { return true; }
	virtual bool IsValidSizeToScale(EDSGizmoOperatorType OperatorType, const FVector& SourceSize, const FVector& NewSize) const;
	virtual bool ShouldTranslateModelAfterScaleByGizmo(EDSGizmoOperatorType OperatorType, const FVector& SourceSize, const FVector& NewSize);
	virtual bool ClampScaleSizeToLimitation(EDSGizmoOperatorType OperatorType, const FVector& SourceSize, FVector& NewSize);

	virtual void TranslateByGizmo(const EAxis::Type& InRotAxis, const FTransform& SourceTrans, const FTransform& NewTrans);
	virtual void PreTranslateByGizmo(const EAxis::Type& InRotAxis, const FTransform& SourceTrans, const FTransform& NewTrans);
	virtual void PostTranslateByGizmo(const EAxis::Type& InRotAxis, const FTransform& SourceTrans, const FTransform& NewTrans);
	
	virtual void RotatorByGizmo(const EAxis::Type& InRotAxis, const FTransform& SourceTrans, const FTransform& NewTrans);
	virtual void PreRotatorByGizmo(const EAxis::Type& InRotAxis, const FTransform& SourceTrans, const FTransform& NewTrans);
	virtual void PostRotatorByGizmo(const EAxis::Type& InRotAxis, const FTransform& SourceTrans, const FTransform& NewTrans);

	virtual void ScaleSizeByGizmo(EDSGizmoOperatorType OperatorType, const FVector& SourceSize, const FVector& NewSize);
	virtual void PreScaleSizeByGizmo(EDSGizmoOperatorType OperatorType, const FVector& SourceSize, const FVector& NewSize);
	virtual void PostScaleSizeByGizmo(EDSGizmoOperatorType OperatorType, const FVector& SourceSize, const FVector& NewSize);

	//刷新属性栏尺寸
	virtual void UpdatePropertyShowSize() {}

	virtual void UpdatePropertyShowRotator() {}

	//retVal : scale size is valid
	virtual bool CheckScaleSizeAfterGizmo(EDSGizmoOperatorType OperatorType, const FVector& SourceSize, const FVector& NewSize) { return true; }


	//
	virtual void SetVisual(bool InbVisual) {}
	virtual bool GetVisual() const { return false; }

	virtual bool IsModelStable() const { return false; }
	
	virtual void UpdateSnapInfo();
	virtual void UpdateMeshInfo();

	virtual void UpdateComponentMeshInfoUV(const int32& InIndex);
	virtual void UpdateMeshInfoUV();

	virtual void InitOutlineInfo()
	{
	}

	virtual void UpdateOutlineInfo();
	virtual void CalculateOutline(){}
	virtual void SyncChildOutline() {}
	virtual bool ExpandOutlineForComponentSelect() { return false; }
	virtual bool RecoverOutline() { return false; }

protected:
	// generate uuid, no outside use
	void GenerateUUID();

public:
	void OnExecuteAction(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr = FDSBroadcastMarkData::BroadcastToMVCMark);

protected:
	//all
	virtual void OnExecute_All(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	
	/*
	*  @@ 对应结构体 EDSExecuteType_Update
	*/
	void OnExecute_Update(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	virtual void OnExecute_Model_UpdateSelf(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	virtual void OnExecute_Model_UpdateByProperty(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	
	/**
	 *  @@ 对应结构体 EDSExecuteType_Select
	 */
	void OnExecute_Select(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	virtual void OnExecute_Model_Select(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	virtual void OnExecute_Model_UnSelect(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	virtual void OnExecute_Model_SelectComponent(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	virtual void OnExecute_Model_UnSelectComponent(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
    virtual void OnExecute_Model_PreciseSelect(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
    virtual void OnExecute_Model_UnPreciseSelect(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	
	/**
	 *  @@ 对应结构体 EDSExecuteType_Hover
	 */
	void OnExecute_Hover(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	virtual void OnExecute_Model_Hover(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	virtual void OnExecute_Model_UnHover(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	
	/**
	 *  @@ 对应结构体 EDSExecuteType_Spawn
	 */
	void OnExecute_Spawn(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	virtual void OnExecute_Model_Spawn(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	virtual void OnExecute_Model_GenerateByProperty(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	virtual void OnExecute_Model_GenerateMesh(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	

	/**
	 *  @@ 对应结构体 EDSExecuteType_Transform
	 */
	void OnExecute_Transform(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	virtual void OnExecute_Model_Transform(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	virtual void OnExecute_Model_TransformByAxis(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	virtual void OnExecute_Model_TransformNormal(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	
	/**
	 *  @@ 对应结构体 EDSExecuteType_Delete
	 */
	void OnExecute_Delete(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
    virtual void OnExecute_Model_Delete(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	virtual void OnExecute_Model_DeleteSelf(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
    virtual void OnExecute_Model_DeleteSelfOnly(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	
	/**
	 *  @@ 对应结构体 EDSExecuteType_Lock
	 */
	void OnExecute_Lock(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	virtual void OnExecute_Model_Lock(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
    virtual void OnExecute_Model_UnLock(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	
	/**
	 *  @@ 对应结构体 EDSExecuteType_Hidden
	 */
	void OnExecute_Hidden(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	virtual void OnExecute_Model_Hidden(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	virtual void OnExecute_Model_UnHidden(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	virtual void OnExecute_Model_AreaHidden(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	virtual void OnExecute_Model_UnAreaHidden(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	virtual void OnExecute_Model_ViewTypeHidden(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	virtual void OnExecute_Model_UnViewTypeHidden(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);

	UFUNCTION()
	void OnHiddenFlagHandle();
	UFUNCTION()
	void OnUnHiddenFlagHandle();

	/**
	 *  @@ 对应结构体 EDSExecuteType_Favorite
	 */
	void OnExecute_Favorite(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	virtual void OnExecute_Model_Favorite(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	virtual void OnExecute_Model_UnFavorite(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	
	void OnExecute_Follow(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);

	/**
	 *  @@ 对应结构体 EDSExecuteType_Refresh
	 */
	void OnExecute_Refresh(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	virtual void OnExecute_Model_RefreshMaterial(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	virtual void OnExecute_Model_RefreshMaterialForce(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);

	/**
	 *  @@ 对应结构体 EDSExecuteType_Revoke
	 */
	void OnExecute_Revoke(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	virtual void OnExecute_Model_Undo(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	virtual void OnExecute_Model_Redo(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);

	/**
	 *  @@ 对应结构体 EDSExecuteType_Flip
	 */
	void OnExecute_Flip(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	virtual void OnExecute_Model_FlipX(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	virtual void OnExecute_Model_FlipY(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);

	/**
	 *  @@ 对应结构体 EDSExecuteType_Multi
	 */
	void OnExecute_Multi(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	virtual void OnExecute_Model_MultiAdd(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	virtual void OnExecute_Model_MultiRelease(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);

	/**
	 *  @@ 对应结构体 EDSExecuteType_Group
	 */
	void OnExecute_Group(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	virtual void OnExecute_Model_GroupCombine(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	virtual void OnExecute_Model_GroupRelease(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);

	/**
	 *  @@ 对应结构体 EDSExecuteType_Overlap
	 */
	void OnExecute_Overlap(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	virtual void OnExecute_Model_Overlap(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	virtual void OnExecute_Model_UnOverlap(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);

	/**
	 *  @@ 对应结构体 EDSExecuteType_Enable
	 */
	void OnExecute_Enable(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	virtual void OnExecute_Model_Disable(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	virtual void OnExecute_Model_Enable(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);

	/**
	 *  @@ 对应结构体 EDSExecuteType_Section
	 */
	void OnExecute_Section(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);
	virtual void OnExecute_Model_UV(const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr);

public:
	virtual void OnReSelected();

	virtual void FlipHiddenState();

	virtual void SyncTransform(const FTransform& InTrans, bool NeedExecute = false);
	//X : W; Y : D; Z : H
	virtual FVector GetCurrentSize();
	virtual FVector GetCurrentRealSize();
	//X : W; Y : D; Z : H
	virtual void SyncScale(const FVector& InScaleSize) {}
	virtual bool CurrentSizeValidToScale(const FVector& InScale) { return true;}
	virtual void ReInitData() {}

protected:
	virtual void OnRefreshDrawing();

public:
	virtual UDSBaseModel* OnCopy() { return nullptr; }
	virtual UDSBaseModel* Split(const FVector& InLoc) { return nullptr; }


	virtual void SetMaterialByUUID(const FString& InUUID,const FString& InResourceID = "", const double& InUSize = 1000.0, const double& InVSize = 1000.0, bool bUpdateUV = true);

	virtual void AddLinkModel(UDSBaseModel* InModel);
	virtual void DeleteLinkModel(UDSBaseModel* InModel);
	virtual void ClearLinkModel();
	virtual void SetDeleted(bool InDeleted);
	UFUNCTION()
	virtual void OnLinkModelUpdatedHandle(UDSBaseModel* InModel)
	{
	};
	UFUNCTION()
	virtual void OnLinkModelDeletedHandle(UDSBaseModel* InModel)
	{
	};

	UFUNCTION()
	virtual void OnLinkModelTransformHandle(UDSBaseModel* InModel)
	{
	};

	UFUNCTION()
	virtual void OnLinkModelZHeightHandle(UDSBaseModel* InModel)
	{
	};

	TSet<TWeakObjectPtr<UDSBaseModel>> GetLinkModels();

	bool GetIsDeleted();

	bool CanMoved();
	void ClearStyle();

	virtual void Serialization(TSharedRef<TJsonWriter<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>> JsonWriter);
	virtual void Deserialization(const TSharedPtr<FJsonObject>& InJsonData, bool bDeserializeID = true);

	UFUNCTION()
	bool GetIsAreaHidden() { return bAreaHidden; };

	void SetCreateIndex(const int32& InIndex);
	int32 GetCreateIndex();

	virtual void BindCameraTypeChangeHandle();
	virtual void ObserveResourceStatusEvent();
	virtual void StopObserveResourceStatusEvent();
	virtual bool CheckIfNeedsStopObserveResourceStatus() const;

	UFUNCTION()
	virtual void OnCameraTypeChange(int32 InType)
	{
	};

	UFUNCTION()
	virtual void OnResourceStatusInPoolChanged(const FString& Identify, EDSResourceType ResourceType, EResourceStatusEventType EventType);


	// model property change delegate, execute to refresh view
	FDSModelPoolDeleteDelegate OnModelPoolDeleteDelegate;
	FLinkModelUpdated OnLinkModelUpdatedDelegate;
	FLinkModelUpdated OnLinkModelDeletedDelegate;
	FLinkModelUpdated OnLinkModelTransformDelegate;
	FLinkModelUpdated OnLinkModelZHeightDelegate;
	
	FDSRefreshPropertyWidgetDelegate DSRefreshPropertyWidgetDelegate;

	FDSRefreshForMultiGroupDelegate DSRefreshForMultiGroupDelegate;
	FDSDeleteForMultiGroupDelegate DSDeleteForMultiGroupDelegate;

protected:
	FString UUID;
	EDSModelType ModelType;

	// model property
	TSharedPtr<FDSBaseProperty> Property;

	//state
	UPROPERTY()
	FDSModelState ModelState;

	// model pendant 
	UPROPERTY()
	FPendantInfo PendantInfo;

	//owner's model
	UPROPERTY()
	UDSBaseModel* OwnerModel;

	//mark for multi select / group
	UPROPERTY()
	FDSGroupMarkData MultiGroupData;

	//child components' model
	UPROPERTY()
	TSet<UDSBaseModel*> ComponentModelSet;

	//outline info
	UPROPERTY()
	FOutlineInfo OutlineInfo;

	//snap info
	UPROPERTY()
	FSnapInfo SnapInfo;

	UPROPERTY()
	TSet<TWeakObjectPtr<UDSBaseModel>> LinkModels;

	UPROPERTY()
	FDSMeshInfo MeshInfo;

	bool bIsDeleted;

	bool bCanMoved;

	/*因房间选择导致的隐藏*/
	UPROPERTY()
	bool bAreaHidden;

	/*因视角变化导致的隐藏*/
	UPROPERTY()
	bool bViewTypeHidden;

	int32 CreateIndex;

	UPROPERTY()
	ADSBaseView* OwnedView;

	UPROPERTY()
	FModelStatusFlag ModelStateFlag;

	UPROPERTY()
	FHiddenFlags ModelHiddenFlags;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly)
	FVector PreScaleLocation;
};
