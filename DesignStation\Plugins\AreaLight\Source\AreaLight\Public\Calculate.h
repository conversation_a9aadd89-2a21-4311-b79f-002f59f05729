﻿#include <vector>
#include <cmath>
#include <limits>
#include "LightPanel.h"

using namespace std;

namespace AreaLightCalculate
{

    struct Point {
        double x, y;
        Point(double x_ = 0, double y_ = 0) : x(x_), y(y_) {}
    };

    struct Line {
        Point p1, p2;
        Line(Point a, Point b) : p1(a), p2(b) {}
    };

    struct Rectangle {
        double x, y, size, brightness;
    };

    vector<Point> get_window_centers(const vector<Line>& windows) {
        vector<Point> centers;
        for (const auto& win : windows) {
            centers.push_back(Point((win.p1.x + win.p2.x) / 2.0, (win.p1.y + win.p2.y) / 2.0));
        }
        return centers;
    }

    // 计算多边形面积
    double polygon_area(const vector<Point>& poly) {
        double area = 0;
        int n = poly.size();
        for (int i = 0; i < n; ++i) {
            const Point& p1 = poly[i];
            const Point& p2 = poly[(i + 1) % n];
            area += (p1.x * p2.y - p2.x * p1.y);
        }
        return fabs(area) / 2.0;
    }

    // 多边形裁剪为两个（只支持垂直/水平切线）
    bool clip_polygon(const vector<Point>& poly, const Point& p, bool vertical, vector<Point>& out1, vector<Point>& out2) {
        out1.clear();
        out2.clear();

        for (size_t i = 0; i < poly.size(); ++i) {
            const Point& a = poly[i];
            const Point& b = poly[(i + 1) % poly.size()];

            bool side_a = vertical ? (a.x < p.x) : (a.y < p.y);
            bool side_b = vertical ? (b.x < p.x) : (b.y < p.y);

            if (side_a) out1.push_back(a);
            else out2.push_back(a);

            if (side_a != side_b) {
                double t = vertical ? (p.x - a.x) / (b.x - a.x) : (p.y - a.y) / (b.y - a.y);
                double ix = a.x + t * (b.x - a.x);
                double iy = a.y + t * (b.y - a.y);
                Point inter(ix, iy);
                out1.push_back(inter);
                out2.push_back(inter);
            }
        }

        return out1.size() >= 3 && out2.size() >= 3;
    }

    bool point_in_polygon(double x, double y, const vector<Point>& poly) {
        int n = poly.size();
        bool inside = false;
        for (int i = 0, j = n - 1; i < n; j = i++) {
            double xi = poly[i].x, yi = poly[i].y;
            double xj = poly[j].x, yj = poly[j].y;
            if ((yi > y) != (yj > y) &&
                x < (xj - xi) * (y - yi) / (yj - yi + 1e-12) + xi)
                inside = !inside;
        }
        return inside;
    }

    bool all_corners_inside(const Point& center, double size, const vector<Point>& polygon, double margin) {
        double half = size / 2.0;
        vector<Point> corners = {
            {center.x - half, center.y - half},
            {center.x + half, center.y - half},
            {center.x + half, center.y + half},
            {center.x - half, center.y + half},
        };

        for (const auto& p : corners) {
            if (!point_in_polygon(p.x, p.y, polygon)) return false;
            // check distance to edge
            double min_dist = 1e9;
            for (int i = 0, j = polygon.size() - 1; i < polygon.size(); j = i++) {
                Point a = polygon[j], b = polygon[i];
                double dx = b.x - a.x, dy = b.y - a.y;
                double t = max(0.0, min(1.0, ((p.x - a.x) * dx + (p.y - a.y) * dy) / (dx * dx + dy * dy)));
                double proj_x = a.x + t * dx, proj_y = a.y + t * dy;
                double dist = hypot(p.x - proj_x, p.y - proj_y);
                min_dist = min(min_dist, dist);
            }
            if (min_dist < margin) return false; // 离边界太近
        }
        return true;
    }

    vector<vector<Point>> split_polygon_to_rectangles(const vector<Point>& poly, int depth = 0) {
        if (depth > 20 || poly.size() < 4) return { poly };

        auto is_rect = [](const vector<Point>& p) -> bool {
            if (p.size() != 4) return false;
            for (int i = 0; i < 4; ++i) {
                Point a = p[i], b = p[(i + 1) % 4], c = p[(i + 2) % 4];
                double dx1 = b.x - a.x, dy1 = b.y - a.y;
                double dx2 = c.x - b.x, dy2 = c.y - b.y;
                double dot = dx1 * dx2 + dy1 * dy2;
                if (abs(dot) > 1e-6) return false;
            }
            return true;
        };

        if (is_rect(poly)) return { poly };

        // 找直角点作为候选切割点
        vector<Point> candidates;
        int n = poly.size();
        for (int i = 0; i < n; ++i) {
            Point p1 = poly[(i - 1 + n) % n];
            Point p2 = poly[i];
            Point p3 = poly[(i + 1) % n];
            double dx1 = p2.x - p1.x, dy1 = p2.y - p1.y;
            double dx2 = p3.x - p2.x, dy2 = p3.y - p2.y;
            double dot = dx1 * dx2 + dy1 * dy2;
            if (abs(dot) < 1e-6) candidates.push_back(p2);
        }

        if (candidates.size() < 2) return { poly };

        double best_diff = 1e12;
        vector<Point> best1, best2;

        for (const auto& p : candidates) {
            for (bool vertical : {true, false}) {
                vector<Point> part1, part2;
                if (clip_polygon(poly, p, vertical, part1, part2)) {
                    if (part1.size() < 4 || part2.size() < 4) continue;
                    double a1 = polygon_area(part1);
                    double a2 = polygon_area(part2);
                    double diff = fabs(a1 - a2);
                    if (diff < best_diff) {
                        best_diff = diff;
                        best1 = part1;
                        best2 = part2;
                    }
                }
            }
        }

        if (!best1.empty() && !best2.empty()) {
            auto r1 = split_polygon_to_rectangles(best1, depth + 1);
            auto r2 = split_polygon_to_rectangles(best2, depth + 1);
            r1.insert(r1.end(), r2.begin(), r2.end());
            return r1;
        }

        return { poly };
    }

    // 删除所有距离墙边小于 wall_margin 的补光板（用于 generate 函数中）
    bool is_valid_panel(const Point& center, const vector<Point>& polygon, double wall_margin) {
        if (!point_in_polygon(center.x, center.y, polygon)) return false;

        double min_dist = 1e9;
        for (int i = 0, j = polygon.size() - 1; i < polygon.size(); j = i++) {
            Point a = polygon[j], b = polygon[i];
            double dx = b.x - a.x, dy = b.y - a.y;
            double t = max(0.0, min(1.0, ((center.x - a.x) * dx + (center.y - a.y) * dy) / (dx * dx + dy * dy)));
            double proj_x = a.x + t * dx, proj_y = a.y + t * dy;
            double dist = hypot(center.x - proj_x, center.y - proj_y);
            min_dist = min(min_dist, dist);
        }
        return min_dist >= wall_margin;
    }

    // 直接在生成时只保留合法补光板
    void generate_light_panels_for_rect(const vector<Point>& polygon, double min_size, double spacing,
        double wall_margin, vector<Rectangle>& panels, const vector<Point>& window_centers) {

        double min_x = 1e9, max_x = -1e9, min_y = 1e9, max_y = -1e9;
        for (const auto& p : polygon) {
            min_x = min(min_x, p.x);
            max_x = max(max_x, p.x);
            min_y = min(min_y, p.y);
            max_y = max(max_y, p.y);
        }

        double usable_width = max_x - min_x - 2 * wall_margin;
        double usable_height = max_y - min_y - 2 * wall_margin;

        if (usable_width <= 0 || usable_height <= 0) return;

        int cols = max(1, int((usable_width + spacing) / (min_size + spacing)));
        int rows = max(1, int((usable_height + spacing) / (min_size + spacing)));

        double total_w = cols * min_size + (cols - 1) * spacing;
        double total_h = rows * min_size + (rows - 1) * spacing;

        double start_x = min_x + wall_margin + (usable_width - total_w) / 2 + min_size / 2;
        double start_y = min_y + wall_margin + (usable_height - total_h) / 2 + min_size / 2;

        for (int i = 0; i < cols; ++i) {
            for (int j = 0; j < rows; ++j) {
                double cx = start_x + i * (min_size + spacing);
                double cy = start_y + j * (min_size + spacing);

                Point center(cx, cy);
                if (!is_valid_panel(center, polygon, wall_margin)) continue;

                double brightness = 5.0;
                for (const auto& wc : window_centers) {
                    double dist = hypot(cx - wc.x, cy - wc.y);
                    brightness -= max(0.0, 4.0 * (1.0 - min(dist / 500.0, 1.0)));
                }
                brightness = max(1.0, brightness);

                panels.push_back({ cx, cy, min_size, brightness });
            }
        }
    }
}

