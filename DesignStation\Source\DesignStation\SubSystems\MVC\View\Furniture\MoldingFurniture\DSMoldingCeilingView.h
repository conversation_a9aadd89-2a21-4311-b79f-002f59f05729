﻿#pragma once

#include "CoreMinimal.h"
//#include "SubSystems/MVC/View/Furniture/DSFurnitureView.h"
#include "SubSystems/MVC/View/Custom/DSCupboardBaseView.h"
#include "DSMoldingCeilingView.generated.h"


/**
 * *    @@ 户型成型吊顶
 * */

UCLASS()
class ADSMoldingCeilingView : public ADSCupboardBaseView
{
	GENERATED_BODY()

public:
	virtual void RealSpawnViewLogic(UDSBaseModel* InModel) override;
	virtual void RealUpdateViewLogic(UDSBaseModel* InModel) override;


	virtual void RealHiddenViewLogic(UDSBaseModel* InModel) override;
	virtual void RealUnHiddenViewLogic(UDSBaseModel* InModel) override;

	virtual void RealHoverViewLogic(UDSBaseModel* InModel) override;
	virtual void RealUnHoverViewLogic(UDSBaseModel* InModel) override;

	virtual void RealSelectViewLogic(UDSBaseModel* InModel) override;
	virtual void RealUnSelectViewLogic(UDSBaseModel* InModel) override;
protected:
	virtual void Init() override;

	virtual void BeginPlay() override;

	virtual void RealTransformViewLogic(UDSBaseModel* InModel) override;
	virtual void OnGenerateMeshWorkFinished() override;
};