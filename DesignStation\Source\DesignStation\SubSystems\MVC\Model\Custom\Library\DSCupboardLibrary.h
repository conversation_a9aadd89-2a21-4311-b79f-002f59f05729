// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

//#pragma warning(disable:4996)

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "SubSystems/DSCore.h"
#include "SubSystems/MVC/CatalogSupport/Core/RefToFileData.h"
#include "SubSystems/MVC/Model/Custom/ModelInfo/DSCupboardModelInfo.h"
#include "SubSystems/MVC/Model/Custom/ModelInfo/DSCustomStyleInfo.h"
#include "SubSystems/UI/Widget/CustomCupboard/Property/CustomCupboard/Core/DSCustomPropertyData.h"

#include "DSCupboardLibrary.generated.h"

class UDSCupboardModel;

DECLARE_DELEGATE_TwoParams(FOnProcessParsedByOriginalNodeDelegate, const TSharedPtr<FMultiComponentDataItem>&, const TSharedPtr<FMultiComponentDataItem>&);

/**
 *   
 */
UCLASS(Blueprintable)
class DESIGNSTATION_API UDSCupboardLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()

public:
	static bool LoadFileByFolderID(const FString& InFolderID, FRefToLocalFileData& OutFileData);
	
	static TArray<FRefToLocalFileData> CollectRelativeFoldersData(const FRefToLocalFileData& InData);

	static void CollectRelativeMaterialFromNode(const TSharedPtr<FMultiComponentDataItem>& InNode, TMap<FString, EDSResourceType>& OutMaterialIds);
	
	static void CalculateTreeRootNodeParams(const FRefToLocalFileData& InData, const TSharedPtr<FMultiComponentDataItem>& InNode,
		const TMap<FString, FParameterData>& StyleParams,
		TMap<FString, FParameterData>& OutParentParams);

	static bool ParseTreeFromNode(const TSharedPtr<FMultiComponentDataItem>& RootNode,
		const TSharedPtr<FMultiComponentDataItem>& InNode,
		const TSharedPtr<FMultiComponentDataItem>& InOriginalNode,
		const TMap<FString, FParameterData>& ParentParams,
		const FOnProcessParsedByOriginalNodeDelegate& ParseNodeProcessor,
		TMap<TSharedPtr<FMultiComponentDataItem>, TSharedPtr<TMap<FString, FParameterData>>>& OutComponentOverrideParams,
		TMap<FString, EDSResourceType>& OutFolderIdsForDownload,
		TArray<FDSCustomStyleReplacementNode>& OutStylizedNodes);
	
	static void GetHeritParameterData(
		const TArray<FRefToLocalFileData>& DirectoryRefDatas,
		const TMap<FString, FParameterData>& GlobalParams,
		TMap<FString, FParameterData>& OutHeritParameters
	);

	static void ConstructTreeRootNode(const FRefToLocalFileData& RefData, FMultiComponentDataItem& TreeNode);

	/**
	 * Combines two sets of parameter data into a single set, resolving any conflicts by prioritizing the second set.
	 *
	 * This function takes two maps of parameter data, where each map represents a collection of parameters identified by their names.
	 * It combines these parameters into a single map. If the same parameter exists in both input maps, the value from the second map
	 * (InCombineParameters) will be used in the resulting map. This is useful for overriding base parameters with more specific ones
	 * in a hierarchical parameter system.
	 *
	 * @param InBaseParameters The base set of parameters. These serve as the default values.
	 * @param InCombineParameters The set of parameters to combine with the base. Values in this map override those in the base map.
	 * @param Result The resulting map of combined parameters. This map is filled by the function.
	 */
	static void CombineParameters(
		const TMap<FString, FParameterData>& InBaseParameters, 
		const TMap<FString, FParameterData>& InCombineParameters, 
		TMap<FString, FParameterData>& Result
	);

	/**
	 * Calculates the final set of parameters by combining global parameters with override parameters.
	 *
	 * This static function is designed to merge two sets of parameters: global parameters and override parameters.
	 * Global parameters serve as the default values for the system, while override parameters are specific to certain
	 * conditions or contexts and can modify or add to the global parameters. This mechanism allows for a flexible and
	 * dynamic parameter system where base settings can be adjusted or extended on a case-by-case basis.
	 *
	 * The function prioritizes the override parameters. If a parameter exists in both the global and override sets,
	 * the value from the override set will be used in the final combined set of parameters. This approach ensures that
	 * specific adjustments and customizations can take precedence over the default settings.
	 *
	 * @param GlobalParameters A map of parameter names to their values representing the global parameters.
	 * @param OverrideParameters A map of parameter names to their values representing the parameters that should override the global ones.
	 * @param Result A reference to a map where the resulting set of parameters will be stored. This map combines the global and override parameters, with override parameters taking precedence.
	 */
	static void CalculateParameters(
		const TMap<FString, FParameterData>& GlobalParameters,
		const TMap<FString, FParameterData>& OverrideParameters,
		TMap<FString, FParameterData>& Result
	);

	static bool ComponentNeedSeparate(const TArray<FParameterData>& InParams);
	static int32 GetModelType_Parameters(const TArray<FParameterData>& InParams);

	static bool GetMultiComponentInfo(
		const TMap<FString, FParameterData>& GlobalParams,
		TMap<FString, FParameterData> UpperLevelParams,
		FDSCupboardModelInfo& OutTreeDatas,
		TArray<FString>& NeedDownloadIDS,
		TArray<FExpressionValuePair>& DependModelIDS,
		TArray<FExpressionValuePair>& DependMatIDS,
		FDependFileData InDependDatas,
		TArray<FString> InSkipDependFiles
	);

protected:
	static bool ParseTreeFromNode_Inner(const TSharedPtr<FMultiComponentDataItem>& InNode,
		const TSharedPtr<FMultiComponentDataItem>& InOriginalNode,
		const TMap<FString, FParameterData>& GlobalParams,
		const FOnProcessParsedByOriginalNodeDelegate& ParseNodeProcessor,
		const TArray<TPair<FString, TWeakPtr<FMultiComponentDataItem>>>& NodePath,
		TMap<TSharedPtr<FMultiComponentDataItem>, TSharedPtr<TMap<FString, FParameterData>>>& OutComponentOverrideParams,
		TMap<FString, EDSResourceType>& OutFolderIdsForDownload,
		TArray<FDSCustomStyleReplacementNode>& OutStylizedNodes);

private:
	//递归获取引用关系
	static bool GetRefRelationRecursiveLevelOrder(
		FRefToLocalFileData& FileData,
		const TMap<FString, FParameterData>& GlobalParams,
		TMap<FString, FParameterData> UpperLevelParams,
		const TSharedPtr<FMultiComponentDataItem>& CurComponentData,
		TArray<FDSComponentInfo>& ModelComponentData,
		const FString& TreePath,
		TArray<FString>& NeedDownloadIDS,
		TArray<FExpressionValuePair>& DependModelIDS,
		TArray<FExpressionValuePair>& DependMatIDS,
		FDependFileData InDependDatas,
		TArray<FString> InSkipDependFiles
	);


public:

	static bool CombineBaseDHWParameters(TMap<FString, FParameterData>& OutParameters, const TMap<FString, FParameterData>& InParameters);

	static bool ConvertBaseDHWParametersToFVector(FVector& OutVector, const TMap<FString, FParameterData>& InParameters);


	static bool ConvertBaseDHWParametersToFVector(FVector& OutVector, const TArray<FParameterData>& InParameters);

	static bool ConvertMaxDHWParametersToFVector(FVector& OutVector, const TMap<FString, FParameterData>& InParameters);

	static bool ConvertMinDHWParametersToFVector(FVector& OutVector, const TMap<FString, FParameterData>& InParameters);

	static bool ConvertDHWParametersToFVector(FVector& OutMinVector, FVector& OutDefaultVector, FVector& OutMaxVector, const TMap<FString, FParameterData>& InParameters);

	static bool WriteBaseDHWParamtersFromVector(const FVector& InVector, TArray<FParameterData>& OutParameters);

	static bool WriteBaseDrawerParamtersFromVector(const FVector& InVector, TArray<FParameterData>& OutParameters);
	static bool WriteBaseFGParamtersFromVector(const double& UpFG, const double& DownFG, const double& LeftFG, const double& RightFG, TArray<FParameterData>& OutParameters);
	//static bool WriteParamter(const FString & InName, const FString& InValue, TArray<FParameterData>& OutParameters);

	static double GetDistanceToFloorParameter(UDSBaseModel* InModel);
	static double GetDistanceToFloorParameter(const TArray<FParameterData>& Parameters);
	static bool WriteDistanceToFloorParameter(const float& InDistance, TArray<FParameterData>& OutParameters,bool bDisableAdaptation = false);
	
	static bool IsFunctionalCupboardModel(UDSBaseModel* Model);

	static bool IsFunctionalCupboardModel(const TArray<FParameterData>& Parameters);

	static UDSBaseModel* GetModelByUUID(const FString& InUUID, const FDSCupboardModelInfo& InModelInfo);

	static UDSBaseModel* GetModelByUUID(const FString& InUUID);

	static UDSBaseModel* GetFunctionalRootModel();

	static bool SetFunctionalRootModel(UDSBaseModel* InRootModel);

	static bool GetRetractValue(UDSBaseModel* InModel,float& ForwardRetract,float& BackwardRetract);

	static bool GetRetractValue(const TArray<FParameterData>& Parameters, float& ForwardRetract, float& BackwardRetract);

	static bool GetAdaptationIsOverallCollision(UDSBaseModel* InModel,bool DefaultOverall = false);

	static bool GetAdaptationIsOverallCollision(const TArray<FParameterData>& Parameters, bool DefaultOverall = false);

	static bool GetIsIngoreCollsion(UDSBaseModel* InModel);

	static bool GetIsIngoreCollsion(const TArray<FParameterData>& Parameters);

	static bool CanCupboardApplyStyle(UDSBaseModel* InModel);


	static int GetCornerCutType(const TArray<FParameterData>& Parameters);


	UFUNCTION(BlueprintPure)
	static TArray<int32> SplitParameterPath(const FString& InPath);

	UFUNCTION(BlueprintPure)
	static UDSBaseModel* FindChildModelByPath(UDSBaseModel* InModel, const FString& InPath);
	
	UFUNCTION(BlueprintCallable)
	static bool FindParameterByPath(UDSBaseModel* InModel, const FDSCustomGroupParam& InParamInfo, FParameterData& OutParameterData);

	UFUNCTION(BlueprintCallable)
	static bool FindParameterFromModel(UDSBaseModel* InModel, const FString& InParamName, FParameterData& OutParameterData);
	static FParameterData* FindParameterPointerFromModel(UDSBaseModel* InModel, const FString& InParamName);

	UFUNCTION(BlueprintCallable)
	static TArray<FParameterEnumTableData> CollectValidSortedEnumTables(const FParameterData& InParam);

	UFUNCTION(BlueprintPure)
	static FString ForceNumericFractionalDigits(const FString& Source, int32 FractionalDigits);

	UFUNCTION(BlueprintPure)
	static FString GetCustomTypeDisplayName(EDSModelType InType);

	UFUNCTION(BlueprintPure)
	static FString GetCustomNodeTypeDisplayName(int32 InType);

	UFUNCTION(BlueprintPure)
	static FString GetRelativeCodeFromModelFileType(int32 InType);

	static bool IsSubOfSpecifiedCustomType(const FString& SuperiorType, int32 InSubType);
	static bool TryGetCodeNameFromCustomType(int32 InCustomType, FString& OutCodeName);

	static void TryGenerateReplacementNodeFromNode(const TSharedPtr<FMultiComponentDataItem>& InNode, const TArray<TPair<FString, TWeakPtr<FMultiComponentDataItem>>>& InNodePath, TArray<FDSCustomStyleReplacementNode>& OutStylizedNodes);

	// 将节点替换为指定FolderId的定制文件，参数从文件中获取，如果新旧参数表中同时存在某个参数，将旧参数覆盖到新参数中
	static bool ReplaceCustomNodeWithFolderId(const TSharedPtr<FMultiComponentDataItem>& InNode, const FString& FolderId);

	static bool IsDoorPanelDrawer(UDSCupboardModel* InModel);

	static UDSBaseModel* FindModelByMultiComponentDataUUID(const FString& InUUID);

	static void CollectAllStylizedNodeInfos(const TSharedPtr<FMultiComponentDataItem>& InNode, TArray<FDSCustomStyleReplacementNode>& OutStylizedNodes, TArray<TPair<FString, TWeakPtr<FMultiComponentDataItem>>>& NodePath);

	static bool RecursiveFindDoorCoreNode(const TSharedPtr<FMultiComponentDataItem>& InNode, TSharedPtr<FMultiComponentDataItem>& OutNode);

	static void CollectTreeRelationship(UDSCupboardModel* RootModel,
		const TSharedPtr<FMultiComponentDataItem>& RootNode,
		const TSharedPtr<FMultiComponentDataItem>& BelongsNode,
		TMap<TSharedPtr<FMultiComponentDataItem>, UDSCupboardModel*>& OutBelongsModel,
		TMap<TSharedPtr<FMultiComponentDataItem>, TSharedPtr<FMultiComponentDataItem>>& OutBelongsNode);
	
	static bool CheckSizeForNewNodeIsValid(const TSharedPtr<FMultiComponentDataItem>& InNode, const FString& FolderId, const TMap<FString, FParameterData>& ParentParams);
	
	//获得子节点的Model，如果这个节点是自己，则返回自己的Model
	static UDSCupboardModel* GetModelByNodeUUID(const FString& InUUID, UDSCupboardModel* InModel);

	static UDSCupboardModel* GetModelByNodeUUID(const FString& InUUID);

	//获得所有子节点，不包括自己
	static void GetAllChildrenModels(const TSharedPtr<FMultiComponentDataItem>& InModel, TArray<UDSBaseModel*>& OutModels);

	//通过子节点的UUID，获得这个子节点
	static TSharedPtr<FMultiComponentDataItem> GetNodeByNodeUUID(const TSharedPtr<FMultiComponentDataItem> &InRootNode, const FString& InNode);

	static void CollectNodesToDisableCollision(const TSharedPtr<FMultiComponentDataItem>& InNode, bool bParentDisabled, TArray<FString>& OutNodes);

	static UDSBaseModel* FindNearestFunctionalCupboardModel(UDSBaseModel* InModel);
	
	//获得所有子节点，包括自己
	static void GetAllNodes(const TSharedPtr<FMultiComponentDataItem>& InRootNode, TArray<TSharedPtr<FMultiComponentDataItem>>& OutNodes);

	static FVector GetFixedSizeParameter(const UDSCupboardModel* InModel);

	static FVector GetFixedSizeParameter(const TSharedPtr<FMultiComponentDataItem> InNode);
	
	static FVector GetFixedOffsetParameter(const UDSCupboardModel* InModel);

	static FVector GetFixedOffsetParameter(const TSharedPtr<FMultiComponentDataItem> InNode);

	static bool IsDrawerOpening(UDSCupboardModel* InModel);

    static bool RecursiveGetTreeNodePath(const TSharedPtr<FMultiComponentDataItem>& RootNode, const TSharedPtr<FMultiComponentDataItem>& TargetNode, TArray<TPair<FString, TWeakPtr<FMultiComponentDataItem>>>& OutPath);

	static bool IsDoorBoardType(int32 ModelType);

	/**
	* Toggles the open/close state of all functional drawer models related to the specified model.
	* 
	* @return TOptional<bool> Returns true if drawers are opened, false if closed, or unset if no drawers were found or toggled.
	*/
	static TOptional<bool> ToggleDrawerState(UDSBaseModel* InModel);

	static bool ShouldNodeHideInGame(const TSharedPtr<FMultiComponentDataItem>& RootNode, const TSharedPtr<FMultiComponentDataItem>& TargetNode);
	
	static FTransform GetNodeTransform(UDSCupboardModel* InRootModel, const TSharedPtr<FMultiComponentDataItem>& InTargetNode);

	static bool CupboardIntersect(const TArray<FVector>& CubeVerticesA, const TArray<FVector>& CubeVerticesB);
	
	static FTransform GetNodeTransform(UDSCupboardModel* InRootModel, const TSharedPtr<FMultiComponentDataItem>& InFromNode, const TSharedPtr<FMultiComponentDataItem>& InToNode);

};
