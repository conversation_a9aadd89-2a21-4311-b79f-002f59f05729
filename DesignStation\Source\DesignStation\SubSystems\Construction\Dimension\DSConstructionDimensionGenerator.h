// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "DSConstructionDimensionGeneratorBase.h"
#include <SubSystems/DSCore.h>

struct FDSConstructionData;

class DESIGNSTATION_API DSConstructionDimensionGenerator:public FDSConstructionDimensionGeneratorBase
{
public:
	DSConstructionDimensionGenerator();
	virtual ~DSConstructionDimensionGenerator();

public:
	 void GenerateDimension(const TArray<FVector>& InInternalOutLine ,const TMap<FString, TSharedPtr<FDSConstructionData>> InConstructDatas);
	 virtual  void GenerateDimension(const TArray<TSharedPtr<FDSConstructionData>>& InConstructDatas);

	 TArray<TArray<TPair<FVector, FVector>>> GetLeftProjectionLineGroups() const { return LeftProjectionLineGroups; }
	 TArray<TArray<TPair<FVector, FVector>>> GetUpProjectionLineGroups() const { return UpProjectionLineGroups; }
	 TArray<TArray<TPair<FVector, FVector>>> GetRightProjectionLineGroups() const { return RightProjectionLineGroups; }
	 TArray<TArray<TPair<FVector, FVector>>> GetDownProjectionLineGroups() const { return DownProjectionLineGroups; }

	 virtual void DrawDimension(const FVector& PaperOffset) override;

private:
	void GetGlobalBox(const TMap<FString, TSharedPtr<FDSConstructionData>>& InConstructDatas);
	void GetGlobalBox(const  TArray<TSharedPtr<FDSConstructionData>>& InConstructDatas);


private:
	FBox2D GlobalAABB;

	TSharedPtr<FDSAxisDimensionData> HorizontalDimensionSets;
	TSharedPtr<FDSAxisDimensionData> VerticalDimensionSets;

	TArray<TArray<TPair<FVector, FVector>>> LeftProjectionLineGroups;
	TArray<TArray<TPair<FVector, FVector>>> UpProjectionLineGroups;
	TArray<TArray<TPair<FVector, FVector>>> RightProjectionLineGroups;
	TArray<TArray<TPair<FVector, FVector>>> DownProjectionLineGroups;

	TArray<FVector> InternalOutLine;

};
