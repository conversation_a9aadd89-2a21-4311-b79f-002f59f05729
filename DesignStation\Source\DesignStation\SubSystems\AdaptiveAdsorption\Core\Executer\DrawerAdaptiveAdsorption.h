// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "DynamicMeshAdaptiveAddorption.h"


DECLARE_LOG_CATEGORY_EXTERN(DrawerAdaptiveAdsorptionLog, Log, All);

class FDrawerAdaptationOperator;
class FModelAdaptationOperatorBase;

//抽屉环境依赖的关系
enum EDrawerAdaptiveRelation
{
	DAR_NONE				= -1,								//空
	DAR_Normal				= 0,								//正常接触
	DAR_Hange				= 1,								//悬空	
	DAR_Embedded			= 1 << 1,							//嵌入
	DAR_Embedded_Hange		= DAR_Embedded | DAR_Hange,			//悬空嵌入
};

class DESIGNSTATION_API FDrawerAdaptiveAdsorption : public FDynamicMeshAdaptiveAdsorption
{
public:
	//virtual void ApplayFixedExtentsOnInitialize() override {}

	virtual void HandleAdaptive(const TArray<TSharedPtr<FIntersectionDynamicMesh>>& Env, const TSharedPtr<FIntersectionDynamicMesh>& HitEnv, FOrientedBox3d& OutExecutedBox) override;

	virtual void GenerateRay(FVector& RayStart, FVector& RayDir) override; 

	virtual FAdaptationEvenInfo ExecuteEven(const TArray<TSharedPtr<FIntersectionDynamicMesh>>& Env) override;

	virtual FOrientedBox3d GetAdaptationOriBoxWithoutAdaptiveRulerOffset() override;

	virtual bool HandleAdaptiveAndAdsorptionWithDependent(const TArray<TSharedPtr<FIntersectionDynamicMesh>>& AdaptationEnvs, const TSharedPtr<struct FFunctionalDependencyInfo>& SelfDependencyInfo) override;

	EDrawerAdaptiveRelation GetXNegRelation() const { return XNegRelation; }
	EDrawerAdaptiveRelation GetXPosRelation() const { return XPosRelation; }
    EDrawerAdaptiveRelation GetYNegRelation() const { return YNegRelation; }
    EDrawerAdaptiveRelation GetYPosRelation() const { return YPosRelation; }
    EDrawerAdaptiveRelation GetZNegRelation() const { return ZNegRelation; }
    EDrawerAdaptiveRelation GetZPosRelation() const { return ZPosRelation; }

	void SetDrawerOperator(const TSharedPtr<FModelAdaptationOperatorBase>& InDrawerOperator);

	//是否外盖改内嵌
	bool IsDrawerNoOriginToEmbedded(const EAdaptationDirection& InDirection) const;

protected:

	/**
	 *  @@ -Y取空间边界，+Y取实际大小
	 */
	virtual void PreHandleAdaptiveOriBox(const TArray<TSharedPtr<FIntersectionDynamicMesh>>& Env, const FRayHitResault& RayHitResault, FOrientedBox3d& OutOriBox) override;

	/**
	 *  @@ 获取自适应空间大小
	 */
	virtual void SelfSpaceAdaptive(FOrientedBox3d& OutOriBox);

	/*
	*  确定基本吸附对象, 偏移原始OBB的位置
	*/
	virtual void CalculateAdsorptionPoint_X(FVector& OutAdsorptionPoint, const FRayHitResault& HitResault, const FVector& InUseExtent,
		const TArray<TSharedPtr<FIntersectionDynamicMesh>>& Env);
	virtual void CalculateAdsorptionPoint_Y(FVector& OutAdsorptionPoint, const FRayHitResault& HitResault, const FVector& InUseExtent,
		const TArray<TSharedPtr<FIntersectionDynamicMesh>>& Env);
	virtual void CalculateAdsorptionPoint_Z(FVector& OutAdsorptionPoint, const FRayHitResault& HitResault, const FVector& InUseExtent,
		const TArray<TSharedPtr<FIntersectionDynamicMesh>>& Env);


	virtual void SetYNegativeDependence(const TSharedPtr<FIntersectionDynamicMesh>& InTarget) override;
	virtual void SetYPositiveDependence(const TSharedPtr<FIntersectionDynamicMesh>& InTarget) override;

	void SyncRealDependence(const TSharedPtr<FIntersectionDynamicMesh>& InEnv, TWeakPtr<FIntersectionDynamicMesh>& ToSyncEnv);

	virtual void SetRealXNegativeDependence(const TSharedPtr<FIntersectionDynamicMesh>& InTarget) override;
	virtual void SetRealXPositiveDependence(const TSharedPtr<FIntersectionDynamicMesh>& InTarget) override;
	virtual void SetRealZNegativeDependence(const TSharedPtr<FIntersectionDynamicMesh>& InTarget) override;
	virtual void SetRealZPositiveDependence(const TSharedPtr<FIntersectionDynamicMesh>& InTarget) override;

protected:
	TWeakPtr<FIntersectionDynamicMesh> YNegativeAdsorption = nullptr;
    TWeakPtr<FIntersectionDynamicMesh> YPositiveAdsorption = nullptr;

	TWeakPtr<FIntersectionDynamicMesh> RealXNegativeAdsorption = nullptr;
	TWeakPtr<FIntersectionDynamicMesh> RealXPositiveAdsorption = nullptr;
	TWeakPtr<FIntersectionDynamicMesh> RealZNegativeAdsorption = nullptr;
	TWeakPtr<FIntersectionDynamicMesh> RealZPositiveAdsorption = nullptr;

	//是否悬空, 缩进 ---- 用于盖值自适应
	EDrawerAdaptiveRelation XNegRelation = EDrawerAdaptiveRelation::DAR_NONE;
	EDrawerAdaptiveRelation XPosRelation = EDrawerAdaptiveRelation::DAR_NONE;
	EDrawerAdaptiveRelation YNegRelation = EDrawerAdaptiveRelation::DAR_NONE;
	EDrawerAdaptiveRelation YPosRelation = EDrawerAdaptiveRelation::DAR_NONE;
	EDrawerAdaptiveRelation ZNegRelation = EDrawerAdaptiveRelation::DAR_NONE;
	EDrawerAdaptiveRelation ZPosRelation = EDrawerAdaptiveRelation::DAR_NONE;

	TWeakPtr<FDrawerAdaptationOperator> BelongOperator = nullptr;
};
