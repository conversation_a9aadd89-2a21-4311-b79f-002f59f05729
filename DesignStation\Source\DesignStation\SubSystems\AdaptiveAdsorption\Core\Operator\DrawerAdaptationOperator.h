// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "FunctionalAdaptationOperator.h"
#include "Decimal.h"
#include "SubSystems/MVC/Model/Core/DSModelExecuteType.h"
#include "SubSystems/MVC/Coroutine/DSVoidCoroutine.h"
#include "SubSystems/AdaptiveAdsorption/Core/Executer/DrawerAdaptiveAdsorption.h"

struct FParameterData;

class DESIGNSTATION_API FDrawerAdaptationOperator :public FFunctionalAdaptationOperator
{
public:
	//FDrawerAdaptationOperator();
	FDrawerAdaptationOperator(UDSBaseModel* InModel);

	virtual ~FDrawerAdaptationOperator() {};

public:
	virtual void GenerateAdaptationSourceInfo() override;

	virtual bool PrepareAdaptation(UDSBaseModel* InSourceModel) override;

	virtual void PrepareAdaptation(UDSBaseModel* InSourceModel, UDSBaseModel* InTargetModel);

	virtual void GeneratorTargetModelIntersectionEnv(UDSBaseModel* InTargetMode, const FTransform& RootTrans, TArray<TSharedPtr<FIntersectionDynamicMesh>>& OutEnv,
		const TSharedPtr<FMultiComponentDataItem>& InComponentTreeData, const TSharedPtr<FMultiComponentDataItem>& IgnoreModelTreeData,
		const TSharedPtr<struct  FFunctionalDependencyInfo>& InDependencyInfo);

	void GeneratorTargetModelIntersectionEnvConsiderOffset(UDSBaseModel* InTargetMode, const FTransform& RootTrans, 
		TArray<TSharedPtr<FIntersectionDynamicMesh>>& OutEnv, TArray<TSharedPtr<FIntersectionDynamicMesh>>& OutAutoEnv,
		const TSharedPtr<FMultiComponentDataItem>& InComponentTreeData, const TSharedPtr<FMultiComponentDataItem>& IgnoreModelTreeData,
		const TSharedPtr<struct  FFunctionalDependencyInfo>& InDependencyInfo);

	virtual void ExecuteStepAdaptation(const TSharedPtr<FDSBroadcastMarkData>& InBroadcastMarkPtr = FDSBroadcastMarkData::BroadcastToMVCMark) override;


	FDSVoidCoroutine DrawerEven(const TArray<FDecimal> InEvenHeight, const FDecimal InUpExtent = 0, const FDecimal InDownExtent = 0);

	virtual bool GetIntersectionHitPoint(FVector& Start, FVector& End, const FVector& Dir) const override;

	virtual void UpdateInitializedData() override;

	void GenerateSpaceInfo();

	//virtual void CompleteAdaptation(FAdaptiveAdsorptionResault& OutResault, bool bRecalculateDependentedNode = true) override; 

	void GenerateInitializedData_Drawer(const TSharedPtr<FAdaptationExecuterInitializedData>& InitialziedData);

	virtual void GenerateAdaptationEnvData() override;
	virtual void GenerateAdditionalAdaptationEnvData() ;

	virtual void GenerateAdapationEnvDataWithoutDenpendented() override;

	virtual void OnAdaptiveAndAdSorptionCallback(const TSharedPtr<FAdaptationData>& AdaptationData) override;

	virtual void ShowDebug(UObject* WorldContextObject) override;

	virtual FAdaptationEvenInfo GetRealAdaptationEvenInfo();

	virtual TArray<TSharedPtr<FIntersectionDynamicMesh>> GetRealEnvironments() override { return ApplyOffsetAdaptationEnvs; }

protected:
	/*
	 * @@ 获取基本参数
	 * @@ RetVal : true 获取成功
	 */
	virtual bool GenerateBaseInfo(const TMap<FString, FParameterData>& ParamsInfo, TMap<FString, FParameterData>& BaseParamsMap, FVector& DefaultExtent, FVector& MaxExtents, FVector& MinExtents);

	/*
	 * @@ 是否生成关于覆盖的参数
	 * @@ RetVal : true 表示需要考虑生成覆盖相关数据
	 */
	virtual bool NeedGenerateSpaceFGInfo(const TMap<FString, FParameterData>& ParamsInfo);
	
	/*
	 * @@ 获取上下左右覆盖的参数信息
	 * @@ 确保内嵌抽屉的覆盖方式全部为内嵌
	 * @@ RetVal : true 表示参数自更新，抽屉需更新
	 */
	virtual bool GenerateAndEnsureSpaceFGInfo(const TSharedPtr<FMultiComponentDataItem>& ModelInfo, TMap<FString, FParameterData>& ParamsInfo,
		FVector& DefaultExtent, double& UpExtent, double& DownExtent, double& LeftExtent, double& RightExtent);

    /*
	 * @@ 刷新自适应尺寸、覆盖信息
	 */
	virtual void ParseAdaptiveSizeInfo(const TSharedPtr<FAdaptationData>& AdaptationData);

	virtual void ParseAdaptiveTransformInfo(const TSharedPtr<FAdaptationData>& AdaptationData);

	/*
	*  @@ 获取特殊宽高深和偏移
	*  @@ RetVal : true --- 表示有特殊宽高深和偏移参数
	*/
    virtual bool GetSpecialSizeAndOffset(const TMap<FString, FParameterData>& ParamsMap, FVector& ModifySize, FVector& ModifyOffset);

	/**
	 * @@ 获取固定宽高深和偏移
	 */
    virtual bool GetFixedSizeAndOffset(const TMap<FString, FParameterData>& ParamsMap, FVector& ModifySize, FVector& ModifyOffset);
	
	/**
	 * @@ 获取普通宽高深和偏移
	 */
    virtual void GetDefaultSize(const TMap<FString, FParameterData>& ParamsMap, FVector& ModifySize);

	/**
	 * @@ 获取环境的内部参数数据
	 */
	virtual FString GetEnvInnerData(const TWeakPtr<FIntersectionDynamicMesh>& EnvironmentInfo, const FString& ParamName);

protected:
	/*
	*  刷新覆盖信息，调节自适应尺寸
	*/
    virtual void ParseSpaceFGInfo(TSharedPtr<FMultiComponentDataItem>& TreeInfo, FVector& ModifySize, FTransform& ModifyTrans);


	/**
	 *  RetVal : 表示真实依赖类型， 当不为抽屉时需考虑尺寸
	 */
	virtual EIntersectionDataType ParseSpaceFGInfoByDir(
		const TWeakPtr<FIntersectionDynamicMesh>& Env, const TWeakPtr<FIntersectionDynamicMesh>& RealEnv,
		const FString& DirectionMark, const EAdaptationDirection& Direction, double& Extent, FTransform& ModifyTrans, bool& IsNoSameEnv, bool IsEmbedded);


	virtual EIntersectionDataType ParseSpaceFGInfoByDir(
		const TWeakPtr<FIntersectionDynamicMesh>& Env,
		const FString& DirectionMark, const EAdaptationDirection& Direction, 
		double& Extent, FTransform& ModifyTrans, bool IsEmbedded);

	virtual EIntersectionDataType GetSpaceFGInfoByDir(
		const TWeakPtr<FIntersectionDynamicMesh>& Env, const FString& DirectionMark, const FString& DirectionFGMark, const EAdaptationDirection& Direction,
		bool IsEmbedded, const EDrawerAdaptiveRelation& CurEnvRelation, double& Extent, int32& FirstFGValue, bool& bApplyFirstFG);

	/**
	 *  根据位置和尺寸获取OBB
	 *  Intrans : 轮廓左下后
	 *  OutSize : 轮廓宽深高（cm）
	 */
	virtual UE::Geometry::FOrientedBox3d GetOBBByTransAndSize(const FTransform& InTrans, const FVector& InSize);

	/**
	 *  覆盖自动避让
	 */
	virtual void AutoFGTypeAdjustment(TSharedPtr<FMultiComponentDataItem>& TreeInfo, const UE::Geometry::FOrientedBox3d& DetectOBB, const UE::Geometry::FOrientedBox3d& DetectSpaceOBB);

	/*
	* @@ 获取Transform
	* @@ RetVal : true 表示位置有变化
	*/
	virtual bool GetAdaptationTransform(const TSharedPtr<FAdaptationData>& AdaptationData, FTransform& OutTrans);

	virtual FString GetValueFromTree(const TSharedPtr<FMultiComponentDataItem>& TreeInfo, const FString& ParamName);

	virtual bool GetFGNameByDirection(const EAdaptationDirection& InDirection, FString& OutDetectName, FString& OutOppositeName);

	virtual int32 GetOppositeFG(int32 CurFG);

	//尺寸弥补mm
    virtual double CompensateSize(const TWeakPtr<FIntersectionDynamicMesh>& AdaptationEnv, const EAdaptationDirection& AdaptationDir);


	FString InsertRevokeData(UDSBaseModel* InModel, FDSModelExecuteType InExecuteType, const FString& InUUID = TEXT(""));

private:
	/**
	 *  OBB在轴上投影
	 */
	double OBBProjectionRadiusToAxis(const UE::Geometry::FOrientedBox3d& InOBB, const FVector3d& InAxis);

	/**
	 *  简单的分离轴SAT算法
	 *  DetectOBB : 检测的OBB
	 *  ConsiderOBB : 需要考虑的OBB
	 *  RetVal : true 存在重叠
	 */
	bool IsOBBOverlap(const UE::Geometry::FOrientedBox3d& InOBB1, const UE::Geometry::FOrientedBox3d& InSpaceOBB1, const UE::Geometry::FOrientedBox3d& InOBB2,
		const TMap<EAdaptationDirection, double>& DirectionBoundaryMap, TMap<EAdaptationDirection, bool>& DirectionOverlapMap);

	/**
	 *  检测6方向上的重叠
	 *  Y方向{EAdaptationDirection::left / EAdaptationDirection::right}表示深度是否分离
	 *  Z方向{EAdaptationDirection::up / EAdaptationDirection::down}表示高度是否分离
	 *  X方向{EAdaptationDirection::front / EAdaptationDirection::back}表示宽度是否分离
	 *  RetVal : true 存在重叠
	 */
	bool IsOBBDirectOverlap(const FVector3d& CenterLine, const FVector3d& DetectDir, const double& DetectExtent, const UE::Geometry::FOrientedBox3d& ConsiderOBB);

	bool IsOBBBoundaryIntersect(
		const UE::Geometry::FOrientedBox3d& DetectOBB, 
		const UE::Geometry::FOrientedBox3d& DetectFullOBB, 
		bool YUseFullOBB,
		const FBox& ConsiderBox,
		const TMap<EAdaptationDirection, double>& DirectionBoundaryMap, 
		TMap<EAdaptationDirection, bool>& DirectionOverlapMap);

	bool IsOBBCornerIntersect(
		const UE::Geometry::FOrientedBox3d& DetectOBB,
		const UE::Geometry::FOrientedBox3d& DetectFullOBB,
		bool YUseFullOBB,
		const FBox& ConsiderBox,
		const TMap<EAdaptationDirection, double>& DirectionBoundaryMap,
		TMap<EAdaptationDirection, bool>& DirectionOverlapMap);

	/*
	*  获取边界处的包围盒FBox
	*/
	bool GetBoundaryBox(const UE::Geometry::FOrientedBox3d& DetectOBB, const EAdaptationDirection& Direction, const TMap<EAdaptationDirection, double>& BoundaryBoxSize, FBox& OutBox);
	
	/**
	 *  0 : 左下角； 1 : 左上角； 2 : 右上角； 3 : 右下角
	 */
	bool GetCornerBox(const UE::Geometry::FOrientedBox3d& DetectOBB, const int32& Direction, const TMap<EAdaptationDirection, double>& BoundaryBoxSize, FBox& OutBox);
	
	bool IsOBBDirectIntersect(const FVector& SPoint, const FVector& EPoint, const FVector& Direction, const FBox& ConsiderBox);

	/**
	 *  中间态，只计算顶层参数表中参数
	 */
	bool GetMiddleFixedSizeAndOffset(TMap<FString, FParameterData> LevelParamsMap, FVector& ModifySize, FVector& ModifyOffset);

protected:
	//应用偏移的环境(实际大小)
	TArray<TSharedPtr<FIntersectionDynamicMesh>> ApplyOffsetAdaptationEnvs;

	//避让环境、只考虑门抽
	TArray<TSharedPtr<FIntersectionDynamicMesh>> DoorDrawerAdaptationEnvs;

};
