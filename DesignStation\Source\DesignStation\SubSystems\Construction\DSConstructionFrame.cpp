// Fill out your copyright notice in the Description page of Project Settings.


#include "SubSystems/Construction/DSConstructionFrame.h"
#include "DSConstructionCore.h"


//const FName UDSConstructionFrame::Frame_PlaneBaseCabinet         = FName(TEXT("平面地柜图框"));       //平面地柜图框
//const FName UDSConstructionFrame::Frame_PlaneWallCabinet         = FName(TEXT("平面吊柜图框"));       //平面吊柜图框
//const FName UDSConstructionFrame::Frame_FrontConstructionCabinet = FName(TEXT("立面柜体结构图框"));   //立面柜体结构图框
//const FName UDSConstructionFrame::Frame_FrontDoorCabinet         = FName(TEXT("立面门板图框"));       //立面门板图框

const FName UDSConstructionFrame::Frame_Default = FName(TEXT("默认图框"));       //平面地柜图框

//const TSet<FName> UDSConstructionFrame::AllFrameNames = {
//	UDSConstructionFrame::Frame_PlaneBaseCabinet,
//	UDSConstructionFrame::Frame_PlaneWallCabinet,
//	UDSConstructionFrame::Frame_FrontConstructionCabinet,
//	UDSConstructionFrame::Frame_FrontDoorCabinet
//};

using namespace DSFrameAttDefName;

const TArray<FString> UDSConstructionFrame::DefaultFrameKeys = {
	Name_DRAWING_NAME  //图纸名称
	,Name_room_name    //区域名称
	,Name_style_name   //风格名称
	,Name_USERNAME
	,Name_USERPHONE
	,Name_DESIGNERNAME  //设计师名称
	,Name_DESIGNERPHONE  //设计师联系方式
	,Name_CITYNAMEBUILDNAMEANDMODELNAME
	,Name_most_door_board_material_name      //门板颜色
	,Name_most_door_board_material_name2
	,Name_most_door_board_material_name3
	,Name_most_door_board_name               //门型
	,Name_most_glass_core_name               //玻璃花色
	,Name_most_knob_name                     //拉手型号
	,Name_most_knob_name2
	,Name_most_cabinet_board_material_name   //柜体颜色
	,Name_most_cabinet_board_material_name2
	,Name_most_cabinet_board_material_name3
	,Name_most_hinge_name                   //铰链品牌
	,Name_most_down_rail_name               //轨道品牌
	,Name_table_surface_mateiralvo          //台面花色
	,Name_front_section_name1               //前挡水
	,Name_back_section_name1                //后挡水
};

UDSConstructionFrame::UDSConstructionFrame()
{
}

//void UDSConstructionFrame::InitAttDefs(const TSet<FName>& InAttDefKeys)
//{
//
//}
