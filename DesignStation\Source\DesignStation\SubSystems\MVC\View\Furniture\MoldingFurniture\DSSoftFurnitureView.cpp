#pragma once

#include "DSSoftFurnitureView.h"

#include "BasicClasses/ImportPakBaseClass.h"
#include "Components/StaticMeshComponent.h"
#include "Subsystems/MVC/View/Gizmo/DSGizmoView.h"
#include "Subsystems/MVC/View/House/Area/DSHouseAreaView.h"
#include "Subsystems/MVC/View/House/Area/DSHouseAreaLabelView.h"

#include "SubSystems/MVC/Library/DSToolLibrary.h"
#include "SubSystems/MVC/Model/Furniture/MoldingFurniture/DSSoftFurnitureModel.h"
#include "SubSystems/Library/DesignStationFunctionLibrary.h"
#include "SubSystems/Resource/DSResourceSubsystem.h"


void ADSSoftFurnitureView::Init()
{
	Super::Init();
}

AImportPakBaseClass* ADSSoftFurnitureView::SpawnChildActorFromProperty()
{
	AImportPakBaseClass* ResultActor = nullptr;
	TSharedPtr<FDSFurnitureBaseProperty> Property = StaticCastSharedPtr<FDSFurnitureBaseProperty>(Model->GetPropertySharedPtr());
	if (Property == nullptr)
	{
		return nullptr;
	}

	UClass* ViewClass = LoadClass<AImportPakBaseClass>(this, *Property->FileSourceProperty.SourcePath);
	if (ViewClass == nullptr)
	{
		return ResultActor;
	}

	//ResultActor = GetWorld()->SpawnActorDeferred<AImportPakBaseClass>(ViewClass, Property->TransformProperty.ToUETransform());
	ResultActor = GetWorld()->SpawnActorDeferred<AImportPakBaseClass>(ViewClass,FTransform());

	if (ResultActor == nullptr)
	{
		return ResultActor;
	}

	ResultActor->Tags.Add(TEXT("Cmp"));
	ResultActor->SetActorEnableCollision(true);
	ResultActor->ForEachComponent(true, [](UActorComponent* InComp) 
	{
		if (UStaticMeshComponent* StaticMeshComponent = Cast<UStaticMeshComponent>(InComp))
		{
			StaticMeshComponent->SetCollisionResponseToAllChannels(ECR_Ignore);
			StaticMeshComponent->SetCollisionResponseToChannel(ECC_Visibility, ECR_Overlap);
		}
	});

	ResultActor->FinishSpawning(FTransform());
	ResultActor->SetOwner(this);

	/*ResultActor->OnActorBeginOverlap.AddDynamic(this, &ThisClass::OnAttachedMeshBeginOverlap);
	ResultActor->OnActorEndOverlap.AddDynamic(this, &ThisClass::OnAttachedMeshEndOverlap);*/

	return ResultActor;
}

void ADSSoftFurnitureView::ChangePostProcessOutlineSettings(bool bEnable, int32 DepthStencil)
{
	TArray<UPrimitiveComponent*> AllComponents;
	GetComponents<UPrimitiveComponent>(AllComponents, true);

	if (AttachedMesh != nullptr)
	{
		AttachedMesh->GetComponents<UPrimitiveComponent>(AllComponents, true);
	}

	for (UPrimitiveComponent* Component : AllComponents)
	{
		Component->SetRenderCustomDepth(bEnable);
		Component->SetCustomDepthStencilValue(DepthStencil);
		Component->SetCustomDepthStencilWriteMask(ERendererStencilMask::ERSM_Default);
	}
}

void ADSSoftFurnitureView::OnAttachedMeshBeginOverlap(AActor* OverlappedActor, AActor* OtherActor)
{
	if (OverlappedActor == nullptr || OtherActor == nullptr)
	{
		return;
	}

	if (OtherActor->IsA<AImportPakBaseClass>())
	{
		UE_LOG(LogTemp, Log, TEXT("Overlapped import pak base class."));
	}

	AActor* ActualOtherActor = OtherActor;
	while (ActualOtherActor != nullptr && ActualOtherActor->ActorHasTag(TEXT("Cmp")))
	{
		ActualOtherActor = ActualOtherActor->GetOwner();
	}

	ADSBaseView* OtherView = Cast<ADSBaseView>(ActualOtherActor);
	if (OtherView &&  !OtherView->IsA<ADSGizmoView>() && !OtherView->IsA<ADSHouseAreaView>())
	{
		if (Model != nullptr)
		{
			Model->OnExecuteAction(FDSModelExecuteType::ExecuteOverlap);
		}
	}
}

void ADSSoftFurnitureView::OnAttachedMeshEndOverlap(AActor* OverlappedActor, AActor* OtherActor)
{
	if (OtherActor->IsA<ADSGizmoView>() || OtherActor->IsA<ADSHouseAreaView>() || OtherActor->IsA<ADSHouseAreaLabelView>())
	{
		return;
	}

	TSet<AActor*> OverlappingActors;
	if (AttachedMesh != nullptr)
	{
		AttachedMesh->GetOverlappingActors(OverlappingActors);
	}
	
	bool bShouldEndOverlap = true;
	for (AActor* OverlappingActor : OverlappingActors)
	{
		AActor* ActualActor = OverlappingActor;
		while (ActualActor != nullptr && ActualActor->ActorHasTag(TEXT("Cmp")))
		{
			ActualActor = ActualActor->GetOwner();
		}
		
		if (ActualActor != nullptr && (ActualActor->IsA<ADSGizmoView>() || ActualActor->IsA<ADSHouseAreaView>()) || ActualActor->IsA<ADSHouseAreaLabelView>())
		{
			continue;
		}

		if (ActualActor != nullptr && ActualActor->IsA<ADSBaseView>())
		{
			bShouldEndOverlap = false;
			break;
		}
	}

	if (bShouldEndOverlap && Model != nullptr)
	{
		Model->OnExecuteAction(FDSModelExecuteType::ExecuteUnOverlap);
	}
}

void ADSSoftFurnitureView::RealSpawnViewLogic(UDSBaseModel* InModel)
{
	Super::RealSpawnViewLogic(InModel);

	MeshComponent->SetCollisionResponseToAllChannels(ECR_Ignore);
	MeshComponent->SetCollisionResponseToChannel(ECC_Visibility, ECR_Overlap);

	RealUpdateViewLogic(InModel);
}

void ADSSoftFurnitureView::RealUpdateViewLogic(UDSBaseModel* InModel)
{
	UDSSoftFurnitureModel* NewModel = Cast<UDSSoftFurnitureModel>(InModel);
	if (NewModel == nullptr || Model == nullptr)
	{
		return;
	}

	checkf(Model->GetUUID().Equals(NewModel->GetUUID()), TEXT("ADSSoftFurnitureView::UpdateView --- No Equal Model"));

	Model->ShallowCopy(NewModel);

	FDSSoftFurnitureProperty* Property = static_cast<FDSSoftFurnitureProperty*>(Model->GetProperty());
	if (Property == nullptr)
	{
		return;
	}

	if (AttachedMesh != nullptr)
	{
		AttachedMesh->DetachFromActor(FDetachmentTransformRules::KeepWorldTransform);
		AttachedMesh->Destroy();
		AttachedMesh = nullptr;
	}

	MeshComponent->ClearAllMeshSections();

	if (Property->FileSourceProperty.SourcePath.IsEmpty())
	{
		UDesignStationFunctionLibrary::CreateLazyLoadBoxMesh(MeshComponent, StaticCastSharedPtr<FDSFurnitureBaseProperty>(Model->GetPropertySharedPtr()));
	}
	else
	{
		AttachedMesh = SpawnChildActorFromProperty();
		if (AttachedMesh != nullptr)
		{
			AttachedMesh->AttachToActor(this, FAttachmentTransformRules::KeepWorldTransform);
			AttachedMesh->SetActorRelativeTransform(FTransform());
			AttachedMesh->RegisterAllComponents();
		}

		BpView = AttachedMesh;

		RealRefreshViewMaterialLogic(InModel);

		RealGenerateViewMeshLogic(InModel);

		RealTransformViewLogic(InModel);
	}
}

void ADSSoftFurnitureView::RealHiddenViewLogic(UDSBaseModel* InModel)
{
	Super::RealHiddenViewLogic(InModel);

	if (AttachedMesh)
	{
		AttachedMesh->SetActorHiddenInGame(true);
	}
}

void ADSSoftFurnitureView::RealUnHiddenViewLogic(UDSBaseModel* InModel)
{
	Super::RealUnHiddenViewLogic(InModel);

	if (AttachedMesh)
	{
		AttachedMesh->SetActorHiddenInGame(false);
	}
}

void ADSSoftFurnitureView::RealHoverViewLogic(UDSBaseModel* InModel)
{
	Super::RealHoverViewLogic(InModel);

	ChangePostProcessOutlineSettings(true, 10);
}

void ADSSoftFurnitureView::RealUnHoverViewLogic(UDSBaseModel* InModel)
{
	Super::RealUnHoverViewLogic(InModel);

	ChangePostProcessOutlineSettings(false, 0);
}

void ADSSoftFurnitureView::RealSelectViewLogic(UDSBaseModel* InModel)
{
	Super::RealSelectViewLogic(InModel);

	ChangePostProcessOutlineSettings(true, 1);
}

void ADSSoftFurnitureView::RealUnSelectViewLogic(UDSBaseModel* InModel)
{
	Super::RealUnSelectViewLogic(InModel);

	ChangePostProcessOutlineSettings(false, 0);
}

void ADSSoftFurnitureView::RealDisableViewLogic(UDSBaseModel* InModel)
{
	Super::RealDisableViewLogic(InModel);

	TArray<USceneComponent*> AllChildrenComponents;
	RootComponent->GetChildrenComponents(true, AllChildrenComponents);
	for (USceneComponent* It : AllChildrenComponents)
	{
		if (UPrimitiveComponent* PrimComponent = Cast<UPrimitiveComponent>(It))
		{
			PrimComponent->SetRenderCustomDepth(true);
			PrimComponent->SetCustomDepthStencilValue(7);
			PrimComponent->SetCustomDepthStencilWriteMask(ERendererStencilMask::ERSM_Default);
		}
	}
}

void ADSSoftFurnitureView::RealEnableViewLogic(UDSBaseModel* InModel)
{
	Super::RealEnableViewLogic(InModel);

	if (InModel == nullptr)
	{
		return;
	}

	if (InModel->IsHasModelFlag(EModelState::E_Selected))
	{
		RealSelectViewLogic(InModel);
	}
	else if (InModel->IsHasModelFlag(EModelState::E_Hovered))
	{
		RealHoverViewLogic(InModel);
	}
	else
	{
		TArray<USceneComponent*> AllChildrenComponents;
		RootComponent->GetChildrenComponents(true, AllChildrenComponents);
		for (USceneComponent* It : AllChildrenComponents)
		{
			if (UPrimitiveComponent* PrimComponent = Cast<UPrimitiveComponent>(It))
			{
				PrimComponent->SetRenderCustomDepth(false);
			}
		}
	}
}

void ADSSoftFurnitureView::RealOverlapViewLogic(UDSBaseModel* InModel)
{
	TArray<USceneComponent*> AllChildrenComponents;
	RootComponent->GetChildrenComponents(true, AllChildrenComponents);
	for (USceneComponent* It : AllChildrenComponents)
	{
		if (UPrimitiveComponent* PrimComponent = Cast<UPrimitiveComponent>(It))
		{
			PrimComponent->SetRenderCustomDepth(true);
			PrimComponent->SetCustomDepthStencilValue(4);
			PrimComponent->SetCustomDepthStencilWriteMask(ERendererStencilMask::ERSM_Default);
		}
	}
}

void ADSSoftFurnitureView::RealUnOverlapViewLogic(UDSBaseModel* InModel)
{
	TArray<USceneComponent*> AllChildrenComponents;
	RootComponent->GetChildrenComponents(true, AllChildrenComponents);
	for (USceneComponent* It : AllChildrenComponents)
	{
		if (UPrimitiveComponent* PrimComponent = Cast<UPrimitiveComponent>(It))
		{
			PrimComponent->SetRenderCustomDepth(false);
			PrimComponent->SetCustomDepthStencilValue(0);
			PrimComponent->SetCustomDepthStencilWriteMask(ERendererStencilMask::ERSM_Default);
		}
	}
}

void ADSSoftFurnitureView::RealGenerateViewMeshLogic(UDSBaseModel* InModel)
{
	if (InModel == nullptr || Model == nullptr)
	{
		return;
	}

	checkf(Model->GetUUID().Equals(InModel->GetUUID()), TEXT("ADSSoftFurnitureView::UpdateView --- No Equal Model"));

	Model->ShallowCopy(InModel);

	if (AttachedMesh == nullptr)
	{
		return;
	}

	TSharedPtr<FDSSoftFurnitureProperty> Property = StaticCastSharedPtr<FDSSoftFurnitureProperty>(Model->GetPropertySharedPtr());
	if (!Property)
	{
		return;
	}

	FVector NewSize(Property->SizeProperty.Width, Property->SizeProperty.Depth, Property->SizeProperty.Height);

	FVector OriginSize;
	OriginSize.X = FCString::Atoi(*Property->BusinessInfo.Width);
	OriginSize.Y = FCString::Atoi(*Property->BusinessInfo.Depth);
	OriginSize.Z = FCString::Atoi(*Property->BusinessInfo.Height);

	FVector NewScale = NewSize / OriginSize;
	FTransform ViewTransform = Property->GetActualTransformNoScale();

	FTransform MeshTransform = AttachedMesh->GetTransform();
	MeshTransform.SetToRelativeTransform(ViewTransform);

	ViewTransform.SetScale3D(NewScale);
	auto MeshScale = FVector::OneVector;

	if (Property->TransformProperty.IsDownRot)
	{
		MeshScale.Y = -1;
	}
	else
	{
		MeshScale.Y = 1;
	}

	if (Property->TransformProperty.IsLeftRot)
	{
		MeshScale.X = -1;
	}
	else
	{
		MeshScale.X = 1;
	}

	MeshTransform.SetScale3D(MeshScale);

	AttachedMesh->SetActorScale3D((MeshTransform * ViewTransform).GetScale3D());
}

void ADSSoftFurnitureView::RealRefreshViewMaterialLogic(UDSBaseModel* InModel)
{
	if (InModel == nullptr || Model == nullptr)
	{
		return;
	}

	checkf(Model->GetUUID().Equals(InModel->GetUUID()), TEXT("ADSSoftFurnitureView::UpdateView --- No Equal Model"));

	Model->ShallowCopy(InModel);

	if (!UDSResourceSubsystem::IsInitialized())
	{
		return;
	}

	FDSBaseProperty* ModelProperty = Model->GetProperty();
	if (ModelProperty == nullptr)
	{
		return;
	}

	if (AttachedMesh == nullptr)
	{
		return;
	}

	UStaticMeshComponent* AttachedMeshComponent = Cast<UStaticMeshComponent>(AttachedMesh->GetComponentByClass(UStaticMeshComponent::StaticClass()));
	if (AttachedMeshComponent == nullptr)
	{
		return;
	}

	EDSMaterialType MaterialType = ModelProperty->StateProperty.bLock ? EDSMaterialType::E_Lock : EDSMaterialType::E_Normal;

	TArray<FDSMaterialInfo>& MaterialList = ADesignStationController::Get()->Is2DScene() ?
		ModelProperty->MaterialProperty.MaterialInfo2D :
		ModelProperty->MaterialProperty.MaterialInfo3D;

	for (const FDSMaterialInfo& MaterialInfo : MaterialList)
	{
		UMaterialInstanceDynamic* CachedMaterial = UDSResourceSubsystem::GetInstance()->FindMaterialFromCache(MaterialInfo.MaterialPath);
		AttachedMeshComponent->SetMaterial(MaterialInfo.Index, CachedMaterial);
	}
}

void ADSSoftFurnitureView::RealRefreshViewMaterialForceLogic(UDSBaseModel* InModel)
{
	RealRefreshViewMaterialLogic(InModel);
}

void ADSSoftFurnitureView::RealTransformViewLogic(UDSBaseModel* InModel)
{
	if (InModel)
	{
		TSharedPtr<FDSSoftFurnitureProperty>  KitchenFurnitureProperty = StaticCastSharedPtr<FDSSoftFurnitureProperty>(InModel->GetPropertySharedPtr());
		if (KitchenFurnitureProperty && KitchenFurnitureProperty.IsValid())
		{
			SetActorTransform(KitchenFurnitureProperty->GetActualTransform(), true);
		}
	}
}

void ADSSoftFurnitureView::GetModelCollectionCaptureComponentsAndActors(TArray<UPrimitiveComponent*>& ShowComponents, TArray<AActor*>& ShowActors)
{
	if (IsValid(AttachedMesh))
	{
		ShowActors.Add(AttachedMesh);
	}
}
