#pragma once

#include "DSCupboardBaseView.h"

#include "QueuedThreadPool.h"
#include "CompGeom/ConvexDecomposition3.h"
#include "Component/MultiComponentDataDefine.h"
#include "Components/VolatileMeshComponent.h"
#include "Library/DSCustomMeshLibrary.h"
#include "Subsystems/Library/DesignStationFunctionLibrary.h"
#include "Subsystems/MVC/DSMVCSubsystem.h"
#include "Subsystems/MVC/Core/Macro.h"
#include "SubSystems/MVC/Core/Property/FurnitureProperty.h"
#include "Subsystems/MVC/Library/DSToolLibrary.h"
#include "SubSystems/MVC/Model/Custom/DSCupboardModel.h"
#include "Subsystems/MVC/Model/Custom/Library/DSCupboardLibrary.h"
#include "Subsystems/MVC/Model/Custom/Library/DSWallBoardLibrary.h"
#include "SubSystems/MVC/View/Gizmo/DSGizmoView.h"
#include "Subsystems/Resource/DSResourceSubsystem.h"
#include "Subsystems/Resource/ParallelGenerateMeshWorker/ParallelGenerateMeshWorker.h"
#include "GeometricCalculate/Library/MaterialLibrary.h"
#include "SubSystems/MVC/View/House/Roof/DSCeilingAreaView.h"
#include "SubSystems/Camera/DSCameraSubsystem.h"
#include "SubSystems/MVC/Core/Property/CupboardProperty.h"
#include "SubSystems/UI/DSUISubsystem.h"

DEFINE_LOG_CATEGORY(DSCupboardBaseViewLog);

ADSCupboardBaseView::ADSCupboardBaseView()
{
	bIsSeparatePart = false;

	Cupboard2DComponent = CreateDefaultSubobject<UProceduralMeshComponent>(TEXT("Cupboard2DComponent"));
	Cupboard2DComponent->AttachToComponent(RootComponent, FAttachmentTransformRules::KeepRelativeTransform);
	Cupboard2DComponent->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
	Cupboard2DComponent->SetCollisionProfileName(TEXT("DSCollisionProfile"));
	//Cupboard2DComponent->SetHiddenInGame(true);

	ReallyMeshComponent = CreateDefaultSubobject<UBoxComponent>(TEXT("ReallyMeshComponent"));
	ReallyMeshComponent->InitBoxExtent(FVector(0.1f, 0.1f, 0.1f));
	ReallyMeshComponent->SetGenerateOverlapEvents(false);
	ReallyMeshComponent->SetCollisionProfileName(TEXT("DSCollisionProfile"));
	ReallyMeshComponent->AttachToComponent(RootComponent, FAttachmentTransformRules::KeepRelativeTransform);

	ReallyMeshComponent->OnComponentBeginOverlap.AddDynamic(this, &ADSCupboardBaseView::OnThisDSComponentBeginOverlapHandle);
	ReallyMeshComponent->OnComponentEndOverlap.AddDynamic(this, &ADSCupboardBaseView::OnThisDSComponentEndOverlapHandle);

	CustomMeshInfo = FDSCustomMeshInfo();
}

void ADSCupboardBaseView::Init() {}

void ADSCupboardBaseView::RealSpawnViewLogic(UDSBaseModel* InModel)
{
	Super::RealSpawnViewLogic(InModel);

	UDSCupboardModel* CastedModel = Cast<UDSCupboardModel>(Model);
	if (CastedModel == nullptr)
	{
		return;
	}

	UDSBaseModel* RootCupboard = CastedModel->GetOwnerModel();
	if (RootCupboard == nullptr)
	{
		return;
	}

	if (RootCupboard != Model)
	{
		bIsSeparatePart = true;
		ADSCupboardBaseView* ParentView = Cast<ADSCupboardBaseView>(RootCupboard->GetOwnedView());

		if (ParentView != nullptr && ParentView != GetAttachParentActor())
		{
			AttachToActor(ParentView, FAttachmentTransformRules::KeepRelativeTransform);
		}
	}

	RealUpdateViewLogic(InModel);
}

void ADSCupboardBaseView::RealUpdateViewLogic(UDSBaseModel* InModel)
{
	Super::RealUpdateViewLogic(InModel);

	if (InModel == nullptr)
	{
		return;
	}

	UDSCupboardModel* CustomModel = Cast<UDSCupboardModel>(InModel);
	if (CustomModel == nullptr)
	{
		return;
	}

	if (CustomModel->GetRootCupboardModel() != CustomModel)
	{
		SetActorRelativeTransform(CustomModel->GetRelativeTransform());
	}

	if (CustomModel->HasParsed() && CustomModel->AreRelatedResourcesLoaded())
	{
		if (MeshComponent != nullptr)
		{
			MeshComponent->ClearAllMeshSections();
		}

		RefreshMesh(*CustomModel->GetComponentTreeDataRef(), CustomModel->GetRootCupboardModel()->GetNodesToDisableCollision());
	}
	else
	{
		if (GenerateMeshWorker)
		{
			GenerateMeshWorker->OnParallelGenerateMeshCompleteEvent().RemoveAll(this);
			GenerateMeshWorker->OnParallelGenerateMeshWorkFinishedEvent().RemoveAll(this);

			if (GenerateMeshWorker->IsRunning())
			{
				GenerateMeshWorker->Abandon();	
			}

			GThreadPool->RetractQueuedWork(GenerateMeshWorker.Get());

			GenerateMeshWorker->ClearGenerateMeshInfos();
			GenerateMeshWorker.Reset();
		}

		ClearMeshData();
		if (!bIsSeparatePart)
		{
			UDSCupboardLibrary::GetFixedOffsetParameter(CustomModel);
			UDSCupboardLibrary::GetFixedSizeParameter(CustomModel);
			UDesignStationFunctionLibrary::CreateLazyLoadBoxMeshForCustom(CustomModel, MeshComponent);

			OnGenerateMeshWorkFinished();
		}
	}
	
	Generate2DMesh();
}

void ADSCupboardBaseView::DeleteView()
{
	if (GenerateMeshWorker)
	{
		GenerateMeshWorker->OnParallelGenerateMeshCompleteEvent().RemoveAll(this);
		GenerateMeshWorker->OnParallelGenerateMeshWorkFinishedEvent().RemoveAll(this);
		if (GenerateMeshWorker->IsRunning())
		{
			GenerateMeshWorker->Abandon();
		}

		GThreadPool->RetractQueuedWork(GenerateMeshWorker.Get());
		GenerateMeshWorker.Reset();
	}

	ClearMeshData();

	Super::DeleteView();
}

FBox ADSCupboardBaseView::GetBoundBox()
{
	FVector Origin, Extend;
	this->GetActorBounds(false, Origin, Extend, true);
	return FBox(Origin - Extend, Origin + Extend);
}

void ADSCupboardBaseView::SetupCollisionIgnoreActor()
{
	if (!UDSToolLibrary::IsActorTopLevel_Only(this))
	{
		return;
	}

	TArray<AActor*> AttachActors;
	this->GetAttachedActors(AttachActors, true, true);

	if (AttachActors.IsEmpty())
	{
		return;
	}

	TArray<AActor*> ThisStructInclude = {this};
	for (auto& AA : AttachActors)
	{
		if (ADSCupboardBaseView* Temp = Cast<ADSCupboardBaseView>(AA))
		{
			ThisStructInclude.AddUnique(Temp);
		}
	}

	this->AddDSIgnoreActors(ThisStructInclude);
	for (auto& AA : AttachActors)
	{
		if (ADSCupboardBaseView* Temp = Cast<ADSCupboardBaseView>(AA))
		{
			Temp->AddDSIgnoreActors(ThisStructInclude);
		}
	}
}

void ADSCupboardBaseView::AddDSIgnoreActors(const TArray<AActor*>& InIgnores)
{
	if (ReallyMeshComponent != nullptr)
	{
		UDSToolLibrary::SetIgnoreActorForComponentCollision(ReallyMeshComponent, InIgnores);
	}
}

void ADSCupboardBaseView::AddToIgnoreDSView(ADSBaseView* ToIgnoreView)
{
	/*if (ToIgnoreView == nullptr)
	{
		return;
	}
	SnapIgnoreActor.AddUnique(ToIgnoreView);

	TArray<AActor*> CurOverlappingActorExceptIgnore;
	UDSToolLibrary::ModifyOverlapBySnap(this, { ToIgnoreView }, CurOverlappingActorExceptIgnore);
	for (auto& SIA : SnapIgnoreActor)
	{
		if (SIA == nullptr) continue;
		CurOverlappingActorExceptIgnore.Remove(SIA);
	}
	if (CurOverlappingActorExceptIgnore.Num() <= 0 && this->GetModel()->IsHasModelFlag(EModelState::E_Overlap))
	{
		this->GetModel()->OnExecuteAction(FDSModelExecuteType::ExecuteUnOverlap);
	}

	UDSToolLibrary::SetIgnoreActorForComponentCollision(ReallyMeshComponent, { ToIgnoreView });*/
}

bool ADSCupboardBaseView::IsGeneratingMesh() const
{
	return GenerateMeshWorker && GenerateMeshWorker->IsRunning();
}

void ADSCupboardBaseView::GetModelCollectionCaptureComponentsAndActors(TArray<UPrimitiveComponent*>& ShowComponents, TArray<AActor*>& ShowActors)
{

	for (auto& MeshComp: CustomMeshInfo.MeshComps)
	{
		ShowComponents.Add(MeshComp);
	}
	for (auto& ImportActor : CustomMeshInfo.ImportActors)
	{
		ShowActors.Add(ImportActor);
	}

	TArray<AActor*> AttachActors;
	this->GetAttachedActors(AttachActors, true, true);

	if (AttachActors.IsEmpty())
	{
		return;
	}

	for (auto& AA : AttachActors)
	{
		if (ADSCupboardBaseView* Temp = Cast<ADSCupboardBaseView>(AA))
		{
			Temp->GetModelCollectionCaptureComponentsAndActors(ShowComponents, ShowActors);
		}
	}
}

void ADSCupboardBaseView::BeginPlay()
{
	Super::BeginPlay();

	this->OnActorBeginOverlap.AddDynamic(this, &ADSCupboardBaseView::OnDSActorBeginOverlapHandle);
	this->OnActorEndOverlap.AddDynamic(this, &ADSCupboardBaseView::OnDSActorEndOverlapHandle);
	this->OnActorHit.AddDynamic(this, &ADSCupboardBaseView::OnDSActorHitHandle);

	UDSCameraSubsystem::GetInstance()->CameraTypeDelegate.AddLambda([&](int32 NewType)
		{
			if (Cupboard2DComponent)
			{
				ECameraType Type = static_cast<ECameraType>(NewType);
				Cupboard2DComponent->SetHiddenInGame(Type == ECameraType::EDollHouse || Type == ECameraType::EWalk);
			}
		});
}

void ADSCupboardBaseView::ResizeMeshPool()
{
	if (!GenerateMeshWorker || GenerateMeshWorker->IsRunning())
	{
		return;
	}

	while (CustomMeshInfo.OutlineComps.IsValidIndex(0))
	{
		UOutlineComponent* Component = CustomMeshInfo.OutlineComps.Top();
		if (Component != nullptr)
		{
			Component->DetachFromComponent(FDetachmentTransformRules::KeepRelativeTransform);
			Component->DestroyComponent();
		}

		CustomMeshInfo.OutlineComps.Pop();
	}

	int32 CustomMeshPoolSize = 0;
	for (const FParallelGenerateMeshInfo& MeshInfo : GenerateMeshWorker->GetSourceMeshInfos())
	{
		if (MeshInfo.SourceItem.ComponentSource != ESingleComponentSource::EImportPAK)
		{
			++CustomMeshPoolSize;
		}
	}

	if (CustomMeshInfo.MeshComps.Num() < CustomMeshPoolSize)
	{
		CustomMeshInfo.MeshComps.AddZeroed(CustomMeshPoolSize - CustomMeshInfo.MeshComps.Num());
	}
	else
	{
		while (CustomMeshInfo.MeshComps.Num() > CustomMeshPoolSize)
		{
			UVolatileMeshComponent* Component = CustomMeshInfo.MeshComps.Top();
			if (Component != nullptr)
			{
				Component->DetachFromComponent(FDetachmentTransformRules::KeepRelativeTransform);
				Component->DestroyComponent();
			}

			CustomMeshInfo.MeshComps.Pop();
		}
	}

	for (int32 Index = 0; Index < CustomMeshInfo.ImportActors.Num(); ++Index)
	{
		if (CustomMeshInfo.ImportActors[Index])
		{
			CustomMeshInfo.ImportActors[Index]->DetachFromActor(FDetachmentTransformRules::KeepRelativeTransform);
			CustomMeshInfo.ImportActors[Index]->Destroy();
		}
	}

	CustomMeshInfo.ImportActors.Empty();
}

void ADSCupboardBaseView::ClearMeshData()
{
	for (AImportPakBaseClass* Actor : CustomMeshInfo.ImportActors)
	{
		if (Actor == nullptr)
		{
			continue;
		}

		Actor->DetachFromActor(FDetachmentTransformRules::KeepWorldTransform);
		Actor->Destroy();
	}

	CustomMeshInfo.ImportActors.Empty();

	for (UOutlineComponent* OutlineComponent : CustomMeshInfo.OutlineComps)
	{
		if (OutlineComponent == nullptr)
		{
			continue;
		}

		OutlineComponent->ClearOutlines();
	}

	for (UVolatileMeshComponent* VolatileMeshComponent : CustomMeshInfo.MeshComps)
	{
		if (VolatileMeshComponent == nullptr)
		{
			continue;
		}

		VolatileMeshComponent->ClearAllMeshSections();
	}
}

void ADSCupboardBaseView::GenerateMesh(const TSharedPtr<FMultiComponentDataItem>& InComponentData, FShowMultiComponentActorProperty& OutMesh, const FString& InDMValue, const TArray<FString>& NodesToDisableCollision, const FTransform& ParentTransform)
{
	OutMesh.Visibility = InComponentData->IsVisiable();
	if (OutMesh.Visibility == false || UDSCupboardLibrary::ComponentNeedSeparate(InComponentData->ComponentParameters))
	{
		return;
	}

	OutMesh.MeshTransform.SetLocation(InComponentData->ComponentLocation.GetLocation());
	OutMesh.MeshTransform.SetScale3D(InComponentData->ComponentScale.GetScale());
	OutMesh.MeshTransform.SetRotation(FQuat(InComponentData->ComponentRotation.GetRotation()));
	OutMesh.ComponentID = InComponentData->GetComponentID();
	OutMesh.ComponentCode = InComponentData->Code;
	OutMesh.ModelType = InComponentData->ModelType;
	OutMesh.UUID = InComponentData->UUID;

	FTransform CurrentAccumulatedTransform = OutMesh.MeshTransform * ParentTransform;

	FString DMValue = InDMValue;
	int32 DMIndex = InComponentData->ComponentParameters.IndexOfByPredicate([](const FParameterData& InOther) { return InOther.Data.name.Equals(TEXT("DM")); });
	if (INDEX_NONE != DMIndex)
	{
		DMValue = InComponentData->ComponentParameters[DMIndex].Data.value;
	}

	if (ECompType::MultiCom == InComponentData->ComponentType)
	{
		for (TSharedPtr<FMultiComponentDataItem>& ChildComponent : InComponentData->ChildComponent)
		{
			if (!ChildComponent->IsVisiable() || UDSCupboardLibrary::ComponentNeedSeparate(ChildComponent->ComponentParameters))
			{
				continue;
			}

			FShowMultiComponentActorProperty NewComponentData = OutMesh.MultiComponentDatas.AddDefaulted_GetRef();

			GenerateMesh(ChildComponent, NewComponentData, DMValue, NodesToDisableCollision, CurrentAccumulatedTransform);
		}
	}
	else
	{
		int32 ComponentId = OutMesh.SingleComponentDatas.AddDefaulted();
		if (InComponentData->SingleComponentData.IsValid() && !FMath::IsNearlyZero(FCString::Atof(*InComponentData->ComponentVisibility.Value)))
		{
			FParameterData PathUVParam;
			InComponentData->GetParameter(TEXT(""), TEXT("FYWLFX"), PathUVParam);

			for (FSingleComponentItem& SingleMeshIter : InComponentData->SingleComponentData.ComponentItems)
			{
				if (FMath::IsNearlyZero(FCString::Atof(*SingleMeshIter.VisibleParam.Value)))
				{
					continue;
				}

				int32 MeshInfoId = OutMesh.SingleComponentDatas[ComponentId].MeshInfo.AddDefaulted();
				OutMesh.SingleComponentDatas[ComponentId].MeshInfo[MeshInfoId].CompSource = SingleMeshIter.ComponentSource;
				OutMesh.SingleComponentDatas[ComponentId].MeshInfo[MeshInfoId].MeshTransform.SetLocation(SingleMeshIter.SingleComponentLocation.GetLocation());
				OutMesh.SingleComponentDatas[ComponentId].MeshInfo[MeshInfoId].MeshTransform.SetRotation(FQuat(SingleMeshIter.SingleComponentRotation.GetRotation()));
				OutMesh.SingleComponentDatas[ComponentId].MeshInfo[MeshInfoId].MeshTransform.SetScale3D(SingleMeshIter.SingleComponentScale.GetScale());

				if (GenerateMeshWorker && !GenerateMeshWorker->IsRunning())
				{
					FParallelGenerateMeshInfo NewMeshInfo;
					NewMeshInfo.bForceVisible = false;
					NewMeshInfo.bCloseCollision = NodesToDisableCollision.Contains(InComponentData->UUID);
					NewMeshInfo.ParentTransform = CurrentAccumulatedTransform;
					NewMeshInfo.ComponentId = ComponentId;
					NewMeshInfo.MeshInfoId = MeshInfoId;
					NewMeshInfo.SourceItem = SingleMeshIter;
					NewMeshInfo.MeshTransform = OutMesh.SingleComponentDatas[ComponentId].MeshInfo[MeshInfoId].MeshTransform;
					NewMeshInfo.bPathUV = !PathUVParam.Data.name.IsEmpty() && FCString::Atoi(*PathUVParam.Data.value) == 0;
					NewMeshInfo.NodeUUID = InComponentData->UUID;
					NewMeshInfo.NodeName = InComponentData->ComponentName;
					GenerateMeshWorker->AddGenerateMeshInfo(NewMeshInfo);
				}
			}
		}
	}
}

void ADSCupboardBaseView::CreateOutlineComponent(const TArray<TPair<FVector, FVector>>& OutlinePairs, const FTransform& Transform)
{
	UOutlineComponent* NewOutline = NewObject<UOutlineComponent>(this, FName(*FGuid::NewGuid().ToString()));
	NewOutline->RegisterComponent();
	NewOutline->SetCollisionEnabled(ECollisionEnabled::NoCollision);
	NewOutline->AttachToComponent(ReallyMeshComponent, FAttachmentTransformRules::KeepRelativeTransform);
	NewOutline->SetCastShadow(false);
	AddInstanceComponent(NewOutline);

	NewOutline->ClearOutlines();
	NewOutline->SetRelativeTransform(Transform);

	for (const TPair<FVector, FVector>& Pair : OutlinePairs)
	{
		NewOutline->AddOutlines(FOutlineLineSegments(Pair.Key, Pair.Value));
	}

	CustomMeshInfo.OutlineComps.Add(NewOutline);
}

void ADSCupboardBaseView::CreatePakActor(const FShowSingleComponentActorWithTransformProperty& MeshProperty,
	const FString& InNodeUUID, const FString& InNodeName,
	const FTransform& Transform, bool bCloseCollision)
{
	UWorld* World = GetWorld();
	if (World == nullptr)
	{
		return;
	}

	if (MeshProperty.SingleMeshInfo.Num() != 1)
	{
		return;
	}

	if (MeshProperty.SingleMeshInfo[0].PakRefPath.IsEmpty())
	{
		return;
	}

	FString PakCode = MeshProperty.SingleMeshInfo[0].PakCode;
	if (!PakCode.IsEmpty())
	{
		PakCode = PakCode.Mid(1, PakCode.Len() - 2);
	}

	UClass* ActorClass = LoadClass<AImportPakBaseClass>(this, *MeshProperty.SingleMeshInfo[0].PakRefPath);
	if (ActorClass == nullptr)
	{
		return;
	}

	AImportPakBaseClass* PakActor = World->SpawnActor<AImportPakBaseClass>(ActorClass);
	if (PakActor == nullptr)
	{
		return;
	}

	PakActor->AttachToComponent(ReallyMeshComponent, FAttachmentTransformRules::KeepWorldTransform);
	PakActor->SetActorRelativeTransform(Transform);
	PakActor->FurnitureCodeChanged(PakCode);
	PakActor->SetOwner(this);
	PakActor->Tags.Add(TEXT("Cmp"));

	//保存所属的节点UUID
	SetNodeUUIDInActorTags(PakActor, InNodeUUID, InNodeName);

	CustomMeshInfo.ImportActors.Add(PakActor);

	if (bCloseCollision)
	{
		PakActor->SetActorEnableCollision(!bCloseCollision);
	}

	//如果Model已经隐藏，则隐藏这个PakActor
	if (GetModel() != nullptr)
	{
		if (GetModel()->HasHidden())
		{
			PakActor->SetActorHiddenInGame(true);
			PakActor->ForEachComponent<UPrimitiveComponent>(true, [](UPrimitiveComponent* Comp) {
				Comp->SetCollisionResponseToChannel(ECollisionChannel::ECC_Visibility, ECollisionResponse::ECR_Ignore);
				});
		}
	}
}

void ADSCupboardBaseView::CreateCustomMeshComponent(int32 ComponentIndex, const FShowSingleComponentActorWithTransformProperty& MeshProperty, 
	const TArray<TArray<FVector>>& MeshCollision, const FTransform& Transform,
	const FString& InNodeUUID, const FString& InNodeName, bool bCloseCollision)
{
	UWorld* World = GetWorld();
	if (World == nullptr)
	{
		return;
	}

	if (!UDSResourceSubsystem::IsInitialized())
	{
		return;
	}

	if (CustomMeshInfo.MeshComps.Num() < (ComponentIndex + 1))
	{
		CustomMeshInfo.MeshComps.AddZeroed((ComponentIndex + 1) - CustomMeshInfo.MeshComps.Num());
	}

	if (CustomMeshInfo.MeshComps[ComponentIndex] == nullptr)
	{
		CustomMeshInfo.MeshComps[ComponentIndex] = NewObject<UVolatileMeshComponent>(this, FName(*FGuid::NewGuid().ToString()));
		CustomMeshInfo.MeshComps[ComponentIndex]->AttachToComponent(ReallyMeshComponent, FAttachmentTransformRules::KeepRelativeTransform);

		CustomMeshInfo.MeshComps[ComponentIndex]->RegisterComponent();
		AddInstanceComponent(CustomMeshInfo.MeshComps[ComponentIndex]);
	}

	//保存这个组件所属的节点UUID
	SetNodeUUIDInComponentTags(CustomMeshInfo.MeshComps[ComponentIndex], InNodeUUID,InNodeName);   

	CustomMeshInfo.MeshComps[ComponentIndex]->SetRelativeTransform(Transform);

	//是否是护墙板中的艺术贴图板子
	bool bIsTextureWallBoardModel = false;  
	UDSBaseModel* TextureWallBoardModel = GetModel();
	EWallBoardType WallBoardType = EWallBoardType::WallBoard_None;
	if (TextureWallBoardModel != nullptr )
	{
		WallBoardType = UDSWallBoardLibrary::GetTextureWallBoardIndex(TextureWallBoardModel);
		if (WallBoardType != EWallBoardType::WallBoard_None)
		{
			bIsTextureWallBoardModel = true;
		}
	}

	for (int32 SectionIndex = 0; SectionIndex < MeshProperty.SingleMeshInfo.Num(); ++SectionIndex)
	{
		FTransform ComponentRelativeTransform = CustomMeshInfo.MeshComps[ComponentIndex]->GetRelativeTransform();
		FPMCSection SectionMesh = MeshProperty.SingleMeshInfo[SectionIndex].MeshInfo;
		
		TSharedPtr<FDSResourceInfo> ResourceInfo = UDSResourceSubsystem::GetInstance()->FindResourceFromCache(FString::Printf(TEXT("%lld"), MeshProperty.SingleMeshInfo[SectionIndex].MaterialFolderID));
		if (!ResourceInfo)
		{
			ResourceInfo = UDSResourceSubsystem::GetInstance()->FindResourceFromCache(TEXT("00000001"));
			if (!ResourceInfo)
			{
				continue;	
			}
		}

		FString MaterialChecksum = ResourceInfo->GetResourceFile(EDSResourceQuality::Low).MD5;
		if (MaterialChecksum.IsEmpty())
		{
			continue;
		}

		auto UVs = MeshProperty.SingleMeshInfo[SectionIndex].MeshInfo.UV;
		
		if (bIsTextureWallBoardModel)   //计算护墙板艺术贴图的UV
		{
			UDSCupboardModel* WallBoardCupboardModel = Cast<UDSCupboardModel>(TextureWallBoardModel->GetTopLevelOwnerModel());
			auto Prop = WallBoardCupboardModel->GetTypedProperty<FCupboardProperty>();

			FVector2D UVOffset(0.0f, 0.0f);
			FVector2D UVSize(1.0f, 1.0f);

			FDSMaterialInfo* MInfo = Prop->MaterialProperty.MaterialInfo3D.FindByPredicate(
				[WallBoardType](const FDSMaterialInfo& Info)->bool
				{
					return Info.Id == FString::Printf(TEXT("ProgramParam_WallBoardUse_%d"), (int32)WallBoardType);
				}
			);

			if (MInfo != nullptr)
			{
				UVOffset.Set(MInfo->UVoffsetU, MInfo->UVoffsetV);
				UVSize.Set(MInfo->USize, MInfo->VSize);
			}
			
			FBox Box = TextureWallBoardModel->GetBoundBoxByPropertyCalculate();
			Box = FBox(Box.Min * ComponentRelativeTransform.GetScale3D(), Box.Max * ComponentRelativeTransform.GetScale3D());
			FVector EightVertices[8];
			Box.GetVertices(EightVertices);

			FVector LeftTopPos = EightVertices[6];
			
			auto& Vertexes = MeshProperty.SingleMeshInfo[SectionIndex].MeshInfo.Vertexes;
			for(int32 i=0;i< Vertexes.Num();++i)
			{
				auto PIte = Vertexes[i] * ComponentRelativeTransform.GetScale3D();
				FVector2D TempUV = FVector2D((PIte - LeftTopPos).X, -(PIte - LeftTopPos).Z);
				TempUV /= FVector2D(Box.GetSize().X, Box.GetSize().Z);
				TempUV *= UVSize;
				TempUV += UVOffset;
				UVs[i] = TempUV;
			}

			SectionMesh.UV = UVs;
		}
		else
		{
			auto TextureWidth = FCString::Atof(*ResourceInfo->Width) * 0.1;
			auto TextureHeight = FCString::Atof(*ResourceInfo->Height) * 0.1;
			auto Vertexes = MeshProperty.SingleMeshInfo[SectionIndex].MeshInfo.Vertexes;
			for (auto& V : Vertexes)
			{
				V *= ComponentRelativeTransform.GetScale3D();
			}
			FMaterialLibrary::AdjustUVWithWorldScale(Vertexes, UVs, TextureWidth, TextureHeight);
			SectionMesh.UV = UVs;
			FMaterialLibrary::RotateUVs(SectionMesh.UV, -PI/2);
		}

		CustomMeshInfo.MeshComps[ComponentIndex]->CreateMeshSection(SectionIndex, SectionMesh, true);
		UMaterialInstanceDynamic* CachedMaterial = UDSResourceSubsystem::GetInstance()->FindMaterialFromCache(MaterialChecksum);
		if (CachedMaterial != nullptr)
		{
			CustomMeshInfo.MeshComps[ComponentIndex]->SetMaterialWithName(
				SectionIndex,
				*FString::Printf(TEXT("%lld"), MeshProperty.SingleMeshInfo[SectionIndex].MaterialFolderID),
				CachedMaterial);
		}
	}

	CustomMeshInfo.MeshComps[ComponentIndex]->SetGenerateOverlapEvents(true);
	CustomMeshInfo.MeshComps[ComponentIndex]->SetCollisionProfileName(TEXT("DSCollisionProfile"));
	
	CustomMeshInfo.MeshComps[ComponentIndex]->bUseComplexAsSimpleCollision = false;
	CustomMeshInfo.MeshComps[ComponentIndex]->SetCollisionConvexMeshes(MeshCollision);

	if (bIsTextureWallBoardModel)
	{
		//护墙板艺术板子不能选中
		CustomMeshInfo.MeshComps[ComponentIndex]->SetCollisionEnabled(ECollisionEnabled::Type::NoCollision);
	}
	else if (bCloseCollision)
	{
		CustomMeshInfo.MeshComps[ComponentIndex]->SetCollisionResponseToAllChannels(ECollisionResponse::ECR_Ignore);
		CustomMeshInfo.MeshComps[ComponentIndex]->SetCollisionResponseToChannel(ECollisionChannel::ECC_Visibility, ECollisionResponse::ECR_Overlap);
	}
}

void ADSCupboardBaseView::RefreshMesh(FMultiComponentDataItem& TreeData, const TArray<FString>& NodesToDisableCollision)
{
	if (GThreadPool == nullptr)
	{
		return;
	}
	
	if (GenerateMeshWorker)
	{
		GenerateMeshWorker->OnParallelGenerateMeshCompleteEvent().RemoveAll(this);
		GenerateMeshWorker->OnParallelGenerateMeshWorkFinishedEvent().RemoveAll(this);
		if (GenerateMeshWorker->IsRunning())
		{
			GenerateMeshWorker->Abandon();
		}

		GThreadPool->RetractQueuedWork(GenerateMeshWorker.Get());
	}
	
	GenerateMeshWorker = MakeShared<FParallelGenerateMeshWorker>();
	GenerateMeshWorker->ClearGenerateMeshInfos();

	ParsedComponents.Empty();

	for (TSharedPtr<FMultiComponentDataItem>& ChildComponent : TreeData.ChildComponent)
	{
		ChildComponent->GenerateDoorItemUUID();
		GenerateMesh(ChildComponent, ParsedComponents.AddDefaulted_GetRef(), TEXT(""), NodesToDisableCollision);
	}

	ResizeMeshPool();

	ReallyMeshComponent->SetRelativeLocation(FVector::ZeroVector);
	ReallyMeshComponent->SetRelativeRotation(FRotator::ZeroRotator);
	ReallyMeshComponent->SetRelativeScale3D(FVector::OneVector);

	GenerateMeshWorker->OnParallelGenerateMeshCompleteEvent().AddUObject(this, &ThisClass::OnGenerateMeshComplete);
	GenerateMeshWorker->OnParallelGenerateMeshWorkFinishedEvent().AddUObject(this, &ThisClass::OnGenerateMeshWorkFinished);

	GThreadPool->AddQueuedWork(GenerateMeshWorker.Get());
}

void ADSCupboardBaseView::RealHoverViewLogic(UDSBaseModel* InModel)
{
	Super::RealHoverViewLogic(InModel);

	/*TArray<USceneComponent*> AllChildrenComponents;
	RootComponent->GetChildrenComponents(true, AllChildrenComponents);
	for (USceneComponent* It : AllChildrenComponents)
	{
		if (UPrimitiveComponent* PrimComponent = Cast<UPrimitiveComponent>(It))
		{
			PrimComponent->SetRenderCustomDepth(true);
			PrimComponent->SetCustomDepthStencilValue(10);
			PrimComponent->SetCustomDepthStencilWriteMask(ERendererStencilMask::ERSM_Default);
		}
	}*/
}

void ADSCupboardBaseView::RealUnHoverViewLogic(UDSBaseModel* InModel)
{
	Super::RealUnHoverViewLogic(InModel);

	/*TArray<USceneComponent*> AllChildrenComponents;
	RootComponent->GetChildrenComponents(true, AllChildrenComponents);
	for (USceneComponent* It : AllChildrenComponents)
	{
		if (UPrimitiveComponent* PrimComponent = Cast<UPrimitiveComponent>(It))
		{
			PrimComponent->SetRenderCustomDepth(false);
			PrimComponent->SetCustomDepthStencilValue(0);
			PrimComponent->SetCustomDepthStencilWriteMask(ERendererStencilMask::ERSM_Default);
		}
	}*/
}

void ADSCupboardBaseView::RealSelectViewLogic(UDSBaseModel* InModel)
{
	Super::RealSelectViewLogic(InModel);

	//auto Flag = InModel->GetModelStateFlag();
	////if (Flag.HasState(EModelState::E_Overlap))
	////{
	////	return;
	////}

	//TArray<USceneComponent*> AllChildrenComponents;
	//RootComponent->GetChildrenComponents(true, AllChildrenComponents);
	//for (USceneComponent* It : AllChildrenComponents)
	//{
	//	if (UPrimitiveComponent* PrimComponent = Cast<UPrimitiveComponent>(It))
	//	{
	//		PrimComponent->SetRenderCustomDepth(true);
	//		PrimComponent->SetCustomDepthStencilValue(1);
	//		PrimComponent->SetCustomDepthStencilWriteMask(ERendererStencilMask::ERSM_Default);
	//	}
	//}
}

void ADSCupboardBaseView::RealUnSelectViewLogic(UDSBaseModel* InModel)
{
	Super::RealUnSelectViewLogic(InModel);

	//TArray<USceneComponent*> AllChildrenComponents;
	//RootComponent->GetChildrenComponents(true, AllChildrenComponents);
	//auto Flag = InModel->GetModelStateFlag();
	////if (Flag.HasState(EModelState::E_Overlap))
	////{
	////	return;
	////}
	//for (USceneComponent* It : AllChildrenComponents)
	//{
	//	if (UPrimitiveComponent* PrimComponent = Cast<UPrimitiveComponent>(It))
	//	{
	//		PrimComponent->SetRenderCustomDepth(false);
	//		PrimComponent->SetCustomDepthStencilValue(0);
	//		PrimComponent->SetCustomDepthStencilWriteMask(ERendererStencilMask::ERSM_Default);
	//	}
	//}
}

void ADSCupboardBaseView::RealHiddenViewLogic(UDSBaseModel* InModel)
{
	for (auto& PA : CustomMeshInfo.ImportActors)
	{
		if (PA != nullptr)
		{
			PA->SetActorHiddenInGame(true);
			PA->ForEachComponent<UPrimitiveComponent>(true, [](UPrimitiveComponent* Comp) {
				Comp->SetCollisionResponseToChannel(ECollisionChannel::ECC_Visibility, ECollisionResponse::ECR_Ignore);
				});
		}
	}
	//Super::RealHiddenViewLogic(InModel);
	//隐藏时，占位保留碰撞

	SetActorHiddenInGame(true);

	ForEachComponent<UPrimitiveComponent>(false, [](UPrimitiveComponent* Comp) {
		Comp->SetCollisionResponseToChannel(ECollisionChannel::ECC_Visibility, ECollisionResponse::ECR_Ignore);
		});
	if (MeshComponent)
	{
		MeshComponent->SetVisibility(false);
	}
}

void ADSCupboardBaseView::RealUnHiddenViewLogic(UDSBaseModel* InModel)
{
	for (auto& PA : CustomMeshInfo.ImportActors)
	{
		if (PA != nullptr)
		{
			PA->SetActorEnableCollision(true);
			PA->SetActorHiddenInGame(false);
			PA->ForEachComponent<UPrimitiveComponent>(true, [](UPrimitiveComponent* Comp) {
				Comp->SetCollisionResponseToChannel(ECollisionChannel::ECC_Visibility, ECollisionResponse::ECR_Overlap);
				});
		}
	}
	ForEachComponent<UPrimitiveComponent>(false, [](UPrimitiveComponent* Comp) {
		Comp->SetCollisionResponseToChannel(ECollisionChannel::ECC_Visibility, ECollisionResponse::ECR_Overlap);
		});


	Super::RealUnHiddenViewLogic(InModel);
}

void ADSCupboardBaseView::RealOverlapViewLogic(UDSBaseModel* InModel)
{
	Super::RealOverlapViewLogic(InModel);

	OnOverlapInner();
}

void ADSCupboardBaseView::RealUnOverlapViewLogic(UDSBaseModel* InModel)
{
	Super::RealUnOverlapViewLogic(InModel);

	OnUnOverlapInner();
}

void ADSCupboardBaseView::RealDisableViewLogic(UDSBaseModel* InModel)
{
	Super::RealDisableViewLogic(InModel);

	TArray<USceneComponent*> AllChildrenComponents;
	RootComponent->GetChildrenComponents(true, AllChildrenComponents);
	for (USceneComponent* It : AllChildrenComponents)
	{
		if (UPrimitiveComponent* PrimComponent = Cast<UPrimitiveComponent>(It))
		{
			PrimComponent->SetRenderCustomDepth(true);
			PrimComponent->SetCustomDepthStencilValue(7);
			PrimComponent->SetCustomDepthStencilWriteMask(ERendererStencilMask::ERSM_Default);
		}
	}
}

void ADSCupboardBaseView::RealEnableViewLogic(UDSBaseModel* InModel)
{
	Super::RealEnableViewLogic(InModel);

	if (InModel == nullptr)
	{
		return;
	}

	if (InModel->IsHasModelFlag(EModelState::E_Selected))
	{
		RealSelectViewLogic(InModel);
	}
	else if (InModel->IsHasModelFlag(EModelState::E_Hovered))
	{
		RealHoverViewLogic(InModel);
	}
	else
	{
		TArray<USceneComponent*> AllChildrenComponents;
		RootComponent->GetChildrenComponents(true, AllChildrenComponents);
		for (USceneComponent* It : AllChildrenComponents)
		{
			if (UPrimitiveComponent* PrimComponent = Cast<UPrimitiveComponent>(It))
			{
				PrimComponent->SetRenderCustomDepth(false);
			}
		}
	}
}

void ADSCupboardBaseView::RealRefreshViewMaterialLogic(UDSBaseModel* InModel)
{
	Super::RealRefreshViewMaterialLogic(InModel);

	if (InModel == nullptr || Model == nullptr)
	{
		return;
	}

	checkf(Model->GetUUID().Equals(InModel->GetUUID()), TEXT("ADSCupboardBaseView::OnRefreshMaterial --- No Equal Model"));

	Model->ShallowCopy(InModel);

	if (!UDSResourceSubsystem::IsInitialized())
	{
		return;
	}

	for (UVolatileMeshComponent* Component : CustomMeshInfo.MeshComps)
	{
		if (Component == nullptr)
		{
			continue;
		}

		TArray<FName> UsedMaterialNames;
		Component->GetUsedMaterialNames(UsedMaterialNames);

		// Do not process local zip material, because the local zip materials are loaded at the time of game start.
		for (const FName& MaterialName : UsedMaterialNames)
		{
			TSharedPtr<FDSResourceInfo> ResourceInfo = UDSResourceSubsystem::GetInstance()->FindResourceFromCache(MaterialName.ToString());
			if (!ResourceInfo)
			{
				continue;
			}

			FString MaterialChecksum = ResourceInfo->GetResourceFile(EDSResourceQuality::Low).MD5;
			if (MaterialChecksum.IsEmpty())
			{
				continue;
			}

			UMaterialInstanceDynamic* CachedMaterial = UDSResourceSubsystem::GetInstance()->FindMaterialFromCache(MaterialChecksum);
			if (CachedMaterial == nullptr)
			{
				continue;
			}

			Component->UpdateMaterialByName(MaterialName, CachedMaterial);
		}
	}
}

void ADSCupboardBaseView::OnGenerateMeshComplete(TSharedPtr<FParallelGenerateMeshResult> GeneratedResult)
{
	if (!GeneratedResult || !GeneratedResult->MeshInfo)
	{
		return;
	}

	GeneratedResult->MeshInfo->MeshTransform = GeneratedResult->MeshInfo->MeshTransform * GeneratedResult->ParentTransform;

	CreateOutlineComponent(GeneratedResult->MeshInfo->OutlinePairs, GeneratedResult->MeshInfo->MeshTransform);

	for (const FShowSingleComponentActorWithTransformProperty& MeshProperty : GeneratedResult->MeshInfo->MeshInfo)
	{
		FTransform CurrentMeshTransform = MeshProperty.MeshTransform * GeneratedResult->MeshInfo->MeshTransform;

		switch (MeshProperty.CompSource)
		{
		case ESingleComponentSource::EImportPAK:
			{
				CreatePakActor(MeshProperty, GeneratedResult->NodeUUID, GeneratedResult->NodeName, CurrentMeshTransform, GeneratedResult->bCloseCollision);
			}
			break;
		case ESingleComponentSource::ECustom:
		case ESingleComponentSource::EImportFBX:
			{
				CreateCustomMeshComponent(GeneratedResult->MeshIndex, MeshProperty, GeneratedResult->CollisionMeshes, CurrentMeshTransform, 
					GeneratedResult->NodeUUID, GeneratedResult->NodeName, GeneratedResult->bCloseCollision);
			}
			break;
		}
	}

	ClearComponentOverlaps();
	UpdateOverlaps(true);
	GetModel()->GetTopLevelOwnerModel()->OnExecuteAction(FDSModelExecuteType::ExecuteUnOverlap, FDSBroadcastMarkData::NotBroadcastToMVCMark);

	UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(Model);
	if (CupboardModel->GetRootCupboardModel()->GetModelType() != EDSModelType::E_Furniture_MoldingCeiling)
	{
		TSet<UDSBaseModel*> IgnoreModels;
		IgnoreModels.Append(UDSMVCSubsystem::GetInstance()->GetModels(EDSModelType::E_Furniture_MoldingCeiling));
		UDSToolLibrary::UpdateOverlap(GetModel()->GetTopLevelOwnerModel(), IgnoreModels);
	}
	
	Cast<UDSCupboardModel>(Model)->BroadcastLinkModelsUpdateSelf();
	Cast<UDSCupboardModel>(Model)->BroadcastLinkModelsTransform();
}

void ADSCupboardBaseView::OnGenerateMeshWorkFinished()
{
	UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(Model);
	if (CupboardModel == nullptr)
	{
		return;
	}
	
	if (UDSCupboardLibrary::ShouldNodeHideInGame(CupboardModel->GetRootCupboardModel()->GetModelInfoRef().ComponentTreeData, CupboardModel->GetModelInfoRef().ComponentTreeData))
	{
		RealHiddenViewLogic(CupboardModel);
	}
}

void ADSCupboardBaseView::OnDSActorBeginOverlapHandle(AActor* OverlappedActor, AActor* OtherActor)
{
	GetModel()->GetTopLevelOwnerModel()->OnExecuteAction(FDSModelExecuteType::ExecuteUnOverlap, FDSBroadcastMarkData::NotBroadcastToMVCMark);

	UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(Model);
	if (CupboardModel->GetRootCupboardModel()->GetModelType() != EDSModelType::E_Furniture_MoldingCeiling)
	{
		TSet<UDSBaseModel*> IgnoreModels;
		IgnoreModels.Append(UDSMVCSubsystem::GetInstance()->GetModels(EDSModelType::E_Furniture_MoldingCeiling));

		UDSToolLibrary::UpdateOverlap(GetModel()->GetTopLevelOwnerModel(), IgnoreModels, false);
	}
}

void ADSCupboardBaseView::OnDSActorEndOverlapHandle(AActor* OverlappedActor, AActor* OtherActor)
{
	GetModel()->GetTopLevelOwnerModel()->OnExecuteAction(FDSModelExecuteType::ExecuteUnOverlap, FDSBroadcastMarkData::NotBroadcastToMVCMark);

	UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(Model);
	if (CupboardModel->GetRootCupboardModel()->GetModelType() != EDSModelType::E_Furniture_MoldingCeiling)
	{
		TSet<UDSBaseModel*> IgnoreModels;
		IgnoreModels.Append(UDSMVCSubsystem::GetInstance()->GetModels(EDSModelType::E_Furniture_MoldingCeiling));

		UDSToolLibrary::UpdateOverlap(GetModel()->GetTopLevelOwnerModel(), IgnoreModels, false);
	}
}

void ADSCupboardBaseView::OnDSActorHitHandle(AActor* SelfActor, AActor* OtherActor, FVector NormalImpulse,
                                             const FHitResult& Hit)
{
	UE_LOG(DSCupboardBaseViewLog, Warning, TEXT("DSActorHit, self [%s], Other [%s]"), *SelfActor->GetName(), *OtherActor->GetName());
}

bool ADSCupboardBaseView::IgnoreActorOverlap(AActor* OverlappedActor)
{
	//如果包含过滤参数，则不处理
	auto OverlapDSActor = Cast<ADSCupboardBaseView>(OverlappedActor);
	if (OverlapDSActor == nullptr)
	{
		return false;
	}
	auto ThisModel = OverlapDSActor ? OverlapDSActor->GetModel() : nullptr;
	auto CupboardModel = Cast<UDSCupboardModel>(ThisModel);
	if (CupboardModel == nullptr)
	{
		return false;
	}

	auto ThisTree = CupboardModel->GetModelInfo().ComponentTreeData;
	if (ThisTree)
	{
		auto Param = ThisTree->ComponentParameters.FindByPredicate([](const FParameterData& InParam) {return InParam.Data.name.Equals(TEXT("BT1")); });
		return Param && FCString::Atoi(*Param->Data.value) == 9;
	}
	return false;
}

bool ADSCupboardBaseView::IgnoreActorOverlap(AActor* OverlappedActor, AActor* OtherActor)
{
	//如果包含过滤参数，则不处理
	return IgnoreActorOverlap(OverlappedActor) || IgnoreActorOverlap(OtherActor);
}

void ADSCupboardBaseView::OnThisDSComponentBeginOverlapHandle(UPrimitiveComponent* OverlappedComponent,
                                                              AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep,
                                                              const FHitResult& SweepResult)
{
	if (OverlappedComponent && OtherActor && OtherComp)
	{
		/*FString OverlapActorName = OverlappedComponent->GetOwner()->GetName();
		FString OverlapName = OverlappedComponent->GetName();
		FString OtherActorName = OtherActor->GetName();
		FString OtherName = OtherComp->GetName();
		if (OverlappedComponent->GetOwner() != OtherActor)
		{
			UE_LOG(DSCupboardBaseViewLog, Warning, TEXT("DSComponentOverlap, overlap actor [%s], overlap comp [%s], Other actor [%s], other comp [%s]"), 
				*OverlapActorName, *OverlapName, *OtherActorName, *OtherName);
		}*/
	}
}

void ADSCupboardBaseView::OnThisDSComponentEndOverlapHandle(UPrimitiveComponent* OverlappedComponent,
                                                            AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex)
{
	if (OverlappedComponent && OtherActor && OtherComp)
	{
		/*FString OverlapActorName = OverlappedComponent->GetOwner()->GetName();
		FString OverlapName = OverlappedComponent->GetName();
		FString OtherActorName = OtherActor->GetName();
		FString OtherName = OtherComp->GetName();
		if (OverlappedComponent->GetOwner() != OtherActor)
		{
			UE_LOG(DSCupboardBaseViewLog, Warning, TEXT("DSComponentEnd~~EndOverlap, overlap actor [%s], overlap comp [%s], Other actor [%s], other comp [%s]"),
				*OverlapActorName, *OverlapName, *OtherActorName, *OtherName);
		}
		*/
	}
}

void ADSCupboardBaseView::OnOverlapInner()
{
	UDSBaseModel* ParentModel = GetModel()->GetOwnerModel();
	if (ParentModel != nullptr && (ParentModel->GetModelType() == EDSModelType::E_House_Door || ParentModel->GetModelType() == EDSModelType::E_House_Window))
	{
		return;
	}

	TArray<USceneComponent*> AllChildrenComponents;
	RootComponent->GetChildrenComponents(true, AllChildrenComponents);
	for (USceneComponent* It : AllChildrenComponents)
	{
		if (UPrimitiveComponent* PrimComponent = Cast<UPrimitiveComponent>(It))
		{
			PrimComponent->SetRenderCustomDepth(true);
			PrimComponent->SetCustomDepthStencilValue(4);
			PrimComponent->SetCustomDepthStencilWriteMask(ERendererStencilMask::ERSM_Default);
		}
	}
	if (GetModel()->GetModelType() == EDSModelType::E_Custom_DoorPanel
		|| GetModel()->GetModelType() == EDSModelType::E_Custom_DoorPanel_AluminumFrame
		|| GetModel()->GetModelType() == EDSModelType::E_Custom_DoorPanel_Fake
		|| GetModel()->GetModelType() == EDSModelType::E_Custom_DoorPanel_Flat
		|| GetModel()->GetModelType() == EDSModelType::E_Custom_DoorPanel_Glass
		|| GetModel()->GetModelType() == EDSModelType::E_Custom_DoorPanel_SolidWood)
	{
		TSet<AActor*> TempActors;
		GetOverlappingActors(TempActors);

		if (!TempActors.IsEmpty())
		{


			FString ItemName = TEXT("其他部件");
			for (auto& A : TempActors)
			{
				if (A->IsA<ADSCupboardBaseView>())
				{
					auto ModelType = Cast<ADSCupboardBaseView>(A)->GetModelType();
					if (ModelType == EDSModelType::E_Custom_CabinetBoard)
					{
						ItemName = TEXT("柜体板件");
						break;
					}
					else if (ModelType == EDSModelType::E_Custom_Board)
					{
						ItemName = TEXT("功能板件");
						break;
					}
					else if (ModelType == EDSModelType::E_Custom_DoorPanel
						|| ModelType == EDSModelType::E_Custom_DoorPanel_AluminumFrame
						|| ModelType == EDSModelType::E_Custom_DoorPanel_Fake
						|| ModelType == EDSModelType::E_Custom_DoorPanel_Flat
						|| ModelType == EDSModelType::E_Custom_DoorPanel_Glass
						|| ModelType == EDSModelType::E_Custom_DoorPanel_SolidWood)
					{
						ItemName = TEXT("其他门");
						break;
					}
				}
			}

			FString Tip = TEXT("此操作导致门与") + ItemName + TEXT("发生冲突");

			UDSUISubsystem::GetInstance()->SetToastState(
				EToastState::EWarning,
				Tip);
		}
	}
}

void ADSCupboardBaseView::OnUnOverlapInner()
{
	UDSBaseModel* ParentModel = GetModel()->GetOwnerModel();
	if (ParentModel != nullptr && (ParentModel->GetModelType() == EDSModelType::E_House_Door || ParentModel->GetModelType() == EDSModelType::E_House_Window))
	{
		return;
	}



	TArray<USceneComponent*> AllChildrenComponents;
	RootComponent->GetChildrenComponents(true, AllChildrenComponents);
	for (USceneComponent* It : AllChildrenComponents)
	{

		if (UPrimitiveComponent* PrimComponent = Cast<UPrimitiveComponent>(It))
		{
			PrimComponent->SetRenderCustomDepth(false);
			PrimComponent->SetCustomDepthStencilValue(0);
			PrimComponent->SetCustomDepthStencilWriteMask(ERendererStencilMask::ERSM_Default);
		}
	}

	TArray<UDSBaseModel*> ChildrenModels;
	UDSCupboardLibrary::GetAllChildrenModels(Cast<UDSCupboardModel>(GetModel())->GetComponentTreeDataRef(), ChildrenModels);
	for (auto & Child : ChildrenModels)
	{
		Child->OnExecuteAction(FDSModelExecuteType::ExecuteUnOverlap, FDSBroadcastMarkData::NotBroadcastToMVCMark);
	}
}

void ADSCupboardBaseView::Generate2DMesh()
{
	if (GetModelType() == EDSModelType::E_Custom_LayoutDoor && Cupboard2DComponent && GetOwner() == nullptr)
	{
		auto Prop = Model->GetProperty();
		auto Loc = Prop->GetActualTransform().GetLocation();
		auto Rot = Prop->GetActualTransform().GetRotation().Rotator();
		auto Width = Prop->SizeProperty.Width * 0.1;
		auto Depth = Prop->SizeProperty.Depth * 0.1;

		auto Center = FGeometryLibrary::ConvertLeftBackToCenterRotated(Loc, Width, Depth, Rot);
		auto AxisX = Rot.Vector();
		auto AxisY = FVector::CrossProduct(FVector::ZAxisVector, AxisX);

		TArray<FVector> TopOutline = {
			FVector(0,0,0),
			FVector(Width,0,0),
			FVector(Width,Depth,0),
			FVector(0,Depth,0)
		};

		for (auto & V : TopOutline)
		{
			V.Z = 600.0;
		}
		TArray<int32> Indices = { 0,3,1,3,2,1 };
		TArray<FVector> Normals = { FVector::ZAxisVector,FVector::ZAxisVector,FVector::ZAxisVector ,FVector::ZAxisVector };
		TArray<FVector2D> UVs = { FVector2D(0,0),FVector2D(1,0),FVector2D(1,1),FVector2D(0,1) };
		TArray<FColor> Colors = { FColor::White,FColor::White,FColor::White,FColor::White };
		TArray<FProcMeshTangent> Tangents = { FProcMeshTangent(0, 1, 0), FProcMeshTangent(0, 1, 0), FProcMeshTangent(0, 1, 0), FProcMeshTangent(0, 1, 0) };
		if (Cupboard2DComponent->GetNumSections() > 0)
		{
			Cupboard2DComponent->ClearAllMeshSections();
		}
		Cupboard2DComponent->CreateMeshSection(0, TopOutline, Indices, Normals, UVs, Colors, Tangents,true);
		ADesignStationController* Controller = ADesignStationController::Get();
		bool bIs2D = Controller->Is2DScene();
		Cupboard2DComponent->SetHiddenInGame(!bIs2D);
	}
	else if (Cupboard2DComponent != nullptr)
	{
		Cupboard2DComponent->SetHiddenInGame(true);
	}
}

bool ADSCupboardBaseView::CupboardShouldEndOverlap(ADSBaseView* OverlappedActor)
{
	TSet<AActor*> OverlappingActors;
	TArray<AActor*> IngoreActors;

	auto CupboardModel = Cast<UDSCupboardModel>(OverlappedActor->GetModel());
	auto AllUUIDs = CupboardModel->GetAllComponentModelsUUID(true);
	for (auto& Iter : AllUUIDs)
	{
		auto CmpModel = UDSMVCSubsystem::GetInstance()->GetModelByID(Iter);
		auto CmpView = UDSMVCSubsystem::GetInstance()->GetView(CmpModel);

		if (CmpModel && UDSToolLibrary::IsCustomCabinetType(CmpModel->GetModelType()))
		{
			auto CV = Cast<ADSCupboardBaseView>(CmpView);
			if (CV)
			{
				auto MeshInfo = CV->GetCustomMeshInfo();
				for (auto& M : MeshInfo.GetImportActors())
				{
					TSet<AActor*> TempActors;
					CmpView->GetOverlappingActors(TempActors);
					OverlappingActors.Append(TempActors);
				}
				for (auto& M : MeshInfo.MeshComps)
				{
					TSet<AActor*> TempActors;
					CmpView->GetOverlappingActors(TempActors);
					OverlappingActors.Append(TempActors);
				}
			}
		}

		if (CmpView)
		{
			IngoreActors.Add(CmpView);
			TSet<AActor*> TempActors;
			CmpView->GetOverlappingActors(TempActors);
			for (auto& Actor : TempActors)
			{
				if (IgnoreActorOverlap(Actor))
				{
					continue;
				}
				auto OverlapView = Cast<ADSBaseView>(Actor);
				if (OverlapView)
				{
					if (OverlapView->GetModelType() != EDSModelType::E_Custom_Board)
					{
						OverlappingActors.Add(OverlapView);
					}
				}
				else
				{
					OverlappingActors.Add(Actor);
				}
			}
		}
	}

	bool bShouldEndOverlap = true;
	for (AActor* OverlappingActor : OverlappingActors)
	{
		if (IngoreActors.Contains(OverlappingActor))
		{
			continue;
		}
		AActor* ActualActor = OverlappingActor;
		while (ActualActor != nullptr && ActualActor->ActorHasTag(TEXT("Cmp")))
		{
			ActualActor = ActualActor->GetOwner();
		}

		if (IngoreActors.Contains(ActualActor))
		{
			continue;
		}

		if (ActualActor != nullptr && ActualActor->IsA<ADSBaseView>() && !ActualActor->IsA<ADSGizmoView>())
		{
			bShouldEndOverlap = false;
			break;
		}
	}

	return bShouldEndOverlap;
}

void ADSCupboardBaseView::SetNodeUUIDInComponentTags(UActorComponent* InComponent, const FString& InNodeUUID, const FString& InNodeName)
{
	InComponent->ComponentTags.RemoveAll([](const FName& Tag) {
		return Tag.ToString().Find(TEXT("NodeUUID_")) == 0 || Tag.ToString().Find(TEXT("NodeName_")) == 0;
		});

	InComponent->ComponentTags.Add(FName(FString::Printf(TEXT("NodeUUID_%s"), *InNodeUUID)));

	InComponent->ComponentTags.Add(FName(FString::Printf(TEXT("NodeName_%s"), *InNodeName)));
}

void ADSCupboardBaseView::SetNodeUUIDInActorTags(AActor* InActor, const FString& InNodeUUID, const FString& InNodeName)
{
	InActor->Tags.RemoveAll([](const FName& Tag) {
		return Tag.ToString().Find(TEXT("NodeUUID_")) == 0 || Tag.ToString().Find(TEXT("NodeName_")) == 0;
		});

	InActor->Tags.Add(FName(FString::Printf(TEXT("NodeUUID_%s"), *InNodeUUID)));

	InActor->Tags.Add(FName(FString::Printf(TEXT("NodeName_%s"), *InNodeName)));
}
