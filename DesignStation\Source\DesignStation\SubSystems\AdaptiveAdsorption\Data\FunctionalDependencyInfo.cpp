// Fill out your copyright notice in the Description page of Project Settings.


#include "FunctionalDependencyInfo.h"

void FFunctionalDependencyInfo::AddDependentNode(const FFunctionalDependencySubNodeInfo& InSubNodeInfo, TWeakPtr<FFunctionalDependencyInfo> NodeInfo)
{
	DependentNodes.Add(InSubNodeInfo, NodeInfo);
}

void FFunctionalDependencyInfo::AddBeDenendentNode(TSharedRef<FFunctionalDependencyInfo> InDependencyInfo, EAdaptationDirection InDir)
{
	BeDependentNodes.Add(InDependencyInfo.ToWeakPtr(), InDir);
}

void FFunctionalDependencyInfo::ClearDependentNode()
{
	for (auto & Iter: DependentNodes)
	{
		if (Iter.Value.IsValid())
		{
			Iter.Value.Pin()->RemoveBeDependentNode(AsShared());
		}
	}

	DependentNodes.Empty();
}

void FFunctionalDependencyInfo::RemoveBeDependentNode(TSharedRef<FFunctionalDependencyInfo> BeDependencyInfo)
{
	if (BeDependentNodes.Contains(BeDependencyInfo.ToWeakPtr()))
	{
		BeDependentNodes.Remove(BeDependencyInfo.ToWeakPtr());
	}
}

bool FFunctionalDependencyInfo::BeDependentBy(const FString& UUID)
{
	for (auto Iter : BeDependentNodes)
	{
		if (Iter.Key.IsValid())
		{
			if (Iter.Key.Pin()->GetUUID().Equals(UUID))
			{
				return true;
			}
		}
	}
	return false;
}

FFunctionalDependencySubNodeInfo FFunctionalDependencyInfo::GetDependencySubNode(const FString& InNodeUUID, bool bSubNodeUUID) const
{

	for (const auto& Iter : DependentNodes)
	{
		if (Iter.Key.NodeUUID == InNodeUUID && !bSubNodeUUID
			|| Iter.Key.SubNodeUUID == InNodeUUID && bSubNodeUUID)
		{
			return Iter.Key;
		}
	}
	return FFunctionalDependencySubNodeInfo();

	// TODO: 在此处插入 return 语句
}

void FFunctionalDependencyInfo::OnCopy(const FFunctionalDependencyInfo& InInfo)
{
	UUID = InInfo.UUID;
	bEnablePassiveAdaptation = InInfo.bEnablePassiveAdaptation;
	// 清空现有数据
	DependentNodes.Empty();
	BeDependentNodes.Empty();
	DependentNodesCache.Empty();
	BeDependentNodesCache.Empty();
	// 复制 DependentNodes
	for (const auto& Pair : InInfo.DependentNodes)
	{
		DependentNodes.Add(Pair.Key, Pair.Value);
	}
	// 复制 BeDependentNodes
	for (const auto& Pair : InInfo.BeDependentNodes)
	{
		BeDependentNodes.Add(Pair.Key, Pair.Value);
	}
	// 复制缓存数据
	DependentNodesCache = InInfo.DependentNodesCache;
	BeDependentNodesCache = InInfo.BeDependentNodesCache;
}

TArray<TWeakPtr<FFunctionalDependencyInfo>> FFunctionalDependencyInfo::GetBeDependentNodesWithDepth()
{
	// 按照依赖层级排序
	TMap<TWeakPtr<FFunctionalDependencyInfo>, int32> NodeLevels;
	TArray<TWeakPtr<FFunctionalDependencyInfo>> SortedNodes;

	// 递归计算依赖层级
	std::function<void(const TWeakPtr<FFunctionalDependencyInfo>&, TMap<TWeakPtr<FFunctionalDependencyInfo>, int32>&, TSet<FString>&,int32)> GetNodeLevel;
	GetNodeLevel = [&](const TWeakPtr<FFunctionalDependencyInfo>& Node, TMap<TWeakPtr<FFunctionalDependencyInfo>, int32>& Visited,TSet<FString>& Path,int32 Levels) -> void
		{
			if (!Node.IsValid()) return ;
			FString UUID = Node.Pin()->GetUUID();
			if (Path.Contains(UUID))
			{
				return;
			}
			Path.Add(UUID);
			if (Visited.Contains(Node)&& Visited[Node]> Levels) return;
			Visited.Add(Node,Levels);

			const auto& BeDependentNodes = Node.Pin()->GetBeDependentNodes();
			for (const auto& Pair : BeDependentNodes)
			{
				if (Pair.Key.IsValid())
				{ 
					
					GetNodeLevel(Pair.Key, Visited, Path, Levels + 1);
				}
			}
			return;
		};
	// 计算每个节点的层级

	for (const auto& Node : BeDependentNodes)
	{
		TSet<FString> Path;
		GetNodeLevel(Node.Key, NodeLevels,Path, 1);
	}
	NodeLevels.GenerateKeyArray(SortedNodes);
	// 按层级升序排序
	SortedNodes.Sort([&](const TWeakPtr<FFunctionalDependencyInfo>& A, const TWeakPtr<FFunctionalDependencyInfo>& B)
		{
			FString UUIDA = A.IsValid() ? A.Pin()->GetUUID() : TEXT("");
			FString UUIDB = B.IsValid() ? B.Pin()->GetUUID() : TEXT("");
			return NodeLevels.FindRef(A) < NodeLevels.FindRef(B);
		});
	return SortedNodes;
}

void FFunctionalDependencyInfo::Serialization(TSharedRef<TJsonWriter<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>> JsonWriter)
{
	// 序列化基本信息
	JsonWriter->WriteValue(TEXT("UUID"), UUID);

	JsonWriter->WriteValue(TEXT("bEnablePassiveAdaptation"), bEnablePassiveAdaptation);

	// 序列化 DependentNodes - 只序列化有效弱指针
	JsonWriter->WriteArrayStart(TEXT("DependentNodes"));
	{
		for (auto& Pair : DependentNodes)
		{
			if (TSharedPtr<FFunctionalDependencyInfo> PinnedInfo = Pair.Value.Pin())
			{
				JsonWriter->WriteObjectStart();

				// 序列化键 (SubNodeInfo)
				JsonWriter->WriteObjectStart(TEXT("Key"));
				Pair.Key.Serialization(JsonWriter);
				JsonWriter->WriteObjectEnd();

				// 序列化值 (依赖节点的UUID)
				JsonWriter->WriteValue(TEXT("Value"), PinnedInfo->UUID);

				JsonWriter->WriteObjectEnd();
			}
		}
	}
	JsonWriter->WriteArrayEnd();

	// 序列化 BeDependentNodes - 只序列化有效弱指针
	JsonWriter->WriteArrayStart(TEXT("BeDependentNodes"));
	for (const auto& Pair : BeDependentNodes) 
	{
		if (TSharedPtr<FFunctionalDependencyInfo> PinnedInfo = Pair.Key.Pin()) 
		{
			JsonWriter->WriteObjectStart();
			JsonWriter->WriteValue(TEXT("Key"), PinnedInfo->UUID);
			JsonWriter->WriteValue(TEXT("Value"), static_cast<int32>(Pair.Value));
			JsonWriter->WriteObjectEnd();
		}
	}
	JsonWriter->WriteArrayEnd();
}

void FFunctionalDependencyInfo::Deserialization(const TSharedPtr<FJsonObject>& InJsonData)
{
	if (!InJsonData.IsValid()) 
		return;

	// 清空现有数据
	DependentNodes.Empty();
	BeDependentNodes.Empty();
	DependentNodesCache.Empty();
	BeDependentNodesCache.Empty();

	// 反序列化基本信息
	InJsonData->TryGetStringField(TEXT("UUID"), UUID);
	InJsonData->TryGetBoolField(TEXT("bEnablePassiveAdaptation"), bEnablePassiveAdaptation);
	// 反序列化 DependentNodes (第一阶段: 只存储数据，不重建指针)
	const TArray<TSharedPtr<FJsonValue>>* DependentNodesArray;
	if (InJsonData->TryGetArrayField(TEXT("DependentNodes"), DependentNodesArray)) 
	{
		for (const TSharedPtr<FJsonValue>& Item : *DependentNodesArray) 
		{
			const TSharedPtr<FJsonObject> ItemObj = Item->AsObject();
			if (ItemObj.IsValid()) {
				// 解析键 (SubNodeInfo)
				FFunctionalDependencySubNodeInfo Key;
				const TSharedPtr<FJsonObject> KeyObj = ItemObj->GetObjectField(TEXT("Key"));
				if (KeyObj.IsValid()) {
					Key.Deserialization(KeyObj);
				}

				// 存储值 (依赖节点的UUID)
				FString ValueUUID;
				if (ItemObj->TryGetStringField(TEXT("Value"), ValueUUID)) 
				{
					DependentNodesCache.Add(Key, ValueUUID);
				}
			}
		}
	}

	// 反序列化 BeDependentNodes (第一阶段: 只存储数据)
	const TArray<TSharedPtr<FJsonValue>>* BeDependentNodesArray;
	if (InJsonData->TryGetArrayField(TEXT("BeDependentNodes"), BeDependentNodesArray)) 
	{
		for (const TSharedPtr<FJsonValue>& Item : *BeDependentNodesArray) 
		{
			const TSharedPtr<FJsonObject> ItemObj = Item->AsObject();
			if (ItemObj.IsValid()) 
			{
				FString KeyUUID;
				int32 ValueDir;
				if (ItemObj->TryGetStringField(TEXT("Key"), KeyUUID) &&
					ItemObj->TryGetNumberField(TEXT("Value"), ValueDir)) 
				{
					if (!KeyUUID.IsEmpty())
					{
						// 创建缓存，用于后续阶段重建
						BeDependentNodesCache.Add(KeyUUID, static_cast<EAdaptationDirection>(ValueDir));
					}
				}
			}
		}
	}
}

const TSharedPtr<FFunctionalDependencyInfo>& FFunctionalDependencyMap::AddDependencyInfo(const FString& InUUID)
{
	if (!DependencyInfoMap.Contains(InUUID))
	{
		TSharedPtr<FFunctionalDependencyInfo> NodeDependencyInfo = MakeShared<FFunctionalDependencyInfo>(InUUID);
		DependencyInfoMap.Add(InUUID,NodeDependencyInfo);
	}
	return DependencyInfoMap[InUUID];
}

const TSharedPtr<FFunctionalDependencyInfo>& FFunctionalDependencyMap::AddDependencyInfo(const FString& InUUID, bool bInPassiveAdaptation)
{
	if (!DependencyInfoMap.Contains(InUUID))
	{
		TSharedPtr<FFunctionalDependencyInfo> NodeDependencyInfo = MakeShared<FFunctionalDependencyInfo>(InUUID,bInPassiveAdaptation);
		DependencyInfoMap.Add(InUUID, NodeDependencyInfo);
	}
	return DependencyInfoMap[InUUID];
}

void FFunctionalDependencyMap::ClearDependencyInfo(const FString& SourceUUID)
{
	if (DependencyInfoMap.Contains(SourceUUID))
	{
		auto DependencyInfo = DependencyInfoMap[SourceUUID];
		DependencyInfo->ClearDependentNode();
	}
}

 TSharedPtr<FFunctionalDependencyInfo> FFunctionalDependencyMap::GetDependencyInfo(const FString& SourceUUID) const
{
	if (DependencyInfoMap.Contains(SourceUUID))
	{
		return DependencyInfoMap[SourceUUID];
	}
	return nullptr;
	// TODO: 在此处插入 return 语句
}

 void FFunctionalDependencyMap::RemoveDependencyInfo(const FString& SourceUUID)
 {
	 if (DependencyInfoMap.Contains(SourceUUID))
	 {
		 DependencyInfoMap.Remove(SourceUUID);
	 }
 }

 void FFunctionalDependencyMap::ReplaceDependencyInfo(const FString& SourceUUID, const FString& TargetUUID)
 {
	 TSharedPtr<FFunctionalDependencyInfo> TempInfo = GetDependencyInfo(SourceUUID);
	 if (TempInfo.IsValid())
	 {
		 if (!DependencyInfoMap.Contains(TargetUUID))
		 {
			 RemoveDependencyInfo(SourceUUID);
			 DependencyInfoMap.Add(TargetUUID, TempInfo);
		 }
	 }
 }

 void FFunctionalDependencyMap::OnCopy(const TSharedPtr<FFunctionalDependencyMap>& InMap)
 {
	 if (InMap)
	 {
		 DependencyInfoMap.Empty();
		 const auto& MapInfoRef = InMap->GetDependencyInfoMapRef();
		 for (auto & Info : MapInfoRef)
		 {
			 auto Node = AddDependencyInfo(Info.Key);
			 Node->OnCopy(*Info.Value);
		 }
	 }
 }

 void FFunctionalDependencyMap::Serialization(TSharedRef<TJsonWriter<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>> JsonWriter)
 {
	 // 序列化整个映射
	 JsonWriter->WriteArrayStart(TEXT("DependencyInfoMap"));
	 for (const auto& Pair : DependencyInfoMap) 
	 {
		 if (Pair.Value.IsValid()) 
		 {
			 JsonWriter->WriteObjectStart();
			 {
				 JsonWriter->WriteValue(TEXT("Key"), Pair.Key);

				 // 序列化值 (FFunctionalDependencyInfo)
				 JsonWriter->WriteObjectStart(TEXT("Value"));
				 {
					 Pair.Value->Serialization(JsonWriter);
				 }
				 JsonWriter->WriteObjectEnd();
			 }
			 JsonWriter->WriteObjectEnd();
		 }
	 }
	 JsonWriter->WriteArrayEnd();
 }

 void FFunctionalDependencyMap::Deserialization(const TSharedPtr<FJsonObject>& InJsonData)
 {
	 if (!InJsonData.IsValid()) 
		 return;

	 // 清空现有数据
	 DependencyInfoMap.Empty();

	 // 第一步: 创建所有节点但不建立链接
	 const TArray<TSharedPtr<FJsonValue>>* MapArray;
	 if (InJsonData->TryGetArrayField(TEXT("DependencyInfoMap"), MapArray)) 
	 {
		 for (const TSharedPtr<FJsonValue>& Item : *MapArray) 
		 {
			 const TSharedPtr<FJsonObject> ItemObj = Item->AsObject();
			 if (ItemObj.IsValid()) 
			 {
				 FString Key;
				 if (ItemObj->TryGetStringField(TEXT("Key"), Key)) 
				 {
					 // 创建节点
					 TSharedPtr<FFunctionalDependencyInfo> NewInfo = AddDependencyInfo(Key);

					 // 反序列化节点数据
					 const TSharedPtr<FJsonObject> ValueObj = ItemObj->GetObjectField(TEXT("Value"));
					 if (ValueObj.IsValid() && NewInfo.IsValid()) 
					 {
						 NewInfo->Deserialization(ValueObj);
					 }
				 }
			 }
		 }
	 }

	 // 第二步: 重建所有链接关系
	 for (auto& Pair : DependencyInfoMap) 
	 {
		 TSharedPtr<FFunctionalDependencyInfo> CurrentInfo = Pair.Value;

		 // 重建 DependentNodes 链接
		 for (auto& DepPair : CurrentInfo->DependentNodesCache)
		 {
			 TWeakPtr<FFunctionalDependencyInfo> FoundNodePtr;
			 if (DepPair.Value.IsEmpty())
			 {
				 FoundNodePtr = nullptr;
			 }
			 else if (DependencyInfoMap.Contains(DepPair.Value))
			 {
				 FoundNodePtr = DependencyInfoMap[DepPair.Value];
			 }
			 if (FoundNodePtr.IsValid())
			 {
				 // 如果 DepPair.Key 的 UUID 为空，则不添加
				 if (DepPair.Key.NodeUUID.IsEmpty() || DepPair.Key.SubNodeUUID.IsEmpty())
				 {
					 continue;
				 }
				 CurrentInfo->AddDependentNode(DepPair.Key, FoundNodePtr);
			 }
		 }

		 // 重建 BeDependentNodes 链接
		 TMap<TWeakPtr<FFunctionalDependencyInfo>, EAdaptationDirection> NewBeDependentNodes;
		 for (auto& BeDepPair : CurrentInfo->BeDependentNodesCache)
		 {

			 TWeakPtr<FFunctionalDependencyInfo> FoundBeNodePtr;
			 // 如果 BeDepPair.Key 的 UUID 为空，则不添加
			 if (BeDepPair.Key.IsEmpty())
			 {
				 continue;
			 }
			 else if (DependencyInfoMap.Contains(BeDepPair.Key))
			 {
				 FoundBeNodePtr = DependencyInfoMap[BeDepPair.Key];
				 if (FoundBeNodePtr.IsValid())
				 {
					 NewBeDependentNodes.Add(FoundBeNodePtr, BeDepPair.Value);
				 }
			 }
		 }
		 CurrentInfo->BeDependentNodes = MoveTemp(NewBeDependentNodes);
	 }
 }

 void FFunctionalDependencySubNodeInfo::Serialization(TSharedRef<TJsonWriter<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>> JsonWriter)
 {
	 JsonWriter->WriteValue(TEXT("NodeUUID"), NodeUUID);
	 JsonWriter->WriteValue(TEXT("SubNodeUUID"), SubNodeUUID);
	 JsonWriter->WriteValue(TEXT("Direction"), static_cast<int32>(Direction));
 }

 void FFunctionalDependencySubNodeInfo::Deserialization(const TSharedPtr<FJsonObject>& InJsonData)
 {
	 if (InJsonData.IsValid()) 
	 {
		 InJsonData->TryGetStringField(TEXT("NodeUUID"), NodeUUID);
		 InJsonData->TryGetStringField(TEXT("SubNodeUUID"), SubNodeUUID);

		 int32 DirValue;
		 if (InJsonData->TryGetNumberField(TEXT("Direction"), DirValue)) 
		 {
			 Direction = static_cast<EAdaptationDirection>(DirValue);
		 }
	 }
 }
