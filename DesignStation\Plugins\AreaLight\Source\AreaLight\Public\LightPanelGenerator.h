#pragma once

#include "CoreMinimal.h"
#include "LightPanel.h"
#include "UObject/NoExportTypes.h"
#include "LightPanelGenerator.generated.h"

UCLASS()
class AREALIGHT_API ULightPanelGenerator : public UGameInstanceSubsystem
{
	GENERATED_BODY()

public:
	static ULightPanelGenerator* GetInstance();

	ULightPanelGenerator();

	virtual ~ULightPanelGenerator() override;

	TArray<FLightPanelData> GeneratePanels(const TArray<FVector>& InArea, const TArray<TPair<FVector, FVector>>& InWindows, double MinSize = 50, double Spacing = 50, double WallDistance = 100);
};
