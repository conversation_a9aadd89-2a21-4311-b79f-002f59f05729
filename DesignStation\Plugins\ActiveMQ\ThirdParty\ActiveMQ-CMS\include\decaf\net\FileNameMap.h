/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef _DECAF_NET_FILENAMEMAP_H_
#define _DECAF_NET_FILENAMEMAP_H_

#include <decaf/util/Config.h>

#include <string>

namespace decaf {
namespace net {

    /**
     * Defines a scheme for mapping a filename type to a MIME content type. Mainly used
     * by URLStreamHandler for determining the right content handler to handle the resource.
     *
     * @since 1.0
     */
    class DECAF_API FileNameMap {
    public:

        virtual ~FileNameMap();

        /**
         * Determines the MIME type for a file {@code fileName} of a URL.
         *
         * @param filename
         *      the name of the file to consider.
         *
         * @return the appropriate MIME type of the given file.
         */
        virtual std::string getContentTypeFor(const std::string& filename) = 0;

    };

}}

#endif /* _DECAF_NET_FILENAMEMAP_H_ */
