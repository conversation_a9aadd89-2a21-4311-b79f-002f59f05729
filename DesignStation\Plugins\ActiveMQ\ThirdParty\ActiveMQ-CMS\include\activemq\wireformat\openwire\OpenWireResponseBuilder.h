/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef ACTIVEMQ_WIREFORMAT_OPENWIRE_OPENWIRERESPONSEBUILDER_H_
#define ACTIVEMQ_WIREFORMAT_OPENWIRE_OPENWIRERESPONSEBUILDER_H_

#include <activemq/util/Config.h>
#include <activemq/transport/mock/ResponseBuilder.h>
#include <decaf/util/LinkedList.h>
#include <decaf/lang/Pointer.h>

namespace activemq{
namespace wireformat{
namespace openwire{

    using decaf::lang::Pointer;

    /**
     * Used to allow a MockTransport to generate response commands to OpenWire Commands.
     */
    class AMQCPP_API OpenWireResponseBuilder: public transport::mock::ResponseBuilder {
    public:

        OpenWireResponseBuilder() {}
        virtual ~OpenWireResponseBuilder() {}

        virtual Pointer<commands::Response> buildResponse(const Pointer<commands::Command> command);

        virtual void buildIncomingCommands(const Pointer<commands::Command> command,
                                           decaf::util::LinkedList<Pointer<commands::Command> >& queue);

    };

}}}

#endif /*ACTIVEMQ_WIREFORMAT_OPENWIRE_OPENWIRERESPONSEBUILDER_H_*/
