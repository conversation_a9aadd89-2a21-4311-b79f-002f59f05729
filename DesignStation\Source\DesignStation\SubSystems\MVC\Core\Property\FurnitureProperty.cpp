#include "FurnitureProperty.h"
#include "CoreMinimal.h"
#include "CommonProperty.h"
#include "JsonObject.h"

extern const FString PARAM_H_STR;
extern const FString PARAM_W_STR;
extern const FString PARAM_D_STR;

void FDSFurnitureBaseProperty::Serialization(TSharedRef<TJsonWriter<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>> JsonWriter)
{
	FDSBaseProperty::Serialization(JsonWriter);

	JsonWriter->WriteObjectStart(TEXT("FDSFurnitureBaseProperty"));
	{
		JsonWriter->WriteValue("SourceType", static_cast<int32>(FileSourceProperty.SourceType));
		JsonWriter->WriteValue("SourcePath", FileSourceProperty.SourcePath);
		JsonWriter->WriteValue("bAsyncLoading", FileSourceProperty.bAsyncLoading);

		JsonWriter->WriteValue("Type", static_cast<int32>(BusinessInfo.Type));
		JsonWriter->WriteValue("Brand", BusinessInfo.Brand);
		JsonWriter->WriteValue("Depth", BusinessInfo.Depth);
		JsonWriter->WriteValue("Width", BusinessInfo.Width);
		JsonWriter->WriteValue("Height", BusinessInfo.Height);
		JsonWriter->WriteValue("Id", BusinessInfo.Id);
		JsonWriter->WriteValue("Code", BusinessInfo.Code);
		JsonWriter->WriteValue("FolderId", BusinessInfo.FolderId);
		JsonWriter->WriteValue("FolderCode", BusinessInfo.FolderCode);
		JsonWriter->WriteValue("Name", BusinessInfo.Name);
		JsonWriter->WriteValue("ProductImg", BusinessInfo.ProductImg);
		JsonWriter->WriteValue("IsCollect", BusinessInfo.IsCollect);
		JsonWriter->WriteValue("PlacementRules", BusinessInfo.PlacementRules);
		JsonWriter->WriteValue("CategoryId", BusinessInfo.CategoryId);
		
		JsonWriter->WriteValue(TEXT("RelativeLocation"),RelativeLocation.ToString());
		JsonWriter->WriteValue(TEXT("RelativeRotation"),RelativeRotation.ToString());

		JsonWriter->WriteArrayStart(TEXT("ResourceList"));
		{
			for (auto& It : BusinessInfo.ResourceList)
			{
				JsonWriter->WriteObjectStart();
				{
					JsonWriter->WriteValue("Type", static_cast<int32>(It.Type));
					JsonWriter->WriteValue("MD5", It.MD5);
					JsonWriter->WriteValue("Name", It.Name);
					JsonWriter->WriteValue("FilePath", It.FilePath);
				}
				JsonWriter->WriteObjectEnd();
			}
			JsonWriter->WriteArrayEnd();
		}

		JsonWriter->WriteArrayStart(TEXT("TagList"));
		{
			for (auto& It : BusinessInfo.TagList)
			{
				JsonWriter->WriteObjectStart();
				{
					JsonWriter->WriteValue("Name", It.Name);
					JsonWriter->WriteValue("Id", It.Id);
					JsonWriter->WriteValue("Code", It.Code);
				}
				JsonWriter->WriteObjectEnd();
			}
			JsonWriter->WriteArrayEnd();
		}


	}
	JsonWriter->WriteObjectEnd();
}

void FDSFurnitureBaseProperty::Deserialization(const TSharedPtr<FJsonObject>& InJsonData)
{
	FDSBaseProperty::Deserialization(InJsonData);

	TSharedPtr<FJsonObject> JsonData = InJsonData->GetObjectField(TEXT("FDSFurnitureBaseProperty"));

	FileSourceProperty.SourceType = static_cast<EDSSourceType>(JsonData->GetIntegerField(TEXT("SourceType")));
	FileSourceProperty.SourcePath = JsonData->GetStringField(TEXT("SourcePath"));
	FileSourceProperty.bAsyncLoading = JsonData->GetBoolField(TEXT("bAsyncLoading"));

	BusinessInfo.Type = static_cast<EDSResourceType>(JsonData->GetIntegerField(TEXT("Type")));
	BusinessInfo.Brand = JsonData->GetStringField(TEXT("Brand"));
	BusinessInfo.Depth = JsonData->GetStringField(TEXT("Depth"));
	BusinessInfo.Height = JsonData->GetStringField(TEXT("Height"));
	BusinessInfo.Width = JsonData->GetStringField(TEXT("Width"));
	BusinessInfo.Id = JsonData->GetStringField(TEXT("Id"));
	BusinessInfo.Code = JsonData->GetStringField(TEXT("Code"));
	BusinessInfo.FolderId = JsonData->GetStringField(TEXT("FolderId"));
	BusinessInfo.FolderCode = JsonData->GetStringField(TEXT("FolderCode"));
	BusinessInfo.Name = JsonData->GetStringField(TEXT("Name"));
	BusinessInfo.ProductImg = JsonData->GetStringField(TEXT("ProductImg"));
	BusinessInfo.IsCollect = JsonData->GetBoolField(TEXT("IsCollect"));
	BusinessInfo.PlacementRules = JsonData->GetStringField(TEXT("PlacementRules"));
	BusinessInfo.CategoryId = JsonData->GetStringField(TEXT("CategoryId"));

	RelativeLocation.InitFromString(JsonData->GetStringField(TEXT("RelativeLocation")));
	RelativeRotation.InitFromString(JsonData->GetStringField(TEXT("RelativeRotation")));

	const TArray<TSharedPtr<FJsonValue>>& ResourceList_JsonObjs = JsonData->GetArrayField(TEXT("ResourceList"));
	for (const auto& IM : ResourceList_JsonObjs)
	{
		TSharedPtr<FJsonObject> JsonItem = IM->AsObject();
		FDSResourceFile RF;
		RF.Type = static_cast<EDSResourceQuality>(JsonItem->GetIntegerField(TEXT("Type")));
		RF.MD5 = JsonItem->GetStringField(TEXT("MD5"));
		RF.Name = JsonItem->GetStringField(TEXT("Name"));
		RF.FilePath = JsonItem->GetStringField(TEXT("FilePath"));
		BusinessInfo.ResourceList.Add(RF);
	}

	const TArray<TSharedPtr<FJsonValue>>& TagList_JsonObjs = JsonData->GetArrayField(TEXT("TagList"));
	for (const auto& IM : TagList_JsonObjs)
	{
		TSharedPtr<FJsonObject> JsonItem = IM->AsObject();
		FDSTagInfo RT;
		RT.Name = JsonItem->GetStringField(TEXT("Name"));
		RT.Id = JsonItem->GetStringField(TEXT("Id"));
		RT.Code = JsonItem->GetStringField(TEXT("Code"));
		BusinessInfo.TagList.Add(RT);
	}
}

void FDSMoldingCeilingProperty::InitSizeData(const FRefToLocalFileData& InFileData)
{

	SizeProperty.Width = 300.0f;
	SizeProperty.bWidthCanEdit = true;
	SizeProperty.Height = 300.0f;
	SizeProperty.bHeightCanEdit = true;
	SizeProperty.Depth = 300.0f;
	SizeProperty.bDepthCanEdit = true;

	auto FindValue = [&InFileData](const FString& InKey, FParameterData& OutValue) -> bool {
		for (auto& Ite : InFileData.ParamDatas)
		{
			if (Ite.Data.name.Equals(InKey))
			{
				OutValue = Ite;
				return true;
			}
		}

		return false;
		};

	FParameterData WidthValue;
	if (FindValue(PARAM_W_STR, WidthValue))
	{
		SizeProperty.Width = FCString::Atof(*WidthValue.Data.value);
		SizeProperty.bWidthCanEdit = FCString::Atoi(*WidthValue.Data.editable) > 0;
		MaxWidthValue = FCString::Atof(*WidthValue.Data.max_value);
		MinWidthValue = FCString::Atof(*WidthValue.Data.min_value);
	}

	FParameterData HeightValue;
	if (FindValue(PARAM_H_STR, HeightValue))
	{
		SizeProperty.Height = FCString::Atof(*HeightValue.Data.value);
		SizeProperty.bHeightCanEdit = FCString::Atoi(*HeightValue.Data.editable) > 0;
		MaxHeightValue = FCString::Atof(*HeightValue.Data.max_value);
		MinHeightValue = FCString::Atof(*HeightValue.Data.min_value);
	}

	FParameterData DepthValue;
	if (FindValue(PARAM_D_STR, DepthValue))
	{
		SizeProperty.Depth = FCString::Atof(*DepthValue.Data.value);
		SizeProperty.bDepthCanEdit = FCString::Atoi(*DepthValue.Data.editable) > 0;
		MaxDepthValue = FCString::Atof(*DepthValue.Data.max_value);
		MinDepthValue = FCString::Atof(*DepthValue.Data.min_value);
	}
}

void FDSMoldingCeilingProperty::Serialization(TSharedRef<TJsonWriter<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>> JsonWriter)
{
	FDSFurnitureBaseProperty::Serialization(JsonWriter);

	JsonWriter->WriteObjectStart(TEXT("FDSMoldingCeilingProperty"));
	{
		JsonWriter->WriteValue("bAdaptive", bAdaptive);
		JsonWriter->WriteValue("AdaptiveRoofUUID", AdaptiveRoofUUID);
		JsonWriter->WriteValue("ModelFolderId", ModelFolderId);
		JsonWriter->WriteValue("MaxWidthValue", MaxWidthValue);
		JsonWriter->WriteValue("MaxHeightValue", MaxHeightValue);
		JsonWriter->WriteValue("MaxDepthValue", MaxDepthValue);
		JsonWriter->WriteValue("MinWidthValue", MinWidthValue);
		JsonWriter->WriteValue("MinHeightValue", MinHeightValue);
		JsonWriter->WriteValue("MinDepthValue", MinDepthValue);
	}
	JsonWriter->WriteObjectEnd();
}

void FDSMoldingCeilingProperty::Deserialization(const TSharedPtr<FJsonObject>& InJsonData)
{
	FDSFurnitureBaseProperty::Deserialization(InJsonData);

	TSharedPtr<FJsonObject> JsonData = InJsonData->GetObjectField(TEXT("FDSMoldingCeilingProperty"));
	bAdaptive = JsonData->GetBoolField(TEXT("bAdaptive"));
	AdaptiveRoofUUID = JsonData->GetStringField(TEXT("AdaptiveRoofUUID"));
	ModelFolderId = JsonData->GetStringField(TEXT("ModelFolderId"));
	MaxWidthValue = JsonData->GetNumberField(TEXT("MaxWidthValue"));
	MaxHeightValue = JsonData->GetNumberField(TEXT("MaxHeightValue"));
	MaxDepthValue = JsonData->GetNumberField(TEXT("MaxDepthValue"));
	MinWidthValue = JsonData->GetNumberField(TEXT("MinWidthValue"));
	MinHeightValue = JsonData->GetNumberField(TEXT("MinHeightValue"));
	MinDepthValue = JsonData->GetNumberField(TEXT("MinDepthValue"));
}

bool FDSMoldingCeilingProperty::IsSizeVaild() const
{
	return SizeProperty.Width >= MinWidthValue && SizeProperty.Width <= MaxWidthValue &&
		SizeProperty.Height >= MinHeightValue && SizeProperty.Height <= MaxHeightValue &&
		SizeProperty.Depth >= MinDepthValue && SizeProperty.Depth <= MaxDepthValue;
}

void FDSSoftFurnitureProperty::Serialization(TSharedRef<TJsonWriter<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>> JsonWriter)
{
	FDSFurnitureBaseProperty::Serialization(JsonWriter);
}

void FDSSoftFurnitureProperty::Deserialization(const TSharedPtr<FJsonObject>& InJsonData)
{
	FDSFurnitureBaseProperty::Deserialization(InJsonData);
}
