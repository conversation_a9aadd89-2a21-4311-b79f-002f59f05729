// Fill out your copyright notice in the Description page of Project Settings.


#include "AdapationIntersectionExecuter.h"
#include "Kismet/KismetSystemLibrary.h"
#include <JsonSerializerMacros.h>


DEFINE_LOG_CATEGORY(AdaptationExecuterLog)

#ifndef ADAPTIVE_TOLERANCE
#define ADAPTIVE_TOLERANCE  0.01f//UE_DOUBLE_KINDA_SMALL_NUMBER
#endif // !ADAPTIVE_TOLERANCE


FIntersectionDynamicMesh::FIntersectionDynamicMesh()
{
}

FIntersectionDynamicMesh::FIntersectionDynamicMesh(const FVector& InExtent, const FVector& InCenter, const FQuat& InRotation, EIntersectionDataType InType)
{
    Initialized(InExtent, InCenter, InRotation);
    IntersectionType = InType;
}

FIntersectionDynamicMesh::~FIntersectionDynamicMesh()
{
}

void FIntersectionDynamicMesh::Initialized(const FVector& InExtent, const FVector& InCenter, const FQuat& InRotation)
{
    FOrientedBox3d& OriBox = BoxGen.Box;
    OriBox.Frame.Origin = InCenter;
    OriBox.Frame.Rotation = FQuaterniond(InRotation);
    OriBox.Extents = InExtent;

    BoxGen.EdgeVertices = FIndex3i(2, 2, 2);
    BoxGen.Generate();

    SourceMesh.Copy(&BoxGen);
    DynamicMeshTree.SetMesh(&SourceMesh);
    DynamicMeshTree.SetTolerance(ADAPTIVE_TOLERANCE);

}
void FIntersectionDynamicMesh::UpdateExtentsAndLocation(const FVector& InExtents, const FVector& InCenter)
{

    bool ExtentsModified, CenterModified;
    UpdateExtentsAndLocation(InExtents, InCenter, ExtentsModified, CenterModified);
}
void FIntersectionDynamicMesh::UpdateExtentsAndLocation(const FVector& InExtents, const FVector& InCenter, bool& OutExtentsModified, bool& OutCenterModified)
{

    FOrientedBox3d& OriBox = BoxGen.Box;

    OutExtentsModified = !OriBox.Extents.Equals(InExtents, ADAPTIVE_TOLERANCE);

    OutCenterModified = !OriBox.Center().Equals(InCenter, ADAPTIVE_TOLERANCE);

    if (OutExtentsModified || OutCenterModified)
    {
        OriBox.Frame.Origin = InCenter;
        OriBox.Extents = InExtents;
        BoxGen.Generate();
        SourceMesh.Copy(&BoxGen);
        DynamicMeshTree.SetMesh(&SourceMesh);

    }
}
bool FIntersectionDynamicMesh::RayHit(const FRay& Ray,const IMeshSpatial::FQueryOptions& Options,int& OutTriID, double& OutDistance)
{
    bool bHited = GetDynamicMeshTree().TestAnyHitTriangle(Ray, Options);

    if (!bHited)
    {
        return false;
    }
  
    GetDynamicMeshTree().FindNearestHitTriangle(Ray, OutDistance, OutTriID, Options);
    return true;
}
bool FIntersectionDynamicMesh::UpdateLocation(const FVector& InCenter)
{

    FOrientedBox3d& OriBox = BoxGen.Box;

    bool bCenterModified = !OriBox.Center().Equals(InCenter, ADAPTIVE_TOLERANCE);
    if (bCenterModified)
    {
        OriBox.Frame.Origin = InCenter;
        BoxGen.Generate();
        SourceMesh.Copy(&BoxGen);
        DynamicMeshTree.SetMesh(&SourceMesh);
    }

    return bCenterModified;
}

bool FIntersectionDynamicMesh::UpdateRotation(const FQuat& InRotation)
{
    FOrientedBox3d& OriBox = BoxGen.Box;
 
    bool bRotationModified = !OriBox.Frame.Rotation.EpsilonEqual(FQuaterniond(InRotation), ADAPTIVE_TOLERANCE);

    if (bRotationModified)
    {
        OriBox.Frame.Rotation = FQuaterniond(InRotation);
        BoxGen.Generate();
        SourceMesh.Copy(&BoxGen);
        DynamicMeshTree.SetMesh(&SourceMesh);
    }

    return bRotationModified;
}

bool FIntersectionDynamicMesh::UpdateTrans(const FVector& InCenter, const FQuat& InRotation)
{
    FOrientedBox3d& OriBox = BoxGen.Box;

    bool bRotationModified = !OriBox.Frame.Rotation.EpsilonEqual(FQuaterniond(InRotation), ADAPTIVE_TOLERANCE);

    bool bCenterModified = !OriBox.Center().Equals(InCenter, ADAPTIVE_TOLERANCE);

    if (bCenterModified || bRotationModified)
    {
        OriBox.Frame.Origin = InCenter;
        OriBox.Frame.Rotation = FQuaterniond(InRotation);

        BoxGen.Generate();
        SourceMesh.Copy(&BoxGen);
        DynamicMeshTree.SetMesh(&SourceMesh);
    }
    return (bCenterModified || bRotationModified);
}

bool FIntersectionDynamicMesh::UpdateOribox(const FOrientedBox3d& InOriBox)
{
    FOrientedBox3d& OriBox = BoxGen.Box;

    bool bRotationModified = !OriBox.Frame.Rotation.EpsilonEqual(InOriBox.Frame.Rotation, ADAPTIVE_TOLERANCE);

    bool bCenterModified = !OriBox.Center().Equals(InOriBox.Center(), ADAPTIVE_TOLERANCE);

    bool OutExtentsModified = !OriBox.Extents.Equals(InOriBox.Extents, ADAPTIVE_TOLERANCE);

    if (bRotationModified|| bCenterModified|| OutExtentsModified)
    {
        OriBox = InOriBox;
        BoxGen.Generate();
        SourceMesh.Copy(&BoxGen);
        DynamicMeshTree.SetMesh(&SourceMesh);

        return true;
    }

    return false;
}

bool FIntersectionDynamicMesh::UpdateOribox(const FVector& InCenter, const FQuaterniond& InRotation, const FVector& InExtent)
{
    FOrientedBox3d& OriBox = BoxGen.Box;
    bool bRotationModified = !OriBox.Frame.Rotation.EpsilonEqual(InRotation, ADAPTIVE_TOLERANCE);

    bool bCenterModified = !OriBox.Center().Equals(InCenter, ADAPTIVE_TOLERANCE);

    bool OutExtentsModified = !OriBox.Extents.Equals(InExtent, ADAPTIVE_TOLERANCE);

    if (bRotationModified || bCenterModified || OutExtentsModified)
    {
        OriBox.Frame.Origin = InCenter;
        OriBox.Frame.Rotation = InRotation;
        OriBox.Extents = InExtent;
        BoxGen.Generate();
        SourceMesh.Copy(&BoxGen);
        DynamicMeshTree.SetMesh(&SourceMesh);
        return true;
    }
    return false;
}


const FDynamicMeshAABBTree3& FIntersectionDynamicMesh::GetDynamicMeshTree() const
{
    return DynamicMeshTree;
}

FAxisAlignedBox3d FIntersectionDynamicMesh::GetBounds() const
{
    return DynamicMeshTree.GetBoundingBox();
}



bool FAdapationIntersectionExecuter::DoIntersectionTest(const FIntersectionDynamicMesh& SelfIntersectionMesh, const FIntersectionDynamicMesh& Other, MeshIntersection::FIntersectionsQueryResult& OutResault, bool bReportCoplanar ,const IMeshSpatial::FQueryOptions SelfQueryOption, const IMeshSpatial::FQueryOptions&  OtherQueryOptions)
{
     //quick AABB intersect test
    const auto& SelfAABB = SelfIntersectionMesh.GetBounds();
    const auto& OtherAABB = Other.GetBounds();
    
    bool bBoundsIntersect = !(
        ((!FMath::IsNearlyEqual(SelfAABB.Max.X, OtherAABB.Min.X, ADAPTIVE_TOLERANCE)) && (SelfAABB.Max.X < OtherAABB.Min.X))
        || ((!FMath::IsNearlyEqual(SelfAABB.Min.X, OtherAABB.Max.X, ADAPTIVE_TOLERANCE)) && (SelfAABB.Min.X > OtherAABB.Max.X))
        || ((!FMath::IsNearlyEqual(SelfAABB.Max.Y, OtherAABB.Min.Y, ADAPTIVE_TOLERANCE)) && (SelfAABB.Max.Y < OtherAABB.Min.Y))
        || ((!FMath::IsNearlyEqual(SelfAABB.Min.Y, OtherAABB.Max.Y, ADAPTIVE_TOLERANCE)) && (SelfAABB.Min.Y > OtherAABB.Max.Y))
        || ((!FMath::IsNearlyEqual(SelfAABB.Max.Z, OtherAABB.Min.Z, ADAPTIVE_TOLERANCE)) && (SelfAABB.Max.Z < OtherAABB.Min.Z))
        || ((!FMath::IsNearlyEqual(SelfAABB.Min.Z, OtherAABB.Max.Z, ADAPTIVE_TOLERANCE)) && (SelfAABB.Min.Z > OtherAABB.Max.Z)));

    //bool bBoundsIntersect = SelfIntersectionMesh.GetBounds().Intersects(Other.GetBounds());
    if (!bBoundsIntersect)
    {
        return false;
    }
    //排除只共点的情况
    bBoundsIntersect &= !(FMath::IsNearlyEqual(SelfAABB.Max.X, OtherAABB.Min.X, ADAPTIVE_TOLERANCE) && FMath::IsNearlyEqual(SelfAABB.Max.Y, OtherAABB.Min.Y, ADAPTIVE_TOLERANCE)
        || FMath::IsNearlyEqual(SelfAABB.Min.X, OtherAABB.Max.X, ADAPTIVE_TOLERANCE) && FMath::IsNearlyEqual(SelfAABB.Max.Y, OtherAABB.Min.Y, ADAPTIVE_TOLERANCE)
        || FMath::IsNearlyEqual(SelfAABB.Max.X, OtherAABB.Min.X, ADAPTIVE_TOLERANCE) && FMath::IsNearlyEqual(SelfAABB.Min.Y, OtherAABB.Max.Y, ADAPTIVE_TOLERANCE)
        || FMath::IsNearlyEqual(SelfAABB.Min.X, OtherAABB.Max.X, ADAPTIVE_TOLERANCE) && FMath::IsNearlyEqual(SelfAABB.Min.Y, OtherAABB.Max.Y, ADAPTIVE_TOLERANCE)

        || FMath::IsNearlyEqual(SelfAABB.Max.X, OtherAABB.Min.X, ADAPTIVE_TOLERANCE) && FMath::IsNearlyEqual(SelfAABB.Max.Z, OtherAABB.Min.Z, ADAPTIVE_TOLERANCE)
        || FMath::IsNearlyEqual(SelfAABB.Min.X, OtherAABB.Max.X, ADAPTIVE_TOLERANCE) && FMath::IsNearlyEqual(SelfAABB.Max.Z, OtherAABB.Min.Z, ADAPTIVE_TOLERANCE)
        || FMath::IsNearlyEqual(SelfAABB.Max.X, OtherAABB.Min.X, ADAPTIVE_TOLERANCE) && FMath::IsNearlyEqual(SelfAABB.Min.Z, OtherAABB.Max.Z, ADAPTIVE_TOLERANCE)
        || FMath::IsNearlyEqual(SelfAABB.Min.X, OtherAABB.Max.X, ADAPTIVE_TOLERANCE) && FMath::IsNearlyEqual(SelfAABB.Min.Z, OtherAABB.Max.Z, ADAPTIVE_TOLERANCE)

        || FMath::IsNearlyEqual(SelfAABB.Max.Y, OtherAABB.Min.Y, ADAPTIVE_TOLERANCE) && FMath::IsNearlyEqual(SelfAABB.Max.Z, OtherAABB.Min.Z, ADAPTIVE_TOLERANCE)
        || FMath::IsNearlyEqual(SelfAABB.Min.Y, OtherAABB.Max.Y, ADAPTIVE_TOLERANCE) && FMath::IsNearlyEqual(SelfAABB.Max.Z, OtherAABB.Min.Z, ADAPTIVE_TOLERANCE)
        || FMath::IsNearlyEqual(SelfAABB.Max.Y, OtherAABB.Min.Y, ADAPTIVE_TOLERANCE) && FMath::IsNearlyEqual(SelfAABB.Min.Z, OtherAABB.Max.Z, ADAPTIVE_TOLERANCE)
        || FMath::IsNearlyEqual(SelfAABB.Min.Y, OtherAABB.Max.Y, ADAPTIVE_TOLERANCE) && FMath::IsNearlyEqual(SelfAABB.Min.Z, OtherAABB.Max.Z, ADAPTIVE_TOLERANCE)
        );

    if (!bBoundsIntersect)
    {
        return false;
    }

    TFunction<bool(FIntrTriangle3Triangle3d&)> IntersectionFn = [bReportCoplanar](FIntrTriangle3Triangle3d& Intr)
        {
            Intr.SetTolerance(ADAPTIVE_TOLERANCE);
            Intr.SetReportCoplanarIntersection(bReportCoplanar);
            return FDynamicMeshAABBTree3::TriangleIntersection(Intr);
        };

    OutResault = SelfIntersectionMesh.GetDynamicMeshTree().FindAllIntersections(Other.GetDynamicMeshTree(), nullptr, SelfQueryOption, OtherQueryOptions, IntersectionFn);
    //for (size_t i = 0; i < OutResault.Points.Num(); i++)
    //{
    //    // UKismetSystemLibrary::DrawDebugSphere(this, OutResault.Points[i].Point, 10.f, 12, FLinearColor::Green, 0.1f);
    //}
    //for (size_t i = 0; i < OutResault.Segments.Num(); i++)
    //{
    //    //UKismetSystemLibrary::DrawDebugLine(this, OutResault.Segments[i].Point[0], OutResault.Segments[i].Point[1], FLinearColor::Red, 0.1f, 0.1f);

    //    int  TriID = OutResault.Segments[i].TriangleID[1];
    //    const FDynamicMesh3* TargetDynamicMesh = DynamicMeshTree.GetMesh();
    //    FVector Normal = TargetDynamicMesh->GetTriNormal(TriID);
    //}
    //for (size_t i = 0; i < OutResault.Polygons.Num(); i++)
    //{
    //    //int Quantity = OutResault.Polygons[i].Quantity;
    //    //for (size_t j = 0; j < Quantity - 1; j++)
    //    //{
    //    //    UKismetSystemLibrary::DrawDebugLine(this, OutResault.Polygons[i].Point[j], OutResault.Polygons[i].Point[j + 1], FLinearColor::Blue, 0.1f, 0.1f);
    //    //}
    //}
    return (OutResault.Points.Num() > 0 || OutResault.Segments.Num() > 0 || OutResault.Polygons.Num() > 0);
}

bool FAdapationIntersectionExecuter::DoIntersectionTest(const FIntersectionDynamicMesh& SelfIntersectionMesh, const FIntersectionDynamicMesh& Ohter, const IMeshSpatial::FQueryOptions SelfQueryOption, const IMeshSpatial::FQueryOptions& OtherQueryOptions)
{
    //TFunction<bool(FIntrTriangle3Triangle3d&)> IntersectionFn = [&](FIntrTriangle3Triangle3d& Intr)
    //    {
    //        Intr.SetTolerance(ADAPTIVE_TOLERANCE);
    //        return FDynamicMeshAABBTree3::TriangleIntersection(Intr);
    //    };
    return SelfIntersectionMesh.GetDynamicMeshTree().TestIntersection(Ohter.GetDynamicMeshTree(),nullptr, SelfQueryOption, OtherQueryOptions);
   
}

bool FAdapationIntersectionExecuter::FindHitNearestPoint(const FVector& RayStart, const FVector& RayDir, const TArray<TSharedPtr<FIntersectionDynamicMesh>>& Env, FVector& OutPoint, FVector& OutNormal, TSharedPtr<FIntersectionDynamicMesh>& OutHitPtr)
{
    double Distance = TNumericLimits<double>().Max();

    IMeshSpatial::FQueryOptions Options;
    Options.bAllowUnsafeModifiedMeshQueries = true;
    Options.MaxDistance = Distance;
    bool bHitAnyTri = false;
    int FinalTriID = 0;
    FRay Ray(RayStart, RayDir);

    TSharedPtr<FIntersectionDynamicMesh> HitEnv = nullptr;
    for (auto& Iter : Env)
    {
        if (!Iter.IsValid())
        {
            continue;
        }
        double CurDistance;
        int TriID;
        bHitAnyTri = Iter->RayHit(Ray, Options, TriID, CurDistance);
        if (bHitAnyTri&&(Distance > CurDistance))
        {
            Distance = CurDistance;
            FinalTriID = TriID;
            HitEnv = Iter;
        }
    }
    if (HitEnv.IsValid())
    {
        OutHitPtr = HitEnv;
        OutNormal = OutHitPtr->GetDynamicMeshTree().GetMesh()->GetTriNormal(FinalTriID);
        //if (OutNormal.Dot(RayDir) < 0)
        // +                                                                                                                                                        
        //{
        //    OutNormal *= -1;
        //}
        OutPoint = RayStart + Distance * RayDir;
    }
    else
    {
        OutHitPtr.Reset();
    }
    return bHitAnyTri;

}

bool FAdapationIntersectionExecuter::FindHitNearestPoint(const FVector& RayStart, const FVector& RayDir,const TArray<TSharedPtr<FIntersectionDynamicMesh>>& Env, FRayHitResault& HitResautl,  const TSharedPtr<FIntersectionDynamicMesh>& IgnoreData)
{
    double Distance = TNumericLimits<double>::Max();
    return FindHitNearestPoint(RayStart, RayDir, Distance, Env, HitResautl, IgnoreData);
}

bool FAdapationIntersectionExecuter::FindHitNearestPoint(const FVector& RayStart, const FVector& RayDir, double RayDistance, const TArray<TSharedPtr<FIntersectionDynamicMesh>>& Env, FRayHitResault& HitResautl, const TSharedPtr<FIntersectionDynamicMesh>& IgnoreData)
{
    double Distance = RayDistance;
    IMeshSpatial::FQueryOptions Options;
    Options.bAllowUnsafeModifiedMeshQueries = true;
    Options.MaxDistance = RayDistance;
    bool bHitAnyTri = false;
    int FinalTriID = 0;
    int UseTriID = 0;
    FRay Ray(RayStart, RayDir);

    TSharedPtr<FIntersectionDynamicMesh> HitEnv = nullptr;
    for (auto& Iter : Env)
    {
        if (!Iter.IsValid())
        {
            continue;
        }
        if (Iter == IgnoreData)
        {
            continue;
        }
        double CurDistance;
        bHitAnyTri = Iter->RayHit(Ray, Options, UseTriID, CurDistance);
        if (bHitAnyTri /*&& (Distance > CurDistance)*/)
        {
            if (FMath::IsNearlyEqual(Distance, CurDistance, ADAPTIVE_TOLERANCE)&& HitEnv.IsValid())
            {
				double CurDis = FMath::Abs(FVector::DotProduct(Ray.Origin - Iter->GetSelfFrame().Origin, Ray.Direction));
				double PreDis = FMath::Abs(FVector::DotProduct(Ray.Origin - HitEnv->GetSelfFrame().Origin, Ray.Direction));
				if (CurDis <= PreDis)
				{
					Distance = CurDistance;
					HitEnv = Iter;
					FinalTriID = UseTriID;
				}
            }
            else if(Distance > CurDistance)
            {
				Distance = CurDistance;
				HitEnv = Iter;
				FinalTriID = UseTriID;
            }

			/*Distance = CurDistance;
			HitEnv = Iter;
			FinalTriID = UseTriID;*/
        }
    }
    if (HitEnv.IsValid())
    {
        HitResautl.HitTarget = HitEnv;
        HitResautl.Distance = Distance;
        HitResautl.HitNormal = HitEnv->GetDynamicMeshTree().GetMesh()->GetTriNormal(FinalTriID);
        //if (OutNormal.Dot(RayDir) < 0)
        //{
        //    OutNormal *= -1;
        //}
        HitResautl.HitPoint = Ray.PointAt(Distance);;
        return true;
    }
    else
    {
        HitResautl.HitTarget.Reset();
        return false;
    }
   
}

void FAdapationIntersectionExecuter::DrawOriBox(UObject* WorldContextObject, const UE::Geometry::FOrientedBox3d& InOriBox, FLinearColor LineColor)
{
    TArray<FVector> Points;
    for (size_t i = 0; i < 8; i++)
    {
        Points.Add(InOriBox.GetCorner(i));
    }
    for (size_t i = 0; i < 8; i++)
    {
        int EndIndex = (i + 1) % 4 + i / 4 * 4;
        UKismetSystemLibrary::DrawDebugLine(WorldContextObject, Points[i], Points[EndIndex], LineColor, 0.f, 0.5f);
    }
    for (size_t i = 0; i < 4; i++)
    {
        int EndIndex = (i + 4);
        UKismetSystemLibrary::DrawDebugLine(WorldContextObject, Points[i], Points[EndIndex], LineColor, 0.f, 0.5f);
    }
}

const FIntersectionDynamicMesh& FAdapationIntersectionExecuter::GetIntersectionData()
{
    if (SourceIntersectionData.IsValid())
    {
        return *SourceIntersectionData;
    }
    else
    {
        SourceIntersectionData = MakeShared<FIntersectionDynamicMesh>();
        return *SourceIntersectionData;
    }
    // TODO: 在此处插入 return 语句
}

FIntersectionDynamicMesh& FAdapationIntersectionExecuter::GetIntersectionDataRef()
{
    if (SourceIntersectionData.IsValid())
    {
        return *SourceIntersectionData;
    }
    else
    {
        SourceIntersectionData = MakeShared<FIntersectionDynamicMesh>();
        return *SourceIntersectionData;
    }
}

void FAdapationIntersectionExecuter::PreproccessMaxAndMinExtents(const FVector& DefaultExtents, FVector& MaxExtents, FVector& MinExtents)
{
        MaxExtents.X = FMath::Max(MaxExtents.X, DefaultExtents.X);
        MaxExtents.Y = FMath::Max(MaxExtents.Y, DefaultExtents.Y);
        MaxExtents.Z = FMath::Max(MaxExtents.Z, DefaultExtents.Z);

        MinExtents.X = FMath::Min(MinExtents.X, DefaultExtents.X);
        MinExtents.Y = FMath::Min(MinExtents.Y, DefaultExtents.Y);
        MinExtents.Z = FMath::Min(MinExtents.Z, DefaultExtents.Z);

        if (FMath::IsNearlyZero(MaxExtents.X ,ADAPATIVE_UNIT))
        {
            MaxExtents.X = DefaultExtents.X;
        }
        if (FMath::IsNearlyZero(MaxExtents.Y, ADAPATIVE_UNIT))
        {
            MaxExtents.Y = DefaultExtents.Y;
        }

        if (FMath::IsNearlyZero(MaxExtents.Z, ADAPATIVE_UNIT))
        {
            MaxExtents.Z = DefaultExtents.Z;
        }
           
        if (FMath::IsNearlyZero(MinExtents.X,ADAPATIVE_UNIT))
        {
            MinExtents.X = DefaultExtents.X;
        }
        if (FMath::IsNearlyZero(MinExtents.Y))
        {
            MinExtents.Y = DefaultExtents.Y;
        }
        if (FMath::IsNearlyZero(MinExtents.Z,ADAPATIVE_UNIT))
        {
            MinExtents.Z = DefaultExtents.Z;
        }
}
