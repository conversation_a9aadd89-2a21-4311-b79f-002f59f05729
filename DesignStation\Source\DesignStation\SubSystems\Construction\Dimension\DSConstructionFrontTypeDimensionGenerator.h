#pragma once
#include "CoreMinimal.h"
#include "DSConstructionDimensionGeneratorBase.h"
class FDSConstructionFrontTypeDimensionGenerator :
    public FDSConstructionDimensionGeneratorBase
{
public:
    FDSConstructionFrontTypeDimensionGenerator() {};
    ~FDSConstructionFrontTypeDimensionGenerator()
    {

    }
     void GenerateDimension(const TMap<FString, TSharedPtr<FDSConstructionData>>& InConstructDatas);

     virtual void GenerateDimension(const TArray<TSharedPtr<FDSConstructionData>>& InConstructDatas) override;
};

