/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef _ACTIVEMQ_CORE_ACTIVEMQXACONNECTION_H_
#define _ACTIVEMQ_CORE_ACTIVEMQXACONNECTION_H_

#include <activemq/util/Config.h>

#include <cms/XAConnection.h>
#include <activemq/core/ActiveMQConnection.h>

namespace activemq {
namespace core {

    using decaf::lang::Pointer;

    class AMQCPP_API ActiveMQXAConnection : public cms::XAConnection, public ActiveMQConnection {
    private:

        ActiveMQXAConnection(const ActiveMQXAConnection&);
        ActiveMQXAConnection& operator= (const ActiveMQXAConnection&);

    public:

        ActiveMQXAConnection(const Pointer<transport::Transport>& transport,
                             const Pointer<decaf::util::Properties>& properties);

        virtual ~ActiveMQXAConnection();

        virtual cms::XASession* createXASession();

        virtual cms::Session* createSession(cms::Session::AcknowledgeMode ackMode);

    public:

        using ActiveMQConnection::createSession;

    };

}}

#endif /* _ACTIVEMQ_CORE_ACTIVEMQXACONNECTION_H_ */
