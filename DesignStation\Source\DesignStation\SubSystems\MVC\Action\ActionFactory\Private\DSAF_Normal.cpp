﻿// Fill out your copyright notice in the Description page of Project Settings.


#include "SubSystems/MVC/Action/ActionFactory/Public/DSAF_Normal.h"

#include "SubSystems/MVC/Action/ActionExecute/Public/Abstruct/DrawingPoint/DSDrawingPointAE.h"
#include "SubSystems/MVC/Action/ActionExecute/Public/Abstruct/PathPoint/DSPathPointAE.h"
#include "SubSystems/MVC/Action/ActionExecute/Public/Abstruct/Plane/DSPlaneAE.h"
#include "SubSystems/MVC/Action/ActionExecute/Public/Abstruct/Point/DSPointAE.h"
#include "SubSystems/MVC/Action/ActionExecute/Public/Custom/Cupboard/DSCupboardAE_Custom.h"
#include "SubSystems/MVC/Action/ActionExecute/Public/Free/DSFreeAE.h"
#include "SubSystems/MVC/Action/ActionExecute/Public/Furniture/MoldingFurniture/DSMoldingCeilingAE.h"
#include "SubSystems/MVC/Action/ActionExecute/Public/Furniture/SoftFurniture/DSSoftFurnitureAE.h"
#include "SubSystems/MVC/Action/ActionExecute/Public/House/Area/HouseArea/DSHouseAreaAE.h"
#include "SubSystems/MVC/Action/ActionExecute/Public/House/Area/HouseAreaLabel/DSHouseAreaLabelAE.h"
#include "SubSystems/MVC/Action/ActionExecute/Public/House/Area/HouseSplitAreaLine/DSHouseSplitAreaLineAE.h"
#include "SubSystems/MVC/Action/ActionExecute/Public/House/DoorAndWindow/DSWinDoorAE.h"
#include "SubSystems/MVC/Action/ActionExecute/Public/House/Item/Pillar/DSPillarAE.h"
#include "SubSystems/MVC/Action/ActionExecute/Public/House/Roof/DSHouseRoofAE.h"
#include "SubSystems/MVC/Action/ActionExecute/Public/House/Wall/DSHouseWallAE.h"
#include "SubSystems/MVC/Action/ActionExecute/Public/Pendant/Axis/Axis2D/DSAxis2DAE.h"
#include "SubSystems/MVC/Action/ActionExecute/Public/Pendant/Scale/Scale2D/DSScale2DAE.h"
#include "SubSystems/MVC/Action/ActionExecute/Public/Pendant/Scale/Scale2DForPath/DSScale2DForPathAE.h"
#include "SubSystems/MVC/Action/ActionExecute/Public/MultiSelect/Multi/DSMultiSelectAE.h"
#include "SubSystems/MVC/Action/ActionExecute/Public/MultiSelect/Group/DSGroupAE.h"
#include "Subsystems/MVC/Action/ActionExecute/Public/Gizmo/DSGizmoAE.h"

UDSAF_Normal::UDSAF_Normal()
{
	
}

void UDSAF_Normal::InitActionMap()
{// FSM_Normal Action Map
	AddActionToMap(EDSModelType::E_None);
	AddActionToMap(EDSModelType::E_Gizmo);
	AddActionToMap(EDSModelType::E_Furniture_HouseFurniture);
	AddActionToMap(EDSModelType::E_Furniture_MoldingCeiling);

	UDSCupboardAE_Custom* CupboardAE = NewObject<UDSCupboardAE_Custom>(this);
	int32 CustomEndIndex = static_cast<int32>(EDSModelType::E_Custom_Furniture_Range_End);
	for (int32 CustomStartIndex = static_cast<int32>(EDSModelType::E_Custom_UpperCabinet); CustomStartIndex < CustomEndIndex; ++CustomStartIndex)
	{
		ActionMap.Add(static_cast<EDSModelType>(CustomStartIndex), CupboardAE);
	}
	
	AddActionToMap(EDSModelType::E_Axis2D);
	AddActionToMap(EDSModelType::E_House_Pillar);
	AddActionToMap(EDSModelType::E_Plane);
	AddActionToMap(EDSModelType::E_Scale2D);
	AddActionToMap(EDSModelType::E_Point);
	AddActionToMap(EDSModelType::E_House_Wall);
	AddActionToMap(EDSModelType::E_House_Beam);
	AddActionToMap(EDSModelType::E_House_Platform);

	AddActionToMap(EDSModelType::E_HouseWallPathPoint);
	AddActionToMap(EDSModelType::E_PlatformPathPoint);
	AddActionToMap(EDSModelType::E_BeamPathPoint);

	AddActionToMap(EDSModelType::E_DrawingPoint);
	AddActionToMap(EDSModelType::E_Scale2DForPath);
	AddActionToMap(EDSModelType::E_House_Area);
	AddActionToMap(EDSModelType::E_House_Area_Label);
	AddActionToMap(EDSModelType::E_House_Area_Split_Line);
	AddActionToMap(EDSModelType::E_House_Door);
	AddActionToMap(EDSModelType::E_House_Window);
	AddActionToMap(EDSModelType::E_House_Roof);

	AddActionToMap(EDSModelType::E_MultiSelect);
	AddActionToMap(EDSModelType::E_Group);

	AddActionToMap(EDSModelType::E_Custom_RangeHood);
}

UDSActionExecuteBase* UDSAF_Normal::GetActionForMap(const EDSModelType& InType)
{
	switch (InType)
	{
	case EDSModelType::E_None:								return NewObject<UDSFreeAE>(this);
	case EDSModelType::E_Gizmo:								return NewObject<UDSGizmoAE>(this);
	case EDSModelType::E_Furniture_HouseFurniture:			return NewObject<UDSSoftFurnitureAE>(this);
	case EDSModelType::E_Furniture_MoldingCeiling:			return NewObject<UDSMoldingCeilingAE>(this);
	case EDSModelType::E_Axis2D:							return NewObject<UDSAxis2DAE>(this);
	case EDSModelType::E_House_Pillar:						return NewObject<UDSPillarAE>(this);
	case EDSModelType::E_Plane:								return NewObject<UDSPlaneAE>(this);
	case EDSModelType::E_Scale2D:							return NewObject<UDSScale2DAE>(this);
	case EDSModelType::E_Point:								return NewObject<UDSPointAE>(this);
	case EDSModelType::E_House_Wall: 						return NewObject<UDSHouseWallAE>(this);
	case EDSModelType::E_House_Beam	:						return NewObject<UDSHouseWallAE>(this);
	case EDSModelType::E_House_Platform:					return NewObject<UDSHouseWallAE>(this);
	case EDSModelType::E_HouseWallPathPoint:				return NewObject<UDSPathPointAE>(this);
	case EDSModelType::E_PlatformPathPoint	:				return NewObject<UDSPathPointAE>(this);
	case EDSModelType::E_BeamPathPoint:						return NewObject<UDSPathPointAE>(this);
	case EDSModelType::E_DrawingPoint:						return NewObject<UDSDrawingPointAE>(this);
	case EDSModelType::E_Scale2DForPath:					return NewObject<UDSScale2DForPathAE>(this);
	case EDSModelType::E_House_Area:						return NewObject<UDSHouseAreaAE>(this);
	case EDSModelType::E_House_Area_Label:					return NewObject<UDSHouseAreaLabelAE>(this);
	case EDSModelType::E_House_Area_Split_Line:				return NewObject<UDSHouseSplitAreaLineAE>(this);
	case EDSModelType::E_House_Door:						return NewObject<UDSWinDoorAE>(this);
	case EDSModelType::E_House_Window:						return NewObject<UDSWinDoorAE>(this);
	case EDSModelType::E_House_Roof:						return NewObject<UDSHouseRoofAE>(this);
	case EDSModelType::E_MultiSelect:						return NewObject<UDSMultiSelectAE>(this);
	case EDSModelType::E_Group:								return NewObject<UDSGroupAE>(this);
	case EDSModelType::E_Custom_RangeHood:					return NewObject<UDRangeHoodAE>(this);
	
		default: return nullptr;
	}
}
