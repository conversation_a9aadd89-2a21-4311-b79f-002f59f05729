#pragma once

#include "Decimal.h"

class DECIMALNUMBER_API FDecimalMath
{
public:
	static FDecimal InvSqrt(const FDecimal& InVal);

	static FDecimal Abs(const FDecimal& InVal);

	static FDecimal Floor(const FDecimal& InVal);

	static FDecimal Ceil(const FDecimal& InVal);

	static FDecimal Round(const FDecimal& InVal);

	static FDecimal Sqrt(const FDecimal& InVal);

	static FORCEINLINE FDecimal Square(const FDecimal& InVal)
	{
		return InVal * InVal;
	}

	static FDecimal Sin(const FDecimal& InVal);

	static FDecimal Cos(const FDecimal& InVal);

	static FDecimal Acos(const FDecimal& InVal);

	static FDecimal Atan(const FDecimal& InVal);

	static FDecimal Atan2(const FDecimal& A, const FDecimal& B);

	template <typename Type UE_REQUIRES(ARITHMETIC_WITH_DECIMAL_CONDITION(Type))>
	static FDecimal Power(const FDecimal& InVal, const Type& InPow)
	{
		FDecimal Result;
		Result.InternalValue = boost::multiprecision::pow(InVal.InternalValue, InPow);
		return Result;
	}
	
	static FDecimal Power(const FDecimal& InVal, const FDecimal& InPow)
	{
		FDecimal Result;
		Result.InternalValue = boost::multiprecision::pow(InVal.InternalValue, InPow.InternalValue);
		return Result;
	}

	static void SinCos(FDecimal& ScalarSin, FDecimal& ScalarCos, const FDecimal& Value);

	static void SinCos(FDecimal& ScalarSin, FDecimal& ScalarCos, float Value);
	static void SinCos(FDecimal& ScalarSin, FDecimal& ScalarCos, double Value);

	static FDecimal UnwindDegrees(FDecimal A);

	static FORCEINLINE bool IsFinite(const FDecimal& InVal)
	{
		return boost::multiprecision::isfinite(InVal.InternalValue);
	}

	static FORCEINLINE bool IsNearlyZero(const FDecimal& Value, const FDecimal& ErrorTolerance = FDecimal(UE_DOUBLE_SMALL_NUMBER))
	{
		return Abs(Value) <= ErrorTolerance;
	}

	static FORCEINLINE bool IsNearlyEqual(const FDecimal& Value1, const FDecimal& Value2, const FDecimal& ErrorTolerance = FDecimal(UE_DOUBLE_SMALL_NUMBER))
	{
		return Abs(Value1 - Value2) <= ErrorTolerance;
	}

	static FORCEINLINE FDecimal DegreesToRadians(const FDecimal& DegVal)
	{
		return DegVal * (FDecimal::ConstantPI / 180.0f);
	}

	static FORCEINLINE FDecimal GridSnap(const FDecimal& Location, const FDecimal& Grid)
	{
		return (Grid == 0) ? Location : (Floor((Location + (Grid / 2)) / Grid) * Grid);
	}

	static FORCEINLINE FDecimal Min(const FDecimal& A, const FDecimal& B)
	{
		return (A <= B) ? A : B;
	}

	static FORCEINLINE FDecimal Max(const FDecimal& A, const FDecimal& B)
	{
		return (A >= B) ? A : B;
	}

	static FORCEINLINE FDecimal Min3(const FDecimal& A, const FDecimal& B, const FDecimal& C)
	{
		return Min(Min(A, B), C);
	}

	static FORCEINLINE FDecimal Max3(const FDecimal& A, const FDecimal& B, const FDecimal& C)
	{
		return Max(Max(A, B), C);
	}

	static FORCEINLINE FDecimal Clamp(const FDecimal& InVal, const FDecimal& Min, const FDecimal& Max)
	{
		return (InVal < Min) ? Min : (InVal < Max) ? InVal : Max;
	}

	/*
	*  @@ 取模
	*/
	static FDecimal FMod(const FDecimal& A, const FDecimal& B);

	/*
	*  @@ 取余操作，特化
	*  @@ 逻辑：A/B 取 整数C，然后取值 D = A - B * C。如果A、B异号，则取 -D
	*/
	static FDecimal RemainderCustom(FDecimal A, FDecimal B);

};
