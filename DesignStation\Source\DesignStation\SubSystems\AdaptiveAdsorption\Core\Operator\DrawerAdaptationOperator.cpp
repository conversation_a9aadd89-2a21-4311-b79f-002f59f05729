#include "DrawerAdaptationOperator.h"
#include "SubSystems/AdaptiveAdsorption/Core/Executer/DynamicMeshAdaptiveAddorption.h"
#include "SubSystems/MVC/Model/Custom/Library/DSCupboardLibrary.h"
#include "SubSystems/MVC/Model/Custom/DSCupboardModel.h"	
#include "Parameter/ParameterProcLibrary.h"
#include "SubSystems/AdaptiveAdsorption/Core/Executer/DrawerAdaptiveAdsorption.h"
#include "SubSystems/MVC/Core/Property/CupboardProperty.h"
#include "SubSystems/MVC/DSMVCSubsystem.h"
#include "SubSystems/ModelDependencySubsystem/DSModelDependencySubsystem.h"
#include "SubSystems/MVC/Library/DSToolLibrary.h"
#include "SubSystems/Undo/DSRevokeSubsystem.h"
#include "SubSystems/File/Core/ProtoDataConvert/ProtobufOperatorFunctionLibrary.h"
#include "Subsystems/MVC/Coroutine/Awaiters/DSDrawerEvenAwaiter.h"
#include "SubSystems/AdaptiveAdsorption/Data/FunctionalDependencyInfo.h"
#include "SubSystems/Library/DesignStationFunctionLibrary.h"
#include "SubSystems/UI/DSUISubsystem.h"
#include "Geometry/DataDefines/GeometryDatas.h"

extern const FString PARAM_W_STR;
extern const FString PARAM_WJ_STR;
extern const FString PARAM_H_STR;
extern const FString PARAM_HJ_STR;
extern const FString PARAM_D_STR;
extern const FString PARAM_DJ_STR;
extern const FString PARAM_W_STR;
extern const FString PARAM_H_STR;
extern const FString PARAM_D_STR;
extern const FString DRAWER_CHD_STR;
extern const FString DRAWER_CMH_STR;
extern const FString PARAM_ZBFG;
extern const FString PARAM_YBFG;
extern const FString PARAM_SBFG;
extern const FString PARAM_XBFG;

extern const FString PARAM_ZCBJT_STR;
extern const FString PARAM_XCBJT_STR;
extern const FString PARAM_YCBJT_STR;
extern const FString PARAM_SCBJT_STR;
extern const FString PARAM_MF_STR;
extern const FString PARAM_MYGLX_STR;

DECLARE_LOG_CATEGORY_CLASS(LogDrawerAdaptationOperator, Log, All);

//FDrawerAdaptationOperator::FDrawerAdaptationOperator()
//	: FFunctionalAdaptationOperator(nullptr)
//{
//	ApplyOffsetAdaptationEnvs.Reset();
//	DoorDrawerAdaptationEnvs.Reset();
//}

FDrawerAdaptationOperator::FDrawerAdaptationOperator(UDSBaseModel* InModel)
	: FFunctionalAdaptationOperator(InModel)
{
}

void FDrawerAdaptationOperator::GenerateAdaptationSourceInfo()
{

	if (!SourceModel || !TargetModel)
	{
		return;
	}

	UDSCupboardModel* SourceCupboardModel = Cast<UDSCupboardModel>(SourceModel);

	UDSCupboardModel* TargetCupboardModel = Cast<UDSCupboardModel>(TargetModel);

	//初始化待检测对象
	SourceAdaptationInfo = MakeShareable<FDrawerAdaptiveAdsorption>(new FDrawerAdaptiveAdsorption());
	TSharedPtr<FFunctionalExecuterInitializedData> InitializedData = MakeShared<FFunctionalExecuterInitializedData>();
	
	//GenerateInitializedData_Drawer(InitializedData);
	GenerateInitializedData_Functional(InitializedData);
	 

	TAdaptationHandle Handle =
		[&](const TSharedPtr<FAdaptationData>& AdaptationData)
		{OnAdaptiveAndAdSorptionCallback(AdaptationData); };

	SourceAdaptationInfo->Initialized(InitializedData);
	SourceAdaptationInfo->SetExecuteHandle(Handle);

	const FString& SelfUUID = SourceCupboardModel->GetModelInfo().ComponentTreeData->UUID;
	SourceAdaptationInfo->GetIntersectionDataRef().SetLinkModelBaseInfo(SelfUUID, SourceCupboardModel->GetModelInfo().ComponentTreeData->ComponentName, static_cast<int>(SourceCupboardModel->GetModelType()), SelfUUID);
	SourceAdaptationInfo->GetIntersectionDataRef().GetLinkModelBaseInfoRef().OwnerModelPtr = SourceModel;

	StaticCastSharedPtr<FDrawerAdaptiveAdsorption>(SourceAdaptationInfo)->SetDrawerOperator(AsShared().ToSharedPtr());
}

bool FDrawerAdaptationOperator::PrepareAdaptation(UDSBaseModel* InSourceModel)
{
	UDSCupboardModel* SourceCupboardModel = Cast<UDSCupboardModel>(InSourceModel);
	UDSCupboardModel* SourceRootModel = SourceCupboardModel->GetRootCupboardModel();
	PrepareAdaptation(InSourceModel, SourceRootModel);
	return false;
}

void FDrawerAdaptationOperator::PrepareAdaptation(UDSBaseModel* InSourceModel, UDSBaseModel* InTargetModel)
{
	SourceModel = InSourceModel;
	TargetModel = InTargetModel;


	if (!InSourceModel)
	{
		return;
	}
	bool bIsFunctionalModel = UDSCupboardLibrary::IsFunctionalCupboardModel(InSourceModel);
	if (!bIsFunctionalModel)
	{
		return;
	}

	//初始化环境
	//GenerateAdaptationEnvData();
	GenerateAdapationEnvDataWithoutDenpendented();
	GenerateAdaptationSourceInfo();
}

void FDrawerAdaptationOperator::GeneratorTargetModelIntersectionEnv(UDSBaseModel* InTargetMode, const FTransform& RootTrans, TArray<TSharedPtr<FIntersectionDynamicMesh>>& OutEnv, const TSharedPtr<FMultiComponentDataItem>& InComponentTreeData, const TSharedPtr<FMultiComponentDataItem>& IgnoreModelTreeData, const TSharedPtr<struct FFunctionalDependencyInfo>& InDependencyInfo)
{
	if (InTargetMode == nullptr || !InComponentTreeData.IsValid())
	{
		return;
	}

	//子部件构建轮廓数据
	for (int32 i = 0; i < InComponentTreeData->ChildComponent.Num(); i++)
	{
		FTransform RelativeRootTransform = RootTrans;
		UDSCupboardModel* TargetCupboardModel = Cast<UDSCupboardModel>(InTargetMode);

		const auto& ComponentArr = TargetCupboardModel->GetModelInfo().ComponentInfoArr;
		const auto& MultiDataIter = InComponentTreeData->ChildComponent[i];

		if (MultiDataIter == IgnoreModelTreeData || !MultiDataIter->IsVisiable())
		{
			continue;
		}

		////忽略柜门
		//if (UDSToolLibrary::IsCustomDoorType(UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(MultiDataIter->ModelType)))
		//{
		//	continue;
		//}

		bool bIsFunctionalCupboardModel = UDSCupboardLibrary::IsFunctionalCupboardModel(MultiDataIter->ComponentParameters);

		//判断是否是整体碰撞,不遍历子部件
		bool bOverallCollision = UDSCupboardLibrary::GetAdaptationIsOverallCollision(MultiDataIter->ComponentParameters, bIsFunctionalCupboardModel);

		//判断是否是可分离对象
		bool bSeparate = UDSCupboardLibrary::ComponentNeedSeparate(MultiDataIter->ComponentParameters);
		bSeparate &= true; //这里添加可移动参数值
		if (bSeparate) //需要单独构建的创建自身节点
		{
			if (MultiDataIter->UUID.IsEmpty())
			{
				continue;
			}

			UDSCupboardModel* ChildrenModel = nullptr;
			for (int32 J = 0; J < ComponentArr.Num(); J++)
			{
				const FDSComponentInfo& ComponentInfo = ComponentArr[J];
				if (ComponentInfo.ComponentUUID.Equals(MultiDataIter->UUID))
				{
					ChildrenModel = Cast<UDSCupboardModel>(ComponentInfo.ComponentModel);
					if (ChildrenModel)
					{
						TargetCupboardModel = ChildrenModel;
						RelativeRootTransform = ChildrenModel->GetRelativeTransform() * RootTrans;

						if (!bOverallCollision)
						{
							bool IsDoor = UDSToolLibrary::IsCustomDoorType(UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(MultiDataIter->ModelType));
							bOverallCollision = (UDSCupboardLibrary::IsDoorPanelDrawer(ChildrenModel)
								|| ChildrenModel->GetModelType() == EDSModelType::E_Custom_FunctionalDrawer
								|| ChildrenModel->GetModelType() == EDSModelType::E_Custom_CabinetBoard
								|| ChildrenModel->GetModelType() == EDSModelType::E_Custom_SideClosurePanel
								|| IsDoor);
						}


					}
					break;
				}
			}
			if (!ChildrenModel)
			{
				continue;
			}
		}

		if (bOverallCollision || MultiDataIter->ComponentType == ECompType::SingleCom)
		{
			//遍历到单部件时TreeData为它上层的多部件
			const auto& UseComponentTreeData = MultiDataIter->ComponentType == ECompType::SingleCom ? InComponentTreeData : MultiDataIter;
			TMap<FString, FParameterData> ParamMap;
			for (auto& Iter : UseComponentTreeData->ComponentParameters)
			{
				ParamMap.Add(Iter.Data.name, Iter);
			}


			FVector  DefaultExtent = FVector::Zero();

			//特殊宽高深
			FVector SpecialExtent = FVector::Zero();
			FVector SpecialOffset = FVector::Zero();
			bool bHasSpecialDWH = GetSpecialSizeAndOffset(ParamMap, SpecialExtent, SpecialOffset);
			if (UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(MultiDataIter->ModelType) == EDSModelType::E_Custom_FunctionalDrawer)
			{
				bHasSpecialDWH = false;
			}
			
			//固定宽高深
			FVector FixedExtent = FVector::Zero();
			FVector FixedOffset = FVector::Zero();
			bool bHasFixedDWH = GetFixedSizeAndOffset(ParamMap, FixedExtent, FixedOffset);
			//if (UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(MultiDataIter->ModelType) == EDSModelType::E_Custom_Board
			//	|| UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(MultiDataIter->ModelType) == EDSModelType::E_Custom_CabinetBoard
			//	|| UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(MultiDataIter->ModelType) == EDSModelType::E_Custom_FunctionalDrawer
			//	)
			//{//功能件板，需使用未内缩的尺寸，保证尺寸正常
			//	bHasFixedDWH = false;
			//}

			//默认宽高深
			TMap<FString, FParameterData> BaseParamsMap;
			bool bHasDHW = UDSCupboardLibrary::CombineBaseDHWParameters(BaseParamsMap, ParamMap);
			if (bHasDHW)
			{
				UDSCupboardLibrary::ConvertBaseDHWParametersToFVector(DefaultExtent, BaseParamsMap);
			}

			//如果没有宽高深数据,跳过
			if (!bHasDHW)
			{
				UE_LOG(AdaptationExecuterLog, Warning, TEXT("Node DHW Data Lost,Please Check Data.Code: %s,UUID:%s "), *MultiDataIter->Code, *MultiDataIter->UUID);
				continue;
			}
			DefaultExtent = DefaultExtent * 0.5f;



			//求板件世界位置
			FTransform ComponentRelativeTransform = RelativeRootTransform;
			TArray<TSharedPtr<FMultiComponentDataItem>> ComponentPath;
			TargetCupboardModel->CollectComponentPath_Public(TargetCupboardModel->GetModelInfo().ComponentTreeData, UseComponentTreeData, ComponentPath);

			if (ComponentPath.IsValidIndex(1))
			{
				for (int32 Index = 1; Index < ComponentPath.Num(); ++Index)
				{
					FTransform CurrentTransform;
					CurrentTransform.SetLocation(ComponentPath[Index]->ComponentLocation.GetLocation());
					CurrentTransform.SetRotation(ComponentPath[Index]->ComponentRotation.GetRotation().Quaternion());
					CurrentTransform.SetScale3D(ComponentPath[Index]->ComponentScale.GetScale());
					ComponentRelativeTransform = CurrentTransform * ComponentRelativeTransform;
				}
			}

			//获取柜体包围盒，求当前包围盒相对柜体包围盒的OBB


			//设置OBB
			FOrientedBox3d Oribox;
			//rot
			Oribox.Frame.Rotation = FQuaterniond(ComponentRelativeTransform.GetRotation());

			//location 、 offset
			double MFValue = ParamMap.Contains(TEXT("MF")) ? FCString::Atod(*ParamMap[TEXT("MF")].Data.value) : 0.0;
			FVector ConsiderExtent = DefaultExtent;
			ConsiderExtent.X = FixedExtent.X;
			ConsiderExtent.Z = FixedExtent.Z + MFValue * 0.05;
			Oribox.Frame.Origin = ComponentRelativeTransform.TransformPosition(ConsiderExtent);
			if (bHasFixedDWH)
			{
				FVector BoxOffset = FixedOffset;
				BoxOffset.Y = 0.0;

				BoxOffset.X -= MFValue * 0.05;
				BoxOffset.Z -= MFValue * 0.05;
				
				Oribox.Frame.Origin = Oribox.Frame.FromFramePoint(BoxOffset);
			}
			Oribox.Extents = ConsiderExtent;

			/*if (bHasFixedDWH)
			{
				Oribox.Frame.Origin = ComponentRelativeTransform.TransformPosition(FixedExtent);
				Oribox.Frame.Origin = Oribox.Frame.FromFramePoint(FixedOffset);
				Oribox.Extents = FixedExtent;
			}
			else
			{
				Oribox.Frame.Origin = ComponentRelativeTransform.TransformPosition(DefaultExtent);
				Oribox.Extents = DefaultExtent;
			}*/

			bool bUseRelativeOBB = !RootTrans.GetRotation().Equals(ComponentRelativeTransform.GetRotation(), 0.001);

			if (bUseRelativeOBB)
			{
				//如果旋转不一样 
				FBox RealtiveAABB(EForceInit::ForceInit);
				Oribox.EnumerateCorners([&](const FVector3d& Corner)
					{
						RealtiveAABB += RootTrans.InverseTransformPosition(Corner);

					});

				Oribox.Frame.Origin = RootTrans.TransformPosition(RealtiveAABB.GetCenter());
				Oribox.Extents = RealtiveAABB.GetSize() * 0.5f;
				Oribox.Frame.Rotation = FQuaterniond(RootTrans.GetRotation());
			}

			//判断是否是自适应功能件
			EIntersectionDataType  IntersectionDataType = EIntersectionDataType::E_Unknown;

			if (UDSCupboardLibrary::IsDoorPanelDrawer(TargetCupboardModel) || TargetCupboardModel->GetModelType() == EDSModelType::E_Custom_FunctionalDrawer)
			{
				IntersectionDataType = EIntersectionDataType::E_Drawer;
			}
			else if (TargetCupboardModel->GetModelType() == EDSModelType::E_Custom_Functional_Combine)
			{//侧封板
				IntersectionDataType = EIntersectionDataType::E_Function_Combine;
			}
			else if (UDSCupboardLibrary::IsFunctionalCupboardModel(TargetCupboardModel))
			{
				IntersectionDataType = EIntersectionDataType::E_Functional;
			}
			

			//如果是功能件，进行自适应规则处理
			if (bIsFunctionalCupboardModel)
			{
				FAdaptiveAdsorptionRule3D AdaptiveRule = TargetCupboardModel->GetModelInfo().ComponentTreeData->AdaptationData;

				FVector MinRelativePoint = -Oribox.Extents;
				FVector MaxRelativePoint = Oribox.Extents;

				if (!FMath::IsNearlyZero(AdaptiveRule.XAxisRule.NegativeOffset, ADAPTIVE_TOLERANCE))
				{
					MinRelativePoint.X -= AdaptiveRule.XAxisRule.NegativeOffset;
				}
				if (!FMath::IsNearlyZero(AdaptiveRule.XAxisRule.PositiveOffset, ADAPTIVE_TOLERANCE))
				{
					MaxRelativePoint.X += AdaptiveRule.XAxisRule.PositiveOffset;
				}

				if (!FMath::IsNearlyZero(AdaptiveRule.YAxisRule.NegativeOffset, ADAPTIVE_TOLERANCE))
				{
					MinRelativePoint.Y -= AdaptiveRule.YAxisRule.NegativeOffset;
				}
				if (!FMath::IsNearlyZero(AdaptiveRule.YAxisRule.PositiveOffset, ADAPTIVE_TOLERANCE))
				{
					MaxRelativePoint.Y += AdaptiveRule.YAxisRule.PositiveOffset;
				}


				if (!FMath::IsNearlyZero(AdaptiveRule.ZAxisRule.NegativeOffset, ADAPTIVE_TOLERANCE))
				{
					MinRelativePoint.Z -= AdaptiveRule.ZAxisRule.NegativeOffset;
				}
				if (!FMath::IsNearlyZero(AdaptiveRule.ZAxisRule.PositiveOffset, ADAPTIVE_TOLERANCE))
				{
					MaxRelativePoint.Z += AdaptiveRule.ZAxisRule.PositiveOffset;
				}

				FVector RelativeCenter = (MaxRelativePoint + MinRelativePoint) * 0.5f;
				Oribox.Frame.Origin = Oribox.Frame.FromFramePoint(RelativeCenter);
				Oribox.Extents = (MaxRelativePoint - MinRelativePoint) * 0.5f;
			}

			TSharedPtr<FFunctionalIntersectionMesh> ChildrenAdaptive =
				MakeShared<FFunctionalIntersectionMesh>(Oribox.Extents, Oribox.Frame.Origin, FQuat(Oribox.Frame.Rotation), IntersectionDataType);
			ChildrenAdaptive->SetLinkModelBaseInfo(TargetCupboardModel->GetComponentTreeDataRef()->UUID, UseComponentTreeData->Description, static_cast<int>(TargetCupboardModel->GetModelType()), MultiDataIter->UUID);
			ChildrenAdaptive->GetLinkModelBaseInfoRef().OwnerModelPtr = (TargetCupboardModel);
			ChildrenAdaptive->bUseRelativeOBB = bUseRelativeOBB;
			ChildrenAdaptive->bHasSpecialExtents = bHasSpecialDWH;
			ChildrenAdaptive->SpecialExtent = SpecialExtent;
			ChildrenAdaptive->SepcialOffset = SpecialOffset;
			OutEnv.Add(ChildrenAdaptive);
		}
		else //没有单独构建的添加到数组管理
		{
			GeneratorTargetModelIntersectionEnv(TargetCupboardModel, RelativeRootTransform, OutEnv, MultiDataIter, IgnoreModelTreeData, InDependencyInfo);
		}
	}

}

void FDrawerAdaptationOperator::GeneratorTargetModelIntersectionEnvConsiderOffset(UDSBaseModel* InTargetMode, const FTransform& RootTrans, TArray<TSharedPtr<FIntersectionDynamicMesh>>& OutEnv, TArray<TSharedPtr<FIntersectionDynamicMesh>>& OutAutoEnv, const TSharedPtr<FMultiComponentDataItem>& InComponentTreeData, const TSharedPtr<FMultiComponentDataItem>& IgnoreModelTreeData, const TSharedPtr<struct FFunctionalDependencyInfo>& InDependencyInfo)
{
	if (InTargetMode == nullptr || !InComponentTreeData.IsValid())
	{
		return;
	}

	//子部件构建轮廓数据
	for (int32 i = 0; i < InComponentTreeData->ChildComponent.Num(); i++)
	{
		FTransform RelativeRootTransform = RootTrans;
		UDSCupboardModel* TargetCupboardModel = Cast<UDSCupboardModel>(InTargetMode);

		const auto& ComponentArr = TargetCupboardModel->GetModelInfo().ComponentInfoArr;
		const auto& MultiDataIter = InComponentTreeData->ChildComponent[i];

		if (MultiDataIter == IgnoreModelTreeData || !MultiDataIter->IsVisiable())
		{
			continue;
		}

		////忽略柜门
		//if (UDSToolLibrary::IsCustomDoorType(UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(MultiDataIter->ModelType)))
		//{
		//	continue;
		//}

		bool bIsFunctionalCupboardModel = UDSCupboardLibrary::IsFunctionalCupboardModel(MultiDataIter->ComponentParameters);

		//判断是否是整体碰撞,不遍历子部件
		bool bOverallCollision = UDSCupboardLibrary::GetAdaptationIsOverallCollision(MultiDataIter->ComponentParameters, bIsFunctionalCupboardModel);

		//判断是否是可分离对象
		bool bSeparate = UDSCupboardLibrary::ComponentNeedSeparate(MultiDataIter->ComponentParameters);
		bSeparate &= true; //这里添加可移动参数值
		if (bSeparate) //需要单独构建的创建自身节点
		{
			if (MultiDataIter->UUID.IsEmpty())
			{
				continue;
			}

			UDSCupboardModel* ChildrenModel = nullptr;
			for (int32 J = 0; J < ComponentArr.Num(); J++)
			{
				const FDSComponentInfo& ComponentInfo = ComponentArr[J];
				if (ComponentInfo.ComponentUUID.Equals(MultiDataIter->UUID))
				{
					ChildrenModel = Cast<UDSCupboardModel>(ComponentInfo.ComponentModel);
					if (ChildrenModel)
					{
						TargetCupboardModel = ChildrenModel;
						RelativeRootTransform = ChildrenModel->GetRelativeTransform() * RootTrans;

						if (!bOverallCollision)
						{
							bool IsDoor = UDSToolLibrary::IsCustomDoorType(UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(MultiDataIter->ModelType));
							bOverallCollision = (UDSCupboardLibrary::IsDoorPanelDrawer(ChildrenModel)
								|| ChildrenModel->GetModelType() == EDSModelType::E_Custom_FunctionalDrawer
								|| ChildrenModel->GetModelType() == EDSModelType::E_Custom_CabinetBoard
								|| ChildrenModel->GetModelType() == EDSModelType::E_Custom_SideClosurePanel
								|| IsDoor);
						}


					}
					break;
				}
			}
			if (!ChildrenModel)
			{
				continue;
			}
		}

		if (bOverallCollision || MultiDataIter->ComponentType == ECompType::SingleCom)
		{
			//遍历到单部件时TreeData为它上层的多部件
			const auto& UseComponentTreeData = MultiDataIter->ComponentType == ECompType::SingleCom ? InComponentTreeData : MultiDataIter;
			TMap<FString, FParameterData> ParamMap;
			for (auto& Iter : UseComponentTreeData->ComponentParameters)
			{
				ParamMap.Add(Iter.Data.name, Iter);
			}

			FVector  DefaultExtent = FVector::Zero();

			//特殊宽高深
			FVector SpecialExtent = FVector::Zero();
			FVector SpecialOffset = FVector::Zero();
			bool bHasSpecialDWH = GetSpecialSizeAndOffset(ParamMap, SpecialExtent, SpecialOffset);

			//固定宽高深
			FVector FixedExtent = FVector::Zero();
			FVector FixedOffset = FVector::Zero();
			bool bHasFixedDWH = GetFixedSizeAndOffset(ParamMap, FixedExtent, FixedOffset);

			//默认宽高深WHD
			TMap<FString, FParameterData> BaseParamsMap;
			bool bHasDHW = UDSCupboardLibrary::CombineBaseDHWParameters(BaseParamsMap, ParamMap);
			if (bHasDHW)
			{
				UDSCupboardLibrary::ConvertBaseDHWParametersToFVector(DefaultExtent, BaseParamsMap);
			}

			//如果没有宽高深数据,跳过
			if (!bHasDHW)
			{
				UE_LOG(AdaptationExecuterLog, Warning, TEXT("Node DHW Data Lost,Please Check Data.Code: %s,UUID:%s "), *MultiDataIter->Code, *MultiDataIter->UUID);
				continue;
			}
			DefaultExtent = DefaultExtent * 0.5f;



			//求板件世界位置
			FTransform ComponentRelativeTransform = RelativeRootTransform;
			TArray<TSharedPtr<FMultiComponentDataItem>> ComponentPath;
			TargetCupboardModel->CollectComponentPath_Public(TargetCupboardModel->GetModelInfo().ComponentTreeData, UseComponentTreeData, ComponentPath);

			if (ComponentPath.IsValidIndex(1))
			{
				for (int32 Index = 1; Index < ComponentPath.Num(); ++Index)
				{
					FTransform CurrentTransform;
					CurrentTransform.SetLocation(ComponentPath[Index]->ComponentLocation.GetLocation());
					CurrentTransform.SetRotation(ComponentPath[Index]->ComponentRotation.GetRotation().Quaternion());
					CurrentTransform.SetScale3D(ComponentPath[Index]->ComponentScale.GetScale());
					ComponentRelativeTransform = CurrentTransform * ComponentRelativeTransform;
				}
			}

			//获取柜体包围盒，求当前包围盒相对柜体包围盒的OBB

			//设置OBB
			FOrientedBox3d Oribox;
			Oribox.Frame.Rotation = FQuaterniond(ComponentRelativeTransform.GetRotation());

			if (bHasFixedDWH)
			{
				Oribox.Frame.Origin = ComponentRelativeTransform.TransformPosition(FixedExtent);
				Oribox.Frame.Origin = Oribox.Frame.FromFramePoint(FixedOffset);
				Oribox.Extents = FixedExtent;
			}
			else
			{
				Oribox.Frame.Origin = ComponentRelativeTransform.TransformPosition(DefaultExtent);
				Oribox.Extents = DefaultExtent;
			}

			bool bUseRelativeOBB = !RootTrans.GetRotation().Equals(ComponentRelativeTransform.GetRotation(), 0.001);
			if (bUseRelativeOBB)
			{
				//如果旋转不一样 
				FBox RealtiveAABB(EForceInit::ForceInit);
				Oribox.EnumerateCorners([&](const FVector3d& Corner)
					{
						RealtiveAABB += RootTrans.InverseTransformPosition(Corner);

					});

				Oribox.Frame.Origin = RootTrans.TransformPosition(RealtiveAABB.GetCenter());
				Oribox.Extents = RealtiveAABB.GetSize() * 0.5f;
				Oribox.Frame.Rotation = FQuaterniond(RootTrans.GetRotation());
			}

			//判断是否是自适应功能件
			EIntersectionDataType  IntersectionDataType = EIntersectionDataType::E_Unknown;

			if (UDSCupboardLibrary::IsDoorPanelDrawer(TargetCupboardModel) || TargetCupboardModel->GetModelType() == EDSModelType::E_Custom_FunctionalDrawer)
			{
				IntersectionDataType = EIntersectionDataType::E_Drawer;
			}
			else if (UDSCupboardLibrary::IsFunctionalCupboardModel(TargetCupboardModel))
			{
				IntersectionDataType = EIntersectionDataType::E_Functional;
			}

			//如果是功能件，进行自适应规则处理
			if (bIsFunctionalCupboardModel)
			{
				FAdaptiveAdsorptionRule3D AdaptiveRule = TargetCupboardModel->GetModelInfo().ComponentTreeData->AdaptationData;

				FVector MinRelativePoint = -Oribox.Extents;
				FVector MaxRelativePoint = Oribox.Extents;

				if (!FMath::IsNearlyZero(AdaptiveRule.XAxisRule.NegativeOffset, ADAPTIVE_TOLERANCE))
				{
					MinRelativePoint.X -= AdaptiveRule.XAxisRule.NegativeOffset;
				}
				if (!FMath::IsNearlyZero(AdaptiveRule.XAxisRule.PositiveOffset, ADAPTIVE_TOLERANCE))
				{
					MaxRelativePoint.X += AdaptiveRule.XAxisRule.PositiveOffset;
				}

				if (!FMath::IsNearlyZero(AdaptiveRule.YAxisRule.NegativeOffset, ADAPTIVE_TOLERANCE))
				{
					MinRelativePoint.Y -= AdaptiveRule.YAxisRule.NegativeOffset;
				}
				if (!FMath::IsNearlyZero(AdaptiveRule.YAxisRule.PositiveOffset, ADAPTIVE_TOLERANCE))
				{
					MaxRelativePoint.Y += AdaptiveRule.YAxisRule.PositiveOffset;
				}


				if (!FMath::IsNearlyZero(AdaptiveRule.ZAxisRule.NegativeOffset, ADAPTIVE_TOLERANCE))
				{
					MinRelativePoint.Z -= AdaptiveRule.ZAxisRule.NegativeOffset;
				}
				if (!FMath::IsNearlyZero(AdaptiveRule.ZAxisRule.PositiveOffset, ADAPTIVE_TOLERANCE))
				{
					MaxRelativePoint.Z += AdaptiveRule.ZAxisRule.PositiveOffset;
				}

				FVector RelativeCenter = (MaxRelativePoint + MinRelativePoint) * 0.5f;
				Oribox.Frame.Origin = Oribox.Frame.FromFramePoint(RelativeCenter);
				Oribox.Extents = (MaxRelativePoint - MinRelativePoint) * 0.5f;
			}

			TSharedPtr<FFunctionalIntersectionMesh> ChildrenAdaptive =
				MakeShared<FFunctionalIntersectionMesh>(Oribox.Extents, Oribox.Frame.Origin, FQuat(Oribox.Frame.Rotation), IntersectionDataType);
			ChildrenAdaptive->SetLinkModelBaseInfo(TargetCupboardModel->GetComponentTreeDataRef()->UUID, UseComponentTreeData->Description, static_cast<int>(TargetCupboardModel->GetModelType()), MultiDataIter->UUID);
			ChildrenAdaptive->GetLinkModelBaseInfoRef().OwnerModelPtr = (TargetCupboardModel);
			ChildrenAdaptive->bUseRelativeOBB = bUseRelativeOBB;
			ChildrenAdaptive->bHasSpecialExtents = bHasSpecialDWH;
			ChildrenAdaptive->SpecialExtent = SpecialExtent;
			ChildrenAdaptive->SepcialOffset = SpecialOffset;
			OutEnv.Add(ChildrenAdaptive);

			if (UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(MultiDataIter->ModelType) == EDSModelType::E_Custom_FunctionalDrawer
				|| UDSToolLibrary::IsCustomDoorType(UDesignStationFunctionLibrary::ConvertCustomTypeToModelType(MultiDataIter->ModelType)))
			{
				OutAutoEnv.Add(ChildrenAdaptive);
			}
		}
		else //没有单独构建的添加到数组管理
		{
			GeneratorTargetModelIntersectionEnvConsiderOffset(
				TargetCupboardModel, 
				RelativeRootTransform, 
				OutEnv, 
				OutAutoEnv,
				MultiDataIter, 
				IgnoreModelTreeData, 
				InDependencyInfo
			);
		}
	}
}

void FDrawerAdaptationOperator::ExecuteStepAdaptation(const TSharedPtr<FDSBroadcastMarkData>& InBroadcastMarkPtr)
{
	BroadcastMarkPtr = InBroadcastMarkPtr;
	PrepareAdaptation(SourceModel);
	TSharedPtr<FFunctionalExecuterInitializedData> FunctionalInitializedData = StaticCastSharedPtr<FFunctionalExecuterInitializedData>(SourceAdaptationInfo->GetInitializedDataRef());
	FunctionalInitializedData->bEnableAlignedAdsorption = false;

	//重新计算依赖板件自适应
	SourceAdaptationInfo->HandleAdaptiveAndAdsorptionWithDependent(AdaptationEnvs, nullptr);

	FAdaptiveAdsorptionResault Resault;
	CompleteAdaptation(Resault, false);

}

FDSVoidCoroutine FDrawerAdaptationOperator::DrawerEven(const TArray<FDecimal> InEvenHeight, const FDecimal InUpExtent, const FDecimal InDownExtent)
{
	if (InEvenHeight.IsEmpty())
	{
		co_return;
	}

	UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(SourceModel);
	if (CupboardModel == nullptr)
	{
		co_return;
	}

	UDSCupboardModel* OwnerModel = Cast<UDSCupboardModel>(CupboardModel->GetOwnerModel());
	if (OwnerModel == nullptr)
	{
		co_return;
	}

	FString DrawerFolderId = CupboardModel->GetModelInfoRef().ComponentTreeData->ComponentID.GetFormattedValue();

	const FString ResourcePath = FPaths::Combine(FPaths::ProjectSavedDir(), FString::Printf(TEXT("Resources/%s.dat"), *DrawerFolderId));
	FRefToLocalFileData NewFileData;
	if (!IFileManager::Get().FileExists(*ResourcePath) || !FProtobufOperatorFunctionLibrary::LoadRelationFromFile(ResourcePath, NewFileData))
	{
		UE_LOG(LogDrawerAdaptationOperator, Error, TEXT("Custom file [%s] does not exists!"), *DrawerFolderId);
		co_return;
	}

	// 记录抽屉宽度和深度
	FString OldDrawerWidth, OldDrawerDepth;
	if (FParameterData* Old_Param_W = CupboardModel->GetModelInfoRef().ComponentTreeData->ComponentParameters.FindByPredicate([](const FParameterData& InParam) { return InParam.Data.name == TEXT("W"); }))
	{
		if (FParameterData* New_Param_W = NewFileData.ParamDatas.FindByPredicate([](const FParameterData& InParam) { return InParam.Data.name == TEXT("W"); }))
		{
			New_Param_W->Data.value = Old_Param_W->Data.value;
			New_Param_W->Data.expression = Old_Param_W->Data.expression;
		}
	}

	if (FParameterData* Old_Param_D = CupboardModel->GetModelInfoRef().ComponentTreeData->ComponentParameters.FindByPredicate([](const FParameterData& InParam) { return InParam.Data.name == TEXT("D"); }))
	{
		if (FParameterData* New_Param_D = NewFileData.ParamDatas.FindByPredicate([](const FParameterData& InParam) { return InParam.Data.name == TEXT("D"); }))
		{
			New_Param_D->Data.value = Old_Param_D->Data.value;
			New_Param_D->Data.expression = Old_Param_D->Data.expression;
		}
	}

	// 记录上下左右关联板厚
	FString LeftBoardThickness, RightBoardThickness, UpBoardThickness, DownBoardThickness;
	if (FParameterData* Old_Param_SCBJT = CupboardModel->GetModelInfoRef().ComponentTreeData->ComponentParameters.FindByPredicate([](const FParameterData& InParam) { return InParam.Data.name == TEXT("SCBJT"); }))
	{
		if (FParameterData* New_Param_SCBJT = NewFileData.ParamDatas.FindByPredicate([](const FParameterData& InParam) { return InParam.Data.name == TEXT("SCBJT"); }))
		{
			New_Param_SCBJT->Data.value = Old_Param_SCBJT->Data.value;
			New_Param_SCBJT->Data.expression = Old_Param_SCBJT->Data.expression;
		}
	}

	if (FParameterData* Old_Param_XCBJT = CupboardModel->GetModelInfoRef().ComponentTreeData->ComponentParameters.FindByPredicate([](const FParameterData& InParam) { return InParam.Data.name == TEXT("XCBJT"); }))
	{
		if (FParameterData* New_Param_XCBJT = NewFileData.ParamDatas.FindByPredicate([](const FParameterData& InParam) { return InParam.Data.name == TEXT("XCBJT"); }))
		{
			New_Param_XCBJT->Data.value = Old_Param_XCBJT->Data.value;
			New_Param_XCBJT->Data.expression = Old_Param_XCBJT->Data.expression;
		}
	}

	if (FParameterData* Old_Param_ZCBJT = CupboardModel->GetModelInfoRef().ComponentTreeData->ComponentParameters.FindByPredicate([](const FParameterData& InParam) { return InParam.Data.name == TEXT("ZCBJT"); }))
	{
		if (FParameterData* New_Param_ZCBJT = NewFileData.ParamDatas.FindByPredicate([](const FParameterData& InParam) { return InParam.Data.name == TEXT("ZCBJT"); }))
		{
			New_Param_ZCBJT->Data.value = Old_Param_ZCBJT->Data.value;
			New_Param_ZCBJT->Data.expression = Old_Param_ZCBJT->Data.expression;
		}
	}

	if (FParameterData* Old_Param_YCBJT = CupboardModel->GetModelInfoRef().ComponentTreeData->ComponentParameters.FindByPredicate([](const FParameterData& InParam) { return InParam.Data.name == TEXT("YCBJT"); }))
	{
		if (FParameterData* New_Param_YCBJT = NewFileData.ParamDatas.FindByPredicate([](const FParameterData& InParam) { return InParam.Data.name == TEXT("YCBJT"); }))
		{
			New_Param_YCBJT->Data.value = Old_Param_YCBJT->Data.value;
			New_Param_YCBJT->Data.expression = Old_Param_YCBJT->Data.expression;
		}
	}

	if (FParameterData* Old_Param_MYGLX = CupboardModel->GetModelInfoRef().ComponentTreeData->ComponentParameters.FindByPredicate([](const FParameterData& InParam){ return InParam.Data.name == TEXT("MYGLX"); }))
	{
		if (FParameterData* New_Param_MYGLX = NewFileData.ParamDatas.FindByPredicate([](const FParameterData& InParam) { return InParam.Data.name == TEXT("MYGLX"); }))
		{
			New_Param_MYGLX->Data.value = Old_Param_MYGLX->Data.value;
			New_Param_MYGLX->Data.expression = Old_Param_MYGLX->Data.expression;
		}
	}

	// 获取抽屉起始点位置
	TSharedPtr<FDSBaseProperty> ModelProperty = CupboardModel->GetPropertySharedPtr();
	FRotator DrawerRotation = ModelProperty->TransformProperty.GetRotation();

	FAdaptationEvenInfo EvenInfo = GetAdaptationEvenInfo();
	FVector DrawerStartPos = EvenInfo.SpaceBox.Frame.ToFramePoint(ModelProperty->TransformProperty.GetLocation());
	DrawerStartPos.Z = -EvenInfo.SpaceBox.Extents.Z - (InDownExtent / 10).ToDouble();
	
	{
		TSharedPtr<FIntersectionDynamicMesh> DownMesh = EvenInfo.GetAroundEnv(EAdaptationDirection::E_Down).Pin();
		if (DownMesh.IsValid() && DownMesh->GetIntersectionType() == EIntersectionDataType::E_Drawer)
		{
			if (UDSCupboardModel* DownModel = Cast<UDSCupboardModel>(DownMesh->GetLinkModelBaseInfo().OwnerModelPtr.Get()))
			{
				if (DownModel->GetModelType() == EDSModelType::E_Custom_FunctionalDrawer)
				{
					double DownT = FCString::Atod(*DownModel->GetModelInfoRef().ComponentTreeData->GetParameterValue(PARAM_XCBJT_STR)) * 0.1;
					DrawerStartPos.Z += DownT; //默认位置上移
				}
			}
		}
	}

	// 删除当前选中的抽屉
	UDSCupboardModel* RootModel = CupboardModel->GetRootCupboardModel();
	InsertRevokeData(RootModel, FDSModelExecuteType::ExecuteUpdateSelf, TEXT(""));

	RootModel->RemoveFunctionalCupboardModel(CupboardModel);
	CupboardModel->OnExecuteAction(FDSModelExecuteType::ExecuteDelete, FDSBroadcastMarkData::BroadcastToMVCMark);
	
	FParameterData* Param_H = NewFileData.ParamDatas.FindByPredicate([](const FParameterData& InParam) { return InParam.Data.name == TEXT("H"); });
	if (Param_H != nullptr)
	{
		// 均分出来的抽屉高度不再需要自适应
		Param_H->bEnableAdaptation = false;
	}

	// 生成均分的抽屉并等待应用完风格后再进行新的自适应
	TArray<UDSCupboardModel*> DrawerModels;
	for (int32 Index = 0; Index < InEvenHeight.Num(); ++Index)
	{
		UDSCupboardModel* NewDrawerModel = NewObject<UDSCupboardModel>();
		TSharedPtr<FDSBaseProperty> NewDrawerProperty = NewDrawerModel->GetPropertySharedPtr();

		NewDrawerProperty->TransformProperty.SetLocation(EvenInfo.SpaceBox.Frame.FromFramePoint(DrawerStartPos));
		NewDrawerProperty->TransformProperty.SetRotation(DrawerRotation);

		double CurrentDrawerHeight = (InEvenHeight[Index] * 0.1f).ToDouble();
		DrawerStartPos.Z += CurrentDrawerHeight;

		if (Param_H != nullptr)
		{
			Param_H->Data.value = InEvenHeight[Index].ToString(1);
			Param_H->Data.expression = InEvenHeight[Index].ToString(1);
		}

		UDSMVCSubsystem::GetInstance()->SpawnViewUnion(NewDrawerModel, FDSBroadcastMarkData::SpawnBroadcastMark);
		NewDrawerModel->InitConstruct_Functional(NewFileData, OwnerModel, FDSBroadcastMarkData::OnlyOutlineBroadcastMark);

		FDSDrawerEvenAwaiter EvenAwaiter;
		EvenAwaiter.OperatorHandle = SharedThis(this);
		EvenAwaiter.Model = NewDrawerModel;

		co_await EvenAwaiter;

		DrawerModels.Add(NewDrawerModel);
	}

	for (UDSCupboardModel* DrawerModel : DrawerModels)
	{
		//TSharedPtr<FDrawerAdaptationOperator> DrawerOperator = StaticCastSharedPtr<FDrawerAdaptationOperator>(DrawerModel->GetAdaptationOperator());
		//if (!DrawerOperator)
		{
		}
		TSharedPtr<FDrawerAdaptationOperator> DrawerOperator = StaticCastSharedPtr<FDrawerAdaptationOperator>(DrawerModel->CreateAdaptationOperator());

		DrawerOperator->ExecuteStepAdaptation(FDSBroadcastMarkData::OnlyOutlineBroadcastMark);
	}
}

bool FDrawerAdaptationOperator::GetIntersectionHitPoint(FVector& Start, FVector& End, const FVector& Dir) const
{
	const auto& SourceOriBox = SourceAdaptationInfo->GetAdaptationOriBoxWithoutAdaptiveRulerOffset();

	//UE_LOG(LogTemp, Warning, TEXT("Drawer -- GetIntersectionHitPoint [%s][%s]"), *SourceOriBox.Frame.Origin.ToString(), *SourceOriBox.Extents.ToString());

	//DrawDebugBox(GWorld, SourceOriBox.Frame.Origin, SourceOriBox.Extents, FColor::Red, true);

	Start = SourceOriBox.Frame.FromFramePoint(Dir * SourceOriBox.Extents);
	FVector RayDir = SourceOriBox.Frame.FromFrameVector(Dir);

	bool IsFullInSpace = true;
	if (TSharedPtr<FDrawerAdaptiveAdsorption> Adsorption = StaticCastSharedPtr<FDrawerAdaptiveAdsorption>(SourceAdaptationInfo))
	{
		if (FMath::IsNearlyEqual(FVector::DotProduct(RayDir, SourceOriBox.AxisX()), 1.0, ADAPTIVE_TOLERANCE))
		{//X
			EDrawerAdaptiveRelation Relation = Adsorption->GetXPosRelation();
			IsFullInSpace = Relation == EDrawerAdaptiveRelation::DAR_Normal || Relation == EDrawerAdaptiveRelation::DAR_Embedded;
		}
		else if (FMath::IsNearlyEqual(FVector::DotProduct(RayDir, -SourceOriBox.AxisX()), 1.0, ADAPTIVE_TOLERANCE))
		{//-x
			EDrawerAdaptiveRelation Relation = Adsorption->GetXNegRelation();
			IsFullInSpace = Relation == EDrawerAdaptiveRelation::DAR_Normal || Relation == EDrawerAdaptiveRelation::DAR_Embedded;
		}
		else if (FMath::IsNearlyEqual(FVector::DotProduct(RayDir, SourceOriBox.AxisZ()), 1.0, ADAPTIVE_TOLERANCE))
		{//z
            EDrawerAdaptiveRelation Relation = Adsorption->GetZPosRelation();
            IsFullInSpace = Relation == EDrawerAdaptiveRelation::DAR_Normal || Relation == EDrawerAdaptiveRelation::DAR_Embedded;
		}
		else if (FMath::IsNearlyEqual(FVector::DotProduct(RayDir, -SourceOriBox.AxisZ()), 1.0, ADAPTIVE_TOLERANCE))
		{//-z
            EDrawerAdaptiveRelation Relation = Adsorption->GetZNegRelation();
            IsFullInSpace = Relation == EDrawerAdaptiveRelation::DAR_Normal || Relation == EDrawerAdaptiveRelation::DAR_Embedded;
		}
	}
	if (IsFullInSpace)
	{
		End = Start;

		return false;
	}
	

	FRayHitResault HitResualt;
	SourceAdaptationInfo->FindHitNearestPoint(Start - RayDir * 0.1f, RayDir, AdaptationEnvs, HitResualt);
	if (HitResualt.HitTarget.IsValid())
	{
		if (HitResualt.HitTarget->GetOriBox().Contains(Start))
		{
			End = Start;
		}
		else
		{
			End = HitResualt.HitPoint;
		}
		return true;
	}
	return false;
}

void FDrawerAdaptationOperator::UpdateInitializedData()
{
	if (!SourceModel)
	{
		return;
	}
	UDSCupboardModel* SourceCupboardModel = Cast<UDSCupboardModel>(SourceModel);
	if (!SourceCupboardModel)
	{
		return;
	}

	if (!SourceAdaptationInfo->IsInitializedDataValid())
	{
		//return;
		SourceAdaptationInfo->SetInitializedData(MakeShared<FFunctionalExecuterInitializedData>());
	}

	auto& InitializedData = SourceAdaptationInfo->GetInitializedDataRef();
	GenerateInitializedData_Drawer(InitializedData);
	SourceAdaptationInfo->OnInitializeDataUpdate();
}

void FDrawerAdaptationOperator::GenerateSpaceInfo()
{
	//增加空间依赖，用于生成把手
	if (SourceModel->GetModelType() == EDSModelType::E_Custom_FunctionalDrawer)
	{
		UDSCupboardModel* SourceCupboardModel = Cast<UDSCupboardModel>(SourceModel);

		TMap<FString, FParameterData> OutParentOverriderParmeters;
		SourceCupboardModel->GetSelfComponentOverriderParametersRef(OutParentOverriderParmeters);
		auto TD = SourceCupboardModel->GetModelInfo().ComponentTreeData;

		TMap<FString, FParameterData> BaseParamsMap;
		FVector DefaultExtent;

		if (UDSCupboardLibrary::CombineBaseDHWParameters(BaseParamsMap, OutParentOverriderParmeters))
		{
			auto W = FCString::Atof(*BaseParamsMap[PARAM_W_STR].Data.value) * 0.1;
			auto D = FCString::Atof(*BaseParamsMap[PARAM_D_STR].Data.value) * 0.1;
			auto H = FCString::Atof(*BaseParamsMap[PARAM_H_STR].Data.value) * 0.1;

			FTransform WorldTransform = SourceCupboardModel->GetWorldTransform();
			UDSCupboardLibrary::ConvertBaseDHWParametersToFVector(DefaultExtent, BaseParamsMap);

			//FVector Center = WorldTransform.GetLocation() + WorldTransform.TransformVector(DefaultExtent * 0.5f);
			FVector Y = WorldTransform.GetRotation().GetAxisY();
			FVector X = WorldTransform.GetRotation().GetAxisX();
			FVector Z = FVector::ZAxisVector;

			auto DependencyInfo = UDSModelDependencySubsystem::GetInstance()->FindDoorDependencyInfo(TD->UUID);
			DependencyInfo.DoorPlane.Points =
			{
				(W * X ) + (D * Y) + (H * Z),
				(D * Y ) + (H * Z ),
				(D * Y ),
				(W * X ) + (D * Y)
			};
			DependencyInfo.DoorPlane.Center = (DependencyInfo.DoorPlane.Points[0] + DependencyInfo.DoorPlane.Points[2]) * 0.5;
			DependencyInfo.DoorPlane.Normal = Y;
			UDSModelDependencySubsystem::GetInstance()->AddDoorDependencyInfo(TD->UUID, DependencyInfo);
		}
	}
}

//void FDrawerAdaptationOperator::CompleteAdaptation(FAdaptiveAdsorptionResault& OutResault, bool bRecalculateDependentedNode)
//{
//	UDSCupboardModel* SourCupboardModel = Cast<UDSCupboardModel>(SourceModel);
//
//	UDSCupboardModel* RootModel = SourCupboardModel->GetRootCupboardModel();
//
//	TSharedPtr<FFunctionalDependencyMap> DependentRootInfo = RootModel->GetSubFunctionalNodeDependencyMap();
//
//	TSharedPtr<FFunctionalDependencyInfo> DependentInfo = nullptr;
//	if (DependentRootInfo.IsValid())
//	{
//		DependentInfo = DependentRootInfo->GetDependencyInfo(GetSourceAdapationInfo()->GetOwnerUUID());
//	}
//
//	//重新计算依赖板件自适应
//	//遍历依赖自己的节点
//
//	HandelAdaptationResault(OutResault);
//}

void FDrawerAdaptationOperator::GenerateInitializedData_Drawer(const TSharedPtr<FAdaptationExecuterInitializedData>& InitialziedData)
{
	UDSCupboardModel* SourceCupboardModel = Cast<UDSCupboardModel>(SourceModel);

	UDSCupboardModel* TargetCupboardModel = Cast<UDSCupboardModel>(TargetModel);

	TSharedPtr<FFunctionalExecuterInitializedData> InInitialziedData = StaticCastSharedPtr<FFunctionalExecuterInitializedData>(InitialziedData);

	TMap<FString, FParameterData> OutParentOverriderParmeters;
	SourceCupboardModel->GetSelfComponentOverriderParametersRef(OutParentOverriderParmeters);

	TMap<FString, FParameterData> BaseParamsMap;
	FVector DefaultExtent, MaxExtents, MinExtents;
	if (UDSCupboardLibrary::CombineBaseDHWParameters(BaseParamsMap, OutParentOverriderParmeters))
	{
		UDSCupboardLibrary::ConvertBaseDHWParametersToFVector(DefaultExtent, BaseParamsMap);
		UDSCupboardLibrary::ConvertMaxDHWParametersToFVector(MaxExtents, BaseParamsMap);
		UDSCupboardLibrary::ConvertMinDHWParametersToFVector(MinExtents, BaseParamsMap);

		DefaultExtent *= 0.5f;
		InInitialziedData->bExtentsXEnableAdaptation = BaseParamsMap[PARAM_W_STR].bEnableAdaptation;
		InInitialziedData->bExtentsYEnableAdaptation = BaseParamsMap[PARAM_D_STR].bEnableAdaptation;
		InInitialziedData->bExtentsZEnableAdaptation = BaseParamsMap[PARAM_H_STR].bEnableAdaptation;


		//极值不合法关闭自适应
		if (BaseParamsMap[PARAM_W_STR].Data.is_enum || BaseParamsMap[PARAM_W_STR].Data.min_value.IsEmpty() || BaseParamsMap[PARAM_W_STR].Data.max_value.IsEmpty())
		{
			InInitialziedData->bExtentsXEnableAdaptation = false;
		}
		if (BaseParamsMap[PARAM_D_STR].Data.is_enum || BaseParamsMap[PARAM_D_STR].Data.min_value.IsEmpty() || BaseParamsMap[PARAM_D_STR].Data.max_value.IsEmpty())
		{
			InInitialziedData->bExtentsYEnableAdaptation = false;
		}
		if (BaseParamsMap[PARAM_H_STR].Data.is_enum || BaseParamsMap[PARAM_H_STR].Data.min_value.IsEmpty() || BaseParamsMap[PARAM_H_STR].Data.max_value.IsEmpty())
		{
			InInitialziedData->bExtentsZEnableAdaptation = false;
		}


		if (!InInitialziedData->bExtentsXEnableAdaptation)
		{
			InInitialziedData->DefaultExtents.X = DefaultExtent.X;
		}
		if (!InInitialziedData->bExtentsYEnableAdaptation)
		{
			InInitialziedData->DefaultExtents.Y = DefaultExtent.Y;
		}
		if (!InInitialziedData->bExtentsZEnableAdaptation)
		{
			InInitialziedData->DefaultExtents.Z = DefaultExtent.Z;
		}
	}
	UDSCupboardModel* SourceRootModel = SourceCupboardModel->GetRootCupboardModel();

	FTransform WorldTransform = SourceCupboardModel->GetWorldTransform();

	FVector Center = WorldTransform.GetLocation() + WorldTransform.TransformVector(DefaultExtent);

	InInitialziedData->Rotation = WorldTransform.GetRotation();

	if (SourceCupboardModel->IsNewGenerate())
	{
		//如果是新生成的模型，使用根模型的旋转

		InInitialziedData->Center = Center;
		InInitialziedData->Rotation = SourceRootModel->GetWorldTransform().GetRotation();
		InInitialziedData->Extents = DefaultExtent;

		InInitialziedData->SelfRealOriBox = FOrientedBox3d(
			FFrame3d(Center, FQuaterniond::Identity()),
			DefaultExtent
		);
	}
	else
	{
		//已存在的模型使用自身OBB计算相对根节点的OBB
		const auto& RootTrans = SourceRootModel->GetWorldTransform();
		if (!RootTrans.GetRotation().Equals(WorldTransform.GetRotation(), 0.001))
		{

			FOrientedBox3d Oribox;
			Oribox.Frame.Origin = Center;
			Oribox.Frame.Rotation = FQuaterniond(WorldTransform.GetRotation());
			Oribox.Extents = DefaultExtent;

			FBox RealtiveAABB(EForceInit::ForceInit);
			Oribox.EnumerateCorners([&](const FVector3d& Corner)
				{
					RealtiveAABB += RootTrans.InverseTransformPosition(Corner);

				});

			InInitialziedData->Center = RootTrans.TransformPosition(RealtiveAABB.GetCenter());
			InInitialziedData->Extents = RealtiveAABB.GetSize() * 0.5f;
			InInitialziedData->Rotation = RootTrans.GetRotation();


			InInitialziedData->SelfRealOriBox.Frame.Origin = InInitialziedData->Center - Oribox.Frame.Origin;
			InInitialziedData->SelfRealOriBox.Extents = Oribox.Extents;
			InInitialziedData->SelfRealOriBox.Frame.Rotation = FQuaterniond(RootTrans.GetRotation().Inverse() * WorldTransform.GetRotation());


			//如果旋转不一样 关闭自适应
			InInitialziedData->bExtentsXEnableAdaptation = false;
			InInitialziedData->bExtentsYEnableAdaptation = false;
			InInitialziedData->bExtentsZEnableAdaptation = false;

			InInitialziedData->bUsedRelativedOBB = true;
		}
		else
		{
			InInitialziedData->Center = Center;
			InInitialziedData->Extents = DefaultExtent;
			InInitialziedData->Rotation = WorldTransform.GetRotation();

			InInitialziedData->SelfRealOriBox = FOrientedBox3d(
				FFrame3d(InInitialziedData->Center, FQuat::Identity),
				DefaultExtent
			);
			InInitialziedData->bUsedRelativedOBB = false;
		}
	}
	InInitialziedData->MaxExtents = MaxExtents * 0.5f;
	InInitialziedData->MinExtents = MinExtents * 0.5f;

	InInitialziedData->AdaptativeRule = SourceCupboardModel->GetModelInfo().ComponentTreeData->AdaptationData;
	FAdaptiveAdsorptionRule3D AdaptiveRule = SourceCupboardModel->GetModelInfo().ComponentTreeData->AdaptationData;
	SourceAdaptationInfo->SetAdaptiveRule(AdaptiveRule);

	//设置吸附阈值
	float  Threshold = UDSConfigSubsystem::GetInstance()->GetValue_Custom_Float(DSSetting::Custom::ECustomType::E_DefaultSnapD);
	InInitialziedData->DistanceAdsorptionThreshold = Threshold * 0.1f;
	InInitialziedData->AlignedAdsorptionThreshold = InInitialziedData->DistanceAdsorptionThreshold;

	FQuat TargetCupboardQuatation;
	TargetCupboardModel->GetModelOrientedBoundingBox(InInitialziedData->CabinetOriBox.Frame.Origin, InInitialziedData->CabinetOriBox.Extents, TargetCupboardQuatation);
	InInitialziedData->CabinetOriBox.Frame.Rotation = FQuaterniond(TargetCupboardQuatation);

}

void FDrawerAdaptationOperator::GenerateAdaptationEnvData()
{
	FFunctionalAdaptationOperator::GenerateAdaptationEnvData();
	GenerateSpaceInfo();
}

void FDrawerAdaptationOperator::GenerateAdditionalAdaptationEnvData()
{
	ApplyOffsetAdaptationEnvs.Reset();
	DoorDrawerAdaptationEnvs.Reset();

	UDSCupboardModel* TopRootModel = Cast<UDSCupboardModel>(TargetModel);
	FTransform RootTrans = FTransform(TopRootModel->GetProperty()->GetTransformProperty().Rotation, TopRootModel->GetProperty()->GetTransformProperty().Location);

	UDSCupboardModel* SourceCupboardModel = Cast<UDSCupboardModel>(SourceModel);
	const TSharedPtr<FMultiComponentDataItem>& SourceComponentTreeData = SourceCupboardModel->GetComponentTreeDataRef();
	GeneratorTargetModelIntersectionEnvConsiderOffset(TopRootModel, RootTrans, ApplyOffsetAdaptationEnvs, DoorDrawerAdaptationEnvs, TopRootModel->GetComponentTreeDataRef(), SourceComponentTreeData, nullptr);

}

void FDrawerAdaptationOperator::GenerateAdapationEnvDataWithoutDenpendented()
{
	GenerateAdaptationEnvData();
	GenerateAdditionalAdaptationEnvData();
}

void FDrawerAdaptationOperator::OnAdaptiveAndAdSorptionCallback(const TSharedPtr<FAdaptationData>& AdaptationData)
{
	if (SourceModel != nullptr && SourceModel->IsA<UDSCupboardModel>())
	{
		auto FunctionalInitializedData = StaticCastSharedPtr<FFunctionalExecuterInitializedData>(SourceAdaptationInfo->GetInitializedDataRef());
        UDSCupboardModel* SourceCupboardModel = Cast<UDSCupboardModel>(SourceModel);
		if (AdaptationData->bExtentsModified)
		{
			ParseAdaptiveSizeInfo(AdaptationData);
		}
		else if(AdaptationData->bTransformModified)
		{
			ParseAdaptiveTransformInfo(AdaptationData);
		}
	}
}

void FDrawerAdaptationOperator::ShowDebug(UObject* WorldContextObject)
{//

	if (SourceAdaptationInfo.IsValid())
	{
		SourceAdaptationInfo->DrawOriBox(WorldContextObject, SourceAdaptationInfo->GetAdaptationOriBox(), FLinearColor::Yellow);
		//SourceAdaptationInfo->DrawOriBox(WorldContextObject, SourceAdaptationInfo->GetIntersectionData().GetOriBox(), FLinearColor::Blue);
		SourceAdaptationInfo->DrawOriBox(WorldContextObject, SourceAdaptationInfo->GetOutAdaptationOriBox(), FLinearColor::Green);

		for (auto& Iter : AdaptationEnvs)
		{
			SourceAdaptationInfo->DrawOriBox(WorldContextObject, Iter->GetOriBox(), FLinearColor::Red);
		}

		for (const auto& AOAE : ApplyOffsetAdaptationEnvs)
		{
			SourceAdaptationInfo->DrawOriBox(WorldContextObject, AOAE->GetOriBox(), FLinearColor::Blue);
		}
	}
}

FAdaptationEvenInfo FDrawerAdaptationOperator::GetRealAdaptationEvenInfo()
{
	return SourceAdaptationInfo->ExecuteEven(ApplyOffsetAdaptationEnvs);
}

bool FDrawerAdaptationOperator::GenerateBaseInfo(const TMap<FString, FParameterData>& ParamsInfo, TMap<FString, FParameterData>& BaseParamsMap, FVector& DefaultExtent, FVector& MaxExtents, FVector& MinExtents)
{
	if(UDSCupboardLibrary::CombineBaseDHWParameters(BaseParamsMap, ParamsInfo))
	{
		UDSCupboardLibrary::ConvertBaseDHWParametersToFVector(DefaultExtent, BaseParamsMap);
		UDSCupboardLibrary::ConvertMaxDHWParametersToFVector(MaxExtents, BaseParamsMap);
		UDSCupboardLibrary::ConvertMinDHWParametersToFVector(MinExtents, BaseParamsMap);
		
		return true;
	}

	return false;
}

bool FDrawerAdaptationOperator::NeedGenerateSpaceFGInfo(const TMap<FString, FParameterData>& ParamsInfo)
{
	return ParamsInfo.Contains(PARAM_SCBJT_STR) && ParamsInfo.Contains(PARAM_XCBJT_STR) && ParamsInfo.Contains(PARAM_ZCBJT_STR) && ParamsInfo.Contains(PARAM_YCBJT_STR)   //覆盖值
			&& ParamsInfo.Contains(PARAM_SBFG) && ParamsInfo.Contains(PARAM_XBFG) && ParamsInfo.Contains(PARAM_ZBFG) && ParamsInfo.Contains(PARAM_YBFG)					//覆盖类型
			&& ParamsInfo.Contains(PARAM_MYGLX_STR);																																//掩盖类型（外盖、内嵌）
}

bool FDrawerAdaptationOperator::GenerateAndEnsureSpaceFGInfo(const TSharedPtr<FMultiComponentDataItem>& ModelInfo, TMap<FString, FParameterData>& ParamsInfo,
	FVector& DefaultExtent, double& UpExtent, double& DownExtent, double& LeftExtent, double& RightExtent)
{
	bool NeedUpdate = false;
	
	auto  SBFG = FCString::Atoi(*ParamsInfo[PARAM_SBFG].Data.value);
	auto  XBFG = FCString::Atoi(*ParamsInfo[PARAM_XBFG].Data.value);	
	auto  ZBFG = FCString::Atoi(*ParamsInfo[PARAM_ZBFG].Data.value);
	auto  YBFG = FCString::Atoi(*ParamsInfo[PARAM_YBFG].Data.value);

	auto SCBJT = FCString::Atof(*ParamsInfo[PARAM_SCBJT_STR].Data.value) * 0.1;
	auto XCBJT = FCString::Atof(*ParamsInfo[PARAM_XCBJT_STR].Data.value) * 0.1;
	auto ZCBJT = FCString::Atof(*ParamsInfo[PARAM_ZCBJT_STR].Data.value) * 0.1;
	auto YCBJT = FCString::Atof(*ParamsInfo[PARAM_YCBJT_STR].Data.value) * 0.1;

	auto  MYGLX = FCString::Atoi(*ParamsInfo[PARAM_MYGLX_STR].Data.value);

	if (MYGLX == 0)
	{
		if (SBFG == 0)
		{
			UpExtent = SCBJT;
		}
		else if (SBFG == 1)
		{
			UpExtent = SCBJT * 0.5;
		}

		if (XBFG == 0)
		{
			DownExtent = XCBJT;
		}
		else if (XBFG == 1)
		{
			DownExtent = XCBJT * 0.5;
		}

		if (ZBFG == 0)
		{
			LeftExtent = ZCBJT;
		}
		else if (SBFG == 1)
		{
			LeftExtent = ZCBJT * 0.5;
		}

		if (YBFG == 0)
		{
			RightExtent = YCBJT;
		}
		else if (XBFG == 1)
		{
			RightExtent = YCBJT * 0.5;
		}
	}
	else if(MYGLX == 1)
	{
		//SBFG
		FString SBFGV = ModelInfo->GetParameterValue(PARAM_SBFG);
		if(FCString::Atoi(*SBFGV) != 3)
		{
			ModelInfo->SetParameter(PARAM_SBFG, FString("3"));
			NeedUpdate = true;
		}

		//XBFG
		FString XBFGV = ModelInfo->GetParameterValue(PARAM_XBFG);
		if(FCString::Atoi(*XBFGV) != 3)
		{
			ModelInfo->SetParameter(PARAM_XBFG, FString("3"));
			NeedUpdate = true;
		}

		//ZBFG
		FString ZBFGV = ModelInfo->GetParameterValue(PARAM_ZBFG);
		if(FCString::Atoi(*ZBFGV) != 3)
		{
			ModelInfo->SetParameter(PARAM_ZBFG, FString("3"));
			NeedUpdate = true;
		}

		//YBFG
		FString YBFGV = ModelInfo->GetParameterValue(PARAM_YBFG);
		if(FCString::Atoi(*YBFGV) != 3)
		{
			ModelInfo->SetParameter(PARAM_YBFG, FString("3"));
			NeedUpdate = true;
		}
	}

	return NeedUpdate;
}

void FDrawerAdaptationOperator::ParseAdaptiveSizeInfo(const TSharedPtr<FAdaptationData>& AdaptationData)
{
	if (UDSCupboardModel * SourceCupboardModel = Cast<UDSCupboardModel>(SourceModel))
	{
		//new size
		FVector Size = AdaptationData->OriBox.Extents * 20.0;
		UE_LOG(LogDrawerAdaptationOperator, Warning, TEXT("Drawer Adaptation Size[No Extent]: %s"), *Size.ToString());
		TSharedPtr<FMultiComponentDataItem>& CurrentTreeData = SourceCupboardModel->GetModelInfoRef().ComponentTreeData;
		
		//FG
		FTransform Trans;
		GetAdaptationTransform(AdaptationData, Trans);
		UE_LOG(LogDrawerAdaptationOperator, Warning, TEXT("Drawer Adaptation -- Size [%s], Transform [%s]"), *Size.ToString(), *Trans.ToString());
		ParseSpaceFGInfo(CurrentTreeData, Size,  Trans);
		UE_LOG(LogDrawerAdaptationOperator, Warning, TEXT("Drawer Adaptation[By FG Modify] -- Size [%s], Transform [%s]"), *Size.ToString(), *Trans.ToString());
		
		//size
		//W
		FParameterData* WP = CurrentTreeData->ComponentParameters.FindByPredicate(
			[](const FParameterData& Param)->bool
			{
				return Param.Data.name.Equals(PARAM_W_STR);
			});
		if(WP != nullptr)
		{
			WP->Data.value = FString::SanitizeFloat(Size.X);
			WP->Data.expression = FString::SanitizeFloat(Size.X);
			WP->FormatValue();
		}

		//H
		FParameterData* HP = CurrentTreeData->ComponentParameters.FindByPredicate(
			[](const FParameterData& Param)->bool
			{
				return Param.Data.name.Equals(PARAM_H_STR);
			});
		if(HP != nullptr)
		{
			HP->Data.value = FString::SanitizeFloat(Size.Z);
			HP->Data.expression = FString::SanitizeFloat(Size.Z);
			HP->FormatValue();
		}

		//D
		FParameterData* DP = CurrentTreeData->ComponentParameters.FindByPredicate(
			[](const FParameterData& Param)->bool
			{
				return Param.Data.name.Equals(PARAM_D_STR);
			});
		if(DP != nullptr)
		{
			DP->Data.value = FString::SanitizeFloat(Size.Y);
			DP->Data.expression = FString::SanitizeFloat(Size.Y);
			DP->FormatValue();
		}

		SourceCupboardModel->OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf, BroadcastMarkPtr);

		//transform
		//model
		SourceCupboardModel->GetPropertySharedPtr()->GetTransformPropertyRef().Location = Trans.GetLocation();
		SourceCupboardModel->GetPropertySharedPtr()->GetTransformPropertyRef().Rotation = Trans.GetRotation().Rotator();

		//model info
		SourceCupboardModel->GetModelInfoRef().ComponentTreeData->ComponentLocation.SetLocation(Trans.GetLocation());
		SourceCupboardModel->GetModelInfoRef().ComponentTreeData->ComponentRotation.SetRotation(Trans.GetRotation().Rotator());


		SourceCupboardModel->OnExecuteAction(FDSModelExecuteType::ExecuteTransform, BroadcastMarkPtr);
	}
}

void FDrawerAdaptationOperator::ParseAdaptiveTransformInfo(const TSharedPtr<FAdaptationData>& AdaptationData)
{
    if (UDSCupboardModel * SourceCupboardModel = Cast<UDSCupboardModel>(SourceModel))
    {
        //transform
        FTransform Trans;
        if (GetAdaptationTransform(AdaptationData, Trans))
        {
            SourceCupboardModel->GetPropertySharedPtr()->GetTransformPropertyRef().Location = Trans.GetLocation();
            SourceCupboardModel->GetPropertySharedPtr()->GetTransformPropertyRef().Rotation = Trans.GetRotation().Rotator();

			SourceCupboardModel->OnExecuteAction(FDSModelExecuteType::ExecuteTransform, BroadcastMarkPtr);
        }
    }
}

bool FDrawerAdaptationOperator::GetSpecialSizeAndOffset(const TMap<FString, FParameterData>& ParamsMap, FVector& ModifySize, FVector& ModifyOffset)
{
	bool bHasSpecialDWH = false;

	if (ParamsMap.Contains("TSW") && ParamsMap.Contains("TSD") && ParamsMap.Contains("TSH"))
	{
		bHasSpecialDWH = true;

		ModifySize.X = FCString::Atof(*ParamsMap["TSW"].Data.value) * 0.1f;
		ModifySize.Y = FCString::Atof(*ParamsMap["TSD"].Data.value) * 0.1f;
		ModifySize.Z = FCString::Atof(*ParamsMap["TSH"].Data.value) * 0.1f;

		ModifySize *= 0.5f;
	}

	if (ParamsMap.Contains("TSOH"))
	{
		ModifyOffset.Z = FCString::Atof(*ParamsMap["TSOH"].Data.value) * 0.1f;
	}
	if (ParamsMap.Contains("TSOD"))
	{
		ModifyOffset.Y = FCString::Atof(*ParamsMap["TSOD"].Data.value) * 0.1f;
	}
	if (ParamsMap.Contains("TSOW"))
	{
		ModifyOffset.X = FCString::Atof(*ParamsMap["TSOW"].Data.value) * 0.1f;
	}

	return bHasSpecialDWH;
}

bool FDrawerAdaptationOperator::GetFixedSizeAndOffset(const TMap<FString, FParameterData>& ParamsMap, FVector& ModifySize, FVector& ModifyOffset)
{
	bool bHasFixedDWH = false;

	if (ParamsMap.Contains("WIDTH") && ParamsMap.Contains("DEPTH") && ParamsMap.Contains("HEIGHT"))
	{
		bHasFixedDWH = true;

		ModifySize.X = FCString::Atof(*ParamsMap["WIDTH"].Data.value) * 0.1f;
		ModifySize.Y = FCString::Atof(*ParamsMap["DEPTH"].Data.value) * 0.1f;
		ModifySize.Z = FCString::Atof(*ParamsMap["HEIGHT"].Data.value) * 0.1f;

		ModifySize *= 0.5f;
	}
	if (ParamsMap.Contains("GDOH"))
	{
		ModifyOffset.Z = FCString::Atof(*ParamsMap["GDOH"].Data.value) * 0.1f;
	}
	if (ParamsMap.Contains("GDOD"))
	{
		ModifyOffset.Y = FCString::Atof(*ParamsMap["GDOD"].Data.value) * 0.1f;
	}
	if (ParamsMap.Contains("GDOW"))
	{
		ModifyOffset.X = FCString::Atof(*ParamsMap["GDOW"].Data.value) * 0.1f;
	}

	return bHasFixedDWH;
}

void FDrawerAdaptationOperator::GetDefaultSize(const TMap<FString, FParameterData>& ParamsMap, FVector& ModifySize)
{
	UDSCupboardLibrary::ConvertBaseDHWParametersToFVector(ModifySize, ParamsMap);
}

FString FDrawerAdaptationOperator::GetEnvInnerData(const TWeakPtr<FIntersectionDynamicMesh>& EnvironmentInfo, const FString& ParamName)
{
	if (EnvironmentInfo.IsValid() && EnvironmentInfo.Pin()->GetLinkModelBaseInfo().OwnerModelPtr.IsValid() && !ParamName.IsEmpty())
	{
		if (UDSCupboardModel* Model = Cast<UDSCupboardModel>(EnvironmentInfo.Pin()->GetLinkModelBaseInfo().OwnerModelPtr.Get()))
		{ 
			return Model->GetModelInfo().ComponentTreeData->GetParameterValue(ParamName);
		}
	}
	return TEXT("0");
}

void FDrawerAdaptationOperator::ParseSpaceFGInfo(TSharedPtr<FMultiComponentDataItem>& TreeInfo, FVector& ModifySize, FTransform& ModifyTrans)
{
	if (TreeInfo.IsValid())
	{
		bool bEnableZAxis = true;
		if (FParameterData* Param_H = TreeInfo->ComponentParameters.FindByPredicate([](const FParameterData& InParam) { return InParam.Data.name == TEXT("H"); }))
		{
			bEnableZAxis = Param_H->bEnableAdaptation;
		}

		bool bEmbedded = FCString::Atoi(*TreeInfo->GetParameterValue(TEXT("MYGLX"))) == 1;
		bool bOriginEmbedde = false;
		FParameterData* Param_MYGLX = TreeInfo->ComponentParameters.FindByPredicate(
			[](const FParameterData& InParam)
			{
				return InParam.Data.name == TEXT("MYGLX");
			}
		);
		if (Param_MYGLX != nullptr)
		{
			bOriginEmbedde = FCString::Atoi(*Param_MYGLX->Data.visibility) == 0;
		}

		TSharedPtr<FDrawerAdaptiveAdsorption> DrawerAdaptiveAdsorption = StaticCastSharedPtr<FDrawerAdaptiveAdsorption>(SourceAdaptationInfo);
		checkf(DrawerAdaptiveAdsorption.IsValid(), TEXT("DrawerAdaptiveAdsorption Type is not valid"));
 		FAdaptationEvenInfo SpaceRelationInfo = DrawerAdaptiveAdsorption->ExecuteEven(AdaptationEnvs);
		FAdaptationEvenInfo ApplyOffsetSpaceRelationInfo = DrawerAdaptiveAdsorption->ExecuteEvenReal(ApplyOffsetAdaptationEnvs);

		//up down
		double UpExtent = 0.0;
		int32 UpFirstFG = 0;
		bool bNeedApplyUpFG = false;
		EIntersectionDataType UpEnvType = GetSpaceFGInfoByDir(
			SpaceRelationInfo.GetAroundEnv(EAdaptationDirection::E_Up), PARAM_SCBJT_STR, PARAM_SBFG, EAdaptationDirection::E_Up, bEmbedded,
			DrawerAdaptiveAdsorption->GetZPosRelation(), UpExtent, UpFirstFG, bNeedApplyUpFG);

		double DownExtent = 0.0;
		int32 DownFirstFG = 0;
		bool bNeedApplyDownFG = false;
		EIntersectionDataType DownEnvType = GetSpaceFGInfoByDir(
			SpaceRelationInfo.GetAroundEnv(EAdaptationDirection::E_Down), PARAM_XCBJT_STR, PARAM_XBFG, EAdaptationDirection::E_Down, bEmbedded,
			DrawerAdaptiveAdsorption->GetZNegRelation(), DownExtent, DownFirstFG, bNeedApplyDownFG);

		if (!bOriginEmbedde)
		{
			if (bEmbedded)
			{//外盖改内嵌
				//if (bEnableZAxis)
				{
					if (UpEnvType == EIntersectionDataType::E_Drawer && DownEnvType == EIntersectionDataType::E_Drawer)
					{//依赖抽屉
						/*bool bNoOriginToEmbedded = DrawerAdaptiveAdsorption->IsDrawerNoOriginToEmbedded(EAdaptationDirection::E_Up);
						if (bNoOriginToEmbedded)
						{
							ModifySize.Z += UpExtent * 20.0;
						}
						else
						{
							ModifySize.Z += UpExtent * 20.0 * 2.0;
						}*/

						ModifySize.Z += UpExtent * 20.0;
                        ModifySize.Z += DownExtent * 20.0;

					}
					else if (UpEnvType != EIntersectionDataType::E_Drawer && DownEnvType == EIntersectionDataType::E_Drawer)
					{
                        ModifySize.Z += UpExtent * 20.0;
                        ModifySize.Z += DownExtent * 20.0;
					}
					else if (UpEnvType == EIntersectionDataType::E_Drawer && DownEnvType != EIntersectionDataType::E_Drawer)
					{
						ModifySize.Z += DownExtent * 20.0;
						ModifySize.Z += DownExtent * 20.0;
					}
					else
					{
						ModifySize.Z += UpExtent * 20.0;
						ModifySize.Z += DownExtent * 20.0;
					}
				}
			}
			else
			{//外盖
				if (UpEnvType != EIntersectionDataType::E_Drawer && bEnableZAxis)
				{
					ModifySize.Z += UpExtent * 20.0;
				}
				if (DownEnvType != EIntersectionDataType::E_Drawer && bEnableZAxis)
				{
					ModifySize.Z += DownExtent * 20.0;
				}
			}
		}
		else
		{//内嵌抽屉

		}

		TreeInfo->SetParameter(PARAM_SCBJT_STR, FString::SanitizeFloat(UpExtent * 20));
		if (bNeedApplyUpFG)
		{
			TreeInfo->SetParameter(PARAM_SBFG, FString::FromInt(UpFirstFG));
		}

		if (DownEnvType == EIntersectionDataType::E_Drawer && !bOriginEmbedde && !bEmbedded)
		{
			ModifyTrans.SetLocation(ModifyTrans.GetLocation() + ModifyTrans.GetRotation().GetUpVector() * DownExtent * 2.0);
		}
		TreeInfo->SetParameter(PARAM_XCBJT_STR, FString::SanitizeFloat(DownExtent * 20));
		if (bNeedApplyDownFG)
		{
			TreeInfo->SetParameter(PARAM_XBFG, FString::FromInt(DownFirstFG));
		}

		//left
		{
			double LeftExtent = 0.0;
			int32 LeftFirstFG = 0;
			bool bNeedApplyLeftFG = false;
			EIntersectionDataType LeftEnvType = GetSpaceFGInfoByDir(
				SpaceRelationInfo.GetAroundEnv(EAdaptationDirection::E_Backward), PARAM_ZCBJT_STR, PARAM_ZBFG, EAdaptationDirection::E_Backward, bEmbedded,
				DrawerAdaptiveAdsorption->GetXNegRelation(), LeftExtent, LeftFirstFG, bNeedApplyLeftFG);
			if (LeftEnvType != EIntersectionDataType::E_Drawer)
			{
				ModifySize.X += LeftExtent * 20.0;
			}
			TreeInfo->SetParameter(PARAM_ZCBJT_STR, FString::SanitizeFloat(LeftExtent * 20));
			if (bNeedApplyLeftFG)
			{
				TreeInfo->SetParameter(PARAM_ZBFG, FString::FromInt(LeftFirstFG));
			}
		}

		//right
		{
			double RExtent = 0.0;
			int32 RFirstFG = 0;
			bool bNeedApplyRightFG = false;
			EIntersectionDataType REnvType = GetSpaceFGInfoByDir(
				SpaceRelationInfo.GetAroundEnv(EAdaptationDirection::E_Forward), PARAM_YCBJT_STR, PARAM_YBFG, EAdaptationDirection::E_Forward, bEmbedded,
				DrawerAdaptiveAdsorption->GetXPosRelation(), RExtent, RFirstFG, bNeedApplyRightFG);
			if (REnvType != EIntersectionDataType::E_Drawer)
			{
				ModifySize.X += RExtent * 20.0;
			}
			TreeInfo->SetParameter(PARAM_YCBJT_STR, FString::SanitizeFloat(RExtent * 20));
			if (bNeedApplyRightFG)
			{
				TreeInfo->SetParameter(PARAM_YBFG, FString::FromInt(RFirstFG));
			}
		}

		 //临时写入，计算一层参数
		 FVector FixSize;
		 FVector FixOffset;
		 TMap<FString, FParameterData> TempParams = FGeometryDatas::ConvertParamsArrayToMap(TreeInfo->ComponentParameters);
		 if (TempParams.Contains(PARAM_W_STR))
		 {
		 	TempParams[PARAM_W_STR].Data.value = FString::SanitizeFloat(ModifySize.X);
		 	TempParams[PARAM_W_STR].Data.expression = FString::SanitizeFloat(ModifySize.X);
		 }
		 if (TempParams.Contains(PARAM_D_STR))
		 {
             TempParams[PARAM_D_STR].Data.value = FString::SanitizeFloat(ModifySize.Y);
             TempParams[PARAM_D_STR].Data.expression = FString::SanitizeFloat(ModifySize.Y);
		 }
		 if (TempParams.Contains(PARAM_H_STR))
		 {
		 	TempParams[PARAM_H_STR].Data.value = FString::SanitizeFloat(ModifySize.Z);
		 	TempParams[PARAM_H_STR].Data.expression = FString::SanitizeFloat(ModifySize.Z);
		 }
         
		 if (GetMiddleFixedSizeAndOffset(TempParams, FixSize, FixOffset))
		 {
		 	FixSize *= 2.0;
			//Y方向尺寸补偿
			FVector AllApaceSize = FixSize;
			double MBTValue = TempParams.Contains(TEXT("MBT")) ? FCString::Atod(*TempParams[TEXT("MBT")].Data.value) * 0.1 : 0.0;
			AllApaceSize.Y = ModifySize.Y * 0.1 + MBTValue;

		 	FTransform OBBTrans = ModifyTrans;
		 	FVector Loc = OBBTrans.GetLocation();
		 	Loc += (OBBTrans.GetRotation().GetAxisZ() * (-1.0) * FMath::Abs(FixOffset.Z) + OBBTrans.GetRotation().GetAxisX() * (-1.0) * FMath::Abs(FixOffset.X));
		 	OBBTrans.SetLocation(Loc);
		 	UE_LOG(LogTemp, Warning, TEXT("[%s], Fix [%s] [%s]"), *OBBTrans.ToString(), *FixSize.ToString(), *FixOffset.ToString());
		 	UE::Geometry::FOrientedBox3d CurBox = GetOBBByTransAndSize(OBBTrans, FixSize);
		 	UE::Geometry::FOrientedBox3d AllSpaceBox = GetOBBByTransAndSize(OBBTrans, AllApaceSize);
		 	AutoFGTypeAdjustment(TreeInfo, CurBox, AllSpaceBox);
		 }
		 else
		 {
		 	checkf(false, TEXT("No Fix Params"));
		 }
		
	}
}

EIntersectionDataType FDrawerAdaptationOperator::ParseSpaceFGInfoByDir(const TWeakPtr<FIntersectionDynamicMesh>& Env, const TWeakPtr<FIntersectionDynamicMesh>& RealEnv, const FString& DirectionMark, const EAdaptationDirection& Direction, double& Extent, FTransform& ModifyTrans, bool& IsNoSameEnv, bool IsEmbedded)
{
	if (Env.IsValid())
	{
		TWeakPtr<FIntersectionDynamicMesh> ConsiderEnv = Env;
		IsNoSameEnv = false;
		if (RealEnv.IsValid())
		{
			IsNoSameEnv = !(Env.Pin()->GetLinkModelBaseInfo().UUID.Equals(RealEnv.Pin()->GetLinkModelBaseInfo().UUID));
		}
		if (IsNoSameEnv)
		{
			ConsiderEnv = RealEnv;
		}

		//FG值
		if (ConsiderEnv.Pin()->GetIntersectionType() == EIntersectionDataType::E_Drawer)
		{
			if(Direction == EAdaptationDirection::E_Up || Direction == EAdaptationDirection::E_Down)
			{
				Extent = FCString::Atod(*GetEnvInnerData(ConsiderEnv, DirectionMark)) * 0.05;
			}
			else
			{
				Extent = 0.0;
			}
		}
		else
		{
			if (Direction == EAdaptationDirection::E_Up || Direction == EAdaptationDirection::E_Down)
			{
				Extent = ConsiderEnv.Pin()->GetOriBox().Extents.Z;
			}
			else if(Direction == EAdaptationDirection::E_Forward || Direction == EAdaptationDirection::E_Backward)
 			{
				Extent = ConsiderEnv.Pin()->GetOriBox().Extents.X;
			}
		}

		//位置偏移
		if (IsNoSameEnv && !IsEmbedded && 
			(Direction == EAdaptationDirection::E_Down || Direction == EAdaptationDirection::E_Backward))
		{
			FVector ShiftDir = Direction == EAdaptationDirection::E_Down ? -ModifyTrans.GetRotation().GetUpVector()  //-Z
				: Direction == EAdaptationDirection::E_Backward ? -ModifyTrans.GetRotation().GetForwardVector()		 //-X
				: FVector::ZeroVector;
            ModifyTrans.SetTranslation(ModifyTrans.GetTranslation() + ShiftDir * Extent * 2.0);
		}

		return ConsiderEnv.Pin()->GetIntersectionType();
	}
    return EIntersectionDataType::E_Unknown;
}

EIntersectionDataType FDrawerAdaptationOperator::ParseSpaceFGInfoByDir(const TWeakPtr<FIntersectionDynamicMesh>& Env, const FString& DirectionMark, const EAdaptationDirection& Direction, double& Extent, FTransform& ModifyTrans, bool IsEmbedded)
{
	if (Env.IsValid())
	{
		//FG值
		if (Env.Pin()->GetIntersectionType() == EIntersectionDataType::E_Drawer)
		{
			if (Direction == EAdaptationDirection::E_Up || Direction == EAdaptationDirection::E_Down)
			{
				Extent = FCString::Atod(*GetEnvInnerData(Env, DirectionMark)) * 0.05;
			}
			else
			{
				Extent = 0.0;
			}
		}
		else
		{
			if (Direction == EAdaptationDirection::E_Up || Direction == EAdaptationDirection::E_Down)
			{
				Extent = Env.Pin()->GetOriBox().Extents.Z;
			}
			else if (Direction == EAdaptationDirection::E_Forward || Direction == EAdaptationDirection::E_Backward)
			{
				Extent = Env.Pin()->GetOriBox().Extents.X;
			}
		}

		////位置偏移
		//if (!IsEmbedded &&
		//	(Direction == EAdaptationDirection::E_Down || Direction == EAdaptationDirection::E_Backward))
		//{
		//	FVector ShiftDir = Direction == EAdaptationDirection::E_Down ? -ModifyTrans.GetRotation().GetUpVector()  //-Z
		//		: Direction == EAdaptationDirection::E_Backward ? -ModifyTrans.GetRotation().GetForwardVector()		 //-X
		//		: FVector::ZeroVector;
		//	ModifyTrans.SetTranslation(ModifyTrans.GetTranslation() + ShiftDir * Extent * 2.0);
		//}

		return Env.Pin()->GetIntersectionType();
	}
	return EIntersectionDataType::E_Unknown;
}

EIntersectionDataType FDrawerAdaptationOperator::GetSpaceFGInfoByDir(const TWeakPtr<FIntersectionDynamicMesh>& Env, const FString& DirectionMark, const FString& DirectionFGMark, const EAdaptationDirection& Direction, bool IsEmbedded, const EDrawerAdaptiveRelation& CurEnvRelation, double& Extent, int32& FirstFGValue, bool& bApplyFirstFG)
{
	if (Env.IsValid())
	{
		bool bAlreadySet = false;
		//FG值
		if (Env.Pin()->GetIntersectionType() == EIntersectionDataType::E_Drawer)
		{
			if (Direction == EAdaptationDirection::E_Up || Direction == EAdaptationDirection::E_Down)
			{
				Extent = FCString::Atod(*GetEnvInnerData(Env, DirectionMark)) * 0.05;
			}
			else
			{
				UE_LOG(LogDrawerAdaptationOperator, Error, TEXT("GetSpaceFGInfoByDir --- Left Right Not Be Drawer, this brunch no enter"));
				Extent = 0.0;
			}
		}
		else
		{
			if (Direction == EAdaptationDirection::E_Up || Direction == EAdaptationDirection::E_Down)
			{
				if (Env.Pin()->GetIntersectionType() == EIntersectionDataType::E_Function_Combine)
				{
					Extent = 0.9;
					FirstFGValue = 2;
					bAlreadySet = true;
				}
				else
				{
					Extent = Env.Pin()->GetOriBox().Extents.Z;
				}
			}
			else if (Direction == EAdaptationDirection::E_Forward || Direction == EAdaptationDirection::E_Backward)
			{
				Extent = Env.Pin()->GetOriBox().Extents.X;
			}
		}

		if (!bAlreadySet)
		{
			if (CurEnvRelation == EDrawerAdaptiveRelation::DAR_Normal)
			{
				FirstFGValue = 0; //全盖
			}
			else
			{
				FirstFGValue = 2; //不盖
			}
		}

		int32 OldFGValue = FCString::Atoi(*GetEnvInnerData(Env, DirectionFGMark));
		bApplyFirstFG = (FirstFGValue > OldFGValue);

		return Env.Pin()->GetIntersectionType();
	}

	return EIntersectionDataType::E_Unknown;
}

UE::Geometry::FOrientedBox3d FDrawerAdaptationOperator::GetOBBByTransAndSize(const FTransform& InTrans, const FVector& InSize)
{
	FOrientedBox3d Result;
	FVector Extent = InSize * 0.5;
	Result.Frame.Rotation = FQuaterniond(InTrans.GetRotation());
	Result.Frame.Origin = InTrans.TransformPosition(Extent);
	Result.Extents = Extent;
	return Result;
}

void FDrawerAdaptationOperator::AutoFGTypeAdjustment(TSharedPtr<FMultiComponentDataItem>& TreeInfo, const UE::Geometry::FOrientedBox3d& DetectOBB, const UE::Geometry::FOrientedBox3d& DetectSpaceOBB)
{
	if(TreeInfo.IsValid() && !DoorDrawerAdaptationEnvs.IsEmpty())
	{//当前环境中有门抽，需要判断覆盖

		int32 SBFGValue = FCString::Atoi(*TreeInfo->GetParameterValue(PARAM_SBFG));
		double SCBJTValue = FCString::Atof(*TreeInfo->GetParameterValue(PARAM_SCBJT_STR)) * 0.1;
		int32 XBFGValue = FCString::Atoi(*TreeInfo->GetParameterValue(PARAM_XBFG));
		double XCBJTValue = FCString::Atof(*TreeInfo->GetParameterValue(PARAM_XCBJT_STR)) * 0.1;
		int32 ZBFGValue = FCString::Atoi(*TreeInfo->GetParameterValue(PARAM_ZBFG));
        double ZCBJTValue = FCString::Atof(*TreeInfo->GetParameterValue(PARAM_ZCBJT_STR)) * 0.1;
		int32 YBFGValue = FCString::Atoi(*TreeInfo->GetParameterValue(PARAM_YBFG));
        double YCBJTValue = FCString::Atof(*TreeInfo->GetParameterValue(PARAM_YCBJT_STR)) * 0.1;
		TMap<FString, int32> FGMap = {
			{PARAM_SBFG, SBFGValue},
            {PARAM_XBFG, XBFGValue},
            {PARAM_ZBFG, ZBFGValue},
            {PARAM_YBFG, YBFGValue}
		};
		// TMap<FString, int32> FGMap = {
		// 	{PARAM_SBFG, 0},
		// 	{PARAM_XBFG, 0},
		// 	{PARAM_ZBFG, 0},
		// 	{PARAM_YBFG, 0}
		// };
		//UE_LOG(LogTemp, Warning, TEXT("Old FG [%d][%d][%d][%d]"), SBFGValue, XBFGValue, ZBFGValue, YBFGValue);

		TMap<EAdaptationDirection, double> BoundaryJT = {
			{EAdaptationDirection::E_Up, SCBJTValue},
            {EAdaptationDirection::E_Down, XCBJTValue},
            {EAdaptationDirection::E_Forward, YCBJTValue},
            {EAdaptationDirection::E_Backward, ZCBJTValue}
		};

		for(const auto& DDAE : DoorDrawerAdaptationEnvs)
		{
			if(!DDAE.IsValid())
			{
				continue;
			}

			TMap<EAdaptationDirection, bool> DirectionOverlapMap;
			if(IsOBBOverlap(DetectOBB, DetectSpaceOBB, DDAE.Get()->GetOriBox(), BoundaryJT, DirectionOverlapMap))
			{
				for (const auto& DOM : DirectionOverlapMap)
				{
					if (!DOM.Value)
					{
						continue;
					}

					FString DetectFGName;
					FString OverlapFGFName;
					if (GetFGNameByDirection(DOM.Key, DetectFGName, OverlapFGFName))
					{
						if (UDSCupboardModel* OverlapModel = Cast<UDSCupboardModel>(DDAE->GetLinkModelBaseInfo().OwnerModelPtr.Get()))
						{
							int32 OverlapFGValue = FCString::Atoi(*OverlapModel->GetModelInfo().ComponentTreeData->GetParameterValue(OverlapFGFName));
							int32 OverlapFGOpposite = GetOppositeFG(OverlapFGValue);
							if (FGMap[DetectFGName] < OverlapFGOpposite)
							{
                                FGMap[DetectFGName] = OverlapFGOpposite;
							}
						}
					}
				}
			}

			for (const auto& FM : FGMap)
			{
				//UE_LOG(LogTemp, Warning, TEXT("Adjust New FG [%s] : [%d]"), *FM.Key, FM.Value);
			}
		}
		for (const auto& FM : FGMap)
		{
			//UE_LOG(LogTemp, Warning, TEXT("Final New FG [%s] : [%d]"), *FM.Key, FM.Value);
			TreeInfo->SetParameter(FM.Key, FString::SanitizeFloat(FM.Value));
		}
	}
}

bool FDrawerAdaptationOperator::GetAdaptationTransform(const TSharedPtr<FAdaptationData>& AdaptationData, FTransform& OutTrans)
{
	TSharedPtr<FFunctionalExecuterInitializedData> FunctionalInitializedData = StaticCastSharedPtr<FFunctionalExecuterInitializedData>(SourceAdaptationInfo->GetInitializedDataRef());
	if (AdaptationData->bTransformModified && FunctionalInitializedData.IsValid())
	{
		FVector LocalToWorldPoint = AdaptationData->OriBox.Frame.FromFrameVector(AdaptationData->OriBox.Extents);


		if (FunctionalInitializedData->bUsedRelativedOBB)
		{
			FVector LocalRealPoint = FunctionalInitializedData->SelfRealOriBox.Frame.FromFramePoint(-FunctionalInitializedData->SelfRealOriBox.Extents);

			LocalToWorldPoint = AdaptationData->OriBox.Frame.FromFramePoint(LocalRealPoint);
		}
		else
		{
			LocalToWorldPoint = AdaptationData->OriBox.Frame.FromFramePoint(-AdaptationData->OriBox.Extents);
		}

		OutTrans.SetLocation(LocalToWorldPoint);
        OutTrans.SetRotation(FQuat(AdaptationData->OriBox.Frame.Rotation * FunctionalInitializedData->SelfRealOriBox.Frame.Rotation));
		
		return true;
	}
	
	return false;
}

FString FDrawerAdaptationOperator::GetValueFromTree(const TSharedPtr<FMultiComponentDataItem>& TreeInfo, const FString& ParamName)
{
	if (TreeInfo.IsValid())
    {
        return TreeInfo->GetParameterValue(ParamName);
    }
	return FString(TEXT("0"));
}

bool FDrawerAdaptationOperator::GetFGNameByDirection(const EAdaptationDirection& InDirection, FString& OutDetectName, FString& OutOppositeName)
{
	if (InDirection == EAdaptationDirection::E_Up)
	{
        OutDetectName = PARAM_SBFG;
        OutOppositeName = PARAM_XBFG;
	}
	else if (InDirection == EAdaptationDirection::E_Down)
	{
		OutDetectName = PARAM_XBFG;
        OutOppositeName = PARAM_SBFG;
	}
	else if (InDirection == EAdaptationDirection::E_Backward)
	{
		OutDetectName = PARAM_ZBFG;
        OutOppositeName = PARAM_YBFG;
	}
	else if (InDirection == EAdaptationDirection::E_Forward)
	{
		OutDetectName = PARAM_YBFG;
        OutOppositeName = PARAM_ZBFG;
	}

	return !OutDetectName.IsEmpty() && !OutOppositeName.IsEmpty();
}

int32 FDrawerAdaptationOperator::GetOppositeFG(int32 CurFG)
{
	if (CurFG == 0)
	{//全改 -- 不盖
		return 2;
	}
	else if (CurFG == 1)
	{//半盖 -- 半盖
		return 1;
	}
	else
	{
        return 0;
	}
}

double FDrawerAdaptationOperator::CompensateSize(const TWeakPtr<FIntersectionDynamicMesh>& AdaptationEnv, const EAdaptationDirection& AdaptationDir)
{//由于原始抽屉Env有偏移，向右偏左边版厚，向上偏下边版厚,尺寸需弥补
	if (AdaptationEnv.IsValid())
	{
		if (UDSCupboardModel* Model = Cast<UDSCupboardModel>(AdaptationEnv.Pin()->GetLinkModelBaseInfo().OwnerModelPtr.Get()))
		{
			if (Model->GetModelType() == EDSModelType::E_Custom_FunctionalDrawer)
			{
				FString ParamName = AdaptationDir == EAdaptationDirection::E_Backward ? PARAM_ZCBJT_STR
					: AdaptationDir ==  EAdaptationDirection::E_Down ? PARAM_XCBJT_STR : TEXT("");
				if (!ParamName.IsEmpty())
				{
					return FCString::Atof(*Model->GetModelInfo().ComponentTreeData->GetParameterValue(ParamName));
				}
			}
		}
	}
	return 0.0;
}

FString FDrawerAdaptationOperator::InsertRevokeData(UDSBaseModel* InModel, FDSModelExecuteType InExecuteType, const FString& InUUID)
{
	FDSRevokePushData PushData(EDSPushDataType::E_Custom, InExecuteType, false);
	PushData.SetData(FDSCustomPushData(InExecuteType, InModel, nullptr));
	return UDSRevokeSubsystem::GetInstance()->InsertCommandDataUnion(
		InModel,
		InExecuteType,
		PushData,
		UDSMVCSubsystem::GetInstance()->GetRevokeMark(),
		InUUID
	);
}

double FDrawerAdaptationOperator::OBBProjectionRadiusToAxis(const UE::Geometry::FOrientedBox3d& InOBB, const FVector3d& InAxis)
{
	return FMath::Abs(FVector3d::DotProduct(InOBB.AxisX(), InAxis)) * InOBB.Extents.X  //x
	+ FMath::Abs(FVector3d::DotProduct(InOBB.AxisY(), InAxis)) * InOBB.Extents.Y		//y
	+ FMath::Abs(FVector3d::DotProduct(InOBB.AxisZ(), InAxis)) * InOBB.Extents.Z;		//z
}

bool FDrawerAdaptationOperator::IsOBBOverlap(const UE::Geometry::FOrientedBox3d& InOBB1, const UE::Geometry::FOrientedBox3d& InSpaceOBB1, const UE::Geometry::FOrientedBox3d& InOBB2,
	const TMap<EAdaptationDirection, double>& DirectionBoundaryMap, TMap<EAdaptationDirection, bool>& DirectionOverlapMap)
{
	FBox DetectBox(
		InOBB1.Center() - InOBB1.Extents,
		InOBB1.Center() + InOBB1.Extents);
	FBox DetectFullBox(
		InSpaceOBB1.Center() - InSpaceOBB1.Extents,
		InSpaceOBB1.Center() + InSpaceOBB1.Extents);
	FBox ConsiderBox(
		InOBB2.Center() - InOBB2.Extents,
		InOBB2.Center() + InOBB2.Extents);

	//DrawDebugBox(GWorld, ConsiderBox.GetCenter(), ConsiderBox.GetExtent(), FColor::Red, true);
	//DrawDebugBox(GWorld, DetectFullBox.GetCenter(), DetectFullBox.GetExtent(), FColor::Cyan, true);
	//DrawDebugBox(GWorld, DetectBox.GetCenter(), DetectBox.GetExtent(), FColor::Blue, true);

	bool bBoxOverlap = DetectBox.Intersect(ConsiderBox);
	bool bFullBoxOverlap = DetectFullBox.Intersect(ConsiderBox);
	UE_LOG(LogTemp, Warning, TEXT("OBBOverlap:%d %d"), bBoxOverlap, bFullBoxOverlap);

	if (bBoxOverlap || bFullBoxOverlap)
	{
		DirectionOverlapMap = 
		{
			{EAdaptationDirection::E_Backward, false},
            {EAdaptationDirection::E_Forward, false},
            {EAdaptationDirection::E_Down, false},
            {EAdaptationDirection::E_Up, false}
		};

		UE_LOG(LogTemp, Warning, TEXT("Consider Boundary Intersect"));
		if (!IsOBBBoundaryIntersect(InOBB1, InSpaceOBB1, !bBoxOverlap && bFullBoxOverlap, ConsiderBox, DirectionBoundaryMap, DirectionOverlapMap))
		{
			UE_LOG(LogTemp, Warning, TEXT("Boundary No Intersect, Consider Corner Intersect"));

			if (!IsOBBCornerIntersect(InOBB1, InSpaceOBB1, !bBoxOverlap && bFullBoxOverlap, ConsiderBox, DirectionBoundaryMap, DirectionOverlapMap))
			{
				UE_LOG(LogTemp, Warning, TEXT("Boundary No Intersect, Consider No Intersect ???????????"));
			}
		}
	}

	return bBoxOverlap || bFullBoxOverlap;
}

bool FDrawerAdaptationOperator::IsOBBDirectOverlap(const FVector3d& CenterLine, const FVector3d& DetectDir, const double& DetectExtent, const UE::Geometry::FOrientedBox3d& ConsiderOBB)
{
	double ProjectDir = FVector3d::DotProduct(CenterLine, DetectDir);
	if(ProjectDir < 0.0 || FMath::IsNearlyZero(ProjectDir, 0.01))
	{// 在OBB的对应轴方向上，检测线段方向为负或者零
		return false;
	}
	
	double ProjectCenterLine = FVector3d::DotProduct(CenterLine, DetectDir);
	double ProjectConsiderOBB = OBBProjectionRadiusToAxis(ConsiderOBB, DetectDir);
	
	return FMath::Abs(ProjectCenterLine) < (FMath::Abs(ProjectConsiderOBB) + FMath::Abs(DetectExtent));
}

bool FDrawerAdaptationOperator::IsOBBBoundaryIntersect(
	const UE::Geometry::FOrientedBox3d& DetectOBB,
	const UE::Geometry::FOrientedBox3d& DetectFullOBB,
	bool YUseFullOBB,
	const FBox& ConsiderBox, 
	const TMap<EAdaptationDirection, double>& DirectionBoundaryMap, 
	TMap<EAdaptationDirection, bool>& DirectionOverlapMap)
{
	bool Res = false;

	FBox XPos(ForceInit);
	bool bPosXOverlap = false;
	if (GetBoundaryBox(YUseFullOBB ? DetectFullOBB : DetectOBB, EAdaptationDirection::E_Forward, DirectionBoundaryMap, XPos))
	{
		bPosXOverlap = ConsiderBox.Intersect(XPos);
		if (!DirectionOverlapMap[EAdaptationDirection::E_Forward])
		{
			DirectionOverlapMap[EAdaptationDirection::E_Forward] = bPosXOverlap;
		}
		Res = Res || bPosXOverlap;
	}

	FBox XNeg(ForceInit);
	bool bNegXOverlap = false;
	if (GetBoundaryBox(YUseFullOBB ? DetectFullOBB : DetectOBB, EAdaptationDirection::E_Backward, DirectionBoundaryMap, XNeg))
	{
		bNegXOverlap = ConsiderBox.Intersect(XNeg);
		if (!DirectionOverlapMap[EAdaptationDirection::E_Backward])
		{
			DirectionOverlapMap[EAdaptationDirection::E_Backward] = bNegXOverlap;
		}
		Res = Res || bNegXOverlap;
	}

	FBox ZPos(ForceInit);
	if (GetBoundaryBox(YUseFullOBB ? DetectFullOBB : DetectOBB, EAdaptationDirection::E_Up, DirectionBoundaryMap, ZPos))
	{
		bool bPosZOverlap = ConsiderBox.Intersect(ZPos);
		if (bNegXOverlap || bPosXOverlap)
		{
			bPosZOverlap = false;
		}
		if (!DirectionOverlapMap[EAdaptationDirection::E_Up])
		{
			DirectionOverlapMap[EAdaptationDirection::E_Up] = bPosZOverlap;
		}
		Res = Res || bPosZOverlap;
	}

	FBox ZNeg(ForceInit);
	if (GetBoundaryBox(YUseFullOBB ? DetectFullOBB : DetectOBB, EAdaptationDirection::E_Down, DirectionBoundaryMap, ZNeg))
	{
		bool bNegZOverlap = ConsiderBox.Intersect(ZNeg);
		if (bNegXOverlap || bPosXOverlap)
		{
			bNegZOverlap = false;
		}
		if (!DirectionOverlapMap[EAdaptationDirection::E_Down])
		{
			DirectionOverlapMap[EAdaptationDirection::E_Down] = bNegZOverlap;
		}
		Res = Res || bNegZOverlap;
	}

	return Res;
}

bool FDrawerAdaptationOperator::IsOBBCornerIntersect(
	const UE::Geometry::FOrientedBox3d& DetectOBB,
	const UE::Geometry::FOrientedBox3d& DetectFullOBB,
	bool YUseFullOBB,
	const FBox& ConsiderBox, 
	const TMap<EAdaptationDirection, double>& DirectionBoundaryMap, 
	TMap<EAdaptationDirection, bool>& DirectionOverlapMap)
{
	bool Res = false;

	FBox XNegZNeg(ForceInit);
	if (GetCornerBox(YUseFullOBB ? DetectFullOBB : DetectOBB, 0, DirectionBoundaryMap, XNegZNeg))
	{
		bool bOverlap = ConsiderBox.Intersect(XNegZNeg);
		if (!DirectionOverlapMap[EAdaptationDirection::E_Backward])
		{
			DirectionOverlapMap[EAdaptationDirection::E_Backward] = bOverlap;
		}
		if (!DirectionOverlapMap[EAdaptationDirection::E_Down])
		{
			DirectionOverlapMap[EAdaptationDirection::E_Down] = bOverlap;
		}
		Res = Res || bOverlap;
	}

	FBox XNegZPos(ForceInit);
	if (GetCornerBox(YUseFullOBB ? DetectFullOBB : DetectOBB, 1, DirectionBoundaryMap, XNegZPos))
	{
		bool bOverlap = ConsiderBox.Intersect(XNegZPos);
		if (!DirectionOverlapMap[EAdaptationDirection::E_Backward])
		{
			DirectionOverlapMap[EAdaptationDirection::E_Backward] = bOverlap;
		}
		if (!DirectionOverlapMap[EAdaptationDirection::E_Up])
		{
			DirectionOverlapMap[EAdaptationDirection::E_Up] = bOverlap;
		}
		Res = Res || bOverlap;
	}

	FBox XPosZPos(ForceInit);
	if (GetCornerBox(YUseFullOBB ? DetectFullOBB : DetectOBB, 2, DirectionBoundaryMap, XPosZPos))
	{
		bool bOverlap = ConsiderBox.Intersect(XPosZPos);
		if (!DirectionOverlapMap[EAdaptationDirection::E_Up])
		{
			DirectionOverlapMap[EAdaptationDirection::E_Up] = bOverlap;
		}
		if (!DirectionOverlapMap[EAdaptationDirection::E_Forward])
		{
			DirectionOverlapMap[EAdaptationDirection::E_Forward] = bOverlap;
		}
		Res = Res || bOverlap;
	}

	FBox XPosZNeg(ForceInit);
	if (GetCornerBox(YUseFullOBB ? DetectFullOBB : DetectOBB, 3, DirectionBoundaryMap, XPosZNeg))
	{
		bool bOverlap = ConsiderBox.Intersect(XPosZNeg);
		if (!DirectionOverlapMap[EAdaptationDirection::E_Down])
		{
			DirectionOverlapMap[EAdaptationDirection::E_Down] = bOverlap;
		}
		if (!DirectionOverlapMap[EAdaptationDirection::E_Forward])
		{
			DirectionOverlapMap[EAdaptationDirection::E_Forward] = bOverlap;
		}
		Res = Res || bOverlap;
	}

	return Res;
}

bool FDrawerAdaptationOperator::GetBoundaryBox(const UE::Geometry::FOrientedBox3d& DetectOBB, const EAdaptationDirection& Direction, const TMap<EAdaptationDirection, double>& BoundaryBoxSize, FBox& OutBox)
{
	bool bResult = false;
    FVector BoxCenter = DetectOBB.Center();
	FVector MinPoint = BoxCenter - DetectOBB.Extents;
    FVector MaxPoint = BoxCenter + DetectOBB.Extents;
	if (Direction == EAdaptationDirection::E_Forward)
	{//X
		MinPoint += DetectOBB.AxisX() * (DetectOBB.Extents * 2.0 - BoundaryBoxSize[EAdaptationDirection::E_Forward]);
		//去除Z方向影响
        MaxPoint -= DetectOBB.AxisZ() * BoundaryBoxSize[EAdaptationDirection::E_Up];
		MinPoint += DetectOBB.AxisZ() * BoundaryBoxSize[EAdaptationDirection::E_Down];

		bResult = true;
	}
	else if (Direction == EAdaptationDirection::E_Backward)
	{//-X
		MaxPoint -= DetectOBB.AxisX() * (DetectOBB.Extents * 2.0 - BoundaryBoxSize[EAdaptationDirection::E_Backward]);

		MaxPoint -= DetectOBB.AxisZ() * BoundaryBoxSize[EAdaptationDirection::E_Up];
		MinPoint += DetectOBB.AxisZ() * BoundaryBoxSize[EAdaptationDirection::E_Down];

		bResult = true;
	}
	else if (Direction == EAdaptationDirection::E_Up)
	{//Z
		MinPoint += DetectOBB.AxisZ() * (DetectOBB.Extents * 2.0 - BoundaryBoxSize[EAdaptationDirection::E_Up]);

		//去除X方向影响
        MaxPoint -= DetectOBB.AxisX() * BoundaryBoxSize[EAdaptationDirection::E_Forward];
        MinPoint += DetectOBB.AxisX() * BoundaryBoxSize[EAdaptationDirection::E_Backward];

		bResult = true;
	}
	else if (Direction == EAdaptationDirection::E_Down)
	{//-Z
		MaxPoint -= DetectOBB.AxisZ() * (DetectOBB.Extents * 2.0 - BoundaryBoxSize[EAdaptationDirection::E_Down]);

		MaxPoint -= DetectOBB.AxisX() * BoundaryBoxSize[EAdaptationDirection::E_Forward];
		MinPoint += DetectOBB.AxisX() * BoundaryBoxSize[EAdaptationDirection::E_Backward];

		bResult = true;
	}
    OutBox = FBox(MinPoint, MaxPoint);
	//DrawDebugBox(GWorld, OutBox.GetCenter(), OutBox.GetExtent(), FColor::Green, true);
	return bResult;
}

bool FDrawerAdaptationOperator::GetCornerBox(const UE::Geometry::FOrientedBox3d& DetectOBB, const int32& Direction, const TMap<EAdaptationDirection, double>& BoundaryBoxSize, FBox& OutBox)
{
	bool bResult = false;
	FVector BoxCenter = DetectOBB.Center();
	FVector MinPoint = BoxCenter - DetectOBB.Extents;
	FVector MaxPoint = BoxCenter + DetectOBB.Extents;
	if (Direction == 0)
	{
		//-X
		MaxPoint -= DetectOBB.AxisX() * (DetectOBB.Extents * 2.0 - BoundaryBoxSize[EAdaptationDirection::E_Backward]);
		//-Z
		MaxPoint -= DetectOBB.AxisZ() * (DetectOBB.Extents * 2.0 - BoundaryBoxSize[EAdaptationDirection::E_Down]);

		bResult = true;
	}
	else if (Direction == 1)
	{
		//-X
		MaxPoint -= DetectOBB.AxisX() * (DetectOBB.Extents * 2.0 - BoundaryBoxSize[EAdaptationDirection::E_Backward]);
		//+Z
		MinPoint += DetectOBB.AxisZ() * (DetectOBB.Extents * 2.0 - BoundaryBoxSize[EAdaptationDirection::E_Up]);

		bResult = true;
	}
	else if (Direction == 2)
	{
		//+X
		MinPoint += DetectOBB.AxisX() * (DetectOBB.Extents * 2.0 - BoundaryBoxSize[EAdaptationDirection::E_Forward]);
		//+Z
		MinPoint += DetectOBB.AxisZ() * (DetectOBB.Extents * 2.0 - BoundaryBoxSize[EAdaptationDirection::E_Up]);

		bResult = true;
	}
	else if (Direction == 3)
	{
		//+X
		MinPoint += DetectOBB.AxisX() * (DetectOBB.Extents * 2.0 - BoundaryBoxSize[EAdaptationDirection::E_Forward]);
		//+Z
		MaxPoint -= DetectOBB.AxisZ() * (DetectOBB.Extents * 2.0 - BoundaryBoxSize[EAdaptationDirection::E_Down]);

		bResult = true;
	}
	OutBox = FBox(MinPoint, MaxPoint);
	//DrawDebugBox(GWorld, OutBox.GetCenter(), OutBox.GetExtent(), FColor::Yellow, true);
	return bResult;
}

bool FDrawerAdaptationOperator::IsOBBDirectIntersect(const FVector& SPoint, const FVector& EPoint, const FVector& Direction, const FBox& ConsiderBox)
{
	//DrawDebugLine(GWorld, SPoint, EPoint, FColor::Green, true);
	return FMath::LineBoxIntersection(ConsiderBox, SPoint, EPoint, Direction);
}

bool FDrawerAdaptationOperator::GetMiddleFixedSizeAndOffset(TMap<FString, FParameterData> LevelParamsMap, FVector& ModifySize, FVector& ModifyOffset)
{
	if (UDSCupboardModel* CupboardModel = Cast<UDSCupboardModel>(SourceModel))
	{
		TMap<FString, FParameterData> OverrideParamsMap = CupboardModel->GetParentComponentParams();
		//重置覆盖，获取真实的原始尺寸
		if (LevelParamsMap.Contains(PARAM_ZBFG))
		{
			LevelParamsMap[PARAM_ZBFG].ChangeParameterValue(TEXT("0"));
		}
        if (LevelParamsMap.Contains(PARAM_YBFG))
        {
			LevelParamsMap[PARAM_YBFG].ChangeParameterValue(TEXT("0"));
        }
		if (LevelParamsMap.Contains(PARAM_XBFG))
		{
			LevelParamsMap[PARAM_XBFG].ChangeParameterValue(TEXT("0"));
		}
		if (LevelParamsMap.Contains(PARAM_SBFG))
		{
			LevelParamsMap[PARAM_SBFG].ChangeParameterValue(TEXT("0"));
		}

		FGeometryDatas::CalculateParameterValue_LevelSort(UDSMVCSubsystem::GetInstance()->GetGlobalParamsMap(), OverrideParamsMap, LevelParamsMap);

		return GetFixedSizeAndOffset(LevelParamsMap, ModifySize, ModifyOffset);
	}
	return false;
}
