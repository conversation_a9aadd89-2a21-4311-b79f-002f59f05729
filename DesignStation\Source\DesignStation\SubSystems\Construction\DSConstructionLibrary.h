// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "DSConstructionCore.h"
#include "LocalCache/Component/MultiComponentDataDefine.h"
#include "DSConstructionLibrary.generated.h"

class UDSBaseModel;
class UDSCupboardModel;
class UVolatileMeshComponent;
class UDSHouseAreaModel;
/**
 * 
 */
UCLASS()
class DESIGNSTATION_API UDSConstructionLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()
	
public:
	DECLARE_DELEGATE_RetVal_OneParam(bool, FOnCheckIsRightNode, TSharedPtr<FMultiComponentDataItem>);

public:
	static void GetBoxOutlines(const FBox& InBox, TArray<TArray<FVector>>& OutOutlines);

	//计算网格在投影平面上的轮廓
	static bool CalcConstructionPrimitive(TArray<UVolatileMeshComponent*> InMeshComponents, const FTransform& InWorldToProjective, FDSPrimitive_MPolygon3D& OutPrimitive);

	//计算模型在投影平面上的轮廓
	//static void GetConstructionPrimitive_TheModel(UDSBaseModel* InModel, TArray<TSharedPtr<FDSPrimitiveBase>>& OutPrimitive,const FTransform &InWorldToProjective);

	static void GetConstructionPrimitive_TheModel(UDSBaseModel* InModel, TArray<TSharedPtr<FDSPrimitiveBase>>& OutPrimitive);

	//static void GetConstructionPrimitive_NoModelNode(UDSCupboardModel* InModel, const FTransform& InWorldToProjective, TMap<TSharedPtr<FMultiComponentDataItem>, TArray<FDSPrimitive_MPolygon3D>>& OutNodeData);

	static void GetConstructionPrimitive_NoModelNode(UDSCupboardModel* InModel,
		TMap<TSharedPtr<FMultiComponentDataItem>, TArray<FDSPrimitive_MPolygon3D>>& OutNodeData,
		FOnCheckIsRightNode InCheck);


	static void GetNoModelNodeInModel(UDSCupboardModel* InModel, TArray<TSharedPtr<FMultiComponentDataItem>>& OutNodes,
		TArray<UVolatileMeshComponent*>& OutMeshComponents);

	static FTransform GetComponentLocalTransfrom(UVolatileMeshComponent* InCom);

	static void Polygon2DBoolean_Union(const TArray<FVector2D>& InA, const TArray<FVector2D>& InB, TArray<TArray<FVector2D>>& OutResult);

	static void Polygon2DBoolean_Union(const TArray<FDSPrimitive_MPolygon>& InPrimitives, FDSPrimitive_MPolygon &OutPrimitive);

	static bool IsBoardType(int32 ModelType);

	static FString GetLayerName(TSharedPtr<FMultiComponentDataItem> InNode, EDSModelType InModelType);

	static FString GetBlockName(UDSBaseModel* InModel, TSharedPtr<FMultiComponentDataItem> InNode, UDSHouseAreaModel* InAreaModel);

	static void CalcCupboard3DOutline(UDSCupboardModel* InModel, TArray<TArray<FVector>>& OutOutlines/*,TArray<FVector>& Normals*/);
	static FBox GetCupboardNodeBox(TSharedPtr<FMultiComponentDataItem> InNode);
	static void CalcCupboard3DOutline(TSharedPtr<FMultiComponentDataItem> InNode, TArray<TArray<FVector>>& OutOutlines/*,TArray<FVector>& Normals*/);

	//计算柜子包围盒在投影平面上的轮廓
	//static bool CalcCupboard2DOutline(UDSCupboardModel* InModel, TArray<TArray<FVector>>& OutOutline);
	static bool CalcCupboard2DOutline_Local(UDSCupboardModel* InModel, TArray<FDSPrimitive_MPolygon3D>& OutMPolygon3D);
	static bool CalcCupboard2DOutline(UDSCupboardModel* InModel, const FTransform& InWorldToProjective, TArray<TArray<FVector>>& OutOutline);
	static bool CalcCupboard2DOutline(UDSCupboardModel* InModel, const FTransform& InWorldToProjective, TArray<FDSPrimitive_MPolygon3D>& OutMPolygon3D);
	
	static bool CalcCupboard2DOutline(TSharedPtr<FMultiComponentDataItem> InNode, const FTransform& InWorld, TArray<FDSPrimitive_MPolygon3D>& OutMPolygon3D);

	//返回柜子子部件的六个投影面
	static void GetCupboardNode6MeshOutline(UDSCupboardModel* InModel, const FTransform& InWorldToProjective, TArray<FDSPrimitive_MPolygon3D>& OutMPolygon3D);
	static void GetCupboardNode6MeshOutline_Local(UDSCupboardModel* InModel, TArray<FDSPrimitive_MPolygon3D>& OutMPolygon3D);

	/*
	static void GetCupboardNode6MeshOutline(TArray<UVolatileMeshComponent*> InMeshComponents, 
		TArray<FTransform> InMeshToNodeTs, const FTransform& InWorldToProjective, TArray<FDSPrimitive_MPolygon3D>& OutMPolygon3D);
	static void GetCupboardNode6MeshOutline(TArray<UVolatileMeshComponent*> InMeshComponents,
		TArray<FTransform> InMeshToNodeTs, TArray<FDSPrimitive_MPolygon3D>& OutMPolygon3D);
    */

	static FBox CalcRoomBox(UDSHouseAreaModel* InAreaModel);

    //得到门板开门方向多线段
	static bool GetDoorOpenDir2DOutline(UDSCupboardModel* InModel, const FTransform& InWorldToProjective, TArray<FVector>& OutOutline);

	static void CalcDoorOpenDir2DOutline(const TArray<FVector>& InDoorOutline, TArray<FVector>& OutOutline, int32 InKX);

	static void GetConstructionDatasByModel(UDSCupboardModel* InModel, const TArray<TSharedPtr<FDSConstructionData>>& InDatas,
		TArray<TSharedPtr<FDSConstructionData>>& OutDatas);

	static bool IsHasDoorNode(TSharedPtr<FMultiComponentDataItem> InNode);

	//对门板进行了处理
	static FTransform GetCupboardTransform(UDSCupboardModel* InModel);

	//得到非镜像的Transfrom
	static FTransform GetCupboardTransformNoMirror(UDSCupboardModel* InModel);

	//UE4坐标转换到CAD坐标
	static FVector UE4ToCAD(const FVector &InVector);

	//CAD坐标转换到UE4坐标
	static FVector CADToUE4(const FVector& InVector);

	//判断子节点是不是全是ModelType为-1
	static bool ChildNodeIsNoNoModelType(TSharedPtr<FMultiComponentDataItem> InNode);
};
