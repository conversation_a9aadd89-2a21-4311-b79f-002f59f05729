﻿// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "WorldSubsystem.h"
#include "StateMachine/DSFiniteStateMachine.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "Model/Custom/DSCupboardModel.h"
#include "LocalCache/Parameter/ParameterDataDefine.h"
#include "SubSystems/Undo/Core/DSRevokePool.h"
#include "SubSystems/Undo/Data/DSRevokePushData.h"
#include "DSMVCSubsystem.generated.h"

class UDSHouseAreaModel;
class UResourceData;
struct FDSCounterTopPushData;
DECLARE_LOG_CATEGORY_EXTERN(DSMVCSubsystemLog, Log, All);

DECLARE_MULTICAST_DELEGATE_ThreeParams(FOnModelExecuteActionComplete, UDSBaseModel*, int32,const TSharedPtr<FDSBroadcastMarkData>&);

DECLARE_MULTICAST_DELEGATE_ThreeParams(FOnModelExecuteCommandComplete, UDSBaseModel*, const FDSModelExecuteType&, const TSharedPtr<FDSBroadcastMarkData>&);
DECLARE_MULTICAST_DELEGATE_OneParam(FOnSingleHoseAreaShowing, bool);
/**
 *
 */
UCLASS()
class DESIGNSTATION_API UDSMVCSubsystem : public UWorldSubsystem
{
	GENERATED_BODY()
public:
	bool bSelectionDebugEnable;
	
public:
	UDSMVCSubsystem();

	virtual void Initialize(FSubsystemCollectionBase& Collection) override;
	virtual void Deinitialize() override;

	virtual void OnWorldBeginPlay(UWorld& InWorld) override;

#pragma region REFRESH_MODULE

	UFUNCTION()
	void OnLayoutRefresh(UDSBaseModel* InModel);

	void OnLayoutRefresh(const EDSModelType& InType);
	
	/*
	 *  @@ item edit refresh for in minimap show
	 */
	UFUNCTION()
	void OnExecuteRefreshShowForAreaSelect(UDSBaseModel* InModel, const FDSModelExecuteType& ExecuteType);

	/*
	 *  @@ Refresh Drawing show [Drawing subsystem]
	 */

	void OnExecuteRefreshDrawing(UDSBaseModel* InModel, const FDSModelExecuteType& ExecuteType);


	UFUNCTION()
	void OnClearSnapLine();

	/*
	 *  @@ execute revoke action
	 */
	UFUNCTION()
	void OnRevokeExecute(bool IsUndo);

	/*
	 *  @@ Union logic for toolbar and property execute
	 */
	void OnExecuteFromFunctionBarOrPropertyWidgetUnion(
		UDSBaseModel* EditModel,
		const FDSModelExecuteType& InExecuteType,
		const FDSRevokePushData& InPushData,
		const FString& InCommandUUID,
		bool NeedExecuteAction = true);

	/*
	 *  @@ 快照数据压入完成后，执行的操作
	 */
	 void ExecuteAfterSnapShot(const FDSRevokePushData& InPushData);
	 
	 /**
	  *  @@ 其它数据压入完成后，执行的操作
	  */
	 void ExecuteAfterInsertData(UDSBaseModel* EditModel, const FDSModelExecuteType& InExecuteType, const FDSRevokePushData& InPushData);
	
	/*
	 *  @@ Temp logic to find this action is only left mouse click
	 *  @@ Associate with RevokeSubsystem with [EnterAction]
	 */
	void RecheckIsOnlyClick(UDSBaseModel* EditModel);

	/*
	*  @@
	*/
	void OnRefreshUIPropertyWidget(UDSBaseModel* EditModel);

#pragma endregion

	static bool IsInitialized();
	static UDSMVCSubsystem* GetInstance();

	virtual TSharedPtr<FUICommandList> GetGenericCommandList() const;
	virtual bool ProcessCommandEvent(const FKeyEvent& InKeyEvent);

protected:
	TSharedPtr<FUICommandList> GenericCommandList;
	
	virtual void RegisterInGameCommands();
	virtual void UnregisterInGameCommands();

	virtual void BindGenericInputCommands();

	virtual bool CanExecuteGenericInputCommand_Undo();
	virtual bool CanExecuteGenericInputCommand_Redo();
	virtual bool CanExecuteGenericInputCommand_HideModel();
	virtual bool CanExecuteGenericInputCommand_CopyModel();
	virtual bool CanExecuteGenericInputCommand_CreateGroup();
	virtual bool CanExecuteGenericInputCommand_BreakGroup();
	virtual bool CanExecuteGenericInputCommand_SaveCameraView();
	virtual bool CanExecuteGenericInputCommand_ResetCamera();

	virtual void OnExecuteGenericInputCommand_Undo();
	virtual void OnExecuteGenericInputCommand_Redo();
	virtual void OnExecuteGenericInputCommand_HideModel();
	virtual void OnExecuteGenericInputCommand_CopyModel();
	virtual void OnExecuteGenericInputCommand_CreateGroup();
	virtual void OnExecuteGenericInputCommand_BreakGroup();
	virtual void OnExecuteGenericInputCommand_SaveCameraView();
	virtual void OnExecuteGenericInputCommand_ResetCamera();

#pragma region CatalogInfo

public:
	void SetGlobalStyle(const FApplyStyleData& InNewStyle);

	const TMap<FString, FParameterData>& GetGlobalParamsMap();
	TArray<FParameterData> GetGlobalParamsArr();

	TMap<FString, FParameterData> GetGlobalStyleParamsMap() const;
	TArray<FParameterData> GetGlobalStyleParamsArr() const;

	const FRefToStyleFile& GetOriginalStyleData();
	const FApplyStyleData& GetGlobalStyleData();

	bool HasGlobalStyle() const;

	FGlobalDisPlayParameter& GetGlobalDisPlayParams()
	{
		return GlobalDisPlayParams;
	};

private:
	UPROPERTY()
	TMap<FString, FParameterData> GlobalParams;

	UPROPERTY()
	FApplyStyleData GlobalStyleData;

	UPROPERTY()
	FRefToStyleFile OriginalStyleData;

	UPROPERTY()
	FGlobalDisPlayParameter GlobalDisPlayParams;

#pragma endregion

public:
#pragma region Multi_Group

	void SwitchMultiGroup(UDSBaseModel* EditModel);
	UDSGroupModel* ConvertMultiToGroup(UDSBaseModel* EditModel);
	void ConvertGroupToMulti(UDSBaseModel* EditModel);

#pragma endregion

public:
	ADSBaseView* SpawnViewUnion(UDSBaseModel* ToGenerateModel, const TSharedPtr<FDSBroadcastMarkData>& BroadcastMarkPtr = FDSBroadcastMarkData::SpawnBroadcastMark, bool bShouldSelect = false);
	
	void OnGenerateAction(UDSBaseModel* GenerateModel, bool bShouldSelect = true);
	UDSBaseModel* OnCopy(UDSBaseModel* Model, const FDSMouseData& MouseDataWhenGenerate);

	/*
	*  @@ self Init Action
	*  @@ normal for generate action
	*/
	void InitAction_Trigger(UDSBaseModel* InModel, const FPointerEvent& InMouseEvent);

	TArray<UDSBaseModel*> GetAllModels();

	void SerializeToJsonFromAllModels(TSharedRef<TJsonWriter<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>> JsonWriter);
	bool DeserializationJsonToAllModels(const TSharedPtr<FJsonObject>& InJsonData);

	TArray<UDSBaseModel*> GetModels(const EDSModelType& InType);
	TArray<UDSBaseModel*> GetModels(const TArray<EDSModelType>& InType);
	UDSBaseModel* GetModelByID(const FString& InModelID);
	TArray<UDSBaseModel*> GetAllCustomModels();

	UDSBaseModel* GetModelsByTypeAndID(const EDSModelType& InType, const FString& InModelID);
	UDSBaseModel* GetCustomModelById(const FString& InModelID, TFunction<bool(EDSModelType)> FilterFunc = nullptr);

	UDSBaseModel* SpawnModelAndViewByType(const EDSModelType& InType, FString UUID);
	
	EDSModelType GetCustomModelTypeByUUID(const FString& InModelId);

	bool ClearModelsByTypeRetIfPoolHas(const EDSModelType& InType);
	
	//refresh model when camera changed,
	//include select and pendant
	void SwitchCameraType(bool bIs2d);

	/*
	* @@ switch machine state
	*/

	void SwitchState(const EDSFSMState& InState);
	/*
	*  @@ AdditionState1 : 大状态下的子模式
	*  @@ 对于台面状态，AdditionState1 <---> ECounterTopPaintingType
	*/
	void SwitchState(const EDSFSMState& InState, const int32& AdditionState1);

	UDSFiniteState* GetPreState();
	EDSFSMState GetPreStateType() const;
	
	UDSFiniteState* GetState();
	UDSFiniteState* GetState(EDSFSMState InType);

	template <typename T>
	T* GetTypedState()
	{
		return Cast<T>(GetState());
	}

	template <typename T>
	T* GetTypedState(EDSFSMState InType)
	{
		return Cast<T>(GetState(InType));
	}
	
	UDSFiniteStateMachine* GetStateMachine();
	EDSFSMState GetStateType();

	//revoke pool mark
	FDSRevokeMark GetRevokeMark() const;

	UDSActionExecuteBase* GetActionByModel(UDSBaseModel* InModel, const FPointerEvent& InMouseEvent = FPointerEvent(), bool bShouldInit = false);

	void SetCurrentModel(UDSBaseModel* InModel);

	UDSBaseModel* GetCurrentModel();

	/**
	 * Create a new model, the created model will automatically init by InProperty and add to resource pool.
	 * @tparam ModelType Types inherited from UDSBaseModel.
	 * @param InProperty Property inherited from FDSBaseProperty for initialize the created model.
	 * @param bSelectAfterCreate Does the new model need to be selected when created.
	 * @return New model with pool controlled view.
	 */
	template <typename ModelType>
	ModelType* CreateModel(FDSBaseProperty* InProperty, bool bSelectAfterCreate = true)
	{
		ModelType* NewModel = NewObject<ModelType>();
		if (InProperty)
		{
			NewModel->SetProperty(InProperty);
		}
		OnGenerateAction(NewModel, bSelectAfterCreate);
		return NewModel;
	}


	template <typename ModelType>
	ModelType* CreateModelReturnView(ADSBaseView*& OutView, FDSBaseProperty* InProperty)
	{
		ModelType* NewModel = NewObject<ModelType>();
		if (InProperty)
		{
			NewModel->SetProperty(InProperty);
		}
		OutView = OnGenerateActionReturnView_Inner(NewModel);
		return NewModel;
	}


	ADSBaseView* GetView(UDSBaseModel* InModel);

	/**
	*显示单个区域内对象，其他对象隐藏
	*param AreaModelId 为空显示所有
	*/
	UFUNCTION()
	void ShowSingleAreaModel(const FString& AreaModelID);

	void ResetAllState();
	
	void OnModelExecuteCommandComplete(UDSBaseModel* InModel, const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSBroadcastMarkData>& InMarkData = FDSBroadcastMarkData::BroadcastToMVCMark);

	UFUNCTION()
	void OnModelStateFlagChange(UDSBaseModel* InModel, FModelStatusFlag StateFlag);

	UFUNCTION()
	UDSBaseModel* GetTopModel(UDSBaseModel* InModel);

	FOnModelExecuteCommandComplete& GetModelExecuteCommandCompleteHandle()
	{
		return OnModelExecuteCommandComplete_Handle;
	};

	FOnSingleHoseAreaShowing& GetSingleHouseAreaShowingHandle()
	{
		return OnSingleHouseAreaShowing_Handle;
	};

	//X : Vertical, Y : Horizontal
	void FlipLayout(bool bDirX = true);


	void HideCeilingByCameraType(ECameraType InCameraType);

	void ReplaceUUID(const UDSBaseModel* InModel, const FString& OldUUID, const FString& InUUID);

protected:
	virtual bool DoesSupportWorldType(const EWorldType::Type WorldType) const override;

	virtual void OnKeyboardFocusChanging(const FFocusEvent& FocusEvent, const FWeakWidgetPath& OldPath, const TSharedPtr<SWidget>& OldWidget, const FWidgetPath& NewPath,
		const TSharedPtr<SWidget>& NewWidget);

	void OnHideCeilinTimer();

	//顶面模式下处理当前选择的Model
	void VerifyCurrentSelectedModelByCameraType(ECameraType InCameraType);

	void ShowHideModelByCameraType(ECameraType InCameraType);

	void ShowHideHouseAreaModels(bool bShow);
private:
	static UDSMVCSubsystem* Instance;

	/*
	 *  @@ Resource Data : all resource data in scene
	 *	@@
	 */
	UPROPERTY()
	UResourceData* ResourceData;

	UPROPERTY()
	UDSFiniteStateMachine* DS_FSM;

	/*区域选择时，选中的HouseArea*/
	UPROPERTY()
	UDSHouseAreaModel* SelectedShowingAreaModel;

	FOnModelExecuteCommandComplete OnModelExecuteCommandComplete_Handle;

	FOnSingleHoseAreaShowing OnSingleHouseAreaShowing_Handle;

	FTimerHandle TimerHandle;

	FDelegateHandle CameraTypeHandle;

	TArray<TWeakObjectPtr<AActor>> AllDefaultGridMeshs;
};
