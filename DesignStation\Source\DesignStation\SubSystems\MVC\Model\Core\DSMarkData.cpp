﻿// Fill out your copyright notice in the Description page of Project Settings.


#include "DSMarkData.h"

TSharedPtr<FDSBroadcastMarkData> FDSBroadcastMarkData::BroadcastToMVCMark = MakeShared<FDSBroadcastMarkData>(true);
TSharedPtr<FDSBroadcastMarkData> FDSBroadcastMarkData::NotBroadcastToMVCMark = MakeShared<FDSBroadcastMarkData>(false);
TSharedPtr<FDSBroadcastMarkData> FDSBroadcastMarkData::SpawnBroadcastMark = MakeShared<FDSBroadcastMarkData>(true, true, false, RPF_NotRefresh, RLF_RefreshAll);
TSharedPtr<FDSBroadcastMarkData> FDSBroadcastMarkData::OnlyOutlineBroadcastMark = MakeShared<FDSBroadcastMarkData>(true, false, false, RPF_NotRefresh, RLF_RefreshOutline);
TSharedPtr<FDSBroadcastMarkData> FDSBroadcastMarkData::SelectBroadcastMark = MakeShared<FDSBroadcastMarkData>(true, false, false, RPF_RefreshAll, RLF_RefreshOutline);
