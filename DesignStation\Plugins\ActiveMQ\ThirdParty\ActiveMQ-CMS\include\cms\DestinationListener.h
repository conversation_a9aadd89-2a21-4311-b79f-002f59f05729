/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef _CMS_DESTINATIONLISTENER_H_
#define _CMS_DESTINATIONLISTENER_H_

#include <cms/Config.h>
#include <cms/DestinationEvent.h>

namespace cms {

    /**
     * A listener class that the client can implement to receive events related
     * to Destination addition or removal on the CMS Provider.
     *
     * @since 3.2
     */
    class CMS_API DestinationListener {
    public:

        virtual ~DestinationListener();

        /**
         * Event call-back method that provides a pointer to an Event object which
         * contains information on destination add / remove activity on the CMS Provider.
         *
         * The passed object remains the property of the caller and should never be
         * deleted by the event listener implementation.
         *
         * @param event
         *      The destination event that triggers this call-back.
         */
        virtual void onDestinationEvent(cms::DestinationEvent* event) = 0;

    };

}

#endif /* _CMS_DESTINATIONLISTENER_H_ */
