#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "Misc/TFunction.h"
#include "SubSystems/ModelDependencySubsystem/Structures/DoorDependencyInfo.h"
#include "SubSystems/MVC/Model/DSBaseModel.h"
#include "SubSystems/UI/Widget/CustomCupboard/Property/CustomDoor/CustomGenerateDoorWidget.h"
#include "CupBoardDoorLibrary.generated.h"

USTRUCT(BlueprintType)
struct FCheckBoardPlane
{
	GENERATED_BODY()
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FPlane BasicPlane;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FVector Normal;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FVector> Points;

	FCheckBoardPlane()
		: BasicPlane(FPlane())
		  , Normal(FVector())
		  , Points(TArray<FVector>()) {}

	void InitProperty(const TArray<FVector>& InPoints)
	{
		Points = InPoints;
		BasicPlane = FPlane(InPoints[2], InPoints[1], InPoints[0]);
		Normal = BasicPlane.GetNormal();
	}
};

UCLASS()
class DESIGNSTATION_API UDSCupBoardDoorLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()

public:
	static FVector GetObbCenter(const TArray<FVector>& Obb);
	static TArray<FCheckBoardPlane> CreateSixPlaneFromObb(const TArray<FVector>& Obb);
	static FVector GetObbExtent(const TArray<FVector>& Obb);
	static bool IsPointOnLineSegment(const FVector& PointToTest, const FVector& LineStart, const FVector& LineEnd, float Tolerance);
	static bool CalculatePlanarLineIntersection(const FVector& Line1Start, const FVector& Line1End, const FVector& Line2Start, const FVector& Line2End, FVector& OutIntersectionPoint, float Tolerance);
	static bool CalculateDoorArea(const TArray<UDSBaseModel*>& InBoards, FDSSuitablePlane& OutPlane, bool& bIsOnlyEmbedded, TArray<TArray<UDSBaseModel*>>& DependenciesGroup, TArray<UDSBaseModel*>& DependentCabinets);
	static TArray<FVector> GetFinalProjectLines(const TArray<TArray<FVector>>& ProjectLines, const FVector& Normal, TArray<float> MoveDistance, bool& bIsOnlyEmbedded);
	static bool CheckIfAllSelectedModelsAreBoards(const TArray<UDSBaseModel*>& InModels);
	static TArray<UDSBaseModel*> GetAllOverlapCabinets(const TArray<FVector>& Plane);
	static bool CheckAreaPlaneIsEnable(const TArray<FVector>& Plane, const TArray<UDSBaseModel*>& Cabinets, const FVector& DoorNormal);
	static bool ArePointsCounterClockwise(const TArray<FVector>& InPoints, const FVector& InNormal);
	static void DoorCoverAutoChange(const FDSDoorDependencyInfo& DependencyInfo, UDSBaseModel* SelectModel, FDSCustomDoorProperty& GenerateDoorData, const bool& bIsOnlyGetCover);
	static void ChangeAllDoorsCoverByBoardId(const TArray<FVector>& Plane, const FDSDoorDependencyInfo& DependencyInfo, const int32& Index, FDSCustomDoorProperty& GenerateDoorData, TArray<UDSCupboardModel*>& UpdateSelfDoor, const bool& bIsOnlyGetCover);
	static void ChangeDrawerDoorCover(const TArray<FVector>& Plane, const TArray<UDSCupboardModel*>& DrawerDoorModels, const int32& Index, const FVector& CubeCenter, UDSBaseModel* SelectModel, FDSCustomDoorProperty& GenerateDoorData, const bool& bIsOnlyGetCover);
	// 门分割计算数据结构
	struct FDoorSplitData
	{
		int32 SeqNo;
		float Size;
		float Location;
		TSharedPtr<FMultiComponentDataItem> Item;
	};

	static void ProcessCoverComparison(const EMaskingMode& HasCover, EMaskingMode& NewCover, const bool& bIsOnlyGetCover, TFunction<void(const EMaskingMode&)> ModifyFunction);
	static bool ValidateParameterRange(const TSharedPtr<FMultiComponentDataItem>& Item, const FString& ParameterName, const float& Value);
	static TArray<FDoorSplitData> CalculateDoorSplitByState(const TSharedPtr<FMultiComponentDataItem>& DoorContainer, const float& NewSize, const double& TotalSize, const int32& State, const bool& bIsHorizontal);
	static void CompareAndChangeCoverOfDrawer(const UDSCupboardModel* Drawer, const EMaskingMode& HasDrawerDoorCover, EMaskingMode& NewDoorCover, const bool& bIsOnlyGetCover, const int32& DrawerIndex);
	static void ModifyDrawerCover(const UDSCupboardModel* Model, const int32& Pos, const EMaskingMode& Cover);
	static void CompareAndChangeCover(const TPair<UDSCupboardModel*, int32> DoorPair, EMaskingMode& NewDoorCover, TArray<UDSCupboardModel*>& UpdateSelfDoor, const bool& bIsOnlyGetCover);
	static FString GetCoverParameterName(const int32& Pos);
	static EMaskingMode GetCover(const UDSCupboardModel* Model, const int32& Pos);
	static void ModifyCover(UDSCupboardModel* Model, const int32& Pos, const EMaskingMode& Cover);
	static bool IsRectOBBIntersectOrAdjacent(const TArray<FVector>& RectVertices, const TArray<FVector>& ObbVertices, const float& Epsilon = 0.01f);
	static void ProjectOntoAxis(const TArray<FVector>& Points, const FVector& Axis, float& OutMin, float& OutMax);
	static void UpdateDoorOnCupboard(UDSBaseModel* InCupboard, UDSCupboardModel* SelfDoor = nullptr);
	static void RecursionRefreshDoor(const TSharedPtr<FMultiComponentDataItem>& ParentContainer, const TSharedPtr<FMultiComponentDataItem>& DoorContainer, const float& WJ, const float& HJ, const double& TotalSize,
	                                 TMap<TSharedPtr<FMultiComponentDataItem>, TSharedPtr<FMultiComponentDataItem>>& NeedDeleteDoors);
	static void UpdateDoorTouchedBoardsThickness(const TArray<UDSBaseModel*>& InBoards, FDSCustomDoorProperty& GenerateDoorData);
	static void GetContainerCoverFromParams(const TSharedPtr<FMultiComponentDataItem>& Container, FDSCustomDoorProperty& GenerateDoorData);
	static FTransform CalDoorWorldTransform(const UDSCupboardModel* InModel, bool bWithRotator = true);
	static TArray<TArray<UDSBaseModel*>> GetRelatedBoardOfDoor(const TArray<UDSBaseModel*>& InBoards, const FDSSuitablePlane& OutPlane, TArray<UDSBaseModel*>& DependentCabinets);
	static TArray<UDSBaseModel*> GetEveryGroupBoards(UDSBaseModel* FirstBoard, const TArray<UDSBaseModel*>& ResultBoard, const FVector& Normal, const FVector& Center);
	static void ConvertInnerPointToContainerPoint(const FVector& WidthStart, const FVector& HeightStart, const FVector& OperatePoint, double BoardWidth, double BoardHeight, const FTransform& RootTransform, FVector& OutPoint);
	static void ConvertInnerPointToContainerPointByCover(const FVector& WidthStart, const FVector& HeightStart, const FVector& OperatePoint, double BoardWidth, double BoardHeight, const FTransform& RootTransform,
	                                                     const EMaskingMode& MaskingModeHor, const EMaskingMode& MaskingModeVer, FVector& OutPoint);
	static TMap<TSharedPtr<FMultiComponentDataItem>, TPair<FTransform, TArray<FVector>>> GetHingesWorldTransform(UDSCupboardModel* InDoorModel);
	static int32 FindMostFrequentNumber(const TArray<int32>& InArray);
	static bool ChangeCoverWhenGenerateHinge(UDSCupboardModel* DoorModel);
	static bool IsPointInRectangle(const FVector& Point, const TArray<FVector>& RectVertices);
	static bool IsLineIntersectingRectangle(const FVector& LineStart, const FVector& LineEnd, const TArray<FVector>& RectVertices);
	static bool AreSegmentsIntersectingOrOverlapping(const FVector& P1, const FVector& Q1, const FVector& P2, const FVector& Q2, float Epsilon);
	static TArray<FVector2D> CalculateProjectedPoints(const FPlane& BasicPlane, const TArray<FVector>& BoxVertices);
	static TArray<FVector> CalculateProjectedPoints3D(const FPlane& BasicPlane, const TArray<FVector>& BoxVertices);
	//生成铰链，避让一次，但不校验
	static bool GenerateDoorHinges();
	static bool CheckAllHingesIsValid();
	static void HingesAvoidOnceMore(UDSCupboardModel* DoorModel, const TArray<FVector>& OBB1, const TArray<FVector>& OBB2, const TSharedPtr<FMultiComponentDataItem>& Hinge);
	static void ProjectOBB(const TArray<FVector>& OBB, const FVector& Axis, float& Min, float& Max);
	static bool Overlap1D(float Min1, float Max1, float Min2, float Max2, float Epsilon);
	static bool AreOBBsIntersecting(const FTransform& OBB1_Transform, const TArray<FVector>& OBB1, const FTransform& OBB2_Transform, const TArray<FVector>& OBB2);
	static bool IsPointOnOBBSurface(const FVector& PointToCheck, const FTransform& OBBTransform, const FVector& OBBExtent, float Tolerance);
	static bool IsPointInOBBInside(const FVector& PointToCheck, const FTransform& OBBTransform, const FVector& OBBExtent, float Tolerance);
	static void FindDoorOfCabinetOfInit(UDSBaseModel* InModel);
	static TArray<UDSBaseModel*> GetAllBoardsOfCabinetByDoor(const FDSDoorDependencyInfo& DependencyInfo, bool bIsSelfDoor, UDSCupboardModel* InDoorModel);
	static void OpenOrCloseDoor(UDSBaseModel* InModel, const TArray<UDSCupboardModel*>& AllDoorModels, const TArray<UDSCupboardModel*>& ClosedDoorModels);
	static bool CheckHasClosedDoor(UDSBaseModel* InModel, TArray<UDSCupboardModel*>& AllDoorModels, TArray<UDSCupboardModel*>& ClosedDoorModels);
	static bool CheckHasClosedDrawer(UDSBaseModel* InModel, TArray<UDSCupboardModel*>& ClosedDrawerModels);
	static void OpenOrCloseSingleDoor(UDSCupboardModel* CupboardModel);
	static TArray<TSharedPtr<FMultiComponentDataItem>> RecursionCollectDoors(const TSharedPtr<FMultiComponentDataItem>& DoorContainer, TArray<TSharedPtr<FMultiComponentDataItem>>& DoorList);
	static TSharedPtr<FMultiComponentDataItem> RecursionFindContainOfDoor(const TSharedPtr<FMultiComponentDataItem>& Container, UDSCupboardModel* DoorModel);
	static void UpdateDoorParams(UDSBaseModel* InCupboard);
	static float GetSplitValue(const TSharedPtr<FMultiComponentDataItem>& Item, const FString& ParameterName);

	//获取门覆盖的板件
	static TArray<FString> GetDoorCoverBoards(const TSharedPtr<FMultiComponentDataItem>& DoorNode);
	// 更新所有功能板件的门,入参为门的节点
	static void UpdateFunctionalBoardsByDoor(const TArray<TSharedPtr<FMultiComponentDataItem>>& DoorNodes, bool bRetraction);
	static void ChangeCoverByProperty(UDSCupboardModel* DoorModel, const FString& InParamName, const FString& InOldValue, const FString& InNewValue);
	static void RecursionPushCoverToUpLevel(const TSharedPtr<FMultiComponentDataItem>& ParentItem,
	                                        const TSharedPtr<FMultiComponentDataItem>& CurrentModelInfo,
	                                        const int32 RootSCBJT,
	                                        const int32 RootXCBJT,
	                                        const int32 RootZCBJT,
	                                        const int32 RootYCBJT,
	                                        const float Offset,
	                                        const FString& InNewValue,
	                                        const FString& InParamName,
	                                        const TMap<TSharedPtr<FMultiComponentDataItem>, TSharedPtr<FMultiComponentDataItem>>& PropertyMap,
	                                        TMap<TSharedPtr<FMultiComponentDataItem>, TSharedPtr<FMultiComponentDataItem>>& NeedDeleteDoors);

	static void OnDoorConveringTypeChange(UDSBaseModel* InDoorModel, const FString& InNewConvertType);

	static void ModifyContainerByConveringChange(EMaskingType DoorConvringType, const double& InNewWidth, const double& InNewHeight, const TSharedPtr<FMultiComponentDataItem>& InContainerNode);

	static bool IsDoorContainerNode(const TSharedPtr<FMultiComponentDataItem>& InNode);

	static void CalculateNewSizeBySplitType(const int SplitType, const int SplitCount, double& NewSize, double OldSize, const double& InOldParentSize, const double& InNewParentSize);

	static void OnPriceListClickAndXml();
};
